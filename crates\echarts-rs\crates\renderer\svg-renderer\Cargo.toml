[package]
name = "svg-renderer"
version = "0.1.0"
edition = "2021"
description = "SVG renderer for ECharts-rs"
license = "MIT"
repository = "https://github.com/your-repo/echarts-rs"

# 独立包，不参与工作空间
[workspace]

[dependencies]
echarts-core = { path = "../../core" }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

[[example]]
name = "simple_test"
path = "examples/simple_test.rs"

[[example]]
name = "chart_test"
path = "examples/chart_test.rs"

[dev-dependencies]
tokio = { version = "1.0", features = ["full"] }
