//! Title component implementation

use crate::{Alignment, Component, Position, Renderable, Themeable};
use echarts_core::{Point, Bounds, TextStyle, Result};
use echarts_core::style::{TextAlign, FontWeight};
use echarts_charts::RenderContext;
use echarts_themes::Theme;
use serde::{Deserialize, Serialize};

/// Title component for chart titles and subtitles
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Title {
    /// Whether the title is visible
    pub visible: bool,

    /// Main title text
    pub text: String,

    /// Subtitle text
    pub subtext: Option<String>,

    /// Title position
    pub position: Position,

    /// Text alignment
    pub alignment: Alignment,

    /// Main title text style
    pub text_style: TextStyle,

    /// Subtitle text style
    pub subtext_style: TextStyle,

    /// Spacing between title and subtitle
    pub item_gap: f64,

    /// Padding
    pub padding: [f64; 4], // top, right, bottom, left
}

impl Default for Title {
    fn default() -> Self {
        Title {
            visible: true,
            text: String::new(),
            subtext: None,
            position: Position::Top,
            alignment: Alignment::Center,
            text_style: TextStyle {
                font_size: 18.0,
                font_weight: FontWeight::Bold,
                ..Default::default()
            },
            subtext_style: TextStyle {
                font_size: 14.0,
                ..Default::default()
            },
            item_gap: 10.0,
            padding: [10.0, 10.0, 10.0, 10.0],
        }
    }
}

impl Title {
    /// Create a new title with text
    pub fn new<S: Into<String>>(text: S) -> Self {
        Title {
            text: text.into(),
            ..Default::default()
        }
    }

    /// Set the title text
    pub fn text<S: Into<String>>(mut self, text: S) -> Self {
        self.text = text.into();
        self
    }

    /// Set the subtitle text
    pub fn subtext<S: Into<String>>(mut self, subtext: S) -> Self {
        self.subtext = Some(subtext.into());
        self
    }

    /// Set the position
    pub fn position(mut self, position: Position) -> Self {
        self.position = position;
        self
    }

    /// Set the alignment
    pub fn alignment(mut self, alignment: Alignment) -> Self {
        self.alignment = alignment;
        self
    }

    /// Set visibility
    pub fn visible(mut self, visible: bool) -> Self {
        self.visible = visible;
        self
    }
}

impl Component for Title {
    fn component_type(&self) -> &'static str {
        "title"
    }

    fn is_visible(&self) -> bool {
        self.visible && !self.text.is_empty()
    }

    fn set_visible(&mut self, visible: bool) {
        self.visible = visible;
    }
}

impl Renderable for Title {
    fn render(&self, ctx: &mut RenderContext, bounds: Bounds) -> Result<()> {
        if !self.is_visible() {
            return Ok(());
        }

        // Calculate title position
        let title_pos = self.calculate_position(bounds);

        // Draw main title
        ctx.draw_text(self.text.clone(), title_pos, self.text_style.clone());

        // Draw subtitle if present
        if let Some(ref subtext) = self.subtext {
            let subtitle_pos = Point::new(
                title_pos.x,
                title_pos.y + self.text_style.font_size + self.item_gap,
            );
            ctx.draw_text(subtext.clone(), subtitle_pos, self.subtext_style.clone());
        }

        Ok(())
    }
}

impl Themeable for Title {
    fn apply_theme(&mut self, theme: &Theme) {
        self.text_style.color = theme.text_style.color;
        self.subtext_style.color = theme.text_style.color;
    }
}

impl Title {
    /// Calculate title position based on alignment and bounds
    fn calculate_position(&self, bounds: Bounds) -> Point {
        let text_width = self.text.len() as f64 * self.text_style.font_size * 0.6; // Rough estimation

        let x = match self.text_style.text_align {
            TextAlign::Left => bounds.origin.x + self.padding[3], // left padding
            TextAlign::Center => bounds.origin.x + (bounds.width() - text_width) / 2.0,
            TextAlign::Right => bounds.origin.x + bounds.width() - text_width - self.padding[1], // right padding
            TextAlign::Start => bounds.origin.x + self.padding[3], // same as left
            TextAlign::End => bounds.origin.x + bounds.width() - text_width - self.padding[1], // same as right
        };

        let y = bounds.origin.y + self.padding[0] + self.text_style.font_size; // top padding + font size

        Point::new(x, y)
    }
}
