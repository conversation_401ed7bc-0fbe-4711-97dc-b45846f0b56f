//! Animation system for charts

use serde::{Deserialize, Serialize};
use std::time::Duration;

/// Animation configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Animation {
    /// Animation duration
    pub duration: Duration,

    /// Animation delay before starting
    pub delay: Duration,

    /// Easing function
    pub easing: EasingFunction,

    /// Whether animation is enabled
    pub enabled: bool,
}

impl Animation {
    /// Create a new animation configuration
    pub fn new(duration: Duration) -> Self {
        Self {
            duration,
            delay: Duration::from_millis(0),
            easing: EasingFunction::EaseInOut,
            enabled: true,
        }
    }

    /// Set animation delay
    pub fn with_delay(mut self, delay: Duration) -> Self {
        self.delay = delay;
        self
    }

    /// Set easing function
    pub fn with_easing(mut self, easing: EasingFunction) -> Self {
        self.easing = easing;
        self
    }

    /// Disable animation
    pub fn disabled(mut self) -> Self {
        self.enabled = false;
        self
    }
}

impl Default for Animation {
    fn default() -> Self {
        Self::new(Duration::from_millis(1000))
    }
}

/// Easing functions for animations
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ial<PERSON><PERSON>, Eq, Serialize, Deserialize)]
pub enum EasingFunction {
    Linear,
    EaseIn,
    EaseOut,
    EaseInOut,
    EaseInQuad,
    EaseOutQuad,
    EaseInOutQuad,
    EaseInCubic,
    EaseOutCubic,
    EaseInOutCubic,
    EaseInQuart,
    EaseOutQuart,
    EaseInOutQuart,
    EaseInQuint,
    EaseOutQuint,
    EaseInOutQuint,
    EaseInSine,
    EaseOutSine,
    EaseInOutSine,
    EaseInExpo,
    EaseOutExpo,
    EaseInOutExpo,
    EaseInCirc,
    EaseOutCirc,
    EaseInOutCirc,
    EaseInBack,
    EaseOutBack,
    EaseInOutBack,
    EaseInElastic,
    EaseOutElastic,
    EaseInOutElastic,
    EaseInBounce,
    EaseOutBounce,
    EaseInOutBounce,
}

impl EasingFunction {
    /// Apply the easing function to a normalized time value (0.0 to 1.0)
    pub fn apply(&self, t: f64) -> f64 {
        let t = t.clamp(0.0, 1.0);

        match self {
            EasingFunction::Linear => t,
            EasingFunction::EaseIn => t * t,
            EasingFunction::EaseOut => 1.0 - (1.0 - t) * (1.0 - t),
            EasingFunction::EaseInOut => {
                if t < 0.5 {
                    2.0 * t * t
                } else {
                    1.0 - 2.0 * (1.0 - t) * (1.0 - t)
                }
            }
            EasingFunction::EaseInQuad => t * t,
            EasingFunction::EaseOutQuad => 1.0 - (1.0 - t) * (1.0 - t),
            EasingFunction::EaseInOutQuad => {
                if t < 0.5 {
                    2.0 * t * t
                } else {
                    1.0 - 2.0 * (1.0 - t) * (1.0 - t)
                }
            }
            EasingFunction::EaseInCubic => t * t * t,
            EasingFunction::EaseOutCubic => 1.0 - (1.0 - t).powi(3),
            EasingFunction::EaseInOutCubic => {
                if t < 0.5 {
                    4.0 * t * t * t
                } else {
                    1.0 - 4.0 * (1.0 - t).powi(3)
                }
            }
            EasingFunction::EaseInQuart => t.powi(4),
            EasingFunction::EaseOutQuart => 1.0 - (1.0 - t).powi(4),
            EasingFunction::EaseInOutQuart => {
                if t < 0.5 {
                    8.0 * t.powi(4)
                } else {
                    1.0 - 8.0 * (1.0 - t).powi(4)
                }
            }
            EasingFunction::EaseInQuint => t.powi(5),
            EasingFunction::EaseOutQuint => 1.0 - (1.0 - t).powi(5),
            EasingFunction::EaseInOutQuint => {
                if t < 0.5 {
                    16.0 * t.powi(5)
                } else {
                    1.0 - 16.0 * (1.0 - t).powi(5)
                }
            }
            EasingFunction::EaseInSine => 1.0 - (t * std::f64::consts::PI / 2.0).cos(),
            EasingFunction::EaseOutSine => (t * std::f64::consts::PI / 2.0).sin(),
            EasingFunction::EaseInOutSine => -(((t * std::f64::consts::PI).cos() - 1.0) / 2.0),
            EasingFunction::EaseInExpo => {
                if t == 0.0 {
                    0.0
                } else {
                    2.0_f64.powf(10.0 * (t - 1.0))
                }
            }
            EasingFunction::EaseOutExpo => {
                if t == 1.0 {
                    1.0
                } else {
                    1.0 - 2.0_f64.powf(-10.0 * t)
                }
            }
            EasingFunction::EaseInOutExpo => {
                if t == 0.0 {
                    0.0
                } else if t == 1.0 {
                    1.0
                } else if t < 0.5 {
                    2.0_f64.powf(20.0 * t - 10.0) / 2.0
                } else {
                    (2.0 - 2.0_f64.powf(-20.0 * t + 10.0)) / 2.0
                }
            }
            EasingFunction::EaseInCirc => 1.0 - (1.0 - t * t).sqrt(),
            EasingFunction::EaseOutCirc => (1.0 - (t - 1.0) * (t - 1.0)).sqrt(),
            EasingFunction::EaseInOutCirc => {
                if t < 0.5 {
                    (1.0 - (1.0 - 4.0 * t * t).sqrt()) / 2.0
                } else {
                    ((1.0 - (-2.0 * t + 2.0).powi(2)).sqrt() + 1.0) / 2.0
                }
            }
            EasingFunction::EaseInBack => {
                let c1 = 1.70158;
                let c3 = c1 + 1.0;
                c3 * t * t * t - c1 * t * t
            }
            EasingFunction::EaseOutBack => {
                let c1 = 1.70158;
                let c3 = c1 + 1.0;
                1.0 + c3 * (t - 1.0).powi(3) + c1 * (t - 1.0).powi(2)
            }
            EasingFunction::EaseInOutBack => {
                let c1 = 1.70158;
                let c2 = c1 * 1.525;
                if t < 0.5 {
                    ((2.0 * t).powi(2) * ((c2 + 1.0) * 2.0 * t - c2)) / 2.0
                } else {
                    ((2.0 * t - 2.0).powi(2) * ((c2 + 1.0) * (t * 2.0 - 2.0) + c2) + 2.0) / 2.0
                }
            }
            EasingFunction::EaseInElastic => {
                let c4 = (2.0 * std::f64::consts::PI) / 3.0;
                if t == 0.0 {
                    0.0
                } else if t == 1.0 {
                    1.0
                } else {
                    -2.0_f64.powf(10.0 * t - 10.0) * ((t * 10.0 - 10.75) * c4).sin()
                }
            }
            EasingFunction::EaseOutElastic => {
                let c4 = (2.0 * std::f64::consts::PI) / 3.0;
                if t == 0.0 {
                    0.0
                } else if t == 1.0 {
                    1.0
                } else {
                    2.0_f64.powf(-10.0 * t) * ((t * 10.0 - 0.75) * c4).sin() + 1.0
                }
            }
            EasingFunction::EaseInOutElastic => {
                let c5 = (2.0 * std::f64::consts::PI) / 4.5;
                if t == 0.0 {
                    0.0
                } else if t == 1.0 {
                    1.0
                } else if t < 0.5 {
                    -(2.0_f64.powf(20.0 * t - 10.0) * ((20.0 * t - 11.125) * c5).sin()) / 2.0
                } else {
                    (2.0_f64.powf(-20.0 * t + 10.0) * ((20.0 * t - 11.125) * c5).sin()) / 2.0 + 1.0
                }
            }
            EasingFunction::EaseInBounce => 1.0 - EasingFunction::EaseOutBounce.apply(1.0 - t),
            EasingFunction::EaseOutBounce => {
                let n1 = 7.5625;
                let d1 = 2.75;

                if t < 1.0 / d1 {
                    n1 * t * t
                } else if t < 2.0 / d1 {
                    let t = t - 1.5 / d1;
                    n1 * t * t + 0.75
                } else if t < 2.5 / d1 {
                    let t = t - 2.25 / d1;
                    n1 * t * t + 0.9375
                } else {
                    let t = t - 2.625 / d1;
                    n1 * t * t + 0.984375
                }
            }
            EasingFunction::EaseInOutBounce => {
                if t < 0.5 {
                    (1.0 - EasingFunction::EaseOutBounce.apply(1.0 - 2.0 * t)) / 2.0
                } else {
                    (1.0 + EasingFunction::EaseOutBounce.apply(2.0 * t - 1.0)) / 2.0
                }
            }
        }
    }
}

/// Animation state for tracking progress
#[derive(Debug, Clone)]
pub struct AnimationState {
    /// Start time of the animation
    pub start_time: std::time::Instant,

    /// Animation configuration
    pub config: Animation,

    /// Whether the animation is currently running
    pub running: bool,

    /// Whether the animation has completed
    pub completed: bool,
}

impl AnimationState {
    /// Create a new animation state
    pub fn new(config: Animation) -> Self {
        Self {
            start_time: std::time::Instant::now(),
            config,
            running: false,
            completed: false,
        }
    }

    /// Start the animation
    pub fn start(&mut self) {
        self.start_time = std::time::Instant::now();
        self.running = true;
        self.completed = false;
    }

    /// Update the animation and return the current progress (0.0 to 1.0)
    pub fn update(&mut self) -> f64 {
        if !self.running || !self.config.enabled {
            return 1.0;
        }

        let elapsed = self.start_time.elapsed();

        // Check if we're still in the delay period
        if elapsed < self.config.delay {
            return 0.0;
        }

        let animation_elapsed = elapsed - self.config.delay;

        // Check if animation is complete
        if animation_elapsed >= self.config.duration {
            self.running = false;
            self.completed = true;
            return 1.0;
        }

        // Calculate progress
        let raw_progress = animation_elapsed.as_secs_f64() / self.config.duration.as_secs_f64();
        self.config.easing.apply(raw_progress)
    }

    /// Check if the animation is complete
    pub fn is_complete(&self) -> bool {
        self.completed || !self.config.enabled
    }

    /// Stop the animation
    pub fn stop(&mut self) {
        self.running = false;
        self.completed = true;
    }
}

/// Animation manager for handling multiple animations
#[derive(Debug)]
pub struct AnimationManager {
    /// Active animations with their identifiers
    animations: Vec<(String, AnimationState)>,
    /// Global animation settings
    default_config: Animation,
}

impl AnimationManager {
    /// Create a new animation manager
    pub fn new(default_config: Animation) -> Self {
        Self {
            animations: Vec::new(),
            default_config,
        }
    }

    /// Add a new animation with a unique identifier
    pub fn add_animation<S: Into<String>>(
        &mut self,
        id: S,
        config: Option<Animation>,
    ) -> &mut AnimationState {
        let config = config.unwrap_or_else(|| self.default_config.clone());
        let mut state = AnimationState::new(config);
        state.start();

        let id = id.into();
        self.animations.push((id, state));
        &mut self.animations.last_mut().unwrap().1
    }

    /// Get animation state by ID
    pub fn get_animation<S: AsRef<str>>(&mut self, id: S) -> Option<&mut AnimationState> {
        self.animations
            .iter_mut()
            .find(|(anim_id, _)| anim_id == id.as_ref())
            .map(|(_, state)| state)
    }

    /// Update all animations and return whether any are still running
    pub fn update(&mut self) -> bool {
        let mut any_running = false;

        for (_, state) in &mut self.animations {
            if state.running {
                state.update();
                if state.running {
                    any_running = true;
                }
            }
        }

        // Remove completed animations
        self.animations
            .retain(|(_, state)| state.running || !state.completed);

        any_running
    }

    /// Stop animation by ID
    pub fn stop_animation<S: AsRef<str>>(&mut self, id: S) {
        if let Some((_, state)) = self
            .animations
            .iter_mut()
            .find(|(anim_id, _)| anim_id == id.as_ref())
        {
            state.stop();
        }
    }

    /// Stop all animations
    pub fn stop_all(&mut self) {
        for (_, state) in &mut self.animations {
            state.stop();
        }
    }

    /// Clear all animations
    pub fn clear(&mut self) {
        self.animations.clear();
    }

    /// Check if any animations are running
    pub fn has_running_animations(&self) -> bool {
        self.animations.iter().any(|(_, state)| state.running)
    }

    /// Get the number of active animations
    pub fn animation_count(&self) -> usize {
        self.animations.len()
    }
}

/// Animation presets for common use cases
impl Animation {
    /// Fast animation (200ms)
    pub fn fast() -> Self {
        Self::new(Duration::from_millis(200)).with_easing(EasingFunction::EaseOut)
    }

    /// Normal animation (400ms)
    pub fn normal() -> Self {
        Self::new(Duration::from_millis(400)).with_easing(EasingFunction::EaseInOut)
    }

    /// Slow animation (800ms)
    pub fn slow() -> Self {
        Self::new(Duration::from_millis(800)).with_easing(EasingFunction::EaseInOut)
    }

    /// Bouncy animation
    pub fn bouncy() -> Self {
        Self::new(Duration::from_millis(600)).with_easing(EasingFunction::EaseOutBounce)
    }

    /// Elastic animation
    pub fn elastic() -> Self {
        Self::new(Duration::from_millis(800)).with_easing(EasingFunction::EaseOutElastic)
    }

    /// No animation (instant)
    pub fn none() -> Self {
        Self::new(Duration::from_millis(0)).disabled()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_easing_functions() {
        let functions = vec![
            EasingFunction::Linear,
            EasingFunction::EaseIn,
            EasingFunction::EaseOut,
            EasingFunction::EaseInOut,
        ];

        for func in functions {
            assert_eq!(func.apply(0.0), 0.0);
            assert_eq!(func.apply(1.0), 1.0);

            let mid = func.apply(0.5);
            assert!(mid >= 0.0 && mid <= 1.0);
        }
    }

    #[test]
    fn test_animation_state() {
        let config = Animation::new(Duration::from_millis(100));
        let mut state = AnimationState::new(config);

        assert!(!state.running);
        assert!(!state.completed);

        state.start();
        assert!(state.running);
        assert!(!state.completed);

        // Should be in progress
        let progress = state.update();
        assert!(progress >= 0.0 && progress <= 1.0);
    }
}
