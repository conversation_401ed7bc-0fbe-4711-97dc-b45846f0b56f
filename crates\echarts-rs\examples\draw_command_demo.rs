/*!
 * ECharts GPUI DrawCommand 架构演示
 *
 * 本示例展示了新的架构：
 * 1. processor 文件夹负责数据处理，生成 Vec<DrawCommand>
 * 2. renderer 文件夹负责渲染，直接处理 Vec<DrawCommand>
 * 3. 完全分离的数据处理与渲染架构
 */

use echarts_rs::{prelude::*, DrawCommand, DrawOptions, PerformanceHint};
use gpui::Bounds as GpuiBounds;
use gpui::{
    div, px, rgb, size, AppContext, Application, Context, FontWeight, IntoElement, ParentElement,
    Render, Styled, TitlebarOptions, Window, WindowBackgroundAppearance, WindowBounds, WindowKind,
    WindowOptions,
};

fn main() {
    println!("🚀 启动 ECharts GPUI DrawCommand 架构演示");

    let app = Application::new();

    app.run(move |cx| {
        let window_size = size(px(1200.0), px(800.0));

        let _window = cx.open_window(
            WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(GpuiBounds::centered(
                    None,
                    window_size,
                    cx,
                ))),
                titlebar: Some(TitlebarOptions {
                    title: Some("ECharts DrawCommand 架构演示".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                window_background: WindowBackgroundAppearance::Opaque,
                app_id: None,
                window_min_size: Some(size(px(800.0), px(600.0))),
                window_decorations: None,
            },
            |_window, cx| {
                println!("🪟 DrawCommand 架构演示窗口已创建");
                cx.new(|cx| DrawCommandDemo::new(cx))
            },
        );
    });
}

/// DrawCommand 架构演示
struct DrawCommandDemo {
    /// 数据处理器 - 负责生成DrawCommand
    data_processor: ChartDataProcessor,
    /// 图表渲染器
    chart_renderer: GpuiChartRenderer,
    /// 已生成的绘制命令缓存
    draw_commands_cache: Vec<Vec<DrawCommand>>,
    /// 图表标题
    chart_titles: Vec<String>,
}

/// 图表数据处理器 - 负责将原始数据转换为DrawCommand
struct ChartDataProcessor {
    /// 处理器配置
    config: ProcessorConfig,
}

/// 处理器配置
struct ProcessorConfig {
    /// 默认绘制选项
    default_draw_options: DrawOptions,
}

impl DrawCommandDemo {
    fn new(_cx: &mut Context<Self>) -> Self {
        println!("📊 初始化 DrawCommand 架构演示");

        let mut demo = Self {
            data_processor: ChartDataProcessor::new(),
            chart_renderer: GpuiChartRenderer::new(),
            draw_commands_cache: Vec::new(),
            chart_titles: vec![
                "简单线条图".to_string(),
                "矩形柱状图".to_string(),
                "圆形散点图".to_string(),
                "文本标签".to_string(),
            ],
        };

        // 预生成所有绘制命令
        demo.pregenerate_all_commands();

        println!("✅ DrawCommand 架构演示初始化完成");
        demo
    }

    /// 预生成所有绘制命令
    fn pregenerate_all_commands(&mut self) {
        println!("🔄 开始预生成所有绘制命令");

        // 生成简单线条图的绘制命令
        let line_commands = self.data_processor.generate_line_chart_commands();
        self.draw_commands_cache.push(line_commands);

        // 生成矩形柱状图的绘制命令
        let bar_commands = self.data_processor.generate_bar_chart_commands();
        self.draw_commands_cache.push(bar_commands);

        // 生成圆形散点图的绘制命令
        let scatter_commands = self.data_processor.generate_scatter_chart_commands();
        self.draw_commands_cache.push(scatter_commands);

        // 生成文本标签的绘制命令
        let text_commands = self.data_processor.generate_text_chart_commands();
        self.draw_commands_cache.push(text_commands);

        println!("🎯 所有绘制命令预生成完成，共 {} 个图表", self.draw_commands_cache.len());
    }

    /// 获取指定图表的绘制命令
    fn get_draw_commands(&self, chart_index: usize) -> Option<&Vec<DrawCommand>> {
        self.draw_commands_cache.get(chart_index)
    }
}

impl ChartDataProcessor {
    fn new() -> Self {
        Self {
            config: ProcessorConfig {
                default_draw_options: DrawOptions {
                    performance_hint: PerformanceHint::Balanced,
                    immediate: true,
                    priority: 0,
                    label: Some("chart_element".to_string()),
                },
            },
        }
    }

    /// 生成线条图的绘制命令
    fn generate_line_chart_commands(&self) -> Vec<DrawCommand> {
        println!("🔄 生成线条图绘制命令");

        let mut commands = Vec::new();

        // 生成连接线
        let points = vec![
            Point::new(50.0, 150.0),
            Point::new(100.0, 100.0),
            Point::new(150.0, 120.0),
            Point::new(200.0, 80.0),
            Point::new(250.0, 110.0),
        ];

        for i in 0..points.len() - 1 {
            commands.push(DrawCommand::Line {
                from: points[i],
                to: points[i + 1],
                style: LineStyle {
                    color: Color::rgb_u8(51, 153, 229),
                    width: 2.0,
                    dash: None,
                    cap: LineCap::Round,
                    join: LineJoin::Round,
                    miter_limit: 10.0,
                },
                options: self.config.default_draw_options.clone(),
            });
        }

        // 生成数据点
        for point in points {
            commands.push(DrawCommand::Circle {
                center: point,
                radius: 4.0,
                style: FillStyle {
                    fill: Fill::Solid(Color::rgb_u8(51, 153, 229)),
                    opacity: 1.0,
                },
                options: self.config.default_draw_options.clone(),
            });
        }

        println!("✅ 线条图绘制命令生成完成，共 {} 个命令", commands.len());
        commands
    }

    /// 生成柱状图的绘制命令
    fn generate_bar_chart_commands(&self) -> Vec<DrawCommand> {
        println!("🔄 生成柱状图绘制命令");

        let mut commands = Vec::new();

        // 生成柱状图数据
        let data = vec![120.0, 200.0, 150.0, 80.0, 70.0, 110.0];
        let bar_width = 30.0;
        let base_y = 200.0;

        for (i, &value) in data.iter().enumerate() {
            let x = 50.0 + i as f64 * 50.0;
            let height = value * 0.8; // 缩放因子
            let y = base_y - height;

            commands.push(DrawCommand::Rect {
                bounds: Bounds::new(x, y, bar_width, height),
                style: FillStyle {
                    fill: Fill::Solid(Color::rgb_u8(76, 204, 76)),
                    opacity: 0.8,
                },
                options: self.config.default_draw_options.clone(),
            });
        }

        println!("✅ 柱状图绘制命令生成完成，共 {} 个命令", commands.len());
        commands
    }

    /// 生成散点图的绘制命令
    fn generate_scatter_chart_commands(&self) -> Vec<DrawCommand> {
        println!("🔄 生成散点图绘制命令");

        let mut commands = Vec::new();

        // 生成随机散点数据
        let points = vec![
            Point::new(80.0, 120.0),
            Point::new(120.0, 160.0),
            Point::new(160.0, 100.0),
            Point::new(200.0, 140.0),
            Point::new(240.0, 110.0),
            Point::new(280.0, 170.0),
        ];

        for point in points {
            commands.push(DrawCommand::Circle {
                center: point,
                radius: 8.0,
                style: FillStyle {
                    fill: Fill::Solid(Color::rgb_u8(229, 102, 51)),
                    opacity: 0.7,
                },
                options: self.config.default_draw_options.clone(),
            });
        }

        println!("✅ 散点图绘制命令生成完成，共 {} 个命令", commands.len());
        commands
    }

    /// 生成文本标签的绘制命令
    fn generate_text_chart_commands(&self) -> Vec<DrawCommand> {
        println!("🔄 生成文本标签绘制命令");

        let mut commands = Vec::new();

        // 生成标题
        commands.push(DrawCommand::Text {
            text: "DrawCommand 架构演示".to_string(),
            position: Point::new(150.0, 30.0),
            style: TextStyle {
                font_family: "Arial".to_string(),
                font_size: 18.0,
                color: Color::rgb_u8(51, 51, 51),
                font_weight: FontWeight::Bold,
                font_style: FontStyle::Normal,
                text_align: TextAlign::Center,
                text_baseline: TextBaseline::Middle,
                line_height: 1.2,
                letter_spacing: 0.0,
            },
            options: self.config.default_draw_options.clone(),
        });

        // 生成坐标轴标签
        let x_labels = ["1月", "2月", "3月", "4月", "5月", "6月"];
        for (i, label) in x_labels.iter().enumerate() {
            commands.push(DrawCommand::Text {
                text: label.to_string(),
                position: Point::new(50.0 + i as f64 * 50.0, 220.0),
                style: TextStyle {
                    font_family: "Arial".to_string(),
                    font_size: 12.0,
                    color: Color::rgb_u8(102, 102, 102),
                    font_weight: FontWeight::Normal,
                    font_style: FontStyle::Normal,
                    text_align: TextAlign::Center,
                    text_baseline: TextBaseline::Middle,
                    line_height: 1.2,
                    letter_spacing: 0.0,
                },
                options: self.config.default_draw_options.clone(),
            });
        }

        println!("✅ 文本标签绘制命令生成完成，共 {} 个命令", commands.len());
        commands
    }
}

impl Render for DrawCommandDemo {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        div()
            .flex()
            .flex_col()
            .size_full()
            .bg(rgb(0xf5f5f5))
            .child(
                // 标题栏
                div()
                    .flex()
                    .justify_center()
                    .items_center()
                    .h(px(80.0))
                    .bg(rgb(0x2c3e50))
                    .child(
                        div()
                            .text_xl()
                            .font_weight(FontWeight::BOLD)
                            .text_color(rgb(0xffffff))
                            .child("ECharts DrawCommand 架构演示 - 数据处理与渲染完全分离"),
                    ),
            )
            .child(
                // 图表网格
                div()
                    .flex()
                    .flex_wrap()
                    .gap_4()
                    .p_4()
                    .children(
                        (0..self.chart_titles.len())
                            .map(|i| self.render_chart_panel(i))
                            .collect::<Vec<_>>(),
                    ),
            )
            .child(
                // 状态栏
                div()
                    .h(px(60.0))
                    .bg(rgb(0x495057))
                    .text_color(rgb(0xffffff))
                    .flex()
                    .items_center()
                    .justify_center()
                    .px_6()
                    .child(
                        div()
                            .text_sm()
                            .child(format!(
                                "💡 DrawCommand 架构演示 | 缓存了 {} 个图表的绘制命令 | processor 处理数据，renderer 直接渲染",
                                self.draw_commands_cache.len()
                            )),
                    ),
            )
    }
}

impl DrawCommandDemo {
    /// 渲染单个图表面板
    fn render_chart_panel(&self, chart_index: usize) -> impl IntoElement {
        let title = self.chart_titles.get(chart_index).cloned().unwrap_or_else(|| "未知图表".to_string());
        let command_count = self.get_draw_commands(chart_index).map(|cmds| cmds.len()).unwrap_or(0);

        div()
            .flex()
            .flex_col()
            .w(px(280.0))
            .h(px(320.0))
            .bg(rgb(0xffffff))
            .border_1()
            .border_color(rgb(0xe0e0e0))
            .rounded_lg()
            .shadow_sm()
            .child(
                // 图表标题
                div()
                    .flex()
                    .justify_center()
                    .items_center()
                    .h(px(40.0))
                    .bg(rgb(0xf8f9fa))
                    .border_b_1()
                    .border_color(rgb(0xe0e0e0))
                    .child(
                        div()
                            .font_weight(FontWeight::MEDIUM)
                            .text_color(rgb(0x333333))
                            .child(title),
                    ),
            )
            .child(
                // 图表内容区域
                div()
                    .flex_1()
                    .p_2()
                    .child(
                        div()
                            .size_full()
                            .flex()
                            .items_center()
                            .justify_center()
                            .child(format!("DrawCommand 数量: {} (已缓存)", command_count))
                    ),
            )
    }
}
