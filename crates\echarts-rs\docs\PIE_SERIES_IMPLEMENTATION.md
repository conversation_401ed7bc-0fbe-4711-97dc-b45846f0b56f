# PieSeries 实现详细设计

**日期**: 2025-07-21  
**状态**: 📋 设计阶段  
**优先级**: P0  
**预计工时**: 16小时

## 📋 总体目标

实现完整的饼图功能，包括环形图、玫瑰图、标签和引导线等高级特性，并与现有的 Series 架构无缝集成。

## 🔍 需求分析

### 功能需求

1. **基础饼图**
   - 支持基本的饼图绘制
   - 支持扇区颜色配置
   - 支持扇区间隔

2. **环形图**
   - 支持内外半径配置
   - 支持空心饼图

3. **玫瑰图**
   - 支持面积模式
   - 支持半径模式

4. **标签和引导线**
   - 支持内部标签
   - 支持外部标签和引导线
   - 支持标签格式化

5. **交互功能**
   - 支持扇区选中
   - 支持扇区高亮
   - 支持工具提示

### 技术需求

1. **架构兼容性**
   - 实现 Series trait
   - 支持 DrawCommand 渲染
   - 支持类型擦除

2. **性能要求**
   - 支持大量数据项
   - 渲染性能良好

3. **扩展性**
   - 支持自定义样式
   - 支持动画配置

## 🚀 实现方案

### 数据结构

```rust
/// 饼图系列配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PieSeriesConfig {
    /// 系列名称
    pub name: String,
    
    /// 是否显示
    pub visible: bool,
    
    /// Z轴索引，控制绘制顺序
    pub z_index: i32,
    
    /// 饼图中心位置 [x, y]，支持百分比和像素值
    pub center: [String; 2],
    
    /// 饼图半径，支持百分比和像素值
    pub radius: PieRadius,
    
    /// 起始角度，弧度制
    pub start_angle: f64,
    
    /// 最小角度（小于该角度的扇区会被合并）
    pub min_angle: f64,
    
    /// 扇区间隔角度
    pub padding_angle: f64,
    
    /// 是否顺时针排列
    pub clockwise: bool,
    
    /// 是否启用玫瑰图模式
    pub rose_type: Option<RoseType>,
    
    /// 选中模式
    pub selected_mode: SelectedMode,
    
    /// 标签配置
    pub label: PieLabel,
    
    /// 高亮样式
    pub emphasis: PieEmphasis,
    
    /// 动画配置
    pub animation: AnimationConfig,
}

/// 饼图半径配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PieRadius {
    /// 单一半径（普通饼图）
    Simple(String),
    
    /// 内外半径（环形图）
    Range(String, String),
}

/// 玫瑰图类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RoseType {
    /// 扇区圆心角展现数据的百分比
    Radius,
    
    /// 扇区面积展现数据的百分比
    Area,
}

/// 饼图标签配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PieLabel {
    /// 是否显示标签
    pub show: bool,
    
    /// 标签位置
    pub position: PieLabelPosition,
    
    /// 标签格式化器
    pub formatter: Option<String>,
    
    /// 标签样式
    pub style: TextStyle,
    
    /// 引导线配置
    pub line: Option<LabelLine>,
}

/// 饼图标签位置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PieLabelPosition {
    /// 扇区内部
    Inside,
    
    /// 扇区外部
    Outside,
    
    /// 中心
    Center,
}

/// 主饼图系列结构
#[derive(Debug, Clone)]
pub struct PieSeries {
    /// 系列名称
    pub name: String,
    
    /// 图表数据
    pub data: DataSet,
    
    /// 配置选项
    pub config: PieSeriesConfig,
    
    /// 是否可见
    pub visible: bool,
    
    /// Z轴索引
    pub z_index: i32,
}
```

### 核心方法

```rust
impl PieSeries {
    /// 创建新的饼图系列
    pub fn new<S: Into<String>>(name: S) -> Self {
        Self {
            name: name.into(),
            data: DataSet::new(),
            config: PieSeriesConfig::default(),
            visible: true,
            z_index: 0,
        }
    }
    
    /// 设置数据（从名称-值对）
    pub fn data<I, S>(mut self, data: I) -> Self
    where
        I: IntoIterator<Item = (S, f64)>,
        S: Into<String>,
    {
        // 实现数据转换逻辑
        self
    }
    
    /// 设置饼图半径
    pub fn radius<S1: Into<String>, S2: Into<String>>(mut self, inner: S1, outer: S2) -> Self {
        self.config.radius = PieRadius::Range(inner.into(), outer.into());
        self
    }
    
    /// 设置为环形图
    pub fn as_donut<S1: Into<String>, S2: Into<String>>(self, inner_radius: S1, outer_radius: S2) -> Self {
        self.radius(inner_radius, outer_radius)
    }
    
    /// 设置为玫瑰图
    pub fn as_rose(mut self, rose_type: RoseType) -> Self {
        self.config.rose_type = Some(rose_type);
        self
    }
    
    /// 设置标签配置
    pub fn label(mut self, position: PieLabelPosition, show_line: bool) -> Self {
        self.config.label.position = position;
        self.config.label.line = if show_line {
            Some(LabelLine::default())
        } else {
            None
        };
        self
    }
    
    /// 计算饼图布局
    fn calculate_layout(&self, bounds: Bounds) -> Vec<PieSlice> {
        // 实现布局计算逻辑
        Vec::new()
    }
}
```

### Series Trait 实现

```rust
impl Series for PieSeries {
    fn name(&self) -> &str {
        &self.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Pie
    }

    fn render_to_commands(&self, _coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        if !self.visible || self.data.is_empty() {
            return Ok(commands);
        }

        // 获取数据总和
        let total: f64 = self.data.iter()
            .filter_map(|point| point.get_number(1))
            .sum();

        // 计算每个扇区的角度
        let mut start_angle = self.config.start_angle;
        let center = self.calculate_center_point(_coord_system.bounds());
        let radius = self.calculate_radius(_coord_system.bounds());

        // 绘制每个扇区
        for (i, point) in self.data.iter().enumerate() {
            if let (Some(name), Some(value)) = (point.name.as_ref(), point.get_number(1)) {
                if value <= 0.0 || total <= 0.0 {
                    continue;
                }

                // 计算扇区角度
                let angle = value / total * 2.0 * std::f64::consts::PI;
                if angle < self.config.min_angle {
                    continue;
                }

                // 计算结束角度
                let end_angle = if self.config.clockwise {
                    start_angle + angle
                } else {
                    start_angle - angle
                };

                // 获取颜色
                let color = self.get_color_for_index(i);

                // 创建扇区路径
                let path_commands = self.create_pie_slice_path(
                    center,
                    radius,
                    start_angle,
                    end_angle,
                    self.config.rose_type.is_some(),
                );

                // 添加扇区绘制命令
                commands.push(DrawCommand::Path {
                    commands: path_commands,
                    style: PathStyle {
                        fill: Some(color),
                        stroke: Some(LineStyle {
                            color: Color::rgb(1.0, 1.0, 1.0),
                            width: 1.0,
                            opacity: 0.5,
                            dash_pattern: None,
                            cap: LineCap::Butt,
                            join: LineJoin::Round,
                        }),
                        opacity: 1.0,
                    },
                });

                // 添加标签
                if self.config.label.show {
                    self.add_label_commands(
                        &mut commands,
                        name,
                        value,
                        center,
                        radius,
                        (start_angle + end_angle) / 2.0,
                    );
                }

                // 更新起始角度
                start_angle = end_angle + self.config.padding_angle;
            }
        }

        Ok(commands)
    }

    fn bounds(&self) -> Option<Bounds> {
        // 饼图的边界是基于其配置的中心点和半径
        None // 饼图通常使用整个可用空间
    }

    fn is_visible(&self) -> bool {
        self.visible
    }

    fn z_index(&self) -> i32 {
        self.z_index
    }

    fn clone_series(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }
}
```

## 📊 测试计划

### 单元测试

1. **基础功能测试**
   - 测试创建和配置
   - 测试数据设置
   - 测试半径和中心点计算

2. **渲染测试**
   - 测试 DrawCommand 生成
   - 测试不同配置下的渲染结果
   - 测试边界情况（空数据、单一数据等）

3. **集成测试**
   - 测试与 Chart 的集成
   - 测试与其他 Series 的组合
   - 测试与 RenderContext 的交互

### 性能测试

1. **大数据集测试**
   - 测试大量数据项的性能
   - 测试内存使用情况

2. **渲染性能**
   - 测试渲染速度
   - 测试动画性能

## 📝 实现步骤

1. **基础结构实现** (4小时)
   - 创建数据结构
   - 实现基本方法
   - 添加配置选项

2. **渲染逻辑实现** (6小时)
   - 实现扇区计算
   - 实现路径生成
   - 实现标签和引导线

3. **Series Trait 实现** (3小时)
   - 实现所有必要方法
   - 确保类型擦除支持
   - 添加边界计算

4. **测试和优化** (3小时)
   - 编写单元测试
   - 进行性能测试
   - 优化渲染逻辑

## 🔍 验收标准

1. **功能完整性**
   - 支持所有计划的功能
   - 与现有架构无缝集成
   - API 易用且一致

2. **代码质量**
   - 通过所有单元测试
   - 通过 Clippy 检查
   - 文档完整

3. **性能指标**
   - 支持至少 100 个数据项
   - 渲染时间 < 100ms
   - 内存使用合理

## 📈 后续扩展

1. **动画支持**
   - 添加扇区动画
   - 支持强调动画

2. **交互增强**
   - 实现拖拽功能
   - 添加选中状态

3. **可访问性**
   - 添加屏幕阅读器支持
   - 支持键盘导航

---

**备注**: 本设计文档将根据实际实现过程进行调整和完善。
