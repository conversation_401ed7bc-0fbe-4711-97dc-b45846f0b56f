---
type: "always_apply"
description: "Example description"
---
<!--
 * @Author: Artis
 * @Date: 2025-07-16 21:42:47
 * @LastEditors: Artis
 * @LastEditTime: 2025-07-16 21:42:54
 * @FilePath: \FscDAQ_GPUI\.cursor\rules\check.mdc
 * @Description: 
 * 
 * Copyright (c) 2025 by Artis, All Rights Reserved. 
-->
# 检查

执行全面的代码质量和安全检查。

## 主要任务：
项目特定的检查命令  并解决产生的任何错误。

## 重要提示：
- 在此过程中不要提交任何代码
- 不要更改版本号
- 只专注于修复检查识别出的问题

## 常见检查包括：
1. **代码检查**：代码风格和语法错误
3. **单元测试**：测试用例失败
4. **安全扫描**：漏洞检测
5. **代码格式化**：风格一致性
6. **构建验证**：编译错误

## 流程：
1. 运行检查命令
2. 分析输出中的错误和警告
3. 按优先级顺序修复问题：
   - 首先解决破坏构建的错误
   - 测试失败
   - 代码检查错误
   - 警告
4. 每次修复后重新运行检查
5. 继续直到所有检查通过

alwaysApply: false
---
