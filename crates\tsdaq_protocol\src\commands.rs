/*
 * @Author: Art<PERSON>
 * @Date: 2025-06-23 23:13:01
 * @LastEditors: Artis
 * @LastEditTime: 2025-07-13 00:09:07
 * @FilePath: \FscDAQ_GPUI\crates\tsdaq_protocol\src\commands.rs
 * @Description: 设备命令和响应处理模块
 *
 * Copyright (c) 2025 by Artis, All Rights Reserved.
 */
use crate::Frame;
use crate::ProtocolError;
use bytes::Bytes;
use tracing::{debug, error, info, warn};

/// 设备命令枚举，表示可以发送给设备的各种命令
#[derive(Debug, Clone, PartialEq)]
pub enum DeviceCommand {
    /// 查询设备信息
    Query,
    /// 连接设备
    Connect,
    /// 断开连接
    Disconnect,
    /// 开始数据采集
    StartCollection,
    /// 停止数据采集
    StopCollection,
    /// 设置设备参数
    SetParameters(Bytes),
    /// 读取设备参数
    ReadParameters,
    /// 读取通道表
    ReadChannelTable,
    /// 发送数据到设备
    SendData(Bytes),
    /// 自定义命令
    Custom {
        /// 功能码
        function: u8,
        /// 地址
        address: u16,
        /// 命令数据
        data: Bytes,
    },
}

impl DeviceCommand {
    /// 将命令转换为帧
    pub fn into_frame(self) -> Frame {
        let frame = match self {
            DeviceCommand::Query => Frame {
                header: [0xAA, 0xDD],
                function: 0x00,
                address: 0x0000,
                data: Bytes::new(),
            },
            DeviceCommand::Connect => Frame {
                header: [0xAA, 0xDD],
                function: 0x01,
                address: 0x0000,
                data: Bytes::new(),
            },
            DeviceCommand::Disconnect => Frame {
                header: [0xAA, 0xDD],
                function: 0x02,
                address: 0x0000,
                data: Bytes::new(),
            },
            DeviceCommand::StartCollection => Frame {
                header: [0xAA, 0xDD],
                function: 0x03,
                address: 0x0000,
                data: Bytes::new(),
            },
            DeviceCommand::StopCollection => Frame {
                header: [0xAA, 0xDD],
                function: 0x04,
                address: 0x0000,
                data: Bytes::new(),
            },
            DeviceCommand::SetParameters(data) => Frame {
                header: [0xAA, 0xDD],
                function: 0x05,
                address: 0x0000,
                data,
            },
            DeviceCommand::ReadParameters => Frame {
                header: [0xAA, 0xDD],
                function: 0x06,
                address: 0x0000,
                data: Bytes::new(),
            },
            DeviceCommand::ReadChannelTable => Frame {
                header: [0xAA, 0xDD],
                function: 0x0C,
                address: 0x0000,
                data: Bytes::new(),
            },
            DeviceCommand::SendData(data) => Frame {
                header: [0xAA, 0xDD],
                function: 0x07,
                address: 0x0000,
                data,
            },
            DeviceCommand::Custom {
                function,
                address,
                data,
            } => Frame {
                header: [0xAA, 0xDD],
                function,
                address,
                data,
            },
        };

        debug!(
            "生成命令帧: 功能码=0x{:02X}, 地址=0x{:04X}, 数据长度={}",
            frame.function,
            frame.address,
            frame.data.len()
        );

        frame
    }

    /// 获取命令的功能码
    pub fn function_code(&self) -> u8 {
        match self {
            DeviceCommand::Query => 0x00,
            DeviceCommand::Connect => 0x01,
            DeviceCommand::Disconnect => 0x02,
            DeviceCommand::StartCollection => 0x03,
            DeviceCommand::StopCollection => 0x04,
            DeviceCommand::SetParameters(_) => 0x05,
            DeviceCommand::ReadParameters => 0x06,
            DeviceCommand::SendData(_) => 0x07,
            DeviceCommand::ReadChannelTable => 0x0C,
            DeviceCommand::Custom { function, .. } => *function,
        }
    }

    /// 获取命令的描述信息
    pub fn description(&self) -> &'static str {
        match self {
            DeviceCommand::Query => "查询设备信息",
            DeviceCommand::Connect => "连接设备",
            DeviceCommand::Disconnect => "断开连接",
            DeviceCommand::StartCollection => "开始数据采集",
            DeviceCommand::StopCollection => "停止数据采集",
            DeviceCommand::SetParameters(_) => "设置设备参数",
            DeviceCommand::ReadParameters => "读取设备参数",
            DeviceCommand::ReadChannelTable => "读取通道表",
            DeviceCommand::SendData(_) => "发送数据到设备",
            DeviceCommand::Custom { .. } => "自定义命令",
        }
    }

    /// 验证响应是否与此命令匹配
    pub fn validate_response(&self, response: &DeviceResponse) -> Result<(), ProtocolError> {
        let command_desc = self.description();
        let function_code = self.function_code();

        match response {
            DeviceResponse::Ack { command, status } => {
                // 检查状态码
                if *status != 0x00 {
                    let error_msg = self.get_status_error_message(*status);
                    error!(
                        "命令执行失败: {} (0x{:02X}) - {}",
                        command_desc, function_code, error_msg
                    );
                    return Err(ProtocolError::DeviceError(format!(
                        "命令 {} (0x{:02X}) 执行失败: {}",
                        command_desc, function_code, error_msg
                    )));
                }

                // 检查命令码匹配性
                if *command != function_code {
                    warn!(
                        "命令响应不匹配: 期望 0x{:02X}，实际 0x{:02X}",
                        function_code, command
                    );
                    // 某些设备可能不严格返回匹配的命令码，记录警告但不返回错误
                }

                info!("命令执行成功: {} (0x{:02X})", command_desc, function_code);
                Ok(())
            }

            DeviceResponse::Parameters(_) => {
                match self {
                    DeviceCommand::ReadParameters => {
                        debug!("收到参数响应，与ReadParameters命令匹配");
                        Ok(())
                    }
                    _ => {
                        warn!("收到参数响应，但命令不是ReadParameters");
                        // 某些设备可能返回额外参数，记录警告但不返回错误
                        Ok(())
                    }
                }
            }

            DeviceResponse::Data(_) => {
                match self {
                    DeviceCommand::ReadChannelTable | DeviceCommand::ReadParameters => {
                        debug!("收到数据响应，与读取命令匹配");
                        Ok(())
                    }
                    _ => {
                        debug!("收到数据响应，可能是额外数据");
                        // 数据响应可能来自多种命令，因此较为宽松
                        Ok(())
                    }
                }
            }

            DeviceResponse::Error(msg) => {
                error!("设备返回错误: {}", msg);
                Err(ProtocolError::DeviceError(msg.clone()))
            }

            DeviceResponse::Custom(_) => {
                debug!("收到自定义响应，无法进行标准验证");
                // 对于自定义响应，无法进行标准验证
                Ok(())
            }
        }
    }

    /// 获取状态码对应的错误消息
    fn get_status_error_message(&self, status: u8) -> &'static str {
        match status {
            0x01 => "设备故障",
            0x02 => "数据错误",
            0x03 => "命令不支持",
            0x04 => "参数错误",
            _ => "未知状态码",
        }
    }
}

/// 设备响应枚举，表示从设备接收的各种响应
#[derive(Debug, Clone)]
pub enum DeviceResponse {
    /// 确认响应
    Ack {
        /// 确认的命令码
        command: u8,
        /// 状态码（0=成功）
        status: u8,
    },
    /// 参数响应
    Parameters(Bytes),
    /// 数据响应
    Data(Bytes),
    /// 错误响应
    Error(String),
    /// 自定义响应
    Custom(Frame),
}

impl DeviceResponse {
    /// 从帧创建响应对象
    pub fn from_frame(frame: Frame) -> Result<Self, ProtocolError> {
        debug!(
            "解析响应帧: 功能码=0x{:02X}, 地址=0x{:04X}, 数据长度={}",
            frame.function,
            frame.address,
            frame.data.len()
        );

        match frame.function {
            // 确认响应（ACK）
            0x0D => {
                if frame.data.len() < 2 {
                    return Err(ProtocolError::Frame(
                        "ACK帧数据不足（需要至少2字节）".to_string(),
                    ));
                }

                let command = frame.data[0];
                let status = frame.data[1];

                match status {
                    0x00 => {
                        info!("命令 0x{:02X} 执行成功", command);
                        Ok(DeviceResponse::Ack { command, status })
                    }
                    0x01 => {
                        warn!("设备故障: 命令=0x{:02X}", command);
                        Ok(DeviceResponse::Error("设备故障".to_string()))
                    }
                    0x02 => {
                        warn!("数据错误: 命令=0x{:02X}", command);
                        Ok(DeviceResponse::Error("数据错误".to_string()))
                    }
                    0x03 => {
                        warn!("不支持的命令: 0x{:02X}", command);
                        Ok(DeviceResponse::Error("不支持的命令".to_string()))
                    }
                    0x04 => {
                        warn!("参数错误: 命令=0x{:02X}", command);
                        Ok(DeviceResponse::Error("参数错误".to_string()))
                    }
                    _ => {
                        warn!("未知状态码: 0x{:02X}, 命令=0x{:02X}", status, command);
                        Ok(DeviceResponse::Error(format!(
                            "未知状态码: 0x{:02X}",
                            status
                        )))
                    }
                }
            }

            // 参数响应
            0x06 => {
                debug!("收到参数数据: {} 字节", frame.data.len());
                Ok(DeviceResponse::Parameters(frame.data))
            }

            // 通道数据响应
            0x07 | 0x08 | 0x09 | 0x0A | 0x0B | 0x0C => {
                debug!(
                    "收到数据: {} 字节, 功能码=0x{:02X}",
                    frame.data.len(),
                    frame.function
                );
                Ok(DeviceResponse::Data(frame.data))
            }

            // 对于其他功能码，作为自定义帧处理
            _ => {
                debug!("未知功能码: 0x{:02X}", frame.function);
                Ok(DeviceResponse::Custom(frame))
            }
        }
    }

    /// 检查响应是否表示成功
    pub fn is_success(&self) -> bool {
        match self {
            DeviceResponse::Ack { status, .. } => *status == 0,
            DeviceResponse::Parameters(_) => true,
            DeviceResponse::Data(_) => true,
            DeviceResponse::Error(_) => false,
            DeviceResponse::Custom(_) => true, // 假设自定义响应默认为成功
        }
    }

    /// 从响应中提取数据
    pub fn extract_data(&self) -> Option<&Bytes> {
        match self {
            DeviceResponse::Parameters(data) | DeviceResponse::Data(data) => Some(data),
            DeviceResponse::Custom(frame) => Some(&frame.data),
            _ => None,
        }
    }

    /// 获取错误消息（如果是错误响应）
    pub fn error_message(&self) -> Option<&str> {
        match self {
            DeviceResponse::Error(msg) => Some(msg),
            _ => None,
        }
    }

    /// 获取响应的描述信息
    pub fn description(&self) -> &'static str {
        match self {
            DeviceResponse::Ack { .. } => "确认响应",
            DeviceResponse::Parameters(_) => "参数响应",
            DeviceResponse::Data(_) => "数据响应",
            DeviceResponse::Error(_) => "错误响应",
            DeviceResponse::Custom(_) => "自定义响应",
        }
    }
}
