//! 简单Canvas接口
//!
//! 提供统一的、简化的绘制接口，专注于基础绘制操作。
//! 渲染器只需要实现这个接口，无需了解复杂的图表逻辑。

use crate::{
    Bounds, CircleStyle, Color, DrawBatch, LineStyle, PathCommand, PathStyle, Point,
    PointShape, PointStyle, PolygonStyle, RectStyle, TextStyle,
};
use crate::draw_commands::DrawCommand;

/// 简单Canvas特征
///
/// 所有渲染后端都应该实现这个特征
pub trait SimpleCanvas {
    type Error;

    /// 开始绘制会话
    fn begin_draw(&mut self, bounds: Bounds) -> Result<(), Self::Error>;

    /// 结束绘制会话
    fn end_draw(&mut self) -> Result<(), Self::Error>;

    /// 绘制线段
    fn draw_line(&mut self, from: Point, to: Point, style: &LineStyle) -> Result<(), Self::Error>;

    /// 绘制圆形
    fn draw_circle(
        &mut self,
        center: Point,
        radius: f64,
        style: &CircleStyle,
    ) -> Result<(), Self::Error>;

    /// 绘制文本
    fn draw_text(
        &mut self,
        text: &str,
        position: Point,
        style: &TextStyle,
    ) -> Result<(), Self::Error>;

    /// 绘制路径
    fn draw_path(&mut self, commands: &[PathCommand], style: &PathStyle)
        -> Result<(), Self::Error>;

    /// 绘制矩形
    fn draw_rect(&mut self, bounds: Bounds, style: &RectStyle) -> Result<(), Self::Error>;

    /// 绘制多边形
    fn draw_polygon(&mut self, points: &[Point], style: &PolygonStyle) -> Result<(), Self::Error>;

    /// 批量绘制点（性能优化）
    fn draw_points(&mut self, points: &[Point], style: &PointStyle) -> Result<(), Self::Error>;

    /// 批量绘制线段（性能优化）
    fn draw_lines(
        &mut self,
        segments: &[(Point, Point)],
        style: &LineStyle,
    ) -> Result<(), Self::Error>;

    /// 设置裁剪区域
    fn set_clip(&mut self, bounds: Option<Bounds>) -> Result<(), Self::Error>;

    /// 清空画布
    fn clear(&mut self, color: Option<Color>) -> Result<(), Self::Error>;

    /// 获取文本尺寸（用于布局计算）
    fn measure_text(&self, text: &str, style: &TextStyle) -> Result<(f64, f64), Self::Error>;
}

/// Canvas渲染器
///
/// 负责执行绘制指令，是渲染系统的核心
pub struct CanvasRenderer<C: SimpleCanvas> {
    canvas: C,
    current_clip: Option<Bounds>,
    stats: RenderStats,
}

/// 渲染统计信息
#[derive(Debug, Clone, Default)]
pub struct RenderStats {
    pub total_commands: usize,
    pub lines_drawn: usize,
    pub circles_drawn: usize,
    pub texts_drawn: usize,
    pub paths_drawn: usize,
    pub rects_drawn: usize,
    pub polygons_drawn: usize,
    pub points_drawn: usize,
    pub render_time_ms: f64,
}

impl<C: SimpleCanvas> CanvasRenderer<C> {
    /// 创建新的Canvas渲染器
    pub fn new(canvas: C) -> Self {
        Self {
            canvas,
            current_clip: None,
            stats: RenderStats::default(),
        }
    }

    /// 渲染绘制批次
    pub fn render_batches(
        &mut self,
        batches: &[DrawBatch],
        bounds: Bounds,
    ) -> Result<(), C::Error> {
        let start_time = std::time::Instant::now();

        self.canvas.begin_draw(bounds)?;

        // 按Z-index排序批次
        let mut sorted_batches = batches.to_vec();
        sorted_batches.sort_by_key(|batch| batch.z_index);

        for batch in &sorted_batches {
            if !batch.visible {
                continue;
            }

            self.render_batch(batch)?;
        }

        self.canvas.end_draw()?;

        self.stats.render_time_ms = start_time.elapsed().as_secs_f64() * 1000.0;

        Ok(())
    }

    /// 渲染单个批次
    pub fn render_batch(&mut self, batch: &DrawBatch) -> Result<(), C::Error> {
        for command in &batch.commands {
            self.render_command(command)?;
        }
        Ok(())
    }

    /// 渲染单个指令
    pub fn render_command(&mut self, command: &DrawCommand) -> Result<(), C::Error> {
        self.stats.total_commands += 1;

        match command {
            DrawCommand::Line { from, to, style } => {
                let converted_style: crate::style::LineStyle = style.clone().into();
                self.canvas.draw_line(*from, *to, &converted_style)?;
                self.stats.lines_drawn += 1;
            }

            DrawCommand::Circle {
                center,
                radius,
                style,
            } => {
                self.canvas.draw_circle(*center, *radius, style)?;
                self.stats.circles_drawn += 1;
            }

            DrawCommand::Text {
                text,
                position,
                style,
            } => {
                let converted_style: crate::style::TextStyle = style.clone().into();
                self.canvas.draw_text(text, *position, &converted_style)?;
                self.stats.texts_drawn += 1;
            }

            DrawCommand::Path { commands, style } => {
                self.canvas.draw_path(commands, style)?;
                self.stats.paths_drawn += 1;
            }

            DrawCommand::Rect { bounds, style } => {
                self.canvas.draw_rect(*bounds, style)?;
                self.stats.rects_drawn += 1;
            }

            DrawCommand::Polygon { points, style } => {
                self.canvas.draw_polygon(points, style)?;
                self.stats.polygons_drawn += 1;
            }

            DrawCommand::Points { points, style } => {
                self.canvas.draw_points(points, style)?;
                self.stats.points_drawn += points.len();
            }

            DrawCommand::Lines { segments, style } => {
                let converted_style: crate::style::LineStyle = style.clone().into();
                self.canvas.draw_lines(segments, &converted_style)?;
                self.stats.lines_drawn += segments.len();
            }
        }

        Ok(())
    }

    /// 设置裁剪区域
    pub fn set_clip(&mut self, bounds: Option<Bounds>) -> Result<(), C::Error> {
        self.current_clip = bounds;
        self.canvas.set_clip(bounds)
    }

    /// 清空画布
    pub fn clear(&mut self, color: Option<Color>) -> Result<(), C::Error> {
        self.stats = RenderStats::default();
        self.canvas.clear(color)
    }

    /// 获取渲染统计信息
    pub fn get_stats(&self) -> &RenderStats {
        &self.stats
    }

    /// 重置统计信息
    pub fn reset_stats(&mut self) {
        self.stats = RenderStats::default();
    }

    /// 获取Canvas引用
    pub fn canvas(&self) -> &C {
        &self.canvas
    }

    /// 获取Canvas可变引用
    pub fn canvas_mut(&mut self) -> &mut C {
        &mut self.canvas
    }
}

/// 批量渲染优化器
///
/// 将多个绘制指令合并为批量操作，提高性能
pub struct BatchOptimizer;

impl BatchOptimizer {
    /// 优化绘制批次
    pub fn optimize_batches(batches: &[DrawBatch]) -> Vec<DrawBatch> {
        let mut optimized = Vec::new();

        for batch in batches {
            let optimized_batch = Self::optimize_batch(batch);
            optimized.push(optimized_batch);
        }

        optimized
    }

    /// 优化单个批次
    pub fn optimize_batch(batch: &DrawBatch) -> DrawBatch {
        let mut optimized = DrawBatch::new();
        optimized.z_index = batch.z_index;
        optimized.visible = batch.visible;
        optimized.bounds = batch.bounds;

        // 收集相同类型的指令
        let mut lines = Vec::new();
        let mut points = Vec::new();
        let mut other_commands = Vec::new();

        for command in &batch.commands {
            match command {
                DrawCommand::Line { from, to, style } => {
                    lines.push((*from, *to, style.clone()));
                }
                DrawCommand::Circle {
                    center,
                    radius,
                    style: _,
                } if *radius <= 2.0 => {
                    // 小圆点可以作为点处理
                    points.push(*center);
                }
                _ => {
                    other_commands.push(command.clone());
                }
            }
        }

        // 批量处理线段
        if !lines.is_empty() {
            // 按样式分组
            let mut line_groups: std::collections::HashMap<
                String,
                (Vec<(Point, Point)>, crate::LineStyle),
            > = std::collections::HashMap::new();

            for (from, to, style) in lines {
                let key = format!("{:?}", style); // 简化的样式键
                line_groups
                    .entry(key)
                    .or_insert_with(|| (Vec::new(), style))
                    .0
                    .push((from, to));
            }

            for (_, (segments, style)) in line_groups {
                if segments.len() > 1 {
                    optimized.add_command(DrawCommand::Lines { segments, style });
                } else if let Some((from, to)) = segments.first() {
                    optimized.add_command(DrawCommand::Line {
                        from: *from,
                        to: *to,
                        style,
                    });
                }
            }
        }

        // 批量处理点
        if !points.is_empty() {
            optimized.add_command(DrawCommand::Points {
                points,
                style: PointStyle {
                    color: Color::rgb(0.0, 0.0, 0.0),
                    size: 2.0,
                    shape: PointShape::Circle,
                    opacity: 1.0,
                },
            });
        }

        // 添加其他指令
        for command in other_commands {
            optimized.add_command(command);
        }

        optimized
    }
}

/// 性能分析器
pub struct PerformanceProfiler {
    frame_times: Vec<f64>,
    max_frames: usize,
}

impl PerformanceProfiler {
    pub fn new(max_frames: usize) -> Self {
        Self {
            frame_times: Vec::new(),
            max_frames,
        }
    }

    pub fn record_frame_time(&mut self, time_ms: f64) {
        self.frame_times.push(time_ms);
        if self.frame_times.len() > self.max_frames {
            self.frame_times.remove(0);
        }
    }

    pub fn get_average_frame_time(&self) -> f64 {
        if self.frame_times.is_empty() {
            0.0
        } else {
            self.frame_times.iter().sum::<f64>() / self.frame_times.len() as f64
        }
    }

    pub fn get_fps(&self) -> f64 {
        let avg_time = self.get_average_frame_time();
        if avg_time > 0.0 {
            1000.0 / avg_time
        } else {
            0.0
        }
    }

    pub fn get_max_frame_time(&self) -> f64 {
        self.frame_times.iter().cloned().fold(0.0, f64::max)
    }

    pub fn get_min_frame_time(&self) -> f64 {
        self.frame_times
            .iter()
            .cloned()
            .fold(f64::INFINITY, f64::min)
    }
}
