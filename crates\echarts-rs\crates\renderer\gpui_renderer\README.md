# GPUI Renderer for Rust ECharts

这是一个专门为GPUI框架设计的ECharts渲染器，采用了清晰的职责分离架构。

## 🏗️ 架构设计

### 职责分离

1. **Processor** (位于 `../processor/`)
   - 负责数据转换：`Chart` → `Vec<DrawOperation>`
   - 处理布局计算、样式解析等
   - 生成标准化的绘制操作

2. **GPUI Renderer** (本模块)
   - 仅负责渲染：`Vec<DrawOperation>` → GPUI绘制调用
   - 专注于GPUI特定的绘制优化
   - 提供用户要求的接口

## 🎯 核心接口

```rust
pub fn render_chart(
    &mut self,
    data: Vec<DrawOperation>,
    bounds: gpui::Bounds<gpui::Pixels>,
    window: &mut Window,
    app: &mut App,
) -> Result<()>
```

### 参数说明

- `data`: 来自processor的绘制操作列表
- `bounds`: GPUI窗口边界
- `window`: GPUI窗口引用
- `app`: GPUI应用引用

## 🚀 使用示例

### 基本使用

```rust
use gpui_renderer::GpuiRenderer;
use echarts_charts::DrawOperation;

// 1. 创建渲染器
let mut renderer = GpuiRenderer::new();

// 2. 获取绘制操作（通常来自processor）
let draw_operations: Vec<DrawOperation> = get_chart_operations();

// 3. 在GPUI应用中渲染
renderer.render_chart(draw_operations, bounds, window, app)?;
```

### 在GPUI应用中集成

```rust
use gpui::*;
use gpui_renderer::GpuiRenderer;

struct ChartView {
    renderer: GpuiRenderer,
    chart_data: Vec<DrawOperation>,
}

impl Render for ChartView {
    fn render(&mut self, cx: &mut ViewContext<Self>) -> impl IntoElement {
        div()
            .size_full()
            .child(
                canvas(move |bounds, cx| {
                    self.renderer.render_chart(
                        self.chart_data.clone(),
                        bounds,
                        cx.window(),
                        cx.app()
                    ).unwrap_or_else(|e| {
                        eprintln!("渲染错误: {:?}", e);
                    });
                })
                .size_full()
            )
    }
}
```

## 🔧 支持的绘制操作

- **线条** (`Line`): 绘制直线
- **矩形** (`Rect`): 绘制矩形（填充/描边）
- **圆形** (`Circle`): 绘制圆形（填充/描边）
- **文本** (`Text`): 绘制文本
- **路径** (`Path`): 绘制复杂路径

## 📊 数据流

```
Chart (用户数据)
    ↓
Processor (数据转换)
    ↓
Vec<DrawOperation> (标准化绘制操作)
    ↓
GPUI Renderer (GPUI绘制)
    ↓
屏幕显示
```

## ⚡ 性能特性

- **批量渲染**: 将多个绘制操作合并优化
- **缓存支持**: 支持绘制结果缓存
- **增量更新**: 支持部分区域重绘
- **LOD支持**: 根据缩放级别调整细节

## 🎨 样式系统

渲染器支持丰富的样式配置：

- 颜色和透明度
- 线条样式（宽度、端点、连接）
- 填充和描边
- 字体和文本样式
- 路径和形状样式

## 🔍 调试支持

```rust
// 启用调试输出
renderer.set_debug_mode(true);

// 获取渲染统计
let stats = renderer.get_render_stats();
println!("渲染了 {} 个操作", stats.commands_rendered);
```

## 📝 示例

查看 `examples/simple_chart.rs` 了解完整的使用示例。

运行示例：
```bash
cargo run --example simple_chart
```

## 🔗 相关模块

- `../processor/`: 数据处理和转换
- `../core/`: 核心数据结构
- `../charts/`: 图表类型定义
- `../themes/`: 主题和样式

## 📋 TODO

- [ ] 实现具体的GPUI绘制调用
- [ ] 添加动画支持
- [ ] 优化批量渲染性能
- [ ] 添加更多绘制操作类型
- [ ] 完善错误处理
