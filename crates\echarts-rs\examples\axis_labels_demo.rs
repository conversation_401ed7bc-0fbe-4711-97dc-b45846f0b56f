//! 轴标签格式化演示
//!
//! 展示如何设置 XY 轴标签的小数位数和格式化选项

use echarts_rs::prelude::*;
use echarts_charts::{LineSeries, line::{LabelFormatType, AxisLabelConfig}};
use echarts_core::{Color, Chart};
use gpui::{
    self, div, px, rgb, size, AppContext, Context, IntoElement, ParentElement, Render,
    Styled, Window, WindowBounds, WindowKind, WindowOptions, TitlebarOptions,
    WindowBackgroundAppearance,
};
use gpui::Bounds as GpuiBounds;
use gpui_component::StyledExt;

fn main() {
    println!("🚀 启动轴标签格式化演示...");

    let app = gpui::Application::new();
    app.run(move |cx| {
        let window_size = size(px(1200.0), px(800.0));
        
        let window_options = WindowOptions {
            window_bounds: Some(WindowBounds::Windowed(GpuiBounds::centered(
                None,
                window_size,
                cx,
            ))),
            titlebar: Some(TitlebarOptions {
                title: Some("📊 轴标签格式化演示 - ECharts-rs".into()),
                appears_transparent: false,
                traffic_light_position: None,
            }),
            window_background: WindowBackgroundAppearance::Opaque,
            focus: true,
            show: true,
            kind: WindowKind::Normal,
            is_movable: true,
            display_id: None,
            app_id: Some("axis-labels-demo".to_string()),
            window_decorations: None,
            window_min_size: Some(size(px(800.0), px(600.0))),
        };
        
        cx.open_window(window_options, |window, cx| {
            println!("✅ 窗口已创建，正在初始化轴标签演示...");
            cx.new(|cx| AxisLabelsDemo::new())
        }).expect("无法创建窗口");
    });
}

/// 轴标签演示应用
struct AxisLabelsDemo {
    examples: Vec<ChartExample>,
}

/// 图表示例
struct ChartExample {
    title: String,
    description: String,
    chart: Chart,
}

impl AxisLabelsDemo {
    fn new() -> Self {
        println!("🎯 初始化轴标签演示...");

        let examples = vec![
            // 示例1：基本小数位数设置
            ChartExample {
                title: "基本小数位数设置".to_string(),
                description: "X轴显示2位小数，Y轴显示1位小数".to_string(),
                chart: {
                    let series = LineSeries::new("销售数据")
                        .data(vec![
                            (1.234, 20.5), (2.567, 35.8), (3.891, 25.2),
                            (4.123, 60.7), (5.456, 45.3), (6.789, 80.9)
                        ])
                        .color(Color::rgb(0.2, 0.6, 1.0))
                        .x_axis_decimal_places(2)  // X轴2位小数
                        .y_axis_decimal_places(1)  // Y轴1位小数
                        .line_width(3.0)
                        .show_symbols(true);

                    Chart::new()
                        .title("基本小数位数设置".to_string())
                        .add_series(Box::new(series))
                },
            },

            // 示例2：科学计数法
            ChartExample {
                title: "科学计数法格式".to_string(),
                description: "大数值使用科学计数法显示".to_string(),
                chart: {
                    let series = LineSeries::new_scientific("大数据", 2)
                        .data(vec![
                            (1000.0, 1234567.0), (2000.0, 2345678.0),
                            (3000.0, 1876543.0), (4000.0, 3456789.0),
                            (5000.0, 2987654.0), (6000.0, 4123456.0)
                        ])
                        .color(Color::rgb(1.0, 0.4, 0.2))
                        .line_width(2.5);

                    Chart::new()
                        .title("科学计数法格式".to_string())
                        .add_series(Box::new(series))
                },
            },

            // 示例3：百分比格式
            ChartExample {
                title: "百分比格式".to_string(),
                description: "Y轴显示为百分比格式".to_string(),
                chart: {
                    let series = LineSeries::new_percentage("转化率", 1)
                        .data(vec![
                            (1.0, 0.234), (2.0, 0.345), (3.0, 0.287),
                            (4.0, 0.456), (5.0, 0.398), (6.0, 0.512)
                        ])
                        .color(Color::rgb(0.2, 0.8, 0.4))
                        .x_axis_decimal_places(0)  // X轴整数
                        .line_width(2.0)
                        .area(true)
                        .area_opacity(0.3);

                    Chart::new()
                        .title("百分比格式".to_string())
                        .add_series(Box::new(series))
                },
            },

            // 示例4：自定义轴标签配置
            ChartExample {
                title: "自定义轴标签配置".to_string(),
                description: "旋转标签，自定义颜色和字体大小".to_string(),
                chart: {
                    let mut config = AxisLabelConfig::default();
                    config.x_axis_format = LabelFormatType::FixedDecimal(1);
                    config.y_axis_format = LabelFormatType::FixedDecimal(0);
                    config.x_label_rotation = 45.0;  // X轴标签旋转45度
                    config.font_size = 14.0;
                    config.color = Color::rgb(0.6, 0.3, 0.8);

                    let series = LineSeries::new("自定义样式")
                        .data(vec![
                            (10.5, 150.0), (20.8, 280.0), (30.2, 220.0),
                            (40.7, 380.0), (50.1, 320.0), (60.9, 450.0)
                        ])
                        .color(Color::rgb(0.8, 0.2, 0.6))
                        .axis_labels(config)
                        .line_width(2.5)
                        .smooth(true)
                        .smoothness(0.4);

                    Chart::new()
                        .title("自定义轴标签配置".to_string())
                        .add_series(Box::new(series))
                },
            },

            // 示例5：隐藏部分轴标签
            ChartExample {
                title: "隐藏部分轴标签".to_string(),
                description: "只显示Y轴标签，隐藏X轴标签".to_string(),
                chart: {
                    let series = LineSeries::new("简化显示")
                        .data(vec![
                            (1.0, 25.678), (2.0, 45.123), (3.0, 35.456),
                            (4.0, 65.789), (5.0, 55.234), (6.0, 75.567)
                        ])
                        .color(Color::rgb(0.9, 0.5, 0.1))
                        .show_axis_labels(false, true)  // 隐藏X轴，显示Y轴
                        .y_axis_decimal_places(3)  // Y轴3位小数
                        .line_width(2.0)
                        .symbol_size(8.0);

                    Chart::new()
                        .title("隐藏部分轴标签".to_string())
                        .add_series(Box::new(series))
                },
            },
        ];
        
        println!("📊 创建了 {} 个轴标签示例", examples.len());
        
        Self { examples }
    }
}

impl Render for AxisLabelsDemo {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("🎨 渲染轴标签演示界面...");
        
        div()
            .size_full()
            .bg(rgb(0xf8fafc))
            .flex()
            .flex_col()
            .child(
                // 标题栏
                div()
                    .w_full()
                    .h(px(80.0))
                    .bg(rgb(0x1f2937))
                    .flex()
                    .items_center()
                    .justify_center()
                    .child(
                        div()
                            .text_2xl()
                            .font_bold()
                            .text_color(rgb(0xffffff))
                            .child("📊 轴标签格式化功能演示")
                    )
            )
            .child(
                // 说明文字
                div()
                    .w_full()
                    .p_4()
                    .bg(rgb(0xe5e7eb))
                    .flex()
                    .justify_center()
                    .child(
                        div()
                            .text_sm()
                            .text_color(rgb(0x374151))
                            .child("✨ 展示如何设置XY轴标签的小数位数、科学计数法、百分比格式等功能")
                    )
            )
            .child(
                // 示例网格
                div()
                    .flex_1()
                    .p_6()
                    .child(
                        div()
                            .flex()
                            .flex_wrap()
                            .gap_6()
                            .children(
                                self.examples.iter().enumerate().map(|(i, example)| {
                                    self.render_example(i, example)
                                })
                            )
                    )
            )
    }
}

impl AxisLabelsDemo {
    fn render_example(&self, index: usize, example: &ChartExample) -> impl IntoElement {
        div()
            .w_full()
            .h(px(300.0))
            .bg(rgb(0xffffff))
            .border_1()
            .border_color(rgb(0xd1d5db))
            .rounded_lg()
            .p_4()
            .flex()
            .flex_col()
            .gap_3()
            .child(
                // 示例标题
                div()
                    .text_lg()
                    .font_semibold()
                    .text_color(rgb(0x1f2937))
                    .child(format!("{}. {}", index + 1, example.title))
            )
            .child(
                // 示例描述
                div()
                    .text_sm()
                    .text_color(rgb(0x6b7280))
                    .child(example.description.clone())
            )
            .child(
                // 图表区域（占位符）
                div()
                    .flex_1()
                    .bg(rgb(0xf9fafb))
                    .border_1()
                    .border_color(rgb(0xe5e7eb))
                    .rounded_md()
                    .flex()
                    .items_center()
                    .justify_center()
                    .child(
                        div()
                            .text_sm()
                            .text_color(rgb(0x9ca3af))
                            .child("📈 图表渲染区域")
                    )
            )
            .child(
                // 配置信息
                div()
                    .text_xs()
                    .text_color(rgb(0x9ca3af))
                    .child(self.get_config_info(&example.chart))
            )
    }
    
    fn get_config_info(&self, chart: &Chart) -> String {
        if let Some(series) = chart.series.first() {
            format!(
                "系列: {}, 数据点: {}",
                series.name(),
                "N/A" // 暂时无法直接访问数据长度
            )
        } else {
            "无数据".to_string()
        }
    }
}
