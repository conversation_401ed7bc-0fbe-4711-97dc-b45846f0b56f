[package]
name = "rust-echarts-wasm"
version = "0.1.0"
edition = "2021"
description = "WebAssembly bindings for ECharts-rs"
license = "MIT"
repository = "https://github.com/your-repo/echarts-rs"

[lib]
crate-type = ["cdylib"]

[dependencies]
rust-echarts-core = { path = "../core" }
rust-echarts-charts = { path = "../charts" }
rust-echarts-components = { path = "../components" }
rust-echarts-themes = { path = "../themes" }
rust-echarts-svg-renderer = { path = "../svg-renderer" }

# WebAssembly dependencies
wasm-bindgen = "0.2"
wasm-bindgen-futures = "0.4"
js-sys = "0.3"
console_error_panic_hook = "0.1"
wee_alloc = "0.4"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde-wasm-bindgen = "0.4"

# Web APIs
web-sys = { version = "0.3", features = [
  "console",
  "Document",
  "Element",
  "HtmlElement",
  "HtmlCanvasElement",
  "CanvasRenderingContext2d",
  "Window",
  "Performance",
  "DomRect",
  "Event",
  "MouseEvent",
  "TouchEvent",
  "KeyboardEvent",
] }

[dev-dependencies]
wasm-bindgen-test = "0.3"
