# ECharts-rs 增强功能演示指南

本指南将帮助您快速体验 ECharts-rs 的新增交互功能。

## 🚀 快速开始

### 运行演示
```bash
cd crates/echarts-rs
cargo run --example simple_gpui_line_chart --features gpui-renderer
```

## 🎮 交互功能演示

### 1. 基本操作演示

#### 十字线功能
- **激活**: 鼠标移动到图表区域
- **切换**: 按 `C` 键开启/关闭十字线
- **信息显示**: 十字线会显示当前位置的坐标和最近数据点信息

#### 帮助系统
- **显示帮助**: 按 `H` 键打开快捷键帮助面板
- **关闭帮助**: 再次按 `H` 键或点击其他区域关闭

### 2. 缩放功能演示

#### 鼠标滚轮缩放
1. 将鼠标移动到图表区域
2. 向上滚动鼠标滚轮 → 放大图表
3. 向下滚动鼠标滚轮 → 缩小图表
4. 缩放中心点为鼠标位置

#### 键盘缩放
- 按 `+` 或 `=` 键 → 放大图表
- 按 `-` 键 → 缩小图表
- 按 `R` 键 → 重置缩放到默认状态

#### 框选缩放
1. 按 `Z` 键切换到缩放模式（状态栏显示 "模式: Zoom"）
2. 在图表区域按住鼠标左键拖拽选择区域
3. 释放鼠标，图表会缩放到选中区域
4. 按 `Esc` 键返回默认模式

### 3. 平移功能演示

#### 拖拽平移
1. 按 `P` 键切换到平移模式（状态栏显示 "模式: Pan"）
2. 在图表区域按住鼠标左键拖拽
3. 图表会跟随鼠标移动
4. 按 `Esc` 键返回默认模式

#### 键盘平移
- `Shift + ↑` → 向上平移
- `Shift + ↓` → 向下平移
- `Shift + ←` → 向左平移
- `Shift + →` → 向右平移

### 4. 数据导航演示

#### 数据集切换
- 按 `←` 键 → 切换到上一个数据集
- 按 `→` 键 → 切换到下一个数据集
- 按数字键 `1-3` → 快速切换到对应数据集

#### 侧边栏操作
- 点击侧边栏中的数据集项目直接切换
- 观察数据集名称和描述的变化
- 注意图表类型图标的变化

### 5. 高级功能演示

#### 选择模式
1. 按 `S` 键切换到选择模式
2. 拖拽鼠标创建选择区域
3. 观察选中的数据点高亮显示

#### 光标精确控制
- `Ctrl + ←` → 光标左移
- `Ctrl + →` → 光标右移
- `Ctrl + ↑` → 光标上移
- `Ctrl + ↓` → 光标下移
- `Home` → 跳转到第一个数据点
- `End` → 跳转到最后一个数据点

## 📊 状态信息观察

### 状态栏信息
在图表底部的状态栏中观察：
- **当前模式**: Default/Zoom/Pan/Select
- **缩放级别**: 显示当前缩放倍数（如 1.0x, 1.5x）
- **操作提示**: 显示可用的操作方式

### 十字线信息面板
当十字线激活时，观察蓝色信息面板：
- **坐标信息**: 显示 X、Y 轴坐标值
- **数据点信息**: 显示最近数据点的详细信息
- **系列名称**: 显示数据所属的系列

### 统计卡片
观察图表下方的统计信息：
- **最大值**: 当前数据集的最大值
- **最小值**: 当前数据集的最小值
- **平均值**: 当前数据集的平均值
- **数据点数**: 当前数据集的数据点总数

## 🎯 测试场景

### 场景1: 数据探索
1. 启动应用，观察默认的月度销售额数据
2. 按 `C` 键开启十字线，移动鼠标查看各月数据
3. 使用鼠标滚轮放大查看特定月份的详细信息
4. 按 `2` 键切换到用户增长数据集
5. 按 `3` 键切换到温度变化数据集

### 场景2: 精确分析
1. 按 `Z` 键进入缩放模式
2. 拖拽选择感兴趣的数据区域进行放大
3. 按 `P` 键进入平移模式，拖拽调整视图位置
4. 使用 `Ctrl + 方向键` 精确移动十字线到特定数据点
5. 按 `R` 键重置视图

### 场景3: 快捷操作
1. 按 `H` 键查看完整的快捷键列表
2. 尝试各种快捷键组合
3. 观察状态栏的实时更新
4. 使用数字键快速在数据集间切换

## 🔧 故障排除

### 常见问题
1. **图表不响应鼠标操作**
   - 确保鼠标在图表绘制区域内
   - 检查当前交互模式是否正确

2. **缩放功能不工作**
   - 确认已启用缩放功能
   - 检查是否达到缩放限制

3. **快捷键不响应**
   - 确保应用窗口处于焦点状态
   - 检查是否有按键冲突

### 性能提示
- 在大数据集上，缩放和平移可能会有轻微延迟
- 频繁的鼠标移动可能影响十字线更新性能
- 建议在合理的缩放范围内操作

## 📝 反馈和建议

如果您在使用过程中发现问题或有改进建议，请：
1. 记录具体的操作步骤
2. 注意观察控制台输出
3. 提供系统环境信息
4. 描述期望的行为

通过这些演示，您可以充分体验 ECharts-rs 的强大交互功能！
