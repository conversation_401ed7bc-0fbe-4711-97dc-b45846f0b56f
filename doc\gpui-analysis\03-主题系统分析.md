# GPUI Component 主题系统分析

## 主题系统概述

GPUI Component 提供了完整的主题系统，支持明暗主题切换、自定义颜色方案和响应式样式，为桌面应用提供一致的视觉体验。

## 主题架构

### 1. 核心主题结构

```rust
#[derive(Debug, Clone, Serialize, Deserialize, JsonSchema)]
pub struct Theme {
    pub colors: ThemeColor,           // 当前主题颜色
    pub light_theme: ThemeColor,      // 浅色主题
    pub dark_theme: ThemeColor,       // 深色主题
    pub highlight_theme: Arc<HighlightTheme>,  // 语法高亮主题
    pub light_highlight_theme: Arc<HighlightTheme>,
    pub dark_highlight_theme: Arc<HighlightTheme>,
    
    pub mode: ThemeMode,              // 主题模式
    pub font_family: SharedString,    // 字体族
    pub font_size: Pixels,            // 字体大小
    pub radius: Pixels,               // 圆角半径
    pub radius_lg: Pixels,            // 大圆角半径
    pub shadow: bool,                 // 阴影开关
    pub transparent: Hsla,            // 透明色
    pub scrollbar_show: ScrollbarShow, // 滚动条显示模式
    pub tile_grid_size: Pixels,       // 网格大小
    pub tile_shadow: bool,            // 网格阴影
}
```

### 2. 主题模式

```rust
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ThemeMode {
    Light,  // 浅色模式
    Dark,   // 深色模式
    Auto,   // 自动跟随系统
}
```

### 3. 颜色系统

```rust
#[derive(Debug, Default, Clone, Copy, Serialize, Deserialize, JsonSchema)]
pub struct ThemeColor {
    // 基础颜色
    pub background: Hsla,             // 背景色
    pub foreground: Hsla,             // 前景色
    pub border: Hsla,                 // 边框色
    
    // 主要颜色
    pub primary: Hsla,                // 主色
    pub primary_foreground: Hsla,     // 主色前景
    pub primary_hover: Hsla,          // 主色悬停
    pub primary_active: Hsla,         // 主色激活
    
    // 次要颜色
    pub secondary: Hsla,              // 次要色
    pub secondary_foreground: Hsla,   // 次要色前景
    pub secondary_hover: Hsla,        // 次要色悬停
    pub secondary_active: Hsla,       // 次要色激活
    
    // 状态颜色
    pub danger: Hsla,                 // 危险色
    pub danger_foreground: Hsla,      // 危险色前景
    pub warning: Hsla,                // 警告色
    pub warning_foreground: Hsla,     // 警告色前景
    pub success: Hsla,                // 成功色
    pub success_foreground: Hsla,     // 成功色前景
    pub info: Hsla,                   // 信息色
    pub info_foreground: Hsla,        // 信息色前景
    
    // 组件特定颜色
    pub card: Hsla,                   // 卡片背景
    pub card_foreground: Hsla,        // 卡片前景
    pub popover: Hsla,                // 弹出框背景
    pub popover_foreground: Hsla,     // 弹出框前景
    pub muted: Hsla,                  // 静音背景
    pub muted_foreground: Hsla,       // 静音前景
    pub accent: Hsla,                 // 强调色
    pub accent_foreground: Hsla,      // 强调色前景
    
    // 表格颜色
    pub table_head: Hsla,             // 表头背景
    pub table_head_foreground: Hsla,  // 表头前景
    pub table_row_even: Hsla,         // 偶数行背景
    pub table_row_hover: Hsla,        // 行悬停背景
    pub table_row_active: Hsla,       // 行激活背景
    
    // 图表颜色
    pub chart_1: Hsla,                // 图表色1
    pub chart_2: Hsla,                // 图表色2
    pub chart_3: Hsla,                // 图表色3
    pub chart_4: Hsla,                // 图表色4
    pub chart_5: Hsla,                // 图表色5
    
    // 更多组件颜色...
}
```

## 主题使用机制

### 1. 全局主题访问

```rust
// 主题特征
pub trait ActiveTheme {
    fn theme(&self) -> &Theme;
}

impl ActiveTheme for App {
    fn theme(&self) -> &Theme {
        Theme::global(self)
    }
}

// 在组件中使用
impl RenderOnce for Button {
    fn render(self, cx: &mut WindowContext) -> impl IntoElement {
        div()
            .bg(cx.theme().primary)
            .text_color(cx.theme().primary_foreground)
            .border_color(cx.theme().border)
    }
}
```

### 2. 主题切换

```rust
impl Theme {
    /// 切换到浅色主题
    pub fn set_light(&mut self) {
        self.mode = ThemeMode::Light;
        self.colors = self.light_theme;
    }
    
    /// 切换到深色主题
    pub fn set_dark(&mut self) {
        self.mode = ThemeMode::Dark;
        self.colors = self.dark_theme;
    }
    
    /// 自动跟随系统主题
    pub fn set_auto(&mut self, cx: &mut App) {
        self.mode = ThemeMode::Auto;
        self.sync_system_appearance(None, cx);
    }
}
```

### 3. 系统主题同步

```rust
impl Theme {
    pub fn sync_system_appearance(
        appearance: Option<WindowAppearance>, 
        cx: &mut App
    ) {
        let appearance = appearance.unwrap_or_else(|| {
            cx.window_appearance()
        });
        
        let theme = Theme::global_mut(cx);
        if theme.mode == ThemeMode::Auto {
            match appearance {
                WindowAppearance::Light => theme.colors = theme.light_theme,
                WindowAppearance::Dark => theme.colors = theme.dark_theme,
            }
        }
    }
}
```

## 主题配置文件

### 1. JSON 主题格式

```json
{
  "$schema": "https://github.com/longbridge/gpui-component/raw/refs/heads/main/.theme-schema.json",
  "name": "Ayu Light",
  "author": "ayu-theme",
  "url": "https://github.com/ayu-theme",
  "themes": [
    {
      "name": "Ayu Light",
      "mode": "light",
      "colors": {
        "background": "#fafafa",
        "foreground": "#5C6773",
        "border": "#E6E8EA",
        "primary.background": "#55B4D4",
        "primary.foreground": "#FAFAFA",
        "danger.background": "#F51818",
        "success.background": "#86B300",
        "warning.background": "#FF9940",
        "chart.1": "#55B4D4",
        "chart.2": "#86B300",
        "chart.3": "#FF9940",
        "chart.4": "#F51818",
        "chart.5": "#A37ACC"
      }
    }
  ]
}
```

### 2. 内置主题

GPUI Component 提供了多个内置主题：

- **ayu.json**: Ayu 主题（浅色/深色）
- **catppuccin.json**: Catppuccin 主题
- **gruvbox.json**: Gruvbox 主题
- **solarized.json**: Solarized 主题
- **tokyonight.json**: Tokyo Night 主题
- **macos-classic.json**: macOS 经典主题

### 3. 主题加载

```rust
// 从文件加载主题
pub fn load_theme_from_file(path: &Path) -> Result<Theme> {
    let content = std::fs::read_to_string(path)?;
    let theme_config: ThemeConfig = serde_json::from_str(&content)?;
    Ok(Theme::from(theme_config))
}

// 从内置主题加载
pub fn load_builtin_theme(name: &str) -> Option<Theme> {
    match name {
        "ayu" => Some(Theme::ayu()),
        "catppuccin" => Some(Theme::catppuccin()),
        "gruvbox" => Some(Theme::gruvbox()),
        _ => None,
    }
}
```

## 样式系统集成

### 1. 颜色扩展

```rust
pub trait Colorize: Styled {
    /// 设置主色背景
    fn primary(mut self, cx: &App) -> Self {
        self.bg(cx.theme().primary)
    }
    
    /// 设置危险色背景
    fn danger(mut self, cx: &App) -> Self {
        self.bg(cx.theme().danger)
    }
    
    /// 设置成功色背景
    fn success(mut self, cx: &App) -> Self {
        self.bg(cx.theme().success)
    }
}
```

### 2. 主题感知组件

```rust
pub trait ThemeAware {
    fn apply_theme(&mut self, theme: &Theme);
}

impl ThemeAware for Button {
    fn apply_theme(&mut self, theme: &Theme) {
        match self.variant {
            ButtonVariant::Primary => {
                self.background_color = theme.primary;
                self.text_color = theme.primary_foreground;
            }
            ButtonVariant::Danger => {
                self.background_color = theme.danger;
                self.text_color = theme.danger_foreground;
            }
            // ...
        }
    }
}
```

## 响应式主题

### 1. 尺寸响应

```rust
pub trait SizeAware {
    fn apply_size(&mut self, size: Size, theme: &Theme);
}

impl SizeAware for Button {
    fn apply_size(&mut self, size: Size, theme: &Theme) {
        match size {
            Size::XSmall => {
                self.padding = theme.spacing_xs;
                self.font_size = theme.font_size_xs;
            }
            Size::Small => {
                self.padding = theme.spacing_sm;
                self.font_size = theme.font_size_sm;
            }
            // ...
        }
    }
}
```

### 2. 状态响应

```rust
pub trait StateAware {
    fn apply_state(&mut self, state: ComponentState, theme: &Theme);
}

#[derive(Debug, Clone, Copy)]
pub enum ComponentState {
    Normal,
    Hover,
    Active,
    Disabled,
    Focus,
}
```

## 语法高亮主题

### 1. 高亮主题结构

```rust
pub struct HighlightTheme {
    pub keyword: Hsla,
    pub string: Hsla,
    pub number: Hsla,
    pub comment: Hsla,
    pub function: Hsla,
    pub variable: Hsla,
    pub type_name: Hsla,
    pub operator: Hsla,
    // ...
}
```

### 2. 代码编辑器集成

```rust
impl CodeEditor {
    fn render_with_highlight(&self, cx: &mut WindowContext) -> impl IntoElement {
        let highlight_theme = &cx.theme().highlight_theme;
        
        div()
            .child(
                syntax_highlighted_text(
                    &self.content,
                    &self.language,
                    highlight_theme
                )
            )
    }
}
```

## 主题定制

### 1. 自定义主题创建

```rust
pub fn create_custom_theme() -> Theme {
    let mut theme = Theme::default();
    
    // 自定义颜色
    theme.colors.primary = hsla(210.0 / 360.0, 1.0, 0.5, 1.0);
    theme.colors.secondary = hsla(120.0 / 360.0, 0.8, 0.6, 1.0);
    
    // 自定义字体
    theme.font_family = "JetBrains Mono".into();
    theme.font_size = px(14.0);
    
    // 自定义圆角
    theme.radius = px(8.0);
    theme.radius_lg = px(12.0);
    
    theme
}
```

### 2. 主题变量系统

```rust
pub struct ThemeVariables {
    pub spacing_xs: Pixels,
    pub spacing_sm: Pixels,
    pub spacing_md: Pixels,
    pub spacing_lg: Pixels,
    pub spacing_xl: Pixels,
    
    pub font_size_xs: Pixels,
    pub font_size_sm: Pixels,
    pub font_size_md: Pixels,
    pub font_size_lg: Pixels,
    pub font_size_xl: Pixels,
}
```

## 性能优化

### 1. 主题缓存

```rust
pub struct ThemeCache {
    computed_styles: HashMap<String, ComputedStyle>,
    color_cache: HashMap<String, Hsla>,
}

impl ThemeCache {
    pub fn get_or_compute_style(&mut self, key: &str, compute: impl FnOnce() -> ComputedStyle) -> &ComputedStyle {
        self.computed_styles.entry(key.to_string()).or_insert_with(compute)
    }
}
```

### 2. 增量更新

```rust
impl Theme {
    pub fn update_colors(&mut self, updates: HashMap<String, Hsla>) {
        for (key, color) in updates {
            self.apply_color_update(&key, color);
        }
        self.invalidate_cache();
    }
    
    fn invalidate_cache(&mut self) {
        // 清除相关缓存
    }
}
```

## 与 ECharts 集成的主题映射

### 1. 颜色映射

```rust
pub fn map_theme_to_echarts(theme: &Theme) -> EChartsTheme {
    EChartsTheme {
        background_color: theme.background,
        text_style: TextStyle {
            color: theme.foreground,
            font_family: theme.font_family.clone(),
            font_size: theme.font_size.0 as u32,
        },
        color_palette: vec![
            theme.chart_1,
            theme.chart_2,
            theme.chart_3,
            theme.chart_4,
            theme.chart_5,
        ],
        axis_style: AxisStyle {
            line_color: theme.border,
            label_color: theme.muted_foreground,
        },
        // ...
    }
}
```

### 2. 主题同步

```rust
pub struct ThemeSync {
    gpui_theme: Theme,
    echarts_theme: EChartsTheme,
}

impl ThemeSync {
    pub fn sync_themes(&mut self) {
        self.echarts_theme = map_theme_to_echarts(&self.gpui_theme);
        self.apply_to_echarts();
    }
    
    fn apply_to_echarts(&self) {
        // 应用主题到 ECharts 实例
    }
}
```

这个主题系统为 GPUI Component 提供了强大的视觉定制能力，特别适合与 ECharts 这样的可视化库进行主题统一。
