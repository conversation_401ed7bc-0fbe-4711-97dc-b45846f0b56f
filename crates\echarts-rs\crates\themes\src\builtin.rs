//! Built-in themes

use crate::Theme;
use echarts_core::*;

impl Theme {
    /// Vintage theme with warm colors
    pub fn vintage() -> Self {
        Theme {
            name: "vintage".to_string(),
            color_palette: vec![
                Color::from_hex("#d87c7c").unwrap(),
                Color::from_hex("#919e8b").unwrap(),
                Color::from_hex("#d7ab82").unwrap(),
                Color::from_hex("#6e7074").unwrap(),
                Color::from_hex("#61a0a8").unwrap(),
                Color::from_hex("#efa18d").unwrap(),
                Color::from_hex("#787464").unwrap(),
                Color::from_hex("#cc7e63").unwrap(),
                Color::from_hex("#724e58").unwrap(),
                Color::from_hex("#4b565b").unwrap(),
            ],
            background_color: Color::from_hex("#fef8ef").unwrap(),
            text_style: TextStyle {
                color: Color::from_hex("#333333").unwrap(),
                ..Default::default()
            },
            line_style: LineStyle::default(),
            custom: std::collections::HashMap::new(),
        }
    }

    /// Macarons theme with pastel colors
    pub fn macarons() -> Self {
        Theme {
            name: "macarons".to_string(),
            color_palette: vec![
                Color::from_hex("#2ec7c9").unwrap(),
                Color::from_hex("#b6a2de").unwrap(),
                Color::from_hex("#5ab1ef").unwrap(),
                Color::from_hex("#ffb980").unwrap(),
                Color::from_hex("#d87a80").unwrap(),
                Color::from_hex("#8d98b3").unwrap(),
                Color::from_hex("#e5cf0d").unwrap(),
                Color::from_hex("#97b552").unwrap(),
                Color::from_hex("#95706d").unwrap(),
                Color::from_hex("#dc69aa").unwrap(),
            ],
            background_color: Color::WHITE,
            text_style: TextStyle {
                color: Color::from_hex("#333333").unwrap(),
                ..Default::default()
            },
            line_style: LineStyle::default(),
            custom: std::collections::HashMap::new(),
        }
    }

    /// Infographic theme with bright colors
    pub fn infographic() -> Self {
        Theme {
            name: "infographic".to_string(),
            color_palette: vec![
                Color::from_hex("#C1232B").unwrap(),
                Color::from_hex("#B5C334").unwrap(),
                Color::from_hex("#FCCE10").unwrap(),
                Color::from_hex("#E87C25").unwrap(),
                Color::from_hex("#27727B").unwrap(),
                Color::from_hex("#FE8463").unwrap(),
                Color::from_hex("#9BCA63").unwrap(),
                Color::from_hex("#FAD860").unwrap(),
                Color::from_hex("#F3A43B").unwrap(),
                Color::from_hex("#60C0DD").unwrap(),
            ],
            background_color: Color::WHITE,
            text_style: TextStyle {
                color: Color::from_hex("#333333").unwrap(),
                ..Default::default()
            },
            line_style: LineStyle::default(),
            custom: std::collections::HashMap::new(),
        }
    }

    /// Shine theme with metallic colors
    pub fn shine() -> Self {
        Theme {
            name: "shine".to_string(),
            color_palette: vec![
                Color::from_hex("#c12e34").unwrap(),
                Color::from_hex("#e6b600").unwrap(),
                Color::from_hex("#0098d9").unwrap(),
                Color::from_hex("#2b821d").unwrap(),
                Color::from_hex("#005eaa").unwrap(),
                Color::from_hex("#339ca8").unwrap(),
                Color::from_hex("#cda819").unwrap(),
                Color::from_hex("#32a487").unwrap(),
            ],
            background_color: Color::from_hex("#1b1b1b").unwrap(),
            text_style: TextStyle {
                color: Color::WHITE,
                ..Default::default()
            },
            line_style: LineStyle::default(),
            custom: std::collections::HashMap::new(),
        }
    }

    /// Roma theme with elegant colors
    pub fn roma() -> Self {
        Theme {
            name: "roma".to_string(),
            color_palette: vec![
                Color::from_hex("#E01F54").unwrap(),
                Color::from_hex("#001852").unwrap(),
                Color::from_hex("#f5e8a3").unwrap(),
                Color::from_hex("#b8d2c7").unwrap(),
                Color::from_hex("#c6b38e").unwrap(),
                Color::from_hex("#a4d8c2").unwrap(),
                Color::from_hex("#f3d999").unwrap(),
                Color::from_hex("#d3758f").unwrap(),
                Color::from_hex("#dcc392").unwrap(),
                Color::from_hex("#2e4783").unwrap(),
            ],
            background_color: Color::WHITE,
            text_style: TextStyle {
                color: Color::from_hex("#333333").unwrap(),
                ..Default::default()
            },
            line_style: LineStyle::default(),
            custom: std::collections::HashMap::new(),
        }
    }
}
