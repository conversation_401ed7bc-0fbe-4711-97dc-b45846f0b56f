//! 新架构演示 - 展示 EXECUTION_PLAN.md 方案2的实现
//! 
//! 这个示例展示了如何使用新的 ECharts-rs 架构：
//! 1. 使用 charts crate 中的真实 Series 实现
//! 2. 通过 RuntimeChart 和 ChartBuilder 进行图表构建
//! 3. 统一的渲染系统

use echarts_rs::{RuntimeChart, ChartBuilder};
use echarts_charts::{LineSeries, BarSeries, PieSeries, ScatterSeries};
use echarts_core::{Bounds, Point, Color, ChartRenderer, Result};

/// 简单的控制台渲染器，用于演示
struct ConsoleRenderer;

impl ChartRenderer for ConsoleRenderer {
    fn draw_rect(&mut self, bounds: Bounds, fill: Option<Color>, stroke: Option<Color>, stroke_width: f64) -> Result<()> {
        println!("🔲 绘制矩形: bounds={:?}, fill={:?}, stroke={:?}, width={}", 
                 bounds, fill, stroke, stroke_width);
        Ok(())
    }

    fn draw_circle(&mut self, center: Point, radius: f64, fill: Option<Color>, stroke: Option<Color>, stroke_width: f64) -> Result<()> {
        println!("⭕ 绘制圆形: center={:?}, radius={}, fill={:?}, stroke={:?}, width={}", 
                 center, radius, fill, stroke, stroke_width);
        Ok(())
    }

    fn draw_text(&mut self, text: &str, position: Point, style: &echarts_core::TextStyle) -> Result<()> {
        println!("📝 绘制文本: '{}' at {:?}, style={:?}", text, position, style);
        Ok(())
    }

    fn draw_path(&mut self, path: &echarts_core::Path, fill: Option<Color>, stroke: Option<(Color, f64)>) -> Result<()> {
        println!("🛤️ 绘制路径: fill={:?}, stroke={:?}", fill, stroke);
        Ok(())
    }

    fn capabilities(&self) -> echarts_core::RendererCapabilities {
        echarts_core::RendererCapabilities {
            supports_animation: false,
            supports_interaction: false,
            supports_text_measurement: false,
            max_texture_size: None,
        }
    }
}

fn main() -> Result<()> {
    println!("🚀 ECharts-rs 新架构演示");
    println!("📋 实现方案：EXECUTION_PLAN.md 方案2 - 集成真实的 charts 实现");
    println!();

    // 演示1: 使用 RuntimeChart 直接构建
    println!("📊 演示1: 使用 RuntimeChart 直接构建折线图");
    demo_runtime_chart()?;
    println!();

    // 演示2: 使用 ChartBuilder 构建
    println!("📊 演示2: 使用 ChartBuilder 构建复合图表");
    demo_chart_builder()?;
    println!();

    // 演示3: 展示不同类型的图表
    println!("📊 演示3: 展示不同类型的图表");
    demo_different_chart_types()?;

    println!("✅ 演示完成！");
    Ok(())
}

fn demo_runtime_chart() -> Result<()> {
    // 创建折线图数据
    let line_series = LineSeries::new("销售数据".to_string())
        .data(vec![
            (0.0, 120.0), (1.0, 200.0), (2.0, 150.0),
            (3.0, 80.0), (4.0, 70.0), (5.0, 110.0)
        ])
        .color(Color::BLUE)
        .line_width(2.0)
        .smooth(true);

    // 使用 RuntimeChart 构建图表
    let chart = RuntimeChart::new()
        .title("月度销售报告")
        .add_line_series(line_series);

    // 渲染图表
    let mut renderer = ConsoleRenderer;
    let bounds = Bounds::new(0.0, 0.0, 800.0, 600.0);
    
    println!("🎨 开始渲染折线图...");
    chart.render(&mut renderer, bounds)?;
    
    Ok(())
}

fn demo_chart_builder() -> Result<()> {
    // 创建多个系列
    let line_series = LineSeries::new("趋势线".to_string())
        .data(vec![(0.0, 100.0), (1.0, 150.0), (2.0, 120.0)])
        .color(Color::BLUE);

    let bar_series = BarSeries::new("柱状数据".to_string())
        .data(vec![(0.0, 80.0), (1.0, 120.0), (2.0, 100.0)])
        .color(Color::GREEN);

    // 使用 ChartBuilder 构建复合图表
    let chart = ChartBuilder::new()
        .title("复合图表示例")
        .add_line_series(line_series)
        .add_bar_series(bar_series)
        .build();

    // 渲染图表
    let mut renderer = ConsoleRenderer;
    let bounds = Bounds::new(0.0, 0.0, 800.0, 600.0);
    
    println!("🎨 开始渲染复合图表...");
    chart.render(&mut renderer, bounds)?;
    
    Ok(())
}

fn demo_different_chart_types() -> Result<()> {
    // 饼图
    let pie_series = PieSeries::new("市场份额".to_string())
        .data(vec![
            ("产品A".to_string(), 30.0),
            ("产品B".to_string(), 25.0),
            ("产品C".to_string(), 20.0),
            ("其他".to_string(), 25.0),
        ])
        .radius(100.0);

    let pie_chart = ChartBuilder::pie_chart()
        .title("市场份额分析")
        .add_pie_series(pie_series)
        .build();

    println!("🥧 渲染饼图:");
    let mut renderer = ConsoleRenderer;
    let bounds = Bounds::new(0.0, 0.0, 400.0, 400.0);
    pie_chart.render(&mut renderer, bounds)?;

    // 散点图
    let scatter_series = ScatterSeries::new("数据点".to_string())
        .data(vec![
            (10.0, 20.0), (15.0, 25.0), (20.0, 15.0),
            (25.0, 30.0), (30.0, 18.0), (35.0, 22.0),
        ])
        .color(Color::RED)
        .symbol_size(6.0);

    let scatter_chart = ChartBuilder::scatter_chart()
        .title("散点分布图")
        .add_scatter_series(scatter_series)
        .build();

    println!("🔵 渲染散点图:");
    scatter_chart.render(&mut renderer, bounds)?;

    Ok(())
}
