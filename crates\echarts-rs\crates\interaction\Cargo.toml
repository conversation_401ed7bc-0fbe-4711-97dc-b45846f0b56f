[package]
name = "echarts-interaction"
version = "0.1.0"
edition = "2021"
authors = ["ECharts-rs Contributors"]
description = "交互系统 - ECharts-rs 图表交互功能"
license = "MIT OR Apache-2.0"
repository = "https://github.com/echarts-rs/echarts-rs"
keywords = ["charts", "visualization", "interaction", "events"]
categories = ["visualization", "gui"]

[dependencies]
echarts-core = { path = "../core" }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

[dev-dependencies]
tokio = { version = "1.0", features = ["rt", "macros"] }

[features]
default = []
web = []
desktop = []
mobile = []
