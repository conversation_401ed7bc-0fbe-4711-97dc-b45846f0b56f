//! GPUI Line Chart Example
//!
//! This example demonstrates how to create a simple line chart using ECharts with GPUI.

use echarts_rs::prelude::*;
use gpui::{
    self, div, px, rgb, size, AppContext, Context, IntoElement, ParentElement, Render,
    Styled, Window, WindowBounds, WindowKind, WindowOptions, TitlebarOptions,
    WindowBackgroundAppearance, Application,
};
use gpui::Bounds as GpuiBounds;

fn main() {
    println!("🚀 Starting GPUI Line Chart Example...");

    let app = Application::new();

    app.run(move |cx| {
        println!("📱 Application context created");

        // Create window
        let window_size = size(px(800.0), px(600.0));

        let window = cx.open_window(
            WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(GpuiBounds::centered(None, window_size, cx))),
                titlebar: Some(TitlebarOptions {
                    title: Some("ECharts Line Chart Demo".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                window_background: WindowBackgroundAppearance::Opaque,
                app_id: None,
                window_min_size: Some(size(px(400.0), px(300.0))),
                window_decorations: None,
            },
            |window, cx| {
                println!("🪟 Window created");
                cx.new(|cx| LineChartDemo::new(cx))
            },
        );

        if let Err(e) = window {
            eprintln!("❌ Failed to create window: {:?}", e);
        }
    });

    println!("👋 Application exited");
}

/// Line Chart Demo Application
struct LineChartDemo {
    /// ECharts chart instance
    chart: Chart,
}

impl LineChartDemo {
    fn new(_cx: &mut Context<Self>) -> Self {
        println!("📊 Creating line chart...");

        // Create sample data
        let data = vec![
            ("Jan", 120.0),
            ("Feb", 200.0),
            ("Mar", 150.0),
            ("Apr", 80.0),
            ("May", 70.0),
            ("Jun", 110.0),
        ];

        // Create a line chart
        let mut chart = Chart::new();
        chart = chart.title("Monthly Sales".to_string());

        // Create series data as JSON (simplified approach)
        let series_data: Vec<(String, f64)> = data.into_iter()
            .map(|(name, value)| (name.to_string(), value))
            .collect();

        let series_json = serde_json::json!({
            "type": "line",
            "name": "Sales",
            "data": series_data,
            "smooth": true
        });

        chart = chart.add_series(series_json);

        // Apply theme (use string instead of Theme enum)
        chart = chart.theme("default");

        println!("✅ Line chart created successfully");

        Self { chart }
    }
}

impl Render for LineChartDemo {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("🎨 Rendering line chart demo");

        div()
            .w_full()
            .h_full()
            .bg(rgb(0xffffff))
            .flex()
            .flex_col()
            .items_center()
            .justify_center()
            .p_8()
            .child(
                div()
                    .w(px(700.0))
                    .h(px(500.0))
                    .bg(rgb(0xffffff))
                    .border_1()
                    .border_color(rgb(0xe5e7eb))
                    .rounded_lg()
                    .p_4()
                    .child(
                        // Chart placeholder
                        div()
                            .w_full()
                            .h_full()
                            .bg(rgb(0xf9f9f9))
                            .flex()
                            .items_center()
                            .justify_center()
                            .child(
                                div()
                                    .flex()
                                    .flex_col()
                                    .items_center()
                                    .gap_4()
                                    .child("📈 Line Chart")
                                    .child("Monthly Sales Data")
                                    .child("(Chart rendering placeholder)"),
                            ),
                    ),
            )
    }
}
