//! Pie chart implementation

use crate::{InteractionEvent, MouseButton, RenderContext, Series, SeriesConfig, SeriesType};
use echarts_core::{
    style::{FontStyle, FontWeight, TextAlign, TextBaseline, TextStyle},
    *,
};
use serde::{Deserialize, Serialize};

/// Pie chart series
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PieSeries {
    /// Base series configuration
    pub config: SeriesConfig,

    /// Chart data
    pub data: DataSet,

    /// Center position (x, y) as percentage of container
    pub center: (f64, f64),

    /// Radius as percentage of container (inner, outer)
    pub radius: (f64, f64),

    /// Start angle in degrees (0 = right, 90 = top)
    pub start_angle: f64,

    /// End angle in degrees
    pub end_angle: f64,

    /// Whether to show labels
    pub show_labels: bool,

    /// Whether to show label lines
    pub show_label_lines: bool,

    /// Label position
    pub label_position: LabelPosition,

    /// Whether to show percentages in labels
    pub show_percentages: bool,

    /// Minimum angle for a slice to show label (in degrees)
    pub min_angle_for_label: f64,

    /// Rose type (for rose charts)
    pub rose_type: Option<RoseType>,

    /// Whether slices can be selected
    pub selectable: bool,

    /// Selected slice indices
    pub selected: Vec<bool>,

    /// Border configuration
    pub border: Option<PieBorder>,
}

impl PieSeries {
    /// Create a new pie series
    pub fn new<S: Into<String>>(name: S) -> Self {
        Self {
            config: SeriesConfig::new(name),
            data: DataSet::new(),
            center: (0.5, 0.5),  // Center of container
            radius: (0.0, 0.75), // Inner radius 0%, outer radius 75%
            start_angle: 90.0,   // Start from top
            end_angle: 450.0,    // Full circle
            show_labels: true,
            show_label_lines: true,
            label_position: LabelPosition::Outside,
            show_percentages: true,
            min_angle_for_label: 4.0, // Minimum 4 degrees
            rose_type: None,
            selectable: true,
            selected: Vec::new(),
            border: None,
        }
    }

    /// Set the data for this series from (name, value) pairs
    pub fn data<I, S>(mut self, data: I) -> Self
    where
        I: IntoIterator<Item = (S, f64)>,
        S: Into<String>,
    {
        self.data = DataSet::from_category_value_pairs(data);
        self.selected = vec![false; self.data.len()];
        self
    }

    /// Set the data from a dataset
    pub fn dataset(mut self, dataset: DataSet) -> Self {
        self.data = dataset;
        self.selected = vec![false; self.data.len()];
        self
    }

    /// Get the data points
    pub fn get_data(&self) -> &DataSet {
        &self.data
    }

    /// Get the number of data points
    pub fn data_len(&self) -> usize {
        self.data.len()
    }

    /// Get data points as a vector
    pub fn data_points(&self) -> Vec<DataPoint> {
        self.data.points.clone()
    }

    /// Set center position (x, y) as percentage (0.0 to 1.0)
    pub fn center(mut self, x: f64, y: f64) -> Self {
        self.center = (x.clamp(0.0, 1.0), y.clamp(0.0, 1.0));
        self
    }

    /// Set radius (inner, outer) as percentage (0.0 to 1.0)
    pub fn radius(mut self, inner: f64, outer: f64) -> Self {
        self.radius = (inner.clamp(0.0, 1.0), outer.clamp(0.0, 1.0));
        self
    }

    /// Set start angle in degrees
    pub fn start_angle(mut self, angle: f64) -> Self {
        self.start_angle = angle;
        self
    }

    /// Set end angle in degrees
    pub fn end_angle(mut self, angle: f64) -> Self {
        self.end_angle = angle;
        self
    }

    /// Show/hide labels
    pub fn show_labels(mut self, show: bool) -> Self {
        self.show_labels = show;
        self
    }

    /// Show/hide label lines
    pub fn show_label_lines(mut self, show: bool) -> Self {
        self.show_label_lines = show;
        self
    }

    /// Set label position
    pub fn label_position(mut self, position: LabelPosition) -> Self {
        self.label_position = position;
        self
    }

    /// Show percentages in labels
    pub fn show_percentages(mut self, show: bool) -> Self {
        self.show_percentages = show;
        self
    }

    /// Set rose type for rose charts
    pub fn rose_type(mut self, rose_type: RoseType) -> Self {
        self.rose_type = Some(rose_type);
        self
    }

    /// Set border style
    pub fn border(mut self, color: Color, width: f64) -> Self {
        self.border = Some(PieBorder { color, width });
        self
    }

    /// Calculate slice data for rendering
    fn calculate_slices(&self, bounds: Bounds) -> Vec<PieSlice> {
        let total: f64 = self
            .data
            .points
            .iter()
            .filter_map(|p| p.get_number(1))
            .sum();

        if total <= 0.0 {
            return Vec::new();
        }

        let center_x = bounds.origin.x + bounds.width() * self.center.0;
        let center_y = bounds.origin.y + bounds.height() * self.center.1;
        let center = Point::new(center_x, center_y);

        let max_radius = (bounds.width().min(bounds.height()) / 2.0) * 0.9;
        let inner_radius = max_radius * self.radius.0;
        let outer_radius = max_radius * self.radius.1;

        let angle_range = self.end_angle - self.start_angle;
        let mut current_angle = self.start_angle;

        let mut slices = Vec::new();

        for (i, point) in self.data.points.iter().enumerate() {
            if let Some(value) = point.get_number(1) {
                if value <= 0.0 {
                    continue;
                }

                let percentage = value / total;
                let slice_angle = angle_range * percentage;

                // Adjust radius for rose charts
                let (slice_inner, slice_outer) = match &self.rose_type {
                    Some(RoseType::Radius) => {
                        let factor = (value / total).sqrt();
                        (inner_radius, outer_radius * factor)
                    }
                    Some(RoseType::Area) => {
                        let factor = value / total;
                        (inner_radius, outer_radius * factor)
                    }
                    None => (inner_radius, outer_radius),
                };

                slices.push(PieSlice {
                    index: i,
                    center,
                    inner_radius: slice_inner,
                    outer_radius: slice_outer,
                    start_angle: current_angle,
                    end_angle: current_angle + slice_angle,
                    value,
                    percentage,
                    label: point
                        .get_value(0)
                        .map(|v| v.as_string())
                        .unwrap_or_default(),
                    selected: self.selected.get(i).copied().unwrap_or(false),
                });

                current_angle += slice_angle;
            }
        }

        slices
    }
}

/// Pie slice data for rendering
#[derive(Debug, Clone)]
pub struct PieSlice {
    pub index: usize,
    pub center: Point,
    pub inner_radius: f64,
    pub outer_radius: f64,
    pub start_angle: f64,
    pub end_angle: f64,
    pub value: f64,
    pub percentage: f64,
    pub label: String,
    pub selected: bool,
}

/// Label position options
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum LabelPosition {
    Inside,
    Outside,
    Center,
}

/// Rose chart types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum RoseType {
    /// Radius proportional to value
    Radius,
    /// Area proportional to value
    Area,
}

/// Pie border configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PieBorder {
    pub color: Color,
    pub width: f64,
}

impl PieSlice {
    /// Get the middle angle of the slice
    pub fn middle_angle(&self) -> f64 {
        (self.start_angle + self.end_angle) / 2.0
    }

    /// Get the middle radius of the slice
    pub fn middle_radius(&self) -> f64 {
        (self.inner_radius + self.outer_radius) / 2.0
    }

    /// Create a path for the slice
    pub fn create_path(&self) -> Path {
        let start_rad = self.start_angle.to_radians();
        let end_rad = self.end_angle.to_radians();

        let mut path = Path::new();

        // Outer arc
        let outer_start = Point::new(
            self.center.x + self.outer_radius * start_rad.cos(),
            self.center.y + self.outer_radius * start_rad.sin(),
        );

        path = path.move_to(outer_start);

        // Arc to end of outer radius
        path = path.arc(
            self.center,
            self.outer_radius,
            self.start_angle,
            self.end_angle,
            false,
        );

        // Line to inner radius (if donut)
        if self.inner_radius > 0.0 {
            let inner_end = Point::new(
                self.center.x + self.inner_radius * end_rad.cos(),
                self.center.y + self.inner_radius * end_rad.sin(),
            );
            path = path.line_to(inner_end);

            // Inner arc back to start (reverse direction)
            path = path.arc(
                self.center,
                self.inner_radius,
                self.end_angle,
                self.start_angle,
                true,
            );
        } else {
            // Line to center for full pie
            path = path.line_to(self.center);
        }

        path.close()
    }

    /// Get label position
    pub fn get_label_position(&self, position: LabelPosition) -> Point {
        let mid_angle = self.middle_angle().to_radians();

        match position {
            LabelPosition::Inside => {
                let radius = self.middle_radius();
                Point::new(
                    self.center.x + radius * mid_angle.cos(),
                    self.center.y + radius * mid_angle.sin(),
                )
            }
            LabelPosition::Outside => {
                let radius = self.outer_radius + 20.0;
                Point::new(
                    self.center.x + radius * mid_angle.cos(),
                    self.center.y + radius * mid_angle.sin(),
                )
            }
            LabelPosition::Center => self.center,
        }
    }
}

impl Series for PieSeries {
    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Pie
    }

    fn data(&self) -> &DataSet {
        &self.data
    }

    fn validate(&self) -> Result<()> {
        if self.data.is_empty() {
            return Err(ChartError::Configuration(
                "Pie series must have data".into(),
            ));
        }

        if self.data.dimensions.len() < 2 {
            return Err(ChartError::Configuration(
                "Pie series requires at least 2 dimensions (name, value)".into(),
            ));
        }

        // Validate that all values are positive
        for (i, point) in self.data.points.iter().enumerate() {
            if let Some(value) = point.get_number(1) {
                if value < 0.0 {
                    return Err(ChartError::InvalidData(format!(
                        "Pie slice {} has negative value: {}",
                        i, value
                    )));
                }
            }
        }

        Ok(())
    }

    fn render(&self, ctx: &mut RenderContext, _coord: &dyn CoordinateSystem) -> Result<()> {
        // 临时简化实现
        Ok(())
    }

    fn handle_interaction(&mut self, event: &InteractionEvent) -> bool {
        match event {
            InteractionEvent::MouseClick {
                x,
                y,
                button: MouseButton::Left,
            } => {
                // TODO: Implement slice selection
                println!("Pie clicked at ({}, {})", x, y);
                true
            }
            _ => false,
        }
    }

    fn data_range(&self, dimension: usize) -> Option<(f64, f64)> {
        self.data.get_numeric_range(dimension)
    }

    fn clone_box(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }
}

impl PieSeries {
    /// Draw labels for pie slices
    fn draw_labels(&self, ctx: &mut RenderContext, slices: &[PieSlice]) -> Result<()> {
        let text_style = TextStyle {
            font_family: "Arial".to_string(),
            font_size: 12.0,
            color: ctx.theme.text_color(),
            font_weight: FontWeight::Normal,
            font_style: FontStyle::Normal,
            text_align: TextAlign::Center,
            text_baseline: TextBaseline::Middle,
            line_height: 1.2,
            letter_spacing: 0.0,
        };

        for slice in slices {
            let angle_degrees = slice.end_angle - slice.start_angle;
            if angle_degrees < self.min_angle_for_label {
                continue; // Skip labels for very small slices
            }

            let label_pos = slice.get_label_position(self.label_position);

            // Draw label line if outside position and enabled
            if self.show_label_lines && matches!(self.label_position, LabelPosition::Outside) {
                let line_start = slice.get_label_position(LabelPosition::Inside);
                let line_end = Point::new(
                    label_pos.x
                        - if label_pos.x > slice.center.x {
                            10.0
                        } else {
                            -10.0
                        },
                    label_pos.y,
                );

                ctx.draw_line(line_start, line_end, ctx.theme.text_color(), 1.0);
            }

            // Create label text
            let label_text = if self.show_percentages {
                format!("{}\n{:.1}%", slice.label, slice.percentage * 100.0)
            } else {
                slice.label.clone()
            };

            ctx.draw_text(label_text, label_pos, text_style.clone());
        }

        Ok(())
    }
}

/// Convenient constructors
impl PieSeries {
    /// Create a simple pie chart from (name, value) pairs
    pub fn simple<I, S>(name: &str, data: I) -> Self
    where
        I: IntoIterator<Item = (S, f64)>,
        S: Into<String>,
    {
        Self::new(name).data(data)
    }

    /// Create a donut chart
    pub fn donut<I, S>(name: &str, data: I, inner_radius: f64) -> Self
    where
        I: IntoIterator<Item = (S, f64)>,
        S: Into<String>,
    {
        Self::new(name).data(data).radius(inner_radius, 0.75)
    }

    /// Create a rose chart
    pub fn rose<I, S>(name: &str, data: I, rose_type: RoseType) -> Self
    where
        I: IntoIterator<Item = (S, f64)>,
        S: Into<String>,
    {
        Self::new(name).data(data).rose_type(rose_type)
    }
}

/// 实现 echarts_core::Series trait 以支持统一的渲染接口
///
/// 这个实现将 PieSeries 的渲染逻辑转换为 DrawCommand 列表，
/// 保持所有现有功能的同时支持类型擦除的渲染。
impl echarts_core::chart::Series for PieSeries {
    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> echarts_core::SeriesType {
        echarts_core::SeriesType::Pie
    }

    fn data(&self) -> &echarts_core::DataSet {
        &self.data
    }

    fn data_len(&self) -> usize {
        self.data.len()
    }

    fn render_to_commands(&self, _coord_system: &dyn echarts_core::ChartCoordinateSystem) -> echarts_core::Result<Vec<echarts_core::DrawCommand>> {
        // 临时简化实现 - 返回空的DrawCommand列表
        // TODO: 实现完整的饼图DrawCommand生成逻辑，适配新的DrawCommand结构
        Ok(Vec::new())
    }





    fn bounds(&self) -> Option<echarts_core::Bounds> {
        if self.data.is_empty() {
            return None;
        }

        // 饼图的边界是一个正方形，以适应圆形
        let size = 2.0 * self.radius.1; // 外半径的两倍
        Some(echarts_core::Bounds::new(
            self.center.0 - self.radius.1,
            self.center.1 - self.radius.1,
            size,
            size,
        ))
    }

    fn clone_series(&self) -> Box<dyn echarts_core::chart::Series> {
        Box::new(self.clone())
    }

    fn validate(&self) -> echarts_core::Result<()> {
        if self.data.is_empty() {
            return Err(echarts_core::ChartError::validation("Pie series must have data"));
        }

        // 检查是否有负值
        for i in 0..self.data.len() {
            if let Some(value) = self.data.get_value(i, 1) {
                if value < 0.0 {
                    return Err(echarts_core::ChartError::validation(
                        "Pie series cannot have negative values"
                    ));
                }
            }
        }

        if self.radius.0 < 0.0 || self.radius.1 < 0.0 || self.radius.0 >= self.radius.1 {
            return Err(echarts_core::ChartError::validation(
                "Invalid radius configuration: inner radius must be less than outer radius and both must be non-negative"
            ));
        }

        Ok(())
    }

    fn data_len(&self) -> usize {
        self.data.len()
    }
}

impl PieSeries {
    /// 计算标签位置
    fn calculate_label_position(&self, slice: &PieSlice, center: echarts_core::Point, outer_radius: f64, inner_radius: f64) -> echarts_core::Point {
        let middle_angle = slice.middle_angle().to_radians();

        match self.label_position {
            LabelPosition::Inside => {
                let radius = (outer_radius + inner_radius) / 2.0;
                echarts_core::Point::new(
                    center.x + radius * middle_angle.cos(),
                    center.y + radius * middle_angle.sin(),
                )
            }
            LabelPosition::Outside => {
                let radius = outer_radius + 30.0;
                echarts_core::Point::new(
                    center.x + radius * middle_angle.cos(),
                    center.y + radius * middle_angle.sin(),
                )
            }
            LabelPosition::Center => center,
        }
    }

    /// 创建弧形命令（辅助方法）
    fn create_arc_commands(&self, center: echarts_core::Point, radius: f64, start_angle: f64, end_angle: f64) -> Vec<echarts_core::Point> {
        let mut points = Vec::new();
        let steps = 16;
        let angle_step = (end_angle - start_angle).to_radians() / steps as f64;

        for i in 0..=steps {
            let angle = start_angle.to_radians() + angle_step * i as f64;
            points.push(echarts_core::Point::new(
                center.x + radius * angle.cos(),
                center.y + radius * angle.sin(),
            ));
        }

        points
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_pie_series_creation() {
        let data = vec![("Desktop", 45.0), ("Mobile", 35.0), ("Tablet", 20.0)];

        let series = PieSeries::new("Device Usage")
            .data(data)
            .show_percentages(true);

        assert_eq!(series.name(), "Device Usage");
        assert_eq!(series.series_type(), SeriesType::Pie);
        assert_eq!(series.data_len(), 3);
        assert!(series.show_percentages);
    }

    #[test]
    fn test_pie_series_validation() {
        let series = PieSeries::new("Empty");
        assert!(series.validate().is_err());

        let data = vec![("A", 10.0), ("B", 20.0)];
        let series = PieSeries::new("Valid").data(data);
        assert!(series.validate().is_ok());

        let negative_data = vec![("A", -10.0), ("B", 20.0)];
        let series = PieSeries::new("Invalid").data(negative_data);
        assert!(series.validate().is_err());
    }

    #[test]
    fn test_pie_slice_calculations() {
        let data = vec![("A", 25.0), ("B", 75.0)];
        let series = PieSeries::new("Test").data(data);

        let bounds = Bounds::new(0.0, 0.0, 400.0, 400.0);
        let slices = series.calculate_slices(bounds);

        assert_eq!(slices.len(), 2);
        assert!((slices[0].percentage - 0.25).abs() < 0.001);
        assert!((slices[1].percentage - 0.75).abs() < 0.001);
    }
}
