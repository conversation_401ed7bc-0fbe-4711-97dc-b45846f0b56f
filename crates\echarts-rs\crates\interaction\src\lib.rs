//! ECharts-rs 交互系统
//!
//! 提供完整的图表交互功能，包括鼠标事件、键盘事件、触摸事件等
//!
//! ## 分层架构
//!
//! - **上层**: InteractionManager - 统一交互管理
//! - **适配层**: InteractionAdapter - 事件转换和协调
//! - **中层**: 图表特定交互 (line.rs, bar.rs等)
//! - **底层**: 通用交互事件 (professional_interactions.rs)

pub mod events;
pub mod handlers;
pub mod tooltip;
pub mod zoom_pan;
pub mod selection;
pub mod legend_interaction;
pub mod adapter;

use echarts_core::{Point, Bounds, Result, Series};
use std::collections::HashMap;
use serde::{Serialize, Deserialize};

// Re-export core types
pub use events::*;
pub use handlers::*;
pub use tooltip::*;
pub use zoom_pan::*;
pub use selection::*;
pub use legend_interaction::*;
pub use adapter::*;

/// 交互管理器 (上层)
///
/// 负责协调所有交互功能，处理事件分发和状态管理
/// 这是分层架构的顶层，统一管理所有交互
pub struct InteractionManager {
    /// 交互适配器 (新增 - 分层架构的核心)
    adapter: InteractionAdapter,

    /// 事件处理器 (保持向后兼容)
    handlers: Vec<Box<dyn InteractionHandler>>,

    /// 当前交互状态
    state: InteractionState,

    /// 工具提示管理器
    tooltip: TooltipManager,

    /// 缩放平移管理器
    zoom_pan: ZoomPanManager,

    /// 选择管理器
    selection: SelectionManager,

    /// 图例交互管理器
    legend: LegendInteractionManager,

    /// 配置选项
    config: InteractionConfig,
}

/// 交互状态
#[derive(Debug, Clone)]
pub struct InteractionState {
    /// 当前鼠标位置
    pub mouse_position: Option<Point>,
    /// 鼠标按下状态
    pub mouse_down: bool,
    /// 鼠标按下位置
    pub mouse_down_position: Option<Point>,
    /// 当前悬停的元素
    pub hovered_element: Option<HoveredElement>,
    /// 选中的元素
    pub selected_elements: Vec<SelectedElement>,
    /// 是否正在拖拽
    pub is_dragging: bool,
    /// 拖拽阈值
    pub drag_threshold: f64,
    /// 键盘修饰键状态
    pub modifiers: KeyModifiers,
}

/// 悬停元素信息
#[derive(Debug, Clone)]
pub struct HoveredElement {
    /// 元素类型
    pub element_type: ElementType,
    /// 系列索引
    pub series_index: Option<usize>,
    /// 数据点索引
    pub data_index: Option<usize>,
    /// 元素边界
    pub bounds: Bounds,
    /// 附加数据
    pub data: HashMap<String, String>,
}



/// 元素类型
#[derive(Debug, Clone, PartialEq)]
pub enum ElementType {
    /// 数据点
    DataPoint,
    /// 系列
    Series,
    /// 图例项
    LegendItem,
    /// 坐标轴
    Axis,
    /// 网格
    Grid,
    /// 标题
    Title,
    /// 自定义元素
    Custom(String),
}



/// 交互配置
#[derive(Debug, Clone)]
pub struct InteractionConfig {
    /// 是否启用交互
    pub enabled: bool,
    /// 工具提示配置
    pub tooltip: TooltipConfig,
    /// 缩放平移配置
    pub zoom_pan: ZoomPanConfig,
    /// 选择配置
    pub selection: SelectionConfig,
    /// 图例交互配置
    pub legend: LegendInteractionConfig,
    /// 拖拽阈值
    pub drag_threshold: f64,
    /// 双击间隔时间（毫秒）
    pub double_click_interval: u64,
}

impl Default for InteractionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            tooltip: TooltipConfig::default(),
            zoom_pan: ZoomPanConfig::default(),
            selection: SelectionConfig::default(),
            legend: LegendInteractionConfig::default(),
            drag_threshold: 5.0,
            double_click_interval: 300,
        }
    }
}

impl Default for InteractionState {
    fn default() -> Self {
        Self {
            mouse_position: None,
            mouse_down: false,
            mouse_down_position: None,
            hovered_element: None,
            selected_elements: Vec::new(),
            is_dragging: false,
            drag_threshold: 5.0,
            modifiers: KeyModifiers::default(),
        }
    }
}

impl InteractionManager {
    /// 创建新的交互管理器
    pub fn new(config: InteractionConfig) -> Self {
        Self {
            adapter: InteractionAdapter::new(),
            handlers: Vec::new(),
            state: InteractionState::default(),
            tooltip: TooltipManager::new(config.tooltip.clone()),
            zoom_pan: ZoomPanManager::new(config.zoom_pan.clone()),
            selection: SelectionManager::new(config.selection.clone()),
            legend: LegendInteractionManager::new(config.legend.clone()),
            config,
        }
    }

    /// 添加事件处理器
    pub fn add_handler(&mut self, handler: Box<dyn InteractionHandler>) {
        self.handlers.push(handler);
    }

    /// 注册图表交互控制器 (新增 - 分层架构)
    pub fn register_chart(&mut self, controller: Box<dyn ChartInteraction>) {
        self.adapter.register_chart(controller);
    }

    /// 处理底层输入事件 (新增 - 分层架构)
    pub fn handle_input_event(&mut self, event: echarts_core::professional_interactions::InteractionEvent) -> Vec<ChartInteractionResult> {
        self.adapter.handle_input_event(event)
    }

    /// 处理交互事件
    pub fn handle_event(&mut self, event: &InteractionEvent, chart_bounds: Bounds, series: &[Box<dyn Series>]) -> Result<InteractionResult> {
        if !self.config.enabled {
            return Ok(InteractionResult::None);
        }

        // 更新交互状态
        self.update_state(event);

        // 处理不同类型的事件
        let mut result = InteractionResult::None;

        match event {
            InteractionEvent::Mouse(mouse_event) => {
                result = self.handle_mouse_event(mouse_event, chart_bounds, series)?;
            }
            InteractionEvent::Key(key_event) => {
                result = self.handle_key_event(key_event)?;
            }
            InteractionEvent::Touch(touch_event) => {
                result = self.handle_touch_event(touch_event, chart_bounds, series)?;
            }
            InteractionEvent::Wheel(wheel_event) => {
                result = self.handle_wheel_event(wheel_event, chart_bounds)?;
            }
            InteractionEvent::Custom { .. } => {
                // 自定义事件暂时不处理
                result = InteractionResult::None;
            }
        }

        // 调用自定义处理器
        for handler in &mut self.handlers {
            let handler_result = handler.handle_event(event, &self.state)?;
            if !matches!(handler_result, InteractionResult::None) {
                result = handler_result;
                break;
            }
        }

        Ok(result)
    }

    /// 更新交互状态
    fn update_state(&mut self, event: &InteractionEvent) {
        match event {
            InteractionEvent::Mouse(mouse_event) => {
                match mouse_event.event_type {
                    MouseEventType::Move => {
                        self.state.mouse_position = Some(mouse_event.position);
                        
                        // 检查是否开始拖拽
                        if self.state.mouse_down {
                            if let Some(down_pos) = self.state.mouse_down_position {
                                let distance = ((mouse_event.position.x - down_pos.x).powi(2) + 
                                               (mouse_event.position.y - down_pos.y).powi(2)).sqrt();
                                if distance > self.state.drag_threshold {
                                    self.state.is_dragging = true;
                                }
                            }
                        }
                    }
                    MouseEventType::Down => {
                        self.state.mouse_down = true;
                        self.state.mouse_down_position = Some(mouse_event.position);
                        self.state.is_dragging = false;
                    }
                    MouseEventType::Up => {
                        self.state.mouse_down = false;
                        self.state.mouse_down_position = None;
                        self.state.is_dragging = false;
                    }
                    _ => {}
                }
                self.state.modifiers = mouse_event.modifiers.clone();
            }
            InteractionEvent::Key(key_event) => {
                // 更新修饰键状态
                match key_event.key.as_str() {
                    "Control" => self.state.modifiers.ctrl = key_event.event_type == KeyEventType::Down,
                    "Shift" => self.state.modifiers.shift = key_event.event_type == KeyEventType::Down,
                    "Alt" => self.state.modifiers.alt = key_event.event_type == KeyEventType::Down,
                    "Meta" => self.state.modifiers.meta = key_event.event_type == KeyEventType::Down,
                    _ => {}
                }
            }
            _ => {}
        }
    }

    /// 处理鼠标事件
    fn handle_mouse_event(&mut self, event: &MouseEvent, chart_bounds: Bounds, series: &[Box<dyn Series>]) -> Result<InteractionResult> {
        let mut result = InteractionResult::None;

        match event.event_type {
            MouseEventType::Move => {
                // 处理悬停
                let hover_result = self.handle_hover(event.position, chart_bounds, series)?;
                if !matches!(hover_result, InteractionResult::None) {
                    result = hover_result;
                }

                // 处理拖拽
                if self.state.is_dragging {
                    let drag_result = self.zoom_pan.handle_drag(event.position, &self.state)?;
                    if !matches!(drag_result, InteractionResult::None) {
                        result = drag_result;
                    }
                }
            }
            MouseEventType::Down => {
                // 处理点击选择
                let select_result = self.selection.handle_click(event.position, chart_bounds, series, &event.modifiers)?;
                if !matches!(select_result, InteractionResult::None) {
                    result = select_result;
                }
            }
            MouseEventType::Up => {
                // 结束拖拽操作
                if self.state.is_dragging {
                    result = InteractionResult::Redraw;
                }
            }
            MouseEventType::DoubleClick => {
                // 处理双击重置
                result = self.zoom_pan.handle_double_click(event.position, chart_bounds)?;
            }
            _ => {}
        }

        Ok(result)
    }

    /// 处理键盘事件
    fn handle_key_event(&mut self, event: &KeyEvent) -> Result<InteractionResult> {
        match event.event_type {
            KeyEventType::Down => {
                match event.key.as_str() {
                    "Escape" => {
                        // 清除选择
                        self.state.selected_elements.clear();
                        Ok(InteractionResult::Redraw)
                    }
                    "Delete" | "Backspace" => {
                        // 删除选中元素（如果支持）
                        if !self.state.selected_elements.is_empty() {
                            Ok(InteractionResult::Custom {
                                name: "delete_selected".to_string(),
                                data: serde_json::json!({
                                    "elements": self.state.selected_elements
                                }),
                            })
                        } else {
                            Ok(InteractionResult::None)
                        }
                    }
                    _ => Ok(InteractionResult::None),
                }
            }
            _ => Ok(InteractionResult::None),
        }
    }

    /// 处理触摸事件
    fn handle_touch_event(&mut self, event: &TouchEvent, chart_bounds: Bounds, series: &[Box<dyn Series>]) -> Result<InteractionResult> {
        // 将触摸事件转换为鼠标事件进行处理
        let mouse_event = MouseEvent {
            position: event.position,
            event_type: match event.event_type {
                TouchEventType::Start => MouseEventType::Down,
                TouchEventType::Move => MouseEventType::Move,
                TouchEventType::End => MouseEventType::Up,
                TouchEventType::Cancel => MouseEventType::Up,
            },
            button: MouseButton::Left,
            modifiers: KeyModifiers::default(),
        };

        self.handle_mouse_event(&mouse_event, chart_bounds, series)
    }

    /// 处理滚轮事件
    fn handle_wheel_event(&mut self, event: &WheelEvent, chart_bounds: Bounds) -> Result<InteractionResult> {
        self.zoom_pan.handle_wheel(event, chart_bounds)
    }

    /// 处理悬停
    fn handle_hover(&mut self, position: Point, chart_bounds: Bounds, series: &[Box<dyn Series>]) -> Result<InteractionResult> {
        // 查找悬停的元素
        let hovered = self.find_element_at_position(position, chart_bounds, series)?;
        
        let mut result = InteractionResult::None;

        // 如果悬停元素发生变化
        if self.has_hover_changed(&hovered) {
            self.state.hovered_element = hovered.clone();
            
            // 更新工具提示
            if let Some(element) = &hovered {
                let tooltip_result = self.tooltip.show(position, element)?;
                if !matches!(tooltip_result, InteractionResult::None) {
                    result = tooltip_result;
                }
            } else {
                let tooltip_result = self.tooltip.hide()?;
                if !matches!(tooltip_result, InteractionResult::None) {
                    result = tooltip_result;
                }
            }
            
            // 请求重绘以更新悬停效果
            if matches!(result, InteractionResult::None) {
                result = InteractionResult::Redraw;
            }
        }

        Ok(result)
    }

    /// 查找指定位置的元素
    fn find_element_at_position(&self, position: Point, chart_bounds: Bounds, series: &[Box<dyn Series>]) -> Result<Option<HoveredElement>> {
        // 检查是否在图表区域内
        if !chart_bounds.contains_point(position) {
            return Ok(None);
        }

        // 遍历系列查找数据点
        for (series_index, series_item) in series.iter().enumerate() {
            // 这里需要系列提供点击测试方法
            // 暂时返回None，具体实现需要在各个系列中添加hit_test方法
        }

        Ok(None)
    }

    /// 检查悬停元素是否发生变化
    fn has_hover_changed(&self, new_hovered: &Option<HoveredElement>) -> bool {
        match (&self.state.hovered_element, new_hovered) {
            (None, None) => false,
            (Some(_), None) | (None, Some(_)) => true,
            (Some(old), Some(new)) => {
                old.element_type != new.element_type ||
                old.series_index != new.series_index ||
                old.data_index != new.data_index
            }
        }
    }

    /// 获取当前交互状态
    pub fn get_state(&self) -> &InteractionState {
        &self.state
    }

    /// 重置交互状态
    pub fn reset(&mut self) {
        self.state = InteractionState::default();
        self.tooltip.hide().ok();
        self.selection.clear();
    }
}
