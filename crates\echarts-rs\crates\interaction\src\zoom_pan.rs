//! 缩放和平移系统
//!
//! 提供图表的缩放和平移交互功能

use crate::{InteractionResult, InteractionState, WheelEvent};
use echarts_core::{Point, Bounds, Result};
use serde::{Serialize, Deserialize};

/// 缩放平移管理器
#[derive(Debug)]
pub struct ZoomPanManager {
    /// 配置选项
    config: ZoomPanConfig,
    /// 当前状态
    state: ZoomPanState,
}

/// 缩放平移配置
#[derive(Debug, Clone)]
pub struct ZoomPanConfig {
    /// 是否启用缩放
    pub zoom_enabled: bool,
    /// 是否启用平移
    pub pan_enabled: bool,
    /// 最小缩放比例
    pub min_zoom: f64,
    /// 最大缩放比例
    pub max_zoom: f64,
    /// 缩放灵敏度
    pub zoom_sensitivity: f64,
    /// 平移灵敏度
    pub pan_sensitivity: f64,
    /// 缩放模式
    pub zoom_mode: ZoomMode,
    /// 平移模式
    pub pan_mode: PanMode,
    /// 边界约束
    pub boundary_constraint: BoundaryConstraint,
    /// 动画配置
    pub animation: ZoomPanAnimation,
}

/// 缩放平移状态
#[derive(Debug, Clone)]
pub struct ZoomPanState {
    /// 当前缩放比例
    pub zoom_level: f64,
    /// 当前平移偏移
    pub pan_offset: Point,
    /// 缩放中心点
    pub zoom_center: Point,
    /// 是否正在缩放
    pub is_zooming: bool,
    /// 是否正在平移
    pub is_panning: bool,
    /// 平移起始位置
    pub pan_start_position: Option<Point>,
    /// 初始边界
    pub initial_bounds: Option<Bounds>,
    /// 当前边界
    pub current_bounds: Option<Bounds>,
}

/// 缩放模式
#[derive(Debug, Clone, PartialEq)]
pub enum ZoomMode {
    /// 滚轮缩放
    Wheel,
    /// 双击缩放
    DoubleClick,
    /// 拖拽缩放（框选）
    Drag,
    /// 捏合缩放（触摸）
    Pinch,
    /// 所有模式
    All,
}

/// 平移模式
#[derive(Debug, Clone, PartialEq)]
pub enum PanMode {
    /// 鼠标拖拽
    Drag,
    /// 键盘方向键
    Keyboard,
    /// 触摸拖拽
    Touch,
    /// 所有模式
    All,
}

/// 边界约束
#[derive(Debug, Clone)]
pub struct BoundaryConstraint {
    /// 是否启用边界约束
    pub enabled: bool,
    /// 允许的最大平移范围
    pub max_pan_range: Option<Bounds>,
    /// 是否允许超出边界
    pub allow_overflow: bool,
    /// 回弹动画
    pub bounce_back: bool,
}

/// 缩放平移动画
#[derive(Debug, Clone)]
pub struct ZoomPanAnimation {
    /// 是否启用动画
    pub enabled: bool,
    /// 动画持续时间（毫秒）
    pub duration: u64,
    /// 动画缓动函数
    pub easing: ZoomPanEasing,
}

/// 动画缓动函数
#[derive(Debug, Clone, PartialEq)]
pub enum ZoomPanEasing {
    /// 线性
    Linear,
    /// 缓入
    EaseIn,
    /// 缓出
    EaseOut,
    /// 缓入缓出
    EaseInOut,
    /// 弹性
    Elastic,
}

impl Default for ZoomPanConfig {
    fn default() -> Self {
        Self {
            zoom_enabled: true,
            pan_enabled: true,
            min_zoom: 0.1,
            max_zoom: 10.0,
            zoom_sensitivity: 1.0,
            pan_sensitivity: 1.0,
            zoom_mode: ZoomMode::All,
            pan_mode: PanMode::All,
            boundary_constraint: BoundaryConstraint::default(),
            animation: ZoomPanAnimation::default(),
        }
    }
}

impl Default for BoundaryConstraint {
    fn default() -> Self {
        Self {
            enabled: true,
            max_pan_range: None,
            allow_overflow: false,
            bounce_back: true,
        }
    }
}

impl Default for ZoomPanAnimation {
    fn default() -> Self {
        Self {
            enabled: true,
            duration: 300,
            easing: ZoomPanEasing::EaseOut,
        }
    }
}

impl Default for ZoomPanState {
    fn default() -> Self {
        Self {
            zoom_level: 1.0,
            pan_offset: Point { x: 0.0, y: 0.0 },
            zoom_center: Point { x: 0.0, y: 0.0 },
            is_zooming: false,
            is_panning: false,
            pan_start_position: None,
            initial_bounds: None,
            current_bounds: None,
        }
    }
}

impl ZoomPanManager {
    /// 创建新的缩放平移管理器
    pub fn new(config: ZoomPanConfig) -> Self {
        Self {
            config,
            state: ZoomPanState::default(),
        }
    }
    
    /// 设置初始边界
    pub fn set_initial_bounds(&mut self, bounds: Bounds) {
        self.state.initial_bounds = Some(bounds);
        self.state.current_bounds = Some(bounds);
    }
    
    /// 处理滚轮事件
    pub fn handle_wheel(&mut self, event: &WheelEvent, chart_bounds: Bounds) -> Result<InteractionResult> {
        if !self.config.zoom_enabled || !matches!(self.config.zoom_mode, ZoomMode::Wheel | ZoomMode::All) {
            return Ok(InteractionResult::None);
        }
        
        // 计算缩放因子
        let zoom_delta = -event.delta_y * self.config.zoom_sensitivity * 0.001;
        let zoom_factor = (1.0 + zoom_delta).max(0.1).min(2.0);
        
        // 计算新的缩放级别
        let new_zoom_level = (self.state.zoom_level * zoom_factor)
            .max(self.config.min_zoom)
            .min(self.config.max_zoom);
        
        if (new_zoom_level - self.state.zoom_level).abs() < f64::EPSILON {
            return Ok(InteractionResult::None);
        }
        
        // 更新状态
        self.state.zoom_level = new_zoom_level;
        self.state.zoom_center = event.position;
        self.state.is_zooming = true;
        
        Ok(InteractionResult::Zoom {
            center: event.position,
            factor: zoom_factor,
        })
    }
    
    /// 处理拖拽
    pub fn handle_drag(&mut self, position: Point, state: &InteractionState) -> Result<InteractionResult> {
        if !self.config.pan_enabled || !matches!(self.config.pan_mode, PanMode::Drag | PanMode::All) {
            return Ok(InteractionResult::None);
        }
        
        if let Some(start_pos) = state.mouse_down_position {
            let delta = Point {
                x: (position.x - start_pos.x) * self.config.pan_sensitivity,
                y: (position.y - start_pos.y) * self.config.pan_sensitivity,
            };
            
            // 应用边界约束
            let constrained_delta = self.apply_boundary_constraint(delta);
            
            // 更新状态
            self.state.pan_offset.x += constrained_delta.x;
            self.state.pan_offset.y += constrained_delta.y;
            self.state.is_panning = true;
            
            return Ok(InteractionResult::Pan {
                delta: constrained_delta,
            });
        }
        
        Ok(InteractionResult::None)
    }
    
    /// 处理双击重置
    pub fn handle_double_click(&mut self, _position: Point, _chart_bounds: Bounds) -> Result<InteractionResult> {
        if !self.config.zoom_enabled || !matches!(self.config.zoom_mode, ZoomMode::DoubleClick | ZoomMode::All) {
            return Ok(InteractionResult::None);
        }
        
        // 重置到初始状态
        self.reset_to_initial();
        
        Ok(InteractionResult::Zoom {
            center: Point { x: 0.0, y: 0.0 },
            factor: 1.0 / self.state.zoom_level,
        })
    }
    
    /// 缩放到指定区域
    pub fn zoom_to_area(&mut self, area: Bounds, chart_bounds: Bounds) -> Result<InteractionResult> {
        if !self.config.zoom_enabled {
            return Ok(InteractionResult::None);
        }
        
        // 计算缩放因子和中心点
        let zoom_factor_x = chart_bounds.size.width / area.size.width;
        let zoom_factor_y = chart_bounds.size.height / area.size.height;
        let zoom_factor = zoom_factor_x.min(zoom_factor_y);
        
        // 限制缩放范围
        let new_zoom_level = (self.state.zoom_level * zoom_factor)
            .max(self.config.min_zoom)
            .min(self.config.max_zoom);
        
        // 计算中心点
        let center = Point {
            x: area.origin.x + area.size.width / 2.0,
            y: area.origin.y + area.size.height / 2.0,
        };
        
        // 更新状态
        self.state.zoom_level = new_zoom_level;
        self.state.zoom_center = center;
        
        Ok(InteractionResult::Zoom {
            center,
            factor: zoom_factor,
        })
    }
    
    /// 平移到指定位置
    pub fn pan_to(&mut self, target: Point) -> Result<InteractionResult> {
        if !self.config.pan_enabled {
            return Ok(InteractionResult::None);
        }
        
        let delta = Point {
            x: target.x - self.state.pan_offset.x,
            y: target.y - self.state.pan_offset.y,
        };
        
        // 应用边界约束
        let constrained_delta = self.apply_boundary_constraint(delta);
        
        // 更新状态
        self.state.pan_offset.x += constrained_delta.x;
        self.state.pan_offset.y += constrained_delta.y;
        
        Ok(InteractionResult::Pan {
            delta: constrained_delta,
        })
    }
    
    /// 重置到初始状态
    pub fn reset_to_initial(&mut self) {
        self.state.zoom_level = 1.0;
        self.state.pan_offset = Point { x: 0.0, y: 0.0 };
        self.state.zoom_center = Point { x: 0.0, y: 0.0 };
        self.state.is_zooming = false;
        self.state.is_panning = false;
        self.state.current_bounds = self.state.initial_bounds;
    }
    
    /// 应用边界约束
    fn apply_boundary_constraint(&self, delta: Point) -> Point {
        if !self.config.boundary_constraint.enabled {
            return delta;
        }
        
        // 如果允许溢出，直接返回
        if self.config.boundary_constraint.allow_overflow {
            return delta;
        }
        
        // 简单的边界检查（具体实现需要根据实际边界计算）
        let mut constrained_delta = delta;
        
        // 这里应该根据实际的图表边界进行约束
        // 暂时返回原始delta
        constrained_delta
    }
    
    /// 获取当前变换矩阵
    pub fn get_transform_matrix(&self) -> TransformMatrix {
        TransformMatrix {
            scale_x: self.state.zoom_level,
            scale_y: self.state.zoom_level,
            translate_x: self.state.pan_offset.x,
            translate_y: self.state.pan_offset.y,
            center_x: self.state.zoom_center.x,
            center_y: self.state.zoom_center.y,
        }
    }
    
    /// 获取当前状态
    pub fn get_state(&self) -> &ZoomPanState {
        &self.state
    }
    
    /// 是否正在交互
    pub fn is_interacting(&self) -> bool {
        self.state.is_zooming || self.state.is_panning
    }
}

/// 变换矩阵
#[derive(Debug, Clone)]
pub struct TransformMatrix {
    /// X轴缩放
    pub scale_x: f64,
    /// Y轴缩放
    pub scale_y: f64,
    /// X轴平移
    pub translate_x: f64,
    /// Y轴平移
    pub translate_y: f64,
    /// 缩放中心X
    pub center_x: f64,
    /// 缩放中心Y
    pub center_y: f64,
}

impl TransformMatrix {
    /// 应用变换到点
    pub fn transform_point(&self, point: Point) -> Point {
        // 先平移到缩放中心
        let centered_x = point.x - self.center_x;
        let centered_y = point.y - self.center_y;
        
        // 应用缩放
        let scaled_x = centered_x * self.scale_x;
        let scaled_y = centered_y * self.scale_y;
        
        // 平移回去并应用平移偏移
        Point {
            x: scaled_x + self.center_x + self.translate_x,
            y: scaled_y + self.center_y + self.translate_y,
        }
    }
    
    /// 应用变换到边界
    pub fn transform_bounds(&self, bounds: Bounds) -> Bounds {
        let top_left = self.transform_point(bounds.origin);
        let bottom_right = self.transform_point(Point {
            x: bounds.origin.x + bounds.size.width,
            y: bounds.origin.y + bounds.size.height,
        });
        
        Bounds {
            origin: top_left,
            size: echarts_core::Size {
                width: bottom_right.x - top_left.x,
                height: bottom_right.y - top_left.y,
            },
        }
    }
}
