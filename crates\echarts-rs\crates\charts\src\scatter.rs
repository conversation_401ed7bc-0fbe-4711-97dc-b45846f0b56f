//! 散点图实现 - 重构版本
//!
//! 基于新的 Core 架构的简化但完整的散点图实现

use echarts_core::{
    Bounds, Color, CoordinateSystem, DataSet, DataValue, DrawCommand, Point, Result, Series, SeriesType,
    draw_commands::{CircleStyle},
    LineStyle, LineCap, LineJoin,
};
use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};
use serde::{Deserialize, Serialize};

/// 散点符号类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SymbolType {
    Circle,
    Square,
    Triangle,
    Diamond,
}

/// 简化的散点图系列
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScatterSeries {
    /// 基础配置
    pub config: ChartConfig,

    /// 图表数据
    pub data: DataSet,
    
    /// 点的颜色
    pub color: Color,
    
    /// 点的大小
    pub symbol_size: f64,
    
    /// 符号类型
    pub symbol_type: SymbolType,
    
    /// 是否显示边框
    pub show_border: bool,
    
    /// 边框颜色
    pub border_color: Color,
    
    /// 边框宽度
    pub border_width: f64,
    
    // visible, z_index 现在在 config 中
}

impl ScatterSeries {
    /// 创建新的散点图系列
    pub fn new<S: Into<String>>(name: S) -> Self {
        Self {
            config: ChartConfig {
                name: name.into(),
                ..ChartConfig::default()
            },
            data: DataSet::new(),
            color: Color::rgb(0.9, 0.4, 0.2), // 默认橙色
            symbol_size: 8.0,
            symbol_type: SymbolType::Circle,
            show_border: true,
            border_color: Color::rgb(1.0, 1.0, 1.0),
            border_width: 1.0,
        }
    }

    /// 设置数据（从 (x, y) 点对）
    pub fn data<I>(mut self, data: I) -> Self
    where
        I: IntoIterator<Item = (f64, f64)>,
    {
        self.data = DataSet::from_xy_pairs(data);
        self
    }

    /// 设置颜色
    pub fn color(mut self, color: Color) -> Self {
        self.color = color;
        self
    }

    /// 设置符号大小
    pub fn symbol_size(mut self, size: f64) -> Self {
        self.symbol_size = size.max(1.0);
        self
    }

    /// 设置符号类型
    pub fn symbol_type(mut self, symbol_type: SymbolType) -> Self {
        self.symbol_type = symbol_type;
        self
    }

    /// 设置边框
    pub fn border(mut self, show: bool, color: Color, width: f64) -> Self {
        self.show_border = show;
        self.border_color = color;
        self.border_width = width;
        self
    }

    /// 设置可见性
    pub fn visible(mut self, visible: bool) -> Self {
        self.config.visible = visible;
        self
    }

    /// 设置 Z-index
    pub fn z_index(mut self, z_index: i32) -> Self {
        self.config.z_index = z_index;
        self
    }
}

impl Series for ScatterSeries {
    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Scatter
    }

    fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        if !self.config.visible || self.data.is_empty() {
            return Ok(commands);
        }

        // 绘制所有散点
        for i in 0..self.data.len() {
            if let Some(point) = self.data.get_point(i) {
                if let (Some(x), Some(y)) = (point.get_number(0), point.get_number(1)) {
                    let data_values = vec![DataValue::Number(x), DataValue::Number(y)];
                    if let Ok(screen_point) = coord_system.data_to_point(&data_values) {
                        // 根据符号类型绘制不同的形状
                        match self.symbol_type {
                            SymbolType::Circle => {
                                commands.push(DrawCommand::Circle {
                                    center: screen_point,
                                    radius: self.symbol_size / 2.0,
                                    style: CircleStyle {
                                        fill: Some(self.color),
                                        stroke: if self.show_border {
                                            Some(LineStyle {
                                                color: self.border_color,
                                                width: self.border_width,
                                                opacity: 1.0,
                                                dash_pattern: None,
                                                cap: LineCap::Round,
                                                join: LineJoin::Round,
                                            })
                                        } else {
                                            None
                                        },
                                        opacity: 1.0,
                                    },
                                });
                            }
                            SymbolType::Square => {
                                let half_size = self.symbol_size / 2.0;
                                let bounds = Bounds::new(
                                    screen_point.x - half_size,
                                    screen_point.y - half_size,
                                    self.symbol_size,
                                    self.symbol_size,
                                );
                                commands.push(DrawCommand::Rect {
                                    bounds,
                                    style: echarts_core::draw_commands::RectStyle {
                                        fill: Some(self.color),
                                        stroke: if self.show_border {
                                            Some(LineStyle {
                                                color: self.border_color,
                                                width: self.border_width,
                                                opacity: 1.0,
                                                dash_pattern: None,
                                                cap: LineCap::Butt,
                                                join: LineJoin::Miter,
                                            })
                                        } else {
                                            None
                                        },
                                        opacity: 1.0,
                                        corner_radius: 0.0,
                                    },
                                });
                            }
                            SymbolType::Triangle | SymbolType::Diamond => {
                                // 对于复杂形状，暂时使用圆形代替
                                // TODO: 实现多边形绘制
                                commands.push(DrawCommand::Circle {
                                    center: screen_point,
                                    radius: self.symbol_size / 2.0,
                                    style: CircleStyle {
                                        fill: Some(self.color),
                                        stroke: if self.show_border {
                                            Some(LineStyle {
                                                color: self.border_color,
                                                width: self.border_width,
                                                opacity: 1.0,
                                                dash_pattern: None,
                                                cap: LineCap::Round,
                                                join: LineJoin::Round,
                                            })
                                        } else {
                                            None
                                        },
                                        opacity: 1.0,
                                    },
                                });
                            }
                        }
                    }
                }
            }
        }

        Ok(commands)
    }

    fn bounds(&self) -> Option<Bounds> {
        if self.data.is_empty() {
            return None;
        }

        let mut min_x = f64::INFINITY;
        let mut max_x = f64::NEG_INFINITY;
        let mut min_y = f64::INFINITY;
        let mut max_y = f64::NEG_INFINITY;

        for i in 0..self.data.len() {
            if let Some(point) = self.data.get_point(i) {
                if let (Some(x), Some(y)) = (point.get_number(0), point.get_number(1)) {
                    min_x = min_x.min(x);
                    max_x = max_x.max(x);
                    min_y = min_y.min(y);
                    max_y = max_y.max(y);
                }
            }
        }

        if min_x.is_finite() && max_x.is_finite() && min_y.is_finite() && max_y.is_finite() {
            Some(Bounds::new(min_x, min_y, max_x - min_x, max_y - min_y))
        } else {
            None
        }
    }

    fn is_visible(&self) -> bool {
        self.config.visible
    }

    fn z_index(&self) -> i32 {
        self.config.z_index
    }

    fn clone_series(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_scatter_series_creation() {
        let series = ScatterSeries::new("Test Scatter")
            .data(vec![(1.0, 2.0), (3.0, 4.0), (5.0, 6.0)])
            .color(Color::rgb(1.0, 0.0, 0.0))
            .symbol_size(10.0);

        assert_eq!(series.name(), "Test Scatter");
        assert_eq!(series.series_type(), SeriesType::Scatter);
        assert_eq!(series.symbol_size, 10.0);
        assert_eq!(series.data.len(), 3);
    }
}
// ============================================================================
// 图表基础架构集成 - ChartBase 实现
// ============================================================================

/// 为 ScatterSeries 实现 ChartBase trait
impl ChartBase for ScatterSeries {
    type DataType = DataSet;

    fn name(&self) -> &str {
        &self.config.name
    }

    fn raw_data(&self) -> &Self::DataType {
        &self.data
    }

    fn to_dataset(&self) -> DataSet {
        self.data.clone()
    }

    fn visible(&self) -> bool {
        self.config.visible
    }

    fn z_index(&self) -> i32 {
        self.config.z_index
    }

    fn bounds(&self) -> Option<Bounds> {
        BoundsCalculator::from_dataset(&self.data)
    }

    fn config(&self) -> &ChartConfig {
        &self.config
    }

    fn config_mut(&mut self) -> &mut ChartConfig {
        &mut self.config
    }
}

/// 为 ScatterSeries 实现 ChartSeries trait
impl ChartSeries for ScatterSeries {
    // 所有方法都由默认实现提供，基于 ChartBase
}
