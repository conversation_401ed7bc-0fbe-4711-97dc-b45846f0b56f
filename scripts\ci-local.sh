#!/bin/bash
# 本地CI/CD脚本 - 在提交前运行以确保代码质量

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否安装了必要的工具
check_prerequisites() {
    log_info "检查必要工具..."
    
    if ! command -v cargo &> /dev/null; then
        log_error "Cargo 未安装，请先安装 Rust"
        exit 1
    fi
    
    if ! command -v rustfmt &> /dev/null; then
        log_warning "rustfmt 未安装，正在安装..."
        rustup component add rustfmt
    fi
    
    if ! command -v clippy &> /dev/null; then
        log_warning "clippy 未安装，正在安装..."
        rustup component add clippy
    fi
    
    log_success "必要工具检查完成"
}

# 代码格式检查
check_formatting() {
    log_info "检查代码格式..."
    
    if cargo fmt --all -- --check; then
        log_success "代码格式检查通过"
    else
        log_error "代码格式检查失败"
        log_info "运行 'cargo fmt --all' 来修复格式问题"
        return 1
    fi
}

# Clippy 检查
run_clippy() {
    log_info "运行 Clippy 检查..."
    
    if cargo clippy --all-targets --all-features -- -D warnings; then
        log_success "Clippy 检查通过"
    else
        log_error "Clippy 检查失败"
        return 1
    fi
}

# 构建检查
build_check() {
    log_info "检查构建..."
    
    if cargo build --workspace --all-features; then
        log_success "构建检查通过"
    else
        log_error "构建失败"
        return 1
    fi
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    if cargo test --workspace --all-features; then
        log_success "测试通过"
    else
        log_error "测试失败"
        return 1
    fi
}

# ECharts-rs 专项测试
test_echarts_modules() {
    log_info "运行 ECharts-rs 模块测试..."
    
    local modules=("rust-echarts-core" "rust-echarts-charts" "rust-echarts-themes" "rust-echarts-components")
    
    for module in "${modules[@]}"; do
        log_info "测试模块: $module"
        if cargo test --package "$module"; then
            log_success "$module 测试通过"
        else
            log_error "$module 测试失败"
            return 1
        fi
    done
    
    # 测试渲染器模块（需要特殊feature）
    log_info "测试模块: rust-echarts-renderer"
    if cargo test --package rust-echarts-renderer --features gpui-backend; then
        log_success "rust-echarts-renderer 测试通过"
    else
        log_error "rust-echarts-renderer 测试失败"
        return 1
    fi
}

# 文档生成检查
check_docs() {
    log_info "检查文档生成..."
    
    if cargo doc --all-features --no-deps --document-private-items; then
        log_success "文档生成成功"
    else
        log_error "文档生成失败"
        return 1
    fi
}

# 安全审计
security_audit() {
    log_info "运行安全审计..."
    
    if command -v cargo-audit &> /dev/null; then
        if cargo audit; then
            log_success "安全审计通过"
        else
            log_warning "安全审计发现问题，请检查"
            return 1
        fi
    else
        log_warning "cargo-audit 未安装，跳过安全审计"
        log_info "运行 'cargo install cargo-audit' 来安装"
    fi
}

# 构建示例程序
build_examples() {
    log_info "构建示例程序..."
    
    if cargo build --examples --all-features; then
        log_success "示例程序构建成功"
    else
        log_error "示例程序构建失败"
        return 1
    fi
    
    # 特别构建GPU加速演示
    log_info "构建GPU加速演示..."
    if cargo build --example gpu_acceleration_demo --package rust-echarts-renderer --features gpui-backend; then
        log_success "GPU加速演示构建成功"
    else
        log_error "GPU加速演示构建失败"
        return 1
    fi
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    cargo clean
    log_success "清理完成"
}

# 主函数
main() {
    echo "========================================"
    echo "    ECharts-rs 本地 CI/CD 检查"
    echo "========================================"
    echo ""
    
    local start_time=$(date +%s)
    local failed_checks=()
    
    # 运行所有检查
    check_prerequisites || failed_checks+=("prerequisites")
    
    check_formatting || failed_checks+=("formatting")
    
    run_clippy || failed_checks+=("clippy")
    
    build_check || failed_checks+=("build")
    
    run_tests || failed_checks+=("tests")
    
    test_echarts_modules || failed_checks+=("echarts-tests")
    
    check_docs || failed_checks+=("docs")
    
    security_audit || failed_checks+=("security")
    
    build_examples || failed_checks+=("examples")
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    echo "========================================"
    echo "           检查结果总结"
    echo "========================================"
    echo "总耗时: ${duration}秒"
    echo ""
    
    if [ ${#failed_checks[@]} -eq 0 ]; then
        log_success "所有检查都通过了！🎉"
        log_info "代码已准备好提交"
        exit 0
    else
        log_error "以下检查失败:"
        for check in "${failed_checks[@]}"; do
            echo "  - $check"
        done
        echo ""
        log_error "请修复上述问题后再次运行"
        exit 1
    fi
}

# 处理命令行参数
case "${1:-}" in
    "clean")
        cleanup
        ;;
    "format")
        cargo fmt --all
        log_success "代码格式化完成"
        ;;
    "fix")
        log_info "尝试自动修复问题..."
        cargo fmt --all
        cargo clippy --all-targets --all-features --fix --allow-dirty
        log_success "自动修复完成，请检查修改"
        ;;
    "help"|"-h"|"--help")
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  (无参数)  运行完整的CI检查"
        echo "  clean     清理构建文件"
        echo "  format    格式化代码"
        echo "  fix       尝试自动修复问题"
        echo "  help      显示此帮助信息"
        ;;
    *)
        main
        ;;
esac
