//! 曲线图辅助函数
//!
//! 包含各种特殊效果的曲线图生成函数

/// 创建渐变色曲线图
pub fn create_gradient_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    format!(r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="multiGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#4ecdc4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6c5ce7;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <text x="50%" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">{}</text>
  <line x1="80" y1="340" x2="520" y2="340" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="60" x2="80" y2="340" stroke="#666" stroke-width="2"/>
  <path d="M 80 200 L 120 180 L 160 220 L 200 160 L 240 240 L 280 140 L 320 200 L 360 120 L 400 180 L 440 100 L 480 160 L 520 120" stroke="url(#multiGradient)" stroke-width="5" fill="none"/>
  <circle cx="80" cy="200" r="5" fill="url(#multiGradient)" stroke="white" stroke-width="2"/>
  <circle cx="160" cy="220" r="5" fill="url(#multiGradient)" stroke="white" stroke-width="2"/>
  <circle cx="240" cy="240" r="5" fill="url(#multiGradient)" stroke="white" stroke-width="2"/>
  <circle cx="320" cy="200" r="5" fill="url(#multiGradient)" stroke="white" stroke-width="2"/>
  <circle cx="400" cy="180" r="5" fill="url(#multiGradient)" stroke="white" stroke-width="2"/>
  <circle cx="480" cy="160" r="5" fill="url(#multiGradient)" stroke="white" stroke-width="2"/>
</svg>"#, width, height, title)
}

/// 创建虚线曲线图
pub fn create_dashed_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    format!(r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <text x="50%" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">{}</text>
  <line x1="80" y1="340" x2="520" y2="340" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="60" x2="80" y2="340" stroke="#666" stroke-width="2"/>
  <path d="M 80 250 L 120 220 L 160 180 L 200 200 L 240 160 L 280 140 L 320 120 L 360 100 L 400 80 L 440 60 L 480 80 L 520 100" stroke="#e74c3c" stroke-width="3" stroke-dasharray="10,5" fill="none"/>
  <rect x="76" y="246" width="8" height="8" fill="#e74c3c" stroke="white" stroke-width="2"/>
  <rect x="156" y="176" width="8" height="8" fill="#e74c3c" stroke="white" stroke-width="2"/>
  <rect x="236" y="156" width="8" height="8" fill="#e74c3c" stroke="white" stroke-width="2"/>
  <rect x="316" y="116" width="8" height="8" fill="#e74c3c" stroke="white" stroke-width="2"/>
  <rect x="396" y="76" width="8" height="8" fill="#e74c3c" stroke="white" stroke-width="2"/>
  <rect x="476" y="76" width="8" height="8" fill="#e74c3c" stroke="white" stroke-width="2"/>
</svg>"#, width, height, title)
}

/// 创建粗细变化曲线图
pub fn create_thick_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    format!(r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <text x="50%" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">{}</text>
  <line x1="80" y1="340" x2="520" y2="340" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="60" x2="80" y2="340" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="280" x2="120" y2="260" stroke="#9b59b6" stroke-width="2"/>
  <line x1="120" y1="260" x2="160" y2="240" stroke="#9b59b6" stroke-width="3"/>
  <line x1="160" y1="240" x2="200" y2="220" stroke="#9b59b6" stroke-width="4"/>
  <line x1="200" y1="220" x2="240" y2="200" stroke="#9b59b6" stroke-width="5"/>
  <line x1="240" y1="200" x2="280" y2="180" stroke="#9b59b6" stroke-width="6"/>
  <line x1="280" y1="180" x2="320" y2="160" stroke="#9b59b6" stroke-width="7"/>
  <line x1="320" y1="160" x2="360" y2="140" stroke="#9b59b6" stroke-width="8"/>
  <line x1="360" y1="140" x2="400" y2="120" stroke="#9b59b6" stroke-width="9"/>
  <line x1="400" y1="120" x2="440" y2="100" stroke="#9b59b6" stroke-width="10"/>
  <circle cx="80" cy="280" r="3" fill="#9b59b6" stroke="white" stroke-width="2"/>
  <circle cx="160" cy="240" r="4" fill="#9b59b6" stroke="white" stroke-width="2"/>
  <circle cx="240" cy="200" r="5" fill="#9b59b6" stroke="white" stroke-width="2"/>
  <circle cx="320" cy="160" r="6" fill="#9b59b6" stroke="white" stroke-width="2"/>
  <circle cx="400" cy="120" r="7" fill="#9b59b6" stroke="white" stroke-width="2"/>
</svg>"#, width, height, title)
}

/// 创建阴影效果曲线图
pub fn create_shadow_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    format!(r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter id="bigShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="4" dy="4" stdDeviation="4" flood-color="#000000" flood-opacity="0.4"/>
    </filter>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <text x="50%" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">{}</text>
  <line x1="80" y1="340" x2="620" y2="340" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="60" x2="80" y2="340" stroke="#666" stroke-width="2"/>
  <path d="M 80 200 L 120 180 L 160 220 L 200 160 L 240 240 L 280 140 L 320 200 L 360 120 L 400 180 L 440 100 L 480 160 L 520 120 L 560 140 L 600 100" stroke="#3498db" stroke-width="4" fill="none" filter="url(#bigShadow)"/>
  <circle cx="80" cy="200" r="6" fill="#3498db" stroke="white" stroke-width="2" filter="url(#glow)"/>
  <circle cx="160" cy="220" r="6" fill="#3498db" stroke="white" stroke-width="2" filter="url(#glow)"/>
  <circle cx="240" cy="240" r="6" fill="#3498db" stroke="white" stroke-width="2" filter="url(#glow)"/>
  <circle cx="320" cy="200" r="6" fill="#3498db" stroke="white" stroke-width="2" filter="url(#glow)"/>
  <circle cx="400" cy="180" r="6" fill="#3498db" stroke="white" stroke-width="2" filter="url(#glow)"/>
  <circle cx="480" cy="160" r="6" fill="#3498db" stroke="white" stroke-width="2" filter="url(#glow)"/>
  <circle cx="560" cy="140" r="6" fill="#3498db" stroke="white" stroke-width="2" filter="url(#glow)"/>
</svg>"#, width, height, title)
}

/// 创建双Y轴曲线图
pub fn create_dual_axis_line_chart(title: &str, width: f64, height: f64) -> String {
    format!(r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <text x="50%" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">{}</text>
  <line x1="80" y1="420" x2="720" y2="420" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="60" x2="80" y2="420" stroke="#666" stroke-width="2"/>
  <line x1="720" y1="60" x2="720" y2="420" stroke="#e74c3c" stroke-width="2"/>
  
  <!-- 温度曲线 -->
  <path d="M 80 300 L 120 280 L 160 260 L 200 240 L 240 220 L 280 200 L 320 180 L 360 160 L 400 140 L 440 160 L 480 180 L 520 200 L 560 220 L 600 240 L 640 260 L 680 280 L 720 300" stroke="#3498db" stroke-width="3" fill="none"/>
  
  <!-- 湿度曲线 -->
  <path d="M 80 350 L 120 340 L 160 330 L 200 320 L 240 310 L 280 300 L 320 290 L 360 280 L 400 270 L 440 280 L 480 290 L 520 300 L 560 310 L 600 320 L 640 330 L 680 340 L 720 350" stroke="#e74c3c" stroke-width="3" fill="none"/>
  
  <!-- 数据点 -->
  <circle cx="160" cy="260" r="4" fill="#3498db" stroke="white" stroke-width="2"/>
  <circle cx="320" cy="180" r="4" fill="#3498db" stroke="white" stroke-width="2"/>
  <circle cx="480" cy="180" r="4" fill="#3498db" stroke="white" stroke-width="2"/>
  <circle cx="640" cy="260" r="4" fill="#3498db" stroke="white" stroke-width="2"/>
  
  <circle cx="160" cy="330" r="4" fill="#e74c3c" stroke="white" stroke-width="2"/>
  <circle cx="320" cy="290" r="4" fill="#e74c3c" stroke="white" stroke-width="2"/>
  <circle cx="480" cy="290" r="4" fill="#e74c3c" stroke="white" stroke-width="2"/>
  <circle cx="640" cy="330" r="4" fill="#e74c3c" stroke="white" stroke-width="2"/>
  
  <!-- 图例 -->
  <line x1="100" y1="80" x2="120" y2="80" stroke="#3498db" stroke-width="3"/>
  <text x="125" y="85" font-size="12" fill="#333">温度 (°C)</text>
  <line x1="200" y1="80" x2="220" y2="80" stroke="#e74c3c" stroke-width="3"/>
  <text x="225" y="85" font-size="12" fill="#333">湿度 (%)</text>
  
  <!-- Y轴标签 -->
  <text x="70" y="100" text-anchor="end" font-size="12" fill="#3498db">30</text>
  <text x="70" y="180" text-anchor="end" font-size="12" fill="#3498db">20</text>
  <text x="70" y="260" text-anchor="end" font-size="12" fill="#3498db">10</text>
  <text x="70" y="340" text-anchor="end" font-size="12" fill="#3498db">0</text>
  
  <text x="730" y="100" text-anchor="start" font-size="12" fill="#e74c3c">80</text>
  <text x="730" y="180" text-anchor="start" font-size="12" fill="#e74c3c">60</text>
  <text x="730" y="260" text-anchor="start" font-size="12" fill="#e74c3c">40</text>
  <text x="730" y="340" text-anchor="start" font-size="12" fill="#e74c3c">20</text>
</svg>"#, width, height, title)
}

/// 创建阶梯曲线图
pub fn create_step_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    format!(r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <text x="50%" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">{}</text>
  <line x1="80" y1="340" x2="520" y2="340" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="60" x2="80" y2="340" stroke="#666" stroke-width="2"/>
  
  <!-- 阶梯路径 -->
  <path d="M 80 300 L 120 300 L 120 280 L 160 280 L 160 260 L 200 260 L 200 240 L 240 240 L 240 220 L 280 220 L 280 200 L 320 200 L 320 180 L 360 180 L 360 160 L 400 160 L 400 140 L 440 140 L 440 120 L 480 120 L 480 100 L 520 100" stroke="#f39c12" stroke-width="3" fill="none"/>
  
  <!-- 数据点 -->
  <circle cx="80" cy="300" r="4" fill="#f39c12" stroke="white" stroke-width="2"/>
  <circle cx="120" cy="280" r="4" fill="#f39c12" stroke="white" stroke-width="2"/>
  <circle cx="160" cy="260" r="4" fill="#f39c12" stroke="white" stroke-width="2"/>
  <circle cx="200" cy="240" r="4" fill="#f39c12" stroke="white" stroke-width="2"/>
  <circle cx="240" cy="220" r="4" fill="#f39c12" stroke="white" stroke-width="2"/>
  <circle cx="280" cy="200" r="4" fill="#f39c12" stroke="white" stroke-width="2"/>
  <circle cx="320" cy="180" r="4" fill="#f39c12" stroke="white" stroke-width="2"/>
  <circle cx="360" cy="160" r="4" fill="#f39c12" stroke="white" stroke-width="2"/>
  <circle cx="400" cy="140" r="4" fill="#f39c12" stroke="white" stroke-width="2"/>
  <circle cx="440" cy="120" r="4" fill="#f39c12" stroke="white" stroke-width="2"/>
  <circle cx="480" cy="100" r="4" fill="#f39c12" stroke="white" stroke-width="2"/>
</svg>"#, width, height, title)
}

/// 创建散点连线图
pub fn create_scatter_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    format!(r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <text x="50%" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">{}</text>
  <line x1="80" y1="340" x2="570" y2="340" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="60" x2="80" y2="340" stroke="#666" stroke-width="2"/>
  
  <!-- 连接线 -->
  <path d="M 100 280 L 140 240 L 180 300 L 220 200 L 260 260 L 300 180 L 340 220 L 380 160 L 420 200 L 460 140 L 500 180 L 540 120" stroke="#16a085" stroke-width="2" fill="none"/>
  
  <!-- 散点 -->
  <circle cx="100" cy="280" r="8" fill="#16a085" fill-opacity="0.7" stroke="white" stroke-width="2"/>
  <circle cx="140" cy="240" r="6" fill="#16a085" fill-opacity="0.7" stroke="white" stroke-width="2"/>
  <circle cx="180" cy="300" r="10" fill="#16a085" fill-opacity="0.7" stroke="white" stroke-width="2"/>
  <circle cx="220" cy="200" r="7" fill="#16a085" fill-opacity="0.7" stroke="white" stroke-width="2"/>
  <circle cx="260" cy="260" r="9" fill="#16a085" fill-opacity="0.7" stroke="white" stroke-width="2"/>
  <circle cx="300" cy="180" r="5" fill="#16a085" fill-opacity="0.7" stroke="white" stroke-width="2"/>
  <circle cx="340" cy="220" r="8" fill="#16a085" fill-opacity="0.7" stroke="white" stroke-width="2"/>
  <circle cx="380" cy="160" r="6" fill="#16a085" fill-opacity="0.7" stroke="white" stroke-width="2"/>
  <circle cx="420" cy="200" r="7" fill="#16a085" fill-opacity="0.7" stroke="white" stroke-width="2"/>
  <circle cx="460" cy="140" r="9" fill="#16a085" fill-opacity="0.7" stroke="white" stroke-width="2"/>
  <circle cx="500" cy="180" r="8" fill="#16a085" fill-opacity="0.7" stroke="white" stroke-width="2"/>
  <circle cx="540" cy="120" r="10" fill="#16a085" fill-opacity="0.7" stroke="white" stroke-width="2"/>
</svg>"#, width, height, title)
}

/// 创建动画增长曲线图
pub fn create_animated_growth_chart(title: &str, width: f64, height: f64) -> String {
    format!(r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <text x="50%" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">{}</text>
  <line x1="80" y1="340" x2="620" y2="340" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="60" x2="80" y2="340" stroke="#666" stroke-width="2"/>

  <!-- 动画路径 -->
  <path d="M 80 320 L 120 300 L 160 280 L 200 250 L 240 220 L 280 180 L 320 140 L 360 100 L 400 80 L 440 60 L 480 50 L 520 40 L 560 30 L 600 20" stroke="#e67e22" stroke-width="4" fill="none">
    <animate attributeName="stroke-dasharray" values="0,1000;1000,0" dur="3s" repeatCount="indefinite"/>
  </path>

  <!-- 动画数据点 -->
  <circle cx="80" cy="320" r="5" fill="#e67e22" stroke="white" stroke-width="2">
    <animate attributeName="r" values="3;8;3" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="200" cy="250" r="5" fill="#e67e22" stroke="white" stroke-width="2">
    <animate attributeName="r" values="3;8;3" dur="2s" begin="0.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="320" cy="140" r="5" fill="#e67e22" stroke="white" stroke-width="2">
    <animate attributeName="r" values="3;8;3" dur="2s" begin="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="440" cy="60" r="5" fill="#e67e22" stroke="white" stroke-width="2">
    <animate attributeName="r" values="3;8;3" dur="2s" begin="1.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="560" cy="30" r="5" fill="#e67e22" stroke="white" stroke-width="2">
    <animate attributeName="r" values="3;8;3" dur="2s" begin="2s" repeatCount="indefinite"/>
  </circle>
</svg>"#, width, height, title)
}

/// 创建波浪动画曲线图
pub fn create_wave_animation_chart(title: &str, width: f64, height: f64) -> String {
    format!(r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <text x="50%" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">{}</text>
  <line x1="80" y1="340" x2="720" y2="340" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="60" x2="80" y2="340" stroke="#666" stroke-width="2"/>

  <!-- 波浪路径 -->
  <path d="M 80 200 Q 120 150 160 200 T 240 200 T 320 200 T 400 200 T 480 200 T 560 200 T 640 200 T 720 200" stroke="#3498db" stroke-width="4" fill="none">
    <animateTransform attributeName="transform" type="translate" values="0,0;-80,0;0,0" dur="2s" repeatCount="indefinite"/>
  </path>

  <!-- 第二层波浪 -->
  <path d="M 80 220 Q 120 170 160 220 T 240 220 T 320 220 T 400 220 T 480 220 T 560 220 T 640 220 T 720 220" stroke="#74b9ff" stroke-width="3" fill="none" opacity="0.7">
    <animateTransform attributeName="transform" type="translate" values="0,0;-80,0;0,0" dur="2.5s" repeatCount="indefinite"/>
  </path>

  <!-- 第三层波浪 -->
  <path d="M 80 240 Q 120 190 160 240 T 240 240 T 320 240 T 400 240 T 480 240 T 560 240 T 640 240 T 720 240" stroke="#a29bfe" stroke-width="2" fill="none" opacity="0.5">
    <animateTransform attributeName="transform" type="translate" values="0,0;-80,0;0,0" dur="3s" repeatCount="indefinite"/>
  </path>
</svg>"#, width, height, title)
}

/// 创建脉冲效果曲线图
pub fn create_pulse_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    format!(r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <text x="50%" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">{}</text>
  <line x1="80" y1="340" x2="620" y2="340" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="60" x2="80" y2="340" stroke="#666" stroke-width="2"/>

  <!-- 脉冲曲线 -->
  <path d="M 80 200 L 120 180 L 160 220 L 200 160 L 240 240 L 280 140 L 320 200 L 360 120 L 400 180 L 440 100 L 480 160 L 520 120 L 560 140 L 600 100" stroke="#e74c3c" stroke-width="3" fill="none">
    <animate attributeName="stroke-width" values="3;6;3" dur="1s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.7;1;0.7" dur="1s" repeatCount="indefinite"/>
  </path>

  <!-- 脉冲数据点 -->
  <circle cx="160" cy="220" r="4" fill="#e74c3c" stroke="white" stroke-width="2">
    <animate attributeName="r" values="4;10;4" dur="1.5s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="280" cy="140" r="4" fill="#e74c3c" stroke="white" stroke-width="2">
    <animate attributeName="r" values="4;10;4" dur="1.5s" begin="0.3s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="1;0.3;1" dur="1.5s" begin="0.3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="400" cy="180" r="4" fill="#e74c3c" stroke="white" stroke-width="2">
    <animate attributeName="r" values="4;10;4" dur="1.5s" begin="0.6s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="1;0.3;1" dur="1.5s" begin="0.6s" repeatCount="indefinite"/>
  </circle>
  <circle cx="520" cy="120" r="4" fill="#e74c3c" stroke="white" stroke-width="2">
    <animate attributeName="r" values="4;10;4" dur="1.5s" begin="0.9s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="1;0.3;1" dur="1.5s" begin="0.9s" repeatCount="indefinite"/>
  </circle>
</svg>"#, width, height, title)
}

/// 创建闪烁点曲线图
pub fn create_blinking_points_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    format!(r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <text x="50%" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">{}</text>
  <line x1="80" y1="340" x2="570" y2="340" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="60" x2="80" y2="340" stroke="#666" stroke-width="2"/>

  <!-- 基础曲线 -->
  <path d="M 80 280 L 120 260 L 160 240 L 200 220 L 240 200 L 280 180 L 320 160 L 360 140 L 400 120 L 440 100 L 480 80 L 520 60 L 560 40" stroke="#2ecc71" stroke-width="3" fill="none"/>

  <!-- 闪烁数据点 -->
  <circle cx="80" cy="280" r="5" fill="#2ecc71" stroke="white" stroke-width="2">
    <animate attributeName="opacity" values="1;0;1" dur="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="160" cy="240" r="5" fill="#2ecc71" stroke="white" stroke-width="2">
    <animate attributeName="opacity" values="1;0;1" dur="1s" begin="0.2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="240" cy="200" r="5" fill="#2ecc71" stroke="white" stroke-width="2">
    <animate attributeName="opacity" values="1;0;1" dur="1s" begin="0.4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="320" cy="160" r="5" fill="#2ecc71" stroke="white" stroke-width="2">
    <animate attributeName="opacity" values="1;0;1" dur="1s" begin="0.6s" repeatCount="indefinite"/>
  </circle>
  <circle cx="400" cy="120" r="5" fill="#2ecc71" stroke="white" stroke-width="2">
    <animate attributeName="opacity" values="1;0;1" dur="1s" begin="0.8s" repeatCount="indefinite"/>
  </circle>
  <circle cx="480" cy="80" r="5" fill="#2ecc71" stroke="white" stroke-width="2">
    <animate attributeName="opacity" values="1;0;1" dur="1s" begin="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="560" cy="40" r="5" fill="#2ecc71" stroke="white" stroke-width="2">
    <animate attributeName="opacity" values="1;0;1" dur="1s" begin="1.2s" repeatCount="indefinite"/>
  </circle>

  <!-- 闪烁光环 -->
  <circle cx="240" cy="200" r="15" fill="none" stroke="#2ecc71" stroke-width="2" opacity="0.3">
    <animate attributeName="r" values="5;20;5" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.5;0;0.5" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="400" cy="120" r="15" fill="none" stroke="#2ecc71" stroke-width="2" opacity="0.3">
    <animate attributeName="r" values="5;20;5" dur="2s" begin="1s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.5;0;0.5" dur="2s" begin="1s" repeatCount="indefinite"/>
  </circle>
</svg>"#, width, height, title)
}

/// 生成展示页面
pub fn generate_line_charts_showcase(output_dir: &str) -> std::result::Result<(), std::io::Error> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全面的曲线图展示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1600px; margin: 0 auto; }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .section {
            background: rgba(255,255,255,0.95);
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .chart-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        .chart-svg {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h2 { color: #333; margin-bottom: 20px; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        .description {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 全面的曲线图展示</h1>
            <p class="description">展现 ECharts-rs 强大的曲线图功能</p>
            <div class="feature-list">
                <div class="feature">
                    <h3>📊 基础曲线</h3>
                    <p>线性、指数、对数增长</p>
                </div>
                <div class="feature">
                    <h3>🔢 数学函数</h3>
                    <p>三角函数、多项式</p>
                </div>
                <div class="feature">
                    <h3>📈 数据分析</h3>
                    <p>股票、温度、销售</p>
                </div>
                <div class="feature">
                    <h3>🎨 样式效果</h3>
                    <p>渐变、虚线、阴影</p>
                </div>
                <div class="feature">
                    <h3>🔧 复杂场景</h3>
                    <p>多系列、双轴、面积</p>
                </div>
                <div class="feature">
                    <h3>🎬 动态效果</h3>
                    <p>动画、波浪、脉冲</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 基础曲线图</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">线性增长曲线</div>
                    <object class="chart-svg" data="01_linear_growth.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">指数增长曲线</div>
                    <object class="chart-svg" data="02_exponential_growth.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">对数增长曲线</div>
                    <object class="chart-svg" data="03_logarithmic_growth.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">波动曲线</div>
                    <object class="chart-svg" data="04_volatile_curve.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔢 数学函数曲线</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">正弦函数曲线</div>
                    <object class="chart-svg" data="05_sine_function.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">余弦函数曲线</div>
                    <object class="chart-svg" data="06_cosine_function.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">正切函数曲线</div>
                    <object class="chart-svg" data="07_tangent_function.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">复合三角函数</div>
                    <object class="chart-svg" data="08_complex_trigonometric.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">二次函数曲线</div>
                    <object class="chart-svg" data="09_quadratic_function.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">三次函数曲线</div>
                    <object class="chart-svg" data="10_cubic_function.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📈 数据分析曲线</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">股票价格走势</div>
                    <object class="chart-svg" data="11_stock_price.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">24小时温度变化</div>
                    <object class="chart-svg" data="12_temperature_curve.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">网站访问量趋势</div>
                    <object class="chart-svg" data="13_website_traffic.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">年度销售业绩</div>
                    <object class="chart-svg" data="14_sales_performance.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎨 样式变化曲线</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">渐变色曲线</div>
                    <object class="chart-svg" data="15_gradient_line.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">虚线曲线</div>
                    <object class="chart-svg" data="16_dashed_line.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">粗细变化曲线</div>
                    <object class="chart-svg" data="17_thick_line.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">阴影效果曲线</div>
                    <object class="chart-svg" data="18_shadow_line.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 复杂场景曲线</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">多产品销售对比</div>
                    <object class="chart-svg" data="19_multi_series_comparison.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">温度与湿度双轴图</div>
                    <object class="chart-svg" data="20_dual_axis_chart.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">面积填充曲线</div>
                    <object class="chart-svg" data="21_area_filled_line.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">阶梯曲线</div>
                    <object class="chart-svg" data="22_step_line.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">散点连线图</div>
                    <object class="chart-svg" data="23_scatter_line.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎬 动态效果曲线</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">动画增长曲线</div>
                    <object class="chart-svg" data="24_animated_growth.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">波浪动画曲线</div>
                    <object class="chart-svg" data="25_wave_animation.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">脉冲效果曲线</div>
                    <object class="chart-svg" data="26_pulse_line.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">闪烁点曲线</div>
                    <object class="chart-svg" data="27_blinking_points.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h2>🎉 曲线图功能总结</h2>
            <p>ECharts-rs 提供了丰富的曲线图功能：</p>
            <ul style="text-align: left; max-width: 800px; margin: 0 auto;">
                <li>✅ <strong>27种不同类型的曲线图</strong> - 覆盖各种应用场景</li>
                <li>✅ <strong>数学函数支持</strong> - 三角函数、多项式、指数对数</li>
                <li>✅ <strong>数据分析图表</strong> - 股票、温度、销售等实际应用</li>
                <li>✅ <strong>丰富的样式效果</strong> - 渐变、虚线、阴影、粗细变化</li>
                <li>✅ <strong>复杂场景支持</strong> - 多系列、双轴、面积填充、阶梯</li>
                <li>✅ <strong>动态效果</strong> - CSS动画、波浪、脉冲、闪烁</li>
                <li>✅ <strong>高质量渲染</strong> - 完整的坐标系统和标签</li>
                <li>✅ <strong>响应式设计</strong> - 适配不同屏幕尺寸</li>
            </ul>
        </div>
    </div>
</body>
</html>"#;

    std::fs::write(format!("{}/line_charts_showcase.html", output_dir), html_content)
}
