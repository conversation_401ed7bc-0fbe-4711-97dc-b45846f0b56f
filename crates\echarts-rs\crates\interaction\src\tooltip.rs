//! 工具提示系统
//!
//! 提供悬停时显示数据详情的工具提示功能

use crate::{InteractionResult, HoveredElement, TooltipContent, TooltipItem};
use echarts_core::{Point, Color, Result};
use serde::{Serialize, Deserialize};

/// 工具提示管理器
pub struct TooltipManager {
    /// 配置选项
    config: TooltipConfig,
    /// 当前状态
    state: TooltipState,
    /// 内容格式化器
    formatter: Option<Box<dyn TooltipFormatter + Send + Sync>>,
}

/// 工具提示配置
#[derive(Debug, Clone)]
pub struct TooltipConfig {
    /// 是否启用
    pub enabled: bool,
    /// 触发方式
    pub trigger: TooltipTrigger,
    /// 显示延迟（毫秒）
    pub show_delay: u64,
    /// 隐藏延迟（毫秒）
    pub hide_delay: u64,
    /// 位置策略
    pub position: TooltipPosition,
    /// 样式配置
    pub style: TooltipStyle,
    /// 动画配置
    pub animation: TooltipAnimation,
}

/// 工具提示状态
#[derive(Debug, Clone)]
pub struct TooltipState {
    /// 是否可见
    pub visible: bool,
    /// 当前位置
    pub position: Option<Point>,
    /// 当前内容
    pub content: Option<TooltipContent>,
    /// 显示时间戳
    pub show_timestamp: Option<u64>,
    /// 隐藏时间戳
    pub hide_timestamp: Option<u64>,
}

/// 工具提示触发方式
#[derive(Debug, Clone, PartialEq)]
pub enum TooltipTrigger {
    /// 鼠标悬停
    Hover,
    /// 鼠标点击
    Click,
    /// 手动触发
    Manual,
}

/// 工具提示位置策略
#[derive(Debug, Clone)]
pub enum TooltipPosition {
    /// 跟随鼠标
    Follow,
    /// 固定位置
    Fixed(Point),
    /// 相对于元素的位置
    Relative {
        anchor: TooltipAnchor,
        offset: Point,
    },
    /// 自动定位（避免超出边界）
    Auto,
}

/// 工具提示锚点
#[derive(Debug, Clone, PartialEq)]
pub enum TooltipAnchor {
    /// 顶部
    Top,
    /// 底部
    Bottom,
    /// 左侧
    Left,
    /// 右侧
    Right,
    /// 顶部左侧
    TopLeft,
    /// 顶部右侧
    TopRight,
    /// 底部左侧
    BottomLeft,
    /// 底部右侧
    BottomRight,
    /// 中心
    Center,
}

/// 工具提示样式
#[derive(Debug, Clone)]
pub struct TooltipStyle {
    /// 背景颜色
    pub background_color: Color,
    /// 边框颜色
    pub border_color: Color,
    /// 边框宽度
    pub border_width: f64,
    /// 圆角半径
    pub border_radius: f64,
    /// 内边距
    pub padding: [f64; 4], // top, right, bottom, left
    /// 字体大小
    pub font_size: f64,
    /// 字体颜色
    pub font_color: Color,
    /// 字体族
    pub font_family: String,
    /// 阴影
    pub shadow: Option<TooltipShadow>,
    /// 最大宽度
    pub max_width: Option<f64>,
    /// 最大高度
    pub max_height: Option<f64>,
}

/// 工具提示阴影
#[derive(Debug, Clone)]
pub struct TooltipShadow {
    /// 阴影颜色
    pub color: Color,
    /// 水平偏移
    pub offset_x: f64,
    /// 垂直偏移
    pub offset_y: f64,
    /// 模糊半径
    pub blur_radius: f64,
}

/// 工具提示动画
#[derive(Debug, Clone)]
pub struct TooltipAnimation {
    /// 是否启用动画
    pub enabled: bool,
    /// 显示动画持续时间（毫秒）
    pub show_duration: u64,
    /// 隐藏动画持续时间（毫秒）
    pub hide_duration: u64,
    /// 动画缓动函数
    pub easing: TooltipEasing,
}

/// 动画缓动函数
#[derive(Debug, Clone, PartialEq)]
pub enum TooltipEasing {
    /// 线性
    Linear,
    /// 缓入
    EaseIn,
    /// 缓出
    EaseOut,
    /// 缓入缓出
    EaseInOut,
}

/// 工具提示格式化器特征
pub trait TooltipFormatter: Send + Sync {
    /// 格式化工具提示内容
    fn format(&self, element: &HoveredElement) -> Result<TooltipContent>;
}

/// 默认工具提示格式化器
#[derive(Debug)]
pub struct DefaultTooltipFormatter;

impl TooltipFormatter for DefaultTooltipFormatter {
    fn format(&self, element: &HoveredElement) -> Result<TooltipContent> {
        let mut items = Vec::new();
        
        // 根据元素类型生成不同的内容
        match element.element_type {
            crate::ElementType::DataPoint => {
                if let Some(series_index) = element.series_index {
                    if let Some(data_index) = element.data_index {
                        items.push(TooltipItem {
                            label: "系列".to_string(),
                            value: format!("系列 {}", series_index),
                            color: None,
                            series_name: None,
                        });
                        
                        items.push(TooltipItem {
                            label: "数据点".to_string(),
                            value: format!("点 {}", data_index),
                            color: None,
                            series_name: None,
                        });
                    }
                }
                
                // 添加自定义数据
                for (key, value) in &element.data {
                    items.push(TooltipItem {
                        label: key.clone(),
                        value: value.clone(),
                        color: None,
                        series_name: None,
                    });
                }
            }
            crate::ElementType::Series => {
                items.push(TooltipItem {
                    label: "类型".to_string(),
                    value: "系列".to_string(),
                    color: None,
                    series_name: None,
                });
            }
            crate::ElementType::LegendItem => {
                items.push(TooltipItem {
                    label: "类型".to_string(),
                    value: "图例项".to_string(),
                    color: None,
                    series_name: None,
                });
            }
            _ => {
                items.push(TooltipItem {
                    label: "类型".to_string(),
                    value: format!("{:?}", element.element_type),
                    color: None,
                    series_name: None,
                });
            }
        }
        
        Ok(TooltipContent {
            title: Some("详细信息".to_string()),
            items,
            html: None,
        })
    }
}

impl Default for TooltipConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            trigger: TooltipTrigger::Hover,
            show_delay: 100,
            hide_delay: 100,
            position: TooltipPosition::Follow,
            style: TooltipStyle::default(),
            animation: TooltipAnimation::default(),
        }
    }
}

impl Default for TooltipStyle {
    fn default() -> Self {
        Self {
            background_color: Color::rgba(0.0, 0.0, 0.0, 0.8),
            border_color: Color::rgba(1.0, 1.0, 1.0, 0.2),
            border_width: 1.0,
            border_radius: 4.0,
            padding: [8.0, 12.0, 8.0, 12.0],
            font_size: 12.0,
            font_color: Color::rgb(1.0, 1.0, 1.0),
            font_family: "Arial, sans-serif".to_string(),
            shadow: Some(TooltipShadow {
                color: Color::rgba(0.0, 0.0, 0.0, 0.3),
                offset_x: 2.0,
                offset_y: 2.0,
                blur_radius: 4.0,
            }),
            max_width: Some(300.0),
            max_height: Some(200.0),
        }
    }
}

impl Default for TooltipAnimation {
    fn default() -> Self {
        Self {
            enabled: true,
            show_duration: 200,
            hide_duration: 150,
            easing: TooltipEasing::EaseOut,
        }
    }
}

impl Default for TooltipState {
    fn default() -> Self {
        Self {
            visible: false,
            position: None,
            content: None,
            show_timestamp: None,
            hide_timestamp: None,
        }
    }
}

impl TooltipManager {
    /// 创建新的工具提示管理器
    pub fn new(config: TooltipConfig) -> Self {
        Self {
            config,
            state: TooltipState::default(),
            formatter: Some(Box::new(DefaultTooltipFormatter)),
        }
    }
    
    /// 设置格式化器
    pub fn set_formatter(&mut self, formatter: Box<dyn TooltipFormatter + Send + Sync>) {
        self.formatter = Some(formatter);
    }
    
    /// 显示工具提示
    pub fn show(&mut self, position: Point, element: &HoveredElement) -> Result<InteractionResult> {
        if !self.config.enabled {
            return Ok(InteractionResult::None);
        }
        
        // 格式化内容
        let content = if let Some(formatter) = &self.formatter {
            formatter.format(element)?
        } else {
            TooltipContent::simple("信息", "无可用数据")
        };
        
        // 计算位置
        let tooltip_position = self.calculate_position(position, element);
        
        // 更新状态
        self.state.visible = true;
        self.state.position = Some(tooltip_position);
        self.state.content = Some(content.clone());
        self.state.show_timestamp = Some(self.current_timestamp());
        
        Ok(InteractionResult::ShowTooltip {
            position: tooltip_position,
            content,
        })
    }
    
    /// 隐藏工具提示
    pub fn hide(&mut self) -> Result<InteractionResult> {
        if !self.state.visible {
            return Ok(InteractionResult::None);
        }
        
        self.state.visible = false;
        self.state.position = None;
        self.state.content = None;
        self.state.hide_timestamp = Some(self.current_timestamp());
        
        Ok(InteractionResult::HideTooltip)
    }
    
    /// 更新工具提示位置
    pub fn update_position(&mut self, position: Point) -> Result<InteractionResult> {
        if !self.state.visible {
            return Ok(InteractionResult::None);
        }
        
        let tooltip_position = match self.config.position {
            TooltipPosition::Follow => position,
            _ => return Ok(InteractionResult::None),
        };
        
        self.state.position = Some(tooltip_position);
        
        if let Some(content) = &self.state.content {
            Ok(InteractionResult::ShowTooltip {
                position: tooltip_position,
                content: content.clone(),
            })
        } else {
            Ok(InteractionResult::None)
        }
    }
    
    /// 计算工具提示位置
    fn calculate_position(&self, mouse_position: Point, _element: &HoveredElement) -> Point {
        match &self.config.position {
            TooltipPosition::Follow => Point {
                x: mouse_position.x + 10.0,
                y: mouse_position.y - 10.0,
            },
            TooltipPosition::Fixed(pos) => *pos,
            TooltipPosition::Relative { anchor: _, offset } => Point {
                x: mouse_position.x + offset.x,
                y: mouse_position.y + offset.y,
            },
            TooltipPosition::Auto => {
                // 简单的自动定位逻辑，避免超出边界
                Point {
                    x: mouse_position.x + 10.0,
                    y: mouse_position.y - 10.0,
                }
            }
        }
    }
    
    /// 获取当前时间戳
    fn current_timestamp(&self) -> u64 {
        std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as u64
    }
    
    /// 获取当前状态
    pub fn get_state(&self) -> &TooltipState {
        &self.state
    }
    
    /// 是否可见
    pub fn is_visible(&self) -> bool {
        self.state.visible
    }
}
