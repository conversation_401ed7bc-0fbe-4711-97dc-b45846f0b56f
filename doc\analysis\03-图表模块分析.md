# ECharts 图表模块分析

## 模块概述

`src/chart/` 目录包含了 ECharts 支持的所有图表类型实现，每种图表类型都有独立的目录和完整的实现。

## 图表类型分类

### 1. 基础图表类型
- **bar**: 柱状图/条形图
- **line**: 折线图/面积图
- **pie**: 饼图/环形图
- **scatter**: 散点图
- **effectScatter**: 带涟漪特效的散点图

### 2. 统计图表类型
- **boxplot**: 箱线图
- **candlestick**: K线图
- **heatmap**: 热力图
- **parallel**: 平行坐标图

### 3. 关系图表类型
- **graph**: 关系图/网络图
- **tree**: 树图
- **treemap**: 矩形树图
- **sankey**: 桑基图
- **chord**: 和弦图

### 4. 地理图表类型
- **map**: 地图
- **lines**: 路径图

### 5. 其他图表类型
- **radar**: 雷达图
- **gauge**: 仪表盘
- **funnel**: 漏斗图
- **sunburst**: 旭日图
- **themeRiver**: 主题河流图
- **custom**: 自定义图表
- **pictorialBar**: 象形柱图

## 图表架构模式

### 1. 标准图表结构

每个图表类型都遵循统一的架构模式：

```
chart/[chartType]/
├── [ChartType]Series.ts    # 数据模型定义
├── [ChartType]View.ts      # 视图渲染逻辑
├── install.ts              # 安装注册文件
├── layout.ts               # 布局算法（可选）
├── visual.ts               # 视觉映射（可选）
└── helper files            # 辅助功能文件
```

### 2. 核心组件分析

#### Series 模型 (数据模型)
**职责**: 定义图表的数据结构和配置选项
**主要功能**:
- 配置选项类型定义
- 数据处理和转换
- 默认值设置
- 数据验证

**示例 - BarSeries.ts**:
```typescript
export interface BarSeriesOption extends BaseBarSeriesOption {
    type?: 'bar'
    coordinateSystem?: 'cartesian2d' | 'polar'
    clip?: boolean
    roundCap?: boolean
    showBackground?: boolean
    backgroundStyle?: ItemStyleOption
    data?: (BarDataItemOption | OptionDataValue)[]
}
```

#### View 组件 (视图渲染)
**职责**: 负责图表的实际渲染和交互
**主要功能**:
- 图形元素创建和更新
- 动画处理
- 事件绑定
- 性能优化

**渲染流程**:
1. `render()`: 主渲染方法
2. `updateData()`: 数据更新
3. `dispose()`: 资源清理
4. `highlight()`/`downplay()`: 高亮处理

#### Install 文件 (注册安装)
**职责**: 将图表类型注册到 ECharts 系统
**主要功能**:
- 组件注册
- 依赖声明
- 默认配置

### 3. 辅助模块

#### helper/ 目录
包含图表渲染的通用辅助功能：
- **Symbol.ts**: 符号绘制
- **Line.ts**: 线条绘制
- **LineDraw.ts**: 线条批量绘制
- **SymbolDraw.ts**: 符号批量绘制
- **EffectSymbol.ts**: 特效符号
- **createSeriesData.ts**: 系列数据创建

## 主要图表类型详细分析

### 1. Bar 图表 (柱状图)

**文件结构**:
```
bar/
├── BarSeries.ts           # 柱状图数据模型
├── BarView.ts             # 柱状图视图
├── BaseBarSeries.ts       # 基础柱状图模型
├── PictorialBarSeries.ts  # 象形柱图模型
├── PictorialBarView.ts    # 象形柱图视图
├── install.ts             # 标准柱图安装
└── installPictorialBar.ts # 象形柱图安装
```

**特性**:
- 支持直角坐标系和极坐标系
- 支持堆叠和分组
- 支持背景显示
- 支持圆角边框
- 支持象形柱图变体

**配置选项**:
```typescript
interface BarSeriesOption {
    coordinateSystem?: 'cartesian2d' | 'polar'
    roundCap?: boolean
    showBackground?: boolean
    backgroundStyle?: ItemStyleOption
    barWidth?: number | string
    barMaxWidth?: number | string
    barMinWidth?: number | string
    barGap?: number | string
    barCategoryGap?: number | string
}
```

### 2. Line 图表 (折线图)

**文件结构**:
```
line/
├── LineSeries.ts          # 折线图数据模型
├── LineView.ts            # 折线图视图
├── helper.ts              # 辅助函数
├── lineAnimationDiff.ts   # 动画差异计算
├── poly.ts                # 多边形处理
└── install.ts             # 安装文件
```

**特性**:
- 支持平滑曲线
- 支持阶梯线
- 支持面积填充
- 支持数据采样
- 支持端点标签
- 支持连接空数据

**配置选项**:
```typescript
interface LineSeriesOption {
    smooth?: boolean | number
    step?: boolean | 'start' | 'middle' | 'end'
    connectNulls?: boolean
    clipOverflow?: boolean
    areaStyle?: AreaStyleOption
    endLabel?: LineEndLabelOption
    sampling?: 'lttb' | 'average' | 'max' | 'min' | 'sum'
}
```

### 3. Pie 图表 (饼图)

**文件结构**:
```
pie/
├── PieSeries.ts           # 饼图数据模型
├── PieView.ts             # 饼图视图
├── labelLayout.ts         # 标签布局算法
├── pieLayout.ts           # 饼图布局算法
└── install.ts             # 安装文件
```

**特性**:
- 支持环形图
- 支持玫瑰图
- 支持标签智能布局
- 支持扇区选择
- 支持动画效果

**配置选项**:
```typescript
interface PieSeriesOption {
    center?: (number | string)[]
    radius?: (number | string)[] | number | string
    startAngle?: number
    endAngle?: number
    clockwise?: boolean
    roseType?: boolean | 'radius' | 'area'
    avoidLabelOverlap?: boolean
    stillShowZeroSum?: boolean
}
```

### 4. Custom 图表 (自定义图表)

**特殊性**: 提供完全自定义的图表绘制能力

**文件结构**:
```
custom/
├── CustomSeries.ts        # 自定义图表模型
├── CustomView.ts          # 自定义图表视图
├── customSeriesRegister.ts # 自定义元素注册
└── install.ts             # 安装文件
```

**渲染项类型**:
- group: 分组容器
- rect: 矩形
- circle: 圆形
- ring: 环形
- sector: 扇形
- arc: 弧形
- polygon: 多边形
- polyline: 折线
- line: 直线
- text: 文本
- image: 图片
- path: 路径

## 性能优化策略

### 1. 大数据处理
- **数据采样**: line 图表支持 LTTB 等采样算法
- **渐进式渲染**: 大数据集分批渲染
- **虚拟化**: 只渲染可见区域的数据点

### 2. 渲染优化
- **脏检查**: 只更新变化的图形元素
- **对象复用**: 复用图形对象减少创建开销
- **批量操作**: 合并多次图形操作

### 3. 内存管理
- **及时清理**: dispose 方法清理资源
- **事件解绑**: 避免内存泄漏
- **缓存策略**: 合理使用缓存

## 扩展机制

### 1. 自定义图表类型
```typescript
// 1. 定义 Series 模型
class CustomChartSeries extends SeriesModel {
    static type = 'customChart'
    static defaultOption = { /* ... */ }
}

// 2. 定义 View 视图
class CustomChartView extends ChartView {
    type = 'customChart'
    render(seriesModel, ecModel, api) {
        // 渲染逻辑
    }
}

// 3. 注册到系统
echarts.registerChart(CustomChartSeries, CustomChartView)
```

### 2. 图表组合
- 多个图表类型可以在同一个坐标系中组合使用
- 支持双轴显示不同类型的图表
- 支持图表联动和数据共享

## 重构建议

### 1. 代码复用
- **抽象基类**: 提取更多通用的基类
- **Mixin 模式**: 使用 Mixin 共享通用功能
- **工具函数**: 统一常用的计算和处理逻辑

### 2. 类型安全
- **严格类型**: 加强 TypeScript 类型约束
- **接口统一**: 统一图表接口定义
- **泛型优化**: 更好的泛型设计

### 3. 性能优化
- **懒加载**: 按需加载图表类型
- **Tree Shaking**: 更好的死代码消除
- **WebGL 支持**: 大数据场景使用 WebGL 渲染

### 4. 开发体验
- **文档完善**: 每个图表类型的详细文档
- **示例丰富**: 提供更多使用示例
- **调试工具**: 图表开发调试工具
