#!/usr/bin/env python3
"""
批量修复 gpui_renderer 中的类型名称
"""

import os
import re

def fix_gpui_renderer_types():
    """修复 gpui_renderer 中的类型名称"""
    file_path = "crates/renderer/gpui_renderer/src/lib.rs"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复所有的 DrawLineStyle -> LineStyle
        content = re.sub(r'DrawLineStyle', 'LineStyle', content)
        
        # 修复所有的 DrawTextStyle -> TextStyle
        content = re.sub(r'DrawTextStyle', 'TextStyle', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 已修复 {file_path}")
            return True
        else:
            print(f"⏭️  无需修复 {file_path}")
            return False
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复 gpui_renderer 类型名称...")
    
    if fix_gpui_renderer_types():
        print("✅ 修复完成！")
    else:
        print("⏭️  无需修复")

if __name__ == "__main__":
    main()
