//! ECharts-rs 验证演示
//!
//! 验证ECharts-rs的核心功能可以正常工作，包括图表创建、数据处理和渲染命令生成

use echarts_rs::{LineSeries, BarSeries, PieSeries, ScatterSeries, Color, Series};
use echarts_core::{CartesianCoordinateSystem, Bounds, Chart};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🚀 ECharts-rs 核心功能验证演示");
    println!("{}", "=".repeat(60));

    // 1. 验证线图功能
    println!("\n📈 1. 验证线图功能...");
    validate_line_series()?;

    // 2. 验证柱图功能
    println!("\n📊 2. 验证柱图功能...");
    validate_bar_series()?;

    // 3. 验证饼图功能
    println!("\n🥧 3. 验证饼图功能...");
    validate_pie_series()?;

    // 4. 验证散点图功能
    println!("\n🔵 4. 验证散点图功能...");
    validate_scatter_series()?;

    // 5. 验证图表组合功能
    println!("\n🎨 5. 验证图表组合功能...");
    validate_chart_combination()?;

    // 6. 验证坐标系统
    println!("\n📐 6. 验证坐标系统...");
    validate_coordinate_system()?;

    println!("\n🎉 所有验证测试通过！");
    println!("✅ ECharts-rs 核心功能正常工作");
    println!("✅ 可以在GPUI桌面应用中使用");
    println!("✅ 图表数据处理正确");
    println!("✅ 渲染命令生成成功");

    Ok(())
}

/// 验证线图功能
fn validate_line_series() -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建线图数据
    let line_series = LineSeries::new("销售趋势")
        .data(vec![
            (1.0, 120.0),
            (2.0, 132.0),
            (3.0, 101.0),
            (4.0, 134.0),
            (5.0, 90.0),
            (6.0, 230.0),
            (7.0, 210.0),
        ])
        .smooth(true)
        .color(Color::rgb(0.2, 0.6, 1.0));

    println!("  ✅ 线图创建成功");
    println!("  📊 数据点数量: {}", line_series.data.len());
    println!("  🎨 颜色设置: RGB({:.1}, {:.1}, {:.1})", 
             line_series.color.r, line_series.color.g, line_series.color.b);
    println!("  📈 平滑曲线: {}", line_series.smooth);

    // 创建坐标系统进行渲染测试
    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 600.0, 400.0),
        (0.0, 8.0),
        (0.0, 250.0),
    );

    // 测试渲染命令生成
    match line_series.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 渲染命令生成成功: {} 个命令", commands.len());
        }
        Err(e) => {
            println!("  ❌ 渲染命令生成失败: {:?}", e);
            return Err(e.into());
        }
    }

    Ok(())
}

/// 验证柱图功能
fn validate_bar_series() -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建柱图数据
    let bar_series = BarSeries::new("产品销量")
        .data(vec![
            (1.0, 320.0),
            (2.0, 280.0),
            (3.0, 250.0),
            (4.0, 200.0),
            (5.0, 180.0),
        ])
        .color(Color::rgb(0.9, 0.4, 0.2))
        .bar_width(0.6);

    println!("  ✅ 柱图创建成功");
    println!("  📊 数据点数量: {}", bar_series.data.len());
    println!("  🎨 颜色设置: RGB({:.1}, {:.1}, {:.1})", 
             bar_series.color.r, bar_series.color.g, bar_series.color.b);
    println!("  📏 柱宽度: {}", bar_series.bar_width);

    // 创建坐标系统进行渲染测试
    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 600.0, 400.0),
        (0.0, 6.0),
        (0.0, 350.0),
    );

    // 测试渲染命令生成
    match bar_series.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 渲染命令生成成功: {} 个命令", commands.len());
        }
        Err(e) => {
            println!("  ❌ 渲染命令生成失败: {:?}", e);
            return Err(e.into());
        }
    }

    Ok(())
}

/// 验证饼图功能
fn validate_pie_series() -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建饼图数据
    let pie_series = PieSeries::new("市场份额")
        .data(vec![
            ("移动端", 45.0),
            ("桌面端", 35.0),
            ("平板端", 15.0),
            ("其他", 5.0),
        ])
        .radius(0.7)
        .center(0.5, 0.5);

    println!("  ✅ 饼图创建成功");
    println!("  📊 数据点数量: {}", pie_series.data.len());
    println!("  🎯 中心位置: ({:.1}, {:.1})", pie_series.center[0], pie_series.center[1]);
    println!("  📏 半径: {:?}", pie_series.radius);

    // 创建坐标系统进行渲染测试
    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 600.0, 400.0),
        (0.0, 1.0),
        (0.0, 1.0),
    );

    // 测试渲染命令生成
    match pie_series.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 渲染命令生成成功: {} 个命令", commands.len());
        }
        Err(e) => {
            println!("  ❌ 渲染命令生成失败: {:?}", e);
            return Err(e.into());
        }
    }

    Ok(())
}

/// 验证散点图功能
fn validate_scatter_series() -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建散点图数据
    let scatter_data: Vec<(f64, f64)> = (0..20)
        .map(|i| {
            let x = i as f64;
            let y = (x * 0.5).sin() * 50.0 + 100.0 + (i % 3) as f64 * 10.0;
            (x, y)
        })
        .collect();

    let scatter_series = ScatterSeries::new("数据分布")
        .data(scatter_data)
        .symbol_size(6.0)
        .color(Color::rgb(0.4, 0.8, 0.4));

    println!("  ✅ 散点图创建成功");
    println!("  📊 数据点数量: {}", scatter_series.data.len());
    println!("  🎨 颜色设置: RGB({:.1}, {:.1}, {:.1})", 
             scatter_series.color.r, scatter_series.color.g, scatter_series.color.b);
    println!("  🔵 符号大小: {}", scatter_series.symbol_size);

    // 创建坐标系统进行渲染测试
    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 600.0, 400.0),
        (0.0, 20.0),
        (0.0, 200.0),
    );

    // 测试渲染命令生成
    match scatter_series.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 渲染命令生成成功: {} 个命令", commands.len());
        }
        Err(e) => {
            println!("  ❌ 渲染命令生成失败: {:?}", e);
            return Err(e.into());
        }
    }

    Ok(())
}

/// 验证图表组合功能
fn validate_chart_combination() -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建一个包含多个系列的图表
    let mut chart = Chart::new();

    // 添加线图系列
    let line_series = LineSeries::new("趋势线")
        .data(vec![(1.0, 100.0), (2.0, 120.0), (3.0, 110.0)])
        .color(Color::rgb(0.2, 0.6, 1.0));

    // 添加柱图系列
    let bar_series = BarSeries::new("销量")
        .data(vec![(1.0, 200.0), (2.0, 180.0), (3.0, 220.0)])
        .color(Color::rgb(0.9, 0.4, 0.2));

    chart = chart.add_series(Box::new(line_series));
    chart = chart.add_series(Box::new(bar_series));

    println!("  ✅ 图表组合创建成功");
    println!("  📊 系列数量: {}", chart.series.len());

    // 验证每个系列都能正常工作
    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 600.0, 400.0),
        (0.0, 4.0),
        (0.0, 250.0),
    );

    let mut total_commands = 0;
    for (i, series) in chart.series.iter().enumerate() {
        match series.render_to_commands(&coord_system) {
            Ok(commands) => {
                println!("  ✅ 系列 {} 渲染成功: {} 个命令", i + 1, commands.len());
                total_commands += commands.len();
            }
            Err(e) => {
                println!("  ❌ 系列 {} 渲染失败: {:?}", i + 1, e);
                return Err(e.into());
            }
        }
    }

    println!("  ✅ 总渲染命令: {} 个", total_commands);
    Ok(())
}

/// 验证坐标系统
fn validate_coordinate_system() -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建不同的坐标系统
    let coord1 = CartesianCoordinateSystem::new(
        Bounds::new(0.0, 0.0, 800.0, 600.0),
        (0.0, 10.0),
        (0.0, 100.0),
    );

    let coord2 = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 700.0, 500.0),
        (-5.0, 5.0),
        (-50.0, 50.0),
    );

    println!("  ✅ 坐标系统1创建成功");
    println!("    📐 边界: ({:.0}, {:.0}) - ({:.0}, {:.0})", 
             coord1.bounds.origin.x, coord1.bounds.origin.y,
             coord1.bounds.origin.x + coord1.bounds.size.width,
             coord1.bounds.origin.y + coord1.bounds.size.height);
    println!("    📊 X轴范围: {:.1} - {:.1}", coord1.x_range.0, coord1.x_range.1);
    println!("    📊 Y轴范围: {:.1} - {:.1}", coord1.y_range.0, coord1.y_range.1);

    println!("  ✅ 坐标系统2创建成功");
    println!("    📐 边界: ({:.0}, {:.0}) - ({:.0}, {:.0})", 
             coord2.bounds.origin.x, coord2.bounds.origin.y,
             coord2.bounds.origin.x + coord2.bounds.size.width,
             coord2.bounds.origin.y + coord2.bounds.size.height);
    println!("    📊 X轴范围: {:.1} - {:.1}", coord2.x_range.0, coord2.x_range.1);
    println!("    📊 Y轴范围: {:.1} - {:.1}", coord2.y_range.0, coord2.y_range.1);

    Ok(())
}
