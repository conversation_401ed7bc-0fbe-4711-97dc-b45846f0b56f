# ECharts-RS 库文件路径重新整理

## 概述

本文档记录了 echarts-rs 库文件路径的重新整理过程，旨在创建一个更加模块化、可维护和可扩展的项目结构。

## 重新整理目标

1. **模块化设计** - 将功能按职责分离到独立的 crate 中
2. **清晰的架构** - 建立明确的依赖层次和模块边界
3. **便于维护** - 提供清晰的代码组织和文档结构
4. **支持扩展** - 为未来功能扩展预留空间
5. **改善开发体验** - 提供更好的 API 设计和示例代码

## 新的文件结构

### 整体架构

```
crates/echarts-rs/
├── Cargo.toml                    # 主库配置文件
├── README.md                     # 项目说明文档
├── src/
│   └── lib.rs                   # 库的公共 API 入口
├── examples/                    # 使用示例
│   └── gpui_line_chart.rs      # GPUI 线图示例
├── benches/                     # 性能基准测试
├── tests/                       # 集成测试
├── docs/                        # 项目文档
│   ├── ARCHITECTURE.md         # 架构设计文档
│   └── RESTRUCTURE.md          # 本文档
└── crates/                      # 子模块 crates
    ├── core/                    # 核心功能模块
    ├── charts/                  # 图表类型实现
    ├── components/              # UI 组件
    ├── themes/                  # 主题系统
    └── renderer/                # 渲染后端
```

### 核心模块 (core)

**路径**: `crates/echarts-rs/crates/core/`

**职责**: 提供基础数据结构、几何类型、颜色系统和核心 traits

```
core/
├── Cargo.toml
└── src/
    ├── lib.rs          # 模块入口
    ├── chart.rs        # 图表配置结构
    ├── geometry.rs     # 几何类型 (Point, Bounds, Transform 等)
    ├── color.rs        # 颜色系统和调色板
    ├── style.rs        # 样式定义 (TextStyle, LineStyle 等)
    ├── data.rs         # 数据结构和类型
    ├── coord.rs        # 坐标系统
    ├── scale.rs        # 刻度系统
    ├── animation.rs    # 动画系统
    ├── event.rs        # 事件处理
    ├── error.rs        # 错误类型定义
    └── utils.rs        # 工具函数
```

**主要类型**:
- `Chart` - 主图表配置
- `Point`, `Bounds`, `Transform` - 几何类型
- `Color`, `ColorPalette` - 颜色系统
- `TextStyle`, `LineStyle`, `FillStyle` - 样式系统

### 图表模块 (charts)

**路径**: `crates/echarts-rs/crates/charts/`

**职责**: 实现各种图表类型 (柱状图、折线图、饼图等)

```
charts/
├── Cargo.toml
└── src/
    ├── lib.rs          # 图表模块入口
    ├── bar.rs          # 柱状图实现
    ├── line.rs         # 折线图实现
    ├── pie.rs          # 饼图实现 (待实现)
    ├── scatter.rs      # 散点图实现 (待实现)
    └── area.rs         # 面积图实现 (待实现)
```

**主要类型**:
- `BarSeries` - 柱状图系列
- `LineSeries` - 折线图系列
- `PieSeries` - 饼图系列
- `ScatterSeries` - 散点图系列

### 组件模块 (components)

**路径**: `crates/echarts-rs/crates/components/`

**职责**: 提供 UI 组件 (图例、提示框、标题等)

```
components/
├── Cargo.toml
└── src/
    ├── lib.rs          # 组件模块入口
    ├── legend.rs       # 图例组件
    ├── tooltip.rs      # 提示框组件
    ├── title.rs        # 标题组件
    ├── grid.rs         # 网格组件
    └── axis.rs         # 坐标轴组件
```

**主要类型**:
- `Legend` - 图例组件
- `Tooltip` - 提示框组件
- `Title` - 标题组件
- `Grid` - 网格布局
- `Axis` - 坐标轴

### 主题模块 (themes)

**路径**: `crates/echarts-rs/crates/themes/`

**职责**: 提供主题系统和内置主题

```
themes/
├── Cargo.toml
└── src/
    ├── lib.rs          # 主题模块入口
    ├── builtin.rs      # 内置主题定义
    └── manager.rs      # 主题管理器
```

**主要功能**:
- 内置主题: `light`, `dark`, `vintage`, `macarons`, `infographic` 等
- 主题管理: 注册、切换、自定义主题
- 颜色调色板管理

### 渲染模块 (renderer)

**路径**: `crates/echarts-rs/crates/renderer/`

**职责**: 提供渲染后端和画布抽象

```
renderer/
├── Cargo.toml
└── src/
    ├── lib.rs          # 渲染模块入口
    ├── canvas.rs       # 画布抽象
    ├── context.rs      # 渲染上下文
    ├── gpui_renderer.rs # GPUI 渲染器 (可选)
    └── svg_renderer.rs  # SVG 渲染器 (可选)
```

**主要功能**:
- 多种渲染后端支持
- 统一的画布 API
- 渲染上下文管理
- 导出功能 (PNG, SVG, PDF 等)

## 依赖关系

### 模块依赖层次

```
┌─────────────┐
│   主库      │ ← 用户接口层
│ echarts-rs  │
└─────────────┘
       │
       ├── components ← UI 组件层
       ├── charts     ← 图表实现层
       ├── themes     ← 主题系统层
       ├── renderer   ← 渲染后端层
       └── core       ← 基础设施层
```

### 依赖规则

1. **core** - 不依赖其他内部模块，提供基础类型和 traits
2. **renderer** - 仅依赖 core，提供渲染抽象
3. **themes** - 仅依赖 core，提供主题管理
4. **components** - 依赖 core，提供 UI 组件
5. **charts** - 依赖 core，提供图表实现
6. **主库** - 依赖所有子模块，提供统一 API

## 重新整理过程

### 第一阶段：创建目录结构

1. 创建子 crate 目录
   ```bash
   mkdir -p crates/echarts-rs/crates/{core,charts,components,themes,renderer}
   mkdir -p crates/echarts-rs/{examples,benches,tests}
   ```

2. 为每个子 crate 创建源码目录
   ```bash
   mkdir -p crates/echarts-rs/crates/{core,charts,components,themes,renderer}/src
   ```

### 第二阶段：移动和重组文件

1. **移动示例代码**
   - `src/gpui_line_chart.rs` → `examples/gpui_line_chart.rs`

2. **创建子 crate 配置**
   - 为每个子 crate 创建 `Cargo.toml`
   - 配置正确的依赖关系

3. **实现基础模块**
   - 创建核心类型定义
   - 实现基础组件结构
   - 添加主题系统

### 第三阶段：更新配置和 API

1. **更新主 Cargo.toml**
   - 修正依赖关系
   - 移除无效的 benchmark 配置
   - 添加 feature flags

2. **优化 API 导出**
   - 重新设计 prelude 模块
   - 统一公共 API 接口
   - 添加便利的重导出

## 配置变更

### 主 Cargo.toml 变更

```toml
# 示例程序配置
[[example]]
name = "gpui_line_chart"
path = "examples/gpui_line_chart.rs"

# 特性配置
[features]
default = ["std"]
std = []
gpu-acceleration = ["rust-echarts-renderer/gpu"]
web-export = ["rust-echarts-renderer/web"]
svg-export = ["rust-echarts-renderer/svg"]
pdf-export = ["rust-echarts-renderer/pdf"]
examples = ["eframe", "egui_plot", "resvg", "tiny-skia"]
```

### 依赖管理

- 使用 workspace 依赖统一版本管理
- 可选依赖支持不同渲染后端
- Feature flags 控制功能启用

## API 设计

### Prelude 模块

```rust
pub mod prelude {
    // 核心类型
    pub use echarts_core::{
        Chart, ChartOption, Color, Bounds, Point,
        ChartError, Result, Renderable, Themeable,
    };

    // 图表类型
    pub use echarts_charts::{
        BarSeries, LineSeries, PieSeries, ScatterSeries,
    };

    // 组件
    pub use echarts_components::{
        Legend, Tooltip, Title, Grid,
    };

    // 主题
    pub use echarts_themes::{
        Theme, ThemeManager,
    };

    // 渲染
    pub use echarts_renderer::{
        Renderer, Canvas, OutputFormat,
    };
}
```

### 使用示例

```rust
use echarts_rs::prelude::*;

let chart = Chart::new()
    .title("销售数据".to_string())
    .background_color(Color::WHITE);

// 使用便利的构造函数
let data = vec![("一月", 120.0), ("二月", 200.0), ("三月", 150.0)];
let bar_chart = BarSeries::new("销售额").data(data);
```

## 优势和改进

### 模块化优势

1. **独立开发** - 每个模块可以独立开发和测试
2. **清晰职责** - 每个模块有明确的功能边界
3. **便于维护** - 问题定位和修复更加容易
4. **支持扩展** - 新功能可以作为独立模块添加

### 用户体验改进

1. **简化导入** - 通过 prelude 模块简化常用类型导入
2. **清晰文档** - 每个模块有独立的文档和示例
3. **灵活配置** - 通过 feature flags 控制功能启用
4. **示例丰富** - 提供完整的使用示例

### 开发体验改进

1. **编译速度** - 模块化编译，只编译需要的部分
2. **测试隔离** - 每个模块可以独立测试
3. **代码复用** - 核心功能可以被多个模块复用
4. **版本管理** - 可以独立发布和版本控制

## 后续计划

### 短期目标

1. **修复编译错误** - 解决当前的类型不匹配问题
2. **完善图表类型** - 实现缺失的图表类型 (pie, scatter, area)
3. **添加测试** - 为每个模块添加单元测试和集成测试
4. **完善文档** - 添加 API 文档和使用指南

### 中期目标

1. **性能优化** - 添加性能基准测试和优化
2. **渲染后端** - 实现多种渲染后端支持
3. **主题扩展** - 添加更多内置主题和自定义主题支持
4. **交互功能** - 实现鼠标交互和动画效果

### 长期目标

1. **生态系统** - 建立插件系统和第三方扩展支持
2. **跨平台** - 支持 Web、移动端等多平台渲染
3. **数据绑定** - 实现响应式数据绑定和实时更新
4. **可视化编辑器** - 提供图形化的图表编辑工具

## 总结

通过这次文件路径重新整理，echarts-rs 库获得了：

- ✅ **清晰的模块化架构**
- ✅ **明确的依赖关系**
- ✅ **便于维护的代码结构**
- ✅ **支持未来扩展的设计**
- ✅ **改善的开发和用户体验**

这为 echarts-rs 库的长期发展奠定了坚实的基础，使其能够成为一个高质量、易用且可扩展的 Rust 图表库。
