[package]
name = "canas-renderer"
version = "0.1.0"
edition = "2021"
description = "Rendering backends for Rust ECharts"
license = "Apache-2.0"

[dependencies]
rust-echarts-core = { path = "../core" }
rust-echarts-themes = { path = "../themes" }
echarts-processor = { path = "../processor" }
serde = { workspace = true }
serde_json = { workspace = true }
thiserror = { workspace = true }
anyhow = { workspace = true }

# Optional rendering backends
gpui = { workspace = true, optional = true }
lyon = { version = "1.0", optional = true }
resvg = { version = "0.37", optional = true }
tiny-skia = { version = "0.11", optional = true }

[features]
default = ["std", "gpui"]
std = []
gpui = ["gpui-backend"]
gpui-backend = ["dep:gpui", "dep:lyon"]
svg = ["dep:resvg", "dep:tiny-skia"]
gpu = ["gpui"]
web = []
pdf = []
