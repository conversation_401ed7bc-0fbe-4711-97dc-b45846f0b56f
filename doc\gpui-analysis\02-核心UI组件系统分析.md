# GPUI Component 核心 UI 组件系统分析

## 组件系统概述

GPUI Component 提供了 40+ 个跨平台桌面 UI 组件，采用无状态 `RenderOnce` 设计模式，具有高性能和易用性。

## 组件架构模式

### 1. 基础组件接口

```rust
// 核心渲染接口
pub trait RenderOnce {
    fn render(self, cx: &mut WindowContext) -> impl IntoElement;
}

// 元素转换接口
pub trait IntoElement {
    type Element: Element;
    fn into_element(self) -> Self::Element;
}

// 样式化接口
pub trait Styled: Sized {
    fn style(&mut self) -> &mut StyleRefinement;
}
```

### 2. 组件特征系统

```rust
// 尺寸特征
pub trait Sizable {
    fn with_size(self, size: impl Into<Size>) -> Self;
    fn xs(self) -> Self { self.with_size(Size::XSmall) }
    fn sm(self) -> Self { self.with_size(Size::Small) }
    fn md(self) -> Self { self.with_size(Size::Medium) }
    fn lg(self) -> Self { self.with_size(Size::Large) }
}

// 禁用特征
pub trait Disableable {
    fn disabled(self, disabled: bool) -> Self;
}

// 可选择特征
pub trait Selectable {
    fn selected(self, selected: bool) -> Self;
}
```

## 核心组件详细分析

### 1. Button 组件系统

**文件结构**:
```
button/
├── button.rs           # 核心按钮实现
├── button_group.rs     # 按钮组
├── dropdown_button.rs  # 下拉按钮
├── toggle.rs           # 切换按钮
└── mod.rs             # 模块导出
```

**按钮变体**:
```rust
pub enum ButtonVariant {
    Primary,    // 主要按钮
    Danger,     // 危险按钮
    Warning,    // 警告按钮
    Success,    // 成功按钮
    Info,       // 信息按钮
    Ghost,      // 幽灵按钮
    Link,       // 链接按钮
    Text,       // 文本按钮
    Custom(ButtonCustomVariant), // 自定义样式
}
```

**核心特性**:
- 支持多种视觉变体
- 可配置圆角大小
- 支持图标和文本组合
- 内置加载状态
- 支持键盘导航

**使用示例**:
```rust
Button::new("Click me")
    .primary()
    .lg()
    .on_click(|_event, cx| {
        // 点击处理逻辑
    })
```

### 2. Input 组件系统

**文件结构**:
```
input/
├── text_input.rs       # 文本输入框
├── number_input.rs     # 数字输入框
├── otp_input.rs        # OTP 验证码输入
├── state.rs            # 输入状态管理
├── cursor.rs           # 光标处理
├── mask_pattern.rs     # 输入掩码
└── ...
```

**输入状态管理**:
```rust
pub struct InputState {
    text: String,
    selection: Range<usize>,
    cursor_position: usize,
    is_focused: bool,
    // ...
}
```

**核心特性**:
- 支持前缀/后缀元素
- 内置清除按钮
- 支持输入掩码
- 密码显示/隐藏切换
- 多行文本支持
- 实时验证

### 3. Table 组件系统

**核心特性**:
- **虚拟化渲染**: 支持大数据集（百万级行）
- **列操作**: 排序、拖拽调整、固定列
- **行选择**: 单选、多选、全选
- **上下文菜单**: 右键菜单支持
- **键盘导航**: 完整的键盘操作支持

**表格结构**:
```rust
pub struct Table<T> {
    data: Vec<T>,
    columns: Vec<TableColumn<T>>,
    selection: TableSelection,
    scroll_handle: UniformListScrollHandle,
    // ...
}

pub struct TableColumn<T> {
    name: SharedString,
    width: Pixels,
    sort: Option<ColSort>,
    fixed: Option<ColFixed>,
    render: Rc<dyn Fn(&T, usize) -> AnyElement>,
}
```

**性能优化**:
- 虚拟滚动只渲染可见行
- 列宽度缓存和优化
- 增量更新机制

### 4. Chart 组件系统

**支持的图表类型**:
```rust
// 折线图
pub struct LineChart<T, X, Y> {
    data: Vec<T>,
    x: Option<Rc<dyn Fn(&T) -> X>>,
    y: Option<Rc<dyn Fn(&T) -> Y>>,
    stroke: Option<Hsla>,
    // ...
}

// 柱状图
pub struct BarChart<T, X, Y> { /* ... */ }

// 饼图
pub struct PieChart<T> { /* ... */ }

// 面积图
pub struct AreaChart<T, X, Y> { /* ... */ }
```

**图表特性**:
- 基于泛型的类型安全数据绑定
- 支持自定义颜色和样式
- 内置坐标轴和网格
- 响应式设计
- 动画支持

### 5. Layout 组件系统

#### Dock 布局系统
```rust
pub struct Dock {
    panels: Vec<Panel>,
    layout: DockLayout,
    // ...
}

pub enum PanelKind {
    Tab(TabPanel),
    Stack(StackPanel),
    Tiles(TilesPanel),
}
```

**特性**:
- 可拖拽的面板
- 多种布局模式（标签、堆叠、平铺）
- 面板分割和合并
- 状态持久化

#### Resizable 组件
```rust
pub struct ResizablePanel {
    direction: Axis,
    min_size: Pixels,
    max_size: Option<Pixels>,
    // ...
}
```

### 6. 虚拟化组件

#### VirtualList
```rust
pub struct VirtualList<T> {
    items: Vec<T>,
    item_height: Pixels,
    render_item: Rc<dyn Fn(&T, usize) -> AnyElement>,
    scroll_handle: UniformListScrollHandle,
}
```

**性能特性**:
- 只渲染可见项目
- 支持动态高度
- 平滑滚动
- 内存使用优化

### 7. 交互组件

#### Modal 模态框
```rust
pub struct Modal {
    title: Option<SharedString>,
    closable: bool,
    mask_closable: bool,
    // ...
}
```

#### Tooltip 提示框
```rust
pub struct Tooltip {
    content: AnyElement,
    placement: TooltipPlacement,
    delay: Duration,
    // ...
}
```

## 状态管理

### 1. Entity 系统

```rust
// 状态实体
pub struct Entity<T> {
    id: EntityId,
    // ...
}

// 使用示例
let input_state = cx.new_entity(InputState::default());
TextInput::new(&input_state)
```

### 2. 事件系统

```rust
// 事件发射器
pub trait EventEmitter<E> {
    fn emit(&mut self, event: E, cx: &mut Context<Self>);
}

// 事件监听
component.on_event(|event, cx| {
    // 处理事件
})
```

## 样式系统

### 1. 主题集成

```rust
impl RenderOnce for Button {
    fn render(self, cx: &mut WindowContext) -> impl IntoElement {
        div()
            .bg(cx.theme().primary)
            .text_color(cx.theme().primary_foreground)
            // ...
    }
}
```

### 2. 响应式样式

```rust
// 尺寸响应
pub trait StyleSized {
    fn apply_size(&mut self, size: Size);
}

// 状态样式
pub trait StyledExt: Styled {
    fn when(self, condition: bool, f: impl FnOnce(Self) -> Self) -> Self;
    fn hover(self, f: impl FnOnce(Self) -> Self) -> Self;
    fn active(self, f: impl FnOnce(Self) -> Self) -> Self;
}
```

## 可访问性支持

### 1. 键盘导航

```rust
// 焦点管理
pub trait Focusable {
    fn focus_handle(&self, cx: &App) -> FocusHandle;
}

// 键盘绑定
cx.bind_keys([
    KeyBinding::new("enter", Confirm, context),
    KeyBinding::new("escape", Cancel, context),
]);
```

### 2. 屏幕阅读器支持

```rust
// ARIA 属性
div()
    .role("button")
    .aria_label("Close dialog")
    .aria_pressed(selected)
```

## 性能优化策略

### 1. 渲染优化
- **增量渲染**: 只重绘变化的部分
- **批量更新**: 合并多次状态变更
- **GPU 加速**: 利用 GPUI 的 GPU 渲染能力

### 2. 内存优化
- **对象池**: 复用组件实例
- **懒加载**: 按需创建组件
- **弱引用**: 避免循环引用

### 3. 虚拟化
- **视口裁剪**: 只渲染可见区域
- **动态加载**: 滚动时动态加载内容
- **缓存策略**: 智能缓存渲染结果

## 组件扩展机制

### 1. 自定义组件

```rust
pub struct CustomComponent {
    // 组件属性
}

impl RenderOnce for CustomComponent {
    fn render(self, cx: &mut WindowContext) -> impl IntoElement {
        div()
            .child("Custom content")
            // 自定义渲染逻辑
    }
}
```

### 2. 组件组合

```rust
pub fn complex_form() -> impl IntoElement {
    v_flex()
        .gap_4()
        .child(TextInput::new(&name_state).placeholder("Name"))
        .child(TextInput::new(&email_state).placeholder("Email"))
        .child(
            h_flex()
                .gap_2()
                .child(Button::new("Cancel").ghost())
                .child(Button::new("Submit").primary())
        )
}
```

## 与 ECharts 集成的潜力

### 1. 图表组件替换
- GPUI Component 的图表组件可以作为 ECharts 的原生替代
- 更好的性能和内存使用
- 与桌面应用的深度集成

### 2. 容器组件
- 可以将 ECharts 嵌入到 GPUI 的容器组件中
- 利用 GPUI 的布局系统
- 统一的主题和样式管理

### 3. 交互增强
- 利用 GPUI 的事件系统增强图表交互
- 更丰富的上下文菜单和工具栏
- 键盘导航支持

这个组件系统为构建现代桌面应用提供了强大的基础，特别适合与 ECharts 这样的数据可视化库集成。
