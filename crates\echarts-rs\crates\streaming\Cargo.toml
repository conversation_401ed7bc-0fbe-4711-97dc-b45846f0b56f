[package]
name = "rust-echarts-streaming"
version = "0.1.0"
edition = "2021"
description = "Real-time data streaming for ECharts-rs"
license = "MIT"
repository = "https://github.com/your-repo/echarts-rs"

[dependencies]
rust-echarts-core = { path = "../core" }
rust-echarts-charts = { path = "../charts" }
rust-echarts-data = { path = "../data" }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Async runtime
tokio = { version = "1.0", features = ["full"] }
futures = "0.3"
async-trait = "0.1"

# Streaming
tokio-stream = "0.1"
futures-util = "0.3"

# WebSocket support
tokio-tungstenite = { version = "0.20", optional = true }
tungstenite = { version = "0.20", optional = true }

# Message queues
rdkafka = { version = "0.34", optional = true }
lapin = { version = "2.3", optional = true }

# Time series
chrono = { version = "0.4", features = ["serde"] }
uuid = "1.0"

# Metrics and monitoring
metrics = "0.21"
tracing = "0.1"

# Buffering and windowing
ringbuf = "0.3"
crossbeam-channel = "0.5"

[features]
default = ["websocket"]
websocket = ["tokio-tungstenite", "tungstenite"]
kafka = ["rdkafka"]
rabbitmq = ["lapin"]
full = ["websocket", "kafka", "rabbitmq"]

[dev-dependencies]
tokio-test = "0.4"
criterion = "0.5"
