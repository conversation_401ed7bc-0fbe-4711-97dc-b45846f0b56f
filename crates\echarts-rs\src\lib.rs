//! # Rust ECharts - 简化版本
//!
//! 专注于 Charts 集成测试的简化版本
//!
//! ## 特性
//!
//! - **类型安全**: 完整的 Rust 类型安全和编译时保证
//! - **丰富的图表类型**: 折线图、柱状图、散点图等
//! - **统一架构**: 基于 DrawCommand 的统一渲染架构
//! - **类型擦除**: 支持 `Box<dyn Series>` 统一处理

// 内部模块
pub mod chart_builder;

// Re-export renderer and interaction
pub use echarts_renderer as renderer;
pub use echarts_interaction as interaction;

// Re-export core types (只导出存在的类型)
pub use echarts_core::{
    Bounds,
    Chart,
    ChartError,
    Color,
    DataPoint,
    DataSet,
    DataValue,
    DrawCommand,
    Point,
    Result,
    Series,
    SeriesType,
    CartesianCoordinateSystem,
    CoordinateSystem,
    RenderContext,
    StyleState,
    Transform,
    PerformanceHint,
    // SimpleTheme, // 暂时注释掉，因为不存在
};

// Re-export chart types from charts crate (只导出存在的类型)
pub use echarts_charts::{
    // 主要图表类型 - 完整功能实现
    LineSeries,      // 支持平滑曲线、符号、面积图、阶梯图
    BarSeries,       // 支持边框、背景、柱子宽度配置
    ScatterSeries,   // 支持多种符号类型和大小
    PieSeries,       // 支持饼图、环形图、玫瑰图
    RadarSeries,     // 支持雷达图、多维数据、能力评估
    GaugeSeries,     // 支持仪表盘、指针、刻度、范围显示
    TreemapSeries,   // 支持矩形树图、层次数据、面积映射
    SunburstSeries,    // 支持旭日图、层次数据、扇形映射
    FunnelSeries,      // 支持漏斗图、转化率分析、流程展示
    CandlestickSeries, // 支持蜡烛图、K线图、金融数据分析
    HeatmapSeries,     // 支持热力图、矩阵数据、颜色映射
    Surface3DSeries,   // 支持3D曲面图、等距投影、光照

    // 符号和样式类型
    SymbolType,

    // 折线图特有类型
    StepType,
    LineOptimizationAlgorithm,

    // 饼图特有类型
    PieRadius,
    RoseType,
    PieLabelPosition,
    SelectedMode,

    // 雷达图特有类型
    RadarDataItem,
    RadarIndicator,

    // 仪表盘特有类型
    GaugeDataItem,
    PointerStyle,
    AxisTick,
    AxisLabel,

    // 矩形树图特有类型
    TreemapDataItem,
    TreemapAlgorithm,
    TreemapLabel,
    TreemapLabelPosition,

    // 旭日图特有类型
    SunburstDataItem,
    SunburstLabel,
    SunburstLabelPosition,

    // 漏斗图特有类型
    FunnelDataItem,
    FunnelLabel,
    FunnelLabelPosition,
    FunnelSort,
    FunnelAlign,

    // 蜡烛图特有类型
    CandlestickDataItem,
    CandlestickStyle,
    CandlestickLabel,
    CandlestickLabelPosition,
    CandlestickLabelContent,

    // 热力图特有类型
    ColorMap,
    ColorMapType,
    HeatmapLabel,

    // 3D曲面图特有类型
    Point3D,
    SimpleLighting,
};

// Re-export themes
pub use echarts_themes::{Theme, ThemeManager};

// Re-export chart builder
pub use chart_builder::{ChartBuilder, line_chart, bar_chart, scatter_chart, multi_line_chart, multi_bar_chart, mixed_chart};

/// Prelude module for convenient imports
pub mod prelude {
    // Re-export core types
    pub use echarts_core::{
        Bounds,
        Chart,
        ChartError,
        Color,
        DataPoint,
        DataSet,
        DataValue,
        DrawCommand,
        Point,
        Result,
        Series,
        SeriesType,
        CartesianCoordinateSystem,
        CoordinateSystem,
        RenderContext,
        StyleState,
        Transform,
        PerformanceHint,
        // SimpleTheme, // 暂时注释掉，因为不存在
    };

    // Re-export chart types (完整的 charts crate 实现)
    pub use echarts_charts::{
        // 主要图表类型 - 完整功能实现
        LineSeries,      // 支持平滑曲线、符号、面积图、阶梯图、动画
        BarSeries,       // 支持堆叠、边框、背景、分类数据处理
        ScatterSeries,   // 支持气泡图、符号映射、大数据集优化
        PieSeries,       // 支持饼图、环形图、玫瑰图
        RadarSeries,     // 支持雷达图、多维数据、能力评估
        GaugeSeries,     // 支持仪表盘、指针、刻度、范围显示
        TreemapSeries,   // 支持矩形树图、层次数据、面积映射
        SunburstSeries,    // 支持旭日图、层次数据、扇形映射
        FunnelSeries,      // 支持漏斗图、转化率分析、流程展示
        CandlestickSeries, // 支持蜡烛图、K线图、金融数据分析
        HeatmapSeries,     // 支持热力图、矩阵数据、颜色映射
        Surface3DSeries,   // 支持3D曲面图、等距投影、光照

        // 符号和样式类型
        SymbolType,

        // 折线图特有类型
        StepType,
        LineOptimizationAlgorithm,

        // 饼图特有类型
        PieRadius,
        RoseType,
        PieLabelPosition,
        SelectedMode,

        // 雷达图特有类型
        RadarDataItem,
        RadarIndicator,

        // 仪表盘特有类型
        GaugeDataItem,
        PointerStyle,
        AxisTick,
        AxisLabel,

        // 矩形树图特有类型
        TreemapDataItem,
        TreemapAlgorithm,
        TreemapLabel,
        TreemapLabelPosition,

        // 旭日图特有类型
        SunburstDataItem,
        SunburstLabel,
        SunburstLabelPosition,

        // 漏斗图特有类型
        FunnelDataItem,
        FunnelLabel,
        FunnelLabelPosition,
        FunnelSort,
        FunnelAlign,

        // 蜡烛图特有类型
        CandlestickDataItem,
        CandlestickStyle,
        CandlestickLabel,
        CandlestickLabelPosition,
        CandlestickLabelContent,

        // 热力图特有类型
        ColorMap,
        ColorMapType,
        HeatmapLabel,

        // 3D曲面图特有类型
        Point3D,
        SimpleLighting,
    };

    // Re-export themes
    pub use echarts_themes::{Theme, ThemeManager};
}

/// Built-in themes
pub mod themes {
    pub use echarts_themes::*;
}

/// Chart creation utilities
pub mod utils {
    pub use echarts_core::utils::*;
}

/// Runtime chart that holds actual series instances
///
/// 这个结构实现了 EXECUTION_PLAN.md 中的方案2：集成真实的 charts 实现
/// 它持有来自 charts crate 的完整 Series 实现
#[derive(Debug)]
pub struct RuntimeChart {
    /// Chart configuration from core crate
    pub config: Chart,
    
    /// Series instances (using type erasure)
    pub series: Vec<Box<dyn Series>>,
    
    /// Chart bounds
    pub bounds: Bounds,
}

impl RuntimeChart {
    /// Create a new runtime chart
    pub fn new() -> Self {
        Self {
            config: Chart::new(),
            series: Vec::new(),
            bounds: Bounds::new(0.0, 0.0, 800.0, 600.0),
        }
    }
    
    /// Add a series to the chart (using type erasure)
    pub fn add_series<S: Series + 'static>(mut self, series: S) -> Self {
        self.series.push(Box::new(series));
        self
    }
    
    /// Set chart bounds
    pub fn bounds(mut self, bounds: Bounds) -> Self {
        self.bounds = bounds;
        self
    }
    
    /// Get all series
    pub fn get_series(&self) -> &[Box<dyn Series>] {
        &self.series
    }
    
    /// Render all series to draw commands
    pub fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut all_commands = Vec::new();
        
        for series in &self.series {
            let commands = series.render_to_commands(coord_system)?;
            all_commands.extend(commands);
        }
        
        Ok(all_commands)
    }
}

impl Default for RuntimeChart {
    fn default() -> Self {
        Self::new()
    }
}

/// Convenience functions for creating charts
impl RuntimeChart {
    /// Create a line chart
    pub fn line_chart() -> Self {
        Self::new()
    }
    
    /// Create a bar chart
    pub fn bar_chart() -> Self {
        Self::new()
    }
    
    /// Create a scatter chart
    pub fn scatter_chart() -> Self {
        Self::new()
    }
    
    /// Create a mixed chart (can contain multiple series types)
    pub fn mixed_chart() -> Self {
        Self::new()
    }
}

/// Simple data item for quick chart creation
#[derive(Debug, Clone)]
pub struct DataItem {
    pub x: f64,
    pub y: f64,
}

impl DataItem {
    pub fn new(x: f64, y: f64) -> Self {
        Self { x, y }
    }
}

impl From<(f64, f64)> for DataItem {
    fn from((x, y): (f64, f64)) -> Self {
        Self::new(x, y)
    }
}

// 旧的便捷函数和 ChartBuilder 已移动到 chart_builder.rs 模块中
// 新的实现提供了更多功能和更好的 API
