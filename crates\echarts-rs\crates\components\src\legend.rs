//! Legend component implementation

use crate::{Alignment, Component, Orientation, Position, Renderable, Themeable};
use echarts_core::*;
use echarts_charts::RenderContext;
use echarts_themes::Theme;
use serde::{Deserialize, Serialize};

/// Legend component for displaying series information
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Legend {
    /// Whether the legend is visible
    pub visible: bool,

    /// Legend position
    pub position: Position,

    /// Legend orientation
    pub orientation: Orientation,

    /// Text alignment
    pub alignment: Alignment,

    /// Legend items
    pub items: Vec<LegendItem>,

    /// Text style
    pub text_style: TextStyle,

    /// Item spacing
    pub item_gap: f64,

    /// Padding
    pub padding: [f64; 4], // top, right, bottom, left

    /// Background color
    pub background_color: Option<Color>,

    /// Border color
    pub border_color: Option<Color>,

    /// Border width
    pub border_width: f64,
}

/// Individual legend item
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LegendItem {
    /// Item name
    pub name: String,

    /// Item color
    pub color: Color,

    /// Whether the item is selected
    pub selected: bool,

    /// Item symbol
    pub symbol: Symbol,
}

impl Default for Legend {
    fn default() -> Self {
        Legend {
            visible: true,
            position: Position::Top,
            orientation: Orientation::Horizontal,
            alignment: Alignment::Center,
            items: Vec::new(),
            text_style: TextStyle::default(),
            item_gap: 10.0,
            padding: [5.0, 5.0, 5.0, 5.0],
            background_color: None,
            border_color: None,
            border_width: 1.0,
        }
    }
}

impl Legend {
    /// Create a new legend
    pub fn new() -> Self {
        Self::default()
    }

    /// Set the position
    pub fn position(mut self, position: Position) -> Self {
        self.position = position;
        self
    }

    /// Set the orientation
    pub fn orientation(mut self, orientation: Orientation) -> Self {
        self.orientation = orientation;
        self
    }

    /// Set the alignment
    pub fn alignment(mut self, alignment: Alignment) -> Self {
        self.alignment = alignment;
        self
    }

    /// Add a legend item
    pub fn add_item(mut self, item: LegendItem) -> Self {
        self.items.push(item);
        self
    }

    /// Set visibility
    pub fn visible(mut self, visible: bool) -> Self {
        self.visible = visible;
        self
    }
}

impl Component for Legend {
    fn component_type(&self) -> &'static str {
        "legend"
    }

    fn is_visible(&self) -> bool {
        self.visible
    }

    fn set_visible(&mut self, visible: bool) {
        self.visible = visible;
    }
}

impl Renderable for Legend {
    fn render<R: ChartRenderer>(&self, ctx: &mut RenderContext<R>, bounds: Bounds) -> Result<()> {
        if !self.visible {
            return Ok(());
        }

        // Calculate legend position
        let legend_bounds = self.calculate_bounds(bounds);

        // Draw background if specified
        if let Some(bg_color) = self.background_color {
            ctx.set_fill_color(bg_color);
            ctx.fill_rect(legend_bounds);
        }

        // Draw border if specified
        if let Some(border_color) = self.border_color {
            ctx.set_stroke(border_color, self.border_width);
            ctx.stroke_rect(legend_bounds);
        }

        // Draw legend items
        self.draw_items(ctx, legend_bounds)?;

        Ok(())
    }
}

impl Themeable for Legend {
    fn apply_theme(&mut self, theme: &Theme) {
        // Apply theme to text style and colors
        self.text_style.color = theme.text_style.color;
    }
}

impl Legend {
    /// Calculate the bounds for the legend based on its position
    fn calculate_bounds(&self, container_bounds: Bounds) -> Bounds {
        // For now, return a simple bounds calculation
        // TODO: Implement proper legend sizing based on content
        let width = 200.0;
        let height = 30.0;

        match self.position {
            Position::Top => Bounds::new(
                container_bounds.origin.x + (container_bounds.width() - width) / 2.0,
                container_bounds.origin.y,
                width,
                height,
            ),
            Position::Bottom => Bounds::new(
                container_bounds.origin.x + (container_bounds.width() - width) / 2.0,
                container_bounds.origin.y + container_bounds.height() - height,
                width,
                height,
            ),
            Position::Left => Bounds::new(
                container_bounds.origin.x,
                container_bounds.origin.y + (container_bounds.height() - height) / 2.0,
                width,
                height,
            ),
            Position::Right => Bounds::new(
                container_bounds.origin.x + container_bounds.width() - width,
                container_bounds.origin.y + (container_bounds.height() - height) / 2.0,
                width,
                height,
            ),
            _ => Bounds::new(
                container_bounds.origin.x,
                container_bounds.origin.y,
                width,
                height,
            ),
        }
    }

    /// Draw legend items
    fn draw_items<R: ChartRenderer>(&self, ctx: &mut RenderContext<R>, bounds: Bounds) -> Result<()> {
        let mut current_x = bounds.origin.x + self.padding[3]; // left padding
        let mut current_y = bounds.origin.y + self.padding[0]; // top padding

        for item in &self.items {
            // Draw color indicator
            let indicator_size = 12.0;
            let indicator_bounds = Bounds::new(
                current_x,
                current_y + (self.text_style.font_size - indicator_size) / 2.0,
                indicator_size,
                indicator_size,
            );

            // Use a simple rectangle drawing method
            ctx.draw_rect(indicator_bounds, Some(item.color), None);

            // Draw text
            let text_x = current_x + indicator_size + 5.0;
            let text_y = current_y + self.text_style.font_size / 2.0;
            let text_pos = Point::new(text_x, text_y);

            ctx.draw_text(item.name.clone(), text_pos, self.text_style.clone());

            // Move to next position
            match self.orientation {
                Orientation::Horizontal => {
                    current_x += indicator_size
                        + 5.0
                        + item.name.len() as f64 * self.text_style.font_size * 0.6
                        + self.item_gap;
                }
                Orientation::Vertical => {
                    current_y += self.text_style.font_size + self.item_gap;
                }
            }
        }

        Ok(())
    }
}
