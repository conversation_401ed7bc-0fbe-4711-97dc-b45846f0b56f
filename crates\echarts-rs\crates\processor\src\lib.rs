/*!
 * ECharts 数据预处理器
 * 
 * 负责在后台线程中解析ECharts配置，计算图表布局，
 * 生成绘制操作队列，与底层渲染器完全分离。
 */

use serde_json::Value;
use std::collections::HashMap;
use echarts_core::Color;

pub mod chart_processor;
pub mod operation_generator;
pub mod layout_calculator;

pub use chart_processor::*;
pub use operation_generator::*;
pub use layout_calculator::*;

/// 预处理结果
#[derive(Debug, Clone)]
pub struct ProcessedChart {
    /// 图表标题
    pub title: Option<String>,
    /// 背景色
    pub background_color: Option<Color>,
    /// 绘制操作队列
    pub draw_operations: Vec<DrawOperation>,
    /// 图表边界
    pub bounds: Bounds,
    /// 元数据
    pub metadata: HashMap<String, Value>,
}

/// 绘制操作（与渲染器无关的抽象）
#[derive(Debug, Clone)]
pub enum DrawOperation {
    /// 绘制矩形
    Rect {
        bounds: Bounds,
        fill_color: Color,
        stroke_color: Option<Color>,
        stroke_width: f64,
    },
    /// 绘制圆形
    Circle {
        center: Point,
        radius: f64,
        fill_color: Color,
        stroke_color: Option<Color>,
        stroke_width: f64,
    },
    /// 绘制线条
    Line {
        from: Point,
        to: Point,
        color: Color,
        width: f64,
        dash_pattern: Option<Vec<f64>>,
    },
    /// 绘制文本
    Text {
        text: String,
        position: Point,
        font_size: f64,
        color: Color,
        font_family: String,
        text_align: TextAlign,
    },
    /// 绘制路径
    Path {
        points: Vec<Point>,
        fill_color: Option<Color>,
        stroke_color: Option<Color>,
        stroke_width: f64,
        closed: bool,
    },
}

/// 几何基础类型
#[derive(Debug, Clone, Copy)]
pub struct Point {
    pub x: f64,
    pub y: f64,
}

#[derive(Debug, Clone, Copy)]
pub struct Bounds {
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
}

// Color 现在从 echarts_core 导入，不再重复定义

#[derive(Debug, Clone)]
pub enum TextAlign {
    Left,
    Center,
    Right,
}

impl Point {
    pub fn new(x: f64, y: f64) -> Self {
        Self { x, y }
    }
}

impl Bounds {
    pub fn new(x: f64, y: f64, width: f64, height: f64) -> Self {
        Self { x, y, width, height }
    }
}

// Color 实现现在从 echarts_core 提供，包含所有必要的方法和常量

/// 预处理器错误类型
#[derive(Debug)]
pub enum ProcessorError {
    InvalidData(String),
    ParseError(String),
    LayoutError(String),
    GenerationError(String),
}

impl std::fmt::Display for ProcessorError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ProcessorError::InvalidData(msg) => write!(f, "Invalid data: {}", msg),
            ProcessorError::ParseError(msg) => write!(f, "Parse error: {}", msg),
            ProcessorError::LayoutError(msg) => write!(f, "Layout error: {}", msg),
            ProcessorError::GenerationError(msg) => write!(f, "Generation error: {}", msg),
        }
    }
}

impl std::error::Error for ProcessorError {}

pub type Result<T> = std::result::Result<T, ProcessorError>;

/// 主要的图表预处理器
pub struct ChartProcessor {
    /// 布局计算器
    layout_calculator: LayoutCalculator,
    /// 操作生成器
    operation_generator: OperationGenerator,
}

impl ChartProcessor {
    /// 创建新的预处理器
    pub fn new() -> Self {
        Self {
            layout_calculator: LayoutCalculator::new(),
            operation_generator: OperationGenerator::new(),
        }
    }
    
    /// 预处理ECharts配置（在后台线程调用）
    pub fn process_chart(&mut self, echarts_option: &Value, bounds: Bounds) -> Result<ProcessedChart> {
        println!("🔄 开始预处理ECharts图表数据（后台线程）");
        
        let mut processed = ProcessedChart {
            title: None,
            background_color: Some(Color::WHITE),
            draw_operations: Vec::new(),
            bounds,
            metadata: HashMap::new(),
        };
        
        // 1. 解析基础配置
        self.parse_basic_config(echarts_option, &mut processed)?;
        
        // 2. 计算布局
        let layout = self.layout_calculator.calculate_layout(&processed, echarts_option)?;
        
        // 3. 生成绘制操作
        self.operation_generator.generate_operations(echarts_option, &layout, &mut processed)?;
        
        println!("✅ 图表预处理完成，生成了 {} 个绘制操作", processed.draw_operations.len());
        Ok(processed)
    }
    
    /// 解析基础配置
    fn parse_basic_config(&self, option: &Value, processed: &mut ProcessedChart) -> Result<()> {
        // 解析标题
        if let Some(title_obj) = option.get("title") {
            if let Some(title_text) = title_obj.get("text").and_then(|t| t.as_str()) {
                processed.title = Some(title_text.to_string());
            }
        }
        
        // 解析背景色
        if let Some(bg_color) = option.get("backgroundColor").and_then(|c| c.as_str()) {
            if bg_color == "#ffffff" || bg_color == "white" {
                processed.background_color = Some(Color::WHITE);
            }
        }
        
        Ok(())
    }
}

impl Default for ChartProcessor {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_basic_processing() {
        let mut processor = ChartProcessor::new();
        let bounds = Bounds::new(0.0, 0.0, 800.0, 600.0);
        
        let option = json!({
            "title": {
                "text": "测试图表"
            },
            "series": [{
                "type": "bar",
                "data": [10, 20, 30, 40, 50]
            }]
        });
        
        let result = processor.process_chart(&option, bounds);
        assert!(result.is_ok());
        
        let processed = result.unwrap();
        assert_eq!(processed.title, Some("测试图表".to_string()));
        assert!(!processed.draw_operations.is_empty());
    }
}
