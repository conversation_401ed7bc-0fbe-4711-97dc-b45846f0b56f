//! 图表渲染测试
//!
//! 测试使用 Charts 模块和 SVG 渲染器创建完整图表

use echarts_core::{
    Bounds, Color, CartesianCoordinateSystem, CoordinateSystem,
};
use svg_renderer::SvgRenderer;

// 模拟 Charts 模块的导入
// 注意：这里需要添加对 echarts-charts 的依赖
// use echarts_charts::{LineSeries, BarSeries, ScatterSeries};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("📊 测试图表渲染到 SVG");

    // 创建 SVG 渲染器
    let mut renderer = SvgRenderer::with_size(800.0, 600.0);

    // 创建坐标系统
    let chart_bounds = Bounds::new(50.0, 50.0, 700.0, 500.0);
    let coord_system = CartesianCoordinateSystem::new(
        chart_bounds,
        (0.0, 6.0),  // X 轴范围
        (0.0, 200.0), // Y 轴范围
    );

    // 模拟创建折线图系列
    // let line_series = LineSeries::new("销售数据")
    //     .data(vec![(1.0, 120.0), (2.0, 200.0), (3.0, 150.0), (4.0, 80.0), (5.0, 170.0)])
    //     .color(Color::rgb(0.2, 0.6, 1.0))
    //     .line_width(3.0)
    //     .smooth(true)
    //     .show_symbols(true);

    // 模拟创建柱状图系列
    // let bar_series = BarSeries::new("目标数据")
    //     .data(vec![(1.0, 100.0), (2.0, 180.0), (3.0, 140.0), (4.0, 90.0), (5.0, 160.0)])
    //     .color(Color::rgb(1.0, 0.4, 0.2))
    //     .bar_width(0.6);

    // 由于当前依赖问题，我们直接创建一些 DrawCommand 来模拟图表
    let mut commands = Vec::new();

    // 模拟折线图的绘制命令
    let line_points = vec![
        (1.0, 120.0), (2.0, 200.0), (3.0, 150.0), (4.0, 80.0), (5.0, 170.0)
    ];

    // 转换数据点到屏幕坐标
    let mut screen_points = Vec::new();
    for (x, y) in line_points {
        let data_values = vec![
            echarts_core::DataValue::Number(x),
            echarts_core::DataValue::Number(y)
        ];
        if let Ok(point) = coord_system.data_to_point(&data_values) {
            screen_points.push(point);
        }
    }

    // 绘制折线
    for i in 0..screen_points.len() - 1 {
        commands.push(echarts_core::DrawCommand::Line {
            from: screen_points[i],
            to: screen_points[i + 1],
            style: echarts_core::draw_commands::LineStyle {
                color: Color::rgb(0.2, 0.6, 1.0),
                width: 3.0,
                opacity: 1.0,
                dash_pattern: None,
                cap: echarts_core::draw_commands::LineCap::Round,
                join: echarts_core::draw_commands::LineJoin::Round,
            },
        });
    }

    // 绘制数据点
    for point in &screen_points {
        commands.push(echarts_core::DrawCommand::Circle {
            center: *point,
            radius: 4.0,
            style: echarts_core::draw_commands::CircleStyle {
                fill: Some(Color::rgb(0.2, 0.6, 1.0)),
                stroke: Some(echarts_core::draw_commands::LineStyle {
                    color: Color::rgb(1.0, 1.0, 1.0),
                    width: 2.0,
                    opacity: 1.0,
                    dash_pattern: None,
                    cap: echarts_core::draw_commands::LineCap::Round,
                    join: echarts_core::draw_commands::LineJoin::Round,
                }),
                opacity: 1.0,
            },
        });
    }

    // 添加图表标题
    commands.push(echarts_core::DrawCommand::Text {
        text: "销售数据趋势图".to_string(),
        position: echarts_core::Point::new(400.0, 30.0),
        style: echarts_core::draw_commands::TextStyle {
            font_family: "Arial".to_string(),
            font_size: 24.0,
            font_weight: echarts_core::draw_commands::FontWeight::Bold,
            font_style: echarts_core::draw_commands::FontStyle::Normal,
            color: Color::rgb(0.2, 0.2, 0.2),
            opacity: 1.0,
            align: echarts_core::draw_commands::TextAlign::Center,
            baseline: echarts_core::draw_commands::TextBaseline::Middle,
            rotation: 0.0,
            letter_spacing: 0.0,
            line_height: 1.0,
        },
    });

    // 添加坐标轴（简化版）
    // X 轴
    commands.push(echarts_core::DrawCommand::Line {
        from: echarts_core::Point::new(chart_bounds.origin.x, chart_bounds.origin.y + chart_bounds.size.height),
        to: echarts_core::Point::new(chart_bounds.origin.x + chart_bounds.size.width, chart_bounds.origin.y + chart_bounds.size.height),
        style: echarts_core::draw_commands::LineStyle {
            color: Color::rgb(0.5, 0.5, 0.5),
            width: 1.0,
            opacity: 1.0,
            dash_pattern: None,
            cap: echarts_core::draw_commands::LineCap::Butt,
            join: echarts_core::draw_commands::LineJoin::Miter,
        },
    });

    // Y 轴
    commands.push(echarts_core::DrawCommand::Line {
        from: echarts_core::Point::new(chart_bounds.origin.x, chart_bounds.origin.y),
        to: echarts_core::Point::new(chart_bounds.origin.x, chart_bounds.origin.y + chart_bounds.size.height),
        style: echarts_core::draw_commands::LineStyle {
            color: Color::rgb(0.5, 0.5, 0.5),
            width: 1.0,
            opacity: 1.0,
            dash_pattern: None,
            cap: echarts_core::draw_commands::LineCap::Butt,
            join: echarts_core::draw_commands::LineJoin::Miter,
        },
    });

    // 渲染为 SVG
    let svg_content = renderer.render_commands(commands)?;

    // 输出 SVG 内容
    println!("生成的图表 SVG：");
    println!("{}", svg_content);

    // 保存到文件
    std::fs::write("chart_output.svg", &svg_content)?;
    println!("✅ 图表已保存到 chart_output.svg");

    Ok(())
}
