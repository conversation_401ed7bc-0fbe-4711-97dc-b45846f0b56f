//! ECharts GPUI 演示 - 真实 Canvas 绘制版本
//!
//! 这个演示展示了如何使用 ECharts-rs 与 GPUI 进行真实的 Canvas 绘制。
//! 
//! ## 特性
//! - 真实的 GPUI Canvas 绘制调用
//! - ECharts-rs 绘制命令生成
//! - GPUI 渲染器集成
//! - 现代化的用户界面

use echarts_rs::{
    LineSeries, Color,
    CartesianCoordinateSystem, Bounds as EchartsBounds, Series
};
use gpui_renderer::GpuiRenderer;
use gpui::*;
use gpui_component::StyledExt;

fn main() {
    println!("🚀 启动 ECharts + GPUI Canvas 演示...");

    App::new().run(move |cx: &mut AppContext| {
        println!("📱 应用程序上下文已创建");
        
        // 获取显示器信息
        let displays = cx.displays();
        let primary_display = displays.first().expect("需要至少一个显示器");
        let display_size = primary_display.bounds().size;
        
        let window_size = size(px(1200.0), px(800.0));
        println!("🖥️  显示器大小: {:?}, 窗口大小: {:?}", display_size, window_size);
        
        println!("🪟 准备创建窗口...");
        
        let window_options = WindowOptions {
            window_bounds: Some(WindowBounds::Windowed(Bounds::centered(
                None,
                window_size,
                cx,
            ))),
            titlebar: Some(TitlebarOptions {
                title: Some("ECharts GPUI Canvas 演示".into()),
                appears_transparent: false,
                traffic_light_position: None,
            }),
            window_background: WindowBackgroundAppearance::Opaque,
            focus: true,
            show: true,
            kind: WindowKind::Normal,
            is_movable: true,
            display_id: None,
            app_id: "echarts-gpui-canvas".into(),
            window_decorations: None,
            window_min_size: Some(size(px(800.0), px(600.0))),
        };
        
        println!("⏳ 等待窗口显示...");
        
        cx.open_window(window_options, |cx| {
            println!("✅ 窗口已创建，正在初始化 ECharts 演示...");
            cx.new_view(|_cx| EChartsGpuiCanvasDemo::new())
        }).expect("无法创建窗口");
        
        println!("🎉 窗口创建成功！");
    });
}

/// 图表类型枚举
#[derive(Debug, Clone, PartialEq)]
enum ChartType {
    LineChart,
    BarChart,
    ScatterChart,
}

impl ChartType {
    fn name(&self) -> &'static str {
        match self {
            ChartType::LineChart => "折线图",
            ChartType::BarChart => "柱状图",
            ChartType::ScatterChart => "散点图",
        }
    }
}

/// ECharts GPUI Canvas 演示应用
struct EChartsGpuiCanvasDemo {
    chart_type: ChartType,
    current_series: Box<dyn Series>,
    coord_system: CartesianCoordinateSystem,
}

impl EChartsGpuiCanvasDemo {
    fn new() -> Self {
        println!("🎯 初始化 ECharts 演示...");
        
        let chart_type = ChartType::LineChart;
        let current_series = Self::create_line_series();
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(0.0, 0.0, 10.0, 100.0),
            (0.0, 10.0),
            (0.0, 100.0)
        );
        
        println!("📊 ECharts 系列已创建");
        
        Self {
            chart_type,
            current_series,
            coord_system,
        }
    }
    
    /// 创建折线图系列
    fn create_line_series() -> Box<dyn Series> {
        println!("📈 创建折线图系列...");
        
        let data_points = vec![
            (1.0, 20.0), (2.0, 35.0), (3.0, 25.0), (4.0, 60.0), (5.0, 45.0),
            (6.0, 80.0), (7.0, 65.0), (8.0, 90.0), (9.0, 75.0), (10.0, 85.0)
        ];
        
        println!("📊 生成数据点: {} 个", data_points.len());
        
        Box::new(
            LineSeries::new("折线图演示")
                .data(data_points)
                .color(Color::BLUE)
                .line_width(2.0)
        )
    }
}

impl Render for EChartsGpuiCanvasDemo {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("🎨 渲染 ECharts 演示界面...");
        
        div()
            .size_full()
            .bg(rgb(0xf8fafc))
            .flex()
            .flex_col()
            .child(
                // 标题栏
                div()
                    .w_full()
                    .h(px(60.0))
                    .bg(rgb(0x1f2937))
                    .flex()
                    .items_center()
                    .justify_center()
                    .child(
                        div()
                            .text_xl()
                            .font_bold()
                            .text_color(rgb(0xffffff))
                            .child("📊 ECharts + GPUI Canvas 真实绘制演示")
                    )
            )
            .child(
                // 主要内容区域
                div()
                    .flex_1()
                    .p_8()
                    .flex()
                    .items_center()
                    .justify_center()
                    .child(
                        div()
                            .w(px(800.0))
                            .h(px(600.0))
                            .child({
                                println!("📊 渲染图表区域...");
                                println!("📊 渲染 ECharts 图表...");

                                // 真实的图表渲染区域
                                ChartCanvasElement::new(
                                    self.current_series.clone_series(),
                                    self.coord_system.clone(),
                                    self.chart_type.clone()
                                )
                            })
                    )
            )
    }
}

/// 图表画布元素 - 负责真实的 Canvas 绘制
struct ChartCanvasElement {
    series: Box<dyn Series>,
    coord_system: CartesianCoordinateSystem,
    chart_type: ChartType,
    renderer: GpuiRenderer,
}

impl ChartCanvasElement {
    fn new(series: Box<dyn Series>, coord_system: CartesianCoordinateSystem, chart_type: ChartType) -> Self {
        Self {
            series,
            coord_system,
            chart_type,
            renderer: GpuiRenderer::new().with_debug(true),
        }
    }
}

impl IntoElement for ChartCanvasElement {
    type Element = Div;

    fn into_element(mut self) -> Self::Element {
        // 生成绘制命令
        let render_result = self.series.render_to_commands(&self.coord_system);

        // 提取命令用于显示状态
        let commands_count = match &render_result {
            Ok(commands) => {
                println!("🎨 成功生成 {} 个绘制命令", commands.len());
                commands.len()
            },
            Err(e) => {
                println!("❌ 绘制命令生成失败: {:?}", e);
                0
            },
        };

        div()
            .w_full()
            .h_full()
            .bg(rgb(0xffffff))
            .border_1()
            .border_color(rgb(0xe5e7eb))
            .rounded_lg()
            .relative()
            .child(
                // 图表标题
                div()
                    .absolute()
                    .top_2()
                    .left_4()
                    .text_lg()
                    .font_semibold()
                    .text_color(rgb(0x374151))
                    .child(format!("📊 {} - GPUI Canvas", self.chart_type.name()))
            )
            .child(
                // 渲染状态
                div()
                    .absolute()
                    .top_2()
                    .right_4()
                    .text_sm()
                    .text_color(rgb(0x64748b))
                    .child(if commands_count > 0 {
                        format!("✅ {} 个命令", commands_count)
                    } else {
                        "❌ 渲染失败".to_string()
                    })
            )
            .child(
                // 真正的 GPUI Canvas 绘制区域
                canvas(
                    // prepaint 回调：准备绘制数据
                    move |bounds, _window, _cx| {
                        (render_result, bounds)
                    },
                    // paint 回调：执行真实绘制
                    move |bounds, (commands_result, canvas_bounds), window, cx| {
                        match commands_result {
                            Ok(commands) => {
                                println!("🖌️ 开始 GPUI Canvas 绘制 {} 个命令", commands.len());

                                // 使用 GPUI 渲染器进行真实绘制
                                if let Err(e) = self.renderer.render_chart(
                                    commands,
                                    *canvas_bounds,
                                    window,
                                    cx,
                                ) {
                                    println!("❌ GPUI 渲染器绘制失败: {:?}", e);
                                } else {
                                    println!("✅ GPUI Canvas 绘制成功！");
                                }
                            }
                            Err(e) => {
                                println!("❌ 绘制命令生成失败: {:?}", e);
                            }
                        }
                    }
                )
                .size_full()
            )
            .child(
                // 技术信息
                div()
                    .absolute()
                    .bottom_2()
                    .left_4()
                    .text_xs()
                    .text_color(rgb(0x9ca3af))
                    .child("🎨 ECharts-rs + GPUI Canvas 真实绘制")
            )
    }
}
