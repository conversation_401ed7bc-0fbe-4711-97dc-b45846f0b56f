//! 专业曲线图系统
//!
//! 提供全面的专业级曲线图功能，包括：
//! - 高级数据处理
//! - 交互功能
//! - 专业图表类型
//! - 技术分析指标
//! - 主题系统
//! - 导出功能

use std::fs;
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("📈 专业曲线图系统生成器");
    println!("{}", "=".repeat(60));

    // 确保输出目录存在
    let output_dir = "temp/svg/professional_line_charts";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 专业金融图表
    println!("\n💰 1. 生成专业金融图表...");
    generate_financial_charts(output_dir)?;

    // 2. 技术分析图表
    println!("\n📊 2. 生成技术分析图表...");
    generate_technical_analysis_charts(output_dir)?;

    // 3. 科学数据图表
    println!("\n🔬 3. 生成科学数据图表...");
    generate_scientific_charts(output_dir)?;

    // 4. 商业智能图表
    println!("\n💼 4. 生成商业智能图表...");
    generate_business_intelligence_charts(output_dir)?;

    // 5. 实时监控图表
    println!("\n⚡ 5. 生成实时监控图表...");
    generate_realtime_monitoring_charts(output_dir)?;

    // 6. 交互式图表
    println!("\n🖱️ 6. 生成交互式图表...");
    generate_interactive_charts(output_dir)?;

    // 7. 主题变体图表
    println!("\n🎨 7. 生成主题变体图表...");
    generate_themed_charts(output_dir)?;

    // 8. 高级分析图表
    println!("\n🧮 8. 生成高级分析图表...");
    generate_advanced_analytics_charts(output_dir)?;

    // 9. 生成专业展示页面
    println!("\n📄 9. 生成专业展示页面...");
    generate_professional_showcase(output_dir)?;

    // 10. 生成配置文件和文档
    println!("\n📋 10. 生成配置文件和文档...");
    generate_configuration_files(output_dir)?;

    println!("\n🎉 专业曲线图系统生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/professional_showcase.html 查看专业展示", output_dir);
    println!("📖 查看 {}/README.md 了解详细功能", output_dir);

    Ok(())
}

/// 生成专业金融图表
fn generate_financial_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. K线图（蜡烛图）
    let candlestick_data = generate_candlestick_data();
    let candlestick_svg = create_candlestick_chart("专业K线图", &candlestick_data, 900.0, 600.0);
    fs::write(format!("{}/01_candlestick_chart.svg", output_dir), candlestick_svg)?;

    // 2. 股票价格与成交量
    let stock_data = generate_stock_data();
    let stock_volume_svg = create_stock_volume_chart("股票价格与成交量", &stock_data, 900.0, 700.0);
    fs::write(format!("{}/02_stock_volume_chart.svg", output_dir), stock_volume_svg)?;

    // 3. 多股票对比
    let multi_stock_data = generate_multi_stock_data();
    let multi_stock_svg = create_multi_stock_chart("多股票对比分析", &multi_stock_data, 1000.0, 600.0);
    fs::write(format!("{}/03_multi_stock_chart.svg", output_dir), multi_stock_svg)?;

    // 4. 财务指标趋势
    let financial_metrics = generate_financial_metrics();
    let metrics_svg = create_financial_metrics_chart("财务指标趋势", &financial_metrics, 900.0, 600.0);
    fs::write(format!("{}/04_financial_metrics.svg", output_dir), metrics_svg)?;

    println!("  ✅ 专业金融图表生成完成 (4个图表)");
    Ok(())
}

/// 生成技术分析图表
fn generate_technical_analysis_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 移动平均线
    let ma_data = generate_moving_average_data();
    let ma_svg = create_moving_average_chart("移动平均线分析", &ma_data, 900.0, 600.0);
    fs::write(format!("{}/05_moving_average.svg", output_dir), ma_svg)?;

    // 2. 布林带
    let bollinger_data = generate_bollinger_bands_data();
    let bollinger_svg = create_bollinger_bands_chart("布林带分析", &bollinger_data, 900.0, 600.0);
    fs::write(format!("{}/06_bollinger_bands.svg", output_dir), bollinger_svg)?;

    // 3. RSI指标
    let rsi_data = generate_rsi_data();
    let rsi_svg = create_rsi_chart("RSI相对强弱指标", &rsi_data, 900.0, 500.0);
    fs::write(format!("{}/07_rsi_indicator.svg", output_dir), rsi_svg)?;

    // 4. MACD指标
    let macd_data = generate_macd_data();
    let macd_svg = create_macd_chart("MACD指标分析", &macd_data, 900.0, 600.0);
    fs::write(format!("{}/08_macd_indicator.svg", output_dir), macd_svg)?;

    println!("  ✅ 技术分析图表生成完成 (4个图表)");
    Ok(())
}

/// 生成科学数据图表
fn generate_scientific_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 实验数据散点图
    let experiment_data = generate_experiment_data();
    let experiment_svg = create_experiment_scatter_chart("实验数据分析", &experiment_data, 800.0, 600.0);
    fs::write(format!("{}/09_experiment_data.svg", output_dir), experiment_svg)?;

    // 2. 回归分析图
    let regression_data = generate_regression_data();
    let regression_svg = create_regression_chart("线性回归分析", &regression_data, 800.0, 600.0);
    fs::write(format!("{}/10_regression_analysis.svg", output_dir), regression_svg)?;

    // 3. 误差条图表
    let error_bar_data = generate_error_bar_data();
    let error_bar_svg = create_error_bar_chart("误差条分析", &error_bar_data, 800.0, 600.0);
    fs::write(format!("{}/11_error_bar_chart.svg", output_dir), error_bar_svg)?;

    // 4. 多变量相关性
    let correlation_data = generate_correlation_data();
    let correlation_svg = create_correlation_chart("多变量相关性分析", &correlation_data, 900.0, 600.0);
    fs::write(format!("{}/12_correlation_analysis.svg", output_dir), correlation_svg)?;

    println!("  ✅ 科学数据图表生成完成 (4个图表)");
    Ok(())
}

/// 生成商业智能图表
fn generate_business_intelligence_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. KPI仪表板
    let kpi_data = generate_kpi_data();
    let kpi_svg = create_kpi_dashboard("KPI关键指标仪表板", &kpi_data, 1000.0, 700.0);
    fs::write(format!("{}/13_kpi_dashboard.svg", output_dir), kpi_svg)?;

    // 2. 销售漏斗分析
    let funnel_data = generate_funnel_data();
    let funnel_svg = create_sales_funnel_chart("销售漏斗分析", &funnel_data, 800.0, 600.0);
    fs::write(format!("{}/14_sales_funnel.svg", output_dir), funnel_svg)?;

    // 3. 用户行为分析
    let behavior_data = generate_user_behavior_data();
    let behavior_svg = create_user_behavior_chart("用户行为分析", &behavior_data, 900.0, 600.0);
    fs::write(format!("{}/15_user_behavior.svg", output_dir), behavior_svg)?;

    // 4. 收入预测
    let forecast_data = generate_revenue_forecast_data();
    let forecast_svg = create_revenue_forecast_chart("收入预测分析", &forecast_data, 900.0, 600.0);
    fs::write(format!("{}/16_revenue_forecast.svg", output_dir), forecast_svg)?;

    println!("  ✅ 商业智能图表生成完成 (4个图表)");
    Ok(())
}

/// 生成实时监控图表
fn generate_realtime_monitoring_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 系统性能监控
    let performance_data = generate_performance_data();
    let performance_svg = create_performance_monitor("系统性能实时监控", &performance_data, 1000.0, 600.0);
    fs::write(format!("{}/17_performance_monitor.svg", output_dir), performance_svg)?;

    // 2. 网络流量监控
    let traffic_data = generate_traffic_data();
    let traffic_svg = create_traffic_monitor("网络流量监控", &traffic_data, 900.0, 600.0);
    fs::write(format!("{}/18_traffic_monitor.svg", output_dir), traffic_svg)?;

    // 3. 服务器状态监控
    let server_data = generate_server_data();
    let server_svg = create_server_monitor("服务器状态监控", &server_data, 1000.0, 700.0);
    fs::write(format!("{}/19_server_monitor.svg", output_dir), server_svg)?;

    // 4. 实时数据流
    let stream_data = generate_stream_data();
    let stream_svg = create_realtime_stream("实时数据流监控", &stream_data, 900.0, 500.0);
    fs::write(format!("{}/20_realtime_stream.svg", output_dir), stream_svg)?;

    println!("  ✅ 实时监控图表生成完成 (4个图表)");
    Ok(())
}

/// 生成交互式图表
fn generate_interactive_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 可缩放曲线图
    let zoomable_data = generate_large_dataset();
    let zoomable_svg = create_zoomable_chart("可缩放交互曲线图", &zoomable_data, 1000.0, 600.0);
    fs::write(format!("{}/21_zoomable_chart.svg", output_dir), zoomable_svg)?;

    // 2. 悬停提示图表
    let tooltip_data = generate_tooltip_data();
    let tooltip_svg = create_tooltip_chart("悬停提示图表", &tooltip_data, 900.0, 600.0);
    fs::write(format!("{}/22_tooltip_chart.svg", output_dir), tooltip_svg)?;

    // 3. 可选择数据系列
    let selectable_data = generate_selectable_data();
    let selectable_svg = create_selectable_chart("可选择数据系列", &selectable_data, 900.0, 600.0);
    fs::write(format!("{}/23_selectable_chart.svg", output_dir), selectable_svg)?;

    // 4. 动态更新图表
    let dynamic_data = generate_dynamic_data();
    let dynamic_svg = create_dynamic_chart("动态更新图表", &dynamic_data, 900.0, 600.0);
    fs::write(format!("{}/24_dynamic_chart.svg", output_dir), dynamic_svg)?;

    println!("  ✅ 交互式图表生成完成 (4个图表)");
    Ok(())
}

/// 生成主题变体图表
fn generate_themed_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let sample_data = generate_sample_data();

    // 1. 深色主题
    let dark_svg = create_themed_chart("深色主题曲线图", &sample_data, 800.0, 600.0, &get_dark_theme());
    fs::write(format!("{}/25_dark_theme.svg", output_dir), dark_svg)?;

    // 2. 商务主题
    let business_svg = create_themed_chart("商务主题曲线图", &sample_data, 800.0, 600.0, &get_business_theme());
    fs::write(format!("{}/26_business_theme.svg", output_dir), business_svg)?;

    // 3. 科技主题
    let tech_svg = create_themed_chart("科技主题曲线图", &sample_data, 800.0, 600.0, &get_tech_theme());
    fs::write(format!("{}/27_tech_theme.svg", output_dir), tech_svg)?;

    // 4. 自然主题
    let nature_svg = create_themed_chart("自然主题曲线图", &sample_data, 800.0, 600.0, &get_nature_theme());
    fs::write(format!("{}/28_nature_theme.svg", output_dir), nature_svg)?;

    println!("  ✅ 主题变体图表生成完成 (4个图表)");
    Ok(())
}

/// 生成高级分析图表
fn generate_advanced_analytics_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 时间序列分解
    let decomposition_data = generate_time_series_data();
    let decomposition_svg = create_time_series_decomposition("时间序列分解", &decomposition_data, 1000.0, 800.0);
    fs::write(format!("{}/29_time_series_decomposition.svg", output_dir), decomposition_svg)?;

    // 2. 异常检测
    let anomaly_data = generate_anomaly_data();
    let anomaly_svg = create_anomaly_detection_chart("异常检测分析", &anomaly_data, 900.0, 600.0);
    fs::write(format!("{}/30_anomaly_detection.svg", output_dir), anomaly_svg)?;

    // 3. 预测区间
    let prediction_data = generate_prediction_data();
    let prediction_svg = create_prediction_interval_chart("预测区间分析", &prediction_data, 900.0, 600.0);
    fs::write(format!("{}/31_prediction_interval.svg", output_dir), prediction_svg)?;

    // 4. 多维度分析
    let multidim_data = generate_multidimensional_data();
    let multidim_svg = create_multidimensional_chart("多维度数据分析", &multidim_data, 1000.0, 700.0);
    fs::write(format!("{}/32_multidimensional.svg", output_dir), multidim_svg)?;

    println!("  ✅ 高级分析图表生成完成 (4个图表)");
    Ok(())
}

// ============================================================================
// 数据结构定义
// ============================================================================

/// 蜡烛图数据点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CandlestickPoint {
    pub timestamp: f64,
    pub open: f64,
    pub high: f64,
    pub low: f64,
    pub close: f64,
    pub volume: f64,
}

/// 股票数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StockData {
    pub symbol: String,
    pub data: Vec<CandlestickPoint>,
}

/// 技术指标数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TechnicalIndicator {
    pub name: String,
    pub values: Vec<(f64, f64)>,
    pub color: String,
    pub line_type: LineType,
}

/// 线条类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LineType {
    Solid,
    Dashed,
    Dotted,
    DashDot,
}

/// 主题配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChartTheme {
    pub name: String,
    pub background: String,
    pub grid_color: String,
    pub text_color: String,
    pub axis_color: String,
    pub primary_colors: Vec<String>,
    pub font_family: String,
    pub font_size: u32,
}

/// 图表配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChartConfig {
    pub title: String,
    pub width: f64,
    pub height: f64,
    pub theme: ChartTheme,
    pub show_grid: bool,
    pub show_legend: bool,
    pub show_tooltip: bool,
    pub enable_zoom: bool,
    pub enable_pan: bool,
    pub animation_duration: u32,
}

/// 误差条数据
#[derive(Debug, Clone)]
pub struct ErrorBarPoint {
    pub x: f64,
    pub y: f64,
    pub error_low: f64,
    pub error_high: f64,
}

/// KPI数据
#[derive(Debug, Clone)]
pub struct KpiData {
    pub name: String,
    pub current_value: f64,
    pub target_value: f64,
    pub trend: Vec<(f64, f64)>,
    pub unit: String,
    pub format: String,
}

// ============================================================================
// 数据生成函数
// ============================================================================

/// 生成蜡烛图数据
fn generate_candlestick_data() -> Vec<CandlestickPoint> {
    let mut data = Vec::new();
    let mut price = 100.0;

    for i in 0..60 {
        let timestamp = i as f64;
        let volatility = 0.02;

        // 生成开盘价
        let open = price;

        // 生成随机变化
        let change = (rand_f64() - 0.5) * volatility * price;
        let close = open + change;

        // 生成高低价
        let high = open.max(close) + rand_f64() * volatility * price * 0.5;
        let low = open.min(close) - rand_f64() * volatility * price * 0.5;

        // 生成成交量
        let volume = 1000000.0 + rand_f64() * 5000000.0;

        data.push(CandlestickPoint {
            timestamp,
            open,
            high,
            low,
            close,
            volume,
        });

        price = close;
    }

    data
}

/// 生成股票数据
fn generate_stock_data() -> StockData {
    StockData {
        symbol: "AAPL".to_string(),
        data: generate_candlestick_data(),
    }
}

/// 生成多股票数据
fn generate_multi_stock_data() -> HashMap<String, Vec<(f64, f64)>> {
    let mut stocks = HashMap::new();

    let symbols = vec!["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN"];

    for symbol in symbols {
        let mut data = Vec::new();
        let mut price = 100.0 + rand_f64() * 200.0;

        for i in 0..100 {
            let change = (rand_f64() - 0.5) * 0.05 * price;
            price += change;
            data.push((i as f64, price));
        }

        stocks.insert(symbol.to_string(), data);
    }

    stocks
}

/// 生成财务指标数据
fn generate_financial_metrics() -> HashMap<String, Vec<(f64, f64)>> {
    let mut metrics = HashMap::new();

    // 收入
    let revenue: Vec<(f64, f64)> = (0..12).map(|i| {
        let base = 1000000.0;
        let growth = 1.0 + (i as f64 * 0.05);
        let seasonal = 1.0 + (i as f64 * std::f64::consts::PI / 6.0).sin() * 0.1;
        (i as f64, base * growth * seasonal)
    }).collect();

    // 利润
    let profit: Vec<(f64, f64)> = revenue.iter().map(|(x, rev)| {
        (*x, rev * (0.15 + rand_f64() * 0.1))
    }).collect();

    // 现金流
    let cashflow: Vec<(f64, f64)> = revenue.iter().map(|(x, rev)| {
        (*x, rev * (0.12 + rand_f64() * 0.08))
    }).collect();

    metrics.insert("收入".to_string(), revenue);
    metrics.insert("利润".to_string(), profit);
    metrics.insert("现金流".to_string(), cashflow);

    metrics
}

/// 生成移动平均线数据
fn generate_moving_average_data() -> (Vec<(f64, f64)>, Vec<TechnicalIndicator>) {
    let mut price_data = Vec::new();
    let mut price = 100.0;

    // 生成价格数据
    for i in 0..100 {
        let change = (rand_f64() - 0.5) * 0.04 * price;
        price += change;
        price_data.push((i as f64, price));
    }

    // 计算移动平均线
    let ma5 = calculate_moving_average(&price_data, 5);
    let ma20 = calculate_moving_average(&price_data, 20);
    let ma60 = calculate_moving_average(&price_data, 60);

    let indicators = vec![
        TechnicalIndicator {
            name: "MA5".to_string(),
            values: ma5,
            color: "ff6b6b".to_string(),
            line_type: LineType::Solid,
        },
        TechnicalIndicator {
            name: "MA20".to_string(),
            values: ma20,
            color: "4ecdc4".to_string(),
            line_type: LineType::Solid,
        },
        TechnicalIndicator {
            name: "MA60".to_string(),
            values: ma60,
            color: "45b7d1".to_string(),
            line_type: LineType::Solid,
        },
    ];

    (price_data, indicators)
}

/// 计算移动平均线
fn calculate_moving_average(data: &[(f64, f64)], period: usize) -> Vec<(f64, f64)> {
    let mut ma_data = Vec::new();

    for i in period..data.len() {
        let sum: f64 = data[i-period+1..=i].iter().map(|(_, y)| y).sum();
        let avg = sum / period as f64;
        ma_data.push((data[i].0, avg));
    }

    ma_data
}

/// 简单随机数生成器
fn rand_f64() -> f64 {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};
    use std::time::{SystemTime, UNIX_EPOCH};

    let mut hasher = DefaultHasher::new();
    SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_nanos().hash(&mut hasher);
    (hasher.finish() % 1000000) as f64 / 1000000.0
}

/// 生成布林带数据
fn generate_bollinger_bands_data() -> (Vec<(f64, f64)>, Vec<TechnicalIndicator>) {
    let mut price_data = Vec::new();
    let mut price = 100.0;

    // 生成价格数据
    for i in 0..100 {
        let change = (rand_f64() - 0.5) * 0.04 * price;
        price += change;
        price_data.push((i as f64, price));
    }

    // 计算布林带
    let (middle, upper, lower) = calculate_bollinger_bands(&price_data, 20, 2.0);

    let indicators = vec![
        TechnicalIndicator {
            name: "中轨".to_string(),
            values: middle,
            color: "ffa726".to_string(),
            line_type: LineType::Solid,
        },
        TechnicalIndicator {
            name: "上轨".to_string(),
            values: upper,
            color: "ef5350".to_string(),
            line_type: LineType::Dashed,
        },
        TechnicalIndicator {
            name: "下轨".to_string(),
            values: lower,
            color: "66bb6a".to_string(),
            line_type: LineType::Dashed,
        },
    ];

    (price_data, indicators)
}

/// 计算布林带
fn calculate_bollinger_bands(data: &[(f64, f64)], period: usize, std_dev: f64) -> (Vec<(f64, f64)>, Vec<(f64, f64)>, Vec<(f64, f64)>) {
    let mut middle = Vec::new();
    let mut upper = Vec::new();
    let mut lower = Vec::new();

    for i in period..data.len() {
        let window = &data[i-period+1..=i];
        let sum: f64 = window.iter().map(|(_, y)| y).sum();
        let avg = sum / period as f64;

        let variance: f64 = window.iter().map(|(_, y)| (y - avg).powi(2)).sum() / period as f64;
        let std = variance.sqrt();

        let x = data[i].0;
        middle.push((x, avg));
        upper.push((x, avg + std_dev * std));
        lower.push((x, avg - std_dev * std));
    }

    (middle, upper, lower)
}

/// 生成RSI数据
fn generate_rsi_data() -> (Vec<(f64, f64)>, Vec<(f64, f64)>) {
    let mut price_data = Vec::new();
    let mut price = 100.0;

    // 生成价格数据
    for i in 0..100 {
        let change = (rand_f64() - 0.5) * 0.06 * price;
        price += change;
        price_data.push((i as f64, price));
    }

    // 计算RSI
    let rsi_data = calculate_rsi(&price_data, 14);

    (price_data, rsi_data)
}

/// 计算RSI
fn calculate_rsi(data: &[(f64, f64)], period: usize) -> Vec<(f64, f64)> {
    let mut rsi_data = Vec::new();

    if data.len() < period + 1 {
        return rsi_data;
    }

    for i in period..data.len() {
        let mut gains = 0.0;
        let mut losses = 0.0;

        for j in i-period+1..=i {
            let change = data[j].1 - data[j-1].1;
            if change > 0.0 {
                gains += change;
            } else {
                losses += -change;
            }
        }

        let avg_gain = gains / period as f64;
        let avg_loss = losses / period as f64;

        let rs = if avg_loss != 0.0 { avg_gain / avg_loss } else { 100.0 };
        let rsi = 100.0 - (100.0 / (1.0 + rs));

        rsi_data.push((data[i].0, rsi));
    }

    rsi_data
}

/// 生成MACD数据
fn generate_macd_data() -> (Vec<(f64, f64)>, Vec<TechnicalIndicator>) {
    let mut price_data = Vec::new();
    let mut price = 100.0;

    // 生成价格数据
    for i in 0..100 {
        let change = (rand_f64() - 0.5) * 0.04 * price;
        price += change;
        price_data.push((i as f64, price));
    }

    // 计算MACD
    let (macd_line, signal_line, histogram) = calculate_macd(&price_data, 12, 26, 9);

    let indicators = vec![
        TechnicalIndicator {
            name: "MACD".to_string(),
            values: macd_line,
            color: "2196f3".to_string(),
            line_type: LineType::Solid,
        },
        TechnicalIndicator {
            name: "信号线".to_string(),
            values: signal_line,
            color: "ff9800".to_string(),
            line_type: LineType::Solid,
        },
        TechnicalIndicator {
            name: "柱状图".to_string(),
            values: histogram,
            color: "4caf50".to_string(),
            line_type: LineType::Solid,
        },
    ];

    (price_data, indicators)
}

/// 计算MACD
fn calculate_macd(data: &[(f64, f64)], fast: usize, slow: usize, signal: usize) -> (Vec<(f64, f64)>, Vec<(f64, f64)>, Vec<(f64, f64)>) {
    let ema_fast = calculate_ema(data, fast);
    let ema_slow = calculate_ema(data, slow);

    let mut macd_line = Vec::new();
    let start_idx = slow.max(fast);

    for i in start_idx..data.len() {
        if i < ema_fast.len() && i < ema_slow.len() {
            let macd_value = ema_fast[i].1 - ema_slow[i].1;
            macd_line.push((data[i].0, macd_value));
        }
    }

    let signal_line = calculate_ema(&macd_line, signal);

    let mut histogram = Vec::new();
    for i in 0..macd_line.len().min(signal_line.len()) {
        let hist_value = macd_line[i].1 - signal_line[i].1;
        histogram.push((macd_line[i].0, hist_value));
    }

    (macd_line, signal_line, histogram)
}

/// 计算指数移动平均线
fn calculate_ema(data: &[(f64, f64)], period: usize) -> Vec<(f64, f64)> {
    let mut ema_data = Vec::new();

    if data.is_empty() || period == 0 {
        return ema_data;
    }

    let multiplier = 2.0 / (period as f64 + 1.0);
    let mut ema = data[0].1;

    ema_data.push((data[0].0, ema));

    for i in 1..data.len() {
        ema = (data[i].1 * multiplier) + (ema * (1.0 - multiplier));
        ema_data.push((data[i].0, ema));
    }

    ema_data
}

/// 生成实验数据
fn generate_experiment_data() -> Vec<ErrorBarPoint> {
    let mut data = Vec::new();

    for i in 0..20 {
        let x = i as f64;
        let y = 10.0 + x * 2.5 + (rand_f64() - 0.5) * 5.0;
        let error = 1.0 + rand_f64() * 2.0;

        data.push(ErrorBarPoint {
            x,
            y,
            error_low: error,
            error_high: error,
        });
    }

    data
}

/// 生成回归数据
fn generate_regression_data() -> (Vec<(f64, f64)>, Vec<(f64, f64)>) {
    let mut data = Vec::new();
    let mut regression_line = Vec::new();

    // 生成带噪声的线性数据
    for i in 0..50 {
        let x = i as f64;
        let y_true = 5.0 + x * 1.5;
        let y_observed = y_true + (rand_f64() - 0.5) * 10.0;

        data.push((x, y_observed));
        regression_line.push((x, y_true));
    }

    (data, regression_line)
}

/// 生成误差条数据
fn generate_error_bar_data() -> Vec<ErrorBarPoint> {
    let mut data = Vec::new();

    for i in 0..15 {
        let x = i as f64;
        let y = 20.0 + (x * 0.5).sin() * 15.0 + rand_f64() * 5.0;
        let error_low = 1.0 + rand_f64() * 3.0;
        let error_high = 1.0 + rand_f64() * 3.0;

        data.push(ErrorBarPoint {
            x,
            y,
            error_low,
            error_high,
        });
    }

    data
}

/// 生成相关性数据
fn generate_correlation_data() -> HashMap<String, Vec<(f64, f64)>> {
    let mut data = HashMap::new();

    let variables = vec!["温度", "湿度", "压力", "风速"];

    for var in variables {
        let mut series = Vec::new();
        let mut value = 50.0;

        for i in 0..100 {
            let change = (rand_f64() - 0.5) * 10.0;
            value += change;
            value = value.max(0.0).min(100.0);
            series.push((i as f64, value));
        }

        data.insert(var.to_string(), series);
    }

    data
}

/// 生成KPI数据
fn generate_kpi_data() -> Vec<KpiData> {
    vec![
        KpiData {
            name: "月度收入".to_string(),
            current_value: 1250000.0,
            target_value: 1200000.0,
            trend: (0..30).map(|i| (i as f64, 1000000.0 + i as f64 * 8000.0 + (rand_f64() - 0.5) * 50000.0)).collect(),
            unit: "元".to_string(),
            format: "currency".to_string(),
        },
        KpiData {
            name: "用户增长".to_string(),
            current_value: 15420.0,
            target_value: 15000.0,
            trend: (0..30).map(|i| (i as f64, 10000.0 + i as f64 * 180.0 + (rand_f64() - 0.5) * 500.0)).collect(),
            unit: "人".to_string(),
            format: "number".to_string(),
        },
        KpiData {
            name: "转化率".to_string(),
            current_value: 3.45,
            target_value: 3.50,
            trend: (0..30).map(|i| (i as f64, 3.0 + (i as f64 * 0.01) + (rand_f64() - 0.5) * 0.2)).collect(),
            unit: "%".to_string(),
            format: "percentage".to_string(),
        },
        KpiData {
            name: "客户满意度".to_string(),
            current_value: 4.2,
            target_value: 4.0,
            trend: (0..30).map(|i| (i as f64, 3.8 + (i as f64 * 0.01) + (rand_f64() - 0.5) * 0.3)).collect(),
            unit: "分".to_string(),
            format: "rating".to_string(),
        },
    ]
}

/// 生成漏斗数据
fn generate_funnel_data() -> Vec<(String, f64)> {
    vec![
        ("访问量".to_string(), 100000.0),
        ("注册用户".to_string(), 25000.0),
        ("活跃用户".to_string(), 15000.0),
        ("付费用户".to_string(), 3000.0),
        ("续费用户".to_string(), 2100.0),
    ]
}

/// 生成用户行为数据
fn generate_user_behavior_data() -> HashMap<String, Vec<(f64, f64)>> {
    let mut data = HashMap::new();

    // 页面浏览量
    let page_views: Vec<(f64, f64)> = (0..24).map(|i| {
        let hour = i as f64;
        let base = 1000.0;
        let pattern = (hour * std::f64::consts::PI / 12.0).sin() * 500.0 + 500.0;
        (hour, base + pattern + (rand_f64() - 0.5) * 200.0)
    }).collect();

    // 用户会话
    let sessions: Vec<(f64, f64)> = (0..24).map(|i| {
        let hour = i as f64;
        let base = 200.0;
        let pattern = (hour * std::f64::consts::PI / 12.0).sin() * 100.0 + 100.0;
        (hour, base + pattern + (rand_f64() - 0.5) * 50.0)
    }).collect();

    // 跳出率
    let bounce_rate: Vec<(f64, f64)> = (0..24).map(|i| {
        let hour = i as f64;
        let base = 45.0;
        let pattern = (hour * std::f64::consts::PI / 8.0).cos() * 10.0;
        (hour, base + pattern + (rand_f64() - 0.5) * 5.0)
    }).collect();

    data.insert("页面浏览量".to_string(), page_views);
    data.insert("用户会话".to_string(), sessions);
    data.insert("跳出率".to_string(), bounce_rate);

    data
}

/// 生成收入预测数据
fn generate_revenue_forecast_data() -> (Vec<(f64, f64)>, Vec<(f64, f64)>, Vec<(f64, f64)>) {
    let mut historical = Vec::new();
    let mut forecast = Vec::new();
    let mut confidence_interval = Vec::new();

    // 历史数据
    let mut revenue = 1000000.0;
    for i in 0..24 {
        let growth = 1.0 + (rand_f64() - 0.3) * 0.1;
        revenue *= growth;
        historical.push((i as f64, revenue));
    }

    // 预测数据
    let last_revenue = revenue;
    for i in 24..36 {
        let growth = 1.05 + (rand_f64() - 0.5) * 0.02;
        revenue *= growth;
        forecast.push((i as f64, revenue));

        // 置信区间
        let confidence = revenue * 0.1;
        confidence_interval.push((i as f64, confidence));
    }

    (historical, forecast, confidence_interval)
}

/// 生成性能监控数据
fn generate_performance_data() -> HashMap<String, Vec<(f64, f64)>> {
    let mut data = HashMap::new();

    // CPU使用率
    let cpu_usage: Vec<(f64, f64)> = (0..120).map(|i| {
        let time = i as f64;
        let base = 30.0;
        let spike = if i % 20 == 0 { 40.0 } else { 0.0 };
        let noise = (rand_f64() - 0.5) * 10.0;
        (time, (base + spike + noise).max(0.0).min(100.0))
    }).collect();

    // 内存使用率
    let memory_usage: Vec<(f64, f64)> = (0..120).map(|i| {
        let time = i as f64;
        let base = 45.0 + (time * 0.1).sin() * 15.0;
        let noise = (rand_f64() - 0.5) * 5.0;
        (time, (base + noise).max(0.0).min(100.0))
    }).collect();

    // 网络I/O
    let network_io: Vec<(f64, f64)> = (0..120).map(|i| {
        let time = i as f64;
        let base = 20.0;
        let burst = if i % 15 == 0 { 60.0 } else { 0.0 };
        let noise = (rand_f64() - 0.5) * 8.0;
        (time, (base + burst + noise).max(0.0))
    }).collect();

    data.insert("CPU使用率".to_string(), cpu_usage);
    data.insert("内存使用率".to_string(), memory_usage);
    data.insert("网络I/O".to_string(), network_io);

    data
}

/// 生成流量监控数据
fn generate_traffic_data() -> HashMap<String, Vec<(f64, f64)>> {
    let mut data = HashMap::new();

    // 入站流量
    let inbound: Vec<(f64, f64)> = (0..100).map(|i| {
        let time = i as f64;
        let base = 100.0;
        let pattern = (time * 0.2).sin() * 50.0 + 50.0;
        (time, base + pattern + (rand_f64() - 0.5) * 20.0)
    }).collect();

    // 出站流量
    let outbound: Vec<(f64, f64)> = (0..100).map(|i| {
        let time = i as f64;
        let base = 80.0;
        let pattern = (time * 0.15).cos() * 40.0 + 40.0;
        (time, base + pattern + (rand_f64() - 0.5) * 15.0)
    }).collect();

    data.insert("入站流量".to_string(), inbound);
    data.insert("出站流量".to_string(), outbound);

    data
}

/// 生成服务器数据
fn generate_server_data() -> HashMap<String, HashMap<String, Vec<(f64, f64)>>> {
    let mut data = HashMap::new();

    let servers = vec!["Web-01", "Web-02", "DB-01", "Cache-01"];

    for server in servers {
        let mut server_metrics = HashMap::new();

        // CPU
        let cpu: Vec<(f64, f64)> = (0..60).map(|i| {
            let time = i as f64;
            let base = 25.0 + rand_f64() * 20.0;
            let load = (time * 0.1).sin() * 15.0;
            (time, (base + load).max(0.0).min(100.0))
        }).collect();

        // 内存
        let memory: Vec<(f64, f64)> = (0..60).map(|i| {
            let time = i as f64;
            let base = 40.0 + rand_f64() * 15.0;
            let trend = time * 0.2;
            (time, (base + trend).max(0.0).min(100.0))
        }).collect();

        server_metrics.insert("CPU".to_string(), cpu);
        server_metrics.insert("内存".to_string(), memory);

        data.insert(server.to_string(), server_metrics);
    }

    data
}

/// 生成实时流数据
fn generate_stream_data() -> Vec<(f64, f64)> {
    (0..200).map(|i| {
        let time = i as f64;
        let base = 50.0;
        let wave1 = (time * 0.1).sin() * 20.0;
        let wave2 = (time * 0.05).cos() * 10.0;
        let noise = (rand_f64() - 0.5) * 5.0;
        (time, base + wave1 + wave2 + noise)
    }).collect()
}

/// 生成大数据集
fn generate_large_dataset() -> Vec<(f64, f64)> {
    (0..1000).map(|i| {
        let x = i as f64;
        let y = 100.0 + (x * 0.01).sin() * 50.0 + (x * 0.005).cos() * 30.0 + (rand_f64() - 0.5) * 10.0;
        (x, y)
    }).collect()
}

/// 生成提示数据
fn generate_tooltip_data() -> Vec<(f64, f64, String)> {
    (0..50).map(|i| {
        let x = i as f64;
        let y = 50.0 + (x * 0.2).sin() * 30.0 + (rand_f64() - 0.5) * 10.0;
        let tooltip = format!("时间: {:.1}h\n数值: {:.2}\n状态: {}", x, y, if y > 50.0 { "正常" } else { "警告" });
        (x, y, tooltip)
    }).collect()
}

/// 生成可选择数据
fn generate_selectable_data() -> HashMap<String, Vec<(f64, f64)>> {
    let mut data = HashMap::new();

    let series = vec!["系列A", "系列B", "系列C", "系列D"];

    for (idx, name) in series.iter().enumerate() {
        let series_data: Vec<(f64, f64)> = (0..50).map(|i| {
            let x = i as f64;
            let phase = idx as f64 * std::f64::consts::PI / 2.0;
            let y = 50.0 + (x * 0.2 + phase).sin() * 25.0 + (rand_f64() - 0.5) * 8.0;
            (x, y)
        }).collect();

        data.insert(name.to_string(), series_data);
    }

    data
}

/// 生成动态数据
fn generate_dynamic_data() -> Vec<(f64, f64)> {
    (0..100).map(|i| {
        let x = i as f64;
        let y = 50.0 + (x * 0.1).sin() * 30.0 + (x * 0.05).cos() * 15.0;
        (x, y)
    }).collect()
}

/// 生成样本数据
fn generate_sample_data() -> Vec<(f64, f64)> {
    (0..50).map(|i| {
        let x = i as f64;
        let y = 30.0 + (x * 0.3).sin() * 20.0 + (rand_f64() - 0.5) * 5.0;
        (x, y)
    }).collect()
}

/// 生成时间序列数据
fn generate_time_series_data() -> (Vec<(f64, f64)>, Vec<(f64, f64)>, Vec<(f64, f64)>, Vec<(f64, f64)>) {
    let mut original = Vec::new();
    let mut trend = Vec::new();
    let mut seasonal = Vec::new();
    let mut residual = Vec::new();

    for i in 0..365 {
        let t = i as f64;

        // 趋势分量
        let trend_value = 100.0 + t * 0.1;

        // 季节性分量
        let seasonal_value = 20.0 * (t * 2.0 * std::f64::consts::PI / 365.0).sin();

        // 残差分量
        let residual_value = (rand_f64() - 0.5) * 10.0;

        // 原始数据
        let original_value = trend_value + seasonal_value + residual_value;

        original.push((t, original_value));
        trend.push((t, trend_value));
        seasonal.push((t, seasonal_value));
        residual.push((t, residual_value));
    }

    (original, trend, seasonal, residual)
}

/// 生成异常数据
fn generate_anomaly_data() -> (Vec<(f64, f64)>, Vec<(f64, f64)>) {
    let mut normal_data = Vec::new();
    let mut anomalies = Vec::new();

    for i in 0..200 {
        let x = i as f64;
        let base = 50.0 + (x * 0.1).sin() * 20.0;

        // 添加异常点
        if i == 50 || i == 120 || i == 180 {
            let anomaly_value = base + (rand_f64() - 0.5) * 80.0;
            anomalies.push((x, anomaly_value));
            normal_data.push((x, anomaly_value));
        } else {
            let normal_value = base + (rand_f64() - 0.5) * 5.0;
            normal_data.push((x, normal_value));
        }
    }

    (normal_data, anomalies)
}

/// 生成预测数据
fn generate_prediction_data() -> (Vec<(f64, f64)>, Vec<(f64, f64)>, Vec<(f64, f64)>, Vec<(f64, f64)>) {
    let mut historical = Vec::new();
    let mut prediction = Vec::new();
    let mut upper_bound = Vec::new();
    let mut lower_bound = Vec::new();

    // 历史数据
    for i in 0..50 {
        let x = i as f64;
        let y = 100.0 + (x * 0.2).sin() * 30.0 + (rand_f64() - 0.5) * 10.0;
        historical.push((x, y));
    }

    // 预测数据
    let last_value = historical.last().unwrap().1;
    for i in 50..80 {
        let x = i as f64;
        let pred_y = last_value + (x - 50.0) * 0.5 + ((x - 50.0) * 0.2).sin() * 20.0;
        let confidence = 5.0 + (x - 50.0) * 0.5;

        prediction.push((x, pred_y));
        upper_bound.push((x, pred_y + confidence));
        lower_bound.push((x, pred_y - confidence));
    }

    (historical, prediction, upper_bound, lower_bound)
}

/// 生成多维数据
fn generate_multidimensional_data() -> HashMap<String, HashMap<String, Vec<(f64, f64)>>> {
    let mut data = HashMap::new();

    let categories = vec!["销售", "市场", "技术", "运营"];
    let metrics = vec!["效率", "质量", "成本", "满意度"];

    for category in categories {
        let mut category_data = HashMap::new();

        for metric in &metrics {
            let series: Vec<(f64, f64)> = (0..12).map(|i| {
                let x = i as f64;
                let base = 50.0 + rand_f64() * 30.0;
                let trend = x * (rand_f64() - 0.5) * 2.0;
                let seasonal = (x * std::f64::consts::PI / 6.0).sin() * 10.0;
                (x, base + trend + seasonal)
            }).collect();

            category_data.insert(metric.to_string(), series);
        }

        data.insert(category.to_string(), category_data);
    }

    data
}

// ============================================================================
// 主题配置
// ============================================================================

/// 获取深色主题
fn get_dark_theme() -> ChartTheme {
    ChartTheme {
        name: "深色主题".to_string(),
        background: "1a1a1a".to_string(),
        grid_color: "333333".to_string(),
        text_color: "ffffff".to_string(),
        axis_color: "666666".to_string(),
        primary_colors: vec![
            "00d4ff".to_string(),
            "ff6b6b".to_string(),
            "4ecdc4".to_string(),
            "45b7d1".to_string(),
            "f9ca24".to_string(),
            "6c5ce7".to_string(),
        ],
        font_family: "Arial, sans-serif".to_string(),
        font_size: 12,
    }
}

/// 获取商务主题
fn get_business_theme() -> ChartTheme {
    ChartTheme {
        name: "商务主题".to_string(),
        background: "f8f9fa".to_string(),
        grid_color: "e9ecef".to_string(),
        text_color: "212529".to_string(),
        axis_color: "6c757d".to_string(),
        primary_colors: vec![
            "007bff".to_string(),
            "28a745".to_string(),
            "dc3545".to_string(),
            "ffc107".to_string(),
            "17a2b8".to_string(),
            "6f42c1".to_string(),
        ],
        font_family: "Segoe UI, Tahoma, Geneva, Verdana, sans-serif".to_string(),
        font_size: 12,
    }
}

/// 获取科技主题
fn get_tech_theme() -> ChartTheme {
    ChartTheme {
        name: "科技主题".to_string(),
        background: "0a0e27".to_string(),
        grid_color: "1e2a5a".to_string(),
        text_color: "00ffff".to_string(),
        axis_color: "4a90e2".to_string(),
        primary_colors: vec![
            "00ffff".to_string(),
            "ff00ff".to_string(),
            "00ff00".to_string(),
            "ffff00".to_string(),
            "ff8c00".to_string(),
            "9370db".to_string(),
        ],
        font_family: "Courier New, monospace".to_string(),
        font_size: 11,
    }
}

/// 获取自然主题
fn get_nature_theme() -> ChartTheme {
    ChartTheme {
        name: "自然主题".to_string(),
        background: "f0f8f0".to_string(),
        grid_color: "d4e6d4".to_string(),
        text_color: "2d5a2d".to_string(),
        axis_color: "5a8a5a".to_string(),
        primary_colors: vec![
            "228b22".to_string(),
            "32cd32".to_string(),
            "90ee90".to_string(),
            "8fbc8f".to_string(),
            "9acd32".to_string(),
            "6b8e23".to_string(),
        ],
        font_family: "Georgia, serif".to_string(),
        font_size: 12,
    }
}

// ============================================================================
// 图表生成函数
// ============================================================================

// 引入图表生成器
mod professional_chart_generators;
mod professional_chart_generators_2;
use professional_chart_generators::*;
use professional_chart_generators_2::*;

/// 创建布林带图表
fn create_bollinger_bands_chart(title: &str, data: &(Vec<(f64, f64)>, Vec<TechnicalIndicator>), width: f64, height: f64) -> String {
    create_moving_average_chart(title, data, width, height)
}

/// 创建RSI图表
fn create_rsi_chart(title: &str, data: &(Vec<(f64, f64)>, Vec<(f64, f64)>), width: f64, height: f64) -> String {
    let (price_data, rsi_data) = data;
    let mut svg = String::new();

    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    // 价格图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let price_height = height * 0.6;
    let rsi_height = height * 0.3;
    let gap = 20.0;

    // 绘制价格图表
    if !price_data.is_empty() {
        let min_price = price_data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_price = price_data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        let price_range = max_price - min_price;
        let max_x = price_data.len() as f64;

        // 价格背景
        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, price_height));

        // 价格网格线
        for i in 0..=4 {
            let y = chart_y + (i as f64 / 4.0) * price_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));

            let price = max_price - (i as f64 / 4.0) * price_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.2}</text>\n", chart_x - 10.0, y + 4.0, price));
        }

        // 绘制价格曲线
        let mut price_path = String::from("M");
        for (i, (_, y)) in price_data.iter().enumerate() {
            let px = chart_x + (i as f64 / max_x) * chart_width;
            let py = chart_y + price_height - ((y - min_price) / price_range) * price_height;

            if i == 0 {
                price_path.push_str(&format!(" {} {}", px, py));
            } else {
                price_path.push_str(&format!(" L {} {}", px, py));
            }
        }

        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#2196f3\" stroke-width=\"2\" fill=\"none\"/>\n", price_path));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"14\" font-weight=\"bold\" fill=\"#333\">价格</text>\n", chart_x, chart_y - 10.0));
    }

    // 绘制RSI图表
    if !rsi_data.is_empty() {
        let rsi_y = chart_y + price_height + gap;

        // RSI背景
        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, rsi_y, chart_width, rsi_height));

        // RSI参考线
        let rsi_70 = rsi_y + rsi_height * 0.3;
        let rsi_30 = rsi_y + rsi_height * 0.7;
        let rsi_50 = rsi_y + rsi_height * 0.5;

        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#ff5722\" stroke-width=\"1\" stroke-dasharray=\"3,3\"/>\n", chart_x, rsi_70, chart_x + chart_width, rsi_70));
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#4caf50\" stroke-width=\"1\" stroke-dasharray=\"3,3\"/>\n", chart_x, rsi_30, chart_x + chart_width, rsi_30));
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#999\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n", chart_x, rsi_50, chart_x + chart_width, rsi_50));

        // RSI标签
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"10\" fill=\"#ff5722\">70</text>\n", chart_x - 5.0, rsi_70 + 3.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"10\" fill=\"#999\">50</text>\n", chart_x - 5.0, rsi_50 + 3.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"10\" fill=\"#4caf50\">30</text>\n", chart_x - 5.0, rsi_30 + 3.0));

        // 绘制RSI曲线
        let max_x = rsi_data.len() as f64;
        let mut rsi_path = String::from("M");

        for (i, (_, rsi)) in rsi_data.iter().enumerate() {
            let px = chart_x + (i as f64 / max_x) * chart_width;
            let py = rsi_y + rsi_height - (rsi / 100.0) * rsi_height;

            if i == 0 {
                rsi_path.push_str(&format!(" {} {}", px, py));
            } else {
                rsi_path.push_str(&format!(" L {} {}", px, py));
            }
        }

        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#9c27b0\" stroke-width=\"2\" fill=\"none\"/>\n", rsi_path));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"14\" font-weight=\"bold\" fill=\"#333\">RSI</text>\n", chart_x, rsi_y - 10.0));
    }

    svg.push_str("</svg>");
    svg
}

/// 创建MACD图表
fn create_macd_chart(title: &str, data: &(Vec<(f64, f64)>, Vec<TechnicalIndicator>), width: f64, height: f64) -> String {
    let (price_data, indicators) = data;
    let mut svg = String::new();

    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 200.0;
    let price_height = height * 0.6;
    let macd_height = height * 0.3;
    let gap = 20.0;

    // 绘制价格图表
    if !price_data.is_empty() {
        let min_price = price_data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_price = price_data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        let price_range = max_price - min_price;
        let max_x = price_data.len() as f64;

        // 价格背景
        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, price_height));

        // 绘制价格曲线
        let mut price_path = String::from("M");
        for (i, (_, y)) in price_data.iter().enumerate() {
            let px = chart_x + (i as f64 / max_x) * chart_width;
            let py = chart_y + price_height - ((y - min_price) / price_range) * price_height;

            if i == 0 {
                price_path.push_str(&format!(" {} {}", px, py));
            } else {
                price_path.push_str(&format!(" L {} {}", px, py));
            }
        }

        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\"/>\n", price_path));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"14\" font-weight=\"bold\" fill=\"#333\">价格</text>\n", chart_x, chart_y - 10.0));
    }

    // 绘制MACD图表
    if indicators.len() >= 3 {
        let macd_y = chart_y + price_height + gap;

        // MACD背景
        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, macd_y, chart_width, macd_height));

        // 零轴线
        let zero_line = macd_y + macd_height / 2.0;
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#999\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n", chart_x, zero_line, chart_x + chart_width, zero_line));

        // 计算MACD值范围
        let mut all_macd_values = Vec::new();
        for indicator in indicators {
            all_macd_values.extend(indicator.values.iter().map(|(_, y)| *y));
        }

        let min_macd = all_macd_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_macd = all_macd_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let macd_range = (max_macd - min_macd).max(0.001);

        // 绘制MACD指标
        for (idx, indicator) in indicators.iter().enumerate() {
            if !indicator.values.is_empty() {
                if indicator.name == "柱状图" {
                    // 绘制柱状图
                    let bar_width = chart_width / indicator.values.len() as f64 * 0.6;

                    for (i, (_, value)) in indicator.values.iter().enumerate() {
                        let px = chart_x + (i as f64 + 0.5) * (chart_width / indicator.values.len() as f64);
                        let bar_height = (value.abs() / macd_range) * macd_height * 0.8;
                        let py = if *value >= 0.0 {
                            zero_line - bar_height
                        } else {
                            zero_line
                        };

                        let color = if *value >= 0.0 { "#4caf50" } else { "#f44336" };

                        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"{}\" opacity=\"0.7\"/>\n",
                            px - bar_width / 2.0, py, bar_width, bar_height, color));
                    }
                } else {
                    // 绘制线条
                    let mut path = String::from("M");

                    for (i, (_, value)) in indicator.values.iter().enumerate() {
                        let px = chart_x + (i as f64 / indicator.values.len() as f64) * chart_width;
                        let py = macd_y + macd_height - ((value - min_macd) / macd_range) * macd_height;

                        if i == 0 {
                            path.push_str(&format!(" {} {}", px, py));
                        } else {
                            path.push_str(&format!(" L {} {}", px, py));
                        }
                    }

                    svg.push_str("  <path d=\"");
                    svg.push_str(&path);
                    svg.push_str("\" stroke=\"#");
                    svg.push_str(&indicator.color);
                    svg.push_str("\" stroke-width=\"2\" fill=\"none\"/>\n");
                }

                // 图例
                let legend_y = chart_y + 30.0 + idx as f64 * 20.0;
                svg.push_str("  <line x1=\"");
                svg.push_str(&(chart_x + chart_width + 20.0).to_string());
                svg.push_str("\" y1=\"");
                svg.push_str(&legend_y.to_string());
                svg.push_str("\" x2=\"");
                svg.push_str(&(chart_x + chart_width + 40.0).to_string());
                svg.push_str("\" y2=\"");
                svg.push_str(&legend_y.to_string());
                svg.push_str("\" stroke=\"#");
                svg.push_str(&indicator.color);
                svg.push_str("\" stroke-width=\"2\"/>\n");
                svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">{}</text>\n", chart_x + chart_width + 45.0, legend_y + 4.0, indicator.name));
            }
        }

        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"14\" font-weight=\"bold\" fill=\"#333\">MACD</text>\n", chart_x, macd_y - 10.0));
    }

    svg.push_str("</svg>");
    svg
}

/// 创建销售漏斗图表
fn create_sales_funnel_chart(title: &str, data: &[(String, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();

    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 定义渐变
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"funnelGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#2196f3;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#1976d2;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    if !data.is_empty() {
        let chart_x = 100.0;
        let chart_y = 60.0;
        let chart_width = width - 200.0;
        let chart_height = height - 120.0;

        let max_value = data.iter().map(|(_, v)| *v).fold(f64::NEG_INFINITY, f64::max);
        let stage_height = chart_height / data.len() as f64;

        for (idx, (stage, value)) in data.iter().enumerate() {
            let y = chart_y + idx as f64 * stage_height;
            let stage_width = (value / max_value) * chart_width;
            let x_offset = (chart_width - stage_width) / 2.0;

            // 漏斗段
            svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"url(#funnelGradient)\" stroke=\"white\" stroke-width=\"2\"/>\n",
                chart_x + x_offset, y, stage_width, stage_height - 5.0));

            // 标签
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"14\" font-weight=\"bold\" fill=\"white\">{}</text>\n",
                chart_x + chart_width / 2.0, y + stage_height / 2.0 - 5.0, stage));

            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"white\">{:.0}</text>\n",
                chart_x + chart_width / 2.0, y + stage_height / 2.0 + 10.0, value));

            // 转化率
            if idx > 0 {
                let prev_value = data[idx - 1].1;
                let conversion_rate = (value / prev_value) * 100.0;
                svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"start\" font-size=\"12\" fill=\"#666\">{:.1}%</text>\n",
                    chart_x + chart_width + 20.0, y + stage_height / 2.0, conversion_rate));
            }
        }
    }

    svg.push_str("</svg>");
    svg
}

/// 创建用户行为图表
fn create_user_behavior_chart(title: &str, data: &HashMap<String, Vec<(f64, f64)>>, width: f64, height: f64) -> String {
    create_correlation_chart(title, data, width, height)
}

/// 创建收入预测图表
fn create_revenue_forecast_chart(title: &str, data: &(Vec<(f64, f64)>, Vec<(f64, f64)>, Vec<(f64, f64)>), width: f64, height: f64) -> String {
    let (historical, forecast, confidence_interval) = data;
    let mut svg = String::new();

    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 定义渐变和滤镜
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"confidenceGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#2196f3;stop-opacity:0.3\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#2196f3;stop-opacity:0.1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 200.0;
    let chart_height = height - 120.0;

    // 绘制背景
    svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, chart_height));

    if !historical.is_empty() && !forecast.is_empty() {
        // 计算数据范围
        let mut all_values: Vec<f64> = historical.iter().map(|(_, y)| *y).collect();
        all_values.extend(forecast.iter().map(|(_, y)| *y));

        let min_y = all_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_y = all_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let y_range = max_y - min_y;

        let max_x = forecast.last().unwrap().0;
        let split_x = historical.last().unwrap().0;

        // 绘制网格线
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));

            let value = max_y - (i as f64 / 5.0) * y_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}万</text>\n", chart_x - 10.0, y + 4.0, value / 10000.0));
        }

        // 分割线
        let split_px = chart_x + (split_x / max_x) * chart_width;
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#ff9800\" stroke-width=\"2\" stroke-dasharray=\"5,5\"/>\n", split_px, chart_y, split_px, chart_y + chart_height));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#ff9800\">预测开始</text>\n", split_px, chart_y - 10.0));

        // 绘制置信区间
        if !confidence_interval.is_empty() {
            let mut upper_path = String::new();
            let mut lower_path = String::new();

            for (i, (x, confidence)) in confidence_interval.iter().enumerate() {
                let px = chart_x + (x / max_x) * chart_width;
                let forecast_y = forecast[i].1;
                let py_upper = chart_y + chart_height - ((forecast_y + confidence - min_y) / y_range) * chart_height;
                let py_lower = chart_y + chart_height - ((forecast_y - confidence - min_y) / y_range) * chart_height;

                if i == 0 {
                    upper_path.push_str(&format!("M {} {}", px, py_upper));
                    lower_path.push_str(&format!("M {} {}", px, py_lower));
                } else {
                    upper_path.push_str(&format!(" L {} {}", px, py_upper));
                    lower_path.push_str(&format!(" L {} {}", px, py_lower));
                }
            }

            // 创建闭合路径
            let mut area_path = upper_path.clone();
            for (x, confidence) in confidence_interval.iter().rev() {
                let px = chart_x + (x / max_x) * chart_width;
                let forecast_y = forecast[confidence_interval.len() - 1 - (confidence_interval.len() - 1)].1;
                let py_lower = chart_y + chart_height - ((forecast_y - confidence - min_y) / y_range) * chart_height;
                area_path.push_str(&format!(" L {} {}", px, py_lower));
            }
            area_path.push_str(" Z");

            svg.push_str(&format!("  <path d=\"{}\" fill=\"url(#confidenceGradient)\"/>\n", area_path));
        }

        // 绘制历史数据
        let mut historical_path = String::from("M");
        for (i, (x, y)) in historical.iter().enumerate() {
            let px = chart_x + (x / max_x) * chart_width;
            let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;

            if i == 0 {
                historical_path.push_str(&format!(" {} {}", px, py));
            } else {
                historical_path.push_str(&format!(" L {} {}", px, py));
            }
        }

        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#333\" stroke-width=\"3\" fill=\"none\"/>\n", historical_path));

        // 绘制预测数据
        let mut forecast_path = String::from("M");
        for (i, (x, y)) in forecast.iter().enumerate() {
            let px = chart_x + (x / max_x) * chart_width;
            let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;

            if i == 0 {
                forecast_path.push_str(&format!(" {} {}", px, py));
            } else {
                forecast_path.push_str(&format!(" L {} {}", px, py));
            }
        }

        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#2196f3\" stroke-width=\"3\" stroke-dasharray=\"8,4\" fill=\"none\"/>\n", forecast_path));

        // 图例
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#333\" stroke-width=\"3\"/>\n", chart_x + chart_width + 20.0, chart_y + 20.0, chart_x + chart_width + 40.0, chart_y + 20.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">历史数据</text>\n", chart_x + chart_width + 45.0, chart_y + 25.0));

        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#2196f3\" stroke-width=\"3\" stroke-dasharray=\"8,4\"/>\n", chart_x + chart_width + 20.0, chart_y + 40.0, chart_x + chart_width + 40.0, chart_y + 40.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">预测数据</text>\n", chart_x + chart_width + 45.0, chart_y + 45.0));

        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"20\" height=\"10\" fill=\"url(#confidenceGradient)\"/>\n", chart_x + chart_width + 20.0, chart_y + 55.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">置信区间</text>\n", chart_x + chart_width + 45.0, chart_y + 65.0));
    }

    svg.push_str("</svg>");
    svg
}
