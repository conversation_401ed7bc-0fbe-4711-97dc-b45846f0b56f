pub mod config;
pub mod error;
pub mod manager;
pub mod types;

pub use config::{DataCollectionConfig, DeviceConfig, SerialConfig};
pub use error::{DeviceError, DeviceResult};
pub use manager::DeviceManager;
pub use types::{<PERSON><PERSON><PERSON>and<PERSON>, DeviceStatus};

use crate::{AppState, SvgName};
use anyhow::Context as _;
use gpui::{
    prelude::*, AppContext, BackgroundExecutor, Context, IntoElement, Render, Task, Timer,
    WeakEntity, Window,
};
use gpui_component::{
    button::{Button, ButtonVariants},
    h_flex, Disableable, IconName, Sizable,
};
use std::future::Future;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use tracing::{debug, error, info, warn};

/// 设备管理操作的超时时间（秒）
const DEVICE_OPERATION_TIMEOUT: u64 = 1;

/// 防双击时间窗口（毫秒）
const DOUBLE_CLICK_PREVENTION_MS: u64 = 1000;

/// 数据采集组件，提供设备连接和数据采集功能的UI界面
pub struct DataCollection {
    /// 连接按钮是否处于加载状态
    connect_loading: bool,
    /// 采集按钮是否处于加载状态
    run_loading: bool,
    /// 设备管理器
    manager: Arc<Mutex<DeviceManager>>,
    /// 当前连接的设备数量
    device_count: Option<usize>,
    /// 设备是否正在采集数据
    is_running: Option<bool>,
    /// 状态更新任务
    _status_update_task: Option<Task<()>>,
    /// 最后采集按钮点击时间戳（防双击）
    last_run_click_time: Option<Instant>,
    /// 最后连接按钮点击时间戳（防双击）
    last_connect_click_time: Option<Instant>,
    /// 防双击期间采集按钮加载状态
    run_double_click_loading: bool,
    /// 防双击期间连接按钮加载状态
    connect_double_click_loading: bool,
}

impl DataCollection {
    /// 创建新的数据采集组件实例
    pub fn new(window: &mut Window, cx: &mut Context<Self>) -> Self {
        debug!("创建DataCollection实例");
        let manager = Arc::new(Mutex::new(DeviceManager::new(Arc::new(
            cx.background_executor().clone(),
        ))));

        // 创建组件
        let mut component = Self {
            connect_loading: false,
            run_loading: false,
            manager,
            device_count: None,
            is_running: None,
            _status_update_task: None,
            last_run_click_time: None,
            last_connect_click_time: None,
            run_double_click_loading: false,
            connect_double_click_loading: false,
        };

        // 启动定期状态更新任务
        component.start_status_update_task(window, cx);

        component
    }

    /// 启动定期状态更新任务
    fn start_status_update_task(&mut self, window: &mut Window, cx: &mut Context<Self>) {
        let manager = self.manager.clone();

        // 定期查询设备状态（每2秒）
        self._status_update_task = Some(cx.spawn_in(window, async move |this, window| {
            loop {
                // 克隆manager以便在内部闭包中使用
                let manager_clone = Arc::clone(&manager);

                // 初始化默认值
                let mut device_count = None;
                let mut is_running = None;

                // 执行异步查询任务
                let query_task: Task<Result<(Option<usize>, Option<bool>), _>> =
                    window.background_executor().spawn(async move {
                        // 尝试获取设备信息
                        if let Ok(manager) = manager_clone.try_lock() {
                            // 获取设备数量
                            let devices = manager.get_devices().await;
                            let is_running = manager.is_any_device_running().await;
                            Ok::<(Option<usize>, Option<bool>), anyhow::Error>((
                                Some(devices.len()),
                                Some(is_running),
                            ))
                        } else {
                            Ok::<(Option<usize>, Option<bool>), anyhow::Error>((None, None))
                        }
                    });

                // 等待任务完成，处理两层结果
                match query_task.await {
                    Ok(result) => {
                        device_count = result.0; // 第一个元素是设备数量
                        is_running = result.1; // 第二个元素是运行状态
                    }
                    Err(err) => {
                        error!("查询设备状态出错: {}", err);
                    }
                }

                // 更新UI状态
                let _ = this.update_in(window, |this, window, cx| {
                    if let Some(count) = device_count {
                        this.device_count = Some(count);
                    }
                    if let Some(running) = is_running {
                        this.is_running = Some(running);
                    }
                    cx.notify();
                });

                // 使用gpui兼容的延迟方式
                Timer::after(Duration::from_secs(2)).await;
            }
        }));
    }

    /// 处理连接/断开按钮点击事件
    fn handle_connect_click(&mut self, cx: &mut Context<Self>) {
        debug!("处理连接按钮点击");

        // 防双击检查
        let now = Instant::now();
        if let Some(last_click) = self.last_connect_click_time {
            let elapsed = now.duration_since(last_click);
            if elapsed.as_millis() < DOUBLE_CLICK_PREVENTION_MS as u128 {
                warn!(
                    "连接按钮点击过于频繁，忽略此次点击 (间隔: {}ms)",
                    elapsed.as_millis()
                );
                // 在防双击期间显示加载状态
                self.connect_double_click_loading = true;
                cx.notify();

                // 启动防双击加载状态定时器
                let double_click_loading = self.connect_double_click_loading;
                cx.spawn(async move |this, cx| {
                    // 等待防双击时间窗口结束
                    Timer::after(Duration::from_millis(DOUBLE_CLICK_PREVENTION_MS)).await;
                    this.update(cx, |this, cx| {
                        this.connect_double_click_loading = false;
                        cx.notify();
                    })
                    .ok();
                })
                .detach();
                return;
            }
        }

        // 更新最后点击时间
        self.last_connect_click_time = Some(now);

        // 获取当前设备数量
        let device_count = self.device_count.unwrap_or(0);
        let is_empty = device_count == 0;

        debug!("当前设备数量: {}, 是否为空: {}", device_count, is_empty);

        // 如果已经在加载中，忽略点击
        if self.connect_loading {
            warn!("连接操作正在进行中，忽略此次点击");
            return;
        }

        // 设置加载状态
        self.connect_loading = true;
        cx.notify();

        // 克隆需要的数据
        let manager = self.manager.clone();
        let bg_exec = Arc::new(cx.background_executor().clone());

        // 创建异步任务
        cx.spawn(async move |this: WeakEntity<DataCollection>, cx| {
            let task = cx.background_executor().spawn(async move {
                if is_empty {
                    Self::connect_devices(&manager, bg_exec.clone()).await
                } else {
                    Self::disconnect_devices(&manager, bg_exec.clone()).await
                }
            });
            let result = task.await;
            this.update(cx, |this, cx| {
                this.connect_loading = false;
                match result {
                    Ok(()) => {
                        info!("连接/断开操作成功");
                        // 更新设备数量
                        this.device_count = Some(if is_empty { 1 } else { 0 });
                    }
                    Err(e) => {
                        error!("连接/断开操作失败: {}", e);
                        // 操作失败时，清除最后点击时间，允许重试
                        this.last_connect_click_time = None;
                    }
                }
                cx.notify();
            })
            .ok();
        })
        .detach();
    }

    /// 连接设备的异步操作
    async fn connect_devices(
        manager: &Arc<Mutex<DeviceManager>>,
        bg_exec: Arc<BackgroundExecutor>,
    ) -> DeviceResult<()> {
        match bg_exec.block_with_timeout(
            Duration::from_secs(DEVICE_OPERATION_TIMEOUT),
            manager.lock(),
        ) {
            Ok(manager) => {
                manager
                    .connect_all_devices()
                    .await
                    .context("连接设备失败")
                    .map_err(|e| DeviceError::ConnectionFailed(e.to_string()))?;
                Ok(())
            }
            Err(_) => Err(DeviceError::TimeoutError(
                "获取设备管理器锁超时".to_string(),
            )),
        }
    }

    /// 断开设备的异步操作
    async fn disconnect_devices(
        manager: &Arc<Mutex<DeviceManager>>,
        bg_exec: Arc<BackgroundExecutor>,
    ) -> DeviceResult<()> {
        match bg_exec.block_with_timeout(
            Duration::from_secs(DEVICE_OPERATION_TIMEOUT),
            manager.lock(),
        ) {
            Ok(manager) => {
                manager
                    .disconnect_all_devices()
                    .await
                    .context("断开设备连接失败")
                    .map_err(|e| DeviceError::DisconnectionFailed(e.to_string()))?;
                Ok(())
            }
            Err(_) => Err(DeviceError::TimeoutError(
                "获取设备管理器锁超时".to_string(),
            )),
        }
    }

    /// 处理开始/停止采集按钮点击事件
    fn handle_run_click(&mut self, cx: &mut Context<Self>) {
        debug!("处理采集按钮点击");

        // 防双击检查
        let now = Instant::now();
        if let Some(last_click) = self.last_run_click_time {
            let elapsed = now.duration_since(last_click);
            if elapsed.as_millis() < DOUBLE_CLICK_PREVENTION_MS as u128 {
                warn!(
                    "采集按钮点击过于频繁，忽略此次点击 (间隔: {}ms)",
                    elapsed.as_millis()
                );
                // 在防双击期间显示加载状态
                self.run_double_click_loading = true;
                cx.notify();

                // 启动防双击加载状态定时器
                cx.spawn(async move |this, cx| {
                    // 等待防双击时间窗口结束
                    Timer::after(Duration::from_millis(DOUBLE_CLICK_PREVENTION_MS)).await;
                    this.update(cx, |this, cx| {
                        this.run_double_click_loading = false;
                        cx.notify();
                    })
                    .ok();
                })
                .detach();
                return;
            }
        }

        // 更新最后点击时间
        self.last_run_click_time = Some(now);

        // 获取当前运行状态
        let is_running = self.is_running.unwrap_or(false);

        debug!("当前运行状态: {}", is_running);

        // 如果已经在加载中，忽略点击
        if self.run_loading {
            warn!("采集操作正在进行中，忽略此次点击");
            return;
        }

        // 设置加载状态
        self.run_loading = true;
        cx.notify();

        // 克隆需要的数据
        let manager = self.manager.clone();
        let bg_exec = Arc::new(cx.background_executor().clone());

        // 创建异步任务
        cx.spawn(async move |this, cx| {
            let result = if !is_running {
                // 开始采集
                Self::start_collection(&manager, bg_exec.clone()).await
            } else {
                // 停止采集
                Self::stop_collection(&manager, bg_exec.clone()).await
            };

            // 更新UI状态
            this.update(cx, |this, cx| {
                // 重置加载状态
                this.run_loading = false;

                // 处理开始/停止采集操作的结果
                match result {
                    Ok(_) => {
                        info!("开始/停止采集操作成功");
                        // 更新运行状态
                        this.is_running = Some(!is_running);
                    }
                    Err(e) => {
                        error!("开始/停止采集操作失败: {}", e);
                        // 操作失败时，清除最后点击时间，允许重试
                        this.last_run_click_time = None;
                    }
                }

                cx.notify();
            })
            .ok();
        })
        .detach();
    }

    /// 开始采集的异步操作
    async fn start_collection(
        manager: &Arc<Mutex<DeviceManager>>,
        bg_exec: std::sync::Arc<BackgroundExecutor>,
    ) -> DeviceResult<()> {
        match bg_exec.block_with_timeout(
            Duration::from_secs(DEVICE_OPERATION_TIMEOUT),
            manager.lock(),
        ) {
            Ok(manager) => {
                manager
                    .start_data_collection()
                    .await
                    .context("开始数据采集失败")
                    .map_err(|e| DeviceError::DataCollectionFailed(e.to_string()))?;
                Ok(())
            }
            Err(_) => Err(DeviceError::TimeoutError(
                "获取设备管理器锁超时".to_string(),
            )),
        }
    }

    /// 停止采集的异步操作
    async fn stop_collection(
        manager: &Arc<Mutex<DeviceManager>>,
        bg_exec: std::sync::Arc<BackgroundExecutor>,
    ) -> DeviceResult<()> {
        debug!("执行停止采集操作");
        match bg_exec.block_with_timeout(
            Duration::from_secs(DEVICE_OPERATION_TIMEOUT),
            manager.lock(),
        ) {
            Ok(manager) => {
                debug!("准备停止所有设备的数据采集");
                manager
                    .stop_data_collection()
                    .await
                    .context("停止数据采集失败")
                    .map_err(|e| DeviceError::DataCollectionFailed(e.to_string()))?;
                Ok(())
            }
            Err(_) => Err(DeviceError::TimeoutError(
                "获取设备管理器锁超时".to_string(),
            )),
        }
    }
}

impl Render for DataCollection {
    fn render(&mut self, _: &mut Window, cx: &mut Context<Self>) -> impl IntoElement {
        // 获取当前设备数量和运行状态
        let device_count = self.device_count.unwrap_or(0);
        let is_running = self.is_running.unwrap_or(false);

        h_flex()
            .items_center()
            .justify_end()
            .child(
                Button::new(if device_count == 0 {
                    "连接全部设备"
                } else {
                    "断开全部设备"
                })
                .small()
                .ghost()
                .loading_icon(IconName::LoaderCircle)
                .icon(if device_count == 0 {
                    SvgName::Disconnect.icon()
                } else {
                    SvgName::Connect.icon()
                })
                // 合并正常加载状态和防双击加载状态
                .loading(self.connect_loading || self.connect_double_click_loading)
                .on_click(cx.listener(move |this, _, _, cx| {
                    debug!("连接按钮被点击");
                    this.handle_connect_click(cx);
                })),
            )
            .child(
                Button::new(if is_running {
                    "停止采集"
                } else {
                    "开始采集"
                })
                .small()
                .ghost()
                .loading_icon(IconName::LoaderCircle)
                .icon(if is_running {
                    SvgName::Stop.icon()
                } else {
                    SvgName::Run.icon()
                })
                // 合并正常加载状态和防双击加载状态
                .loading(self.run_loading || self.run_double_click_loading)
                // 当没有设备连接时禁用采集按钮
                .disabled(device_count == 0)
                .on_click(cx.listener(move |this, _, _, cx| {
                    debug!("采集按钮被点击");
                    this.handle_run_click(cx);
                })),
            )
    }
}
