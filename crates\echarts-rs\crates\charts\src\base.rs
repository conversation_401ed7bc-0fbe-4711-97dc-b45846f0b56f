//! 图表基础架构
//!
//! 提供统一的图表基础类和通用功能，减少重复代码

use echarts_core::{Bounds, DataSet, Series};
use serde::{Deserialize, Serialize};

/// 图表基础配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChartConfig {
    /// 图表名称
    pub name: String,
    
    /// 是否可见
    pub visible: bool,
    
    /// Z轴索引（绘制顺序）
    pub z_index: i32,
    
    /// 动画配置
    pub animation: AnimationConfig,
    
    /// 交互配置
    pub interaction: InteractionConfig,
}

impl Default for ChartConfig {
    fn default() -> Self {
        Self {
            name: "Chart".to_string(),
            visible: true,
            z_index: 0,
            animation: AnimationConfig::default(),
            interaction: InteractionConfig::default(),
        }
    }
}

/// 动画配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnimationConfig {
    /// 是否启用动画
    pub enabled: bool,
    
    /// 动画持续时间（毫秒）
    pub duration: u32,
    
    /// 动画延迟（毫秒）
    pub delay: u32,
    
    /// 动画缓动函数
    pub easing: EasingFunction,
}

impl Default for AnimationConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            duration: 1000,
            delay: 0,
            easing: EasingFunction::EaseInOut,
        }
    }
}

/// 缓动函数类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EasingFunction {
    Linear,
    EaseIn,
    EaseOut,
    EaseInOut,
    EaseInBack,
    EaseOutBack,
    EaseInOutBack,
    Bounce,
    Elastic,
}

/// 交互配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InteractionConfig {
    /// 是否启用交互
    pub enabled: bool,
    
    /// 是否可选择
    pub selectable: bool,
    
    /// 是否显示工具提示
    pub show_tooltip: bool,
    
    /// 悬停高亮
    pub hover_highlight: bool,
    
    /// 点击回调
    pub on_click: Option<String>,
    
    /// 悬停回调
    pub on_hover: Option<String>,
}

impl Default for InteractionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            selectable: true,
            show_tooltip: true,
            hover_highlight: true,
            on_click: None,
            on_hover: None,
        }
    }
}

/// 图表基础特征 - 支持泛型数据类型
///
/// 提供图表的基本属性和行为，包括名称、数据、可见性等
/// 支持泛型数据类型以适应不同图表的数据结构
pub trait ChartBase {
    /// 图表的原始数据类型
    type DataType;

    /// 获取图表名称
    fn name(&self) -> &str;

    /// 获取图表原始数据
    fn raw_data(&self) -> &Self::DataType;

    /// 将原始数据转换为标准 DataSet
    fn to_dataset(&self) -> DataSet;

    /// 是否可见
    fn visible(&self) -> bool;

    /// 获取Z轴索引
    fn z_index(&self) -> i32;

    /// 获取边界
    fn bounds(&self) -> Option<Bounds>;

    /// 获取配置
    fn config(&self) -> &ChartConfig;

    /// 获取可变配置
    fn config_mut(&mut self) -> &mut ChartConfig;

    /// 获取标准数据接口（为了向后兼容）
    fn data(&self) -> DataSet {
        self.to_dataset()
    }
}

/// 图表系列特征（结合 ChartBase 和 Series）
pub trait ChartSeries: ChartBase + Series {
    /// 设置可见性（链式调用）
    fn visible(mut self, visible: bool) -> Self 
    where 
        Self: Sized 
    {
        self.config_mut().visible = visible;
        self
    }
    
    /// 设置Z轴索引（链式调用）
    fn z_index(mut self, z_index: i32) -> Self 
    where 
        Self: Sized 
    {
        self.config_mut().z_index = z_index;
        self
    }
    
    /// 设置动画配置（链式调用）
    fn animation(mut self, animation: AnimationConfig) -> Self 
    where 
        Self: Sized 
    {
        self.config_mut().animation = animation;
        self
    }
    
    /// 启用/禁用动画（链式调用）
    fn animated(mut self, enabled: bool) -> Self 
    where 
        Self: Sized 
    {
        self.config_mut().animation.enabled = enabled;
        self
    }
    
    /// 设置动画持续时间（链式调用）
    fn animation_duration(mut self, duration: u32) -> Self 
    where 
        Self: Sized 
    {
        self.config_mut().animation.duration = duration;
        self
    }
    
    /// 设置交互配置（链式调用）
    fn interaction(mut self, interaction: InteractionConfig) -> Self 
    where 
        Self: Sized 
    {
        self.config_mut().interaction = interaction;
        self
    }
    
    /// 启用/禁用交互（链式调用）
    fn interactive(mut self, enabled: bool) -> Self 
    where 
        Self: Sized 
    {
        self.config_mut().interaction.enabled = enabled;
        self
    }
    
    /// 启用/禁用工具提示（链式调用）
    fn tooltip(mut self, enabled: bool) -> Self 
    where 
        Self: Sized 
    {
        self.config_mut().interaction.show_tooltip = enabled;
        self
    }
    
    /// 设置点击回调（链式调用）
    fn on_click<S: Into<String>>(mut self, callback: S) -> Self 
    where 
        Self: Sized 
    {
        self.config_mut().interaction.on_click = Some(callback.into());
        self
    }
    
    /// 设置悬停回调（链式调用）
    fn on_hover<S: Into<String>>(mut self, callback: S) -> Self 
    where 
        Self: Sized 
    {
        self.config_mut().interaction.on_hover = Some(callback.into());
        self
    }
}

/// 通用边界计算工具
pub struct BoundsCalculator;

impl BoundsCalculator {
    /// 从数据集计算边界
    pub fn from_dataset(dataset: &DataSet) -> Option<Bounds> {
        if dataset.is_empty() {
            return None;
        }
        
        let points = dataset.points();
        let mut min_x = f64::INFINITY;
        let mut max_x = f64::NEG_INFINITY;
        let mut min_y = f64::INFINITY;
        let mut max_y = f64::NEG_INFINITY;
        
        for point in points {
            if point.values.len() >= 2 {
                if let (echarts_core::DataValue::Number(x), echarts_core::DataValue::Number(y)) = 
                    (&point.values[0], &point.values[1]) {
                    min_x = min_x.min(*x);
                    max_x = max_x.max(*x);
                    min_y = min_y.min(*y);
                    max_y = max_y.max(*y);
                }
            }
        }
        
        if min_x.is_finite() && max_x.is_finite() && min_y.is_finite() && max_y.is_finite() {
            Some(Bounds::new(min_x, min_y, max_x - min_x, max_y - min_y))
        } else {
            None
        }
    }
    
    /// 扩展边界（添加边距）
    pub fn expand(bounds: Bounds, margin: f64) -> Bounds {
        Bounds::new(
            bounds.origin.x - margin,
            bounds.origin.y - margin,
            bounds.size.width + 2.0 * margin,
            bounds.size.height + 2.0 * margin,
        )
    }
    
    /// 合并多个边界
    pub fn union(bounds_list: &[Bounds]) -> Option<Bounds> {
        if bounds_list.is_empty() {
            return None;
        }
        
        let mut result = bounds_list[0];
        for &bounds in &bounds_list[1..] {
            let min_x = result.origin.x.min(bounds.origin.x);
            let min_y = result.origin.y.min(bounds.origin.y);
            let max_x = (result.origin.x + result.size.width).max(bounds.origin.x + bounds.size.width);
            let max_y = (result.origin.y + result.size.height).max(bounds.origin.y + bounds.size.height);
            
            result = Bounds::new(min_x, min_y, max_x - min_x, max_y - min_y);
        }
        
        Some(result)
    }
}
