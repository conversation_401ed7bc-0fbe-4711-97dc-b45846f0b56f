//! 渲染缓存系统
//!
//! 提供智能缓存机制，避免重复计算几何体和绘制指令。
//! 支持增量更新和内存管理。

use crate::{Bounds, CompileResult, DrawBatch};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};

/// 缓存键类型
pub type CacheKey = String;

/// 缓存项
#[derive(Debug, Clone)]
pub struct CacheItem {
    /// 编译结果
    pub result: CompileResult,

    /// 创建时间
    pub created_at: Instant,

    /// 最后访问时间
    pub last_accessed: Instant,

    /// 访问次数
    pub access_count: u64,

    /// 内存使用量（字节）
    pub memory_usage: usize,

    /// 是否为脏数据
    pub dirty: bool,
}

/// 缓存统计信息
#[derive(Debug, Clone)]
pub struct CacheStats {
    /// 总缓存项数
    pub total_items: usize,

    /// 缓存命中次数
    pub hit_count: u64,

    /// 缓存未命中次数
    pub miss_count: u64,

    /// 总内存使用量
    pub total_memory_bytes: usize,

    /// 缓存命中率
    pub hit_rate: f64,
}

/// 缓存配置
#[derive(Debug, Clone)]
pub struct CacheConfig {
    /// 最大缓存项数
    pub max_items: usize,

    /// 最大内存使用量（字节）
    pub max_memory_bytes: usize,

    /// 缓存过期时间
    pub expire_duration: Duration,

    /// 是否启用LRU淘汰
    pub enable_lru: bool,

    /// 是否启用内存压缩
    pub enable_compression: bool,
}

/// 渲染缓存管理器
pub struct RenderCache {
    /// 缓存存储
    cache: Arc<RwLock<HashMap<CacheKey, CacheItem>>>,

    /// 配置
    config: CacheConfig,

    /// 统计信息
    stats: Arc<RwLock<CacheStats>>,

    /// LRU访问顺序
    lru_order: Arc<RwLock<Vec<CacheKey>>>,
}

impl RenderCache {
    /// 创建新的缓存管理器
    pub fn new(config: CacheConfig) -> Self {
        Self {
            cache: Arc::new(RwLock::new(HashMap::new())),
            config,
            stats: Arc::new(RwLock::new(CacheStats::default())),
            lru_order: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// 获取缓存项
    pub fn get(&self, key: &CacheKey) -> Option<CompileResult> {
        let mut cache = self.cache.write().unwrap();
        let mut stats = self.stats.write().unwrap();

        if let Some(item) = cache.get_mut(key) {
            // 检查是否过期
            if item.created_at.elapsed() > self.config.expire_duration {
                cache.remove(key);
                stats.miss_count += 1;
                return None;
            }

            // 更新访问信息
            item.last_accessed = Instant::now();
            item.access_count += 1;

            // 更新LRU顺序
            if self.config.enable_lru {
                self.update_lru_order(key);
            }

            stats.hit_count += 1;
            stats.hit_rate = stats.hit_count as f64 / (stats.hit_count + stats.miss_count) as f64;

            Some(item.result.clone())
        } else {
            stats.miss_count += 1;
            stats.hit_rate = stats.hit_count as f64 / (stats.hit_count + stats.miss_count) as f64;
            None
        }
    }

    /// 设置缓存项
    pub fn set(&self, key: CacheKey, result: CompileResult) {
        let memory_usage = self.estimate_memory_usage(&result);

        let item = CacheItem {
            result,
            created_at: Instant::now(),
            last_accessed: Instant::now(),
            access_count: 1,
            memory_usage,
            dirty: false,
        };

        let mut cache = self.cache.write().unwrap();
        let mut stats = self.stats.write().unwrap();

        // 检查是否需要清理缓存
        if cache.len() >= self.config.max_items
            || stats.total_memory_bytes + memory_usage > self.config.max_memory_bytes
        {
            self.cleanup_cache(&mut cache, &mut stats);
        }

        cache.insert(key.clone(), item);
        stats.total_items = cache.len();
        stats.total_memory_bytes += memory_usage;

        // 更新LRU顺序
        if self.config.enable_lru {
            self.update_lru_order(&key);
        }
    }

    /// 标记缓存项为脏数据
    pub fn mark_dirty(&self, key: &CacheKey) {
        let mut cache = self.cache.write().unwrap();
        if let Some(item) = cache.get_mut(key) {
            item.dirty = true;
        }
    }

    /// 清除脏数据
    pub fn clear_dirty(&self) {
        let mut cache = self.cache.write().unwrap();
        let mut stats = self.stats.write().unwrap();

        let keys_to_remove: Vec<_> = cache
            .iter()
            .filter(|(_, item)| item.dirty)
            .map(|(key, _)| key.clone())
            .collect();

        for key in keys_to_remove {
            if let Some(item) = cache.remove(&key) {
                stats.total_memory_bytes -= item.memory_usage;
            }
        }

        stats.total_items = cache.len();
    }

    /// 清空所有缓存
    pub fn clear_all(&self) {
        let mut cache = self.cache.write().unwrap();
        let mut stats = self.stats.write().unwrap();
        let mut lru_order = self.lru_order.write().unwrap();

        cache.clear();
        lru_order.clear();

        stats.total_items = 0;
        stats.total_memory_bytes = 0;
    }

    /// 获取缓存统计信息
    pub fn get_stats(&self) -> CacheStats {
        self.stats.read().unwrap().clone()
    }

    /// 估算内存使用量
    fn estimate_memory_usage(&self, result: &CompileResult) -> usize {
        // 简化的内存估算
        let mut size = std::mem::size_of::<CompileResult>();

        for batch in &result.batches {
            size += std::mem::size_of::<DrawBatch>();
            size += batch.commands.len() * 128; // 估算每个绘制指令的大小
        }

        size
    }

    /// 更新LRU访问顺序
    fn update_lru_order(&self, key: &CacheKey) {
        let mut lru_order = self.lru_order.write().unwrap();

        // 移除旧位置
        if let Some(pos) = lru_order.iter().position(|k| k == key) {
            lru_order.remove(pos);
        }

        // 添加到末尾（最近访问）
        lru_order.push(key.clone());
    }

    /// 清理缓存
    fn cleanup_cache(&self, cache: &mut HashMap<CacheKey, CacheItem>, stats: &mut CacheStats) {
        if self.config.enable_lru {
            self.cleanup_lru(cache, stats);
        } else {
            self.cleanup_oldest(cache, stats);
        }
    }

    /// LRU清理策略
    fn cleanup_lru(&self, cache: &mut HashMap<CacheKey, CacheItem>, stats: &mut CacheStats) {
        let lru_order = self.lru_order.read().unwrap();
        let target_size = self.config.max_items / 2; // 清理到一半大小

        let mut _removed_count = 0;
        for key in lru_order.iter() {
            if cache.len() <= target_size {
                break;
            }

            if let Some(item) = cache.remove(key) {
                stats.total_memory_bytes -= item.memory_usage;
                _removed_count += 1;
            }
        }

        stats.total_items = cache.len();

        // 更新LRU顺序
        drop(lru_order);
        let mut lru_order = self.lru_order.write().unwrap();
        lru_order.retain(|key| cache.contains_key(key));
    }

    /// 按时间清理策略
    fn cleanup_oldest(&self, cache: &mut HashMap<CacheKey, CacheItem>, stats: &mut CacheStats) {
        let mut items: Vec<_> = cache
            .iter()
            .map(|(k, v)| (k.clone(), v.created_at))
            .collect();
        items.sort_by_key(|(_, created_at)| *created_at);

        let target_size = self.config.max_items / 2;
        let to_remove = cache.len().saturating_sub(target_size);

        for (key, _) in items.iter().take(to_remove) {
            if let Some(item) = cache.remove(key) {
                stats.total_memory_bytes -= item.memory_usage;
            }
        }

        stats.total_items = cache.len();
    }
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            max_items: 1000,
            max_memory_bytes: 100 * 1024 * 1024,       // 100MB
            expire_duration: Duration::from_secs(300), // 5分钟
            enable_lru: true,
            enable_compression: false,
        }
    }
}

impl Default for CacheStats {
    fn default() -> Self {
        Self {
            total_items: 0,
            hit_count: 0,
            miss_count: 0,
            total_memory_bytes: 0,
            hit_rate: 0.0,
        }
    }
}

/// 缓存键生成器
pub struct CacheKeyGenerator;

impl CacheKeyGenerator {
    /// 为图表数据生成缓存键
    pub fn generate_chart_key(
        chart_id: &str,
        data_hash: u64,
        style_hash: u64,
        bounds: &Bounds,
        lod_level: u8,
    ) -> CacheKey {
        format!(
            "chart:{}:data:{}:style:{}:bounds:{}:{}:{}:{}:lod:{}",
            chart_id,
            data_hash,
            style_hash,
            bounds.origin.x,
            bounds.origin.y,
            bounds.size.width,
            bounds.size.height,
            lod_level
        )
    }

    /// 为几何体生成缓存键
    pub fn generate_geometry_key(
        geometry_type: &str,
        data_hash: u64,
        transform_hash: u64,
    ) -> CacheKey {
        format!(
            "geometry:{}:data:{}:transform:{}",
            geometry_type, data_hash, transform_hash
        )
    }
}

/// 简单的哈希计算工具
pub fn calculate_hash<T: std::hash::Hash>(obj: &T) -> u64 {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::Hasher;

    let mut hasher = DefaultHasher::new();
    obj.hash(&mut hasher);
    hasher.finish()
}
