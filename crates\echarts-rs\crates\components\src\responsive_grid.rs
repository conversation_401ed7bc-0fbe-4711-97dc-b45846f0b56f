//! 响应式网格系统
//!
//! 根据数据范围和图表大小自动调整网格密度和样式

use crate::grid::Grid;
use echarts_core::{Bounds, Color, DrawCommand, LineStyle, Point, Result};
use std::any::Any;

/// 响应式网格配置
#[derive(Debug, Clone)]
pub struct ResponsiveGridConfig {
    /// 最小网格间距（像素）
    pub min_grid_spacing: f64,
    /// 最大网格间距（像素）
    pub max_grid_spacing: f64,
    /// 自动调整密度
    pub auto_density: bool,
    /// 自动调整样式
    pub auto_style: bool,
    /// 数据密度阈值
    pub data_density_threshold: f64,
}

impl Default for ResponsiveGridConfig {
    fn default() -> Self {
        Self {
            min_grid_spacing: 20.0,
            max_grid_spacing: 100.0,
            auto_density: true,
            auto_style: true,
            data_density_threshold: 0.1,
        }
    }
}

/// 响应式网格
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct ResponsiveGrid {
    /// 基础网格
    base_grid: Grid,
    /// 响应式配置
    config: ResponsiveGridConfig,
    /// 数据范围
    data_range: Option<(f64, f64, f64, f64)>, // (x_min, x_max, y_min, y_max)
    /// 数据点数量
    data_point_count: usize,
}

impl ResponsiveGrid {
    /// 创建新的响应式网格
    pub fn new() -> Self {
        Self {
            base_grid: Grid::default(),
            config: ResponsiveGridConfig::default(),
            data_range: None,
            data_point_count: 0,
        }
    }

    /// 从基础网格创建
    pub fn from_grid(grid: Grid) -> Self {
        Self {
            base_grid: grid,
            config: ResponsiveGridConfig::default(),
            data_range: None,
            data_point_count: 0,
        }
    }

    /// 设置响应式配置
    pub fn with_config(mut self, config: ResponsiveGridConfig) -> Self {
        self.config = config;
        self
    }

    /// 设置数据范围
    pub fn with_data_range(mut self, x_min: f64, x_max: f64, y_min: f64, y_max: f64) -> Self {
        self.data_range = Some((x_min, x_max, y_min, y_max));
        self
    }

    /// 设置数据点数量
    pub fn with_data_point_count(mut self, count: usize) -> Self {
        self.data_point_count = count;
        self
    }

    /// 计算最优网格密度
    pub fn calculate_optimal_density(&self, bounds: Bounds) -> (usize, usize) {
        if !self.config.auto_density {
            return (10, 8); // 默认密度
        }

        // 根据图表大小计算基础密度
        let base_h_lines = (bounds.size.width / self.config.min_grid_spacing).max(5.0).min(20.0) as usize;
        let base_v_lines = (bounds.size.height / self.config.min_grid_spacing).max(4.0).min(15.0) as usize;

        // 根据数据密度调整
        let density_factor = self.calculate_data_density_factor();
        
        let h_lines = ((base_h_lines as f64) * density_factor).max(5.0).min(25.0) as usize;
        let v_lines = ((base_v_lines as f64) * density_factor).max(4.0).min(20.0) as usize;

        (h_lines, v_lines)
    }

    /// 计算数据密度因子
    fn calculate_data_density_factor(&self) -> f64 {
        if let Some((x_min, x_max, y_min, y_max)) = self.data_range {
            let x_range = x_max - x_min;
            let y_range = y_max - y_min;
            
            // 数据范围越大，需要更多网格线
            let range_factor = ((x_range + y_range) / 200.0).max(0.5).min(2.0);
            
            // 数据点越多，需要更密集的网格
            let point_factor = (self.data_point_count as f64 / 100.0).max(0.5).min(2.0);
            
            (range_factor + point_factor) / 2.0
        } else {
            1.0
        }
    }

    /// 计算自适应样式
    pub fn calculate_adaptive_style(&self, bounds: Bounds) -> LineStyle {
        if !self.config.auto_style {
            return self.base_grid.grid_line_style.clone();
        }

        let density_factor = self.calculate_data_density_factor();
        
        // 密度高时使用更细的线条
        let line_width = if density_factor > 1.5 {
            0.3
        } else if density_factor > 1.0 {
            0.5
        } else {
            0.8
        };

        // 根据图表大小调整透明度
        let alpha = if bounds.size.width * bounds.size.height > 500000.0 {
            0.3 // 大图表使用更透明的网格
        } else {
            0.6
        };

        LineStyle {
            color: Color::rgba(0.7, 0.7, 0.7, alpha),
            width: line_width,
            ..Default::default()
        }
    }

    /// 生成响应式网格线
    pub fn generate_responsive_grid_lines(&self, bounds: Bounds) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();
        
        let (h_lines, v_lines) = self.calculate_optimal_density(bounds);
        let line_style = self.calculate_adaptive_style(bounds);

        // 垂直网格线
        for i in 0..=h_lines {
            let x = bounds.origin.x + (bounds.size.width * i as f64) / h_lines as f64;
            commands.push(DrawCommand::Line {
                from: Point::new(x, bounds.origin.y),
                to: Point::new(x, bounds.origin.y + bounds.size.height),
                style: line_style.clone(),
            });
        }

        // 水平网格线
        for i in 0..=v_lines {
            let y = bounds.origin.y + (bounds.size.height * i as f64) / v_lines as f64;
            commands.push(DrawCommand::Line {
                from: Point::new(bounds.origin.x, y),
                to: Point::new(bounds.origin.x + bounds.size.width, y),
                style: line_style.clone(),
            });
        }

        Ok(commands)
    }

    /// 生成智能网格线（避免与数据重叠）
    pub fn generate_smart_grid_lines(&self, bounds: Bounds) -> Result<Vec<DrawCommand>> {
        let mut commands = self.generate_responsive_grid_lines(bounds)?;
        
        // TODO: 实现智能避让算法
        // 分析数据分布，避免网格线与重要数据点重叠
        
        Ok(commands)
    }

    /// 获取基础网格的引用
    pub fn base_grid(&self) -> &Grid {
        &self.base_grid
    }

    /// 获取基础网格的可变引用
    pub fn base_grid_mut(&mut self) -> &mut Grid {
        &mut self.base_grid
    }
}

impl Default for ResponsiveGrid {
    fn default() -> Self {
        Self::new()
    }
}

// 实现 ChartComponent trait
impl echarts_core::ChartComponent for ResponsiveGrid {
    fn component_type(&self) -> &'static str {
        "responsive_grid"
    }
    
    fn is_visible(&self) -> bool {
        self.base_grid.visible
    }
    
    fn render_to_commands(&self, bounds: Bounds) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();
        
        if !self.is_visible() {
            return Ok(commands);
        }
        
        // 渲染背景和边框（使用基础网格的设置）
        let grid_bounds = self.base_grid.calculate_grid_bounds(bounds);
        
        if let Some(bg_color) = self.base_grid.background_color {
            commands.push(DrawCommand::Rect {
                bounds: grid_bounds,
                style: crate::draw_commands::RectStyle {
                    fill: Some(bg_color),
                    stroke: None,
                    opacity: 1.0,
                    corner_radius: 0.0,
                },
            });
        }
        
        if let Some(border_color) = self.base_grid.border_color {
            commands.push(DrawCommand::Rect {
                bounds: grid_bounds,
                style: crate::draw_commands::RectStyle {
                    fill: None,
                    stroke: Some((border_color, self.base_grid.border_width)),
                    opacity: 1.0,
                    corner_radius: 0.0,
                },
            });
        }
        
        // 渲染响应式网格线
        if self.base_grid.show_grid_lines {
            let grid_commands = self.generate_smart_grid_lines(grid_bounds)?;
            commands.extend(grid_commands);
        }
        
        Ok(commands)
    }
    
    fn clone_component(&self) -> Box<dyn echarts_core::ChartComponent> {
        Box::new(self.clone())
    }
    
    fn as_any(&self) -> &dyn Any {
        self
    }
}

/// 响应式网格预设
pub struct ResponsiveGridPresets;

impl ResponsiveGridPresets {
    /// 高密度数据网格
    pub fn high_density_data() -> ResponsiveGrid {
        ResponsiveGrid::new().with_config(ResponsiveGridConfig {
            min_grid_spacing: 15.0,
            max_grid_spacing: 60.0,
            auto_density: true,
            auto_style: true,
            data_density_threshold: 0.05,
        })
    }

    /// 稀疏数据网格
    pub fn sparse_data() -> ResponsiveGrid {
        ResponsiveGrid::new().with_config(ResponsiveGridConfig {
            min_grid_spacing: 40.0,
            max_grid_spacing: 150.0,
            auto_density: true,
            auto_style: true,
            data_density_threshold: 0.2,
        })
    }

    /// 时间序列网格
    pub fn time_series() -> ResponsiveGrid {
        ResponsiveGrid::new().with_config(ResponsiveGridConfig {
            min_grid_spacing: 30.0,
            max_grid_spacing: 120.0,
            auto_density: true,
            auto_style: true,
            data_density_threshold: 0.1,
        })
    }

    /// 金融数据网格
    pub fn financial_data() -> ResponsiveGrid {
        ResponsiveGrid::new().with_config(ResponsiveGridConfig {
            min_grid_spacing: 20.0,
            max_grid_spacing: 80.0,
            auto_density: true,
            auto_style: true,
            data_density_threshold: 0.08,
        })
    }

    /// 科学数据网格
    pub fn scientific_data() -> ResponsiveGrid {
        ResponsiveGrid::new().with_config(ResponsiveGridConfig {
            min_grid_spacing: 25.0,
            max_grid_spacing: 100.0,
            auto_density: true,
            auto_style: true,
            data_density_threshold: 0.12,
        })
    }
}
