/*!
 * ECharts GPUI 实时图表案例
 * 
 * 本示例展示了如何创建一个实时更新的ECharts图表，
 * 模拟实时数据流并动态更新图表显示。
 */

use echarts_rs::prelude::*;
use gpui::{
    div, px, rgb, size, AppContext, Context, IntoElement, ParentElement, Render,
    Styled, Window, WindowBounds, WindowKind, WindowOptions, TitlebarOptions,
    WindowBackgroundAppearance, Application, Timer,
};
use gpui::Bounds as GpuiBounds;
use serde_json::json;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use std::collections::VecDeque;

fn main() {
    println!("⏱️ 启动 ECharts 实时图表");

    let app = Application::new();
    
    app.run(move |cx: &mut AppContext| {
        let window_size = size(px(1000.0), px(700.0));
        
        let _window = cx.open_window(
            WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(GpuiBounds::centered(None, window_size, cx))),
                titlebar: Some(TitlebarOptions {
                    title: Some("实时数据监控".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                window_background: WindowBackgroundAppearance::Opaque,
                app_id: None,
                window_min_size: Some(size(px(800.0), px(600.0))),
                window_decorations: None,
            },
            |_window, cx| {
                println!("🪟 实时图表窗口已创建");
                cx.new(|cx| RealtimeChart::new(cx))
            },
        );
    });
}

/// 数据点结构
#[derive(Debug, Clone)]
struct DataPoint {
    timestamp: u64,
    value: f64,
}

/// 实时图表结构
struct RealtimeChart {
    /// 数据缓冲区（最多保存50个数据点）
    data_buffer: VecDeque<DataPoint>,
    /// 当前图表
    current_chart: Chart,
    /// 更新计数器
    update_counter: u32,
    /// 定时器
    _timer: Timer,
}

impl RealtimeChart {
    fn new(cx: &mut Context<Self>) -> Self {
        println!("📊 初始化实时图表");
        
        let mut instance = Self {
            data_buffer: VecDeque::with_capacity(50),
            current_chart: Chart::new(),
            update_counter: 0,
            _timer: Timer::new(Duration::from_millis(500)),
        };
        
        // 初始化一些数据点
        instance.initialize_data();
        instance.update_chart();
        
        // 设置定时器来模拟实时数据更新
        instance.start_data_simulation(cx);
        
        instance
    }
    
    /// 初始化数据
    fn initialize_data(&mut self) {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // 添加初始数据点
        for i in 0..20 {
            let timestamp = now - (20 - i) as u64;
            let value = 50.0 + (i as f64 * 0.5).sin() * 20.0;
            
            self.data_buffer.push_back(DataPoint { timestamp, value });
        }
    }
    
    /// 开始数据模拟
    fn start_data_simulation(&mut self, cx: &mut Context<Self>) {
        // 使用定时器定期更新数据
        cx.spawn(|this, mut cx| async move {
            loop {
                cx.update(|this, cx| {
                    this.add_new_data_point();
                    this.update_chart();
                    cx.notify();
                }).ok();
                
                // 等待500毫秒
                async_std::task::sleep(Duration::from_millis(500)).await;
            }
        }).detach();
    }
    
    /// 添加新数据点
    fn add_new_data_point(&mut self) {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // 生成模拟数据
        let base_value = 50.0;
        let trend = (self.update_counter as f64 * 0.1).sin() * 15.0;
        let noise = (rand::random::<f64>() - 0.5) * 10.0;
        let value = base_value + trend + noise;
        
        // 添加新数据点
        self.data_buffer.push_back(DataPoint {
            timestamp: now,
            value,
        });
        
        // 保持缓冲区大小
        if self.data_buffer.len() > 50 {
            self.data_buffer.pop_front();
        }
        
        self.update_counter += 1;
    }
    
    /// 更新图表
    fn update_chart(&mut self) {
        let mut chart = Chart::new();
        
        // 准备时间序列数据
        let timestamps: Vec<String> = self.data_buffer
            .iter()
            .enumerate()
            .map(|(i, _)| format!("T{}", i))
            .collect();
        
        let values: Vec<f64> = self.data_buffer
            .iter()
            .map(|point| point.value)
            .collect();
        
        // 计算统计信息
        let avg = if !values.is_empty() {
            values.iter().sum::<f64>() / values.len() as f64
        } else {
            0.0
        };
        
        let max_val = values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let min_val = values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        
        let option = json!({
            "title": {
                "text": "实时数据监控",
                "subtext": format!("当前值: {:.2} | 平均值: {:.2} | 最大值: {:.2} | 最小值: {:.2}", 
                    values.last().unwrap_or(&0.0), avg, max_val, min_val),
                "left": "center",
                "textStyle": {
                    "fontSize": 18,
                    "color": "#333"
                },
                "subtextStyle": {
                    "fontSize": 12,
                    "color": "#666"
                }
            },
            "tooltip": {
                "trigger": "axis",
                "formatter": "时间: {b}<br/>数值: {c}"
            },
            "grid": {
                "left": "3%",
                "right": "4%",
                "bottom": "3%",
                "containLabel": true
            },
            "xAxis": {
                "type": "category",
                "boundaryGap": false,
                "data": timestamps,
                "axisLabel": {
                    "show": false
                }
            },
            "yAxis": {
                "type": "value",
                "scale": true,
                "name": "数值",
                "min": "dataMin",
                "max": "dataMax"
            },
            "series": [
                {
                    "name": "实时数据",
                    "type": "line",
                    "data": values,
                    "smooth": true,
                    "symbol": "circle",
                    "symbolSize": 4,
                    "itemStyle": {
                        "color": "#5470c6"
                    },
                    "areaStyle": {
                        "color": {
                            "type": "linear",
                            "x": 0,
                            "y": 0,
                            "x2": 0,
                            "y2": 1,
                            "colorStops": [
                                {"offset": 0, "color": "rgba(84, 112, 198, 0.3)"},
                                {"offset": 1, "color": "rgba(84, 112, 198, 0.1)"}
                            ]
                        }
                    }
                },
                {
                    "name": "平均线",
                    "type": "line",
                    "data": vec![avg; values.len()],
                    "lineStyle": {
                        "color": "#91cc75",
                        "type": "dashed"
                    },
                    "symbol": "none",
                    "itemStyle": {
                        "color": "#91cc75"
                    }
                }
            ]
        });
        
        self.current_chart = chart.option(option);
    }
    
    /// 获取状态信息
    fn get_status_info(&self) -> (usize, f64, String) {
        let data_count = self.data_buffer.len();
        let current_value = self.data_buffer.back().map(|p| p.value).unwrap_or(0.0);
        let status = if current_value > 60.0 {
            "🔴 高"
        } else if current_value > 40.0 {
            "🟡 中"
        } else {
            "🟢 低"
        };
        
        (data_count, current_value, status.to_string())
    }
}

impl Render for RealtimeChart {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        let (data_count, current_value, status) = self.get_status_info();
        
        div()
            .flex()
            .flex_col()
            .w_full()
            .h_full()
            .bg(rgb(0xf8f9fa))
            .child(
                // 状态栏
                div()
                    .flex()
                    .justify_between()
                    .items_center()
                    .h_16()
                    .bg(rgb(0x212529))
                    .text_color(rgb(0xffffff))
                    .px_6()
                    .child(
                        div()
                            .text_xl()
                            .font_bold()
                            .child("⏱️ 实时数据监控")
                    )
                    .child(
                        div()
                            .flex()
                            .gap_6()
                            .child(
                                div()
                                    .text_sm()
                                    .child(format!("数据点: {}", data_count))
                            )
                            .child(
                                div()
                                    .text_sm()
                                    .child(format!("当前值: {:.2}", current_value))
                            )
                            .child(
                                div()
                                    .text_sm()
                                    .child(format!("状态: {}", status))
                            )
                            .child(
                                div()
                                    .text_sm()
                                    .child(format!("更新: #{}", self.update_counter))
                            )
                    )
            )
            .child(
                // 图表显示区域
                div()
                    .flex_1()
                    .bg(rgb(0xffffff))
                    .m_4()
                    .rounded_lg()
                    .shadow_lg()
                    .p_4()
                    .child(self.current_chart.draw())
            )
            .child(
                // 底部信息栏
                div()
                    .h_12()
                    .bg(rgb(0x495057))
                    .text_color(rgb(0xffffff))
                    .flex()
                    .items_center()
                    .justify_center()
                    .text_sm()
                    .child("💡 数据每500毫秒自动更新 | 最多显示50个数据点")
            )
    }
}
