# ECharts-rs API 文档

本文档详细介绍了ECharts-rs的API接口和使用方法。

**⚠️ 重要更新**: 本文档已更新以反映 EXECUTION_PLAN.md 方案2的实现。

## 目录

- [新架构 API](#新架构-api)
- [RuntimeChart](#runtimechart)
- [ChartBuilder](#chartbuilder)
- [图表系列 (Series)](#图表系列-series)
- [渲染器 (Renderer)](#渲染器-renderer)
- [迁移指南](#迁移指南)

## 新架构 API

### RuntimeChart

`RuntimeChart` 是新架构的核心，持有实际的 Series 实例。

```rust
use echarts_rs::RuntimeChart;
use echarts_charts::LineSeries;
use echarts_core::Color;

// 创建运行时图表
let chart = RuntimeChart::new()
    .title("我的图表")
    .add_line_series(LineSeries::new("数据".to_string()).data(data));
```

#### 方法

- `new() -> Self` - 创建新的运行时图表
- `title<S: Into<String>>(self, title: S) -> Self` - 设置图表标题
- `add_line_series(self, series: LineSeries) -> Self` - 添加折线图系列
- `add_bar_series(self, series: BarSeries) -> Self` - 添加柱状图系列
- `add_pie_series(self, series: PieSeries) -> Self` - 添加饼图系列
- `add_scatter_series(self, series: ScatterSeries) -> Self` - 添加散点图系列
- `render<R>(&self, renderer: &mut R, bounds: Bounds) -> Result<()>` - 渲染图表

### ChartBuilder

`ChartBuilder` 提供便捷的图表构建 API。

```rust
use echarts_rs::ChartBuilder;
use echarts_charts::{LineSeries, BarSeries};

// 使用构建器模式
let chart = ChartBuilder::new()
    .title("复合图表")
    .add_line_series(line_series)
    .add_bar_series(bar_series)
    .build();
```

#### 方法

- `new() -> Self` - 创建新的图表构建器
- `title<S: Into<String>>(self, title: S) -> Self` - 设置图表标题
- `add_*_series(self, series: *Series) -> Self` - 添加各种类型的系列
- `build(self) -> RuntimeChart` - 构建最终的运行时图表
- `line_chart() -> Self` - 创建折线图构建器
- `bar_chart() -> Self` - 创建柱状图构建器
- `pie_chart() -> Self` - 创建饼图构建器
- `scatter_chart() -> Self` - 创建散点图构建器
- `with_background(color: Color) -> Chart` - 设置背景颜色

### DataPoint

数据点结构，用于存储图表数据。

```rust
use echarts_core::{DataPoint, DataValue};

// 创建数值数据点
let point = DataPoint::new(vec![
    DataValue::Number(10.0),
    DataValue::Number(20.0)
]);

// 创建分类数据点
let point = DataPoint::new(vec![
    DataValue::String("类别A".to_string()),
    DataValue::Number(100.0)
]);
```

#### 方法

- `new(values: Vec<DataValue>) -> DataPoint` - 创建数据点
- `x() -> f64` - 获取X轴数值
- `y() -> f64` - 获取Y轴数值
- `get_value(index: usize) -> Option<&DataValue>` - 获取指定索引的值

### DataValue

数据值枚举，支持多种数据类型。

```rust
use echarts_core::DataValue;

let number = DataValue::Number(42.0);
let text = DataValue::String("文本".to_string());
let flag = DataValue::Boolean(true);
```

#### 变体

- `Number(f64)` - 数值类型
- `String(String)` - 字符串类型
- `Boolean(bool)` - 布尔类型

#### 方法

- `as_number() -> Option<f64>` - 转换为数值
- `as_string() -> Option<String>` - 转换为字符串
- `as_boolean() -> Option<bool>` - 转换为布尔值

### Color

颜色结构，支持RGBA颜色空间。

```rust
use echarts_core::Color;

// 创建RGB颜色
let red = Color::rgb(1.0, 0.0, 0.0);

// 创建RGBA颜色
let transparent_blue = Color::rgba(0.0, 0.0, 1.0, 0.5);

// 使用预定义颜色
let white = Color::WHITE;
let black = Color::BLACK;
```

#### 常量

- `WHITE` - 白色
- `BLACK` - 黑色
- `RED` - 红色
- `GREEN` - 绿色
- `BLUE` - 蓝色

#### 方法

- `rgb(r: f64, g: f64, b: f64) -> Color` - 创建RGB颜色
- `rgba(r: f64, g: f64, b: f64, a: f64) -> Color` - 创建RGBA颜色
- `to_hex() -> String` - 转换为十六进制字符串

### Point

二维点结构。

```rust
use echarts_core::Point;

let point = Point::new(10.0, 20.0);
let distance = point.distance_to(Point::new(30.0, 40.0));
```

#### 方法

- `new(x: f64, y: f64) -> Point` - 创建点
- `distance_to(other: Point) -> f64` - 计算到另一点的距离

### Bounds

边界矩形结构。

```rust
use echarts_core::Bounds;

let bounds = Bounds::new(0.0, 0.0, 800.0, 600.0);
let center = bounds.center();
let area = bounds.size.area();
```

#### 方法

- `new(x: f64, y: f64, width: f64, height: f64) -> Bounds` - 创建边界
- `center() -> Point` - 获取中心点
- `contains_point(point: Point) -> bool` - 检查是否包含点
- `intersect(other: Bounds) -> Bounds` - 计算与另一边界的交集

## 图表类型 (Charts)

### BarChart

柱状图实现。

```rust
use echarts_charts::BarChart;

let mut bar_chart = BarChart::new();
bar_chart.set_data(data);
bar_chart.set_color(Color::BLUE);
```

### LineChart

折线图实现。

```rust
use echarts_charts::LineChart;

let mut line_chart = LineChart::new();
line_chart.set_data(data);
line_chart.set_smooth(true);
line_chart.set_line_width(2.0);
```

### PieChart

饼图实现。

```rust
use echarts_charts::PieChart;

let mut pie_chart = PieChart::new();
pie_chart.set_data(data);
pie_chart.set_radius(0.8);
pie_chart.set_inner_radius(0.3); // 环形图
```

### ScatterChart

散点图实现。

```rust
use echarts_charts::ScatterChart;

let mut scatter_chart = ScatterChart::new();
scatter_chart.set_data(data);
scatter_chart.set_symbol_size(5.0);
```

## 主题系统 (Themes)

### Theme

主题结构，定义图表的视觉样式。

```rust
use echarts_themes::Theme;

// 使用内置主题
let light_theme = Theme::light();
let dark_theme = Theme::dark();
let vintage_theme = Theme::vintage();
let macarons_theme = Theme::macarons();

// 创建自定义主题
let mut custom_theme = Theme::light();
custom_theme.name = "自定义主题".to_string();
custom_theme.background_color = Color::rgb(0.95, 0.95, 0.95);
```

#### 内置主题

- `light()` - 浅色主题
- `dark()` - 深色主题
- `vintage()` - 复古主题
- `macarons()` - 马卡龙主题

#### 属性

- `name: String` - 主题名称
- `background_color: Color` - 背景颜色
- `color_palette: Vec<Color>` - 颜色调色板
- `text_style: TextStyle` - 文本样式

## 组件系统 (Components)

### Title

标题组件。

```rust
use echarts_components::Title;

let mut title = Title::new("图表标题");
title.subtext = Some("副标题".to_string());
title.visible = true;
```

### Legend

图例组件。

```rust
use echarts_components::Legend;

let mut legend = Legend::new();
legend.visible = true;
legend.position = Position::Top;
```

### Tooltip

提示框组件。

```rust
use echarts_components::Tooltip;

let mut tooltip = Tooltip::new();
tooltip.visible = true;
tooltip.trigger = TooltipTrigger::Item;
```

### Axis

坐标轴组件。

```rust
use echarts_components::Axis;

let mut x_axis = Axis::new();
x_axis.axis_type = AxisType::Category;
x_axis.visible = true;

let mut y_axis = Axis::new();
y_axis.axis_type = AxisType::Value;
y_axis.visible = true;
```

## 渲染器 (Renderer)

### GpuiRenderer

GPUI渲染器，用于桌面应用。

```rust
use echarts_renderer::GpuiRenderer;

let mut renderer = GpuiRenderer::new();
renderer.render(&chart, bounds)?;
```

### SvgRenderer

SVG渲染器，用于Web应用。

```rust
use echarts_svg_renderer::SvgRenderer;

let mut renderer = SvgRenderer::new();
let svg_content = renderer.render_to_string(&chart)?;
renderer.save_to_file(&chart, "chart.svg")?;
```

## 错误处理

所有可能失败的操作都返回 `Result<T, ChartError>`。

```rust
use echarts_core::{Result, ChartError};

fn create_chart() -> Result<Chart> {
    let mut chart = Chart::new();
    // ... 配置图表
    Ok(chart)
}

match create_chart() {
    Ok(chart) => println!("图表创建成功"),
    Err(ChartError::InvalidData(msg)) => println!("数据错误: {}", msg),
    Err(ChartError::RenderError(msg)) => println!("渲染错误: {}", msg),
    Err(err) => println!("其他错误: {:?}", err),
}
```

## 性能优化

### 大数据集处理

```rust
// 自动数据优化
let optimized_data = if data.len() > 10000 {
    // 采样数据以提高性能
    data.iter().step_by(data.len() / 10000).cloned().collect()
} else {
    data
};
```

### GPU加速

```rust
// GPU加速自动启用，无需额外配置
let renderer = GpuiRenderer::new();
renderer.enable_gpu_acceleration(true);
```

## 最佳实践

1. **数据预处理**: 在创建图表前对数据进行预处理和验证
2. **主题一致性**: 在应用中保持主题的一致性
3. **性能监控**: 对大数据集使用性能监控
4. **错误处理**: 始终处理可能的错误情况
5. **内存管理**: 及时释放不需要的图表资源

## 示例代码

查看 `examples/` 目录获取完整的示例代码：

- `basic_charts.rs` - 基础图表示例
- `data_visualization_showcase.rs` - 数据可视化展示
- `realtime_dashboard.rs` - 实时仪表板

## 版本兼容性

- Rust 1.70+ 
- GPUI 0.1+
- 支持的平台: Windows, macOS, Linux
