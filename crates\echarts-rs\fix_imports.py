#!/usr/bin/env python3
"""
修复基础架构导入问题
"""

import os
import re
import glob

def fix_imports():
    """修复基础架构导入问题"""
    
    chart_files = [
        "crates/charts/src/enhanced_bar.rs",
        "crates/charts/src/pie.rs", 
        "crates/charts/src/radar.rs",
        "crates/charts/src/gauge.rs",
        "crates/charts/src/treemap.rs",
        "crates/charts/src/sunburst.rs",
        "crates/charts/src/funnel.rs",
        "crates/charts/src/candlestick.rs",
        "crates/charts/src/heatmap.rs",
        "crates/charts/src/surface3d.rs",
    ]
    
    for file_path in chart_files:
        if not os.path.exists(file_path):
            continue
            
        print(f"修复导入: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经有基础架构导入
            if "use crate::base::" not in content:
                # 在 echarts_core 导入后添加基础架构导入
                pattern = r'(use echarts_core::\{[^}]+\};)\n(use serde::\{[^}]+\};)'
                replacement = r'\1\nuse crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};\n\2'
                content = re.sub(pattern, replacement, content)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ 已修复 {file_path}")
                
        except Exception as e:
            print(f"  ❌ 修复失败 {file_path}: {e}")

if __name__ == "__main__":
    print("🚀 开始修复导入问题...")
    fix_imports()
    print("✅ 修复完成！")
