---
type: "always_apply"
description: "Example description"
---
# 代码分析

执行多种检查选项的高级代码分析。

## 分析菜单：

### 1. 知识图谱生成
- 映射组件之间的关系
- 可视化依赖关系
- 识别架构模式

### 2. 代码质量评估
- 复杂度指标
- 可维护性指数
- 技术债务评估
- 代码重复检测

### 3. 性能分析
- 识别瓶颈
- 内存使用模式
- 算法复杂度
- 数据库查询优化

### 4. 安全审查
- 漏洞扫描
- 输入验证检查
- 认证/授权审查
- 敏感数据处理

### 5. 架构审查
- 设计模式遵循
- SOLID原则合规性
- 耦合和内聚分析
- 模块边界

### 6. 测试覆盖率分析
- 覆盖率百分比
- 未测试的代码路径
- 测试质量评估
- 缺失的边缘情况

## 流程：
1. 根据需要选择分析类型
2. 运行适当的工具和检查
3. 生成全面报告
4. 提供可行的建议
5. 按影响对改进进行优先级排序

## 输出格式：
- 执行摘要
- 详细发现
- 风险评估
- 改进路线图
- 相关代码示例
---