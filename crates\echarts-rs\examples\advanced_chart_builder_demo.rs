//! 高级图表构建器演示
//!
//! 本演示展示了新的 API 功能：
//! 1. 扩展的 ChartBuilder API（坐标轴、网格、图例支持）
//! 2. AdvancedChartBuilder（智能配置和自动化功能）
//! 3. 坐标轴配置预设
//! 4. 响应式网格系统
//! 5. 主题集成

use echarts_rs::prelude::*;
use echarts_components::{
    AxisPresets, ResponsiveGridPresets, ThemedComponents,
    presets::{time_value_axes, month_value_axes, financial_axes}
};
use gpui::*;
use gpui_renderer::{EChartsCanvas, EChartsElement};

/// 高级图表构建器演示应用
struct AdvancedBuilderDemo {
    /// 当前演示索引
    current_demo: usize,
    /// 演示列表
    demos: Vec<DemoConfig>,
}

/// 演示配置
struct DemoConfig {
    name: String,
    description: String,
    chart_fn: fn() -> EnhancedChart,
}

impl AdvancedBuilderDemo {
    fn new() -> Self {
        let demos = vec![
            DemoConfig {
                name: "基础坐标轴演示".to_string(),
                description: "展示扩展的 ChartBuilder API，包含坐标轴和网格".to_string(),
                chart_fn: Self::basic_axes_demo,
            },
            DemoConfig {
                name: "智能折线图".to_string(),
                description: "使用 AdvancedChartBuilder 创建智能配置的折线图".to_string(),
                chart_fn: Self::smart_line_chart_demo,
            },
            DemoConfig {
                name: "坐标轴预设演示".to_string(),
                description: "展示各种坐标轴预设配置".to_string(),
                chart_fn: Self::axis_presets_demo,
            },
            DemoConfig {
                name: "响应式网格演示".to_string(),
                description: "展示自适应的响应式网格系统".to_string(),
                chart_fn: Self::responsive_grid_demo,
            },
            DemoConfig {
                name: "主题集成演示".to_string(),
                description: "展示主题化的组件和一键切换主题".to_string(),
                chart_fn: Self::theme_integration_demo,
            },
            DemoConfig {
                name: "金融数据图表".to_string(),
                description: "使用预设创建专业的金融数据图表".to_string(),
                chart_fn: Self::financial_chart_demo,
            },
        ];

        Self {
            current_demo: 0,
            demos,
        }
    }

    /// 基础坐标轴演示
    fn basic_axes_demo() -> EnhancedChart {
        println!("🎯 创建基础坐标轴演示...");
        
        // 使用扩展的 ChartBuilder API
        ChartBuilder::line_chart()
            .title("基础坐标轴演示")
            .size(800.0, 600.0)
            .default_x_axis(Some(0.0), Some(100.0))
            .default_y_axis(Some(0.0), Some(50.0))
            .default_grid()
            .default_legend()
            .add_line_series(
                LineSeries::new("示例数据")
                    .data(vec![(0.0, 10.0), (25.0, 30.0), (50.0, 20.0), (75.0, 40.0), (100.0, 35.0)])
                    .color(Color::rgb(0.2, 0.6, 0.9))
            )
            .build_enhanced()
    }

    /// 智能折线图演示
    fn smart_line_chart_demo() -> EnhancedChart {
        println!("🧠 创建智能折线图演示...");
        
        // 使用 AdvancedChartBuilder
        AdvancedChartBuilder::new()
            .smart_line_chart()
            .theme("light")
            .responsive(true)
            .builder_mut()
            .title("智能折线图")
            .size(800.0, 600.0)
            .add_line_series(
                LineSeries::new("自动配置数据")
                    .data(vec![(0.0, 15.0), (20.0, 25.0), (40.0, 35.0), (60.0, 28.0), (80.0, 42.0), (100.0, 38.0)])
                    .color(Color::rgb(0.9, 0.4, 0.2))
            )
            .build()
    }

    /// 坐标轴预设演示
    fn axis_presets_demo() -> EnhancedChart {
        println!("📊 创建坐标轴预设演示...");
        
        // 使用时间-数值轴预设
        let (x_axis, y_axis) = time_value_axes();
        
        ChartBuilder::line_chart()
            .title("时间序列数据")
            .size(800.0, 600.0)
            .x_axis(x_axis)
            .y_axis(y_axis)
            .default_grid()
            .add_line_series(
                LineSeries::new("温度数据")
                    .data(vec![(0.0, 22.0), (6.0, 18.0), (12.0, 25.0), (18.0, 30.0), (24.0, 26.0)])
                    .color(Color::rgb(0.8, 0.2, 0.2))
            )
            .build_enhanced()
    }

    /// 响应式网格演示
    fn responsive_grid_demo() -> EnhancedChart {
        println!("📐 创建响应式网格演示...");
        
        // 使用响应式网格
        let responsive_grid = ResponsiveGridPresets::high_density_data()
            .with_data_range(0.0, 200.0, 0.0, 100.0)
            .with_data_point_count(50);
        
        ChartBuilder::scatter_chart()
            .title("高密度数据散点图")
            .size(800.0, 600.0)
            .default_x_axis(Some(0.0), Some(200.0))
            .default_y_axis(Some(0.0), Some(100.0))
            .add_scatter_series(
                ScatterSeries::new("高密度数据")
                    .data((0..50).map(|i| {
                        let x = i as f64 * 4.0;
                        let y = (x * 0.1).sin() * 30.0 + 50.0;
                        (x, y)
                    }).collect())
                    .color(Color::rgb(0.3, 0.7, 0.3))
            )
            .build_enhanced()
    }

    /// 主题集成演示
    fn theme_integration_demo() -> EnhancedChart {
        println!("🎨 创建主题集成演示...");
        
        // 使用主题化组件
        let (x_axis, y_axis) = ThemedComponents::light_standard_axes();
        let grid = ThemedComponents::light_responsive_grid();
        
        ChartBuilder::bar_chart()
            .title("主题化柱状图")
            .size(800.0, 600.0)
            .x_axis(x_axis)
            .y_axis(y_axis)
            .add_bar_series(
                BarSeries::new("主题化数据")
                    .data(vec![(0.0, 30.0), (1.0, 45.0), (2.0, 35.0), (3.0, 50.0), (4.0, 40.0)])
                    .color(Color::rgb(0.4, 0.6, 0.8))
            )
            .build_enhanced()
    }

    /// 金融数据图表演示
    fn financial_chart_demo() -> EnhancedChart {
        println!("💰 创建金融数据图表演示...");
        
        // 使用金融图表预设
        let (x_axis, y_axis) = financial_axes();
        let grid = ResponsiveGridPresets::financial_data();
        
        ChartBuilder::line_chart()
            .title("股价走势图")
            .size(800.0, 600.0)
            .x_axis(x_axis)
            .y_axis(y_axis)
            .add_line_series(
                LineSeries::new("股价")
                    .data(vec![
                        (0.0, 100.0), (1.0, 105.0), (2.0, 98.0), (3.0, 110.0), 
                        (4.0, 108.0), (5.0, 115.0), (6.0, 112.0), (7.0, 120.0)
                    ])
                    .color(Color::rgb(0.2, 0.8, 0.2))
                    .line_width(2.0)
            )
            .build_enhanced()
    }

    /// 切换到下一个演示
    fn next_demo(&mut self) {
        self.current_demo = (self.current_demo + 1) % self.demos.len();
        println!("🔄 切换到演示: {}", self.demos[self.current_demo].name);
    }

    /// 切换到上一个演示
    fn prev_demo(&mut self) {
        self.current_demo = if self.current_demo == 0 {
            self.demos.len() - 1
        } else {
            self.current_demo - 1
        };
        println!("🔄 切换到演示: {}", self.demos[self.current_demo].name);
    }

    /// 获取当前演示的图表
    fn current_chart(&self) -> EnhancedChart {
        let demo = &self.demos[self.current_demo];
        println!("📈 生成图表: {}", demo.name);
        println!("📝 描述: {}", demo.description);
        (demo.chart_fn)()
    }
}

impl Render for AdvancedBuilderDemo {
    fn render(&mut self, _cx: &mut ViewContext<Self>) -> impl IntoElement {
        let current_chart = self.current_chart();
        let demo = &self.demos[self.current_demo];

        div()
            .flex()
            .flex_col()
            .size_full()
            .bg(rgb(0xf5f5f5))
            .child(
                // 标题栏
                div()
                    .flex()
                    .items_center()
                    .justify_between()
                    .p_4()
                    .bg(rgb(0x2c3e50))
                    .text_color(rgb(0xffffff))
                    .child(
                        div()
                            .text_xl()
                            .font_bold()
                            .child(format!("高级图表构建器演示 - {}", demo.name))
                    )
                    .child(
                        div()
                            .flex()
                            .gap_2()
                            .child(
                                div()
                                    .px_3()
                                    .py_1()
                                    .bg(rgb(0x3498db))
                                    .rounded_md()
                                    .cursor_pointer()
                                    .child("上一个")
                                    .on_click(cx, |this, _, _| this.prev_demo())
                            )
                            .child(
                                div()
                                    .px_3()
                                    .py_1()
                                    .bg(rgb(0x3498db))
                                    .rounded_md()
                                    .cursor_pointer()
                                    .child("下一个")
                                    .on_click(cx, |this, _, _| this.next_demo())
                            )
                    )
            )
            .child(
                // 描述栏
                div()
                    .p_4()
                    .bg(rgb(0xecf0f1))
                    .border_b_1()
                    .border_color(rgb(0xbdc3c7))
                    .child(demo.description.clone())
            )
            .child(
                // 图表区域
                div()
                    .flex_1()
                    .p_4()
                    .child(
                        EChartsElement::new(
                            EChartsCanvas::from_enhanced_chart(current_chart)
                                .with_debug(true)
                        )
                    )
            )
    }
}

fn main() {
    println!("🚀 启动高级图表构建器演示...");
    
    App::new().run(|cx: &mut AppContext| {
        cx.open_window(
            WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(Bounds {
                    origin: Point { x: px(100.0), y: px(100.0) },
                    size: Size { width: px(1000.0), height: px(800.0) },
                })),
                ..Default::default()
            },
            |cx| cx.new_view(|_cx| AdvancedBuilderDemo::new()),
        );
    });
}
