//! TSDAQ 协议库
//!
//! 提供设备通信、数据解析和协议处理功能。
//!
//! # 主要功能
//!
//! - 设备连接和通信管理
//! - 协议帧编码和解码
//! - 数据解析和处理
//! - 错误处理和日志记录
//!
//! # 示例
//!
//! ```rust
//! use tsdaq_protocol::{DeviceCommand, DeviceResponse, DataProcessor};
//!
//! // 创建数据处理器
//! let processor = DataProcessor::new();
//!
//! // 发送查询命令
//! let query_cmd = DeviceCommand::Query;
//! let frame = query_cmd.into_frame();
//!
//! // 处理响应数据
//! let channels = processor.single_packet_data(&frame.data);
//! ```

pub mod client;
pub mod codec;
pub mod commands;
pub mod device;
pub mod error;
pub mod processor;
pub mod transport;

pub use client::*;
pub use codec::*;
pub use commands::*;
pub use device::*;
pub use error::ProtocolError;
pub use processor::*;
pub use transport::*;

use bytes::Bytes;
use tracing::{debug, error, warn};

/// 从 Bytes 中读取 4 字节并转换为 f32（大端模式）
///
/// # 参数
/// * `data` - 包含浮点数的字节数据
///
/// # 返回值
/// 解析成功的 f32 值 或 错误信息
///
/// # 示例
///
/// ```rust
/// use tsdaq_protocol::parse_f32_be;
/// use bytes::Bytes;
///
/// let data = Bytes::from(vec![0x41, 0x20, 0x00, 0x00]); // 10.0f32 in BE
/// let result = parse_f32_be(&data);
/// assert!(result.is_ok());
/// ```
pub fn parse_f32_be(data: &Bytes) -> Result<f32, String> {
    if data.len() < 4 {
        error!("数据长度不足: 需要4字节，实际 {} 字节", data.len());
        return Err("数据长度不足".to_string());
    }

    let bytes_array: [u8; 4] = data[..4].try_into().map_err(|_| {
        error!("字节转换失败: 数据长度 {}", data.len());
        "字节转换失败".to_string()
    })?;

    let result = f32::from_be_bytes(bytes_array);
    debug!("解析大端浮点数: {} -> {}", data.len(), result);
    Ok(result)
}

/// 从 Bytes 中读取 4 字节并转换为 f32（小端模式）
///
/// # 参数
/// * `data` - 包含浮点数的字节数据
///
/// # 返回值
/// 解析成功的 f32 值 或 错误信息
///
/// # 示例
///
/// ```rust
/// use tsdaq_protocol::parse_f32_le;
/// use bytes::Bytes;
///
/// let data = Bytes::from(vec![0x00, 0x00, 0x20, 0x41]); // 10.0f32 in LE
/// let result = parse_f32_le(&data);
/// assert!(result.is_ok());
/// ```
pub fn parse_f32_le(data: &Bytes) -> Result<f32, String> {
    if data.len() < 4 {
        error!("数据长度不足: 需要4字节，实际 {} 字节", data.len());
        return Err("数据长度不足".to_string());
    }

    let bytes_array: [u8; 4] = data[..4].try_into().map_err(|_| {
        error!("字节转换失败: 数据长度 {}", data.len());
        "字节转换失败".to_string()
    })?;

    let result = f32::from_le_bytes(bytes_array);
    debug!("解析小端浮点数: {} -> {}", data.len(), result);
    Ok(result)
}

/// 连续转换 Bytes 中的所有 4 字节浮点数
///
/// # 参数
/// * `data` - 原始数据切片
/// * `is_big_endian` - 是否使用大端模式（true: 大端，false: 小端）
///
/// # 返回值
/// 解析成功的 f32 向量 或 错误信息
///
/// # 示例
///
/// ```rust
/// use tsdaq_protocol::convert_bytes_to_f32;
/// use bytes::Bytes;
///
/// let data = Bytes::from(vec![0x41, 0x20, 0x00, 0x00, 0x42, 0x48, 0x00, 0x00]);
/// let result = convert_bytes_to_f32(&data, true);
/// assert!(result.is_ok());
/// ```
pub fn convert_bytes_to_f32(data: &Bytes, is_big_endian: bool) -> Result<Vec<f32>, String> {
    if data.len() % 4 != 0 {
        error!("数据长度错误: {} 字节不是4的倍数", data.len());
        return Err("数据长度必须是 4 的整数倍".to_string());
    }

    let mut result = Vec::with_capacity(data.len() / 4);
    let mut i = 0;

    while i + 4 <= data.len() {
        let chunk = &data[i..i + 4];
        let value = match is_big_endian {
            true => f32::from_be_bytes(chunk.try_into().map_err(|_| {
                error!("字节转换失败: 位置 {}, 数据长度 {}", i, data.len());
                "字节转换失败".to_string()
            })?),
            false => f32::from_le_bytes(chunk.try_into().map_err(|_| {
                error!("字节转换失败: 位置 {}, 数据长度 {}", i, data.len());
                "字节转换失败".to_string()
            })?),
        };
        result.push(value);
        i += 4;
    }

    debug!(
        "转换字节到浮点数: {} 字节 -> {} 个浮点数 ({}端)",
        data.len(),
        result.len(),
        if is_big_endian { "大" } else { "小" }
    );

    Ok(result)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_f32_be() {
        let data = Bytes::from(vec![0x41, 0x20, 0x00, 0x00]); // 10.0f32 in BE
        let result = parse_f32_be(&data);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 10.0);
    }

    #[test]
    fn test_parse_f32_le() {
        let data = Bytes::from(vec![0x00, 0x00, 0x20, 0x41]); // 10.0f32 in LE
        let result = parse_f32_le(&data);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 10.0);
    }

    #[test]
    fn test_convert_bytes_to_f32() {
        let data = Bytes::from(vec![
            0x41, 0x20, 0x00, 0x00, // 10.0f32 in BE
            0x42, 0x48, 0x00, 0x00, // 50.0f32 in BE
        ]);
        let result = convert_bytes_to_f32(&data, true);
        assert!(result.is_ok());
        let values = result.unwrap();
        assert_eq!(values.len(), 2);
        assert_eq!(values[0], 10.0);
        assert_eq!(values[1], 50.0);
    }

    #[test]
    fn test_convert_bytes_to_f32_invalid_length() {
        let data = Bytes::from(vec![0x41, 0x20, 0x00]); // 3 bytes
        let result = convert_bytes_to_f32(&data, true);
        assert!(result.is_err());
    }
}
