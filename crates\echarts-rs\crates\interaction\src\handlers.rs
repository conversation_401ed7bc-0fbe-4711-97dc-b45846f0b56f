//! 交互事件处理器
//!
//! 定义事件处理器接口和常用处理器实现

use crate::{InteractionEvent, InteractionResult, InteractionState};
use echarts_core::Result;

/// 交互事件处理器特征
pub trait InteractionHandler: Send + Sync {
    /// 处理交互事件
    fn handle_event(&mut self, event: &InteractionEvent, state: &InteractionState) -> Result<InteractionResult>;
    
    /// 获取处理器名称
    fn name(&self) -> &str;
    
    /// 获取处理器优先级（数值越小优先级越高）
    fn priority(&self) -> i32 {
        0
    }
    
    /// 是否启用
    fn is_enabled(&self) -> bool {
        true
    }
    
    /// 设置启用状态
    fn set_enabled(&mut self, enabled: bool);
}

/// 基础事件处理器
#[derive(Debug)]
pub struct BaseInteractionHandler {
    name: String,
    enabled: bool,
    priority: i32,
}

impl BaseInteractionHandler {
    /// 创建新的基础处理器
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            name: name.into(),
            enabled: true,
            priority: 0,
        }
    }
    
    /// 设置优先级
    pub fn priority(mut self, priority: i32) -> Self {
        self.priority = priority;
        self
    }
}

impl InteractionHandler for BaseInteractionHandler {
    fn handle_event(&mut self, _event: &InteractionEvent, _state: &InteractionState) -> Result<InteractionResult> {
        Ok(InteractionResult::None)
    }
    
    fn name(&self) -> &str {
        &self.name
    }
    
    fn priority(&self) -> i32 {
        self.priority
    }
    
    fn is_enabled(&self) -> bool {
        self.enabled
    }
    
    fn set_enabled(&mut self, enabled: bool) {
        self.enabled = enabled;
    }
}

/// 点击处理器
pub struct ClickHandler {
    base: BaseInteractionHandler,
    on_click: Option<Box<dyn Fn(&InteractionEvent, &InteractionState) -> Result<InteractionResult> + Send + Sync>>,
}

impl ClickHandler {
    /// 创建新的点击处理器
    pub fn new() -> Self {
        Self {
            base: BaseInteractionHandler::new("click_handler"),
            on_click: None,
        }
    }
    
    /// 设置点击回调
    pub fn on_click<F>(mut self, callback: F) -> Self 
    where
        F: Fn(&InteractionEvent, &InteractionState) -> Result<InteractionResult> + Send + Sync + 'static,
    {
        self.on_click = Some(Box::new(callback));
        self
    }
}

impl InteractionHandler for ClickHandler {
    fn handle_event(&mut self, event: &InteractionEvent, state: &InteractionState) -> Result<InteractionResult> {
        if !self.is_enabled() {
            return Ok(InteractionResult::None);
        }
        
        match event {
            InteractionEvent::Mouse(mouse_event) => {
                if matches!(mouse_event.event_type, crate::MouseEventType::Click) {
                    if let Some(callback) = &self.on_click {
                        return callback(event, state);
                    }
                }
            }
            _ => {}
        }
        
        Ok(InteractionResult::None)
    }
    
    fn name(&self) -> &str {
        self.base.name()
    }
    
    fn priority(&self) -> i32 {
        self.base.priority
    }

    fn is_enabled(&self) -> bool {
        self.base.enabled
    }

    fn set_enabled(&mut self, enabled: bool) {
        self.base.enabled = enabled;
    }
}

/// 悬停处理器
pub struct HoverHandler {
    base: BaseInteractionHandler,
    on_hover: Option<Box<dyn Fn(&InteractionEvent, &InteractionState) -> Result<InteractionResult> + Send + Sync>>,
    on_leave: Option<Box<dyn Fn(&InteractionEvent, &InteractionState) -> Result<InteractionResult> + Send + Sync>>,
}

impl HoverHandler {
    /// 创建新的悬停处理器
    pub fn new() -> Self {
        Self {
            base: BaseInteractionHandler::new("hover_handler"),
            on_hover: None,
            on_leave: None,
        }
    }
    
    /// 设置悬停回调
    pub fn on_hover<F>(mut self, callback: F) -> Self 
    where
        F: Fn(&InteractionEvent, &InteractionState) -> Result<InteractionResult> + Send + Sync + 'static,
    {
        self.on_hover = Some(Box::new(callback));
        self
    }
    
    /// 设置离开回调
    pub fn on_leave<F>(mut self, callback: F) -> Self 
    where
        F: Fn(&InteractionEvent, &InteractionState) -> Result<InteractionResult> + Send + Sync + 'static,
    {
        self.on_leave = Some(Box::new(callback));
        self
    }
}

impl InteractionHandler for HoverHandler {
    fn handle_event(&mut self, event: &InteractionEvent, state: &InteractionState) -> Result<InteractionResult> {
        if !self.is_enabled() {
            return Ok(InteractionResult::None);
        }
        
        match event {
            InteractionEvent::Mouse(mouse_event) => {
                match mouse_event.event_type {
                    crate::MouseEventType::Enter => {
                        if let Some(callback) = &self.on_hover {
                            return callback(event, state);
                        }
                    }
                    crate::MouseEventType::Leave => {
                        if let Some(callback) = &self.on_leave {
                            return callback(event, state);
                        }
                    }
                    _ => {}
                }
            }
            _ => {}
        }
        
        Ok(InteractionResult::None)
    }
    
    fn name(&self) -> &str {
        self.base.name()
    }
    
    fn priority(&self) -> i32 {
        self.base.priority
    }

    fn is_enabled(&self) -> bool {
        self.base.enabled
    }

    fn set_enabled(&mut self, enabled: bool) {
        self.base.enabled = enabled;
    }
}

/// 拖拽处理器
pub struct DragHandler {
    base: BaseInteractionHandler,
    on_drag_start: Option<Box<dyn Fn(&InteractionEvent, &InteractionState) -> Result<InteractionResult> + Send + Sync>>,
    on_drag: Option<Box<dyn Fn(&InteractionEvent, &InteractionState) -> Result<InteractionResult> + Send + Sync>>,
    on_drag_end: Option<Box<dyn Fn(&InteractionEvent, &InteractionState) -> Result<InteractionResult> + Send + Sync>>,
}

impl DragHandler {
    /// 创建新的拖拽处理器
    pub fn new() -> Self {
        Self {
            base: BaseInteractionHandler::new("drag_handler"),
            on_drag_start: None,
            on_drag: None,
            on_drag_end: None,
        }
    }
    
    /// 设置拖拽开始回调
    pub fn on_drag_start<F>(mut self, callback: F) -> Self 
    where
        F: Fn(&InteractionEvent, &InteractionState) -> Result<InteractionResult> + Send + Sync + 'static,
    {
        self.on_drag_start = Some(Box::new(callback));
        self
    }
    
    /// 设置拖拽回调
    pub fn on_drag<F>(mut self, callback: F) -> Self 
    where
        F: Fn(&InteractionEvent, &InteractionState) -> Result<InteractionResult> + Send + Sync + 'static,
    {
        self.on_drag = Some(Box::new(callback));
        self
    }
    
    /// 设置拖拽结束回调
    pub fn on_drag_end<F>(mut self, callback: F) -> Self 
    where
        F: Fn(&InteractionEvent, &InteractionState) -> Result<InteractionResult> + Send + Sync + 'static,
    {
        self.on_drag_end = Some(Box::new(callback));
        self
    }
}

impl InteractionHandler for DragHandler {
    fn handle_event(&mut self, event: &InteractionEvent, state: &InteractionState) -> Result<InteractionResult> {
        if !self.is_enabled() {
            return Ok(InteractionResult::None);
        }
        
        match event {
            InteractionEvent::Mouse(mouse_event) => {
                match mouse_event.event_type {
                    crate::MouseEventType::Down => {
                        if let Some(callback) = &self.on_drag_start {
                            return callback(event, state);
                        }
                    }
                    crate::MouseEventType::Move => {
                        if state.is_dragging {
                            if let Some(callback) = &self.on_drag {
                                return callback(event, state);
                            }
                        }
                    }
                    crate::MouseEventType::Up => {
                        if state.is_dragging {
                            if let Some(callback) = &self.on_drag_end {
                                return callback(event, state);
                            }
                        }
                    }
                    _ => {}
                }
            }
            _ => {}
        }
        
        Ok(InteractionResult::None)
    }
    
    fn name(&self) -> &str {
        self.base.name()
    }
    
    fn priority(&self) -> i32 {
        self.base.priority
    }
    
    fn is_enabled(&self) -> bool {
        self.base.is_enabled()
    }
    
    fn set_enabled(&mut self, enabled: bool) {
        self.base.set_enabled(enabled);
    }
}

/// 键盘处理器
pub struct KeyboardHandler {
    base: BaseInteractionHandler,
    key_handlers: std::collections::HashMap<String, Box<dyn Fn(&InteractionEvent, &InteractionState) -> Result<InteractionResult> + Send + Sync>>,
}

impl KeyboardHandler {
    /// 创建新的键盘处理器
    pub fn new() -> Self {
        Self {
            base: BaseInteractionHandler::new("keyboard_handler"),
            key_handlers: std::collections::HashMap::new(),
        }
    }
    
    /// 添加按键处理器
    pub fn add_key_handler<F>(mut self, key: impl Into<String>, callback: F) -> Self 
    where
        F: Fn(&InteractionEvent, &InteractionState) -> Result<InteractionResult> + Send + Sync + 'static,
    {
        self.key_handlers.insert(key.into(), Box::new(callback));
        self
    }
}

impl InteractionHandler for KeyboardHandler {
    fn handle_event(&mut self, event: &InteractionEvent, state: &InteractionState) -> Result<InteractionResult> {
        if !self.is_enabled() {
            return Ok(InteractionResult::None);
        }
        
        match event {
            InteractionEvent::Key(key_event) => {
                if let Some(callback) = self.key_handlers.get(&key_event.key) {
                    return callback(event, state);
                }
            }
            _ => {}
        }
        
        Ok(InteractionResult::None)
    }
    
    fn name(&self) -> &str {
        self.base.name()
    }
    
    fn priority(&self) -> i32 {
        self.base.priority
    }
    
    fn is_enabled(&self) -> bool {
        self.base.is_enabled()
    }
    
    fn set_enabled(&mut self, enabled: bool) {
        self.base.set_enabled(enabled);
    }
}

/// 处理器管理器
pub struct HandlerManager {
    handlers: Vec<Box<dyn InteractionHandler>>,
}

impl HandlerManager {
    /// 创建新的处理器管理器
    pub fn new() -> Self {
        Self {
            handlers: Vec::new(),
        }
    }
    
    /// 添加处理器
    pub fn add_handler(&mut self, handler: Box<dyn InteractionHandler>) {
        self.handlers.push(handler);
        // 按优先级排序
        self.handlers.sort_by_key(|h| h.priority());
    }
    
    /// 移除处理器
    pub fn remove_handler(&mut self, name: &str) {
        self.handlers.retain(|h| h.name() != name);
    }
    
    /// 处理事件
    pub fn handle_event(&mut self, event: &InteractionEvent, state: &InteractionState) -> Result<InteractionResult> {
        for handler in &mut self.handlers {
            if handler.is_enabled() {
                let result = handler.handle_event(event, state)?;
                if !matches!(result, InteractionResult::None) {
                    return Ok(result);
                }
            }
        }
        Ok(InteractionResult::None)
    }
    
    /// 启用/禁用处理器
    pub fn set_handler_enabled(&mut self, name: &str, enabled: bool) {
        for handler in &mut self.handlers {
            if handler.name() == name {
                handler.set_enabled(enabled);
                break;
            }
        }
    }
    
    /// 获取处理器列表
    pub fn get_handlers(&self) -> Vec<&str> {
        self.handlers.iter().map(|h| h.name()).collect()
    }
}

impl Default for HandlerManager {
    fn default() -> Self {
        Self::new()
    }
}
