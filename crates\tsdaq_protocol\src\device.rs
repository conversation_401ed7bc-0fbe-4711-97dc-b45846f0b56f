use crate::commands::{DeviceCommand, DeviceResponse};
use crate::{Frame, FrameCodec, ProtocolError, Transport};
use async_trait::async_trait;
use bytes::{Buf, BytesMut};
use std::collections::VecDeque;
use std::sync::atomic::{AtomicU32, Ordering};
use std::sync::Arc;
use std::sync::Mutex as StdMutex;
use std::time::Duration;
use tracing::{debug, error, info, trace, warn};

/// 设备状态统计信息
#[derive(Debug, Default, Clone)]
pub struct DeviceStats {
    /// 发送的帧数
    pub frames_sent: u64,
    /// 接收的帧数
    pub frames_received: u64,
    /// 失败的帧数
    pub frames_failed: u64,
    /// 校验错误数
    pub checksum_errors: u64,
    /// 解析错误数
    pub parse_errors: u64,
    /// 超时错误数
    pub timeouts: u64,
    /// 最后错误信息
    pub last_error: Option<String>,
}

#[async_trait]
pub trait Device: Send + Sync {
    async fn send_frame(&mut self, frame: Frame) -> Result<(), ProtocolError>;
    async fn recv_frame(&mut self, timeout: Duration) -> Result<Frame, ProtocolError>;
    async fn execute_command(
        &mut self,
        command: DeviceCommand,
    ) -> Result<DeviceResponse, ProtocolError>;
    fn get_stats(&self) -> DeviceStats;
}

/// 设备实现，处理设备通信协议
pub struct TSDevice {
    transport: Box<dyn Transport>,
    codec: Box<dyn FrameCodec>,
    buffer: BytesMut,
    recv_timeout: Duration,
    stats: Arc<StdMutex<DeviceStats>>,
    command_queue: VecDeque<DeviceCommand>,
    max_buffer_size: usize,
    frame_id_counter: AtomicU32,
}

impl std::fmt::Debug for TSDevice {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("TSDevice")
            .field("buffer_len", &self.buffer.len())
            .field("recv_timeout", &self.recv_timeout)
            .field("stats", &self.stats)
            .field("command_queue_len", &self.command_queue.len())
            .field("max_buffer_size", &self.max_buffer_size)
            .field("frame_id_counter", &self.frame_id_counter)
            .finish()
    }
}

impl TSDevice {
    pub fn new(transport: Box<dyn Transport>, codec: Box<dyn FrameCodec>) -> Self {
        Self {
            transport,
            codec,
            buffer: BytesMut::with_capacity(8192), // 增大初始缓冲区
            recv_timeout: Duration::from_millis(100),
            stats: Arc::new(StdMutex::new(DeviceStats::default())),
            command_queue: VecDeque::with_capacity(16),
            max_buffer_size: 65536, // 设置最大缓冲区大小
            frame_id_counter: AtomicU32::new(0),
        }
    }

    pub fn set_codec(&mut self, codec: Box<dyn FrameCodec>) {
        self.codec = codec;
    }

    pub fn set_recv_timeout(&mut self, timeout: Duration) {
        self.recv_timeout = timeout;
    }

    pub fn clear_buffer(&mut self) {
        self.buffer.clear();
    }

    /// 增加最大缓冲区大小
    pub fn set_max_buffer_size(&mut self, size: usize) {
        self.max_buffer_size = size;
    }

    /// 获取下一个帧ID（递增）
    fn next_frame_id(&self) -> u32 {
        self.frame_id_counter.fetch_add(1, Ordering::Relaxed)
    }

    /// 检查缓冲区大小并适当处理
    fn check_buffer_size(&mut self) {
        if self.buffer.len() > self.max_buffer_size {
            warn!(
                "缓冲区大小超过阈值 ({}/{}), 正在截断",
                self.buffer.len(),
                self.max_buffer_size
            );

            // 尝试找到有效帧
            let mut found_valid_header = false;

            // 检查缓冲区是否包含有效帧头
            for i in 0..self.buffer.len().saturating_sub(2) {
                if (self.buffer[i] == 0xDD && self.buffer[i + 1] == 0xAA) || self.buffer[i] == 0xDA
                {
                    self.buffer.advance(i); // 保留从有效帧头开始的数据
                    found_valid_header = true;
                    break;
                }
            }

            // 如果没有找到有效帧头，保留最后1KB数据
            if !found_valid_header {
                let preserve_size = 1024.min(self.buffer.len());
                let advance_by = self.buffer.len() - preserve_size;
                self.buffer.advance(advance_by);
            }

            debug!("截断后缓冲区大小: {} 字节", self.buffer.len());
        }
    }

    /// 更新设备状态统计
    fn update_stats<F>(&self, updater: F)
    where
        F: FnOnce(&mut DeviceStats),
    {
        if let Ok(mut stats) = self.stats.lock() {
            updater(&mut stats);
        } else {
            error!("无法获取设备统计信息锁");
        }
    }
}

#[async_trait]
impl Device for TSDevice {
    async fn send_frame(&mut self, frame: Frame) -> Result<(), ProtocolError> {
        // 创建临时缓冲区用于编码
        let mut buf = BytesMut::with_capacity(frame.data.len() + 16);
        trace!("正在编码帧: {:?}", frame);
        self.codec.encode(&frame, &mut buf);

        match self.transport.send(&buf).await {
            Ok(_) => {
                // 更新统计信息
                self.update_stats(|stats| {
                    stats.frames_sent += 1;
                });
                trace!("发送帧成功 ({} 字节)", buf.len());
                Ok(())
            }
            Err(e) => {
                // 更新统计信息
                self.update_stats(|stats| {
                    stats.frames_failed += 1;
                    stats.last_error = Some(format!("发送错误: {}", e));
                });
                error!("发送帧失败: {}", e);
                Err(e)
            }
        }
    }

    async fn recv_frame(&mut self, timeout: Duration) -> Result<Frame, ProtocolError> {
        let mut temp_buf = [0u8; 8192]; // 增大临时缓冲区

        // 使用传入的超时或默认值
        let timeout = if timeout == Duration::ZERO {
            self.recv_timeout
        } else {
            timeout
        };

        // 最多尝试10次解码
        for attempt in 1..=10 {
            // 首先检查缓冲区大小，防止内存泄漏
            self.check_buffer_size();

            // 尝试从缓冲区解析帧
            match self.codec.decode(&mut self.buffer) {
                Ok(Some(frame)) => {
                    // 更新统计信息
                    self.update_stats(|stats| {
                        stats.frames_received += 1;
                    });
                    trace!("成功解码帧: {:?}", frame);
                    return Ok(frame);
                }
                Ok(None) => {} // 继续读取
                Err(ProtocolError::ChecksumMismatch) => {
                    // 校验和错误处理
                    self.update_stats(|stats| {
                        stats.checksum_errors += 1;
                        stats.last_error = Some("校验和错误".to_string());
                    });

                    warn!("校验和错误，尝试恢复 (尝试 {}/10)", attempt);

                    if self.buffer.len() > 0 {
                        // 移除一个字节，尝试重新找到有效帧
                        self.buffer.advance(1);
                    }
                }
                Err(ProtocolError::FrameHeaderError) => {
                    // 帧头错误处理
                    self.update_stats(|stats| {
                        stats.parse_errors += 1;
                        stats.last_error = Some("帧头错误".to_string());
                    });

                    warn!("帧头错误，尝试恢复 (尝试 {}/10)", attempt);
                    self.codec.delete_invalid_frame_header(&mut self.buffer);
                }
                Err(e) => {
                    self.update_stats(|stats| {
                        stats.frames_failed += 1;
                        stats.last_error = Some(format!("解析错误: {}", e));
                    });
                    return Err(e);
                }
            }

            // 需要更多数据时，读取设备输入
            let read_result = self.transport.recv(&mut temp_buf, timeout).await;

            match read_result {
                Ok(n) => {
                    trace!("读取 {} 字节数据", n);
                    if n == 0 {
                        // 零字节读取可能意味着连接关闭
                        return Err(ProtocolError::Io(std::io::Error::new(
                            std::io::ErrorKind::ConnectionAborted,
                            "连接断开",
                        )));
                    }
                    // 追加数据到缓冲区
                    self.buffer.extend_from_slice(&temp_buf[..n]);
                }
                Err(ProtocolError::Timeout) => {
                    // 超时不一定是错误，但应该记录
                    self.update_stats(|stats| {
                        stats.timeouts += 1;
                    });
                    debug!("接收超时 ({:?})", timeout);
                    return Err(ProtocolError::Timeout);
                }
                Err(e) => {
                    // // 其它IO错误
                    // self.update_stats(|stats| {
                    //     stats.frames_failed += 1;
                    //     stats.last_error = Some(format!("接收错误: {}", e));
                    // });
                    // error!("接收数据出错: {}", e);
                    return Err(e);
                }
            }
        }

        // 如果多次尝试后仍无法解析有效帧，返回错误
        self.update_stats(|stats| {
            stats.frames_failed += 1;
            stats.last_error = Some("无法解析有效帧，超过最大尝试次数".to_string());
        });

        Err(ProtocolError::Frame(
            "无法解析有效帧，超过最大尝试次数".to_string(),
        ))
    }

    async fn execute_command(
        &mut self,
        command: DeviceCommand,
    ) -> Result<DeviceResponse, ProtocolError> {
        // 添加命令到队列以便跟踪
        self.command_queue.push_back(command.clone());
        if self.command_queue.len() > 100 {
            self.command_queue.pop_front(); // 防止队列无限增长
        }

        // 根据命令类型设置不同的超时
        let timeout = match &command {
            DeviceCommand::Query => Duration::from_millis(2000), // 增加超时
            DeviceCommand::StartCollection => Duration::from_millis(5000), // 大幅增加超时
            DeviceCommand::StopCollection => Duration::from_millis(5000), // 大幅增加超时
            _ => Duration::from_millis(2000),
        };

        debug!("执行命令: {:?}", command);

        // 获取帧ID并构建帧
        let frame_id = self.next_frame_id();
        let frame = command.clone().into_frame();

        // 发送命令帧
        self.send_frame(frame).await?;

        // 等待并过滤响应，直到找到正确的命令响应
        let max_attempts = 20; // 增加尝试次数
        for attempt in 1..=max_attempts {
            match self.recv_frame(timeout).await {
                Ok(frame) => {
                    let response = DeviceResponse::from_frame(frame)?;

                    // 检查是否是命令响应而不是数据帧
                    match &response {
                        DeviceResponse::Ack { .. } => {
                            // 这是命令响应，验证它
                            command.validate_response(&response)?;
                            debug!("收到命令响应: {:?}", response);
                            return Ok(response);
                        }
                        DeviceResponse::Error(_) => {
                            // 这是错误响应
                            debug!("收到错误响应: {:?}", response);
                            return Ok(response);
                        }
                        DeviceResponse::Custom(custom_frame) => {
                            // 检查是否是数据帧（function = 0 且 header = [0, 0xDA]）
                            if custom_frame.function == 0 && custom_frame.header == [0, 0xDA] {
                                debug!(
                                    "跳过数据帧 (尝试 {}/{}): function=0x{:02X}",
                                    attempt, max_attempts, custom_frame.function
                                );
                                continue; // 跳过数据帧，继续等待命令响应
                            } else {
                                // 可能是其他类型的响应
                                debug!("收到自定义响应: {:?}", response);
                                return Ok(response);
                            }
                        }
                        _ => {
                            // 其他类型的响应
                            debug!("收到其他响应: {:?}", response);
                            return Ok(response);
                        }
                    }
                }
                Err(ProtocolError::Timeout) => {
                    if attempt < max_attempts {
                        warn!("命令响应超时 (尝试 {}/{}), 继续等待", attempt, max_attempts);
                        continue;
                    } else {
                        error!("命令执行超时，已尝试 {} 次", max_attempts);
                        return Err(ProtocolError::Timeout);
                    }
                }
                Err(e) => {
                    error!("接收响应失败: {}", e);
                    return Err(e);
                }
            }
        }

        Err(ProtocolError::Timeout)
    }

    fn get_stats(&self) -> DeviceStats {
        self.stats.lock().map(|s| s.clone()).unwrap_or_default()
    }
}
