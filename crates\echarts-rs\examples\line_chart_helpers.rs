//! 曲线图辅助函数
//!
//! 包含各种特殊效果的曲线图生成函数

/// 创建阴影效果曲线图
pub fn create_shadow_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 定义阴影滤镜
    svg.push_str("  <defs>\n");
    svg.push_str("    <filter id=\"bigShadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n");
    svg.push_str("      <feDropShadow dx=\"4\" dy=\"4\" stdDeviation=\"4\" flood-color=\"#000000\" flood-opacity=\"0.4\"/>\n");
    svg.push_str("    </filter>\n");
    svg.push_str("    <filter id=\"glow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n");
    svg.push_str("      <feGaussianBlur stdDeviation=\"3\" result=\"coloredBlur\"/>\n");
    svg.push_str("      <feMerge>\n");
    svg.push_str("        <feMergeNode in=\"coloredBlur\"/>\n");
    svg.push_str("        <feMergeNode in=\"SourceGraphic\"/>\n");
    svg.push_str("      </feMerge>\n");
    svg.push_str("    </filter>\n");
    svg.push_str("  </defs>\n");
    
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));
    
    if !data.is_empty() {
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        
        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };
        
        // 生成路径
        let mut path = String::from("M");
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        // 绘制带阴影的曲线
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#3498db\" stroke-width=\"4\" fill=\"none\" filter=\"url(#bigShadow)\"/>\n", path));
        
        // 绘制发光数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"6\" fill=\"#3498db\" stroke=\"white\" stroke-width=\"2\" filter=\"url(#glow)\"/>\n", px, py));
        }
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建多系列对比曲线图
pub fn create_multi_series_line_chart(title: &str, width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 200.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));
    
    // 多个产品数据
    let series_data = vec![
        ("产品A", (0..=12).map(|i| (i as f64, 100.0 + (i as f64 * 0.5).sin() * 30.0 + i as f64 * 5.0)).collect::<Vec<_>>(), "e74c3c"),
        ("产品B", (0..=12).map(|i| (i as f64, 150.0 + (i as f64 * 0.3).cos() * 40.0 + i as f64 * 3.0)).collect::<Vec<_>>(), "3498db"),
        ("产品C", (0..=12).map(|i| (i as f64, 80.0 + (i as f64 * 0.7).sin() * 25.0 + i as f64 * 7.0)).collect::<Vec<_>>(), "2ecc71"),
        ("产品D", (0..=12).map(|i| (i as f64, 120.0 + (i as f64 * 0.4).cos() * 35.0 + i as f64 * 4.0)).collect::<Vec<_>>(), "f39c12"),
        ("产品E", (0..=12).map(|i| (i as f64, 90.0 + (i as f64 * 0.6).sin() * 20.0 + i as f64 * 6.0)).collect::<Vec<_>>(), "9b59b6"),
    ];
    
    let max_value = 300.0;
    let max_x = 12.0;
    
    // 网格线
    for i in 0..=6 {
        let x = chart_x + (i as f64 / 6.0) * chart_width;
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", x, chart_y, x, chart_y + chart_height));
    }
    
    // 绘制每个系列
    for (name, data, color) in &series_data {
        let mut path = String::from("M");
        
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x / max_x) * chart_width;
            let py = chart_y + chart_height - (y / max_value) * chart_height;
            
            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#{}\" stroke-width=\"3\" fill=\"none\"/>\n", path, color));
        
        // 绘制数据点
        for (x, y) in data {
            let px = chart_x + (x / max_x) * chart_width;
            let py = chart_y + chart_height - (y / max_value) * chart_height;
            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#{}\" stroke=\"white\" stroke-width=\"2\"/>\n", px, py, color));
        }
    }
    
    // 图例
    for (i, (name, _, color)) in series_data.iter().enumerate() {
        let y = chart_y + 30.0 + i as f64 * 25.0;
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#{}\" stroke-width=\"3\"/>\n", chart_x + chart_width + 20.0, y, chart_x + chart_width + 40.0, y, color));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">{}</text>\n", chart_x + chart_width + 45.0, y + 4.0, name));
    }
    
    // 坐标轴标签
    for i in 0..=12 {
        let x = chart_x + (i as f64 / 12.0) * chart_width;
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{}月</text>\n", x, chart_y + chart_height + 20.0, i + 1));
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建面积填充曲线图
pub fn create_area_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 定义渐变
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"areaFill\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#74b9ff;stop-opacity:0.8\" />\n");
    svg.push_str("      <stop offset=\"50%\" style=\"stop-color:#0984e3;stop-opacity:0.6\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#74b9ff;stop-opacity:0.2\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");
    
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));
    
    if !data.is_empty() {
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        
        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };
        
        // 生成面积路径
        let mut area_path = String::new();
        area_path.push_str(&format!("M {} {}", chart_x, chart_y + chart_height));
        
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            area_path.push_str(&format!(" L {} {}", px, py));
        }
        
        let last_px = chart_x + (data.last().unwrap().0 - min_x) / x_range * chart_width;
        area_path.push_str(&format!(" L {} {} Z", last_px, chart_y + chart_height));
        
        // 绘制面积
        svg.push_str(&format!("  <path d=\"{}\" fill=\"url(#areaFill)\"/>\n", area_path));
        
        // 生成线条路径
        let mut line_path = String::from("M");
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            if i == 0 {
                line_path.push_str(&format!(" {} {}", px, py));
            } else {
                line_path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        // 绘制边界线
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#0984e3\" stroke-width=\"3\" fill=\"none\"/>\n", line_path));
        
        // 绘制数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#0984e3\" stroke=\"white\" stroke-width=\"2\"/>\n", px, py));
        }
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建阶梯曲线图
pub fn create_step_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));
    
    if !data.is_empty() && data.len() > 1 {
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        
        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };
        
        // 绘制阶梯路径
        let mut path = String::new();
        for i in 0..data.len() {
            let (x, y) = data[i];
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            if i == 0 {
                path.push_str(&format!("M {} {}", px, py));
            } else {
                let (prev_x, _) = data[i - 1];
                let prev_px = chart_x + (prev_x - min_x) / x_range * chart_width;
                path.push_str(&format!(" L {} {} L {} {}", prev_px, py, px, py));
            }
        }
        
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#f39c12\" stroke-width=\"3\" fill=\"none\"/>\n", path));
        
        // 绘制数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#f39c12\" stroke=\"white\" stroke-width=\"2\"/>\n", px, py));
        }
    }
    
    svg.push_str("</svg>");
    svg
}
