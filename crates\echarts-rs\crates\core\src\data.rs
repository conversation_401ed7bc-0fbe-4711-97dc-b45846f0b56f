//! Data structures and types for chart data

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::hash::{Hash, Hasher};

/// Generic data point that can hold various types of values
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum DataValue {
    /// Numeric value
    Number(f64),
    /// String/category value
    String(String),
    /// Date/time value
    DateTime(DateTime<Utc>),
    /// Boolean value
    Boolean(bool),
    /// Point with x, y coordinates
    Point(f64, f64),
    /// Named value (for pie charts, etc.)
    NameValue(String, f64),
    /// Null/missing value
    Null,
}

impl DataValue {
    /// Convert to f64 if possible
    pub fn as_number(&self) -> Option<f64> {
        match self {
            DataValue::Number(n) => Some(*n),
            DataValue::Boolean(b) => Some(if *b { 1.0 } else { 0.0 }),
            DataValue::DateTime(dt) => Some(dt.timestamp() as f64),
            _ => None,
        }
    }

    /// Convert to string
    pub fn as_string(&self) -> String {
        match self {
            DataValue::Number(n) => n.to_string(),
            DataValue::String(s) => s.clone(),
            DataValue::DateTime(dt) => dt.to_rfc3339(),
            DataValue::Boolean(b) => b.to_string(),
            DataValue::Point(x, y) => format!("({}, {})", x, y),
            DataValue::NameValue(name, value) => format!("{}: {}", name, value),
            DataValue::Null => "null".to_string(),
        }
    }

    /// Check if value is numeric
    pub fn is_numeric(&self) -> bool {
        matches!(
            self,
            DataValue::Number(_) | DataValue::Boolean(_) | DataValue::DateTime(_)
        )
    }

    /// Check if value is null/missing
    pub fn is_null(&self) -> bool {
        matches!(self, DataValue::Null)
    }
}

impl Hash for DataValue {
    fn hash<H: Hasher>(&self, state: &mut H) {
        match self {
            DataValue::Number(n) => {
                0u8.hash(state);
                n.to_bits().hash(state);
            }
            DataValue::String(s) => {
                1u8.hash(state);
                s.hash(state);
            }
            DataValue::DateTime(dt) => {
                2u8.hash(state);
                dt.timestamp().hash(state);
                dt.timestamp_subsec_nanos().hash(state);
            }
            DataValue::Boolean(b) => {
                3u8.hash(state);
                b.hash(state);
            }
            DataValue::Point(x, y) => {
                4u8.hash(state);
                x.to_bits().hash(state);
                y.to_bits().hash(state);
            }
            DataValue::NameValue(name, value) => {
                5u8.hash(state);
                name.hash(state);
                value.to_bits().hash(state);
            }
            DataValue::Null => {
                6u8.hash(state);
            }
        }
    }
}

impl From<f64> for DataValue {
    fn from(value: f64) -> Self {
        DataValue::Number(value)
    }
}

impl From<i32> for DataValue {
    fn from(value: i32) -> Self {
        DataValue::Number(value as f64)
    }
}

impl From<String> for DataValue {
    fn from(value: String) -> Self {
        DataValue::String(value)
    }
}

impl From<&str> for DataValue {
    fn from(value: &str) -> Self {
        DataValue::String(value.to_string())
    }
}

impl From<bool> for DataValue {
    fn from(value: bool) -> Self {
        DataValue::Boolean(value)
    }
}

impl From<DateTime<Utc>> for DataValue {
    fn from(value: DateTime<Utc>) -> Self {
        DataValue::DateTime(value)
    }
}

/// A single data point with multiple dimensions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataPoint {
    /// Values for each dimension
    pub values: Vec<DataValue>,

    /// Optional name/label for this data point
    pub name: Option<String>,

    /// Custom properties for this data point
    pub properties: HashMap<String, DataValue>,
}

impl DataPoint {
    /// Create a new data point
    pub fn new(values: Vec<DataValue>) -> Self {
        Self {
            values,
            name: None,
            properties: HashMap::new(),
        }
    }

    /// Create a data point with a name
    pub fn with_name(values: Vec<DataValue>, name: String) -> Self {
        Self {
            values,
            name: Some(name),
            properties: HashMap::new(),
        }
    }

    /// Get value by dimension index
    pub fn get_value(&self, dimension: usize) -> Option<&DataValue> {
        self.values.get(dimension)
    }

    /// Get numeric value by dimension index
    pub fn get_number(&self, dimension: usize) -> Option<f64> {
        self.get_value(dimension)?.as_number()
    }

    /// Set a custom property
    pub fn set_property<K: Into<String>, V: Into<DataValue>>(mut self, key: K, value: V) -> Self {
        self.properties.insert(key.into(), value.into());
        self
    }

    /// Get a custom property
    pub fn get_property(&self, key: &str) -> Option<&DataValue> {
        self.properties.get(key)
    }

    /// Get X coordinate (first dimension) as f64
    pub fn x(&self) -> f64 {
        self.get_number(0).unwrap_or(0.0)
    }

    /// Get Y coordinate (second dimension) as f64
    pub fn y(&self) -> f64 {
        self.get_number(1).unwrap_or(0.0)
    }
}

impl Hash for DataPoint {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.values.hash(state);
        self.name.hash(state);
        // For properties HashMap, we need to hash in a deterministic way
        let mut props: Vec<_> = self.properties.iter().collect();
        props.sort_by_key(|(k, _)| *k);
        for (k, v) in props {
            k.hash(state);
            v.hash(state);
        }
    }
}

/// Collection of data points forming a dataset
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataSet {
    /// Data points
    pub points: Vec<DataPoint>,

    /// Dimension names/labels
    pub dimensions: Vec<String>,

    /// Dataset metadata
    pub metadata: HashMap<String, DataValue>,
}

impl DataSet {
    /// Create a new empty dataset
    pub fn new() -> Self {
        Self {
            points: Vec::new(),
            dimensions: Vec::new(),
            metadata: HashMap::new(),
        }
    }

    /// Create dataset with dimensions
    pub fn with_dimensions(dimensions: Vec<String>) -> Self {
        Self {
            points: Vec::new(),
            dimensions,
            metadata: HashMap::new(),
        }
    }

    /// Add a data point
    pub fn add_point(mut self, point: DataPoint) -> Self {
        self.points.push(point);
        self
    }

    /// Add multiple data points
    pub fn add_points(mut self, points: Vec<DataPoint>) -> Self {
        self.points.extend(points);
        self
    }

    /// Get number of data points
    pub fn len(&self) -> usize {
        self.points.len()
    }

    /// Check if dataset is empty
    pub fn is_empty(&self) -> bool {
        self.points.is_empty()
    }

    /// Get data point by index
    pub fn get_point(&self, index: usize) -> Option<&DataPoint> {
        self.points.get(index)
    }

    /// Get all data points
    pub fn points(&self) -> &Vec<DataPoint> {
        &self.points
    }

    /// Get dimensions
    pub fn dimensions(&self) -> &Vec<String> {
        &self.dimensions
    }

    /// Get all values for a specific dimension
    pub fn get_dimension_values(&self, dimension: usize) -> Vec<&DataValue> {
        self.points
            .iter()
            .filter_map(|point| point.get_value(dimension))
            .collect()
    }

    /// Get numeric range for a dimension
    pub fn get_numeric_range(&self, dimension: usize) -> Option<(f64, f64)> {
        let values: Vec<f64> = self
            .points
            .iter()
            .filter_map(|point| point.get_number(dimension))
            .collect();

        if values.is_empty() {
            return None;
        }

        let min = values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max = values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));

        Some((min, max))
    }

    /// Filter data points by predicate
    pub fn filter<F>(&self, predicate: F) -> DataSet
    where
        F: Fn(&DataPoint) -> bool,
    {
        DataSet {
            points: self
                .points
                .iter()
                .filter(|p| predicate(p))
                .cloned()
                .collect(),
            dimensions: self.dimensions.clone(),
            metadata: self.metadata.clone(),
        }
    }

    /// Sort data points by a dimension
    pub fn sort_by_dimension(&mut self, dimension: usize, ascending: bool) {
        self.points.sort_by(|a, b| {
            let a_val = a.get_value(dimension);
            let b_val = b.get_value(dimension);

            match (a_val, b_val) {
                (Some(DataValue::Number(a)), Some(DataValue::Number(b))) => {
                    if ascending {
                        a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal)
                    } else {
                        b.partial_cmp(a).unwrap_or(std::cmp::Ordering::Equal)
                    }
                }
                (Some(DataValue::String(a)), Some(DataValue::String(b))) => {
                    if ascending {
                        a.cmp(b)
                    } else {
                        b.cmp(a)
                    }
                }
                (Some(DataValue::DateTime(a)), Some(DataValue::DateTime(b))) => {
                    if ascending {
                        a.cmp(b)
                    } else {
                        b.cmp(a)
                    }
                }
                _ => std::cmp::Ordering::Equal,
            }
        });
    }
}

impl Default for DataSet {
    fn default() -> Self {
        Self::new()
    }
}

/// Convenient constructors for common data patterns
impl DataSet {
    /// Create dataset from (x, y) numeric pairs
    pub fn from_xy_pairs<I>(data: I) -> Self
    where
        I: IntoIterator<Item = (f64, f64)>,
    {
        let points: Vec<DataPoint> = data
            .into_iter()
            .map(|(x, y)| DataPoint::new(vec![x.into(), y.into()]))
            .collect();

        DataSet::with_dimensions(vec!["x".to_string(), "y".to_string()]).add_points(points)
    }

    /// Create dataset from (category, value) pairs
    pub fn from_category_value_pairs<I, S>(data: I) -> Self
    where
        I: IntoIterator<Item = (S, f64)>,
        S: Into<String>,
    {
        let points: Vec<DataPoint> = data
            .into_iter()
            .map(|(cat, val)| DataPoint::new(vec![cat.into().into(), val.into()]))
            .collect();

        DataSet::with_dimensions(vec!["category".to_string(), "value".to_string()])
            .add_points(points)
    }

    /// Create dataset from named values (for pie charts)
    pub fn from_named_values<I, S>(data: I) -> Self
    where
        I: IntoIterator<Item = (S, f64)>,
        S: Into<String>,
    {
        let points: Vec<DataPoint> = data
            .into_iter()
            .map(|(name, value)| DataPoint::with_name(vec![value.into()], name.into()))
            .collect();

        DataSet::with_dimensions(vec!["value".to_string()]).add_points(points)
    }
}
