# ECharts-rs CI/CD 流水线指南

## 🚀 概述

ECharts-rs 项目配置了完整的 CI/CD 流水线，基于 Gitee Go 实现自动化构建、测试和部署。

## 📋 流水线组成

### 1. 主 CI 流水线 (`.gitee/workflows/ci.yml`)

**触发条件:**
- 推送到 `main`、`master`、`develop` 分支
- 创建 Pull Request
- 每日定时检查 (凌晨2点)

**包含的作业:**

#### 代码质量检查
- ✅ 代码格式检查 (`cargo fmt`)
- ✅ Clippy 静态分析 (`cargo clippy`)
- ✅ 文档生成检查 (`cargo doc`)

#### 构建和测试
- ✅ 多 Rust 版本测试 (stable, beta)
- ✅ 工作空间完整构建
- ✅ 全功能测试套件
- ✅ 示例程序构建

#### ECharts-rs 专项测试
- ✅ 核心模块测试
- ✅ 图表模块测试  
- ✅ 主题模块测试
- ✅ 组件模块测试
- ✅ 渲染器模块测试 (GPU 功能)
- ✅ GPU 加速演示构建

#### 安全和质量
- ✅ 安全漏洞扫描 (`cargo audit`)
- ✅ 代码覆盖率分析
- ✅ 发布准备检查

### 2. 发布流水线 (`.gitee/workflows/release.yml`)

**触发条件:**
- 推送版本标签 (`v*`)
- 手动触发发布

**包含的作业:**
- 🏗️ 创建 GitHub/Gitee Release
- 📦 构建发布版本
- 📚 生成完整文档
- 📊 发布总结报告

## 🛠️ 本地开发工具

### Linux/macOS 脚本
```bash
# 运行完整 CI 检查
./scripts/ci-local.sh

# 格式化代码
./scripts/ci-local.sh format

# 自动修复问题
./scripts/ci-local.sh fix

# 清理构建文件
./scripts/ci-local.sh clean
```

### Windows PowerShell 脚本
```powershell
# 运行完整 CI 检查
.\scripts\ci-local.ps1 check

# 格式化代码
.\scripts\ci-local.ps1 format

# 自动修复问题
.\scripts\ci-local.ps1 fix

# 清理构建文件
.\scripts\ci-local.ps1 clean
```

## 📊 代码质量标准

### 覆盖率目标
- **整体项目**: 80%
- **核心模块**: 85%
- **图表模块**: 80%
- **渲染器模块**: 75%
- **主题/组件模块**: 70%

### 代码规范
- **格式化**: 使用 `rustfmt.toml` 配置
- **静态分析**: 使用 `clippy.toml` 配置
- **文档**: 所有公共 API 必须有文档
- **测试**: 新功能必须包含测试

## 🔧 配置文件说明

### `rustfmt.toml`
- 代码格式化规则
- 行宽限制: 100 字符
- 导入排序和分组
- 注释格式化

### `clippy.toml`
- 静态分析配置
- 复杂度阈值设置
- 允许的第三方 crate

### `.codecov.yml`
- 代码覆盖率配置
- 模块级别覆盖率目标
- 忽略文件设置

## 🚦 提交前检查清单

在提交代码前，请确保:

- [ ] 运行本地 CI 脚本并通过所有检查
- [ ] 代码已正确格式化
- [ ] 没有 Clippy 警告
- [ ] 所有测试通过
- [ ] 新功能包含适当的测试
- [ ] 公共 API 有完整文档
- [ ] 更新了相关的 CHANGELOG

## 📈 性能监控

### 基准测试
- 渲染性能基准
- 内存使用监控
- GPU 加速效果测试

### 回归检测
- 自动性能回归检测
- 构建时间监控
- 依赖更新影响分析

## 🔄 发布流程

### 自动发布
1. 创建版本标签: `git tag v0.1.0`
2. 推送标签: `git push origin v0.1.0`
3. 自动触发发布流水线
4. 生成发布包和文档

### 手动发布
1. 在 Gitee 仓库中触发 "发布流水线"
2. 输入版本号
3. 确认发布

## 🐛 故障排除

### 常见问题

**构建失败**
- 检查依赖版本兼容性
- 确认系统依赖已安装
- 查看详细错误日志

**测试失败**
- 运行 `cargo test --verbose` 查看详情
- 检查测试环境配置
- 确认 GPU 功能可用性

**格式检查失败**
- 运行 `cargo fmt --all` 修复
- 检查 `rustfmt.toml` 配置

**Clippy 检查失败**
- 运行 `cargo clippy --fix` 自动修复
- 手动修复复杂问题
- 必要时添加 `#[allow]` 注解

### 获取帮助
- 查看 CI 日志详情
- 运行本地 CI 脚本调试
- 检查项目文档和示例

## 📚 相关文档

- [GPU 硬件加速总结](crates/echarts-rs/GPU_ACCELERATION_SUMMARY.md)
- [项目状态报告](crates/echarts-rs/FINAL_STATUS_REPORT.md)
- [优化进度报告](crates/echarts-rs/OPTIMIZATION_PROGRESS.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 运行本地 CI 检查
4. 提交 Pull Request
5. 等待 CI 验证通过
6. 代码审查和合并

---

**注意**: 所有 CI/CD 配置都经过优化，确保快速反馈和高质量代码。如有问题，请查看相关日志或联系维护者。
