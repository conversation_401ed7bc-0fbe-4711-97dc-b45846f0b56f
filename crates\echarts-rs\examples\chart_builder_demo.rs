//! Chart Builder 演示
//!
//! 展示使用真实 charts 实现的增强 Chart 构建器

use echarts_rs::prelude::*;
use echarts_rs::{ChartBuilder, line_chart, bar_chart, scatter_chart, multi_line_chart, mixed_chart};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🏗️ Chart Builder 演示");

    // 1. 基础图表创建
    println!("\n📊 基础图表创建:");
    
    // 简单折线图
    let simple_line = line_chart("简单折线图", vec![
        (1.0, 10.0), (2.0, 20.0), (3.0, 15.0), (4.0, 25.0), (5.0, 30.0)
    ]);
    println!("  ✅ 简单折线图: {} 个系列", simple_line.series.len());

    // 简单柱状图
    let simple_bar = bar_chart("简单柱状图", vec![
        (1.0, 100.0), (2.0, 200.0), (3.0, 150.0), (4.0, 250.0), (5.0, 300.0)
    ]);
    println!("  ✅ 简单柱状图: {} 个系列", simple_bar.series.len());

    // 简单散点图
    let simple_scatter = scatter_chart("简单散点图", vec![
        (1.0, 2.0), (3.0, 4.0), (5.0, 6.0), (7.0, 8.0)
    ]);
    println!("  ✅ 简单散点图: {} 个系列", simple_scatter.series.len());

    // 2. 使用 ChartBuilder 创建高级图表
    println!("\n🔧 高级图表构建:");

    // 高级折线图（平滑曲线 + 符号）
    let advanced_line = ChartBuilder::line_chart()
        .title("高级折线图演示")
        .size(1000.0, 600.0)
        .background_color(Color::rgb(0.98, 0.98, 0.98))
        .padding(40.0, 30.0, 40.0, 50.0)
        .add_smooth_line_series("平滑曲线", vec![
            (0.0, 10.0), (1.0, 25.0), (2.0, 15.0), (3.0, 35.0), (4.0, 20.0), (5.0, 40.0)
        ], Color::rgb(0.2, 0.6, 1.0))
        .add_area_series("面积图", vec![
            (0.0, 5.0), (1.0, 15.0), (2.0, 8.0), (3.0, 20.0), (4.0, 12.0), (5.0, 25.0)
        ], Color::rgb(1.0, 0.4, 0.2))
        .add_step_series("阶梯线", vec![
            (0.0, 0.0), (1.0, 10.0), (2.0, 10.0), (3.0, 20.0), (4.0, 20.0), (5.0, 30.0)
        ], StepType::Start)
        .build();

    println!("  ✅ 高级折线图: {} 个系列", advanced_line.series.len());
    println!("    - 标题: {:?}", advanced_line.title);
    println!("    - 大小: {}x{}", advanced_line.width, advanced_line.height);

    // 高级柱状图（带边框）
    let advanced_bar = ChartBuilder::bar_chart()
        .title("高级柱状图演示")
        .size(800.0, 500.0)
        .add_bordered_bar_series("销售额", vec![
            (1.0, 120.0), (2.0, 200.0), (3.0, 150.0), (4.0, 80.0), (5.0, 170.0)
        ], Color::rgb(0.3, 0.7, 0.9), Color::rgb(0.1, 0.3, 0.5))
        .add_advanced_bar_series("目标值", vec![
            (1.0, 100.0), (2.0, 180.0), (3.0, 140.0), (4.0, 90.0), (5.0, 160.0)
        ], |series| {
            series.color(Color::rgb(0.9, 0.5, 0.2))
                  .bar_width(0.4)
                  .border(true, Color::rgb(0.5, 0.2, 0.1), 2.0)
        })
        .build();

    println!("  ✅ 高级柱状图: {} 个系列", advanced_bar.series.len());

    // 高级散点图（自定义符号）
    let advanced_scatter = ChartBuilder::scatter_chart()
        .title("高级散点图演示")
        .add_symbol_scatter_series("圆形", vec![
            (1.0, 2.0), (2.0, 4.0), (3.0, 3.0)
        ], SymbolType::Circle, 8.0, Color::rgb(1.0, 0.2, 0.2))
        .add_symbol_scatter_series("方形", vec![
            (1.5, 2.5), (2.5, 4.5), (3.5, 3.5)
        ], SymbolType::Square, 10.0, Color::rgb(0.2, 1.0, 0.2))
        .add_symbol_scatter_series("三角形", vec![
            (2.0, 3.0), (3.0, 5.0), (4.0, 4.0)
        ], SymbolType::Triangle, 12.0, Color::rgb(0.2, 0.2, 1.0))
        .build();

    println!("  ✅ 高级散点图: {} 个系列", advanced_scatter.series.len());

    // 3. 多系列图表
    println!("\n📈 多系列图表:");

    let multi_line = multi_line_chart(vec![
        ("系列1", vec![(0.0, 10.0), (1.0, 20.0), (2.0, 15.0), (3.0, 25.0)]),
        ("系列2", vec![(0.0, 5.0), (1.0, 15.0), (2.0, 10.0), (3.0, 20.0)]),
        ("系列3", vec![(0.0, 8.0), (1.0, 18.0), (2.0, 12.0), (3.0, 22.0)]),
    ]);

    println!("  ✅ 多系列折线图: {} 个系列", multi_line.series.len());

    // 4. 混合图表（不同类型的系列）
    println!("\n🎨 混合图表:");

    let mixed = mixed_chart()
        .title("混合图表演示")
        .size(1200.0, 700.0)
        .add_line_series("趋势线", vec![
            (1.0, 100.0), (2.0, 120.0), (3.0, 110.0), (4.0, 130.0), (5.0, 140.0)
        ])
        .add_bar_series("实际值", vec![
            (1.0, 95.0), (2.0, 115.0), (3.0, 105.0), (4.0, 125.0), (5.0, 135.0)
        ])
        .add_scatter_series("异常点", vec![
            (1.5, 80.0), (3.5, 160.0)
        ])
        .build();

    println!("  ✅ 混合图表: {} 个系列", mixed.series.len());
    for (i, series) in mixed.series.iter().enumerate() {
        println!("    {}. {} ({})", i + 1, series.name(), series.series_type().as_str());
    }

    // 5. 渲染测试
    println!("\n🎨 渲染测试:");

    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 700.0, 500.0),
        (0.0, 6.0),
        (0.0, 200.0),
    );

    // 测试混合图表的渲染
    match mixed.render_to_commands() {
        Ok(commands) => {
            println!("  ✅ 混合图表渲染成功: {} 个绘制命令", commands.len());
            
            // 统计不同类型的命令
            let mut line_count = 0;
            let mut rect_count = 0;
            let mut circle_count = 0;
            let mut text_count = 0;
            
            for cmd in &commands {
                match cmd {
                    DrawCommand::Line { .. } => line_count += 1,
                    DrawCommand::Rect { .. } => rect_count += 1,
                    DrawCommand::Circle { .. } => circle_count += 1,
                    DrawCommand::Text { .. } => text_count += 1,
                    _ => {}
                }
            }
            
            println!("    - 线条命令: {}", line_count);
            println!("    - 矩形命令: {}", rect_count);
            println!("    - 圆形命令: {}", circle_count);
            println!("    - 文本命令: {}", text_count);
        }
        Err(e) => {
            println!("  ❌ 渲染失败: {}", e);
        }
    }

    // 6. 高级配置演示
    println!("\n⚙️ 高级配置演示:");

    let dashboard = ChartBuilder::dashboard()
        .title("数据仪表板")
        .background_color(Color::rgb(0.95, 0.95, 0.95))
        .padding(50.0, 40.0, 50.0, 60.0)
        .add_advanced_line_series("收入趋势", vec![
            (1.0, 1000.0), (2.0, 1200.0), (3.0, 1100.0), (4.0, 1300.0), 
            (5.0, 1400.0), (6.0, 1350.0), (7.0, 1500.0)
        ], |series| {
            series.color(Color::rgb(0.1, 0.7, 0.3))
                  .smooth(true)
                  .smoothness(0.4)
                  .show_symbols(true)
                  .line_width(3.0)
        })
        .add_advanced_bar_series("月度支出", vec![
            (1.0, 800.0), (2.0, 900.0), (3.0, 850.0), (4.0, 950.0), 
            (5.0, 1000.0), (6.0, 980.0), (7.0, 1100.0)
        ], |series| {
            series.color(Color::rgb(0.9, 0.3, 0.1))
                  .bar_width(0.5)
                  .border(true, Color::rgb(0.5, 0.1, 0.0), 1.5)
        })
        .build();

    println!("  ✅ 数据仪表板: {} 个系列", dashboard.series.len());
    println!("    - 大小: {}x{}", dashboard.width, dashboard.height);
    println!("    - 边距: {:?}", dashboard.padding);

    println!("\n🎉 Chart Builder 演示完成！");
    println!("✨ 新的 ChartBuilder 提供了强大而灵活的图表创建能力");
    println!("🔗 支持所有 charts crate 的高级功能，同时保持简洁的 API");

    Ok(())
}
