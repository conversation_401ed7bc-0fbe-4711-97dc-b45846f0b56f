//! Grid component implementation

use crate::{Component, Renderable, Themeable};
use echarts_core::*;
use echarts_core::RenderContext;
use echarts_themes::Theme;
use serde::{Deserialize, Serialize};

/// Grid component for chart layout
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Grid {
    /// Whether the grid is visible
    pub visible: bool,

    /// Grid bounds (left, top, right, bottom)
    pub bounds: [f64; 4],

    /// Background color
    pub background_color: Option<Color>,

    /// Border color
    pub border_color: Option<Color>,

    /// Border width
    pub border_width: f64,

    /// Whether to show grid lines
    pub show_grid_lines: bool,

    /// Grid line style
    pub grid_line_style: LineStyle,
}

impl Default for Grid {
    fn default() -> Self {
        Grid {
            visible: true,
            bounds: [60.0, 60.0, 60.0, 60.0], // left, top, right, bottom margins
            background_color: None,
            border_color: None,
            border_width: 0.0,
            show_grid_lines: false,
            grid_line_style: LineStyle {
                color: Color::from_rgba(200, 200, 200, 128), // 0.5 * 255 = 128
                width: 1.0,
                ..Default::default()
            },
        }
    }
}

impl Component for Grid {
    fn component_type(&self) -> &'static str {
        "grid"
    }

    fn is_visible(&self) -> bool {
        self.visible
    }

    fn set_visible(&mut self, visible: bool) {
        self.visible = visible;
    }
}

impl Renderable for Grid {
    fn render<R: ChartRenderer>(&self, ctx: &mut RenderContext<R>, bounds: Bounds) -> Result<()> {
        if !self.visible {
            return Ok(());
        }

        // Calculate grid area
        let grid_bounds = self.calculate_grid_bounds(bounds);

        // Draw background
        if let Some(bg_color) = self.background_color {
            ctx.set_fill_color(bg_color);
            ctx.fill_rect(grid_bounds);
        }

        // Draw border
        if let Some(border_color) = self.border_color {
            ctx.set_stroke(border_color, self.border_width);
            ctx.stroke_rect(grid_bounds);
        }

        // Draw grid lines if enabled
        if self.show_grid_lines {
            self.draw_grid_lines(ctx, grid_bounds)?;
        }

        Ok(())
    }
}

impl Themeable for Grid {
    fn apply_theme(&mut self, theme: &Theme) {
        if self.background_color.is_none() {
            self.background_color = Some(theme.background_color);
        }
    }
}

impl Grid {
    /// Calculate grid bounds based on margins
    fn calculate_grid_bounds(&self, container_bounds: Bounds) -> Bounds {
        let left = container_bounds.origin.x + self.bounds[0]; // left margin
        let top = container_bounds.origin.y + self.bounds[1]; // top margin
        let right = container_bounds.origin.x + container_bounds.width() - self.bounds[2]; // right margin
        let bottom = container_bounds.origin.y + container_bounds.height() - self.bounds[3]; // bottom margin

        Bounds::new(left, top, right - left, bottom - top)
    }

    /// Draw grid lines
    fn draw_grid_lines<R: ChartRenderer>(&self, ctx: &mut RenderContext<R>, bounds: Bounds) -> Result<()> {
        let grid_color = Color::from_rgba(200, 200, 200, 100); // Light gray
        let line_width = 1.0;

        // Draw vertical grid lines (simplified - would normally be based on axis ticks)
        let num_vertical_lines = 10;
        for i in 0..=num_vertical_lines {
            let x = bounds.origin.x + (bounds.width() * i as f64) / num_vertical_lines as f64;
            let from = Point::new(x, bounds.origin.y);
            let to = Point::new(x, bounds.origin.y + bounds.height());
            ctx.draw_line(from, to, grid_color, line_width);
        }

        // Draw horizontal grid lines
        let num_horizontal_lines = 8;
        for i in 0..=num_horizontal_lines {
            let y = bounds.origin.y + (bounds.height() * i as f64) / num_horizontal_lines as f64;
            let from = Point::new(bounds.origin.x, y);
            let to = Point::new(bounds.origin.x + bounds.width(), y);
            ctx.draw_line(from, to, grid_color, line_width);
        }

        Ok(())
    }
}
