[package]
name = "echarts-charts"
version = "0.1.0"
edition = "2021"
description = "Chart implementations for Rust ECharts"
license = "Apache-2.0"

[dependencies]
echarts-core = { path = "../core" }
echarts-themes = { path = "../themes" }
echarts-interaction = { path = "../interaction" }
serde = { workspace = true }
serde_json = { workspace = true }
thiserror = { workspace = true }
anyhow = { workspace = true }

[features]
default = ["std"]
std = []
