# ECharts-rs Clippy 配置

# 认知复杂度阈值
cognitive-complexity-threshold = 30

# 类型复杂度阈值
type-complexity-threshold = 250

# 单个表达式的最大行数
single-char-binding-names-threshold = 4

# 过多参数的阈值
too-many-arguments-threshold = 7

# 过多行数的阈值
too-many-lines-threshold = 100

# 琐碎复制的最大大小
trivial-copy-size-limit = 128

# 枚举变体名称阈值
enum-variant-name-threshold = 3

# 字面量表示的最大位数
literal-representation-threshold = 10

# 避免破坏性更改
avoid-breaking-exported-api = true

# 允许的脚本
allowed-scripts = ["Latin"]

# 第三方 crate 列表（用于某些 lint）
third-party = [
    "serde",
    "serde_json", 
    "tokio",
    "anyhow",
    "thiserror",
    "gpui",
    "nalgebra",
    "euclid"
]
