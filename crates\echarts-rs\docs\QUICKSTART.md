# ECharts-RS 快速开始指南

## 🚀 安装

### 添加依赖

在您的 `Cargo.toml` 文件中添加：

```toml
[dependencies]
echarts-rs = "0.1.0"
```

### 可选功能

```toml
[dependencies]
echarts-rs = { version = "0.1.0", features = ["svg-export", "gpu-acceleration"] }
```

可用的 features：
- `gpu-acceleration` - GPU 加速渲染
- `svg-export` - SVG 导出支持
- `pdf-export` - PDF 导出支持
- `web-export` - Web 导出支持

## 📊 基础用法

### 创建简单的柱状图

```rust
use echarts_rs::prelude::*;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 准备数据
    let data = vec![
        ("一月", 120.0),
        ("二月", 200.0),
        ("三月", 150.0),
        ("四月", 80.0),
        ("五月", 70.0),
        ("六月", 110.0),
    ];
    
    // 创建图表
    let chart = Chart::new()
        .title("月度销售数据".to_string())
        .background_color(Color::WHITE);
    
    // 创建柱状图系列
    let bar_series = BarSeries::new("销售额")
        .data(data)
        .color(Color::from_hex("#5470c6")?);
    
    // 渲染图表
    let bounds = Bounds::new(0.0, 0.0, 800.0, 600.0);
    chart.render(bounds)?;
    
    Ok(())
}
```

### 创建折线图

```rust
use echarts_rs::prelude::*;

fn create_line_chart() -> Result<Chart, ChartError> {
    let data = vec![
        (1.0, 120.0),
        (2.0, 132.0),
        (3.0, 101.0),
        (4.0, 134.0),
        (5.0, 90.0),
        (6.0, 230.0),
        (7.0, 210.0),
    ];
    
    let chart = Chart::new()
        .title("趋势分析".to_string());
    
    let line_series = LineSeries::new("数据趋势")
        .data(data)
        .smooth(true)
        .color(Color::from_hex("#91cc75")?);
    
    Ok(chart)
}
```

## 🎨 主题和样式

### 使用内置主题

```rust
use echarts_rs::prelude::*;

// 使用深色主题
let dark_chart = Chart::new()
    .title("深色主题图表".to_string())
    .background_color(Color::from_hex("#1e1e1e")?);

// 使用复古主题
let vintage_chart = Chart::new()
    .title("复古主题图表".to_string())
    .background_color(Color::from_hex("#fef8ef")?);
```

### 自定义颜色

```rust
use echarts_rs::prelude::*;

let custom_colors = vec![
    Color::from_hex("#ff6b6b")?,  // 红色
    Color::from_hex("#4ecdc4")?,  // 青色
    Color::from_hex("#45b7d1")?,  // 蓝色
    Color::from_hex("#96ceb4")?,  // 绿色
];

let chart = Chart::new()
    .title("自定义配色".to_string());
```

## 🔧 高级配置

### 添加图例和提示框

```rust
use echarts_rs::prelude::*;

let chart = Chart::new()
    .title("完整配置示例".to_string());

// 配置图例
let legend = Legend::default()
    .position(Position::Top)
    .orientation(Orientation::Horizontal);

// 配置提示框
let tooltip = Tooltip::default()
    .trigger(TooltipTrigger::Item)
    .background_color(Color::from_rgba(0, 0, 0, 200));
```

### 自定义坐标轴

```rust
use echarts_rs::prelude::*;

// X 轴配置
let x_axis = Axis::default()
    .axis_type(AxisType::Category)
    .show_line(true)
    .show_labels(true);

// Y 轴配置  
let y_axis = Axis::default()
    .axis_type(AxisType::Value)
    .min(Some(0.0))
    .max(Some(300.0));
```

## 📤 导出功能

### 导出为 SVG

```rust
use echarts_rs::prelude::*;

let chart = Chart::new()
    .title("导出示例".to_string());

let export_options = ExportOptions {
    format: OutputFormat::Svg { 
        width: 800.0, 
        height: 600.0 
    },
    background_color: Some(Color::WHITE),
    dpi: Some(96.0),
};

// 导出到文件
chart.export_to_file("chart.svg", export_options)?;
```

### 导出为 PNG

```rust
use echarts_rs::prelude::*;

let export_options = ExportOptions {
    format: OutputFormat::Png { 
        width: 1200, 
        height: 800 
    },
    background_color: Some(Color::WHITE),
    dpi: Some(150.0),
};

chart.export_to_file("chart.png", export_options)?;
```

## 🎯 实用示例

### 多系列图表

```rust
use echarts_rs::prelude::*;

fn multi_series_chart() -> Result<Chart, ChartError> {
    let sales_data = vec![("Q1", 120.0), ("Q2", 200.0), ("Q3", 150.0), ("Q4", 80.0)];
    let profit_data = vec![("Q1", 20.0), ("Q2", 40.0), ("Q3", 30.0), ("Q4", 15.0)];
    
    let chart = Chart::new()
        .title("销售与利润对比".to_string());
    
    let sales_series = BarSeries::new("销售额")
        .data(sales_data)
        .color(Color::from_hex("#5470c6")?);
        
    let profit_series = LineSeries::new("利润")
        .data(profit_data)
        .color(Color::from_hex("#91cc75")?);
    
    Ok(chart)
}
```

### 响应式图表

```rust
use echarts_rs::prelude::*;

fn responsive_chart(width: f64, height: f64) -> Result<Chart, ChartError> {
    let chart = Chart::new()
        .title("响应式图表".to_string());
    
    // 根据尺寸调整配置
    let font_size = if width < 600.0 { 12.0 } else { 16.0 };
    
    Ok(chart)
}
```

## 🔍 调试和故障排除

### 启用调试日志

```rust
use echarts_rs::prelude::*;

// 在程序开始时启用日志
env_logger::init();

// 或者使用 tracing
use tracing_subscriber;
tracing_subscriber::fmt::init();
```

### 常见问题

1. **编译错误**: 确保使用正确的 Rust 版本 (1.70+)
2. **渲染问题**: 检查数据格式和坐标系配置
3. **性能问题**: 对于大数据集，考虑使用数据采样

### 错误处理

```rust
use echarts_rs::prelude::*;

match chart.render(bounds) {
    Ok(_) => println!("图表渲染成功"),
    Err(ChartError::InvalidData(msg)) => {
        eprintln!("数据错误: {}", msg);
    },
    Err(ChartError::RenderingError(msg)) => {
        eprintln!("渲染错误: {}", msg);
    },
    Err(e) => {
        eprintln!("其他错误: {}", e);
    }
}
```

## 📚 更多资源

- [完整 API 文档](https://docs.rs/echarts-rs)
- [示例代码库](./examples/)
- [架构设计文档](./ARCHITECTURE.md)
- [项目状态](./STATUS.md)

## 🤝 获得帮助

如果您遇到问题：

1. 查看 [常见问题](./FAQ.md)
2. 搜索现有的 [GitHub Issues](https://github.com/your-repo/echarts-rs/issues)
3. 创建新的 Issue 描述您的问题
4. 参与 [Discussions](https://github.com/your-repo/echarts-rs/discussions)

---

*祝您使用愉快！如有问题欢迎反馈。*
