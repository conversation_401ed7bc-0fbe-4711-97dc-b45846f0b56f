/*
 * @Author: Artis
 * @Date: 2025-06-29 16:27:03
 * @LastEditors: Artis
 * @LastEditTime: 2025-06-30 01:03:26
 * @FilePath: \FscDAQ_GPUI\crates\story\src\toggle_panel_action.rs
 * @Description:
 *
 * Copyright (c) 2025 by Artis, All Rights Reserved.
 */

use gpui::{Action, SharedString};

use serde::{Deserialize, Serialize};

#[derive(Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct TogglePanelAction {
    pub panel: String,
}

impl TogglePanelAction {
    pub fn new(panel: &str) -> Self {
        Self {
            panel: panel.to_string(),
        }
    }
}

impl Action for TogglePanelAction {
    fn boxed_clone(&self) -> Box<dyn Action> {
        Box::new(self.clone())
    }
    fn partial_eq(&self, other: &dyn Action) -> bool {
        other
            .as_any()
            .downcast_ref::<Self>()
            .map_or(false, |a| a == self)
    }
    fn name(&self) -> &'static str {
        "TogglePanelAction"
    }
    fn name_for_type() -> &'static str
    where
        Self: Sized,
    {
        "TogglePanelAction"
    }
    fn build(value: serde_json::Value) -> std::result::Result<Box<dyn Action>, anyhow::Error> {
        Ok(Box::new(serde_json::from_value::<Self>(value)?))
    }
}

#[derive(Action, Clone, PartialEq, Eq, Deserialize)]
#[action(namespace = story, no_json)]
pub struct TogglePanelVisible(pub SharedString);
