[package]
name = "fscdaq"
version = "0.1.0"
edition = "2021"

[dependencies]

anyhow.workspace = true
gpui-component = { workspace = true, features = ["webview"] }
gpui.workspace = true
# chart.workspace = true  # 暂时注释掉，等库修复后再启用
echarts-rs = { workspace = true }  # 已修复，重新启用，暂时移除svg-renderer feature
tracing-subscriber.workspace = true
tracing.workspace =true
strum.workspace = true
tsdaq_protocol.workspace = true
rust-i18n.workspace = true

opentelemetry.workspace = true
tracing-error.workspace = true
tracing-opentelemetry .workspace = true
tracing-appender.workspace = true
opentelemetry-jaeger.workspace = true

tokio.workspace = true
tokio-stream.workspace = true
futures.workspace = true



# rust-i18n = "3.1.5"

chrono = "0.4"
fake = { version = "2.10.0", features = ["dummy"] }
rand = "0.8"
raw-window-handle = { version = "0.6", features = ["std"] }
regex = "1"
reqwest_client = { git = "https://github.com/zed-industries/zed.git" }
rust-embed = "8.5.0"
serde_json = "1"
unindent = "0.2.3"
itertools = "0.14.0"
thiserror = "1.0"
strum_macros = "0.26"
serde = { version = "1.0", features = ["derive"] }
crossbeam-channel = "0.5.15"


[target.'cfg(target_os = "linux")'.dependencies]
gtk = { version = "0.18" }


[lints]
workspace = true


