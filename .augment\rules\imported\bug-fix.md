---
type: "agent_requested"
description: "Example description"
---
<!--
 * @Author: Artis
 * @Date: 2025-07-16 21:42:31
 * @LastEditors: Artis
 * @LastEditTime: 2025-07-16 22:10:53
 * @FilePath: \FscDAQ_GPUI\.cursor\rules\bug-fix.mdc
 * @Description: 
 * 
 * Copyright (c) 2025 by Artis, All Rights Reserved. 
-->
# 修复Bug

简化从问题创建到拉取请求的Bug修复工作流程。

## 流程：

### 开始前：
1. **GitHub**：创建一个具有简短描述性标题的问题
2. **Git**：创建并切换到功能分支（`git checkout -b fix/<问题描述>`）

### 修复Bug：
1. 复现问题
2. 编写展示Bug的失败测试
3. 实施修复
4. 验证测试通过
5. 运行完整测试套件
6. 审查代码更改

### 完成时：
1. **Git**：使用引用问题的描述性消息提交
   - 格式：`fix: <描述> (#<问题编号>)`
2. **Git**：将分支推送到远程仓库
3. **GitHub**：创建PR并链接问题
   - 在PR描述中使用"Fixes #<问题编号>"
   - 添加相关标签和审阅者

## 最佳实践：
- 保持更改专注于特定Bug
- 包含回归测试
- 如果行为发生变化，更新文档
- 考虑边缘情况和相关问题
description:
globs:
alwaysApply: false
---
