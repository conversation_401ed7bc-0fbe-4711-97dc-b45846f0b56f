//! 高性能绘制指令系统
//!
//! 这个模块定义了所有基础绘制指令，用于将图表编译为GPU友好的绘制操作。
//! 渲染器只需要执行这些简单的指令，无需了解图表的具体逻辑。

use crate::{Bounds, Color, Point};
use crate::style::{TextStyle, LineStyle};
use serde::{Deserialize, Serialize};

/// 基础绘制指令枚举
///
/// 所有复杂的图表最终都会被编译为这些基础指令
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DrawCommand {
    /// 绘制线段
    Line {
        from: Point,
        to: Point,
        style: LineStyle,
    },

    /// 绘制圆形
    Circle {
        center: Point,
        radius: f64,
        style: CircleStyle,
    },

    /// 绘制文本
    Text {
        text: String,
        position: Point,
        style: TextStyle,
    },

    /// 绘制路径（用于复杂曲线）
    Path {
        commands: Vec<PathCommand>,
        style: PathStyle,
    },

    /// 绘制矩形
    Rect { bounds: Bounds, style: RectStyle },

    /// 绘制多边形
    Polygon {
        points: Vec<Point>,
        style: PolygonStyle,
    },

    /// 批量绘制点（用于大数据量优化）
    Points {
        points: Vec<Point>,
        style: PointStyle,
    },

    /// 批量绘制线段（用于大数据量优化）
    Lines {
        segments: Vec<(Point, Point)>,
        style: LineStyle,
    },
}

/// 路径绘制指令
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PathCommand {
    MoveTo(Point),
    LineTo(Point),
    CurveTo {
        control1: Point,
        control2: Point,
        to: Point,
    },
    QuadTo {
        control: Point,
        to: Point,
    },
    Close,
}

// LineStyle, LineCap, LineJoin 现在从 crate::style 导入

/// 圆形样式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircleStyle {
    pub fill: Option<Color>,
    pub stroke: Option<LineStyle>,
    pub opacity: f64,
}

// TextStyle, FontWeight, FontStyle, TextAlign, TextBaseline 现在从 crate::style 导入

/// 路径样式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PathStyle {
    pub fill: Option<Color>,
    pub stroke: Option<LineStyle>,
    pub opacity: f64,
    pub fill_rule: FillRule,
}

/// 填充规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FillRule {
    NonZero,
    EvenOdd,
}

/// 矩形样式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RectStyle {
    pub fill: Option<Color>,
    pub stroke: Option<LineStyle>,
    pub opacity: f64,
    pub corner_radius: f64,
}

/// 多边形样式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PolygonStyle {
    pub fill: Option<Color>,
    pub stroke: Option<LineStyle>,
    pub opacity: f64,
}

/// 点样式（用于散点图等）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PointStyle {
    pub color: Color,
    pub size: f64,
    pub shape: PointShape,
    pub opacity: f64,
}

/// 点的形状
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PointShape {
    Circle,
    Square,
    Triangle,
    Diamond,
    Cross,
    Plus,
}

/// 绘制指令批次
///
/// 用于批量处理绘制指令，提高性能
#[derive(Debug, Clone)]
pub struct DrawBatch {
    pub commands: Vec<DrawCommand>,
    pub bounds: Bounds,
    pub z_index: i32,
    pub visible: bool,
}

impl DrawBatch {
    pub fn new() -> Self {
        Self {
            commands: Vec::new(),
            bounds: Bounds::zero(),
            z_index: 0,
            visible: true,
        }
    }

    pub fn add_command(&mut self, command: DrawCommand) {
        self.commands.push(command);
    }

    pub fn set_z_index(&mut self, z_index: i32) {
        self.z_index = z_index;
    }

    pub fn set_visible(&mut self, visible: bool) {
        self.visible = visible;
    }

    pub fn clear(&mut self) {
        self.commands.clear();
    }

    pub fn is_empty(&self) -> bool {
        self.commands.is_empty()
    }

    pub fn len(&self) -> usize {
        self.commands.len()
    }
}

impl Default for DrawBatch {
    fn default() -> Self {
        Self::new()
    }
}

// LineStyle 相关实现已移除，现在直接使用 crate::style::LineStyle

// TextStyle 相关实现已移除，现在直接使用 crate::style::TextStyle

impl Default for CircleStyle {
    fn default() -> Self {
        Self {
            fill: Some(Color::rgb(0.0, 0.0, 0.0)),
            stroke: None,
            opacity: 1.0,
        }
    }
}

impl Default for PathStyle {
    fn default() -> Self {
        Self {
            fill: None,
            stroke: Some(LineStyle::default()),
            opacity: 1.0,
            fill_rule: FillRule::NonZero,
        }
    }
}
