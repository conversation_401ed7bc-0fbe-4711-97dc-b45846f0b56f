//! UI components for Rust ECharts
//!
//! This crate provides UI components like legend, tooltip, title, grid, etc.

use echarts_core::{RenderContext, ChartRenderer};
use echarts_themes::Theme;

pub mod axis;
pub mod axis_presets;
pub mod datazoom;
pub mod grid;
pub mod legend;
pub mod responsive_grid;
pub mod theme_integration;
pub mod title;
pub mod toolbox;
pub mod tooltip;

// 测试模块
#[cfg(test)]
pub mod tests;

// Re-export components
pub use axis::*;
pub use axis_presets::*;
pub use datazoom::*;
pub use grid::*;
pub use legend::*;
pub use responsive_grid::*;
pub use theme_integration::*;
pub use title::*;
pub use toolbox::*;
pub use tooltip::*;

use echarts_core::*;
use serde::{Deserialize, Serialize};

/// Trait for objects that can be rendered with context
pub trait Renderable {
    fn render<R: ChartRenderer>(&self, ctx: &mut RenderContext<R>, bounds: Bounds) -> Result<()>;
}

/// Trait for objects that can be themed
pub trait Themeable {
    fn apply_theme(&mut self, theme: &Theme);
}

/// Base trait for all UI components
pub trait Component: Renderable + Themeable {
    /// Get component type name
    fn component_type(&self) -> &'static str;

    /// Check if component is visible
    fn is_visible(&self) -> bool;

    /// Set component visibility
    fn set_visible(&mut self, visible: bool);
}

/// Component positioning
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Position {
    /// Absolute position with x, y coordinates
    Absolute {
        x: f64,
        y: f64,
    },
    /// Relative position (percentage)
    Relative {
        x: f64,
        y: f64,
    },
    /// Predefined positions
    Top,
    Bottom,
    Left,
    Right,
    Center,
    TopLeft,
    TopRight,
    BottomLeft,
    BottomRight,
}

impl Default for Position {
    fn default() -> Self {
        Position::Center
    }
}

/// Component alignment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Alignment {
    Start,
    Center,
    End,
}

impl Default for Alignment {
    fn default() -> Self {
        Alignment::Center
    }
}

/// Component orientation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Orientation {
    Horizontal,
    Vertical,
}

impl Default for Orientation {
    fn default() -> Self {
        Orientation::Horizontal
    }
}
