#!/usr/bin/env python3
"""
ECharts-rs 任务运行器
简单的跨平台CI/CD任务执行脚本
"""

import sys
import subprocess
import os
from pathlib import Path

def run_command(cmd, description=""):
    """运行命令并检查结果"""
    if description:
        print(f"🔍 {description}")
    
    print(f"   执行: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        print(f"   ✅ 成功")
        if result.stdout.strip():
            print(f"   输出: {result.stdout.strip()}")
        return True
    else:
        print(f"   ❌ 失败 (退出码: {result.returncode})")
        if result.stderr.strip():
            print(f"   错误: {result.stderr.strip()}")
        return False

def check_prerequisites():
    """检查必要工具"""
    print("🔧 检查必要工具...")
    
    # 检查 Cargo
    if not run_command("cargo --version", "检查 Cargo"):
        print("❌ Cargo 未安装，请先安装 Rust")
        return False
    
    # 检查 rustfmt
    if not run_command("rustfmt --version", "检查 rustfmt"):
        print("⚠️  rustfmt 未安装，正在安装...")
        run_command("rustup component add rustfmt")
    
    # 检查 clippy
    if not run_command("cargo clippy --version", "检查 clippy"):
        print("⚠️  clippy 未安装，正在安装...")
        run_command("rustup component add clippy")
    
    return True

def format_check():
    """检查代码格式"""
    return run_command("cargo fmt --all -- --check", "检查代码格式")

def format_code():
    """格式化代码"""
    return run_command("cargo fmt --all", "格式化代码")

def clippy_check():
    """运行 Clippy 检查"""
    return run_command("cargo clippy --all-targets --all-features -- -D warnings", "运行 Clippy 检查")

def build_project():
    """构建项目"""
    return run_command("cargo build --workspace --all-features", "构建项目")

def run_tests():
    """运行测试"""
    return run_command("cargo test --workspace --all-features", "运行测试")

def test_echarts_modules():
    """测试 ECharts-rs 模块"""
    print("📊 测试 ECharts-rs 模块...")
    
    modules = [
        "rust-echarts-core",
        "rust-echarts-charts", 
        "rust-echarts-themes",
        "rust-echarts-components"
    ]
    
    success = True
    for module in modules:
        if not run_command(f"cargo test --package {module}", f"测试模块 {module}"):
            success = False
    
    # 测试渲染器模块（需要特殊feature）
    if not run_command("cargo test --package rust-echarts-renderer --features gpui-backend", "测试渲染器模块"):
        success = False
    
    return success

def build_examples():
    """构建示例程序"""
    print("🎯 构建示例程序...")
    
    success = True
    if not run_command("cargo build --examples --all-features", "构建所有示例"):
        success = False
    
    if not run_command("cargo build --example gpu_acceleration_demo --package rust-echarts-renderer --features gpui-backend", "构建GPU加速演示"):
        success = False
    
    return success

def generate_docs():
    """生成文档"""
    return run_command("cargo doc --all-features --no-deps --document-private-items", "生成文档")

def security_audit():
    """安全审计"""
    print("🔒 运行安全审计...")
    if not run_command("cargo audit --version", "检查 cargo-audit"):
        print("⚠️  cargo-audit 未安装，跳过安全审计")
        print("   运行 'cargo install cargo-audit' 来安装")
        return True
    
    return run_command("cargo audit", "安全审计")

def clean_project():
    """清理项目"""
    return run_command("cargo clean", "清理构建文件")

def full_check():
    """完整的CI检查"""
    print("=" * 50)
    print("    ECharts-rs 本地 CI/CD 检查")
    print("=" * 50)
    print()
    
    failed_checks = []
    
    if not check_prerequisites():
        failed_checks.append("prerequisites")
    
    if not format_check():
        failed_checks.append("format")
    
    if not clippy_check():
        failed_checks.append("clippy")
    
    if not build_project():
        failed_checks.append("build")
    
    if not run_tests():
        failed_checks.append("tests")
    
    if not test_echarts_modules():
        failed_checks.append("echarts-tests")
    
    if not generate_docs():
        failed_checks.append("docs")
    
    if not security_audit():
        failed_checks.append("security")
    
    if not build_examples():
        failed_checks.append("examples")
    
    print()
    print("=" * 50)
    print("           检查结果总结")
    print("=" * 50)
    
    if not failed_checks:
        print("✅ 所有检查都通过了！🎉")
        print("📝 代码已准备好提交")
        return True
    else:
        print("❌ 以下检查失败:")
        for check in failed_checks:
            print(f"   - {check}")
        print()
        print("🔧 请修复上述问题后再次运行")
        return False

def show_help():
    """显示帮助信息"""
    print("ECharts-rs 任务运行器")
    print("=" * 30)
    print()
    print("用法: python scripts/run-task.py <任务>")
    print()
    print("可用任务:")
    print("  check      - 运行完整的CI检查")
    print("  format     - 格式化代码")
    print("  clean      - 清理构建文件")
    print("  build      - 构建项目")
    print("  test       - 运行测试")
    print("  docs       - 生成文档")
    print("  examples   - 构建示例")
    print("  help       - 显示此帮助")
    print()
    print("示例:")
    print("  python scripts/run-task.py check")
    print("  python scripts/run-task.py format")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        task = "check"
    else:
        task = sys.argv[1].lower()
    
    # 切换到项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    if task == "check":
        success = full_check()
        sys.exit(0 if success else 1)
    elif task == "format":
        success = format_code()
        sys.exit(0 if success else 1)
    elif task == "clean":
        success = clean_project()
        sys.exit(0 if success else 1)
    elif task == "build":
        success = build_project()
        sys.exit(0 if success else 1)
    elif task == "test":
        success = run_tests()
        sys.exit(0 if success else 1)
    elif task == "docs":
        success = generate_docs()
        sys.exit(0 if success else 1)
    elif task == "examples":
        success = build_examples()
        sys.exit(0 if success else 1)
    elif task == "help":
        show_help()
        sys.exit(0)
    else:
        print(f"❌ 未知任务: {task}")
        show_help()
        sys.exit(1)

if __name__ == "__main__":
    main()
