//! 雷达图SVG演示
//!
//! 生成各种雷达图的SVG文件，展示RadarSeries的完整功能

use std::fs;
use echarts_rs::{RadarSeries, RadarDataItem, RadarIndicator, Color};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("📡 雷达图SVG演示系统");
    println!("{}", "=".repeat(50));

    // 确保输出目录存在
    let output_dir = "temp/svg/radar_charts";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 基础雷达图
    println!("\n📡 1. 生成基础雷达图...");
    generate_basic_radar_chart(output_dir)?;

    // 2. 多系列雷达图
    println!("\n📊 2. 生成多系列雷达图...");
    generate_multi_series_radar_chart(output_dir)?;

    // 3. 能力评估雷达图
    println!("\n🎯 3. 生成能力评估雷达图...");
    generate_ability_assessment_radar(output_dir)?;

    // 4. 产品对比雷达图
    println!("\n🔍 4. 生成产品对比雷达图...");
    generate_product_comparison_radar(output_dir)?;

    // 5. 自定义样式雷达图
    println!("\n🎨 5. 生成自定义样式雷达图...");
    generate_custom_style_radar(output_dir)?;

    // 6. 生成展示页面
    println!("\n📄 6. 生成展示页面...");
    generate_radar_showcase(output_dir)?;

    println!("\n🎉 雷达图SVG演示生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/radar_demo.html 查看演示", output_dir);

    Ok(())
}

/// 生成基础雷达图
fn generate_basic_radar_chart(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let indicators = vec![
        RadarIndicator::new("销售", 0.0, 100.0),
        RadarIndicator::new("管理", 0.0, 100.0),
        RadarIndicator::new("信息技术", 0.0, 100.0),
        RadarIndicator::new("客服", 0.0, 100.0),
        RadarIndicator::new("研发", 0.0, 100.0),
        RadarIndicator::new("市场", 0.0, 100.0),
    ];

    let data = vec![
        RadarDataItem::new("预算分配", vec![80.0, 90.0, 70.0, 85.0, 95.0, 75.0])
            .color(Color::rgb(0.2, 0.6, 1.0))
            .opacity(0.8),
    ];

    let radar_series = RadarSeries::new("基础雷达图")
        .indicators(indicators)
        .data(data)
        .center(0.5, 0.5)
        .radius(0.75)
        .show_label(true)
        .area_style(true);

    let svg = create_radar_svg(&radar_series, "基础雷达图演示", 600.0, 600.0)?;
    fs::write(format!("{}/01_basic_radar.svg", output_dir), svg)?;
    
    println!("  ✅ 基础雷达图生成完成");
    Ok(())
}

/// 生成多系列雷达图
fn generate_multi_series_radar_chart(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let indicators = vec![
        RadarIndicator::new("攻击", 0.0, 100.0),
        RadarIndicator::new("防御", 0.0, 100.0),
        RadarIndicator::new("速度", 0.0, 100.0),
        RadarIndicator::new("生命", 0.0, 100.0),
        RadarIndicator::new("魔法", 0.0, 100.0),
    ];

    let data = vec![
        RadarDataItem::new("战士", vec![90.0, 85.0, 60.0, 95.0, 30.0])
            .color(Color::rgb(1.0, 0.4, 0.4))
            .opacity(0.8),
        RadarDataItem::new("法师", vec![40.0, 50.0, 70.0, 60.0, 95.0])
            .color(Color::rgb(0.4, 0.4, 1.0))
            .opacity(0.8),
        RadarDataItem::new("刺客", vec![85.0, 40.0, 95.0, 70.0, 50.0])
            .color(Color::rgb(0.4, 0.8, 0.4))
            .opacity(0.8),
    ];

    let radar_series = RadarSeries::new("多系列雷达图")
        .indicators(indicators)
        .data(data)
        .center(0.5, 0.5)
        .radius(0.7)
        .show_label(true)
        .area_style(true)
        .line_width(2.5);

    let svg = create_radar_svg(&radar_series, "多系列雷达图演示", 600.0, 600.0)?;
    fs::write(format!("{}/02_multi_series_radar.svg", output_dir), svg)?;
    
    println!("  ✅ 多系列雷达图生成完成");
    Ok(())
}

/// 生成能力评估雷达图
fn generate_ability_assessment_radar(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let indicators = vec![
        RadarIndicator::new("沟通能力", 0.0, 10.0),
        RadarIndicator::new("团队协作", 0.0, 10.0),
        RadarIndicator::new("技术能力", 0.0, 10.0),
        RadarIndicator::new("创新思维", 0.0, 10.0),
        RadarIndicator::new("学习能力", 0.0, 10.0),
        RadarIndicator::new("执行力", 0.0, 10.0),
        RadarIndicator::new("领导力", 0.0, 10.0),
    ];

    let data = vec![
        RadarDataItem::new("员工A", vec![8.5, 9.0, 7.5, 8.0, 9.5, 8.5, 7.0])
            .color(Color::rgb(0.8, 0.4, 1.0))
            .opacity(0.7),
    ];

    let radar_series = RadarSeries::new("能力评估雷达图")
        .indicators(indicators)
        .data(data)
        .center(0.5, 0.5)
        .radius(0.8)
        .show_label(true)
        .area_style(true)
        .split_number(5);

    let svg = create_radar_svg(&radar_series, "能力评估雷达图", 600.0, 600.0)?;
    fs::write(format!("{}/03_ability_assessment_radar.svg", output_dir), svg)?;
    
    println!("  ✅ 能力评估雷达图生成完成");
    Ok(())
}

/// 生成产品对比雷达图
fn generate_product_comparison_radar(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let indicators = vec![
        RadarIndicator::new("性能", 0.0, 100.0),
        RadarIndicator::new("价格", 0.0, 100.0),
        RadarIndicator::new("易用性", 0.0, 100.0),
        RadarIndicator::new("功能", 0.0, 100.0),
        RadarIndicator::new("稳定性", 0.0, 100.0),
        RadarIndicator::new("支持", 0.0, 100.0),
    ];

    let data = vec![
        RadarDataItem::new("产品A", vec![85.0, 60.0, 90.0, 80.0, 95.0, 85.0])
            .color(Color::rgb(1.0, 0.6, 0.2))
            .opacity(0.8),
        RadarDataItem::new("产品B", vec![75.0, 85.0, 75.0, 90.0, 80.0, 70.0])
            .color(Color::rgb(0.2, 0.8, 0.6))
            .opacity(0.8),
    ];

    let radar_series = RadarSeries::new("产品对比雷达图")
        .indicators(indicators)
        .data(data)
        .center(0.5, 0.5)
        .radius(0.75)
        .show_label(true)
        .area_style(true)
        .line_width(3.0);

    let svg = create_radar_svg(&radar_series, "产品对比雷达图", 600.0, 600.0)?;
    fs::write(format!("{}/04_product_comparison_radar.svg", output_dir), svg)?;
    
    println!("  ✅ 产品对比雷达图生成完成");
    Ok(())
}

/// 生成自定义样式雷达图
fn generate_custom_style_radar(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let indicators = vec![
        RadarIndicator::new("创意", 0.0, 100.0).color(Color::rgb(1.0, 0.2, 0.2)),
        RadarIndicator::new("执行", 0.0, 100.0).color(Color::rgb(0.2, 1.0, 0.2)),
        RadarIndicator::new("合作", 0.0, 100.0).color(Color::rgb(0.2, 0.2, 1.0)),
        RadarIndicator::new("沟通", 0.0, 100.0).color(Color::rgb(1.0, 1.0, 0.2)),
        RadarIndicator::new("学习", 0.0, 100.0).color(Color::rgb(1.0, 0.2, 1.0)),
    ];

    let data = vec![
        RadarDataItem::new("团队表现", vec![88.0, 92.0, 85.0, 90.0, 87.0])
            .color(Color::rgb(0.6, 0.8, 1.0))
            .opacity(0.9),
    ];

    let radar_series = RadarSeries::new("自定义样式雷达图")
        .indicators(indicators)
        .data(data)
        .center(0.5, 0.5)
        .radius(0.7)
        .show_label(true)
        .area_style(false) // 不填充区域
        .line_width(4.0)
        .split_number(4);

    let svg = create_radar_svg(&radar_series, "自定义样式雷达图", 600.0, 600.0)?;
    fs::write(format!("{}/05_custom_style_radar.svg", output_dir), svg)?;
    
    println!("  ✅ 自定义样式雷达图生成完成");
    Ok(())
}

/// 创建雷达图SVG
fn create_radar_svg(series: &RadarSeries, title: &str, width: f64, height: f64) -> std::result::Result<String, Box<dyn std::error::Error>> {
    use echarts_core::{CartesianCoordinateSystem, Bounds, Series, Point, Size};
    
    // 创建坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        Bounds {
            origin: Point { x: 50.0, y: 50.0 },
            size: Size { width: width - 100.0, height: height - 100.0 },
        },
        (0.0, 1.0),
        (0.0, 1.0),
    );
    
    // 获取渲染命令
    let commands = series.render_to_commands(&coord_system)?;
    
    // 生成SVG
    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 渲染命令
    for command in commands {
        render_radar_svg_command(&mut svg, &command);
    }
    
    svg.push_str("</svg>");
    Ok(svg)
}

/// 渲染雷达图SVG命令
fn render_radar_svg_command(svg: &mut String, command: &echarts_core::DrawCommand) {
    use echarts_core::{DrawCommand, PathCommand};
    
    match command {
        DrawCommand::Path { commands, style } => {
            let mut path_data = String::new();
            for cmd in commands {
                match cmd {
                    PathCommand::MoveTo(point) => {
                        path_data.push_str(&format!("M {} {} ", point.x, point.y));
                    }
                    PathCommand::LineTo(point) => {
                        path_data.push_str(&format!("L {} {} ", point.x, point.y));
                    }
                    PathCommand::CurveTo { control1, control2, to } => {
                        path_data.push_str(&format!("C {} {} {} {} {} {} ", 
                            control1.x, control1.y, control2.x, control2.y, to.x, to.y));
                    }
                    PathCommand::QuadTo { control, to } => {
                        path_data.push_str(&format!("Q {} {} {} {} ", control.x, control.y, to.x, to.y));
                    }
                    PathCommand::Close => {
                        path_data.push_str("Z ");
                    }
                }
            }
            
            let fill = style.fill.map(|c| format!("#{:02x}{:02x}{:02x}", 
                (c.r * 255.0) as u8, (c.g * 255.0) as u8, (c.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke = style.stroke.as_ref().map(|s| format!("#{:02x}{:02x}{:02x}", 
                (s.color.r * 255.0) as u8, (s.color.g * 255.0) as u8, (s.color.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke_width = style.stroke.as_ref().map(|s| s.width).unwrap_or(0.0);
            
            svg.push_str(&format!("  <path d=\"{}\" fill=\"{}\" stroke=\"{}\" stroke-width=\"{}\" opacity=\"{}\"/>\n", 
                path_data.trim(), fill, stroke, stroke_width, style.opacity));
        }
        DrawCommand::Text { text, position, style } => {
            let color = format!("#{:02x}{:02x}{:02x}", 
                (style.color.r * 255.0) as u8, (style.color.g * 255.0) as u8, (style.color.b * 255.0) as u8);
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"{}\" fill=\"{}\" text-anchor=\"middle\" opacity=\"{}\">{}</text>\n", 
                position.x, position.y, style.font_size, color, style.opacity, text));
        }
        DrawCommand::Circle { center, radius, style } => {
            let fill = style.fill.map(|c| format!("#{:02x}{:02x}{:02x}", 
                (c.r * 255.0) as u8, (c.g * 255.0) as u8, (c.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke = style.stroke.as_ref().map(|s| format!("#{:02x}{:02x}{:02x}", 
                (s.color.r * 255.0) as u8, (s.color.g * 255.0) as u8, (s.color.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke_width = style.stroke.as_ref().map(|s| s.width).unwrap_or(0.0);
            
            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"{}\" fill=\"{}\" stroke=\"{}\" stroke-width=\"{}\" opacity=\"{}\"/>\n", 
                center.x, center.y, radius, fill, stroke, stroke_width, style.opacity));
        }
        _ => {} // 忽略其他命令类型
    }
}

/// 生成展示页面
fn generate_radar_showcase(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts-rs 雷达图演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .section {
            background: rgba(255,255,255,0.95);
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .chart-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        .chart-svg {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h2 { color: #333; margin-bottom: 20px; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        .description {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📡 ECharts-rs 雷达图演示</h1>
            <p class="description">展现 RadarSeries 的强大功能和多维数据可视化</p>
            <div class="feature-list">
                <div class="feature">
                    <h3>📡 多维数据</h3>
                    <p>多指标数据可视化</p>
                </div>
                <div class="feature">
                    <h3>📊 多系列对比</h3>
                    <p>多个数据系列对比</p>
                </div>
                <div class="feature">
                    <h3>🎯 能力评估</h3>
                    <p>综合能力评估展示</p>
                </div>
                <div class="feature">
                    <h3>🎨 自定义样式</h3>
                    <p>灵活的样式配置</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📡 基础雷达图功能</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">基础雷达图</div>
                    <object class="chart-svg" data="01_basic_radar.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">多系列雷达图</div>
                    <object class="chart-svg" data="02_multi_series_radar.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 应用场景展示</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">能力评估雷达图</div>
                    <object class="chart-svg" data="03_ability_assessment_radar.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">产品对比雷达图</div>
                    <object class="chart-svg" data="04_product_comparison_radar.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎨 自定义样式</h2>
            <div class="chart-item">
                <div class="chart-title">自定义样式雷达图</div>
                <object class="chart-svg" data="05_custom_style_radar.svg" type="image/svg+xml">SVG不支持</object>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h2>🎉 RadarSeries 功能总结</h2>
            <p>ECharts-rs RadarSeries 完整功能展示：</p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>✅ <strong>多维数据展示</strong> - 支持任意数量的指标维度</li>
                <li>✅ <strong>多系列对比</strong> - 同时展示多个数据系列</li>
                <li>✅ <strong>灵活的指标配置</strong> - 自定义指标名称、范围和颜色</li>
                <li>✅ <strong>区域填充</strong> - 可选的区域填充效果</li>
                <li>✅ <strong>网格线配置</strong> - 可调节的网格线数量</li>
                <li>✅ <strong>标签系统</strong> - 指标名称标签显示</li>
                <li>✅ <strong>样式定制</strong> - 线条宽度、透明度等样式配置</li>
                <li>✅ <strong>高性能渲染</strong> - 优化的SVG输出</li>
            </ul>
        </div>
    </div>
</body>
</html>"#;

    fs::write(format!("{}/radar_demo.html", output_dir), html_content)?;
    Ok(())
}
