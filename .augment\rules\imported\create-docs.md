---
type: "agent_requested"
description: "Example description"
---
# 创建文档

为指定组件或功能创建全面的文档。
生成中文文档
重要对话 总结创建全面的文档
## 分析领域：
1. 代码结构和目的
2. 输入、输出和行为
3. 用户交互流程
4. 边缘情况和错误处理
5. 与其他组件/系统的集成点

## 文档模板：

### 概述
简短的1-2段概述，解释目的和价值

### 用法
如何使用此组件/功能，附带示例

### API / 属性 / 参数
详细的接口规范

### 组件层次结构
结构和关系（如适用）

### 状态管理
状态如何处理及在系统中流动

### 行为
不同场景下的预期行为

### 错误处理
如何捕获、处理和报告错误

### 性能考虑
优化注意事项和性能特性

### 无障碍性
无障碍功能和合规性

### 测试
如何测试此组件/功能

### 相关组件/功能
相关文档链接

## 流程：
1. 彻底分析目标代码
2. 识别所有公共接口
3. 记录预期行为
4. 包含代码示例
5. 在有帮助的地方添加图表
6. 遵循项目文档标准
7. 确保清晰、完整和可操作性

## 输出格式：
- 通用文档使用Markdown
- 代码注释使用JSDoc/TSDoc
- API文档格式
- README文件
- 架构决策记录(ADRs)