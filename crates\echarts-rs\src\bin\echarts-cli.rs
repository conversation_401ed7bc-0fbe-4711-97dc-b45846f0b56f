//! ECharts-rs 命令行工具
//! 
//! 这个CLI工具提供了创建、预览和导出图表的功能

use clap::{Arg, Command, ArgMatches};
use echarts_core::{Chart, DataPoint, DataValue};
use echarts_themes::Theme;
use echarts_charts::{LineSeries, BarSeries, ScatterSeries, SymbolType};
// 注意：PieSeries, HeatmapSeries, Surface3DSeries 尚未实现
// use echarts_charts::heatmap::HeatmapSeries;
// use echarts_charts::surface3d::Surface3DSeries;
// use echarts_svg_renderer::SvgRenderer; // 暂时禁用SVG渲染器
use serde_json::Value;
use std::fs;



fn main() {
    let matches = Command::new("echarts-cli")
        .version("0.1.0")
        .author("ECharts-rs Team")
        .about("ECharts-rs 命令行工具 - 创建和导出图表")
        .subcommand(
            Command::new("create")
                .about("创建新图表")
                .arg(
                    Arg::new("type")
                        .short('t')
                        .long("type")
                        .value_name("TYPE")
                        .help("图表类型 (line, bar, pie, scatter, heatmap, surface3d)")
                        .required(true)
                )
                .arg(
                    Arg::new("data")
                        .short('d')
                        .long("data")
                        .value_name("FILE")
                        .help("数据文件路径 (JSON格式)")
                        .required(true)
                )
                .arg(
                    Arg::new("output")
                        .short('o')
                        .long("output")
                        .value_name("FILE")
                        .help("输出文件路径")
                        .required(true)
                )
                .arg(
                    Arg::new("theme")
                        .long("theme")
                        .value_name("THEME")
                        .help("主题 (light, dark, vintage, macarons)")
                        .default_value("light")
                )
                .arg(
                    Arg::new("title")
                        .long("title")
                        .value_name("TITLE")
                        .help("图表标题")
                        .default_value("ECharts-rs 图表")
                )
                .arg(
                    Arg::new("width")
                        .long("width")
                        .value_name("WIDTH")
                        .help("图表宽度")
                        .default_value("800")
                )
                .arg(
                    Arg::new("height")
                        .long("height")
                        .value_name("HEIGHT")
                        .help("图表高度")
                        .default_value("600")
                )
        )
        .subcommand(
            Command::new("demo")
                .about("生成演示图表")
                .arg(
                    Arg::new("type")
                        .short('t')
                        .long("type")
                        .value_name("TYPE")
                        .help("演示图表类型")
                        .default_value("all")
                )
                .arg(
                    Arg::new("output-dir")
                        .short('o')
                        .long("output-dir")
                        .value_name("DIR")
                        .help("输出目录")
                        .default_value("./demo_charts")
                )
        )
        .subcommand(
            Command::new("validate")
                .about("验证数据文件")
                .arg(
                    Arg::new("file")
                        .short('f')
                        .long("file")
                        .value_name("FILE")
                        .help("要验证的数据文件")
                        .required(true)
                )
        )
        .subcommand(
            Command::new("info")
                .about("显示系统信息")
        )
        .get_matches();

    match matches.subcommand() {
        Some(("create", sub_matches)) => {
            if let Err(e) = handle_create_command(sub_matches) {
                eprintln!("❌ 创建图表失败: {}", e);
                std::process::exit(1);
            }
        }
        Some(("demo", sub_matches)) => {
            if let Err(e) = handle_demo_command(sub_matches) {
                eprintln!("❌ 生成演示失败: {}", e);
                std::process::exit(1);
            }
        }
        Some(("validate", sub_matches)) => {
            if let Err(e) = handle_validate_command(sub_matches) {
                eprintln!("❌ 验证失败: {}", e);
                std::process::exit(1);
            }
        }
        Some(("info", _)) => {
            handle_info_command();
        }
        _ => {
            println!("使用 --help 查看可用命令");
        }
    }
}

/// 处理创建图表命令
fn handle_create_command(matches: &ArgMatches) -> Result<(), Box<dyn std::error::Error>> {
    let chart_type = matches.get_one::<String>("type").unwrap();
    let data_file = matches.get_one::<String>("data").unwrap();
    let output_file = matches.get_one::<String>("output").unwrap();
    let theme_name = matches.get_one::<String>("theme").unwrap();
    let title = matches.get_one::<String>("title").unwrap();
    let width: u32 = matches.get_one::<String>("width").unwrap().parse()?;
    let height: u32 = matches.get_one::<String>("height").unwrap().parse()?;

    println!("🎨 创建 {} 图表...", chart_type);
    println!("   数据文件: {}", data_file);
    println!("   输出文件: {}", output_file);
    println!("   主题: {}", theme_name);
    println!("   尺寸: {}x{}", width, height);

    // 读取数据
    let data = load_data_from_file(data_file)?;
    
    // 创建图表
    let mut chart = Chart::new();
    chart.title = Some(title.clone());
    
    // 应用主题
    let theme = match theme_name.as_str() {
        "light" => Theme::light(),
        "dark" => Theme::dark(),
        "vintage" => Theme::vintage(),
        "macarons" => Theme::macarons(),
        _ => {
            println!("⚠️  未知主题 '{}', 使用默认主题", theme_name);
            Theme::light()
        }
    };
    chart.background_color = Some(theme.background_color);

    // 根据类型创建系列
    match chart_type.as_str() {
        "line" => create_line_chart(&mut chart, data)?,
        "bar" => create_bar_chart(&mut chart, data)?,
        "pie" => create_pie_chart(&mut chart, data)?,
        "scatter" => create_scatter_chart(&mut chart, data)?,
        "heatmap" => create_heatmap_chart(&mut chart, data)?,
        _ => return Err(format!("不支持的图表类型: {}", chart_type).into()),
    }

    // 渲染并保存 - 暂时禁用SVG渲染器
    // let mut renderer = SvgRenderer::default();
    // let svg_content = renderer.render_to_string(&chart)?;
    // fs::write(output_file, svg_content)?;

    println!("⚠️ SVG渲染器暂时禁用，图表创建完成但未保存");
    
    println!("✅ 图表已保存到: {}", output_file);
    Ok(())
}

/// 处理演示命令
fn handle_demo_command(matches: &ArgMatches) -> Result<(), Box<dyn std::error::Error>> {
    let demo_type = matches.get_one::<String>("type").unwrap();
    let output_dir = matches.get_one::<String>("output-dir").unwrap();

    println!("🚀 生成演示图表...");
    println!("   类型: {}", demo_type);
    println!("   输出目录: {}", output_dir);

    // 创建输出目录
    fs::create_dir_all(output_dir)?;

    if demo_type == "all" || demo_type == "line" {
        create_demo_line_chart(output_dir)?;
    }
    
    if demo_type == "all" || demo_type == "bar" {
        create_demo_bar_chart(output_dir)?;
    }
    
    if demo_type == "all" || demo_type == "pie" {
        create_demo_pie_chart(output_dir)?;
    }
    
    if demo_type == "all" || demo_type == "scatter" {
        create_demo_scatter_chart(output_dir)?;
    }
    
    if demo_type == "all" || demo_type == "heatmap" {
        create_demo_heatmap_chart(output_dir)?;
    }

    if demo_type == "all" || demo_type == "surface3d" {
        create_demo_surface3d_chart(output_dir)?;
    }

    println!("✅ 演示图表生成完成!");
    Ok(())
}

/// 处理验证命令
fn handle_validate_command(matches: &ArgMatches) -> Result<(), Box<dyn std::error::Error>> {
    let file_path = matches.get_one::<String>("file").unwrap();
    
    println!("🔍 验证数据文件: {}", file_path);
    
    let data = load_data_from_file(file_path)?;
    
    println!("✅ 数据文件验证通过");
    println!("   数据点数量: {}", data.len());
    
    if !data.is_empty() {
        println!("   示例数据点: {:?}", data[0]);
    }
    
    Ok(())
}

/// 处理信息命令
fn handle_info_command() {
    println!("📊 ECharts-rs 系统信息");
    println!("========================");
    println!("版本: 0.1.0");
    println!("支持的图表类型:");
    println!("  - line: 折线图");
    println!("  - bar: 柱状图");
    println!("  - pie: 饼图");
    println!("  - scatter: 散点图");
    println!("  - heatmap: 热力图");
    println!();
    println!("支持的主题:");
    println!("  - light: 浅色主题");
    println!("  - dark: 深色主题");
    println!("  - vintage: 复古主题");
    println!("  - macarons: 马卡龙主题");
    println!();
    println!("支持的输出格式:");
    println!("  - SVG: 矢量图形格式");
    println!();
    println!("数据格式: JSON");
    println!("示例数据格式:");
    println!(r#"[
  {{"x": 0, "y": 10}},
  {{"x": 1, "y": 20}},
  {{"x": 2, "y": 15}}
]"#);
}

// 辅助函数

/// 从文件加载数据
fn load_data_from_file(file_path: &str) -> Result<Vec<DataPoint>, Box<dyn std::error::Error>> {
    let content = fs::read_to_string(file_path)?;
    let json_data: Value = serde_json::from_str(&content)?;
    
    let mut data_points = Vec::new();
    
    if let Value::Array(array) = json_data {
        for item in array {
            if let Value::Object(obj) = item {
                let x = obj.get("x").and_then(|v| v.as_f64()).unwrap_or(0.0);
                let y = obj.get("y").and_then(|v| v.as_f64()).unwrap_or(0.0);
                
                data_points.push(DataPoint::new(vec![
                    DataValue::Number(x),
                    DataValue::Number(y),
                ]));
            }
        }
    }
    
    Ok(data_points)
}

/// 创建折线图
fn create_line_chart(_chart: &mut Chart, data: Vec<DataPoint>) -> Result<(), Box<dyn std::error::Error>> {
    let line_data: Vec<(f64, f64)> = data.iter().map(|p| (p.x(), p.y())).collect();
    let _series = LineSeries::new("数据系列").data(line_data).smooth(true);
    Ok(())
}

/// 创建柱状图
fn create_bar_chart(_chart: &mut Chart, data: Vec<DataPoint>) -> Result<(), Box<dyn std::error::Error>> {
    // 转换DataPoint为(f64, f64)格式（使用索引作为x值）
    let bar_data: Vec<(f64, f64)> = data.iter().enumerate().map(|(i, p)| {
        let x = i as f64;
        let y = p.get_number(0).unwrap_or(0.0);
        (x, y)
    }).collect();

    let _series = BarSeries::new("数据系列").data(bar_data);
    Ok(())
}

/// 创建饼图（暂未实现 PieSeries）
fn create_pie_chart(_chart: &mut Chart, _data: Vec<DataPoint>) -> Result<(), Box<dyn std::error::Error>> {
    // TODO: 实现 PieSeries 后启用此功能
    println!("饼图功能暂未实现，请等待 PieSeries 完成");
    Ok(())
}

/// 创建散点图
fn create_scatter_chart(_chart: &mut Chart, data: Vec<DataPoint>) -> Result<(), Box<dyn std::error::Error>> {
    let scatter_data: Vec<(f64, f64)> = data.iter().map(|p| (p.x(), p.y())).collect();
    let _series = ScatterSeries::new("数据系列")
        .data(scatter_data)
        .symbol_size(5.0);
    Ok(())
}

/// 创建热力图（暂未实现 HeatmapSeries）
fn create_heatmap_chart(_chart: &mut Chart, _data: Vec<DataPoint>) -> Result<(), Box<dyn std::error::Error>> {
    // TODO: 实现 HeatmapSeries 后启用此功能
    println!("热力图功能暂未实现，请等待 HeatmapSeries 完成");
    Ok(())
}

// 演示图表创建函数

fn create_demo_line_chart(output_dir: &str) -> Result<(), Box<dyn std::error::Error>> {
    let mut chart = Chart::new();
    chart.title = Some("演示折线图".to_string());
    chart.background_color = Some(Theme::light().background_color);
    
    let data: Vec<(f64, f64)> = (0..20).map(|i| (i as f64, (i as f64 * 0.5).sin() * 10.0 + 20.0)).collect();
    let _series = LineSeries::new("正弦波").data(data).smooth(true);
    
    // let mut renderer = SvgRenderer::default();
    // let svg_content = renderer.render_to_string(&chart)?;
    // fs::write(format!("{}/demo_line.svg", output_dir), svg_content)?;
    println!("  ⚠️ 折线图演示创建完成但未保存（SVG渲染器禁用）");
    
    println!("  ✅ 折线图演示已生成");
    Ok(())
}

fn create_demo_bar_chart(output_dir: &str) -> Result<(), Box<dyn std::error::Error>> {
    let mut chart = Chart::new();
    chart.title = Some("演示柱状图".to_string());
    chart.background_color = Some(Theme::dark().background_color);
    
    let data = vec![
        (1.0, 120.0),  // 一月
        (2.0, 200.0),  // 二月
        (3.0, 150.0),  // 三月
        (4.0, 180.0),  // 四月
    ];
    let _series = BarSeries::new("月度销售").data(data);
    
    // let mut renderer = SvgRenderer::default();
    // let svg_content = renderer.render_to_string(&chart)?;
    // fs::write(format!("{}/demo_bar.svg", output_dir), svg_content)?;

    println!("  ⚠️ 柱状图演示创建完成但未保存（SVG渲染器禁用）");
    Ok(())
}

fn create_demo_pie_chart(_output_dir: &str) -> Result<(), Box<dyn std::error::Error>> {
    // TODO: 实现 PieSeries 后启用此功能
    println!("  ⚠️ 饼图演示暂未实现，请等待 PieSeries 完成");
    Ok(())
}

fn create_demo_scatter_chart(output_dir: &str) -> Result<(), Box<dyn std::error::Error>> {
    let mut chart = Chart::new();
    chart.title = Some("演示散点图".to_string());
    chart.background_color = Some(Theme::macarons().background_color);
    
    let data: Vec<(f64, f64)> = (0..50).map(|i| {
        let x = i as f64;
        let y = (x * 0.1).sin() * 50.0 + pseudo_random(i) * 20.0;
        (x, y)
    }).collect();
    let _series = ScatterSeries::new("随机数据")
        .data(data)
        .symbol_size(4.0);
    
    // let mut renderer = SvgRenderer::default();
    // let svg_content = renderer.render_to_string(&chart)?;
    // fs::write(format!("{}/demo_scatter.svg", output_dir), svg_content)?;

    println!("  ⚠️ 散点图演示创建完成但未保存（SVG渲染器禁用）");
    Ok(())
}

fn create_demo_heatmap_chart(_output_dir: &str) -> Result<(), Box<dyn std::error::Error>> {
    // TODO: 实现 HeatmapSeries 后启用此功能
    println!("  ⚠️ 热力图演示暂未实现，请等待 HeatmapSeries 完成");
    Ok(())
}

fn create_demo_surface3d_chart(_output_dir: &str) -> Result<(), Box<dyn std::error::Error>> {
    // TODO: 实现 Surface3DSeries 后启用此功能
    println!("  ⚠️ 3D曲面图演示暂未实现，请等待 Surface3DSeries 完成");
    Ok(())
}

fn pseudo_random(seed: usize) -> f64 {
    let a = 1664525u64;
    let c = 1013904223u64;
    let m = 2u64.pow(32);
    let x = ((a * seed as u64 + c) % m) as f64 / m as f64;
    x * 2.0 - 1.0
}
