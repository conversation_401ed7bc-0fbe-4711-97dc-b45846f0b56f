/*
 * @Author: Artis
 * @Date: 2025-06-23 23:12:48
 * @LastEditors: Artis
 * @LastEditTime: 2025-06-28 01:33:19
 * @FilePath: \FscDAQ_GPUI\crates\tsdaq_protocol\src\codec.rs
 * @Description:
 *
 * Copyright (c) 2025 by Artis, All Rights Reserved.
 */

use crate::ProtocolError;
use bytes::{Buf, BufMut, Bytes, BytesMut};
use memchr::memmem;
use std::collections::VecDeque;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::Arc;
use std::time::Instant;
use tracing::{debug, error, trace, warn};

/// 校验方法枚举，决定使用哪种校验和算法
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
pub enum CheckMethod {
    /// CRC16 校验 (更可靠但计算量大)
    Crc16,
    /// 无校验 (高速模式)
    NoCheck,
    /// 简单累加校验和 (折中)
    Checksum,
}

/// 编解码器性能统计
#[derive(Debug, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct CodecStats {
    /// 编码的帧数量
    pub frames_encoded: usize,
    /// 解码成功的帧数量
    pub frames_decoded: usize,
    /// 解码失败的帧数量
    pub frames_failed: usize,
    /// 校验和错误数量
    pub checksum_errors: usize,
    /// 处理的总字节数
    pub total_bytes_processed: usize,
    /// 最近100次解码的平均时间 (微秒)
    pub avg_decode_time_us: f64,
    /// 最近100次编码的平均时间 (微秒)
    pub avg_encode_time_us: f64,
    /// 缓冲区溢出次数
    pub buffer_overflows: usize,
}

/// 帧编解码接口
pub trait FrameCodec: Send + Sync {
    /// 将帧编码为字节序列
    fn encode(&self, frame: &Frame, buf: &mut BytesMut);

    /// 从字节序列解码帧
    fn decode(&self, buf: &mut BytesMut) -> Result<Option<Frame>, ProtocolError>;

    /// 删除无效的帧头数据
    fn delete_invalid_frame_header(&self, buf: &mut BytesMut);

    /// 获取编解码器统计信息
    fn get_stats(&self) -> CodecStats;
}

/// 帧结构，表示一个完整的协议帧
#[derive(Debug, Clone, PartialEq)]
pub struct Frame {
    /// 帧头 (2字节)
    pub header: [u8; 2],
    /// 功能码 (1字节)
    pub function: u8,
    /// 地址 (2字节)
    pub address: u16,
    /// 帧数据
    pub data: Bytes,
}

/// 帧解码缓存
#[derive(Debug)]
struct DecodeCache {
    /// 最近处理的时间列表
    process_times: VecDeque<u64>,
    /// 平均处理时间 (微秒)
    avg_time: f64,
    /// 处理的帧数统计
    decoded_count: AtomicUsize,
    /// 失败的帧数统计
    failed_count: AtomicUsize,
    /// 校验和错误统计
    checksum_errors: AtomicUsize,
    /// 处理的总字节数
    total_bytes: AtomicUsize,
}

impl DecodeCache {
    fn new() -> Self {
        Self {
            process_times: VecDeque::with_capacity(100),
            avg_time: 0.0,
            decoded_count: AtomicUsize::new(0),
            failed_count: AtomicUsize::new(0),
            checksum_errors: AtomicUsize::new(0),
            total_bytes: AtomicUsize::new(0),
        }
    }

    fn record_decode_time(&mut self, time_us: u64) {
        self.process_times.push_back(time_us);
        if self.process_times.len() > 100 {
            self.process_times.pop_front();
        }

        // 更新平均时间
        self.avg_time =
            self.process_times.iter().sum::<u64>() as f64 / self.process_times.len() as f64;
    }

    fn increment_decoded(&self) {
        self.decoded_count.fetch_add(1, Ordering::Relaxed);
    }

    fn increment_failed(&self) {
        self.failed_count.fetch_add(1, Ordering::Relaxed);
    }

    fn increment_checksum_error(&self) {
        self.checksum_errors.fetch_add(1, Ordering::Relaxed);
    }

    fn add_bytes_processed(&self, bytes: usize) {
        self.total_bytes.fetch_add(bytes, Ordering::Relaxed);
    }
}

/// 标准编解码器实现
pub struct StandardCodec {
    /// 校验方法
    check_method: CheckMethod,
    /// 缓存
    cache: Arc<std::sync::Mutex<DecodeCache>>,
    /// 编码统计
    encode_times: Arc<std::sync::Mutex<VecDeque<u64>>>,
    /// 编码的帧数量
    encoded_count: AtomicUsize,
}

impl StandardCodec {
    /// 创建新的标准编解码器
    pub fn new(check_method: CheckMethod) -> Self {
        let mut encode_times = VecDeque::with_capacity(100);
        Self {
            check_method,
            cache: Arc::new(std::sync::Mutex::new(DecodeCache::new())),
            encode_times: Arc::new(std::sync::Mutex::new(encode_times)),
            encoded_count: AtomicUsize::new(0),
        }
    }

    /// 计算CRC16校验和
    ///
    /// 使用Modbus算法 (多项式0xA001)
    pub fn crc16(&self, data: &[u8]) -> u16 {
        let mut crc: u16 = 0xFFFF;

        if data.is_empty() {
            return crc;
        }

        // 使用查表法优化CRC16计算
        // CRC16 MODBUS 查找表
        static CRC_TABLE: [u16; 256] = [
            0x0000, 0xC0C1, 0xC181, 0x0140, 0xC301, 0x03C0, 0x0280, 0xC241, 0xC601, 0x06C0, 0x0780,
            0xC741, 0x0500, 0xC5C1, 0xC481, 0x0440, 0xCC01, 0x0CC0, 0x0D80, 0xCD41, 0x0F00, 0xCFC1,
            0xCE81, 0x0E40, 0x0A00, 0xCAC1, 0xCB81, 0x0B40, 0xC901, 0x09C0, 0x0880, 0xC841, 0xD801,
            0x18C0, 0x1980, 0xD941, 0x1B00, 0xDBC1, 0xDA81, 0x1A40, 0x1E00, 0xDEC1, 0xDF81, 0x1F40,
            0xDD01, 0x1DC0, 0x1C80, 0xDC41, 0x1400, 0xD4C1, 0xD581, 0x1540, 0xD701, 0x17C0, 0x1680,
            0xD641, 0xD201, 0x12C0, 0x1380, 0xD341, 0x1100, 0xD1C1, 0xD081, 0x1040, 0xF001, 0x30C0,
            0x3180, 0xF141, 0x3300, 0xF3C1, 0xF281, 0x3240, 0x3600, 0xF6C1, 0xF781, 0x3740, 0xF501,
            0x35C0, 0x3480, 0xF441, 0x3C00, 0xFCC1, 0xFD81, 0x3D40, 0xFF01, 0x3FC0, 0x3E80, 0xFE41,
            0xFA01, 0x3AC0, 0x3B80, 0xFB41, 0x3900, 0xF9C1, 0xF881, 0x3840, 0x2800, 0xE8C1, 0xE981,
            0x2940, 0xEB01, 0x2BC0, 0x2A80, 0xEA41, 0xEE01, 0x2EC0, 0x2F80, 0xEF41, 0x2D00, 0xEDC1,
            0xEC81, 0x2C40, 0xE401, 0x24C0, 0x2580, 0xE541, 0x2700, 0xE7C1, 0xE681, 0x2640, 0x2200,
            0xE2C1, 0xE381, 0x2340, 0xE101, 0x21C0, 0x2080, 0xE041, 0xA001, 0x60C0, 0x6180, 0xA141,
            0x6300, 0xA3C1, 0xA281, 0x6240, 0x6600, 0xA6C1, 0xA781, 0x6740, 0xA501, 0x65C0, 0x6480,
            0xA441, 0x6C00, 0xACC1, 0xAD81, 0x6D40, 0xAF01, 0x6FC0, 0x6E80, 0xAE41, 0xAA01, 0x6AC0,
            0x6B80, 0xAB41, 0x6900, 0xA9C1, 0xA881, 0x6840, 0x7800, 0xB8C1, 0xB981, 0x7940, 0xBB01,
            0x7BC0, 0x7A80, 0xBA41, 0xBE01, 0x7EC0, 0x7F80, 0xBF41, 0x7D00, 0xBDC1, 0xBC81, 0x7C40,
            0xB401, 0x74C0, 0x7580, 0xB541, 0x7700, 0xB7C1, 0xB681, 0x7640, 0x7200, 0xB2C1, 0xB381,
            0x7340, 0xB101, 0x71C0, 0x7080, 0xB041, 0x5000, 0x90C1, 0x9181, 0x5140, 0x9301, 0x53C0,
            0x5280, 0x9241, 0x9601, 0x56C0, 0x5780, 0x9741, 0x5500, 0x95C1, 0x9481, 0x5440, 0x9C01,
            0x5CC0, 0x5D80, 0x9D41, 0x5F00, 0x9FC1, 0x9E81, 0x5E40, 0x5A00, 0x9AC1, 0x9B81, 0x5B40,
            0x9901, 0x59C0, 0x5880, 0x9841, 0x8801, 0x48C0, 0x4980, 0x8941, 0x4B00, 0x8BC1, 0x8A81,
            0x4A40, 0x4E00, 0x8EC1, 0x8F81, 0x4F40, 0x8D01, 0x4DC0, 0x4C80, 0x8C41, 0x4400, 0x84C1,
            0x8581, 0x4540, 0x8701, 0x47C0, 0x4680, 0x8641, 0x8201, 0x42C0, 0x4380, 0x8341, 0x4100,
            0x81C1, 0x8081, 0x4040,
        ];

        for byte in data {
            let index = ((crc ^ (*byte as u16)) & 0xFF) as usize;
            crc = (crc >> 8) ^ CRC_TABLE[index];
        }

        crc
    }

    /// 记录编码时间
    fn record_encode_time(&self, time_us: u64) {
        if let Ok(mut times) = self.encode_times.lock() {
            times.push_back(time_us);
            if times.len() > 100 {
                times.pop_front();
            }
        }
    }

    /// 获取平均编码时间
    fn get_avg_encode_time(&self) -> f64 {
        if let Ok(times) = self.encode_times.lock() {
            if !times.is_empty() {
                return times.iter().sum::<u64>() as f64 / times.len() as f64;
            }
        }
        0.0
    }

    /* 短帧数据解码  */
    fn short_frame_data_decode(&self, buf: &mut BytesMut) -> Result<Option<Frame>, ProtocolError> {
        // 解析命令部分
        let start_time = Instant::now();

        // 记录处理的字节数
        if let Ok(mut cache) = self.cache.lock() {
            cache.add_bytes_processed(buf.len());
        }

        // 需要至少6字节才能解析基本帧 (1头 + 数据 4 + 1校验 累加和)
        if buf.len() < 6 {
            trace!("短帧解码: 数据不足，至少需要6字节 (当前: {})", buf.len());
            return Ok(None);
        }

        // 检查帧头 (接收方向帧头为0xDA)
        if buf[0] != 0xDA {
            if let Ok(mut cache) = self.cache.lock() {
                cache.increment_failed();
            }
            return Err(ProtocolError::FrameHeaderError);
        }

        // 完整帧大小 = 1字节固定头 + 4字节数据 + 1字节校验
        let total_len = 6;

        if buf.len() < total_len {
            trace!(
                "短帧解码: 帧不完整，需要 {} 字节 (当前: {})",
                total_len,
                buf.len()
            );
            return Ok(None);
        }
        //固定数据长度 4 字节
        let data_len = 4;

        // 提取帧各部分
        let header = [0, buf[0]];
        let function = 0;
        let address = 0;
        let data_start = 1;
        let data_end = data_start + data_len;
        let data = buf[data_start..data_end].to_vec();

        // 计算并验证校验
        let checksum = buf[data_end];
        let calculated = buf[..total_len - 1]
            .iter()
            .fold(0u8, |acc, &x| acc.wrapping_add(x));
        if calculated != checksum {
            if let Ok(mut cache) = self.cache.lock() {
                cache.increment_checksum_error();
            }
            return Err(ProtocolError::ChecksumMismatch);
        }
        // 移除已处理的数据
        buf.advance(total_len);

        // 记录处理时间和成功解码
        let elapsed_us = start_time.elapsed().as_micros() as u64;
        if let Ok(mut cache) = self.cache.lock() {
            cache.record_decode_time(elapsed_us);
            cache.increment_decoded();
        }

        trace!(
            "短帧解析成功: func=0x{:02X}, addr=0x{:04X}, data={} bytes",
            function,
            address,
            data.len()
        );

        Ok(Some(Frame {
            header,
            function,
            address,
            data: Bytes::from(data),
        }))
    }

    /// 标准帧解码
    fn standard_decode(&self, buf: &mut BytesMut) -> Result<Option<Frame>, ProtocolError> {
        let start_time = Instant::now();

        // 记录处理的字节数
        if let Ok(mut cache) = self.cache.lock() {
            cache.add_bytes_processed(buf.len());
        }

        // 解析命令部分
        // 需要至少9字节才能解析基本帧 (2头 + 1功能 + 2地址 + 2长度 + 2校验)
        if buf.len() < 9 {
            trace!("标准帧解码: 数据不足，至少需要9字节 (当前: {})", buf.len());
            return Ok(None);
        }

        // 检查帧头 (接收方向帧头为0xDD, 0xAA)
        if buf[0] != 0xDD || buf[1] != 0xAA {
            if let Ok(mut cache) = self.cache.lock() {
                cache.increment_failed();
            }
            return Err(ProtocolError::FrameHeaderError);
        }

        // 解析长度字段 (位置5-6, 因为功能码在位置2，地址在3-4，所以长度在5-6)
        let data_len = u16::from_be_bytes([buf[5], buf[6]]) as usize;

        // 合理性检查 - 数据长度不应过大
        if data_len > 65535 {
            warn!("可疑的数据长度: {} 字节", data_len);
            if let Ok(mut cache) = self.cache.lock() {
                cache.increment_failed();
            }
            return Err(ProtocolError::FrameFormatError(format!(
                "数据长度过大: {} 字节",
                data_len
            )));
        }

        // 完整帧大小 = 9字节固定头 + 数据长度
        let total_len = 9 + data_len;

        if buf.len() < total_len {
            trace!(
                "标准帧解码: 帧不完整，需要 {} 字节 (当前: {})",
                total_len,
                buf.len()
            );
            return Ok(None);
        }

        // 提取帧各部分
        let header = [buf[0], buf[1]];
        let function = buf[2];
        let address = u16::from_be_bytes([buf[3], buf[4]]);
        let data_start = 7;
        let data_end = data_start + data_len;
        let data = buf[data_start..data_end].to_vec();

        // 计算并验证校验
        match self.check_method {
            CheckMethod::Crc16 => {
                let received_checksum =
                    u16::from_be_bytes([buf[total_len - 2], buf[total_len - 1]]);
                let calculated = self.crc16(&buf[..total_len - 2]);
                if calculated != received_checksum {
                    if let Ok(mut cache) = self.cache.lock() {
                        cache.increment_checksum_error();
                    }
                    return Err(ProtocolError::ChecksumMismatch);
                }
            }
            CheckMethod::Checksum => {
                /* 累加和校验一个字节 */
                let calculated = buf[..total_len - 1]
                    .iter()
                    .fold(0u8, |acc, &x| acc.wrapping_add(x));
                let checksum = buf[total_len - 1];
                if calculated != checksum {
                    if let Ok(mut cache) = self.cache.lock() {
                        cache.increment_checksum_error();
                    }
                    return Err(ProtocolError::ChecksumMismatch);
                }
            }
            CheckMethod::NoCheck => {
                // 不验证校验和
            }
        }

        // 移除已处理的数据
        buf.advance(total_len);

        // 记录处理时间和成功解码
        let elapsed_us = start_time.elapsed().as_micros() as u64;
        if let Ok(mut cache) = self.cache.lock() {
            cache.record_decode_time(elapsed_us);
            cache.increment_decoded();
        }

        trace!(
            "标准帧解析成功: func=0x{:02X}, addr=0x{:04X}, data={} bytes",
            function,
            address,
            data.len()
        );

        Ok(Some(Frame {
            header,
            function,
            address,
            data: Bytes::from(data),
        }))
    }
}

impl FrameCodec for StandardCodec {
    fn encode(&self, frame: &Frame, buf: &mut BytesMut) {
        let start_time = Instant::now();

        // 预先分配足够的空间
        buf.reserve(frame.data.len() + 16);

        // 帧头
        buf.put_slice(&frame.header);

        // 功能码
        buf.put_u8(frame.function);

        // 地址 (大端)
        buf.put_u16(frame.address);

        // 数据长度 (大端)
        let data_len = frame.data.len() as u16;
        buf.put_u16(data_len);

        // 数据
        buf.put_slice(&frame.data);

        // 计算并添加校验
        match self.check_method {
            CheckMethod::Crc16 => {
                let crc = self.crc16(buf);
                buf.put_u16(crc);
            }
            CheckMethod::Checksum => {
                let checksum = buf.iter().fold(0u8, |acc, &x| acc.wrapping_add(x));
                buf.put_u16(checksum as u16);
            }
            CheckMethod::NoCheck => {
                buf.put_u16(0);
            }
        }

        // 记录处理时间和编码数
        let elapsed_us = start_time.elapsed().as_micros() as u64;
        self.record_encode_time(elapsed_us);
        self.encoded_count.fetch_add(1, Ordering::Relaxed);

        trace!("编码帧: len={}, data={} bytes", buf.len(), frame.data.len());
    }

    /// 解析帧
    fn decode(&self, buf: &mut BytesMut) -> Result<Option<Frame>, ProtocolError> {
        // 如果缓冲区为空，直接返回None
        if buf.is_empty() {
            return Ok(None);
        }

        // 首先尝试短帧解码，如果失败则尝试标准帧解码
        match self.short_frame_data_decode(buf) {
            Ok(Some(frame)) => Ok(Some(frame)),
            Ok(None) => self.standard_decode(buf),
            Err(ProtocolError::FrameHeaderError) => self.standard_decode(buf),
            Err(e) => Err(e),
        }
    }

    /// 删除无效的帧头数据
    ///
    /// # 参数
    /// * `buf` - 带有可变字节缓冲区的引用，用于查找和删除无效帧头
    ///
    /// # 返回值
    /// 无返回值。该方法直接操作传入的缓冲区。
    ///
    /// # 功能说明
    /// 1. 尝试在缓冲区中查找帧头 `b"\xDA"`
    /// 2. 如果找不到，则尝试查找双字节帧头 `b"\xDD\xAA"`
    /// 3. 如果仍然找不到，则清空缓冲区
    ///
    /// # 文档测试示例
    ///
    /// ## 测试1: 单字节帧头匹配
    /// 当缓冲区中包含单字节帧头时，应正确删除帧头之前的数据
    /// ```
    /// use bytes::BytesMut;
    ///
    ///
    /// let mut buf = BytesMut::from(&b"\x00\x01\xDAHello World"[..]);
    /// let protocol = StandardCodec::default();
    /// protocol.delete_invalid_frame_header(&mut buf);
    /// assert_eq!(buf.freeze().as_ref(), &b"\xDAHello World"[..]);
    /// ```
    ///
    /// ## 测试2: 双字节帧头匹配
    /// 当缓冲区中不包含单字节帧头但包含双字节帧头时，应正确删除双字节帧头之前的数据
    /// ```
    /// let mut buf = BytesMut::from(&b"\x00\x01\xDD\xAAHello World"[..]);
    /// let protocol = StandardCodec::default();
    /// protocol.delete_invalid_frame_header(&mut buf);
    /// assert_eq!(buf.freeze().as_ref(), &b"\xDD\xAAHello World"[..]);
    /// ```
    ///
    /// ## 测试3: 无有效帧头
    /// 当缓冲区中既没有单字节帧头也没有双字节帧头时，应清空缓冲区
    /// ```
    /// let mut buf = BytesMut::from(&b"\x00\x01\x02\x03"[..]);
    /// let protocol = StandardCodec::default();
    /// protocol.delete_invalid_frame_header(&mut buf);
    /// assert!(buf.is_empty());
    /// ```
    fn delete_invalid_frame_header(&self, buf: &mut BytesMut) {
        // 优化：对于空缓冲区直接返回
        if buf.is_empty() {
            return;
        }

        // 优化：使用memchr快速查找帧头
        let single_byte_header_pos = memchr::memchr(0xDA, buf);
        let double_byte_header_pos = if buf.len() >= 2 {
            memchr::memmem::find(buf, b"\xDD\xAA")
        } else {
            None
        };

        match (single_byte_header_pos, double_byte_header_pos) {
            // Both headers found - keep the earliest valid header
            (Some(single), Some(double)) => {
                let pos = single.min(double);
                if pos > 0 {
                    debug!("删除 {} 字节无效数据", pos);
                    buf.advance(pos);
                }
            }
            // Only single-byte header found
            (Some(pos), None) => {
                if pos > 0 {
                    debug!("删除 {} 字节无效数据 (到单字节帧头)", pos);
                    buf.advance(pos);
                }
            }
            // Only double-byte header found
            (None, Some(pos)) => {
                if pos > 0 {
                    debug!("删除 {} 字节无效数据 (到双字节帧头)", pos);
                    buf.advance(pos);
                }
            }
            // No valid headers found - clear buffer if too large, otherwise keep for more data
            (None, None) => {
                if buf.len() > 1024 {
                    warn!("没有找到有效帧头，清空大型缓冲区 ({} 字节)", buf.len());
                    buf.clear();
                } else {
                    trace!(
                        "没有找到有效帧头，保留小型缓冲区 ({} 字节) 等待更多数据",
                        buf.len()
                    );
                }
            }
        }
    }

    fn get_stats(&self) -> CodecStats {
        // 获取缓存状态
        let mut stats = CodecStats::default();

        if let Ok(cache) = self.cache.lock() {
            stats.frames_decoded = cache.decoded_count.load(Ordering::Relaxed);
            stats.frames_failed = cache.failed_count.load(Ordering::Relaxed);
            stats.checksum_errors = cache.checksum_errors.load(Ordering::Relaxed);
            stats.total_bytes_processed = cache.total_bytes.load(Ordering::Relaxed);
            stats.avg_decode_time_us = cache.avg_time;
        }

        // 获取编码统计
        stats.frames_encoded = self.encoded_count.load(Ordering::Relaxed);
        stats.avg_encode_time_us = self.get_avg_encode_time();

        stats
    }
}

impl Default for StandardCodec {
    fn default() -> Self {
        Self::new(CheckMethod::Crc16)
    }
}
