{"rustc": 1842507548689473721, "features": "[\"alloc\", \"std\"]", "declared_features": "[\"alloc\", \"core\", \"default\", \"libc\", \"logging\", \"rustc-dep-of-std\", \"std\", \"use_std\"]", "target": 11745930252914242013, "profile": 15657897354478470176, "path": 341663623790807340, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\memchr-ee7ec8cc7146aa15\\dep-lib-memchr", "checksum": false}}], "rustflags": ["-C", "link-arg=/STACK:16000000"], "config": 2069994364910194474, "compile_kind": 0}