//! 组件模块测试套件

#[cfg(test)]
mod tests {
    use crate::*;
    use echarts_core::Color;

    #[test]
    fn test_title_creation() {
        let title = Title::new("测试标题");

        assert_eq!(title.text, "测试标题");
        assert!(!title.text.is_empty());

        println!("✅ 标题创建测试通过");
    }

    #[test]
    fn test_title_with_subtitle() {
        let mut title = Title::new("主标题");
        title.subtext = Some("副标题".to_string());

        assert_eq!(title.text, "主标题");
        assert_eq!(title.subtext, Some("副标题".to_string()));

        println!("✅ 带副标题的标题测试通过");
    }

    #[test]
    fn test_legend_creation() {
        let legend = Legend::new();

        // 验证图例的基本属性
        assert!(legend.visible);

        println!("✅ 图例创建测试通过");
    }

    #[test]
    fn test_tooltip_creation() {
        let tooltip = Tooltip::new();

        // 验证提示框的基本属性
        assert!(tooltip.visible);

        println!("✅ 提示框创建测试通过");
    }

    #[test]
    fn test_axis_creation() {
        let axis = Axis::new();

        // 验证坐标轴的基本属性
        assert!(axis.visible);

        println!("✅ 坐标轴创建测试通过");
    }

    #[test]
    fn test_component_performance() {
        use std::time::Instant;

        // 测试组件创建性能
        let start = Instant::now();
        for _ in 0..100 {
            let _title = Title::new("性能测试");
            let _legend = Legend::new();
            let _tooltip = Tooltip::new();
            let _axis = Axis::new();
        }
        let creation_time = start.elapsed();

        assert!(creation_time.as_millis() < 100); // 应该很快

        println!("✅ 组件性能测试通过");
        println!("   - 组件创建时间: {:?}", creation_time);
    }

    #[test]
    fn test_component_cloning() {
        let original_title = Title::new("原始标题");
        let cloned_title = original_title.clone();

        assert_eq!(original_title.text, cloned_title.text);
        assert_eq!(original_title.visible, cloned_title.visible);

        println!("✅ 组件克隆测试通过");
    }

    #[test]
    fn test_component_edge_cases() {
        // 测试边界情况

        // 空标题
        let empty_title = Title::new("");
        assert!(empty_title.text.is_empty());

        // 很长的标题
        let long_title = Title::new(&"很长的标题".repeat(100));
        assert!(long_title.text.len() > 100);

        println!("✅ 组件边界情况测试通过");
    }
}
