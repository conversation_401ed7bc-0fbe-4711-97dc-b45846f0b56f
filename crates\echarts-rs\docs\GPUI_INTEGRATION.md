# GPUI Integration Guide

ECharts-rs comes with **GPUI rendering enabled by default**, providing high-performance, GPU-accelerated chart rendering out of the box.

## 🚀 Quick Start with GPUI

### Default Configuration

ECharts-rs is configured to use GPUI rendering by default:

```toml
[dependencies]
echarts-rs = "0.1.0"  # GPUI renderer included by default
```

### Basic Usage

```rust
use echarts_rs::prelude::*;

fn main() -> Result<()> {
    // Create chart data
    let data = vec![
        ("Jan".to_string(), 120.0),
        ("Feb".to_string(), 132.0),
        ("Mar".to_string(), 101.0),
        ("Apr".to_string(), 134.0),
    ];

    // Create and render chart with GPUI
    let chart = Chart::line_chart("Monthly Sales", data)
        .title(Title::new("Sales Trend"))
        .theme("light");

    // Render with GPU acceleration (default)
    chart.render_with_gpui(800.0, 600.0)?;
    
    Ok(())
}
```

## 🎯 GPUI Features

### GPU Acceleration
- **Hardware acceleration** for smooth rendering
- **High performance** with large datasets (100K+ points)
- **Smooth animations** with 60+ FPS
- **Memory efficient** GPU buffer management

### Modern UI Integration
- **Native GPUI elements** for seamless integration
- **Responsive layouts** that adapt to window size
- **Event handling** for interactive charts
- **Theme integration** with GPUI's styling system

## 📊 Chart Types with GPUI

### Line Charts
```rust
use echarts_rs::prelude::*;

let chart = Chart::line_chart("Sales", data)
    .title(Title::new("Sales Trend"))
    .smooth(true)
    .show_symbols(true);

chart.render_with_gpui(800.0, 600.0)?;
```

### Bar Charts
```rust
let chart = Chart::bar_chart("Products", data)
    .title(Title::new("Product Performance"))
    .grid(Grid::new().show_grid_lines(true));

chart.render_with_gpui(800.0, 600.0)?;
```

### Pie Charts
```rust
let pie_data = vec![
    PieDataItem::new("Desktop", 60.0),
    PieDataItem::new("Mobile", 30.0),
    PieDataItem::new("Tablet", 10.0),
];

let chart = Chart::pie_chart("Device Usage", pie_data)
    .title(Title::new("Device Distribution"));

chart.render_with_gpui(800.0, 600.0)?;
```

## 🎨 GPUI Element Integration

### Creating GPUI Elements

```rust
use echarts_rs::prelude::*;
use gpui::*;

// Create a chart element for GPUI
let chart = Chart::line_chart("Data", data);
let chart_element = chart.to_gpui_element()
    .size(800.0, 600.0);

// Use in GPUI view
impl Render for MyView {
    fn render(&mut self, cx: &mut ViewContext<Self>) -> impl IntoElement {
        div()
            .size_full()
            .child(chart_element)
    }
}
```

### Interactive Charts

```rust
use gpui::*;

struct InteractiveChartView {
    chart: Chart,
    data: Vec<(String, f64)>,
}

impl InteractiveChartView {
    fn update_data(&mut self, new_data: Vec<(String, f64)>) {
        self.data = new_data;
        self.chart = Chart::line_chart("Updated Data", self.data.clone());
    }
}

impl Render for InteractiveChartView {
    fn render(&mut self, cx: &mut ViewContext<Self>) -> impl IntoElement {
        div()
            .flex()
            .flex_col()
            .child(
                // Chart area
                div()
                    .flex_1()
                    .child(self.chart.to_gpui_element())
            )
            .child(
                // Controls
                div()
                    .p_4()
                    .child(
                        div()
                            .px_3()
                            .py_2()
                            .bg(rgb(0x007acc))
                            .text_color(rgb(0xffffff))
                            .rounded_md()
                            .cursor_pointer()
                            .on_click(cx.listener(|view, _event, _cx| {
                                // Update chart data
                                let new_data = generate_random_data();
                                view.update_data(new_data);
                            }))
                            .child("Refresh Data")
                    )
            )
    }
}
```

## ⚡ Performance Optimization

### Large Datasets

```rust
// Optimized for large datasets
let large_data: Vec<(f64, f64)> = (0..100000)
    .map(|i| (i as f64, (i as f64 * 0.01).sin()))
    .collect();

let chart = Chart::scatter_chart("Large Dataset", large_data)
    .symbol_size(SymbolSize::Fixed(1.0))  // Small symbols for performance
    .animation(Animation::none());        // Disable animation for large data

chart.render_with_gpui(1200.0, 800.0)?;
```

### GPU Memory Management

```rust
// Configure GPU memory usage
let renderer = GpuiRenderer::new(800.0, 600.0)
    .with_memory_limit(100 * 1024 * 1024)  // 100MB limit
    .with_buffer_pooling(true)             // Enable buffer pooling
    .with_texture_compression(true);       // Enable texture compression

let context = GpuiRenderContext::new(800.0, 600.0)
    .with_vsync(true)                      // Enable VSync
    .with_msaa(4);                         // 4x anti-aliasing

renderer.render_chart(&chart, &context)?;
```

## 🎬 Animations with GPUI

### Smooth Animations

```rust
let chart = Chart::line_chart("Animated Data", data)
    .animation(Animation::elastic())       // Elastic animation
    .transition_duration(1000);           // 1 second transition

chart.render_with_gpui(800.0, 600.0)?;
```

### Custom Animation Timing

```rust
let animation = Animation::new()
    .duration(800)
    .easing(EasingFunction::EaseOutBounce)
    .delay(200)
    .stagger(50);  // Stagger animation for multiple elements

let chart = Chart::bar_chart("Staggered Animation", data)
    .animation(animation);

chart.render_with_gpui(800.0, 600.0)?;
```

## 🎨 Theming with GPUI

### Built-in Themes

```rust
// Light theme (default)
let chart = Chart::line_chart("Data", data)
    .theme("light");

// Dark theme
let chart = Chart::line_chart("Data", data)
    .theme("dark");

chart.render_with_gpui(800.0, 600.0)?;
```

### Custom GPUI Styling

```rust
use gpui::*;

// Custom styled chart container
div()
    .bg(rgb(0xf5f5f5))
    .border_1()
    .border_color(rgb(0xe0e0e0))
    .rounded_lg()
    .p_4()
    .shadow_lg()
    .child(
        chart.to_gpui_element()
            .size(800.0, 600.0)
    )
```

## 🔧 Configuration

### Feature Flags

```toml
[dependencies]
echarts-rs = { version = "0.1.0", features = ["gpui-renderer"] }

# Or with additional features
echarts-rs = { 
    version = "0.1.0", 
    features = ["gpui-renderer", "svg-renderer", "data-processing"] 
}
```

### Renderer Settings

```rust
// Configure GPUI renderer
let settings = GpuiRenderSettings {
    width: 1200.0,
    height: 800.0,
    dpi_scale: 2.0,           // High DPI support
    vsync: true,              // Smooth rendering
    msaa_samples: 4,          // Anti-aliasing
    background_color: Color::WHITE,
    enable_animations: true,
    max_fps: 60,
};

let renderer = GpuiRenderer::with_settings(settings);
```

## 📱 Responsive Design

### Adaptive Sizing

```rust
impl Render for ResponsiveChartView {
    fn render(&mut self, cx: &mut ViewContext<Self>) -> impl IntoElement {
        let window_size = cx.window_bounds().size;
        let chart_width = window_size.width * 0.8;
        let chart_height = window_size.height * 0.6;

        div()
            .size_full()
            .child(
                self.chart.to_gpui_element()
                    .size(chart_width, chart_height)
            )
    }
}
```

### Breakpoint-based Layouts

```rust
fn responsive_chart_layout(window_width: f64) -> impl IntoElement {
    let (chart_width, chart_height) = if window_width < 768.0 {
        // Mobile layout
        (window_width * 0.95, 400.0)
    } else if window_width < 1024.0 {
        // Tablet layout
        (window_width * 0.85, 500.0)
    } else {
        // Desktop layout
        (800.0, 600.0)
    };

    chart.to_gpui_element()
        .size(chart_width, chart_height)
}
```

## 🚀 Best Practices

### Performance Tips

1. **Use appropriate chart types** for your data size
2. **Disable animations** for large datasets (>10K points)
3. **Use small symbol sizes** for scatter plots with many points
4. **Enable GPU memory pooling** for frequent updates
5. **Batch chart updates** instead of individual changes

### Memory Management

```rust
// Efficient memory usage
let chart = Chart::scatter_chart("Large Data", data)
    .symbol_size(SymbolSize::Fixed(2.0))   // Small symbols
    .large_dataset_optimization(true)      // Enable optimizations
    .level_of_detail(true)                 // Use LOD for distant points
    .culling(true);                        // Enable frustum culling

chart.render_with_gpui(800.0, 600.0)?;
```

### Error Handling

```rust
use echarts_rs::prelude::*;

fn render_chart_safely() -> Result<()> {
    let chart = Chart::line_chart("Data", data);
    
    match chart.render_with_gpui(800.0, 600.0) {
        Ok(_) => println!("Chart rendered successfully"),
        Err(ChartError::GpuError(msg)) => {
            eprintln!("GPU error: {}", msg);
            // Fallback to software rendering
            chart.render_with_svg(800.0, 600.0)?;
        }
        Err(e) => return Err(e),
    }
    
    Ok(())
}
```

## 📚 Examples

Check out these complete examples:

- [`gpui_integration.rs`](../examples/gpui_integration.rs) - Full GPUI integration demo
- [`performance_test.rs`](../examples/performance_test.rs) - Performance benchmarks
- [`interactive_charts.rs`](../examples/interactive_charts.rs) - Interactive chart examples

## 🔗 Related Documentation

- [GPUI Documentation](https://docs.rs/gpui)
- [ECharts-rs API Reference](https://docs.rs/echarts-rs)
- [Performance Guide](PERFORMANCE.md)
- [Theming Guide](THEMING.md)
