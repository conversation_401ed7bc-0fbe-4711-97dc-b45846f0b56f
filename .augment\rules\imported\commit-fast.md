---
type: "agent_requested"
description: "Example description"
---
# 快速提交任务

根据已暂存的更改生成3条提交消息建议，然后自动使用第一条建议而无需用户确认。

遵循常规提交格式，使用适当的表情符号，并创建描述性消息来解释更改的目的。跳过手动消息选择步骤以简化提交过程。

## 步骤：
1. 运行 `git status` 查看已暂存的更改
2. 生成3条遵循常规提交格式的提交消息建议
3. 自动选择第一条建议
4. 使用所选消息执行 `git commit -m`
5. 从提交中排除Claude共同作者页脚

## 提交类型：
- ✨ feat: 新功能
- 🐛 fix: Bug修复
- 📝 docs: 文档更改
- ♻️ refactor: 代码重构
- 🧑‍💻 chore: 工具和维护
- 🎨 style: 代码格式化，缺少分号等
- ⚡️ perf: 性能改进
- ✅ test: 添加或修正测试
description:
globs:
alwaysApply: false
---


