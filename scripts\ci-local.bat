@echo off
REM ECharts-rs 本地 CI/CD 检查脚本 (Windows Batch)

setlocal enabledelayedexpansion

set ACTION=%1
if "%ACTION%"=="" set ACTION=check

echo ========================================
echo     ECharts-rs 本地 CI/CD 检查
echo ========================================
echo.

if "%ACTION%"=="help" goto :help
if "%ACTION%"=="clean" goto :clean
if "%ACTION%"=="format" goto :format
if "%ACTION%"=="check" goto :check

echo [ERROR] 未知选项: %ACTION%
goto :help

:check
echo [INFO] 检查代码格式...
cargo fmt --all -- --check
if !errorlevel! neq 0 (
    echo [ERROR] 代码格式检查失败
    echo [INFO] 运行 'cargo fmt --all' 来修复格式问题
    exit /b 1
)
echo [SUCCESS] 代码格式检查通过

echo [INFO] 运行 Clippy 检查...
cargo clippy --all-targets --all-features -- -D warnings
if !errorlevel! neq 0 (
    echo [ERROR] Clippy 检查失败
    exit /b 1
)
echo [SUCCESS] Clippy 检查通过

echo [INFO] 构建项目...
cargo build --workspace --all-features
if !errorlevel! neq 0 (
    echo [ERROR] 构建失败
    exit /b 1
)
echo [SUCCESS] 构建检查通过

echo [INFO] 运行测试...
cargo test --workspace --all-features
if !errorlevel! neq 0 (
    echo [ERROR] 测试失败
    exit /b 1
)
echo [SUCCESS] 测试通过

echo [INFO] 测试 ECharts-rs 核心模块...
cargo test --package rust-echarts-core
if !errorlevel! neq 0 (
    echo [ERROR] 核心模块测试失败
    exit /b 1
)
echo [SUCCESS] 核心模块测试通过

echo [INFO] 测试 ECharts-rs 渲染器模块...
cargo test --package rust-echarts-renderer --features gpui-backend
if !errorlevel! neq 0 (
    echo [ERROR] 渲染器模块测试失败
    exit /b 1
)
echo [SUCCESS] 渲染器模块测试通过

echo [INFO] 构建示例程序...
cargo build --examples --all-features
if !errorlevel! neq 0 (
    echo [ERROR] 示例程序构建失败
    exit /b 1
)
echo [SUCCESS] 示例程序构建成功

echo.
echo ========================================
echo           检查结果总结
echo ========================================
echo [SUCCESS] 所有检查都通过了！🎉
echo [INFO] 代码已准备好提交
exit /b 0

:clean
echo [INFO] 清理构建文件...
cargo clean
echo [SUCCESS] 清理完成
exit /b 0

:format
echo [INFO] 格式化代码...
cargo fmt --all
echo [SUCCESS] 代码格式化完成
exit /b 0

:help
echo 用法: ci-local.bat [选项]
echo.
echo 选项:
echo   check     运行完整的CI检查 (默认)
echo   clean     清理构建文件
echo   format    格式化代码
echo   help      显示此帮助信息
echo.
echo 示例:
echo   ci-local.bat check
echo   ci-local.bat format
echo   ci-local.bat clean
exit /b 0
