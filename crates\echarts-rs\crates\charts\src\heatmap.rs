//! 热力图系列实现
//!
//! 提供完整的热力图功能，包括矩阵数据、颜色映射和视觉映射

use echarts_core::{
    Bounds, Color, CoordinateSystem, DataSet, DataValue, DataPoint, DrawCommand, Point, Result, Series, SeriesType,
    draw_commands::{RectStyle},
    LineStyle, LineCap, LineJoin, TextStyle, FontWeight, FontStyle, TextAlign, TextBaseline,
};
use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};
use serde::{Deserialize, Serialize};

/// 颜色映射类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ColorMapType {
    /// 线性映射
    Linear,
    /// 对数映射
    Logarithmic,
    /// 分段映射
    Piecewise,
}

impl Default for ColorMapType {
    fn default() -> Self {
        ColorMapType::Linear
    }
}

/// 颜色映射配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ColorMap {
    /// 映射类型
    pub map_type: ColorMapType,
    
    /// 颜色范围
    pub colors: Vec<Color>,
    
    /// 数值范围
    pub range: (f64, f64),
    
    /// 是否反转颜色
    pub invert: bool,
    
    /// 分段数量（仅用于分段映射）
    pub pieces: Option<usize>,
}

impl Default for ColorMap {
    fn default() -> Self {
        Self {
            map_type: ColorMapType::Linear,
            colors: vec![
                Color::rgb(0.0, 0.0, 1.0),   // 蓝色（低值）
                Color::rgb(0.0, 1.0, 1.0),   // 青色
                Color::rgb(0.0, 1.0, 0.0),   // 绿色
                Color::rgb(1.0, 1.0, 0.0),   // 黄色
                Color::rgb(1.0, 0.0, 0.0),   // 红色（高值）
            ],
            range: (0.0, 100.0),
            invert: false,
            pieces: None,
        }
    }
}

impl ColorMap {
    /// 创建新的颜色映射
    pub fn new(colors: Vec<Color>, range: (f64, f64)) -> Self {
        Self {
            colors,
            range,
            ..Default::default()
        }
    }
    
    /// 设置映射类型
    pub fn map_type(mut self, map_type: ColorMapType) -> Self {
        self.map_type = map_type;
        self
    }
    
    /// 设置是否反转
    pub fn invert(mut self, invert: bool) -> Self {
        self.invert = invert;
        self
    }
    
    /// 设置分段数量
    pub fn pieces(mut self, pieces: usize) -> Self {
        self.pieces = Some(pieces);
        self
    }
    
    /// 根据数值获取颜色
    pub fn get_color(&self, value: f64) -> Color {
        if self.colors.is_empty() {
            return Color::rgb(0.5, 0.5, 0.5); // 默认灰色
        }
        
        // 归一化值到 [0, 1] 范围
        let normalized = ((value - self.range.0) / (self.range.1 - self.range.0))
            .clamp(0.0, 1.0);
        
        let t = if self.invert { 1.0 - normalized } else { normalized };
        
        match self.map_type {
            ColorMapType::Linear => self.linear_interpolate(t),
            ColorMapType::Logarithmic => {
                let log_t = if t > 0.0 { t.ln() / 1.0_f64.ln() } else { 0.0 };
                self.linear_interpolate(log_t.clamp(0.0, 1.0))
            },
            ColorMapType::Piecewise => {
                let pieces = self.pieces.unwrap_or(5);
                let piece_index = (t * pieces as f64).floor() as usize;
                let piece_index = piece_index.min(self.colors.len() - 1);
                self.colors[piece_index]
            }
        }
    }
    
    /// 线性插值颜色
    fn linear_interpolate(&self, t: f64) -> Color {
        if self.colors.len() == 1 {
            return self.colors[0];
        }
        
        let segment_size = 1.0 / (self.colors.len() - 1) as f64;
        let segment_index = (t / segment_size).floor() as usize;
        let segment_index = segment_index.min(self.colors.len() - 2);
        
        let local_t = (t - segment_index as f64 * segment_size) / segment_size;
        
        let color1 = self.colors[segment_index];
        let color2 = self.colors[segment_index + 1];
        
        Color::rgb(
            color1.r + (color2.r - color1.r) * local_t as f32,
            color1.g + (color2.g - color1.g) * local_t as f32,
            color1.b + (color2.b - color1.b) * local_t as f32,
        )
    }
}

/// 热力图标签配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HeatmapLabel {
    /// 是否显示标签
    pub show: bool,
    
    /// 标签格式化器
    pub formatter: Option<String>,
    
    /// 标签样式
    pub style: TextStyle,
    
    /// 标签位置偏移
    pub offset: (f64, f64),
}

impl Default for HeatmapLabel {
    fn default() -> Self {
        Self {
            show: false,
            formatter: None,
            style: TextStyle {
                font_family: "Arial".to_string(),
                font_size: 10.0,
                font_weight: FontWeight::Normal,
                font_style: FontStyle::Normal,
                color: Color::rgb(0.2, 0.2, 0.2),
                opacity: 1.0,
                align: TextAlign::Center,
                baseline: TextBaseline::Middle,
                rotation: 0.0,
                letter_spacing: 0.0,
                line_height: 1.2,
            },
            offset: (0.0, 0.0),
        }
    }
}

/// 热力图系列
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HeatmapSeries {
    /// 基础配置

    pub config: ChartConfig,

    

    /// 图表数据

    pub data: DataSet,
    
    /// 网格尺寸 (width, height)
    pub grid_size: (usize, usize),
    
    /// 颜色映射
    pub color_map: ColorMap,
    
    /// 单元格间隔
    pub gap: f64,
    
    /// 单元格圆角半径
    pub border_radius: f64,
    
    /// 边框样式
    pub border_width: f64,
    pub border_color: Color,
    
    /// 标签配置
    pub label: HeatmapLabel,
    
    /// 透明度
    pub opacity: f64,

    // visible, z_index 现在在 config 中

    /// 数据范围（自动计算或手动设置）
    pub data_range: Option<(f64, f64)>,
}

impl HeatmapSeries {
    /// 创建新的热力图系列
    pub fn new<S: Into<String>>(name: S) -> Self {
        Self {
            config: ChartConfig {
                name: name.into(),
                ..ChartConfig::default()
            },
            data: DataSet::new(),
            grid_size: (10, 10),
            color_map: ColorMap::default(),
            gap: 1.0,
            border_radius: 0.0,
            border_width: 0.0,
            border_color: Color::rgb(1.0, 1.0, 1.0),
            label: HeatmapLabel::default(),
            opacity: 1.0,
            data_range: None,
        }
    }
    
    /// 从矩阵数据创建热力图
    pub fn from_matrix(mut self, matrix: Vec<Vec<f64>>) -> Self {
        let mut dataset = DataSet::new();
        
        for (y, row) in matrix.iter().enumerate() {
            for (x, &value) in row.iter().enumerate() {
                let point = DataPoint::new(vec![
                    DataValue::Number(x as f64),
                    DataValue::Number(y as f64),
                    DataValue::Number(value),
                ]);
                dataset = dataset.add_point(point);
            }
        }
        
        self.data = dataset;
        self.grid_size = (
            matrix.first().map(|row| row.len()).unwrap_or(0),
            matrix.len(),
        );
        
        // 自动计算数据范围
        let values: Vec<f64> = matrix.iter().flatten().copied().collect();
        if !values.is_empty() {
            let min_val = values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
            let max_val = values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
            self.data_range = Some((min_val, max_val));
            self.color_map.range = (min_val, max_val);
        }
        
        self
    }

    /// 从坐标-值数据创建热力图
    pub fn data<I>(mut self, data: I) -> Self
    where
        I: IntoIterator<Item = (usize, usize, f64)>,
    {
        let mut dataset = DataSet::new();
        let mut max_x = 0;
        let mut max_y = 0;
        let mut values = Vec::new();

        for (x, y, value) in data {
            let point = DataPoint::new(vec![
                DataValue::Number(x as f64),
                DataValue::Number(y as f64),
                DataValue::Number(value),
            ]);
            dataset = dataset.add_point(point);

            max_x = max_x.max(x);
            max_y = max_y.max(y);
            values.push(value);
        }

        self.data = dataset;
        self.grid_size = (max_x + 1, max_y + 1);

        // 自动计算数据范围
        if !values.is_empty() {
            let min_val = values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
            let max_val = values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
            self.data_range = Some((min_val, max_val));
            self.color_map.range = (min_val, max_val);
        }

        self
    }

    /// 设置网格大小
    pub fn grid_size(mut self, width: usize, height: usize) -> Self {
        self.grid_size = (width, height);
        self
    }

    /// 设置颜色映射
    pub fn color_map(mut self, color_map: ColorMap) -> Self {
        self.color_map = color_map;
        self
    }

    /// 设置颜色范围
    pub fn color_range(mut self, colors: Vec<Color>) -> Self {
        self.color_map.colors = colors;
        self
    }

    /// 设置数值范围
    pub fn value_range(mut self, min: f64, max: f64) -> Self {
        self.data_range = Some((min, max));
        self.color_map.range = (min, max);
        self
    }

    /// 设置单元格间隔
    pub fn gap(mut self, gap: f64) -> Self {
        self.gap = gap;
        self
    }

    /// 设置圆角半径
    pub fn border_radius(mut self, radius: f64) -> Self {
        self.border_radius = radius;
        self
    }

    /// 设置边框样式
    pub fn border(mut self, width: f64, color: Color) -> Self {
        self.border_width = width;
        self.border_color = color;
        self
    }

    /// 设置标签显示
    pub fn show_label(mut self, show: bool) -> Self {
        self.label.show = show;
        self
    }

    /// 设置标签格式化器
    pub fn label_formatter<S: Into<String>>(mut self, formatter: S) -> Self {
        self.label.formatter = Some(formatter.into());
        self
    }

    /// 设置透明度
    pub fn opacity(mut self, opacity: f64) -> Self {
        self.opacity = opacity;
        self
    }

    /// 设置可见性
    pub fn visible(mut self, visible: bool) -> Self {
        self.config.visible = visible;
        self
    }

    /// 设置Z轴索引
    pub fn z_index(mut self, z_index: i32) -> Self {
        self.config.z_index = z_index;
        self
    }

    /// 计算单元格大小和位置
    fn calculate_cell_layout(&self, bounds: Bounds) -> (f64, f64, f64, f64) {
        let available_width = bounds.size.width - self.gap * (self.grid_size.0 - 1) as f64;
        let available_height = bounds.size.height - self.gap * (self.grid_size.1 - 1) as f64;

        let cell_width = available_width / self.grid_size.0 as f64;
        let cell_height = available_height / self.grid_size.1 as f64;

        (cell_width, cell_height, bounds.origin.x, bounds.origin.y)
    }

    /// 获取数据值范围
    fn get_value_range(&self) -> (f64, f64) {
        if let Some(range) = self.data_range {
            return range;
        }

        // 从数据中计算范围
        let values: Vec<f64> = self.data.points()
            .iter()
            .filter_map(|point| point.get_number(2))
            .collect();

        if values.is_empty() {
            return (0.0, 1.0);
        }

        let min_val = values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_val = values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));

        (min_val, max_val)
    }
}

/// 实现 Series trait
impl Series for HeatmapSeries {
    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Heatmap
    }

    fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        if !self.config.visible || self.data.is_empty() {
            return Ok(commands);
        }

        // 计算单元格布局
        let (cell_width, cell_height, start_x, start_y) =
            self.calculate_cell_layout(coord_system.bounds());

        // 获取数据值范围
        let (min_val, max_val) = self.get_value_range();

        // 更新颜色映射范围
        let mut color_map = self.color_map.clone();
        color_map.range = (min_val, max_val);

        // 绘制每个单元格
        for point in self.data.points() {
            if let (Some(x), Some(y), Some(value)) = (
                point.get_number(0),
                point.get_number(1),
                point.get_number(2)
            ) {
                let x_idx = x as usize;
                let y_idx = y as usize;

                // 计算单元格位置
                let cell_x = start_x + x_idx as f64 * (cell_width + self.gap);
                let cell_y = start_y + y_idx as f64 * (cell_height + self.gap);

                // 获取颜色
                let color = color_map.get_color(value);

                // 创建矩形样式
                let rect_style = RectStyle {
                    fill: Some(color),
                    stroke: if self.border_width > 0.0 {
                        Some(LineStyle {
                            color: self.border_color,
                            width: self.border_width,
                            opacity: self.opacity,
                            dash_pattern: None,
                            cap: LineCap::Butt,
                            join: LineJoin::Round,
                        })
                    } else {
                        None
                    },
                    opacity: self.opacity,
                    corner_radius: self.border_radius,
                };

                // 添加矩形绘制命令
                commands.push(DrawCommand::Rect {
                    bounds: Bounds::new(cell_x, cell_y, cell_width, cell_height),
                    style: rect_style,
                });

                // 添加标签
                if self.label.show {
                    let label_text = match &self.label.formatter {
                        Some(formatter) => {
                            formatter.replace("{value}", &format!("{:.2}", value))
                                     .replace("{x}", &format!("{}", x_idx))
                                     .replace("{y}", &format!("{}", y_idx))
                        },
                        None => format!("{:.1}", value),
                    };

                    let label_x = cell_x + cell_width / 2.0 + self.label.offset.0;
                    let label_y = cell_y + cell_height / 2.0 + self.label.offset.1;

                    commands.push(DrawCommand::Text {
                        text: label_text,
                        position: Point::new(label_x, label_y),
                        style: self.label.style.clone(),
                    });
                }
            }
        }

        Ok(commands)
    }

    fn bounds(&self) -> Option<Bounds> {
        // 热力图的边界基于网格大小
        None // 热力图通常使用整个可用空间
    }

    fn is_visible(&self) -> bool {
        self.config.visible
    }

    fn z_index(&self) -> i32 {
        self.config.z_index
    }

    fn clone_series(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use echarts_core::CartesianCoordinateSystem;

    #[test]
    fn test_heatmap_series_creation() {
        let series = HeatmapSeries::new("测试热力图")
            .grid_size(5, 5)
            .gap(2.0)
            .show_label(true);

        assert_eq!(series.name, "测试热力图");
        assert_eq!(series.series_type(), SeriesType::Heatmap);
        assert_eq!(series.grid_size, (5, 5));
        assert_eq!(series.gap, 2.0);
        assert!(series.label.show);
        assert!(series.is_visible());
    }

    #[test]
    fn test_heatmap_from_matrix() {
        let matrix = vec![
            vec![1.0, 2.0, 3.0],
            vec![4.0, 5.0, 6.0],
            vec![7.0, 8.0, 9.0],
        ];

        let series = HeatmapSeries::new("矩阵热力图")
            .from_matrix(matrix);

        assert_eq!(series.grid_size, (3, 3));
        assert_eq!(series.data.len(), 9);
        assert_eq!(series.data_range, Some((1.0, 9.0)));
        assert_eq!(series.color_map.range, (1.0, 9.0));
    }

    #[test]
    fn test_heatmap_from_data() {
        let data = vec![
            (0, 0, 10.0),
            (1, 0, 20.0),
            (0, 1, 30.0),
            (1, 1, 40.0),
        ];

        let series = HeatmapSeries::new("坐标热力图")
            .data(data);

        assert_eq!(series.grid_size, (2, 2));
        assert_eq!(series.data.len(), 4);
        assert_eq!(series.data_range, Some((10.0, 40.0)));
    }

    #[test]
    fn test_color_map() {
        let colors = vec![
            Color::rgb(0.0, 0.0, 1.0),
            Color::rgb(1.0, 0.0, 0.0),
        ];

        let color_map = ColorMap::new(colors, (0.0, 100.0));

        // 测试边界值
        let blue = color_map.get_color(0.0);
        let red = color_map.get_color(100.0);
        let middle = color_map.get_color(50.0);

        assert_eq!(blue, Color::rgb(0.0, 0.0, 1.0));
        assert_eq!(red, Color::rgb(1.0, 0.0, 0.0));
        // 中间值应该是插值结果
        assert!(middle.r > 0.0 && middle.r < 1.0);
        assert!(middle.b > 0.0 && middle.b < 1.0);
    }

    #[test]
    fn test_color_map_invert() {
        let colors = vec![
            Color::rgb(0.0, 0.0, 1.0),
            Color::rgb(1.0, 0.0, 0.0),
        ];

        let color_map = ColorMap::new(colors, (0.0, 100.0))
            .invert(true);

        // 反转后，低值应该得到红色，高值应该得到蓝色
        let low_color = color_map.get_color(0.0);
        let high_color = color_map.get_color(100.0);

        assert_eq!(low_color, Color::rgb(1.0, 0.0, 0.0)); // 红色
        assert_eq!(high_color, Color::rgb(0.0, 0.0, 1.0)); // 蓝色
    }

    #[test]
    fn test_render_to_commands() {
        let matrix = vec![
            vec![1.0, 2.0],
            vec![3.0, 4.0],
        ];

        let series = HeatmapSeries::new("渲染测试")
            .from_matrix(matrix)
            .show_label(true);

        let coord_system = CartesianCoordinateSystem::new(
            Bounds::new(0.0, 0.0, 200.0, 200.0),
            (0.0, 1.0),
            (0.0, 1.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();

        // 应该生成8个命令：4个矩形 + 4个标签
        assert_eq!(commands.len(), 8);

        // 检查命令类型
        let mut rect_count = 0;
        let mut text_count = 0;

        for cmd in &commands {
            match cmd {
                DrawCommand::Rect { .. } => rect_count += 1,
                DrawCommand::Text { .. } => text_count += 1,
                _ => {}
            }
        }

        assert_eq!(rect_count, 4); // 4个矩形
        assert_eq!(text_count, 4); // 4个标签
    }

    #[test]
    fn test_empty_data() {
        let series = HeatmapSeries::new("空数据测试");

        let coord_system = CartesianCoordinateSystem::new(
            Bounds::new(0.0, 0.0, 200.0, 200.0),
            (0.0, 1.0),
            (0.0, 1.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();

        // 空数据应该不生成任何命令
        assert!(commands.is_empty());
    }

    #[test]
    fn test_series_trait_object() {
        // 测试类型擦除
        let series: Box<dyn Series> = Box::new(HeatmapSeries::new("类型擦除测试")
            .from_matrix(vec![vec![1.0]]));

        assert_eq!(series.name(), "类型擦除测试");
        assert_eq!(series.series_type(), SeriesType::Heatmap);
        assert!(series.is_visible());
    }
}
// ============================================================================
// 图表基础架构集成 - ChartBase 实现
// ============================================================================

/// 为 HeatmapSeries 实现 ChartBase trait
impl ChartBase for HeatmapSeries {
    type DataType = DataSet;

    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn raw_data(&self) -> &Self::DataType {
        &self.data
    }

    fn to_dataset(&self) -> DataSet {
        // TODO: 实现 DataSet 到 DataSet 的转换
        DataSet::new()
    }
    
    fn visible(&self) -> bool {
        self.config.visible
    }
    
    fn z_index(&self) -> i32 {
        self.config.z_index
    }
    
    fn bounds(&self) -> Option<Bounds> {
        // TODO: 为 HeatmapSeries 实现边界计算
        None
    }
    
    fn config(&self) -> &ChartConfig {
        &self.config
    }
    
    fn config_mut(&mut self) -> &mut ChartConfig {
        &mut self.config
    }
}

/// 为 HeatmapSeries 实现 ChartSeries trait
impl ChartSeries for HeatmapSeries {
    // 所有方法都由默认实现提供，基于 ChartBase
}
