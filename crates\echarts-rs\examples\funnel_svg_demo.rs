//! 漏斗图SVG演示
//!
//! 生成各种漏斗图的SVG文件，展示FunnelSeries的完整功能

use std::fs;
use echarts_rs::{FunnelSeries, FunnelDataItem, FunnelLabel, FunnelLabelPosition, FunnelSort, FunnelAlign, Color};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🔻 漏斗图SVG演示系统");
    println!("{}", "=".repeat(50));

    // 确保输出目录存在
    let output_dir = "temp/svg/funnel_charts";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 基础漏斗图
    println!("\n🔻 1. 生成基础漏斗图...");
    generate_basic_funnel(output_dir)?;

    // 2. 销售漏斗图
    println!("\n💰 2. 生成销售漏斗图...");
    generate_sales_funnel(output_dir)?;

    // 3. 转化率漏斗图
    println!("\n📊 3. 生成转化率漏斗图...");
    generate_conversion_funnel(output_dir)?;

    // 4. 流程分析漏斗图
    println!("\n⚙️ 4. 生成流程分析漏斗图...");
    generate_process_funnel(output_dir)?;

    // 5. 多样式漏斗图
    println!("\n🎨 5. 生成多样式漏斗图...");
    generate_styled_funnel(output_dir)?;

    // 6. 生成展示页面
    println!("\n📄 6. 生成展示页面...");
    generate_funnel_showcase(output_dir)?;

    println!("\n🎉 漏斗图SVG演示生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/funnel_demo.html 查看演示", output_dir);

    Ok(())
}

/// 生成基础漏斗图
fn generate_basic_funnel(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        FunnelDataItem::new("阶段1", 100.0).color(Color::rgb(0.3, 0.6, 1.0)),
        FunnelDataItem::new("阶段2", 80.0).color(Color::rgb(0.6, 0.8, 0.4)),
        FunnelDataItem::new("阶段3", 60.0).color(Color::rgb(1.0, 0.6, 0.3)),
        FunnelDataItem::new("阶段4", 40.0).color(Color::rgb(0.8, 0.4, 0.8)),
        FunnelDataItem::new("阶段5", 20.0).color(Color::rgb(0.4, 0.8, 0.8)),
    ];

    let funnel_series = FunnelSeries::new("基础漏斗图")
        .data(data)
        .position(0.1, 0.1, 0.8, 0.8)
        .sort(FunnelSort::Descending)
        .align(FunnelAlign::Center)
        .gap(3.0)
        .border(1.0, Color::rgb(1.0, 1.0, 1.0));

    let svg = create_funnel_svg(&funnel_series, "基础漏斗图演示", 600.0, 600.0)?;
    fs::write(format!("{}/01_basic_funnel.svg", output_dir), svg)?;
    
    println!("  ✅ 基础漏斗图生成完成");
    Ok(())
}

/// 生成销售漏斗图
fn generate_sales_funnel(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        FunnelDataItem::new("潜在客户", 10000.0)
            .color(Color::rgb(0.2, 0.6, 1.0))
            .label("潜在客户 (10,000)"),
        FunnelDataItem::new("意向客户", 5000.0)
            .color(Color::rgb(0.4, 0.8, 0.6))
            .label("意向客户 (5,000)"),
        FunnelDataItem::new("商机客户", 2000.0)
            .color(Color::rgb(1.0, 0.6, 0.3))
            .label("商机客户 (2,000)"),
        FunnelDataItem::new("报价客户", 800.0)
            .color(Color::rgb(1.0, 0.4, 0.6))
            .label("报价客户 (800)"),
        FunnelDataItem::new("成交客户", 300.0)
            .color(Color::rgb(0.8, 0.2, 0.4))
            .label("成交客户 (300)"),
    ];

    let label = FunnelLabel {
        show: true,
        font_size: 12.0,
        color: Color::rgb(0.1, 0.1, 0.1),
        position: FunnelLabelPosition::Inside,
        formatter: Some("{name}: {value}".to_string()),
    };

    let funnel_series = FunnelSeries::new("销售漏斗")
        .data(data)
        .position(0.15, 0.1, 0.7, 0.8)
        .min_size(0.1)
        .max_size(0.9)
        .sort(FunnelSort::Descending)
        .align(FunnelAlign::Center)
        .gap(5.0)
        .label(label)
        .border(1.5, Color::rgb(0.9, 0.9, 0.9));

    let svg = create_funnel_svg(&funnel_series, "销售漏斗分析", 600.0, 600.0)?;
    fs::write(format!("{}/02_sales_funnel.svg", output_dir), svg)?;
    
    println!("  ✅ 销售漏斗图生成完成");
    Ok(())
}

/// 生成转化率漏斗图
fn generate_conversion_funnel(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        FunnelDataItem::new("访问", 100000.0)
            .color(Color::rgb(0.8, 0.2, 0.2))
            .label("网站访问 100%"),
        FunnelDataItem::new("浏览", 50000.0)
            .color(Color::rgb(1.0, 0.4, 0.2))
            .label("深度浏览 50%"),
        FunnelDataItem::new("注册", 10000.0)
            .color(Color::rgb(1.0, 0.6, 0.2))
            .label("用户注册 10%"),
        FunnelDataItem::new("购买", 2000.0)
            .color(Color::rgb(1.0, 0.8, 0.2))
            .label("完成购买 2%"),
        FunnelDataItem::new("复购", 500.0)
            .color(Color::rgb(0.8, 1.0, 0.2))
            .label("重复购买 0.5%"),
    ];

    let label = FunnelLabel {
        show: true,
        font_size: 11.0,
        color: Color::rgb(0.2, 0.2, 0.2),
        position: FunnelLabelPosition::Right,
        formatter: None,
    };

    let funnel_series = FunnelSeries::new("转化率分析")
        .data(data)
        .position(0.1, 0.1, 0.6, 0.8)
        .min_size(0.05)
        .max_size(0.95)
        .sort(FunnelSort::Descending)
        .align(FunnelAlign::Left)
        .gap(4.0)
        .label(label)
        .border(1.0, Color::rgb(0.8, 0.8, 0.8));

    let svg = create_funnel_svg(&funnel_series, "网站转化率漏斗", 700.0, 600.0)?;
    fs::write(format!("{}/03_conversion_funnel.svg", output_dir), svg)?;
    
    println!("  ✅ 转化率漏斗图生成完成");
    Ok(())
}

/// 生成流程分析漏斗图
fn generate_process_funnel(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        FunnelDataItem::new("需求分析", 90.0)
            .color(Color::rgb(0.2, 0.4, 0.8))
            .label("需求分析阶段"),
        FunnelDataItem::new("方案设计", 75.0)
            .color(Color::rgb(0.4, 0.6, 0.8))
            .label("方案设计阶段"),
        FunnelDataItem::new("开发实现", 60.0)
            .color(Color::rgb(0.6, 0.8, 0.8))
            .label("开发实现阶段"),
        FunnelDataItem::new("测试验证", 45.0)
            .color(Color::rgb(0.8, 1.0, 0.8))
            .label("测试验证阶段"),
        FunnelDataItem::new("上线部署", 30.0)
            .color(Color::rgb(1.0, 1.0, 0.6))
            .label("上线部署阶段"),
    ];

    let label = FunnelLabel {
        show: true,
        font_size: 10.0,
        color: Color::rgb(0.1, 0.1, 0.1),
        position: FunnelLabelPosition::Outside,
        formatter: Some("{name} ({value}%)".to_string()),
    };

    let funnel_series = FunnelSeries::new("项目流程")
        .data(data)
        .position(0.2, 0.15, 0.6, 0.7)
        .min_size(0.2)
        .max_size(0.8)
        .sort(FunnelSort::None) // 保持原始顺序
        .align(FunnelAlign::Center)
        .gap(6.0)
        .label(label)
        .border(2.0, Color::rgb(0.7, 0.7, 0.7));

    let svg = create_funnel_svg(&funnel_series, "项目流程分析", 600.0, 600.0)?;
    fs::write(format!("{}/04_process_funnel.svg", output_dir), svg)?;
    
    println!("  ✅ 流程分析漏斗图生成完成");
    Ok(())
}

/// 生成多样式漏斗图
fn generate_styled_funnel(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        FunnelDataItem::new("优秀", 25.0).color(Color::rgb(0.2, 0.8, 0.2)),
        FunnelDataItem::new("良好", 35.0).color(Color::rgb(0.6, 0.8, 0.2)),
        FunnelDataItem::new("一般", 30.0).color(Color::rgb(1.0, 0.8, 0.2)),
        FunnelDataItem::new("较差", 10.0).color(Color::rgb(1.0, 0.4, 0.2)),
    ];

    let label = FunnelLabel {
        show: true,
        font_size: 13.0,
        color: Color::rgb(0.0, 0.0, 0.0),
        position: FunnelLabelPosition::Inside,
        formatter: Some("{name}\n{value}%".to_string()),
    };

    let funnel_series = FunnelSeries::new("评价分布")
        .data(data)
        .position(0.25, 0.2, 0.5, 0.6)
        .min_size(0.3)
        .max_size(0.7)
        .sort(FunnelSort::Ascending) // 升序排列
        .align(FunnelAlign::Right)
        .gap(8.0)
        .label(label)
        .border(3.0, Color::rgb(0.5, 0.5, 0.5));

    let svg = create_funnel_svg(&funnel_series, "用户评价分布", 500.0, 500.0)?;
    fs::write(format!("{}/05_styled_funnel.svg", output_dir), svg)?;
    
    println!("  ✅ 多样式漏斗图生成完成");
    Ok(())
}

/// 创建漏斗图SVG
fn create_funnel_svg(series: &FunnelSeries, title: &str, width: f64, height: f64) -> std::result::Result<String, Box<dyn std::error::Error>> {
    use echarts_core::{CartesianCoordinateSystem, Bounds, Series, Point, Size};
    
    // 创建坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        Bounds {
            origin: Point { x: 50.0, y: 50.0 },
            size: Size { width: width - 100.0, height: height - 100.0 },
        },
        (0.0, 1.0),
        (0.0, 1.0),
    );
    
    // 获取渲染命令
    let commands = series.render_to_commands(&coord_system)?;
    
    // 生成SVG
    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 渲染命令
    for command in commands {
        render_funnel_svg_command(&mut svg, &command);
    }
    
    svg.push_str("</svg>");
    Ok(svg)
}

/// 渲染漏斗图SVG命令
fn render_funnel_svg_command(svg: &mut String, command: &echarts_core::DrawCommand) {
    use echarts_core::DrawCommand;
    
    match command {
        DrawCommand::Path { commands, style } => {
            let mut path_data = String::new();
            for cmd in commands {
                match cmd {
                    echarts_core::draw_commands::PathCommand::MoveTo(point) => {
                        path_data.push_str(&format!("M {} {} ", point.x, point.y));
                    }
                    echarts_core::draw_commands::PathCommand::LineTo(point) => {
                        path_data.push_str(&format!("L {} {} ", point.x, point.y));
                    }
                    echarts_core::draw_commands::PathCommand::Close => {
                        path_data.push_str("Z ");
                    }
                    _ => {} // 忽略其他路径命令
                }
            }
            
            let fill = style.fill.map(|c| format!("#{:02x}{:02x}{:02x}", 
                (c.r * 255.0) as u8, (c.g * 255.0) as u8, (c.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke = style.stroke.as_ref().map(|s| format!("#{:02x}{:02x}{:02x}", 
                (s.color.r * 255.0) as u8, (s.color.g * 255.0) as u8, (s.color.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke_width = style.stroke.as_ref().map(|s| s.width).unwrap_or(0.0);
            
            svg.push_str(&format!("  <path d=\"{}\" fill=\"{}\" stroke=\"{}\" stroke-width=\"{}\" opacity=\"{}\"/>\n", 
                path_data.trim(), fill, stroke, stroke_width, style.opacity));
        }
        DrawCommand::Text { text, position, style } => {
            let color = format!("#{:02x}{:02x}{:02x}", 
                (style.color.r * 255.0) as u8, (style.color.g * 255.0) as u8, (style.color.b * 255.0) as u8);
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"{}\" fill=\"{}\" text-anchor=\"middle\" opacity=\"{}\">{}</text>\n", 
                position.x, position.y, style.font_size, color, style.opacity, text));
        }
        _ => {} // 忽略其他命令类型
    }
}

/// 生成展示页面
fn generate_funnel_showcase(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts-rs 漏斗图演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .section {
            background: rgba(255,255,255,0.95);
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .chart-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        .chart-svg {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h2 { color: #333; margin-bottom: 20px; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        .description {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .config-info {
            background: rgba(255,255,255,0.05);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔻 ECharts-rs 漏斗图演示</h1>
            <p class="description">展现 FunnelSeries 的强大功能和转化率分析的梯形可视化能力</p>
            <div class="feature-list">
                <div class="feature">
                    <h3>🔻 基础漏斗图</h3>
                    <p>经典的梯形映射设计</p>
                </div>
                <div class="feature">
                    <h3>💰 销售漏斗</h3>
                    <p>销售流程转化分析</p>
                </div>
                <div class="feature">
                    <h3>📊 转化率分析</h3>
                    <p>网站用户行为漏斗</p>
                </div>
                <div class="feature">
                    <h3>⚙️ 流程分析</h3>
                    <p>业务流程效率展示</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔻 基础漏斗图功能</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">基础漏斗图</div>
                    <object class="chart-svg" data="01_basic_funnel.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">销售漏斗分析</div>
                    <object class="chart-svg" data="02_sales_funnel.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 实际应用场景</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">网站转化率漏斗</div>
                    <object class="chart-svg" data="03_conversion_funnel.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">项目流程分析</div>
                    <object class="chart-svg" data="04_process_funnel.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎨 样式配置展示</h2>
            <div class="chart-item">
                <div class="chart-title">用户评价分布（多样式配置）</div>
                <object class="chart-svg" data="05_styled_funnel.svg" type="image/svg+xml">SVG不支持</object>
            </div>

            <div class="config-info">
                <h3>🔧 配置选项说明</h3>
                <ul>
                    <li><strong>排序方式</strong>：支持降序、升序、不排序三种模式</li>
                    <li><strong>对齐方式</strong>：左对齐、居中对齐、右对齐</li>
                    <li><strong>标签位置</strong>：内侧、外侧、左侧、右侧四种位置</li>
                    <li><strong>尺寸控制</strong>：可配置最小和最大宽度占比</li>
                    <li><strong>间隙设置</strong>：可调节各层级之间的间距</li>
                    <li><strong>边框样式</strong>：可配置边框宽度和颜色</li>
                </ul>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h2>🎉 FunnelSeries 功能总结</h2>
            <p>ECharts-rs FunnelSeries 完整功能展示：</p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>✅ <strong>转化率分析</strong> - 支持销售漏斗、用户转化等业务分析</li>
                <li>✅ <strong>梯形映射算法</strong> - 数值大小直观的宽度表示</li>
                <li>✅ <strong>灵活的标签系统</strong> - 可配置的标签位置和格式化</li>
                <li>✅ <strong>多种排序模式</strong> - 降序、升序、保持原序</li>
                <li>✅ <strong>对齐方式配置</strong> - 左对齐、居中、右对齐</li>
                <li>✅ <strong>尺寸范围控制</strong> - 最小最大宽度占比设置</li>
                <li>✅ <strong>间隙和边框</strong> - 可调节的层级间距和边框样式</li>
                <li>✅ <strong>高质量渲染</strong> - 优化的SVG输出和视觉效果</li>
            </ul>
        </div>
    </div>
</body>
</html>"#;

    fs::write(format!("{}/funnel_demo.html", output_dir), html_content)?;
    Ok(())
}
