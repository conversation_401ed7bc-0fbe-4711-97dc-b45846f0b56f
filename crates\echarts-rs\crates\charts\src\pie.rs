//! 饼图系列实现
//!
//! 提供完整的饼图功能，包括环形图、玫瑰图、标签和引导线等高级特性

use echarts_core::{
    Bounds, Color, CoordinateSystem, DataSet, DataValue, DataPoint, DrawCommand, Point, Result, Series, SeriesType,
    draw_commands::{PathCommand, PathStyle, FillRule},
    LineStyle, LineCap, LineJoin, TextStyle, FontWeight, FontStyle, TextAlign, TextBaseline,
};
use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};
use serde::{Deserialize, Serialize};
use std::f64::consts::PI;

/// 饼图半径配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PieRadius {
    /// 单一半径（普通饼图）
    Simple(f64),
    
    /// 内外半径（环形图）
    Range(f64, f64),
}

impl Default for PieRadius {
    fn default() -> Self {
        PieRadius::Simple(0.75) // 默认75%半径
    }
}

/// 玫瑰图类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RoseType {
    /// 扇区圆心角展现数据的百分比，半径展现数据的大小
    Radius,
    
    /// 扇区面积展现数据的百分比
    Area,
}

/// 饼图标签位置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PieLabelPosition {
    /// 扇区内部
    Inside,
    
    /// 扇区外部
    Outside,
    
    /// 中心
    Center,
}

impl Default for PieLabelPosition {
    fn default() -> Self {
        PieLabelPosition::Outside
    }
}

/// 选中模式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SelectedMode {
    /// 不可选中
    None,
    
    /// 单选
    Single,
    
    /// 多选
    Multiple,
}

impl Default for SelectedMode {
    fn default() -> Self {
        SelectedMode::Single
    }
}

/// 引导线配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LabelLine {
    /// 是否显示引导线
    pub show: bool,
    
    /// 引导线长度
    pub length: f64,
    
    /// 引导线第二段长度
    pub length2: f64,
    
    /// 引导线样式
    pub line_style: LineStyle,
}

impl Default for LabelLine {
    fn default() -> Self {
        Self {
            show: true,
            length: 15.0,
            length2: 15.0,
            line_style: LineStyle {
                color: Color::rgb(0.5, 0.5, 0.5),
                width: 1.0,
                opacity: 0.8,
                dash_pattern: None,
                cap: LineCap::Butt,
                join: LineJoin::Round,
            },
        }
    }
}

/// 饼图标签配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PieLabel {
    /// 是否显示标签
    pub show: bool,
    
    /// 标签位置
    pub position: PieLabelPosition,
    
    /// 标签格式化器
    pub formatter: Option<String>,
    
    /// 标签样式
    pub style: TextStyle,
    
    /// 引导线配置
    pub line: Option<LabelLine>,
    
    /// 标签距离
    pub distance: f64,
}

impl Default for PieLabel {
    fn default() -> Self {
        Self {
            show: true,
            position: PieLabelPosition::Outside,
            formatter: None,
            style: TextStyle {
                font_family: "Arial".to_string(),
                font_size: 12.0,
                font_weight: FontWeight::Normal,
                font_style: FontStyle::Normal,
                color: Color::rgb(0.2, 0.2, 0.2),
                opacity: 1.0,
                align: TextAlign::Center,
                baseline: TextBaseline::Middle,
                rotation: 0.0,
                letter_spacing: 0.0,
                line_height: 1.2,
            },
            line: Some(LabelLine::default()),
            distance: 5.0,
        }
    }
}

/// 饼图高亮样式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PieEmphasis {
    /// 是否启用高亮
    pub disabled: bool,
    
    /// 高亮时的缩放比例
    pub scale: f64,
    
    /// 高亮时的扇区样式
    pub item_style: Option<PieItemStyle>,
    
    /// 高亮时的标签样式
    pub label: Option<PieLabel>,
}

impl Default for PieEmphasis {
    fn default() -> Self {
        Self {
            disabled: false,
            scale: 1.1,
            item_style: None,
            label: None,
        }
    }
}

/// 饼图扇区样式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PieItemStyle {
    /// 扇区颜色
    pub color: Option<Color>,
    
    /// 边框颜色
    pub border_color: Color,
    
    /// 边框宽度
    pub border_width: f64,
    
    /// 透明度
    pub opacity: f64,
    
    /// 阴影模糊大小
    pub shadow_blur: f64,
    
    /// 阴影颜色
    pub shadow_color: Color,
    
    /// 阴影偏移
    pub shadow_offset_x: f64,
    pub shadow_offset_y: f64,
}

impl Default for PieItemStyle {
    fn default() -> Self {
        Self {
            color: None,
            border_color: Color::rgb(1.0, 1.0, 1.0),
            border_width: 1.0,
            opacity: 1.0,
            shadow_blur: 0.0,
            shadow_color: Color::rgba(0.0, 0.0, 0.0, 0.5),
            shadow_offset_x: 0.0,
            shadow_offset_y: 0.0,
        }
    }
}

/// 饼图数据项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PieDataItem {
    /// 数据项名称
    pub name: String,
    
    /// 数据值
    pub value: f64,
    
    /// 是否选中
    pub selected: bool,
    
    /// 自定义样式
    pub item_style: Option<PieItemStyle>,
    
    /// 自定义标签
    pub label: Option<PieLabel>,
    
    /// 自定义高亮样式
    pub emphasis: Option<PieEmphasis>,
}

impl PieDataItem {
    /// 创建新的数据项
    pub fn new<S: Into<String>>(name: S, value: f64) -> Self {
        Self {
            name: name.into(),
            value,
            selected: false,
            item_style: None,
            label: None,
            emphasis: None,
        }
    }
    
    /// 设置选中状态
    pub fn selected(mut self, selected: bool) -> Self {
        self.selected = selected;
        self
    }
    
    /// 设置自定义颜色
    pub fn color(mut self, color: Color) -> Self {
        if self.item_style.is_none() {
            self.item_style = Some(PieItemStyle::default());
        }
        self.item_style.as_mut().unwrap().color = Some(color);
        self
    }
}

/// 饼图系列
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PieSeries {
    /// 基础配置

    pub config: ChartConfig,

    

    /// 图表数据

    pub data: DataSet,
    
    /// 饼图中心位置 [x, y]，相对于容器的百分比
    pub center: [f64; 2],
    
    /// 饼图半径
    pub radius: PieRadius,
    
    /// 起始角度（度数）
    pub start_angle: f64,
    
    /// 最小角度（小于该角度的扇区会被合并）
    pub min_angle: f64,
    
    /// 扇区间隔角度（度数）
    pub padding_angle: f64,
    
    /// 是否顺时针排列
    pub clockwise: bool,
    
    /// 是否启用玫瑰图模式
    pub rose_type: Option<RoseType>,
    
    /// 选中模式
    pub selected_mode: SelectedMode,
    
    /// 标签配置
    pub label: PieLabel,
    
    /// 扇区样式
    pub item_style: PieItemStyle,
    
    /// 高亮样式
    pub emphasis: PieEmphasis,

    // visible, z_index 现在在 config 中

    /// 调色板
    pub color_palette: Vec<Color>,
}

impl PieSeries {
    /// 创建新的饼图系列
    pub fn new<S: Into<String>>(name: S) -> Self {
        Self {
            config: ChartConfig {
                name: name.into(),
                ..ChartConfig::default()
            },
            data: DataSet::new(),
            center: [0.5, 0.5], // 默认居中
            radius: PieRadius::default(),
            start_angle: 90.0, // 从12点钟方向开始
            min_angle: 0.0,
            padding_angle: 0.0,
            clockwise: true,
            rose_type: None,
            selected_mode: SelectedMode::default(),
            label: PieLabel::default(),
            item_style: PieItemStyle::default(),
            emphasis: PieEmphasis::default(),
            color_palette: vec![
                Color::rgb(0.2, 0.6, 1.0),   // 蓝色
                Color::rgb(1.0, 0.4, 0.2),   // 橙色
                Color::rgb(0.2, 0.8, 0.4),   // 绿色
                Color::rgb(1.0, 0.6, 0.2),   // 黄色
                Color::rgb(0.8, 0.2, 0.8),   // 紫色
                Color::rgb(0.2, 0.8, 0.8),   // 青色
                Color::rgb(1.0, 0.2, 0.4),   // 红色
                Color::rgb(0.6, 0.4, 0.8),   // 紫蓝色
            ],
        }
    }

    /// 设置数据（从名称-值对）
    pub fn data<I, S>(mut self, data: I) -> Self
    where
        I: IntoIterator<Item = (S, f64)>,
        S: Into<String>,
    {
        let mut dataset = DataSet::new();

        for (name, value) in data {
            let point = DataPoint::with_name(vec![DataValue::Number(value)], name.into());
            dataset = dataset.add_point(point);
        }

        self.data = dataset;
        self
    }

    /// 设置饼图中心位置
    pub fn center(mut self, x: f64, y: f64) -> Self {
        self.center = [x, y];
        self
    }

    /// 设置饼图半径
    pub fn radius(mut self, radius: f64) -> Self {
        self.radius = PieRadius::Simple(radius);
        self
    }

    /// 设置饼图内外半径（环形图）
    pub fn radius_range(mut self, inner: f64, outer: f64) -> Self {
        self.radius = PieRadius::Range(inner, outer);
        self
    }

    /// 设置为环形图
    pub fn as_donut(self, inner_radius: f64, outer_radius: f64) -> Self {
        self.radius_range(inner_radius, outer_radius)
    }

    /// 设置为玫瑰图
    pub fn as_rose(mut self, rose_type: RoseType) -> Self {
        self.rose_type = Some(rose_type);
        self
    }

    /// 设置起始角度（度数）
    pub fn start_angle(mut self, angle: f64) -> Self {
        self.start_angle = angle;
        self
    }

    /// 设置是否顺时针排列
    pub fn clockwise(mut self, clockwise: bool) -> Self {
        self.clockwise = clockwise;
        self
    }

    /// 设置标签配置
    pub fn label(mut self, position: PieLabelPosition, show_line: bool) -> Self {
        self.label.position = position;
        self.label.line = if show_line {
            Some(LabelLine::default())
        } else {
            None
        };
        self
    }

    /// 设置标签显示
    pub fn show_label(mut self, show: bool) -> Self {
        self.label.show = show;
        self
    }

    /// 设置选中模式
    pub fn selected_mode(mut self, mode: SelectedMode) -> Self {
        self.selected_mode = mode;
        self
    }

    /// 设置边框样式
    pub fn border(mut self, show: bool, color: Color, width: f64) -> Self {
        self.item_style.border_width = if show { width } else { 0.0 };
        self.item_style.border_color = color;
        self
    }

    /// 设置透明度
    pub fn opacity(mut self, opacity: f64) -> Self {
        self.item_style.opacity = opacity;
        self
    }

    /// 设置可见性
    pub fn visible(mut self, visible: bool) -> Self {
        self.config.visible = visible;
        self
    }

    /// 设置Z轴索引
    pub fn z_index(mut self, z_index: i32) -> Self {
        self.config.z_index = z_index;
        self
    }

    /// 设置调色板
    pub fn color_palette(mut self, colors: Vec<Color>) -> Self {
        self.color_palette = colors;
        self
    }

    /// 获取指定索引的颜色
    fn get_color_for_index(&self, index: usize) -> Color {
        if self.color_palette.is_empty() {
            return Color::rgb(0.5, 0.5, 0.5); // 默认灰色
        }

        self.color_palette[index % self.color_palette.len()]
    }

    /// 计算中心点坐标
    fn calculate_center_point(&self, bounds: Bounds) -> Point {
        Point::new(
            bounds.origin.x + bounds.size.width * self.center[0],
            bounds.origin.y + bounds.size.height * self.center[1],
        )
    }

    /// 计算半径
    fn calculate_radius(&self, bounds: Bounds) -> (f64, f64) {
        let min_dimension = bounds.size.width.min(bounds.size.height);

        match self.radius {
            PieRadius::Simple(radius) => {
                let r = min_dimension * 0.5 * radius;
                (0.0, r) // 内半径为0
            },
            PieRadius::Range(inner, outer) => {
                let r_inner = min_dimension * 0.5 * inner;
                let r_outer = min_dimension * 0.5 * outer;
                (r_inner, r_outer)
            }
        }
    }

    /// 创建扇区路径
    fn create_pie_slice_path(
        &self,
        center: Point,
        radius: (f64, f64),
        start_angle: f64,
        end_angle: f64,
        is_rose: bool,
        value_ratio: f64,
    ) -> Vec<PathCommand> {
        let mut commands = Vec::new();

        // 转换角度为弧度
        let start_rad = (90.0 - start_angle) * PI / 180.0;
        let end_rad = (90.0 - end_angle) * PI / 180.0;

        let (inner_radius, outer_radius) = radius;

        // 对于玫瑰图，调整外半径
        let actual_outer_radius = if is_rose {
            match self.rose_type {
                Some(RoseType::Radius) => {
                    // 半径模式：半径与数据成正比
                    inner_radius + (outer_radius - inner_radius) * value_ratio
                },
                Some(RoseType::Area) => {
                    // 面积模式：面积与数据成正比
                    (inner_radius * inner_radius +
                     (outer_radius * outer_radius - inner_radius * inner_radius) * value_ratio).sqrt()
                },
                None => outer_radius,
            }
        } else {
            outer_radius
        };

        // 计算外弧起点
        let outer_start_x = center.x + actual_outer_radius * start_rad.cos();
        let outer_start_y = center.y + actual_outer_radius * start_rad.sin();

        // 移动到外弧起点
        commands.push(PathCommand::MoveTo(Point::new(outer_start_x, outer_start_y)));

        // 绘制外弧（使用多个线段近似）
        let angle_diff = end_rad - start_rad;
        let segments = ((angle_diff.abs() * 180.0 / PI) / 10.0).ceil() as i32; // 每10度一个线段

        for i in 1..=segments {
            let t = i as f64 / segments as f64;
            let angle = start_rad + angle_diff * t;
            let x = center.x + actual_outer_radius * angle.cos();
            let y = center.y + actual_outer_radius * angle.sin();
            commands.push(PathCommand::LineTo(Point::new(x, y)));
        }

        // 如果是环形图，绘制内弧
        if inner_radius > 0.0 {
            // 计算内弧终点
            let inner_end_x = center.x + inner_radius * end_rad.cos();
            let inner_end_y = center.y + inner_radius * end_rad.sin();

            // 连接到内弧终点
            commands.push(PathCommand::LineTo(Point::new(inner_end_x, inner_end_y)));

            // 绘制内弧（使用多个线段近似）
            let inner_angle_diff = start_rad - end_rad; // 反向绘制
            let inner_segments = ((inner_angle_diff.abs() * 180.0 / PI) / 10.0).ceil() as i32;

            for i in 1..=inner_segments {
                let t = i as f64 / inner_segments as f64;
                let angle = end_rad + inner_angle_diff * t;
                let x = center.x + inner_radius * angle.cos();
                let y = center.y + inner_radius * angle.sin();
                commands.push(PathCommand::LineTo(Point::new(x, y)));
            }
        } else {
            // 普通饼图，连接到中心点
            commands.push(PathCommand::LineTo(center));
        }

        // 闭合路径
        commands.push(PathCommand::Close);

        commands
    }

    /// 添加标签命令
    fn add_label_commands(
        &self,
        commands: &mut Vec<DrawCommand>,
        name: &str,
        value: f64,
        center: Point,
        radius: (f64, f64),
        angle: f64,
    ) {
        if !self.label.show {
            return;
        }

        // 转换角度为弧度
        let rad = (90.0 - angle) * PI / 180.0;

        // 计算标签位置
        let (inner_radius, outer_radius) = radius;
        let label_position = match self.label.position {
            PieLabelPosition::Inside => {
                // 内部标签位置：在内外半径中间
                let r = inner_radius + (outer_radius - inner_radius) * 0.5;
                Point::new(
                    center.x + r * rad.cos(),
                    center.y + r * rad.sin(),
                )
            },
            PieLabelPosition::Outside => {
                // 外部标签位置：在外半径之外
                let r = outer_radius + self.label.distance;
                Point::new(
                    center.x + r * rad.cos(),
                    center.y + r * rad.sin(),
                )
            },
            PieLabelPosition::Center => {
                // 中心标签位置：在扇区中心
                center
            },
        };

        // 格式化标签文本
        let label_text = match &self.label.formatter {
            Some(formatter) => {
                // 简单的格式化支持
                formatter.replace("{name}", name)
                         .replace("{value}", &format!("{:.1}", value))
                         .replace("{percent}", &format!("{:.1}%", value * 100.0))
            },
            None => {
                // 默认格式：名称 + 值
                format!("{}: {:.1}", name, value)
            },
        };

        // 添加标签文本
        commands.push(DrawCommand::Text {
            text: label_text,
            position: label_position,
            style: self.label.style.clone(),
        });

        // 如果是外部标签，添加引导线
        if let PieLabelPosition::Outside = self.label.position {
            if let Some(line) = &self.label.line {
                if line.show {
                    // 计算引导线起点（在外半径上）
                    let start = Point::new(
                        center.x + outer_radius * rad.cos(),
                        center.y + outer_radius * rad.sin(),
                    );

                    // 计算引导线拐点
                    let middle = Point::new(
                        center.x + (outer_radius + line.length) * rad.cos(),
                        center.y + (outer_radius + line.length) * rad.sin(),
                    );

                    // 计算引导线终点
                    let end = Point::new(
                        middle.x + (if rad.cos() > 0.0 { line.length2 } else { -line.length2 }),
                        middle.y,
                    );

                    // 添加引导线
                    commands.push(DrawCommand::Line {
                        from: start,
                        to: middle,
                        style: line.line_style.clone(),
                    });

                    commands.push(DrawCommand::Line {
                        from: middle,
                        to: end,
                        style: line.line_style.clone(),
                    });
                }
            }
        }
    }
}

/// 实现 Series trait
impl Series for PieSeries {
    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Pie
    }

    fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        if !self.config.visible || self.data.is_empty() {
            return Ok(commands);
        }

        // 获取数据总和
        let total: f64 = self.data.points()
            .iter()
            .filter_map(|point| point.get_number(0))
            .sum();

        if total <= 0.0 {
            return Ok(commands);
        }

        // 计算中心点和半径
        let center = self.calculate_center_point(coord_system.bounds());
        let radius = self.calculate_radius(coord_system.bounds());

        // 计算起始角度（转换为度数）
        let mut start_angle = self.start_angle;

        // 绘制每个扇区
        for (i, point) in self.data.points().iter().enumerate() {
            if let (Some(name), Some(value)) = (point.name.as_ref(), point.get_number(0)) {
                if value <= 0.0 {
                    continue;
                }

                // 计算扇区角度
                let angle = value / total * 360.0;
                if angle < self.min_angle {
                    continue;
                }

                // 计算结束角度
                let end_angle = if self.clockwise {
                    start_angle + angle
                } else {
                    start_angle - angle
                };

                // 获取颜色
                let color = self.get_color_for_index(i);

                // 创建扇区路径
                let path_commands = self.create_pie_slice_path(
                    center,
                    radius,
                    start_angle,
                    end_angle,
                    self.rose_type.is_some(),
                    value / total,
                );

                // 添加扇区绘制命令
                commands.push(DrawCommand::Path {
                    commands: path_commands,
                    style: PathStyle {
                        fill: Some(color),
                        stroke: if self.item_style.border_width > 0.0 {
                            Some(LineStyle {
                                color: self.item_style.border_color,
                                width: self.item_style.border_width,
                                opacity: self.item_style.opacity,
                                dash_pattern: None,
                                cap: LineCap::Butt,
                                join: LineJoin::Round,
                            })
                        } else {
                            None
                        },
                        opacity: self.item_style.opacity,
                        fill_rule: FillRule::NonZero,
                    },
                });

                // 添加标签
                self.add_label_commands(
                    &mut commands,
                    name,
                    value,
                    center,
                    radius,
                    (start_angle + end_angle) / 2.0,
                );

                // 更新起始角度
                start_angle = end_angle + self.padding_angle;
            }
        }

        Ok(commands)
    }

    fn bounds(&self) -> Option<Bounds> {
        // 饼图的边界是基于其配置的中心点和半径
        None // 饼图通常使用整个可用空间
    }

    fn is_visible(&self) -> bool {
        self.config.visible
    }

    fn z_index(&self) -> i32 {
        self.config.z_index
    }

    fn clone_series(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use echarts_core::CartesianCoordinateSystem;

    #[test]
    fn test_pie_series_creation() {
        let series = PieSeries::new("测试饼图")
            .data(vec![
                ("类别A", 30.0),
                ("类别B", 50.0),
                ("类别C", 20.0),
            ]);

        assert_eq!(series.name, "测试饼图");
        assert_eq!(series.series_type(), SeriesType::Pie);
        assert_eq!(series.data.len(), 3);
        assert!(series.is_visible());
    }

    #[test]
    fn test_pie_series_config() {
        let series = PieSeries::new("配置测试")
            .center(0.6, 0.4)
            .radius(0.8)
            .start_angle(45.0)
            .clockwise(false)
            .show_label(true)
            .label(PieLabelPosition::Inside, false)
            .opacity(0.8);

        assert_eq!(series.center, [0.6, 0.4]);
        assert!(matches!(series.radius, PieRadius::Simple(0.8)));
        assert_eq!(series.start_angle, 45.0);
        assert!(!series.clockwise);
        assert!(series.label.show);
        assert!(matches!(series.label.position, PieLabelPosition::Inside));
        assert!(series.label.line.is_none());
        assert_eq!(series.item_style.opacity, 0.8);
    }

    #[test]
    fn test_donut_chart() {
        let series = PieSeries::new("环形图")
            .as_donut(0.4, 0.8);

        assert!(matches!(series.radius, PieRadius::Range(0.4, 0.8)));
    }

    #[test]
    fn test_rose_chart() {
        let series = PieSeries::new("玫瑰图")
            .as_rose(RoseType::Radius);

        assert!(matches!(series.rose_type, Some(RoseType::Radius)));
    }

    #[test]
    fn test_render_to_commands() {
        let series = PieSeries::new("渲染测试")
            .data(vec![
                ("A", 30.0),
                ("B", 70.0),
            ]);

        let coord_system = CartesianCoordinateSystem::new(
            Bounds::new(0.0, 0.0, 400.0, 300.0),
            (0.0, 1.0),
            (0.0, 1.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();

        // 检查命令类型
        let mut path_count = 0;
        let mut text_count = 0;
        let mut line_count = 0;

        for cmd in &commands {
            match cmd {
                DrawCommand::Path { .. } => path_count += 1,
                DrawCommand::Text { .. } => text_count += 1,
                DrawCommand::Line { .. } => line_count += 1,
                _ => {}
            }
        }

        // 应该生成：2个扇区路径 + 2个标签文本 + 4个引导线（每个标签2条线）
        assert_eq!(path_count, 2); // 2个扇区
        assert_eq!(text_count, 2); // 2个标签
        assert_eq!(line_count, 4); // 4个引导线（每个标签2条）
        assert_eq!(commands.len(), 8); // 总共8个命令
    }

    #[test]
    fn test_empty_data() {
        let series = PieSeries::new("空数据测试");

        let coord_system = CartesianCoordinateSystem::new(
            Bounds::new(0.0, 0.0, 400.0, 300.0),
            (0.0, 1.0),
            (0.0, 1.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();

        // 空数据应该不生成任何命令
        assert!(commands.is_empty());
    }

    #[test]
    fn test_series_trait_object() {
        // 测试类型擦除
        let series: Box<dyn Series> = Box::new(PieSeries::new("类型擦除测试")
            .data(vec![("A", 100.0)]));

        assert_eq!(series.name(), "类型擦除测试");
        assert_eq!(series.series_type(), SeriesType::Pie);
        assert!(series.is_visible());
    }
}
// ============================================================================
// 图表基础架构集成 - ChartBase 实现
// ============================================================================

/// 为 PieSeries 实现 ChartBase trait
impl ChartBase for PieSeries {
    type DataType = DataSet;

    fn name(&self) -> &str {
        &self.config.name
    }

    fn raw_data(&self) -> &Self::DataType {
        &self.data
    }

    fn to_dataset(&self) -> DataSet {
        self.data.clone()
    }

    fn visible(&self) -> bool {
        self.config.visible
    }

    fn z_index(&self) -> i32 {
        self.config.z_index
    }

    fn bounds(&self) -> Option<Bounds> {
        BoundsCalculator::from_dataset(&self.data)
    }

    fn config(&self) -> &ChartConfig {
        &self.config
    }

    fn config_mut(&mut self) -> &mut ChartConfig {
        &mut self.config
    }
}

/// 为 PieSeries 实现 ChartSeries trait
impl ChartSeries for PieSeries {
    // 所有方法都由默认实现提供，基于 ChartBase
}
