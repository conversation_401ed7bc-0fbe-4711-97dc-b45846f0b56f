//! 图表模块测试套件

#[cfg(test)]
mod tests {
    use echarts_core::{Chart, DataPoint, DataValue, Color};

    #[test]
    fn test_chart_data_structures() {
        let mut chart = Chart::new();
        chart.title = Some("图表数据结构测试".to_string());

        // 创建测试数据
        let data = vec![
            DataPoint::new(vec![DataValue::String("A".to_string()), DataValue::Number(10.0)]),
            DataPoint::new(vec![DataValue::String("B".to_string()), DataValue::Number(20.0)]),
            DataPoint::new(vec![DataValue::String("C".to_string()), DataValue::Number(15.0)]),
        ];

        assert_eq!(data.len(), 3);
        assert_eq!(data[0].y(), 10.0);
        assert_eq!(data[1].y(), 20.0);
        assert_eq!(data[2].y(), 15.0);

        println!("✅ 图表数据结构测试通过");
    }

    #[test]
    fn test_chart_data_processing() {
        // 创建数值数据
        let data = vec![
            DataPoint::new(vec![DataValue::Number(1.0), DataValue::Number(10.0)]),
            DataPoint::new(vec![DataValue::Number(2.0), DataValue::Number(25.0)]),
            DataPoint::new(vec![DataValue::Number(3.0), DataValue::Number(15.0)]),
            DataPoint::new(vec![DataValue::Number(4.0), DataValue::Number(30.0)]),
        ];

        // 验证数据连续性
        for i in 1..data.len() {
            let prev_x = data[i-1].x();
            let curr_x = data[i].x();
            assert!(curr_x > prev_x, "X轴数据应该是递增的");
        }

        println!("✅ 图表数据处理测试通过");
    }

    #[test]
    fn test_chart_data_validation() {
        // 创建分类数据
        let data = vec![
            DataPoint::new(vec![DataValue::String("苹果".to_string()), DataValue::Number(30.0)]),
            DataPoint::new(vec![DataValue::String("香蕉".to_string()), DataValue::Number(25.0)]),
            DataPoint::new(vec![DataValue::String("橙子".to_string()), DataValue::Number(20.0)]),
            DataPoint::new(vec![DataValue::String("其他".to_string()), DataValue::Number(25.0)]),
        ];

        // 验证数据总和
        let total: f64 = data.iter().map(|p| p.y()).sum();
        assert_eq!(total, 100.0);

        println!("✅ 图表数据验证测试通过");
    }

    #[test]
    fn test_chart_color_schemes() {
        // 测试不同的颜色方案
        let colors = vec![
            Color::rgb(1.0, 0.0, 0.0),  // 红色
            Color::rgb(0.0, 1.0, 0.0),  // 绿色
            Color::rgb(0.0, 0.0, 1.0),  // 蓝色
        ];

        assert_eq!(colors.len(), 3);

        // 验证颜色基本属性
        assert_eq!(colors[0].r, 1.0);
        assert_eq!(colors[1].g, 1.0);
        assert_eq!(colors[2].b, 1.0);

        println!("✅ 图表颜色方案测试通过");
    }

    #[test]
    fn test_chart_bounds_calculation() {
        // 测试图表边界计算
        let data = vec![
            DataPoint::new(vec![DataValue::Number(-10.0), DataValue::Number(5.0)]),
            DataPoint::new(vec![DataValue::Number(20.0), DataValue::Number(-15.0)]),
            DataPoint::new(vec![DataValue::Number(0.0), DataValue::Number(25.0)]),
        ];

        let x_values: Vec<f64> = data.iter().map(|p| p.x()).collect();
        let y_values: Vec<f64> = data.iter().map(|p| p.y()).collect();

        let x_min = x_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let x_max = x_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let y_min = y_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let y_max = y_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));

        assert_eq!(x_min, -10.0);
        assert_eq!(x_max, 20.0);
        assert_eq!(y_min, -15.0);
        assert_eq!(y_max, 25.0);

        println!("✅ 图表边界计算测试通过");
    }

    #[test]
    fn test_chart_performance() {
        use std::time::Instant;

        // 测试数据处理性能
        let start = Instant::now();
        let data: Vec<DataPoint> = (0..1000)
            .map(|i| {
                let x = i as f64;
                let y = (x * 0.01).sin() * 100.0;
                DataPoint::new(vec![DataValue::Number(x), DataValue::Number(y)])
            })
            .collect();

        let creation_time = start.elapsed();

        // 测试数据处理
        let start = Instant::now();
        let _sum: f64 = data.iter().map(|p| p.y()).sum();
        let processing_time = start.elapsed();

        assert_eq!(data.len(), 1000);

        println!("✅ 图表性能测试通过");
        println!("   - 数据创建时间: {:?}", creation_time);
        println!("   - 数据处理时间: {:?}", processing_time);
    }
}
