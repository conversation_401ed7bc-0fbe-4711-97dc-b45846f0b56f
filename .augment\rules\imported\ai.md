---
type: "always_apply"
description: "Example description"
---
# 1. Rust 核心开发规范 [P0]

## 1.1 最高规制 [强制执行]
- PowerShell不支持&&语法
- 中文对话
- 生成中文文档
- 将整个工程关键代码，结构，目录，记录上下文中
- 严格按照cargo.lock 版本依赖 生成代码
- 优先使用工程中已使用的方式处理问题、函数、方法
- 修改完代码后必须检测代码是否报错
- 如果需要参考某文件，要严格仔细查看与理解其中的细节
- 如果遇到问题，不允许使用更简单的方式，按照原来的思路，进行修复修复问题
- 你认为比较重要的对话，总结，按照1~10等级 生成文档放到doc/ai/文件夹下，
- 生成代码必须严格按照相关依赖版本执行
- 禁止偏移主题，
- 禁止擅自更改任务
- 禁止未阅读整个文档，分析代码，请勿修改添加任何代码


## 1.2 代码质量与检查-严格执行

- 作为 Rust 系统工程师，遵循 Rust RFC 和 Cargo 最佳实践
- 所有代码必须通过 `cargo clippy --all-targets -- -D warnings` 检查
- 使用 `tracing` 替代 `println!` 进行日志记录
- 使用 `rustfmt` 和 `clippy` 格式化和检查代码

## 1.3 代码风格与命名规范

- 文件名和模块名使用蛇形命名法 (snake_case)
- 结构体和枚举使用大驼峰命名法 (PascalCase)
- 常量使用全大写下划线分隔 (SCREAMING_SNAKE_CASE)
- 变量、方法、函数使用蛇形命名法 (snake_case)
- 每个公开函数和类型必须有文档注释
- 注释使用中文，代码中的标识符使用英文

## 1.4 项目架构

- 遵循模块化设计，相关功能放在同一模块
- 导出公共API使用 `pub use` 重导出模式
- 配置项集中管理，使用结构化配置
- 禁止循环依赖
- 分离业务逻辑和UI组件
- 为可能变动的功能提供抽象接口

## 1.5 安全规范

- 禁止使用 `unwrap()`，必须使用 `expect()` 带上下文
- FFI 接口必须包含 `# Safety` 文档块
- 所有公开类型必须实现 `Debug + Send + Sync`
- 涉及 unsafe 代码时必须输出《安全审计报告》包含：
  - 内存布局示意图
  - 竞争条件分析
  - MIRI 测试方案

## 1.6 性能约束

- 堆分配需标注 `// PERF: ` 注释说明必要性
- 热路径函数必须添加 `#[inline]` 提示
- 禁止在循环中使用动态分发

## 1.7 运行时与异步

- 禁止使用 tokio 线程相关方法，统一使用 gpui 线程
- 异步任务必须设置合理超时
- 禁止使用 `tokio::spawn`、`tokio::time::timeout` 等
- 长时间运行的任务必须可取消
- 异步任务需标注 `#[must_use]` 并生成执行流程图
- 所有公开 API 必须包含文档测试示例

# 2. 项目级约束

## 2.1 技术栈基准

```
[workspace.dependencies]
tokio = { version = "1.37", features = ["rt-multi-thread", "macros"] }
serde = { version = "1.0", features = ["derive"] }
tracing = "0.1"
```

# 3. 协作工作流程

## 3.1 输出阶段

1. 方案设计 (3种架构对比)
2. 安全审计报告
3. 核心实现代码
4. 集成测试模板
5. 性能优化建议

## 3.2 复杂任务流程

```mermaid
graph TD
  A[复杂任务] --> B{上下文分析->知识检索->强制规划阶段}
  B --> C[生成3+方案]
  C --> D[用户整合优化]
  D --> E[激活扩展思考模式]
  E --> F[分步执行]
  F --> G[工具交互迭代]
```

## 3.3 新功能开发流程

```mermaid
graph TD
  A[新功能] --> B[所有权分析]
  B --> C{涉及并发?}
  C -->|Yes| D[Send/Sync 审计]
  C -->|No| E[生命周期验证]
  D --> F[Arc/Mutex 优化]
  E --> G[Pin 和 Unpin 检查]
  F & G --> H[生成 MIRI 测试]
  H --> I[性能剖析]
  I --> J[文档测试生成]
```

# 4. AI 协作方法论

## 4.1 核心方法论

### 内在校准机制
- 用户需建立对模型能力的认知边界：明确哪些任务可一次性完成，哪些需要分步引导
- 模型迭代需重新校准：每次升级后能力提升，可逐步移交更多任务

### 规划优先原则
- 关键技巧：复杂任务前强制让模型先输出方案（而非直接执行）
- 操作流程：
  - 要求列出多种解决思路
  - 用户选择/组合方案
  - 再启动执行

## 4.2 高效协作模式

### 交替式工作流
- 提供上下文 → 模型思考 → 调用工具 → 再思考 → 输出
- （类比人类：需先阅读资料再头脑风暴，而非空想）



# 5. AI 响应规范 [强制执行]

## 5.1 响应结构

- 每次响应必须先进行知识检索和分析
- 复杂问题必须先输出规划再执行
- 响应格式必须包含以下部分：
  1. 【分析】问题/任务的理解和关键点
  2. 【方案】解决思路（复杂任务提供多个方案）
  3. 【执行】代码实现或具体步骤
  4. 【检查】可能的问题和改进点

## 5.2 响应质量要求

- 所有代码必须遵循 1.1-1.7 节的所有规范
- 禁止生成未经检查的不安全代码
- 必须确保导入所有必要的依赖
- 必须验证生成的代码能在当前环境中正常工作
- 复杂逻辑必须提供注释说明

## 5.3 响应流程

```mermaid
graph TD
  A[接收用户查询] --> B[知识检索与上下文分析]
  B --> C{是否复杂任务?}
  C -->|是| D[输出多方案规划]
  C -->|否| E[直接执行]
  D --> F[等待用户选择]
  F --> G[执行选定方案]
  E --> G
  G --> H[检查与改进]
  H --> I[最终输出]
```

## 5.4 质量保证机制

- 每次响应必须执行自检，确保符合上述规范
- 发现不符合项时必须主动纠正
- 对于无法满足的需求，必须明确说明原因和替代方案
- 优先引用项目内现有代码风格和解决方案

---

**底层逻辑**：模拟人类解决问题路径（分析→规划→执行→修正），释放AI最大潜能。

# 6. 工具使用与故障排除

## 6.1 终端交互问题

- AI执行终端，可能一直等待终端响应
- 遇到终端命令执行卡住时， 自动重启终端，并重试
- 对于长时间运行或需要用户交互的命令，应添加适当的超时机制 
- 运行复杂命令前先进行小规模测试，确认终端响应正常
- 当检测到终端无响应时，可使用分步执行方式或替代命令实现目标


# 4. AI 协作方法论

## 4.1 核心方法论

### 内在校准机制
- 用户需建立对模型能力的认知边界：明确哪些任务可一次性完成，哪些需要分步引导
- 模型迭代需重新校准：每次升级后能力提升，可逐步移交更多任务

### 规划优先原则
- 关键技巧：复杂任务前强制让模型先输出方案（而非直接执行）
- 操作流程：
  - 要求列出多种解决思路
  - 用户选择/组合方案
  - 再启动执行

## 4.2 高效协作模式

### 交替式工作流
- 提供上下文 → 模型思考 → 调用工具 → 再思考 → 输出
- （类比人类：需先阅读资料再头脑风暴，而非空想）





