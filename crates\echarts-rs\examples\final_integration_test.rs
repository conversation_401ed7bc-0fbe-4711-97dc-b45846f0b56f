//! ECharts-rs 最终集成测试
//!
//! 验证所有修复后的功能是否正常工作

use echarts_rs::{LineSeries, BarSeries, PieSeries, ScatterSeries, Color, Series};
use echarts_core::{CartesianCoordinateSystem, Bounds};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🚀 ECharts-rs 最终集成测试");
    println!("{}", "=".repeat(60));

    // 1. 测试线图
    println!("\n📈 1. 测试线图功能...");
    test_line_series()?;

    // 2. 测试柱图
    println!("\n📊 2. 测试柱图功能...");
    test_bar_series()?;

    // 3. 测试饼图
    println!("\n🥧 3. 测试饼图功能...");
    test_pie_series()?;

    // 4. 测试散点图
    println!("\n🔵 4. 测试散点图功能...");
    test_scatter_series()?;

    // 5. 测试坐标系统
    println!("\n📐 5. 测试坐标系统...");
    test_coordinate_system()?;

    // 6. 综合测试
    println!("\n🎯 6. 综合功能测试...");
    comprehensive_test()?;

    println!("\n🎉 所有测试通过！");
    println!("✅ ECharts-rs 编译错误已全部修复");
    println!("✅ 核心功能正常工作");
    println!("✅ 可以在GPUI应用中使用");
    println!("✅ 图表渲染命令生成正常");

    Ok(())
}

fn test_line_series() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let line_series = LineSeries::new("测试线图")
        .data(vec![
            (1.0, 120.0), (2.0, 132.0), (3.0, 101.0), 
            (4.0, 134.0), (5.0, 90.0), (6.0, 230.0)
        ])
        .smooth(true)
        .color(Color::rgb(0.2, 0.6, 1.0));

    println!("  ✅ 线图创建成功");
    println!("  📊 数据点数量: {}", line_series.data.len());
    println!("  🎨 颜色: RGB({:.1}, {:.1}, {:.1})", 
             line_series.color.r, line_series.color.g, line_series.color.b);
    println!("  📈 平滑曲线: {}", line_series.smooth);

    // 测试渲染
    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 600.0, 400.0),
        (0.0, 7.0),
        (0.0, 250.0),
    );

    match line_series.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 渲染命令生成成功: {} 个命令", commands.len());
        }
        Err(e) => {
            println!("  ❌ 渲染失败: {:?}", e);
            return Err(e.into());
        }
    }

    Ok(())
}

fn test_bar_series() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let bar_series = BarSeries::new("测试柱图")
        .data(vec![
            (1.0, 320.0), (2.0, 280.0), (3.0, 250.0), 
            (4.0, 200.0), (5.0, 180.0)
        ])
        .color(Color::rgb(0.9, 0.4, 0.2))
        .bar_width(0.6);

    println!("  ✅ 柱图创建成功");
    println!("  📊 数据点数量: {}", bar_series.data.len());
    println!("  🎨 颜色: RGB({:.1}, {:.1}, {:.1})", 
             bar_series.color.r, bar_series.color.g, bar_series.color.b);
    println!("  📏 柱宽度: {}", bar_series.bar_width);

    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 600.0, 400.0),
        (0.0, 6.0),
        (0.0, 350.0),
    );

    match bar_series.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 渲染命令生成成功: {} 个命令", commands.len());
        }
        Err(e) => {
            println!("  ❌ 渲染失败: {:?}", e);
            return Err(e.into());
        }
    }

    Ok(())
}

fn test_pie_series() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let pie_series = PieSeries::new("测试饼图")
        .data(vec![
            ("移动端", 45.0), ("桌面端", 35.0), 
            ("平板端", 15.0), ("其他", 5.0)
        ])
        .radius(0.7)
        .center(0.5, 0.5);

    println!("  ✅ 饼图创建成功");
    println!("  📊 数据点数量: {}", pie_series.data.len());
    println!("  🎯 中心位置: ({:.1}, {:.1})", pie_series.center[0], pie_series.center[1]);
    println!("  📏 半径: {:?}", pie_series.radius);

    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 600.0, 400.0),
        (0.0, 1.0),
        (0.0, 1.0),
    );

    match pie_series.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 渲染命令生成成功: {} 个命令", commands.len());
        }
        Err(e) => {
            println!("  ❌ 渲染失败: {:?}", e);
            return Err(e.into());
        }
    }

    Ok(())
}

fn test_scatter_series() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let scatter_data: Vec<(f64, f64)> = (0..15)
        .map(|i| {
            let x = i as f64;
            let y = (x * 0.5).sin() * 50.0 + 100.0;
            (x, y)
        })
        .collect();

    let scatter_series = ScatterSeries::new("测试散点图")
        .data(scatter_data)
        .symbol_size(6.0)
        .color(Color::rgb(0.4, 0.8, 0.4));

    println!("  ✅ 散点图创建成功");
    println!("  📊 数据点数量: {}", scatter_series.data.len());
    println!("  🎨 颜色: RGB({:.1}, {:.1}, {:.1})", 
             scatter_series.color.r, scatter_series.color.g, scatter_series.color.b);
    println!("  🔵 符号大小: {}", scatter_series.symbol_size);

    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 600.0, 400.0),
        (0.0, 15.0),
        (0.0, 200.0),
    );

    match scatter_series.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 渲染命令生成成功: {} 个命令", commands.len());
        }
        Err(e) => {
            println!("  ❌ 渲染失败: {:?}", e);
            return Err(e.into());
        }
    }

    Ok(())
}

fn test_coordinate_system() -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 测试不同的坐标系统配置
    let coord1 = CartesianCoordinateSystem::new(
        Bounds::new(0.0, 0.0, 800.0, 600.0),
        (0.0, 10.0),
        (0.0, 100.0),
    );

    let coord2 = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 700.0, 500.0),
        (-5.0, 5.0),
        (-50.0, 50.0),
    );

    println!("  ✅ 坐标系统1创建成功");
    println!("    📐 边界: ({:.0}, {:.0}) - ({:.0}, {:.0})", 
             coord1.bounds.origin.x, coord1.bounds.origin.y,
             coord1.bounds.origin.x + coord1.bounds.size.width,
             coord1.bounds.origin.y + coord1.bounds.size.height);
    println!("    📊 X轴范围: {:.1} - {:.1}", coord1.x_range.0, coord1.x_range.1);
    println!("    📊 Y轴范围: {:.1} - {:.1}", coord1.y_range.0, coord1.y_range.1);

    println!("  ✅ 坐标系统2创建成功");
    println!("    📐 边界: ({:.0}, {:.0}) - ({:.0}, {:.0})", 
             coord2.bounds.origin.x, coord2.bounds.origin.y,
             coord2.bounds.origin.x + coord2.bounds.size.width,
             coord2.bounds.origin.y + coord2.bounds.size.height);
    println!("    📊 X轴范围: {:.1} - {:.1}", coord2.x_range.0, coord2.x_range.1);
    println!("    📊 Y轴范围: {:.1} - {:.1}", coord2.y_range.0, coord2.y_range.1);

    Ok(())
}

fn comprehensive_test() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("  🔄 执行综合功能测试...");

    // 创建多个系列
    let line_series = LineSeries::new("综合测试线图")
        .data(vec![(1.0, 100.0), (2.0, 120.0), (3.0, 110.0)])
        .color(Color::rgb(0.2, 0.6, 1.0));

    let bar_series = BarSeries::new("综合测试柱图")
        .data(vec![(1.0, 200.0), (2.0, 180.0), (3.0, 220.0)])
        .color(Color::rgb(0.9, 0.4, 0.2));

    let pie_series = PieSeries::new("综合测试饼图")
        .data(vec![("A", 30.0), ("B", 40.0), ("C", 30.0)])
        .radius(0.6);

    let scatter_series = ScatterSeries::new("综合测试散点图")
        .data(vec![(1.0, 50.0), (2.0, 60.0), (3.0, 55.0)])
        .symbol_size(5.0);

    // 测试所有系列
    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 600.0, 400.0),
        (0.0, 4.0),
        (0.0, 250.0),
    );

    let mut total_commands = 0;

    // 测试线图
    match line_series.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("    ✅ 线图渲染成功: {} 个命令", commands.len());
            total_commands += commands.len();
        }
        Err(e) => {
            println!("    ❌ 线图渲染失败: {:?}", e);
            return Err(e.into());
        }
    }

    // 测试柱图
    match bar_series.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("    ✅ 柱图渲染成功: {} 个命令", commands.len());
            total_commands += commands.len();
        }
        Err(e) => {
            println!("    ❌ 柱图渲染失败: {:?}", e);
            return Err(e.into());
        }
    }

    // 测试饼图
    match pie_series.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("    ✅ 饼图渲染成功: {} 个命令", commands.len());
            total_commands += commands.len();
        }
        Err(e) => {
            println!("    ❌ 饼图渲染失败: {:?}", e);
            return Err(e.into());
        }
    }

    // 测试散点图
    match scatter_series.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("    ✅ 散点图渲染成功: {} 个命令", commands.len());
            total_commands += commands.len();
        }
        Err(e) => {
            println!("    ❌ 散点图渲染失败: {:?}", e);
            return Err(e.into());
        }
    }

    println!("  ✅ 综合测试完成，总计 {} 个渲染命令", total_commands);

    Ok(())
}
