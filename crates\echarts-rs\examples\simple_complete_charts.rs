//! 简化的完整图表生成器
//!
//! 生成真实完整的图表内容，替换之前的简单圆点

use std::fs;

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🎨 简化的完整图表生成器");
    println!("{}", "=".repeat(50));

    // 确保输出目录存在
    let output_dir = "temp/svg/simple_complete";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 生成完整的基础图表
    println!("\n📊 生成完整的基础图表...");
    
    // 折线图
    let line_data = vec![
        (0.0, 120.0), (1.0, 132.0), (2.0, 101.0), (3.0, 134.0), 
        (4.0, 90.0), (5.0, 230.0), (6.0, 210.0)
    ];
    let line_svg = generate_complete_line_chart("完整折线图", &line_data, 600.0, 400.0);
    fs::write(format!("{}/01_complete_line.svg", output_dir), line_svg)?;
    println!("  ✅ 完整折线图已生成");

    // 柱状图
    let bar_data = vec![
        ("Q1", 85.0), ("Q2", 120.0), ("Q3", 95.0), ("Q4", 140.0)
    ];
    let bar_svg = generate_complete_bar_chart("完整柱状图", &bar_data, 600.0, 400.0);
    fs::write(format!("{}/02_complete_bar.svg", output_dir), bar_svg)?;
    println!("  ✅ 完整柱状图已生成");

    // 散点图
    let scatter_data: Vec<(f64, f64)> = (0..30)
        .map(|i| {
            let x = i as f64 * 0.3;
            let y = (x * 1.5).sin() * 50.0 + 100.0 + (i as f64 * 0.2).cos() * 20.0;
            (x, y)
        })
        .collect();
    let scatter_svg = generate_complete_scatter_chart("完整散点图", &scatter_data, 600.0, 400.0);
    fs::write(format!("{}/03_complete_scatter.svg", output_dir), scatter_svg)?;
    println!("  ✅ 完整散点图已生成");

    // 饼图
    let pie_data = vec![
        ("产品A", 335.0), ("产品B", 310.0), ("产品C", 234.0), 
        ("产品D", 135.0), ("产品E", 548.0)
    ];
    let pie_svg = generate_complete_pie_chart("完整饼图", &pie_data, 500.0, 500.0);
    fs::write(format!("{}/04_complete_pie.svg", output_dir), pie_svg)?;
    println!("  ✅ 完整饼图已生成");

    // 多系列折线图
    let multi_line_svg = generate_multi_line_chart("多系列折线图", 700.0, 450.0);
    fs::write(format!("{}/05_multi_line.svg", output_dir), multi_line_svg)?;
    println!("  ✅ 多系列折线图已生成");

    // 混合图表
    let mixed_svg = generate_mixed_chart("混合图表", 800.0, 500.0);
    fs::write(format!("{}/06_mixed_chart.svg", output_dir), mixed_svg)?;
    println!("  ✅ 混合图表已生成");

    // 2. 生成展示页面
    println!("\n📄 生成展示页面...");
    generate_showcase_html(output_dir)?;

    println!("\n🎉 简化的完整图表生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/showcase.html 查看完整图表", output_dir);

    Ok(())
}

/// 生成完整的折线图
fn generate_complete_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    // SVG 头部
    svg.push_str(&format!(
        "<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n",
        width, height
    ));
    
    // 定义渐变
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"lineGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#007bff;stop-opacity:0.3\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#007bff;stop-opacity:0.1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    
    // 标题
    svg.push_str(&format!(
        "  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
        title
    ));
    
    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height
    ));
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y, chart_x, chart_y + chart_height
    ));
    
    if !data.is_empty() {
        // 计算数据范围
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        
        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };
        
        // 添加网格线
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            svg.push_str(&format!(
                "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
                x, chart_y, x, chart_y + chart_height
            ));
        }
        
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!(
                "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
                chart_x, y, chart_x + chart_width, y
            ));
        }
        
        // 生成路径
        let mut path = String::from("M");
        let mut area_path = String::from("M");
        
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
                area_path.push_str(&format!(" {} {}", px, chart_y + chart_height));
                area_path.push_str(&format!(" L {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
                area_path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        // 完成面积路径
        if let Some((last_x, _)) = data.last() {
            let last_px = chart_x + (last_x - min_x) / x_range * chart_width;
            area_path.push_str(&format!(" L {} {} Z", last_px, chart_y + chart_height));
        }
        
        // 绘制面积
        svg.push_str(&format!(
            "  <path d=\"{}\" fill=\"url(#lineGradient)\"/>\n",
            area_path
        ));
        
        // 绘制折线
        svg.push_str(&format!(
            "  <path d=\"{}\" stroke=\"#007bff\" stroke-width=\"3\" fill=\"none\"/>\n",
            path
        ));
        
        // 绘制数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            svg.push_str(&format!(
                "  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#007bff\" stroke=\"white\" stroke-width=\"2\"/>\n",
                px, py
            ));
        }
        
        // 添加坐标轴标签
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            let value = min_x + (i as f64 / 5.0) * x_range;
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{:.1}</text>\n",
                x, chart_y + chart_height + 20.0, value
            ));
        }
        
        for i in 0..=5 {
            let y = chart_y + chart_height - (i as f64 / 5.0) * chart_height;
            let value = min_y + (i as f64 / 5.0) * y_range;
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n",
                chart_x - 10.0, y + 4.0, value
            ));
        }
    }
    
    svg.push_str("</svg>");
    svg
}

/// 生成完整的柱状图
fn generate_complete_bar_chart(title: &str, data: &[(&str, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();

    // SVG 头部
    svg.push_str(&format!(
        "<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n",
        width, height
    ));

    // 定义渐变
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"barGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#28a745;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#1e7e34;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");

    // 标题
    svg.push_str(&format!(
        "  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
        title
    ));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;

    // 坐标轴
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height
    ));
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y, chart_x, chart_y + chart_height
    ));

    if !data.is_empty() {
        let max_value = data.iter().map(|(_, v)| *v).fold(0.0, f64::max);
        let bar_width = chart_width / data.len() as f64 * 0.8;
        let bar_spacing = chart_width / data.len() as f64;

        // 添加网格线
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!(
                "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
                chart_x, y, chart_x + chart_width, y
            ));
        }

        // 绘制柱子
        for (i, (label, value)) in data.iter().enumerate() {
            let x = chart_x + i as f64 * bar_spacing + bar_spacing * 0.1;
            let bar_height = (value / max_value) * chart_height;
            let y = chart_y + chart_height - bar_height;

            // 柱子
            svg.push_str(&format!(
                "  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"url(#barGradient)\" stroke=\"#1e7e34\" stroke-width=\"1\" rx=\"3\"/>\n",
                x, y, bar_width, bar_height
            ));

            // 数值标签
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" font-weight=\"bold\" fill=\"#333\">{:.0}</text>\n",
                x + bar_width / 2.0, y - 8.0, value
            ));

            // X轴标签
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{}</text>\n",
                x + bar_width / 2.0, chart_y + chart_height + 20.0, label
            ));
        }

        // Y轴标签
        for i in 0..=5 {
            let y = chart_y + chart_height - (i as f64 / 5.0) * chart_height;
            let value = (i as f64 / 5.0) * max_value;
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n",
                chart_x - 10.0, y + 4.0, value
            ));
        }
    }

    svg.push_str("</svg>");
    svg
}
