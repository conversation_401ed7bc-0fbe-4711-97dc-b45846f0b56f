use gpui::*;
mod plugin_manager;
mod ui_components;
use fscdaq::{init, open_new, Assets};
//  #[macro_use]
// extern crate rust_i18n;
// rust_i18n::i18n!("locales", fallback = "en");
// use rust_i18n::t;

fn main() {
    let app = Application::new().with_assets(Assets);

    // rust_i18n::set_locale("zh-CN");
    // println!("{:?}", t!("Run.run"));

    //  rust_i18n::set_locale("en");
    app.run(move |cx| {
        init(cx);
        open_new(cx, |_, _, _| {
            // div().child(t!("app.title"));
        })
        .detach();
    });
}
