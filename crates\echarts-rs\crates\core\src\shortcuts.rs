//! Keyboard shortcuts system for chart interactions
//!
//! Provides configurable keyboard shortcuts for common chart operations.

use crate::event::KeyEvent;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Chart action types that can be triggered by shortcuts
#[derive(Debug, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ChartAction {
    /// Toggle crosshair visibility
    ToggleCrosshair,

    /// Reset zoom to fit all data
    ResetZoom,

    /// Show/hide help information
    ToggleHelp,

    /// Toggle legend visibility
    ToggleLegend,

    /// Toggle grid lines
    ToggleGrid,

    /// Switch to next dataset
    NextDataset,

    /// Switch to previous dataset
    PreviousDataset,

    /// Zoom in
    ZoomIn,

    /// Zoom out
    ZoomOut,

    /// Pan left
    PanLeft,

    /// Pan right
    PanRight,

    /// Pan up
    PanUp,

    /// Pan down
    PanDown,

    /// Export chart
    ExportChart,

    /// Refresh data
    RefreshData,

    /// Toggle fullscreen
    ToggleFullscreen,

    /// Copy data to clipboard
    CopyData,

    /// Paste data from clipboard
    PasteData,

    /// Undo last action
    Undo,

    /// Redo last action
    Redo,

    /// Select all data points
    SelectAll,

    /// Clear selection
    ClearSelection,

    /// Switch to zoom mode
    ZoomMode,

    /// Switch to pan mode
    PanMode,

    /// Switch to select mode
    SelectMode,

    /// Switch to default mode
    DefaultMode,

    /// Move cursor left
    MoveCursorLeft,

    /// Move cursor right
    MoveCursorRight,

    /// Move cursor up
    MoveCursorUp,

    /// Move cursor down
    MoveCursorDown,

    /// Jump to first data point
    JumpToFirst,

    /// Jump to last data point
    JumpToLast,

    /// Custom action
    Custom(String),
}

/// Key combination for shortcuts
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct KeyCombination {
    /// Main key
    pub key: String,

    /// Whether Ctrl is required
    pub ctrl: bool,

    /// Whether Alt is required
    pub alt: bool,

    /// Whether Shift is required
    pub shift: bool,

    /// Whether Meta is required
    pub meta: bool,
}

impl KeyCombination {
    /// Create a simple key combination (just the key)
    pub fn key(key: &str) -> Self {
        Self {
            key: key.to_string(),
            ctrl: false,
            alt: false,
            shift: false,
            meta: false,
        }
    }

    /// Create a key combination with Ctrl
    pub fn ctrl_key(key: &str) -> Self {
        Self {
            key: key.to_string(),
            ctrl: true,
            alt: false,
            shift: false,
            meta: false,
        }
    }

    /// Create a key combination with Alt
    pub fn alt_key(key: &str) -> Self {
        Self {
            key: key.to_string(),
            ctrl: false,
            alt: true,
            shift: false,
            meta: false,
        }
    }

    /// Create a key combination with Shift
    pub fn shift_key(key: &str) -> Self {
        Self {
            key: key.to_string(),
            ctrl: false,
            alt: false,
            shift: true,
            meta: false,
        }
    }

    /// Check if this combination matches a key event
    pub fn matches(&self, event: &KeyEvent) -> bool {
        self.key == event.key
            && self.ctrl == event.ctrl_key
            && self.alt == event.alt_key
            && self.shift == event.shift_key
            && self.meta == event.meta_key
    }

    /// Get display string for this combination
    pub fn display_string(&self) -> String {
        let mut parts = Vec::new();

        if self.ctrl {
            parts.push("Ctrl");
        }
        if self.alt {
            parts.push("Alt");
        }
        if self.shift {
            parts.push("Shift");
        }
        if self.meta {
            parts.push("Meta");
        }
        parts.push(&self.key);

        parts.join("+")
    }
}

/// Keyboard shortcuts configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeyboardShortcuts {
    /// Shortcut mappings
    shortcuts: HashMap<KeyCombination, ChartAction>,

    /// Whether shortcuts are enabled
    enabled: bool,
}

impl Default for KeyboardShortcuts {
    fn default() -> Self {
        Self::new_with_defaults()
    }
}

impl KeyboardShortcuts {
    /// Create empty shortcuts configuration
    pub fn new() -> Self {
        Self {
            shortcuts: HashMap::new(),
            enabled: true,
        }
    }

    /// Create shortcuts with default mappings
    pub fn new_with_defaults() -> Self {
        let mut shortcuts = Self::new();
        shortcuts.add_default_shortcuts();
        shortcuts
    }

    /// Add default shortcut mappings
    pub fn add_default_shortcuts(&mut self) {
        // 基本功能
        self.add_shortcut(KeyCombination::key("c"), ChartAction::ToggleCrosshair);
        self.add_shortcut(KeyCombination::key("r"), ChartAction::ResetZoom);
        self.add_shortcut(KeyCombination::key("h"), ChartAction::ToggleHelp);
        self.add_shortcut(KeyCombination::key("l"), ChartAction::ToggleLegend);
        self.add_shortcut(KeyCombination::key("g"), ChartAction::ToggleGrid);

        // 数据集切换
        self.add_shortcut(KeyCombination::key("ArrowRight"), ChartAction::NextDataset);
        self.add_shortcut(
            KeyCombination::key("ArrowLeft"),
            ChartAction::PreviousDataset,
        );

        // 缩放和平移
        self.add_shortcut(KeyCombination::key("="), ChartAction::ZoomIn);
        self.add_shortcut(KeyCombination::key("-"), ChartAction::ZoomOut);
        self.add_shortcut(KeyCombination::shift_key("ArrowLeft"), ChartAction::PanLeft);
        self.add_shortcut(
            KeyCombination::shift_key("ArrowRight"),
            ChartAction::PanRight,
        );
        self.add_shortcut(KeyCombination::shift_key("ArrowUp"), ChartAction::PanUp);
        self.add_shortcut(KeyCombination::shift_key("ArrowDown"), ChartAction::PanDown);

        // 交互模式切换
        self.add_shortcut(KeyCombination::key("z"), ChartAction::ZoomMode);
        self.add_shortcut(KeyCombination::key("p"), ChartAction::PanMode);
        self.add_shortcut(KeyCombination::key("s"), ChartAction::SelectMode);
        self.add_shortcut(KeyCombination::key("Escape"), ChartAction::DefaultMode);

        // 光标移动 (使用 Ctrl + 方向键避免与数据集切换冲突)
        self.add_shortcut(
            KeyCombination::ctrl_key("ArrowLeft"),
            ChartAction::MoveCursorLeft,
        );
        self.add_shortcut(
            KeyCombination::ctrl_key("ArrowRight"),
            ChartAction::MoveCursorRight,
        );
        self.add_shortcut(
            KeyCombination::ctrl_key("ArrowUp"),
            ChartAction::MoveCursorUp,
        );
        self.add_shortcut(
            KeyCombination::ctrl_key("ArrowDown"),
            ChartAction::MoveCursorDown,
        );
        self.add_shortcut(KeyCombination::key("Home"), ChartAction::JumpToFirst);
        self.add_shortcut(KeyCombination::key("End"), ChartAction::JumpToLast);

        // 选择操作
        self.add_shortcut(KeyCombination::ctrl_key("a"), ChartAction::SelectAll);
        self.add_shortcut(KeyCombination::key("Delete"), ChartAction::ClearSelection);

        // 编辑操作
        self.add_shortcut(KeyCombination::ctrl_key("c"), ChartAction::CopyData);
        self.add_shortcut(KeyCombination::ctrl_key("v"), ChartAction::PasteData);
        self.add_shortcut(KeyCombination::ctrl_key("z"), ChartAction::Undo);
        self.add_shortcut(KeyCombination::ctrl_key("y"), ChartAction::Redo);

        // 数字键快速切换数据集
        for i in 1..=9 {
            self.add_shortcut(
                KeyCombination::key(&i.to_string()),
                ChartAction::Custom(format!("switch_dataset_{}", i - 1)),
            );
        }

        // 高级功能
        self.add_shortcut(KeyCombination::ctrl_key("s"), ChartAction::ExportChart);
        self.add_shortcut(KeyCombination::key("F5"), ChartAction::RefreshData);
        self.add_shortcut(KeyCombination::key("F11"), ChartAction::ToggleFullscreen);
    }

    /// Add a shortcut mapping
    pub fn add_shortcut(&mut self, key_combo: KeyCombination, action: ChartAction) {
        self.shortcuts.insert(key_combo, action);
    }

    /// Remove a shortcut mapping
    pub fn remove_shortcut(&mut self, key_combo: &KeyCombination) {
        self.shortcuts.remove(key_combo);
    }

    /// Get action for a key event
    pub fn get_action(&self, event: &KeyEvent) -> Option<&ChartAction> {
        if !self.enabled {
            return None;
        }

        for (key_combo, action) in &self.shortcuts {
            if key_combo.matches(event) {
                return Some(action);
            }
        }

        None
    }

    /// Enable or disable shortcuts
    pub fn set_enabled(&mut self, enabled: bool) {
        self.enabled = enabled;
    }

    /// Check if shortcuts are enabled
    pub fn is_enabled(&self) -> bool {
        self.enabled
    }

    /// Get all shortcuts as a list for display
    pub fn get_shortcuts_list(&self) -> Vec<(String, String)> {
        let mut shortcuts: Vec<_> = self
            .shortcuts
            .iter()
            .map(|(key_combo, action)| {
                (key_combo.display_string(), self.action_description(action))
            })
            .collect();

        shortcuts.sort_by(|a, b| a.0.cmp(&b.0));
        shortcuts
    }

    /// Get description for an action
    fn action_description(&self, action: &ChartAction) -> String {
        match action {
            ChartAction::ToggleCrosshair => "切换十字线".to_string(),
            ChartAction::ResetZoom => "重置缩放".to_string(),
            ChartAction::ToggleHelp => "显示/隐藏帮助".to_string(),
            ChartAction::ToggleLegend => "切换图例".to_string(),
            ChartAction::ToggleGrid => "切换网格线".to_string(),
            ChartAction::NextDataset => "下一个数据集".to_string(),
            ChartAction::PreviousDataset => "上一个数据集".to_string(),
            ChartAction::ZoomIn => "放大".to_string(),
            ChartAction::ZoomOut => "缩小".to_string(),
            ChartAction::PanLeft => "向左平移".to_string(),
            ChartAction::PanRight => "向右平移".to_string(),
            ChartAction::PanUp => "向上平移".to_string(),
            ChartAction::PanDown => "向下平移".to_string(),
            ChartAction::ExportChart => "导出图表".to_string(),
            ChartAction::RefreshData => "刷新数据".to_string(),
            ChartAction::ToggleFullscreen => "切换全屏".to_string(),
            ChartAction::CopyData => "复制数据".to_string(),
            ChartAction::PasteData => "粘贴数据".to_string(),
            ChartAction::Undo => "撤销".to_string(),
            ChartAction::Redo => "重做".to_string(),
            ChartAction::SelectAll => "全选".to_string(),
            ChartAction::ClearSelection => "清除选择".to_string(),
            ChartAction::ZoomMode => "缩放模式".to_string(),
            ChartAction::PanMode => "平移模式".to_string(),
            ChartAction::SelectMode => "选择模式".to_string(),
            ChartAction::DefaultMode => "默认模式".to_string(),
            ChartAction::MoveCursorLeft => "光标左移".to_string(),
            ChartAction::MoveCursorRight => "光标右移".to_string(),
            ChartAction::MoveCursorUp => "光标上移".to_string(),
            ChartAction::MoveCursorDown => "光标下移".to_string(),
            ChartAction::JumpToFirst => "跳转到开始".to_string(),
            ChartAction::JumpToLast => "跳转到结束".to_string(),
            ChartAction::Custom(name) => format!("自定义: {}", name),
        }
    }

    /// Clear all shortcuts
    pub fn clear(&mut self) {
        self.shortcuts.clear();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::event::{KeyEvent, KeyEventType};

    #[test]
    fn test_key_combination_matches() {
        let combo = KeyCombination::ctrl_key("s");

        let matching_event = KeyEvent::new(KeyEventType::KeyDown, "s".to_string())
            .with_modifiers(false, true, false, false);

        let non_matching_event = KeyEvent::new(KeyEventType::KeyDown, "s".to_string())
            .with_modifiers(false, false, false, false);

        assert!(combo.matches(&matching_event));
        assert!(!combo.matches(&non_matching_event));
    }

    #[test]
    fn test_shortcuts_default() {
        let shortcuts = KeyboardShortcuts::new_with_defaults();

        let crosshair_event = KeyEvent::new(KeyEventType::KeyDown, "c".to_string());
        let action = shortcuts.get_action(&crosshair_event);

        assert_eq!(action, Some(&ChartAction::ToggleCrosshair));
    }

    #[test]
    fn test_shortcuts_custom() {
        let mut shortcuts = KeyboardShortcuts::new();
        shortcuts.add_shortcut(
            KeyCombination::key("x"),
            ChartAction::Custom("test".to_string()),
        );

        let event = KeyEvent::new(KeyEventType::KeyDown, "x".to_string());
        let action = shortcuts.get_action(&event);

        assert_eq!(action, Some(&ChartAction::Custom("test".to_string())));
    }
}
