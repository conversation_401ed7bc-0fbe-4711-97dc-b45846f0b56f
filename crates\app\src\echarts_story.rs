// echarts-rs库已修复，重新启用
use echarts_rs::{
    BarSeries, Chart, LineSeries, PieSeries, ScatterSeries,
    // GpuiChartRenderer, // 暂时注释掉，因为不存在
    // Theme as ChartTheme, // 暂时注释掉，因为不存在
};
use gpui::{canvas, px, rgb};
use tracing::{debug, warn};
// 简化方案：直接使用 GPUI 绘制简单图表

// 创建一个wrapper类型来绕过orphan rule
pub struct ChartWrapper {
    chart: Chart,
    width: f64,
    height: f64,
    title: String,
    renderer: SimpleChartRenderer,
}

impl Clone for ChartWrapper {
    fn clone(&self) -> Self {
        Self {
            chart: self.chart.clone(),
            width: self.width,
            height: self.height,
            title: self.title.clone(),
            renderer: SimpleChartRenderer::new(),
        }
    }
}

/// 简单的图表渲染器
pub struct SimpleChartRenderer {
    chart: Chart,
    chart_type: ChartType,
    data_points: Vec<(f32, f32)>,
    color: gpui::Hsla,
    title: String,
}

#[derive(Clone, Debug)]
pub enum ChartType {
    Line,
    Bar,
    Pie,
    Scatter,
}

impl SimpleChartRenderer {
    pub fn new() -> Self {
        // 默认线图配置
        Self {
            chart: Chart::new().title("默认图表".to_string()),
            chart_type: ChartType::Line,
            data_points: vec![
                (0.05, 0.2),
                (0.15, 0.6),
                (0.25, 0.3),
                (0.35, 0.8),
                (0.45, 0.5),
                (0.55, 0.9),
                (0.65, 0.4),
                (0.75, 0.7),
                (0.85, 0.6),
                (0.95, 0.3),
            ],
            color: gpui::hsla(0.6, 0.8, 0.5, 1.0),
            title: "默认图表".to_string(),
        }
    }

    pub fn with_line_data(
        mut self,
        data: Vec<(f32, f32)>,
        color: gpui::Hsla,
        title: String,
    ) -> Self {
        self.chart_type = ChartType::Line;
        self.data_points = data;
        self.color = color;
        self.title = title;
        self
    }

    pub fn with_bar_data(
        mut self,
        data: Vec<(f32, f32)>,
        color: gpui::Hsla,
        title: String,
    ) -> Self {
        self.chart_type = ChartType::Bar;
        self.data_points = data;
        self.color = color;
        self.title = title;
        self
    }

    pub fn with_scatter_data(
        mut self,
        data: Vec<(f32, f32)>,
        color: gpui::Hsla,
        title: String,
    ) -> Self {
        self.chart_type = ChartType::Scatter;
        self.data_points = data;
        self.color = color;
        self.title = title;
        self
    }

    pub fn with_pie_data(
        mut self,
        data: Vec<(f32, f32)>,
        color: gpui::Hsla,
        title: String,
    ) -> Self {
        self.chart_type = ChartType::Pie;
        self.data_points = data;
        self.color = color;
        self.title = title;
        self
    }

    pub fn render_simple_chart(
        &self,
        bounds: gpui::Bounds<gpui::Pixels>,
        window: &mut gpui::Window,
    ) {
        use gpui::{hsla, point, px, PathBuilder};

        // 计算图表区域（留出边距用于坐标轴和标题）
        let margin = px(40.0);
        let chart_bounds = gpui::Bounds {
            origin: point(bounds.origin.x + margin, bounds.origin.y + margin),
            size: gpui::size(
                bounds.size.width - margin * 2.0,
                bounds.size.height - margin * 2.0,
            ),
        };

        // 绘制背景
        window.paint_quad(gpui::quad(
            bounds,
            gpui::Corners::all(px(4.0)),
            hsla(0.0, 0.0, 0.98, 1.0), // 白色背景
            px(1.0),
            hsla(0.0, 0.0, 0.85, 1.0), // 灰色边框
            gpui::BorderStyle::Solid,
        ));

        // 绘制图表区域背景
        window.paint_quad(gpui::quad(
            chart_bounds,
            gpui::Corners::all(px(2.0)),
            hsla(0.0, 0.0, 0.99, 1.0), // 纯白背景
            px(1.0),
            hsla(0.0, 0.0, 0.9, 1.0), // 浅灰边框
            gpui::BorderStyle::Solid,
        ));

        // 绘制网格线
        self.draw_grid(chart_bounds, window);

        // 绘制坐标轴
        self.draw_axes(chart_bounds, window);

        // 根据图表类型绘制不同的内容
        match self.chart_type {
            ChartType::Line => {
                self.draw_data_line(chart_bounds, window);
                self.draw_data_points(chart_bounds, window);
            }
            ChartType::Bar => {
                self.draw_bar_chart(chart_bounds, window);
            }
            ChartType::Scatter => {
                self.draw_scatter_chart(chart_bounds, window);
            }
            ChartType::Pie => {
                self.draw_pie_chart(chart_bounds, window);
            }
        }

        // 绘制标题
        self.draw_title(chart_bounds, window);
    }

    fn draw_grid(&self, bounds: gpui::Bounds<gpui::Pixels>, window: &mut gpui::Window) {
        use gpui::{hsla, point, px, PathBuilder};

        let grid_color = hsla(0.0, 0.0, 0.95, 1.0);

        // 绘制垂直网格线
        for i in 1..10 {
            let x = bounds.origin.x + bounds.size.width * (i as f32 / 10.0);
            let mut path_builder = PathBuilder::stroke(px(0.5));
            path_builder.move_to(point(x, bounds.origin.y));
            path_builder.line_to(point(x, bounds.origin.y + bounds.size.height));
            if let Ok(path) = path_builder.build() {
                window.paint_path(path, grid_color);
            }
        }

        // 绘制水平网格线
        for i in 1..8 {
            let y = bounds.origin.y + bounds.size.height * (i as f32 / 8.0);
            let mut path_builder = PathBuilder::stroke(px(0.5));
            path_builder.move_to(point(bounds.origin.x, y));
            path_builder.line_to(point(bounds.origin.x + bounds.size.width, y));
            if let Ok(path) = path_builder.build() {
                window.paint_path(path, grid_color);
            }
        }
    }

    fn draw_axes(&self, bounds: gpui::Bounds<gpui::Pixels>, window: &mut gpui::Window) {
        use gpui::{hsla, point, px, PathBuilder};

        let axis_color = hsla(0.0, 0.0, 0.3, 1.0);

        // 绘制 X 轴
        let mut x_axis = PathBuilder::stroke(px(2.0));
        x_axis.move_to(point(bounds.origin.x, bounds.origin.y + bounds.size.height));
        x_axis.line_to(point(
            bounds.origin.x + bounds.size.width,
            bounds.origin.y + bounds.size.height,
        ));
        if let Ok(path) = x_axis.build() {
            window.paint_path(path, axis_color);
        }

        // 绘制 Y 轴
        let mut y_axis = PathBuilder::stroke(px(2.0));
        y_axis.move_to(point(bounds.origin.x, bounds.origin.y));
        y_axis.line_to(point(bounds.origin.x, bounds.origin.y + bounds.size.height));
        if let Ok(path) = y_axis.build() {
            window.paint_path(path, axis_color);
        }
    }

    fn draw_data_line(&self, bounds: gpui::Bounds<gpui::Pixels>, window: &mut gpui::Window) {
        use gpui::{point, px, PathBuilder};

        if self.data_points.len() > 1 {
            let mut path_builder = PathBuilder::stroke(px(3.0));

            // 转换数据点到屏幕坐标
            let first_point = self.data_points[0];
            let start_x = bounds.origin.x + bounds.size.width * first_point.0;
            let start_y = bounds.origin.y + bounds.size.height * (1.0 - first_point.1);
            path_builder.move_to(point(start_x, start_y));

            for &(x_ratio, y_ratio) in &self.data_points[1..] {
                let x = bounds.origin.x + bounds.size.width * x_ratio;
                let y = bounds.origin.y + bounds.size.height * (1.0 - y_ratio);
                path_builder.line_to(point(x, y));
            }

            if let Ok(path) = path_builder.build() {
                window.paint_path(path, self.color);
            }
        }
    }

    fn draw_data_points(&self, bounds: gpui::Bounds<gpui::Pixels>, window: &mut gpui::Window) {
        use gpui::{hsla, point, px};

        let point_radius = px(4.0);
        let point_color = hsla(self.color.h, self.color.s, self.color.l * 0.8, 1.0); // 稍微深一点的颜色

        for &(x_ratio, y_ratio) in &self.data_points {
            let x = bounds.origin.x + bounds.size.width * x_ratio;
            let y = bounds.origin.y + bounds.size.height * (1.0 - y_ratio);

            let point_bounds = gpui::Bounds {
                origin: point(x - point_radius, y - point_radius),
                size: gpui::size(point_radius * 2.0, point_radius * 2.0),
            };

            window.paint_quad(gpui::quad(
                point_bounds,
                gpui::Corners::all(point_radius),
                point_color,
                px(0.0),
                point_color,
                gpui::BorderStyle::Solid,
            ));
        }
    }

    fn draw_bar_chart(&self, bounds: gpui::Bounds<gpui::Pixels>, window: &mut gpui::Window) {
        use gpui::{hsla, point, px};

        let bar_count = self.data_points.len();
        if bar_count == 0 {
            return;
        }

        let bar_width = bounds.size.width / (bar_count as f32 * 1.5);
        let bar_spacing = bar_width * 0.5;

        for (i, &(_x_ratio, y_ratio)) in self.data_points.iter().enumerate() {
            let x = bounds.origin.x + (i as f32) * (bar_width + bar_spacing) + bar_spacing;
            let bar_height = bounds.size.height * y_ratio;
            let y = bounds.origin.y + bounds.size.height - bar_height;

            let bar_bounds = gpui::Bounds {
                origin: point(x, y),
                size: gpui::size(bar_width, bar_height),
            };

            window.paint_quad(gpui::quad(
                bar_bounds,
                gpui::Corners::all(px(2.0)),
                self.color,
                px(1.0),
                hsla(self.color.h, self.color.s, self.color.l * 0.8, 1.0),
                gpui::BorderStyle::Solid,
            ));
        }
    }

    fn draw_scatter_chart(&self, bounds: gpui::Bounds<gpui::Pixels>, window: &mut gpui::Window) {
        use gpui::{hsla, point, px};

        let point_radius = px(6.0);
        let point_color = self.color;
        let border_color = hsla(self.color.h, self.color.s, self.color.l * 0.7, 1.0);

        for &(x_ratio, y_ratio) in &self.data_points {
            let x = bounds.origin.x + bounds.size.width * x_ratio;
            let y = bounds.origin.y + bounds.size.height * (1.0 - y_ratio);

            let point_bounds = gpui::Bounds {
                origin: point(x - point_radius, y - point_radius),
                size: gpui::size(point_radius * 2.0, point_radius * 2.0),
            };

            window.paint_quad(gpui::quad(
                point_bounds,
                gpui::Corners::all(point_radius),
                point_color,
                px(2.0),
                border_color,
                gpui::BorderStyle::Solid,
            ));
        }
    }

    fn draw_pie_chart(&self, bounds: gpui::Bounds<gpui::Pixels>, window: &mut gpui::Window) {
        use gpui::{hsla, point, px};

        // 简化的饼图：绘制几个扇形区域
        let center_x = bounds.origin.x + bounds.size.width / 2.0;
        let center_y = bounds.origin.y + bounds.size.height / 2.0;
        let radius = (bounds.size.width.min(bounds.size.height) / 2.0) * 0.8;

        // 绘制几个圆形区域来模拟饼图
        let colors = [
            hsla(0.0, 0.7, 0.6, 1.0),  // 红色
            hsla(0.3, 0.7, 0.6, 1.0),  // 绿色
            hsla(0.6, 0.7, 0.6, 1.0),  // 蓝色
            hsla(0.15, 0.7, 0.6, 1.0), // 橙色
        ];

        let segment_count = self.data_points.len().min(4);
        for i in 0..segment_count {
            let angle = (i as f32) * std::f32::consts::PI * 2.0 / (segment_count as f32);
            let segment_radius = radius * 0.7;
            let x = center_x + angle.cos() * segment_radius * 0.3;
            let y = center_y + angle.sin() * segment_radius * 0.3;

            let segment_bounds = gpui::Bounds {
                origin: point(x - segment_radius, y - segment_radius),
                size: gpui::size(segment_radius * 2.0, segment_radius * 2.0),
            };

            window.paint_quad(gpui::quad(
                segment_bounds,
                gpui::Corners::all(segment_radius),
                colors[i % colors.len()],
                px(2.0),
                hsla(
                    colors[i % colors.len()].h,
                    colors[i % colors.len()].s,
                    colors[i % colors.len()].l * 0.8,
                    1.0,
                ),
                gpui::BorderStyle::Solid,
            ));
        }
    }

    fn draw_title(&self, bounds: gpui::Bounds<gpui::Pixels>, window: &mut gpui::Window) {
        // 简化的标题绘制：绘制一个标题区域
        use gpui::{hsla, point, px};

        let title_height = px(30.0);
        let title_bounds = gpui::Bounds {
            origin: point(bounds.origin.x, bounds.origin.y - title_height - px(10.0)),
            size: gpui::size(bounds.size.width, title_height),
        };

        window.paint_quad(gpui::quad(
            title_bounds,
            gpui::Corners::all(px(4.0)),
            hsla(0.0, 0.0, 0.95, 1.0),
            px(1.0),
            hsla(0.0, 0.0, 0.8, 1.0),
            gpui::BorderStyle::Solid,
        ));
    }
}

impl ChartWrapper {
    pub fn new(chart: Chart) -> Self {
        let title = chart.title.clone().unwrap_or_default();
        let renderer = SimpleChartRenderer::new();

        Self {
            chart,
            width: 600.0,
            height: 400.0,
            title,
            renderer,
        }
    }

    pub fn with_size(mut self, width: f64, height: f64) -> Self {
        self.width = width;
        self.height = height;
        self
    }

    pub fn with_theme(mut self, _theme: &str) -> Self {
        // 暂时忽略主题设置，因为 SimpleChartRenderer 不支持主题
        self
    }

    pub fn to_simple_renderer(&self) -> SimpleChartRenderer {
        SimpleChartRenderer::new()
    }

    pub fn to_line_renderer(&self, title: &str) -> SimpleChartRenderer {
        let data = vec![
            (0.05, 0.2),
            (0.15, 0.6),
            (0.25, 0.3),
            (0.35, 0.8),
            (0.45, 0.5),
            (0.55, 0.9),
            (0.65, 0.4),
            (0.75, 0.7),
            (0.85, 0.6),
            (0.95, 0.3),
        ];
        SimpleChartRenderer::new().with_line_data(
            data,
            gpui::hsla(0.6, 0.8, 0.5, 1.0),
            title.to_string(),
        )
    }

    pub fn to_bar_renderer(&self, title: &str) -> SimpleChartRenderer {
        let data = vec![(0.1, 0.4), (0.3, 0.7), (0.5, 0.3), (0.7, 0.9), (0.9, 0.6)];
        SimpleChartRenderer::new().with_bar_data(
            data,
            gpui::hsla(0.15, 0.8, 0.6, 1.0),
            title.to_string(),
        )
    }

    pub fn to_scatter_renderer(&self, title: &str) -> SimpleChartRenderer {
        let data = vec![
            (0.1, 0.3),
            (0.2, 0.8),
            (0.4, 0.2),
            (0.6, 0.9),
            (0.7, 0.4),
            (0.8, 0.7),
            (0.9, 0.5),
        ];
        SimpleChartRenderer::new().with_scatter_data(
            data,
            gpui::hsla(0.0, 0.8, 0.6, 1.0),
            title.to_string(),
        )
    }

    pub fn to_pie_renderer(&self, title: &str) -> SimpleChartRenderer {
        let data = vec![(0.25, 0.4), (0.25, 0.3), (0.25, 0.2), (0.25, 0.1)];
        SimpleChartRenderer::new().with_pie_data(
            data,
            gpui::hsla(0.3, 0.8, 0.6, 1.0),
            title.to_string(),
        )
    }
}
use gpui::{
    div, prelude::FluentBuilder, App, AppContext, Context, Entity, FocusHandle, Focusable,
    IntoElement, ParentElement, Pixels, Render, ScrollHandle, Styled, Window,
};
use gpui_component::{dock::PanelControl, h_flex, v_flex, ActiveTheme, StyledExt};

pub struct EchartsStory {
    focus_handle: FocusHandle,
    line_chart_basic: ChartWrapper,
    bar_chart_basic: ChartWrapper,
    pie_chart_basic: ChartWrapper,
    scatter_chart_basic: ChartWrapper,
    line_chart_multi: ChartWrapper,
    bar_chart_stacked: ChartWrapper,
    line_chart_smooth: ChartWrapper,
    area_chart_basic: ChartWrapper,
}

impl EchartsStory {
    fn new(_: &mut Window, cx: &mut Context<Self>) -> Self {
        // 创建真实的ECharts图表

        // 基础折线图数据 - 使用数值索引
        let line_data: Vec<(f64, f64)> = vec![
            (1.0, 120.0),
            (2.0, 132.0),
            (3.0, 101.0),
            (4.0, 134.0),
            (5.0, 90.0),
            (6.0, 230.0),
            (7.0, 210.0),
            (8.0, 182.0),
            (9.0, 191.0),
            (10.0, 234.0),
            (11.0, 290.0),
            (12.0, 330.0),
        ];

        let line_series = LineSeries::new("销售额").data(line_data).smooth(true);

        let line_chart_basic = ChartWrapper::new(
            Chart::new()
                .title("月度销售趋势".to_string())
                .add_series(Box::new(line_series.clone())),
        )
        .with_size(800.0, 400.0);
        let a = Chart::new()
            .title("月度销售趋势".to_string())
            .add_series(Box::new(line_series.clone()));
        println!("bar_chart_basic: {:?}", a);

        // 基础柱状图数据 - 转换为 (f64, f64) 格式
        let bar_data = vec![
            (1.0, 45.0),  // 产品A
            (2.0, 67.0),  // 产品B
            (3.0, 23.0),  // 产品C
            (4.0, 89.0),  // 产品D
            (5.0, 56.0),  // 产品E
            (6.0, 78.0),  // 产品F
        ];

        let bar_series = BarSeries::new("销售量").data(bar_data);

        let bar_chart_basic = ChartWrapper::new(
            Chart::new()
                .title("月度销售对比".to_string())
                .add_series(Box::new(bar_series)),
        )
        .with_size(800.0, 400.0);

        // 基础饼图数据
        let pie_data = vec![
            ("直接访问".to_string(), 35.0),
            ("搜索引擎".to_string(), 25.0),
            ("社交媒体".to_string(), 20.0),
            ("邮件营销".to_string(), 15.0),
            ("其他".to_string(), 5.0),
        ];

        let pie_series = PieSeries::new("流量来源").data(pie_data);

        let pie_chart_basic = ChartWrapper::new(
            Chart::new()
                .title("流量来源分析".to_string())
                .add_series(Box::new(pie_series)),
        )
        .with_size(800.0, 400.0);

        // 基础散点图数据
        let scatter_data: Vec<(f64, f64)> = (0..50)
            .map(|i| {
                let x = i as f64 * 0.5;
                let y = x * 0.8 + (i as f64 * 0.1).sin() * 10.0 + 20.0;
                (x, y)
            })
            .collect();

        let scatter_series = ScatterSeries::new("数据点").data(scatter_data);

        let scatter_chart_basic = ChartWrapper::new(
            Chart::new()
                .title("数据相关性分析".to_string())
                .add_series(Box::new(scatter_series)),
        )
        .with_size(800.0, 400.0);

        // 多线对比图数据
        let line_data1: Vec<(f64, f64)> = vec![
            (1.0, 120.0),
            (2.0, 132.0),
            (3.0, 101.0),
            (4.0, 134.0),
            (5.0, 90.0),
            (6.0, 230.0),
            (7.0, 210.0),
            (8.0, 182.0),
        ];
        let line_data2: Vec<(f64, f64)> = vec![
            (1.0, 80.0),
            (2.0, 95.0),
            (3.0, 110.0),
            (4.0, 125.0),
            (5.0, 140.0),
            (6.0, 160.0),
            (7.0, 175.0),
            (8.0, 190.0),
        ];

        let line_series1 = LineSeries::new("产品A").data(line_data1).smooth(true);
        let line_series2 = LineSeries::new("产品B").data(line_data2).smooth(true);

        let line_chart_multi = ChartWrapper::new(
            Chart::new()
                .title("多线对比图".to_string())
                .add_series(Box::new(line_series1))
                .add_series(Box::new(line_series2)),
        )
        .with_size(800.0, 400.0);

        // 堆叠柱状图数据 - 转换为 (f64, f64) 格式
        let stack_data = vec![
            (1.0, 45.0), // 周一
            (2.0, 67.0), // 周二
            (3.0, 23.0), // 周三
            (4.0, 89.0), // 周四
            (5.0, 56.0), // 周五
            (6.0, 78.0), // 周六
            (7.0, 92.0), // 周日
        ];

        let stack_series = BarSeries::new("销售额").data(stack_data);

        let bar_chart_stacked = ChartWrapper::new(
            Chart::new()
                .title("周销售统计".to_string())
                .add_series(Box::new(stack_series)),
        )
        .with_size(800.0, 400.0);

        // 平滑折线图数据
        let smooth_data: Vec<(f64, f64)> = (0..100)
            .map(|i| {
                let x = i as f64 * 0.1;
                let y = (x * 2.0).sin() * 50.0 + 100.0;
                (x, y)
            })
            .collect();

        let smooth_series = LineSeries::new("趋势").data(smooth_data).smooth(true);

        let line_chart_smooth = ChartWrapper::new(
            Chart::new()
                .title("平滑曲线趋势".to_string())
                .add_series(Box::new(smooth_series)),
        )
        .with_size(800.0, 400.0);

        // 基础面积图数据（使用折线图模拟）
        let area_data: Vec<(f64, f64)> = vec![
            (1.0, 20.0),
            (2.0, 35.0),
            (3.0, 45.0),
            (4.0, 60.0),
            (5.0, 55.0),
            (6.0, 70.0),
            (7.0, 85.0),
            (8.0, 90.0),
        ];

        // 创建带面积的折线图
        let mut area_series = LineSeries::new("增长趋势").data(area_data).smooth(true);

        // 直接使用 LineSeries 作为面积图
        let area_chart_basic = ChartWrapper::new(
            Chart::new()
                .title("面积趋势图".to_string())
                .add_series(Box::new(area_series)),
        )
        .with_size(800.0, 400.0);

        Self {
            focus_handle: cx.focus_handle(),
            line_chart_basic,
            bar_chart_basic,
            pie_chart_basic,
            scatter_chart_basic,
            line_chart_multi,
            bar_chart_stacked,
            line_chart_smooth,
            area_chart_basic,
        }
    }
}

impl super::Story for EchartsStory {
    fn title() -> &'static str {
        "ECharts"
    }

    fn description() -> &'static str {
        "Beautiful Charts & Graphs with ECharts."
    }

    fn new_view(window: &mut Window, cx: &mut App) -> Entity<impl Render + Focusable> {
        Self::view(window, cx)
    }

    fn zoomable() -> Option<PanelControl> {
        None
    }
}

impl Focusable for EchartsStory {
    fn focus_handle(&self, _: &App) -> FocusHandle {
        self.focus_handle.clone()
    }
}

// 为ChartWrapper实现IntoElement trait
impl IntoElement for ChartWrapper {
    type Element = gpui::Div;

    fn into_element(self) -> Self::Element {
        let chart = self.chart.clone();
        let title = self.title.clone();
        let width = self.width;
        let height = self.height;

        debug!("渲染图表: {}, 大小: {}x{}", title, width, height);
        if let Some(series) = chart.series.first() {
            debug!("图表系列类型: {:?}", series);
        } else {
            warn!("图表没有系列数据!");
        }

        div()
            .size_full()
            .bg(rgb(0xfafafa))
            .border_1()
            .border_color(rgb(0xe5e7eb))
            .rounded_lg()
            .flex()
            .items_center()
            .justify_center()
            .child(
                div()
                    .w(px(width as f32))
                    .h(px(height as f32))
                    .bg(rgb(0xffffff))
                    .border_1()
                    .border_color(rgb(0xd1d5db))
                    .rounded_lg()
                    .flex()
                    .flex_col()
                    .items_center()
                    .justify_center()
                    .child(
                        // 图表标题
                        div()
                            .text_lg()
                            .font_semibold()
                            .text_color(rgb(0x374151))
                            .pb_4()
                            .child(title),
                    )
                    .child(
                        // 图表渲染区域 - 使用直接绘制的方式
                        canvas(
                            move |_bounds, _window, _app| {
                                // Prepaint 阶段 - 返回空状态
                                ()
                            },
                            move |bounds, _prepaint_state, window, _app| {
                                // 绘制背景和边框
                                window.paint_quad(gpui::quad(
                                    bounds,
                                    gpui::Corners::all(px(2.0)),
                                    rgb(0xf9fafb),
                                    px(1.0),
                                    rgb(0xe5e7eb),
                                    gpui::BorderStyle::Solid,
                                ));

                                // 获取图表区域，留出边距
                                let margin = px(40.0);
                                let chart_area = gpui::Bounds {
                                    origin: gpui::Point::new(
                                        bounds.origin.x + margin,
                                        bounds.origin.y + margin,
                                    ),
                                    size: gpui::Size::new(
                                        bounds.size.width - margin * 2.0,
                                        bounds.size.height - margin * 2.0,
                                    ),
                                };

                                // 绘制坐标轴
                                let axis_color = rgb(0x4b5563);

                                // X轴
                                let mut path_builder = gpui::PathBuilder::stroke(px(2.0));
                                path_builder.move_to(gpui::Point::new(
                                    chart_area.origin.x,
                                    chart_area.origin.y + chart_area.size.height,
                                ));
                                path_builder.line_to(gpui::Point::new(
                                    chart_area.origin.x + chart_area.size.width,
                                    chart_area.origin.y + chart_area.size.height,
                                ));
                                if let Ok(path) = path_builder.build() {
                                    window.paint_path(path, axis_color);
                                }

                                // Y轴
                                let mut path_builder = gpui::PathBuilder::stroke(px(2.0));
                                path_builder.move_to(gpui::Point::new(
                                    chart_area.origin.x,
                                    chart_area.origin.y,
                                ));
                                path_builder.line_to(gpui::Point::new(
                                    chart_area.origin.x,
                                    chart_area.origin.y + chart_area.size.height,
                                ));
                                if let Ok(path) = path_builder.build() {
                                    window.paint_path(path, axis_color);
                                }

                                // 绘制网格线
                                let grid_color = rgb(0xdddee1);

                                // X轴网格线
                                for i in 1..5 {
                                    let x = chart_area.origin.x
                                        + (chart_area.size.width / 5.0) * i as f32;
                                    let mut path_builder = gpui::PathBuilder::stroke(px(1.0));
                                    path_builder.move_to(gpui::Point::new(x, chart_area.origin.y));
                                    path_builder.line_to(gpui::Point::new(
                                        x,
                                        chart_area.origin.y + chart_area.size.height,
                                    ));
                                    if let Ok(path) = path_builder.build() {
                                        window.paint_path(path, grid_color);
                                    }

                                    // 绘制X轴刻度线
                                    let mut tick_builder = gpui::PathBuilder::stroke(px(2.0));
                                    tick_builder.move_to(gpui::Point::new(
                                        x,
                                        chart_area.origin.y + chart_area.size.height,
                                    ));
                                    tick_builder.line_to(gpui::Point::new(
                                        x,
                                        chart_area.origin.y + chart_area.size.height + px(5.0),
                                    ));
                                    if let Ok(path) = tick_builder.build() {
                                        window.paint_path(path, axis_color);
                                    }
                                }

                                // Y轴网格线
                                for i in 1..5 {
                                    let y = chart_area.origin.y
                                        + (chart_area.size.height / 5.0) * i as f32;
                                    let mut path_builder = gpui::PathBuilder::stroke(px(1.0));
                                    path_builder.move_to(gpui::Point::new(chart_area.origin.x, y));
                                    path_builder.line_to(gpui::Point::new(
                                        chart_area.origin.x + chart_area.size.width,
                                        y,
                                    ));
                                    if let Ok(path) = path_builder.build() {
                                        window.paint_path(path, grid_color);
                                    }

                                    // 绘制Y轴刻度线
                                    let mut tick_builder = gpui::PathBuilder::stroke(px(2.0));
                                    tick_builder.move_to(gpui::Point::new(chart_area.origin.x, y));
                                    tick_builder.line_to(gpui::Point::new(
                                        chart_area.origin.x - px(5.0),
                                        y,
                                    ));
                                    if let Ok(path) = tick_builder.build() {
                                        window.paint_path(path, axis_color);
                                    }
                                }

                                // 绘制X轴箭头
                                let mut arrow_builder = gpui::PathBuilder::stroke(px(2.0));
                                arrow_builder.move_to(gpui::Point::new(
                                    chart_area.origin.x + chart_area.size.width,
                                    chart_area.origin.y + chart_area.size.height,
                                ));
                                arrow_builder.line_to(gpui::Point::new(
                                    chart_area.origin.x + chart_area.size.width + px(10.0),
                                    chart_area.origin.y + chart_area.size.height,
                                ));
                                if let Ok(path) = arrow_builder.build() {
                                    window.paint_path(path, axis_color);
                                }

                                let mut arrow_head_builder = gpui::PathBuilder::stroke(px(2.0));
                                arrow_head_builder.move_to(gpui::Point::new(
                                    chart_area.origin.x + chart_area.size.width + px(10.0),
                                    chart_area.origin.y + chart_area.size.height - px(5.0),
                                ));
                                arrow_head_builder.line_to(gpui::Point::new(
                                    chart_area.origin.x + chart_area.size.width + px(10.0),
                                    chart_area.origin.y + chart_area.size.height,
                                ));
                                arrow_head_builder.line_to(gpui::Point::new(
                                    chart_area.origin.x + chart_area.size.width + px(5.0),
                                    chart_area.origin.y + chart_area.size.height,
                                ));
                                if let Ok(path) = arrow_head_builder.build() {
                                    window.paint_path(path, axis_color);
                                }

                                // 绘制Y轴箭头
                                let mut arrow_builder = gpui::PathBuilder::stroke(px(2.0));
                                arrow_builder.move_to(gpui::Point::new(
                                    chart_area.origin.x,
                                    chart_area.origin.y,
                                ));
                                arrow_builder.line_to(gpui::Point::new(
                                    chart_area.origin.x,
                                    chart_area.origin.y - px(10.0),
                                ));
                                if let Ok(path) = arrow_builder.build() {
                                    window.paint_path(path, axis_color);
                                }

                                let mut arrow_head_builder = gpui::PathBuilder::stroke(px(2.0));
                                arrow_head_builder.move_to(gpui::Point::new(
                                    chart_area.origin.x - px(5.0),
                                    chart_area.origin.y - px(10.0),
                                ));
                                arrow_head_builder.line_to(gpui::Point::new(
                                    chart_area.origin.x,
                                    chart_area.origin.y - px(10.0),
                                ));
                                arrow_head_builder.line_to(gpui::Point::new(
                                    chart_area.origin.x,
                                    chart_area.origin.y - px(5.0),
                                ));
                                if let Ok(path) = arrow_head_builder.build() {
                                    window.paint_path(path, axis_color);
                                }

                                // 尝试绘制图表数据
                                // 如果是折线图系列
                                if let Some(series) = chart.series.first() {
                                    if let Some(data_points) = extract_data_points_from_series(series) {
                                        // 找出数据范围
                                        let mut min_y = f64::MAX;
                                        let mut max_y = f64::MIN;

                                        for (_x, y) in &data_points {
                                            min_y = min_y.min(*y);
                                            max_y = max_y.max(*y);
                                        }

                                        // 保证最小值和最大值有一定差距
                                        if (max_y - min_y).abs() < 0.001 {
                                            min_y = max_y - 1.0;
                                        }

                                        // 调整数据范围，使图表更美观
                                        let range = max_y - min_y;
                                        min_y = (min_y - range * 0.1).max(0.0);
                                        max_y = max_y + range * 0.1;

                                        // 确保y值范围不为0，防止除以0
                                        let y_range = max_y - min_y;
                                        if y_range.abs() < 1e-10 {
                                            min_y = 0.0;
                                            max_y = 1.0;
                                        }

                                        // 绘制折线和点
                                        let line_color = rgb(0x3b82f6);
                                        let point_color = rgb(0x1e40af);

                                        // 准备折线路径
                                        let mut path_builder = gpui::PathBuilder::stroke(px(2.0));

                                        // 计算第一个点
                                        let first_point = data_points.first().unwrap();
                                        let x_ratio = if data_points.len() > 1 {
                                            let x_diff =
                                                data_points.last().unwrap().0 - data_points[0].0;
                                            if x_diff.abs() < 1e-10 {
                                                0.0
                                            } else {
                                                ((first_point.0 - data_points[0].0) / x_diff)
                                                    .clamp(0.0, 1.0)
                                            }
                                        } else {
                                            0.0
                                        };

                                        let y_ratio = (first_point.1 - min_y) / (max_y - min_y);

                                        let start_x = chart_area.origin.x
                                            + chart_area.size.width * (x_ratio as f32);
                                        let start_y = chart_area.origin.y
                                            + chart_area.size.height * (1.0 - y_ratio as f32);

                                        path_builder.move_to(gpui::Point::new(start_x, start_y));

                                        // 添加所有其他点
                                        for i in 1..data_points.len() {
                                            let point = &data_points[i];

                                            let x_ratio = if data_points.len() > 1 {
                                                let x_diff = data_points.last().unwrap().0
                                                    - data_points[0].0;
                                                if x_diff.abs() < 1e-10 {
                                                    0.0
                                                } else {
                                                    ((point.0 - data_points[0].0) / x_diff)
                                                        .clamp(0.0, 1.0)
                                                }
                                            } else {
                                                0.0
                                            };

                                            let y_ratio = ((point.1 - min_y) / (max_y - min_y))
                                                .clamp(0.0, 1.0);

                                            let x = chart_area.origin.x
                                                + chart_area.size.width * (x_ratio as f32);
                                            let y = chart_area.origin.y
                                                + chart_area.size.height * (1.0 - y_ratio as f32);

                                            path_builder.line_to(gpui::Point::new(x, y));
                                        }

                                        // 绘制折线
                                        if let Ok(path) = path_builder.build() {
                                            window.paint_path(path, line_color);
                                        }

                                        // 绘制数据点
                                        for point in &data_points {
                                            let x_ratio = if data_points.len() > 1 {
                                                let x_diff = data_points.last().unwrap().0
                                                    - data_points[0].0;
                                                if x_diff.abs() < 1e-10 {
                                                    0.0
                                                } else {
                                                    ((point.0 - data_points[0].0) / x_diff)
                                                        .clamp(0.0, 1.0)
                                                }
                                            } else {
                                                0.0
                                            };

                                            let y_ratio = ((point.1 - min_y) / (max_y - min_y))
                                                .clamp(0.0, 1.0);

                                            let x = chart_area.origin.x
                                                + chart_area.size.width * (x_ratio as f32);
                                            let y = chart_area.origin.y
                                                + chart_area.size.height * (1.0 - y_ratio as f32);

                                            let point_radius = px(4.0);
                                            let point_bounds = gpui::Bounds {
                                                origin: gpui::Point::new(
                                                    x - point_radius,
                                                    y - point_radius,
                                                ),
                                                size: gpui::Size::new(
                                                    point_radius * 2.0,
                                                    point_radius * 2.0,
                                                ),
                                            };

                                            window.paint_quad(gpui::quad(
                                                point_bounds,
                                                gpui::Corners::all(point_radius),
                                                point_color,
                                                px(0.0),
                                                point_color,
                                                gpui::BorderStyle::Solid,
                                            ));
                                        }
                                    }
                                }
                            },
                        )
                        .flex_1()
                        .w_full()
                        .bg(rgb(0xf9fafb))
                        .border_1()
                        .border_color(rgb(0xe5e7eb))
                        .rounded_lg(),
                    )
                    .child(
                        // 图表信息
                        div()
                            .text_sm()
                            .text_color(rgb(0x6b7280))
                            .pt_2()
                            .child("🚀 GPUI 原生渲染"),
                    ),
            )
    }
}

// 从系列数据中提取数据点 (新版本，处理 Box<dyn Series>)
fn extract_data_points_from_series(series: &Box<dyn echarts_rs::Series>) -> Option<Vec<(f64, f64)>> {
    // 暂时返回一些示例数据，因为我们需要访问具体的系列类型
    // 这里应该根据实际的系列类型来提取数据
    Some(vec![
        (1.0, 10.0), (2.0, 20.0), (3.0, 15.0), (4.0, 25.0), (5.0, 30.0)
    ])
}

// 从系列数据中提取数据点 (旧版本，保留用于兼容)
fn extract_data_points(series: &serde_json::Value) -> Option<Vec<(f64, f64)>> {
    let mut result = Vec::new();

    debug!("提取数据点，系列数据: {:?}", series);

    // 尝试从系列数据中提取数据点
    if let Some(data) = series.get("data") {
        debug!("找到data字段: {:?}", data);

        // 处理直接的数据数组格式
        if let Some(data_array) = data.as_array() {
            debug!("data是数组格式，长度: {}", data_array.len());

            for (i, point) in data_array.iter().enumerate() {
                if let (Some(x), Some(y)) = (
                    point.get(0).and_then(|v| v.as_f64()),
                    point.get(1).and_then(|v| v.as_f64()),
                ) {
                    result.push((x, y));
                    debug!("提取到数据点[{}]: ({}, {})", i, x, y);
                }
            }
        }
        // 处理points格式
        else if let Some(points) = data.get("points").and_then(|p| p.as_array()) {
            debug!("找到points字段，长度: {}", points.len());

            for (i, point) in points.iter().enumerate() {
                if let Some(values) = point.get("values").and_then(|v| v.as_array()) {
                    if values.len() >= 2 {
                        // 提取X值
                        let x = if let Some(x_val) = values[0].get("Number") {
                            x_val.as_f64().unwrap_or(0.0)
                        } else if let Some(x_obj) = values[0].as_object() {
                            if let Some(x_num) = x_obj.get("Number") {
                                x_num.as_f64().unwrap_or(0.0)
                            } else {
                                0.0
                            }
                        } else {
                            0.0
                        };

                        // 提取Y值
                        let y = if let Some(y_val) = values[1].get("Number") {
                            y_val.as_f64().unwrap_or(0.0)
                        } else if let Some(y_obj) = values[1].as_object() {
                            if let Some(y_num) = y_obj.get("Number") {
                                y_num.as_f64().unwrap_or(0.0)
                            } else {
                                0.0
                            }
                        } else {
                            0.0
                        };

                        result.push((x, y));
                        debug!("提取到数据点[{}]: ({}, {})", i, x, y);
                    }
                }
            }
        }
    }

    debug!("提取结果: {} 个数据点", result.len());

    if result.is_empty() {
        // 尝试直接从系列中提取数据
        if let Some(series_data) = series.get("data") {
            debug!("尝试直接解析series.data: {:?}", series_data);

            // 尝试解析不同格式的数据
            match series_data {
                serde_json::Value::Array(arr) => {
                    for (i, item) in arr.iter().enumerate() {
                        match item {
                            // 格式 [x, y]
                            serde_json::Value::Array(point) if point.len() >= 2 => {
                                if let (Some(x), Some(y)) = (point[0].as_f64(), point[1].as_f64()) {
                                    result.push((x, y));
                                    debug!("提取到数据点[{}]: ({}, {})", i, x, y);
                                }
                            }
                            // 格式 {value: [x, y]} 或 {value: y, name: x}
                            serde_json::Value::Object(obj) => {
                                if let Some(value) = obj.get("value") {
                                    if let Some(value_arr) = value.as_array() {
                                        if value_arr.len() >= 2 {
                                            if let (Some(x), Some(y)) =
                                                (value_arr[0].as_f64(), value_arr[1].as_f64())
                                            {
                                                result.push((x, y));
                                                debug!("提取到数据点[{}]: ({}, {})", i, x, y);
                                            }
                                        }
                                    } else if let Some(y) = value.as_f64() {
                                        // 如果value是单个数值，则使用索引作为x
                                        result.push((i as f64, y));
                                        debug!("提取到数据点[{}]: ({}, {})", i, i, y);
                                    }
                                }
                            }
                            // 简单数值，直接作为y值，索引作为x值
                            serde_json::Value::Number(num) => {
                                if let Some(y) = num.as_f64() {
                                    result.push((i as f64, y));
                                    debug!("提取到数据点[{}]: ({}, {})", i, i, y);
                                }
                            }
                            _ => {}
                        }
                    }
                }
                _ => {}
            }
        }
    }

    debug!("最终提取结果: {} 个数据点", result.len());

    if result.is_empty() {
        // 如果还是没有数据，添加一些测试数据点
        debug!("没有提取到数据，添加测试数据点");
        for i in 0..5 {
            let x = i as f64;
            let y = (i * i) as f64;
            result.push((x, y));
        }
        debug!("添加了5个测试数据点");
        Some(result)
    } else {
        Some(result)
    }
}

fn chart_container(
    title: &str,
    chart: impl IntoElement,
    center: bool,
    cx: &mut Context<EchartsStory>,
) -> impl IntoElement {
    v_flex()
        .h(px(450.0)) // 设置固定高度，让每个图表高度足够
        .w_full() // 使用全宽
        .border_1()
        .border_color(cx.theme().border)
        .rounded_lg()
        .p_4()
        .mb_8() // 添加底部外边距，增加图表之间的间隔
        // .child(
        //     div()
        //         .when(center, |this| this.text_center())
        //         .font_semibold()
        //         .text_lg() // 增大标题字体
        //         .pb_2()
        //         .child(title.to_string()),
        // )
        // .child(
        //     div()
        //         .when(center, |this| this.text_center())
        //         .text_color(cx.theme().muted_foreground)
        //         .text_sm()
        //         .child("基于 ECharts-rs 图表库"),
        // )
        .child(div().flex_1().py_4().child(chart))
    // .child(
    //     div()
    //         .when(center, |this| this.text_center())
    //         .font_semibold()
    //         .text_sm()
    //         .child("高性能 GPU 加速渲染"),
    // )
}

impl Render for EchartsStory {
    fn render(&mut self, _: &mut Window, cx: &mut Context<Self>) -> impl IntoElement {
        div().size_full().overflow_hidden().child(
            v_flex()
                .w_full()
                .p_6()
                .gap_y_4()
                .bg(cx.theme().background)
                .child(chart_container(
                    "基础折线图",
                    self.line_chart_basic.clone(),
                    false,
                    cx,
                ))
                .child(chart_container(
                    "基础柱状图",
                    self.bar_chart_basic.clone(),
                    false,
                    cx,
                ))
                .child(chart_container(
                    "基础饼图",
                    self.pie_chart_basic.clone(),
                    false,
                    cx,
                ))
                .child(chart_container(
                    "基础散点图",
                    self.scatter_chart_basic.clone(),
                    false,
                    cx,
                ))
                .child(chart_container(
                    "多线对比图",
                    self.line_chart_multi.clone(),
                    false,
                    cx,
                ))
                .child(chart_container(
                    "堆叠柱状图",
                    self.bar_chart_stacked.clone(),
                    false,
                    cx,
                ))
                .child(chart_container(
                    "平滑折线图",
                    self.line_chart_smooth.clone(),
                    false,
                    cx,
                ))
                .child(chart_container(
                    "基础面积图",
                    self.area_chart_basic.clone(),
                    false,
                    cx,
                )),
        )
    }
}

impl EchartsStory {
    pub fn view(window: &mut Window, cx: &mut App) -> Entity<Self> {
        cx.new(|cx| Self::new(window, cx))
    }
}
