---
type: "agent_requested"
description: "Example description"
---
# 上下文准备

为Claude提供全面的项目理解。

## 标准上下文加载：
1. 阅读README.md了解项目概述
2. 阅读CLAUDE.md获取AI特定指令
3. 列出项目文件（排除被忽略的路径）
4. 审查关键配置文件
5. 理解项目结构和约定

## 步骤：
1. **项目概述**：
   - 阅读README.md
   - 确定项目类型和目的
   - 记录关键技术和依赖项

2. **AI指南**：
   - 如存在，阅读CLAUDE.md
   - 加载项目特定的AI指令
   - 记录编码标准和偏好

3. **仓库结构**：
   - 运行：`git ls-files | head -50`获取初始结构
   - 确定主要目录及其用途
   - 记录命名约定

4. **配置审查**：
   - 包管理器文件（package.json, Cargo.toml等）
   - 构建配置
   - 环境设置

5. **开发上下文**：
   - 确定测试框架
   - 记录CI/CD配置
   - 审查贡献指南

## 高级选项：
- 加载特定子系统上下文
- 专注于特定技术栈
- 包含最近的提交历史
- 加载自定义命令定义

## 输出：
建立对以下方面的清晰理解：
- 项目目标和约束
- 技术架构
- 开发工作流
- 协作参数