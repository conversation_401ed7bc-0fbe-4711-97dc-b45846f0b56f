# 设备管理模块

本模块提供设备连接、数据采集和设备状态管理的功能。

## 功能特性

### 设备连接管理
- 自动扫描并连接可用的串口设备
- 支持多设备并发连接
- 设备连接状态实时监控
- 设备断开连接和清理

### 数据采集控制
- 开始/停止数据采集
- 多设备并发数据采集
- 数据流实时处理
- 采集状态监控

### 防双击机制
- **时间窗口防双击**: 设置2秒防双击时间窗口，防止用户快速连续点击
- **加载状态防双击**: 当操作正在进行时，自动忽略新的点击事件
- **失败重试机制**: 操作失败时清除防双击限制，允许用户重试
- **详细日志记录**: 记录防双击事件和操作状态变化

#### 防双击实现细节

```rust
// 防双击时间窗口（毫秒）
const DOUBLE_CLICK_PREVENTION_MS: u64 = 2000;

// 防双击检查逻辑
let now = Instant::now();
if let Some(last_click) = self.last_run_click_time {
    let elapsed = now.duration_since(last_click);
    if elapsed.as_millis() < DOUBLE_CLICK_PREVENTION_MS as u128 {
        warn!("采集按钮点击过于频繁，忽略此次点击 (间隔: {}ms)", elapsed.as_millis());
        return;
    }
}
```

#### 防双击特性

1. **时间戳记录**: 每次点击都记录时间戳
2. **间隔检查**: 检查与上次点击的时间间隔
3. **状态检查**: 检查按钮是否处于加载状态
4. **失败处理**: 操作失败时清除防双击限制
5. **日志记录**: 详细记录防双击事件

## 使用示例

```rust
// 创建数据采集组件
let data_collection = DataCollection::new(window, cx);

// 组件会自动处理防双击逻辑
// 用户快速点击会被忽略并记录警告日志
// 操作失败时会清除防双击限制，允许重试
```

## 错误处理

- 设备连接失败时自动重试
- 数据采集操作失败时提供详细错误信息
- 防双击机制确保操作稳定性
- 异步操作超时处理

## 日志记录

- 使用 `tracing` 进行结构化日志记录
- 记录设备连接状态变化
- 记录数据采集操作结果
- 记录防双击事件和操作状态

## 性能优化

- 异步操作避免阻塞UI
- 并发处理多设备操作
- 合理的内存管理和资源清理
- 防双击机制减少不必要的操作 