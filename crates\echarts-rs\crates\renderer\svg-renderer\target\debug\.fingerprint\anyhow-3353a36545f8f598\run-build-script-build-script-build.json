{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13625485746686963219, "build_script_build", false, 6178415638063109200]], "local": [{"RerunIfChanged": {"output": "debug\\build\\anyhow-3353a36545f8f598\\output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": ["-C", "link-arg=/STACK:16000000"], "config": 0, "compile_kind": 0}