#!/usr/bin/env python3
"""
批量修复基础架构导入
"""

import os
import re

def fix_imports_for_file(file_path):
    """为单个文件修复导入"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有基础架构导入
        if "use crate::base::" in content:
            print(f"  ⏭️  {file_path} 已有导入")
            return False
        
        # 查找 serde 导入行
        serde_pattern = r'(use serde::\{[^}]+\};)'
        match = re.search(serde_pattern, content)
        
        if match:
            # 在 serde 导入前添加基础架构导入
            serde_line = match.group(1)
            new_import = "use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};"
            content = content.replace(serde_line, f"{new_import}\n{serde_line}")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ 已修复 {file_path}")
            return True
        else:
            print(f"  ❌ 未找到 serde 导入: {file_path}")
            return False
            
    except Exception as e:
        print(f"  ❌ 修复失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始批量修复导入...")
    
    # 需要修复的文件列表
    files_to_fix = [
        "crates/charts/src/radar.rs",
        "crates/charts/src/gauge.rs", 
        "crates/charts/src/treemap.rs",
        "crates/charts/src/sunburst.rs",
        "crates/charts/src/funnel.rs",
        "crates/charts/src/candlestick.rs",
    ]
    
    fixed_count = 0
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            print(f"修复文件: {file_path}")
            if fix_imports_for_file(file_path):
                fixed_count += 1
        else:
            print(f"  ❌ 文件不存在: {file_path}")
    
    print(f"✅ 修复完成！共修复 {fixed_count} 个文件")

if __name__ == "__main__":
    main()
