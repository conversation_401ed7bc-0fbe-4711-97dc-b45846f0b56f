/*
 * @Author: Artis
 * @Date: 2025-06-15 14:20:44
 * @LastEditors: Artis
 * @LastEditTime: 2025-07-19 16:46:48
 * @FilePath: \FscDAQ_echarts\crates\chart\src\shape\line.rs
 * @Description:
 *
 * Copyright (c) 2025 by Artis, All Rights Reserved.
 */

use gpui::{
    px, quad, size, Background, BorderStyle, Bounds, Hsla, PaintQuad, Path, PathBuilder, Pixels,
    Point, Window,
};

use crate::shape::PointCoord;

#[derive(Clone)]
pub struct LineData {
    pub point: Vec<PointCoord>,
    pub color: Hsla,
    pub width: u32,
    pub(crate) dash_array: Option<[Pixels; 2]>,
}

#[derive(Clone)]
pub struct PaintLine {
    color: Hsla,
    width: f32,
    p1: Option<Point<Pixels>>,
    p2: Option<Point<Pixels>>,
}

pub struct Line {
    data: Vec<LineData>,
}

impl Default for Line {
    fn default() -> Self {
        Self { data: Vec::new() }
    }
}

impl Line {
    pub fn new() -> Self {
        Self::default()
    }

    /// Set the data of the Line.
    pub fn push(mut self, data: LineData) -> Self {
        self.data.push(data);
        self
    }

    fn paint_line(&mut self, window: &mut Window, line: PaintLine) {
        let mut path = None;
        if let Some(point) = line.p1 {
            let (p1_1, p1_2) = self.offset_points(None, point, line.p2, line.width / 2.);
            if let Some(point) = line.p2 {
                let (p2_1, p2_2) = self.offset_points(line.p1, point, None, line.width / 2.);
                self.add_point(&mut path, p1_1);
                self.add_point(&mut path, p2_1);
                self.add_point(&mut path, p2_2);
                self.add_point(&mut path, p1_2);
                if let Some(path) = path {
                    window.paint_path(path, line.color);
                }
            }
        }
    }
    fn offset_points(
        &mut self,
        prev: Option<Point<Pixels>>,
        curr: Point<Pixels>,
        next: Option<Point<Pixels>>,
        offset: f32,
    ) -> (Point<Pixels>, Point<Pixels>) {
        let mut bisector_angle = 0.0;
        let mut count = 0;

        if let Some(point) = prev {
            bisector_angle -= f32::from(curr.y - point.y).atan2((curr.x - point.x).into());
            count += 1;
        }
        if let Some(point) = next {
            bisector_angle -= f32::from(point.y - curr.y).atan2((point.x - curr.x).into());
            count += 1;
        }

        bisector_angle /= count as f32;

        let offset_x = (offset * bisector_angle.sin()).into();
        let offset_y = (offset * bisector_angle.cos()).into();
        let p1 = Point::new(curr.x + offset_x, curr.y + offset_y);
        let p2 = Point::new(curr.x - offset_x, curr.y - offset_y);

        (p1, p2)
    }

    fn add_point(&mut self, path: &mut Option<Path<Pixels>>, point: Point<Pixels>) {
        if let Some(path) = path {
            path.line_to(point);
        } else {
            *path = Some(Path::new(point));
        }
    }

    /// 绘制线段
    fn draw_line_segments(&mut self, bounds: &Bounds<Pixels>, window: &mut Window, line: LineData) {
        // 线段最少需要两个点
        if line.point.len() <= 1 {
            return;
        }

        let points = line
            .point
            .iter()
            .map(|(x, y)| {
                Point::new(
                    bounds.origin.x + Pixels(*x as f32),
                    bounds.origin.y + Pixels(*y as f32),
                )
            })
            .collect::<Vec<_>>();

        // 如果设置了虚线，使用dash_line函数绘制虚线
        if let Some(dash_array) = line.dash_array {
            // 逐段绘制虚线
            for i in 0..points.len() - 1 {
                if let Some(path) = crate::shape::dash_line(
                    points[i], 
                    points[i + 1], 
                    dash_array, 
                    line.width as f32
                ) {
                    window.paint_path(path, line.color);
                }
            }
        } else {
            // 使用原始方法绘制实线
        for (idx, point) in points.iter().enumerate() {
            self.paint_line(
                window,
                PaintLine {
                    color: line.color,
                    width: line.width as f32,
                    p1: Some(*point),
                    p2: points.get(idx + 1).cloned(),
                },
            );
            }
        }
    }
    /// Paint the Line.
    pub fn paint(&mut self, bounds: &Bounds<Pixels>, window: &mut Window) {
        for line in self.data.clone() {
            self.draw_line_segments(bounds, window, line);
        }
    }
}
