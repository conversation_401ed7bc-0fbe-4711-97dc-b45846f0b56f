//! 完整的 SVG 案例生成器
//!
//! 生成所有类型的完整真实图表案例

use std::fs;

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🎨 完整的 SVG 案例生成器");
    println!("{}", "=".repeat(60));

    // 确保输出目录存在
    let output_dir = "temp/svg/complete_cases";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 基础图表类型
    println!("\n📊 1. 生成基础图表类型...");
    generate_basic_charts(output_dir)?;

    // 2. 数据处理案例
    println!("\n📈 2. 生成数据处理案例...");
    generate_data_processing_cases(output_dir)?;

    // 3. 样式和主题案例
    println!("\n🎨 3. 生成样式和主题案例...");
    generate_style_theme_cases(output_dir)?;

    // 4. 复杂场景案例
    println!("\n🔧 4. 生成复杂场景案例...");
    generate_complex_cases(output_dir)?;

    // 5. 性能测试案例
    println!("\n⚡ 5. 生成性能测试案例...");
    generate_performance_cases(output_dir)?;

    // 6. 交互动画案例
    println!("\n🎬 6. 生成交互动画案例...");
    generate_interactive_cases(output_dir)?;

    // 7. 生成完整展示页面
    println!("\n📄 7. 生成完整展示页面...");
    generate_complete_showcase_html(output_dir)?;

    println!("\n🎉 完整 SVG 案例生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/complete_showcase.html 查看所有完整案例", output_dir);

    Ok(())
}

/// 生成基础图表类型
fn generate_basic_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 完整折线图
    let line_data = vec![
        (0.0, 120.0), (1.0, 132.0), (2.0, 101.0), (3.0, 134.0), 
        (4.0, 90.0), (5.0, 230.0), (6.0, 210.0), (7.0, 180.0)
    ];
    let line_svg = generate_complete_line_chart("完整折线图 - 销售趋势", &line_data, 600.0, 400.0);
    fs::write(format!("{}/01_complete_line.svg", output_dir), line_svg)?;

    // 2. 多系列折线图
    let multi_line_svg = generate_multi_line_chart("多系列折线图 - 产品对比", 700.0, 450.0);
    fs::write(format!("{}/02_multi_line.svg", output_dir), multi_line_svg)?;

    // 3. 完整柱状图
    let bar_data = vec![
        ("Q1", 85.0), ("Q2", 120.0), ("Q3", 95.0), ("Q4", 140.0), 
        ("Q5", 110.0), ("Q6", 160.0)
    ];
    let bar_svg = generate_complete_bar_chart("完整柱状图 - 季度业绩", &bar_data, 600.0, 400.0);
    fs::write(format!("{}/03_complete_bar.svg", output_dir), bar_svg)?;

    // 4. 堆叠柱状图
    let stacked_bar_svg = generate_stacked_bar_chart("堆叠柱状图 - 分类统计", 700.0, 450.0);
    fs::write(format!("{}/04_stacked_bar.svg", output_dir), stacked_bar_svg)?;

    // 5. 完整散点图
    let scatter_data: Vec<(f64, f64)> = (0..50)
        .map(|i| {
            let x = i as f64 * 0.2;
            let y = (x * 2.0).sin() * 50.0 + 100.0 + (i as f64 * 0.1).cos() * 20.0;
            (x, y)
        })
        .collect();
    let scatter_svg = generate_complete_scatter_chart("完整散点图 - 数据分布", &scatter_data, 600.0, 400.0);
    fs::write(format!("{}/05_complete_scatter.svg", output_dir), scatter_svg)?;

    // 6. 气泡图
    let bubble_svg = generate_bubble_chart("气泡图 - 三维数据", 600.0, 450.0);
    fs::write(format!("{}/06_bubble_chart.svg", output_dir), bubble_svg)?;

    // 7. 完整饼图
    let pie_data = vec![
        ("直接访问", 335.0), ("邮件营销", 310.0), ("联盟广告", 234.0), 
        ("视频广告", 135.0), ("搜索引擎", 548.0)
    ];
    let pie_svg = generate_complete_pie_chart("完整饼图 - 访问来源", &pie_data, 500.0, 500.0);
    fs::write(format!("{}/07_complete_pie.svg", output_dir), pie_svg)?;

    // 8. 环形图
    let donut_data = vec![
        ("移动端", 60.0), ("桌面端", 30.0), ("平板端", 10.0)
    ];
    let donut_svg = generate_donut_chart("环形图 - 设备分布", &donut_data, 500.0, 500.0);
    fs::write(format!("{}/08_donut_chart.svg", output_dir), donut_svg)?;

    println!("  ✅ 基础图表类型生成完成 (8个完整图表)");
    Ok(())
}

/// 生成数据处理案例
fn generate_data_processing_cases(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 大数据量折线图
    let large_data: Vec<(f64, f64)> = (0..200)
        .map(|i| {
            let x = i as f64 * 0.1;
            let y = (x * 0.5).sin() * 50.0 + 100.0 + (x * 0.1).cos() * 20.0;
            (x, y)
        })
        .collect();
    let large_svg = generate_complete_line_chart("大数据量处理 - 200个数据点", &large_data, 800.0, 500.0);
    fs::write(format!("{}/09_large_data.svg", output_dir), large_svg)?;

    // 2. 高精度数据
    let precision_data: Vec<(f64, f64)> = (0..100)
        .map(|i| {
            let x = i as f64 * 0.01;
            let y = (x * std::f64::consts::PI * 100.0).sin() * 0.5 + 50.0;
            (x, y)
        })
        .collect();
    let precision_svg = generate_complete_line_chart("高精度数据处理", &precision_data, 700.0, 400.0);
    fs::write(format!("{}/10_precision_data.svg", output_dir), precision_svg)?;

    // 3. 稀疏数据散点图
    let sparse_data = vec![
        (0.0, 10.0), (5.0, 25.0), (15.0, 8.0), (30.0, 45.0), 
        (50.0, 12.0), (80.0, 38.0), (100.0, 22.0)
    ];
    let sparse_svg = generate_complete_scatter_chart("稀疏数据处理", &sparse_data, 600.0, 400.0);
    fs::write(format!("{}/11_sparse_data.svg", output_dir), sparse_svg)?;

    // 4. 负值数据柱状图
    let negative_data = vec![
        ("A", 20.0), ("B", -15.0), ("C", 30.0), ("D", -25.0), 
        ("E", 10.0), ("F", -5.0), ("G", 35.0), ("H", -20.0)
    ];
    let negative_svg = generate_negative_bar_chart("负值数据处理", &negative_data, 600.0, 400.0);
    fs::write(format!("{}/12_negative_data.svg", output_dir), negative_svg)?;

    println!("  ✅ 数据处理案例生成完成 (4个完整图表)");
    Ok(())
}

/// 生成样式和主题案例
fn generate_style_theme_cases(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 深色主题
    let dark_svg = generate_dark_theme_chart("深色主题图表", 600.0, 400.0);
    fs::write(format!("{}/13_dark_theme.svg", output_dir), dark_svg)?;

    // 2. 渐变效果
    let gradient_svg = generate_gradient_chart("渐变效果图表", 600.0, 400.0);
    fs::write(format!("{}/14_gradient_chart.svg", output_dir), gradient_svg)?;

    // 3. 透明度效果
    let transparency_svg = generate_transparency_chart("透明度效果图表", 600.0, 400.0);
    fs::write(format!("{}/15_transparency_chart.svg", output_dir), transparency_svg)?;

    println!("  ✅ 样式和主题案例生成完成 (3个完整图表)");
    Ok(())
}

/// 生成复杂场景案例
fn generate_complex_cases(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 混合图表
    let mixed_svg = generate_mixed_chart("混合图表 - 柱状+折线", 800.0, 500.0);
    fs::write(format!("{}/16_mixed_chart.svg", output_dir), mixed_svg)?;

    // 2. 多轴图表
    let multi_axis_svg = generate_multi_axis_chart("多轴图表 - 温度+湿度", 800.0, 500.0);
    fs::write(format!("{}/17_multi_axis.svg", output_dir), multi_axis_svg)?;

    // 3. 仪表盘
    let dashboard_svg = generate_dashboard_chart("仪表盘样式", 600.0, 600.0);
    fs::write(format!("{}/18_dashboard.svg", output_dir), dashboard_svg)?;

    println!("  ✅ 复杂场景案例生成完成 (3个完整图表)");
    Ok(())
}

/// 生成性能测试案例
fn generate_performance_cases(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 超大数据集
    let huge_data: Vec<(f64, f64)> = (0..1000)
        .map(|i| {
            let x = i as f64 * 0.01;
            let y = (x * 2.0).sin() * 50.0 + 100.0 + (x * 0.5).cos() * 30.0;
            (x, y)
        })
        .collect();
    let huge_svg = generate_complete_line_chart("超大数据集 - 1000个数据点", &huge_data, 1000.0, 600.0);
    fs::write(format!("{}/19_huge_dataset.svg", output_dir), huge_svg)?;

    // 2. 极值数据
    let extreme_data = vec![
        (0.0, 0.000001), (1.0, 1000000.0), (2.0, -500000.0), 
        (3.0, 0.0), (4.0, 999999.9)
    ];
    let extreme_svg = generate_complete_line_chart("极值数据处理", &extreme_data, 700.0, 400.0);
    fs::write(format!("{}/20_extreme_values.svg", output_dir), extreme_svg)?;

    // 3. 多系列压力测试
    let stress_svg = generate_multi_series_stress_test("多系列压力测试 - 20个系列", 1200.0, 800.0);
    fs::write(format!("{}/21_stress_test.svg", output_dir), stress_svg)?;

    println!("  ✅ 性能测试案例生成完成 (3个完整图表)");
    Ok(())
}

/// 生成交互动画案例
fn generate_interactive_cases(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 动画折线图
    let animated_svg = generate_animated_line_chart("动画折线图", 700.0, 450.0);
    fs::write(format!("{}/22_animated_line.svg", output_dir), animated_svg)?;

    // 2. 交互式散点图
    let interactive_svg = generate_interactive_scatter_chart("交互式散点图", 600.0, 500.0);
    fs::write(format!("{}/23_interactive_scatter.svg", output_dir), interactive_svg)?;

    // 3. 动态饼图
    let dynamic_svg = generate_dynamic_pie_chart("动态饼图", 500.0, 500.0);
    fs::write(format!("{}/24_dynamic_pie.svg", output_dir), dynamic_svg)?;

    println!("  ✅ 交互动画案例生成完成 (3个完整图表)");
    Ok(())
}

// ============================================================================
// 完整图表生成函数
// ============================================================================

/// 生成完整的折线图
fn generate_complete_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();

    // SVG 头部
    svg.push_str(&format!(
        "<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n",
        width, height
    ));

    // 定义渐变
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"lineGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#007bff;stop-opacity:0.3\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#007bff;stop-opacity:0.1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");

    // 标题
    svg.push_str(&format!(
        "  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
        title
    ));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;

    // 坐标轴
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height
    ));
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y, chart_x, chart_y + chart_height
    ));

    if !data.is_empty() {
        // 计算数据范围
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);

        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };

        // 添加网格线
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            svg.push_str(&format!(
                "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
                x, chart_y, x, chart_y + chart_height
            ));
        }

        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!(
                "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
                chart_x, y, chart_x + chart_width, y
            ));
        }

        // 生成路径
        let mut path = String::from("M");
        let mut area_path = String::from("M");

        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;

            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
                area_path.push_str(&format!(" {} {}", px, chart_y + chart_height));
                area_path.push_str(&format!(" L {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
                area_path.push_str(&format!(" L {} {}", px, py));
            }
        }

        // 完成面积路径
        if let Some((last_x, _)) = data.last() {
            let last_px = chart_x + (last_x - min_x) / x_range * chart_width;
            area_path.push_str(&format!(" L {} {} Z", last_px, chart_y + chart_height));
        }

        // 绘制面积
        svg.push_str(&format!(
            "  <path d=\"{}\" fill=\"url(#lineGradient)\"/>\n",
            area_path
        ));

        // 绘制折线
        svg.push_str(&format!(
            "  <path d=\"{}\" stroke=\"#007bff\" stroke-width=\"3\" fill=\"none\"/>\n",
            path
        ));

        // 绘制数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;

            svg.push_str(&format!(
                "  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#007bff\" stroke=\"white\" stroke-width=\"2\"/>\n",
                px, py
            ));
        }

        // 添加坐标轴标签
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            let value = min_x + (i as f64 / 5.0) * x_range;
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{:.1}</text>\n",
                x, chart_y + chart_height + 20.0, value
            ));
        }

        for i in 0..=5 {
            let y = chart_y + chart_height - (i as f64 / 5.0) * chart_height;
            let value = min_y + (i as f64 / 5.0) * y_range;
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n",
                chart_x - 10.0, y + 4.0, value
            ));
        }
    }

    svg.push_str("</svg>");
    svg
}

/// 生成完整的柱状图
fn generate_complete_bar_chart(title: &str, data: &[(&str, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();

    // SVG 头部
    svg.push_str(&format!(
        "<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n",
        width, height
    ));

    // 定义渐变
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"barGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#28a745;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#1e7e34;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");

    // 标题
    svg.push_str(&format!(
        "  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
        title
    ));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;

    // 坐标轴
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height
    ));
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y, chart_x, chart_y + chart_height
    ));

    if !data.is_empty() {
        let max_value = data.iter().map(|(_, v)| *v).fold(0.0, f64::max);
        let bar_width = chart_width / data.len() as f64 * 0.8;
        let bar_spacing = chart_width / data.len() as f64;

        // 添加网格线
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!(
                "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
                chart_x, y, chart_x + chart_width, y
            ));
        }

        // 绘制柱子
        for (i, (label, value)) in data.iter().enumerate() {
            let x = chart_x + i as f64 * bar_spacing + bar_spacing * 0.1;
            let bar_height = (value / max_value) * chart_height;
            let y = chart_y + chart_height - bar_height;

            // 柱子
            svg.push_str(&format!(
                "  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"url(#barGradient)\" stroke=\"#1e7e34\" stroke-width=\"1\" rx=\"3\"/>\n",
                x, y, bar_width, bar_height
            ));

            // 数值标签
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" font-weight=\"bold\" fill=\"#333\">{:.0}</text>\n",
                x + bar_width / 2.0, y - 8.0, value
            ));

            // X轴标签
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{}</text>\n",
                x + bar_width / 2.0, chart_y + chart_height + 20.0, label
            ));
        }

        // Y轴标签
        for i in 0..=5 {
            let y = chart_y + chart_height - (i as f64 / 5.0) * chart_height;
            let value = (i as f64 / 5.0) * max_value;
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n",
                chart_x - 10.0, y + 4.0, value
            ));
        }
    }

    svg.push_str("</svg>");
    svg
}

/// 生成完整的散点图
fn generate_complete_scatter_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();

    // SVG 头部
    svg.push_str(&format!(
        "<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n",
        width, height
    ));

    // 定义滤镜
    svg.push_str("  <defs>\n");
    svg.push_str("    <filter id=\"shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n");
    svg.push_str("      <feDropShadow dx=\"2\" dy=\"2\" stdDeviation=\"2\" flood-color=\"#000000\" flood-opacity=\"0.3\"/>\n");
    svg.push_str("    </filter>\n");
    svg.push_str("  </defs>\n");

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");

    // 标题
    svg.push_str(&format!(
        "  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
        title
    ));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;

    // 坐标轴
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height
    ));
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y, chart_x, chart_y + chart_height
    ));

    if !data.is_empty() {
        // 计算数据范围
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);

        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };

        // 添加网格线
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            svg.push_str(&format!(
                "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
                x, chart_y, x, chart_y + chart_height
            ));
        }

        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!(
                "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
                chart_x, y, chart_x + chart_width, y
            ));
        }

        // 绘制散点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;

            svg.push_str(&format!(
                "  <circle cx=\"{}\" cy=\"{}\" r=\"5\" fill=\"#dc3545\" fill-opacity=\"0.8\" stroke=\"white\" stroke-width=\"1\" filter=\"url(#shadow)\"/>\n",
                px, py
            ));
        }

        // 添加坐标轴标签
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            let value = min_x + (i as f64 / 5.0) * x_range;
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{:.1}</text>\n",
                x, chart_y + chart_height + 20.0, value
            ));
        }

        for i in 0..=5 {
            let y = chart_y + chart_height - (i as f64 / 5.0) * chart_height;
            let value = min_y + (i as f64 / 5.0) * y_range;
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n",
                chart_x - 10.0, y + 4.0, value
            ));
        }
    }

    svg.push_str("</svg>");
    svg
}

/// 生成完整的饼图
fn generate_complete_pie_chart(title: &str, data: &[(&str, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();

    // SVG 头部
    svg.push_str(&format!(
        "<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n",
        width, height
    ));

    // 定义滤镜
    svg.push_str("  <defs>\n");
    svg.push_str("    <filter id=\"pieShadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n");
    svg.push_str("      <feDropShadow dx=\"3\" dy=\"3\" stdDeviation=\"3\" flood-color=\"#000000\" flood-opacity=\"0.3\"/>\n");
    svg.push_str("    </filter>\n");
    svg.push_str("  </defs>\n");

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");

    // 标题
    svg.push_str(&format!(
        "  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
        title
    ));

    if !data.is_empty() {
        let center_x = width / 2.0;
        let center_y = height / 2.0 + 20.0;
        let radius = (width.min(height) - 120.0) / 2.5;

        let total: f64 = data.iter().map(|(_, v)| *v).sum();
        let mut current_angle = -std::f64::consts::PI / 2.0; // 从顶部开始

        let colors = [
            "#007bff", "#28a745", "#dc3545", "#ffc107", "#17a2b8",
            "#6f42c1", "#e83e8c", "#fd7e14", "#20c997", "#6c757d"
        ];

        for (i, (label, value)) in data.iter().enumerate() {
            let angle = (value / total) * 2.0 * std::f64::consts::PI;
            let end_angle = current_angle + angle;

            let x1 = center_x + radius * current_angle.cos();
            let y1 = center_y + radius * current_angle.sin();
            let x2 = center_x + radius * end_angle.cos();
            let y2 = center_y + radius * end_angle.sin();

            let large_arc = if angle > std::f64::consts::PI { 1 } else { 0 };

            let color = colors[i % colors.len()];

            // 绘制扇形
            svg.push_str(&format!(
                "  <path d=\"M {} {} L {} {} A {} {} 0 {} 1 {} {} Z\" fill=\"{}\" stroke=\"white\" stroke-width=\"3\" filter=\"url(#pieShadow)\"/>\n",
                center_x, center_y, x1, y1, radius, radius, large_arc, x2, y2, color
            ));

            // 添加标签
            let label_angle = current_angle + angle / 2.0;
            let label_radius = radius + 40.0;
            let label_x = center_x + label_radius * label_angle.cos();
            let label_y = center_y + label_radius * label_angle.sin();

            // 连接线
            let line_start_x = center_x + (radius + 5.0) * label_angle.cos();
            let line_start_y = center_y + (radius + 5.0) * label_angle.sin();
            let line_mid_x = center_x + (radius + 25.0) * label_angle.cos();
            let line_mid_y = center_y + (radius + 25.0) * label_angle.sin();

            svg.push_str(&format!(
                "  <polyline points=\"{},{} {},{} {},{}\" stroke=\"#666\" stroke-width=\"1\" fill=\"none\"/>\n",
                line_start_x, line_start_y, line_mid_x, line_mid_y, label_x, label_y
            ));

            // 标签文本
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
                label_x, label_y - 5.0, label
            ));

            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"10\" fill=\"#666\">{:.1}%</text>\n",
                label_x, label_y + 10.0, (value / total) * 100.0
            ));

            current_angle = end_angle;
        }

        // 添加图例
        let legend_x = 20.0;
        let legend_y = height - 100.0;

        svg.push_str(&format!(
            "  <text x=\"{}\" y=\"{}\" font-size=\"14\" font-weight=\"bold\" fill=\"#333\">图例</text>\n",
            legend_x, legend_y - 10.0
        ));

        for (i, (label, value)) in data.iter().enumerate() {
            let y = legend_y + i as f64 * 20.0;
            let color = colors[i % colors.len()];

            svg.push_str(&format!(
                "  <rect x=\"{}\" y=\"{}\" width=\"15\" height=\"15\" fill=\"{}\"/>\n",
                legend_x, y - 10.0, color
            ));

            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">{}: {:.0}</text>\n",
                legend_x + 20.0, y, label, value
            ));
        }
    }

    svg.push_str("</svg>");
    svg
}
