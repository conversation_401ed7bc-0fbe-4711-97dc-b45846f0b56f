# ECharts 适配 GPUI Component 建议与实施方案

## 适配目标

将 Apache ECharts 成功适配到 GPUI Component 框架，创建高性能、原生体验的桌面数据可视化解决方案。

## 核心适配策略

### 1. 架构适配

#### 从 Web 到桌面的转换
```
Web 架构                    →    桌面架构
─────────────────────────────────────────────────────
DOM + Canvas                →    GPUI Elements
JavaScript Event System     →    Rust Event System
CSS Styling                 →    GPUI Styling
Browser APIs               →    Native Desktop APIs
Web Workers                →    Rust Async/Threading
```

#### 渲染引擎适配
```rust
// ECharts 原有渲染抽象
trait Renderer {
    fn draw_rect(&self, x: f32, y: f32, width: f32, height: f32);
    fn draw_circle(&self, x: f32, y: f32, radius: f32);
    fn draw_text(&self, text: &str, x: f32, y: f32);
}

// GPUI 适配实现
struct GPUIRenderer<'a> {
    paint_context: &'a mut PaintContext,
    bounds: Bounds<Pixels>,
}

impl<'a> Renderer for GPUIRenderer<'a> {
    fn draw_rect(&self, x: f32, y: f32, width: f32, height: f32) {
        let rect = Bounds::new(
            point(px(x), px(y)),
            size(px(width), px(height))
        );
        self.paint_context.paint_quad(quad(rect, self.current_color()));
    }
    
    fn draw_circle(&self, x: f32, y: f32, radius: f32) {
        let center = point(px(x), px(y));
        self.paint_context.paint_circle(center, px(radius), self.current_color());
    }
    
    fn draw_text(&self, text: &str, x: f32, y: f32) {
        let position = point(px(x), px(y));
        self.paint_context.paint_text(text, position, &self.current_text_style());
    }
}
```

### 2. 数据模型适配

#### 类型安全的数据绑定
```rust
// ECharts 原有数据结构（JavaScript）
// {
//   xAxis: { type: 'category', data: ['A', 'B', 'C'] },
//   yAxis: { type: 'value' },
//   series: [{ type: 'bar', data: [1, 2, 3] }]
// }

// GPUI 适配的类型安全版本
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChartOption {
    pub x_axis: AxisOption,
    pub y_axis: AxisOption,
    pub series: Vec<SeriesOption>,
    pub title: Option<TitleOption>,
    pub legend: Option<LegendOption>,
    pub tooltip: Option<TooltipOption>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AxisOption {
    Category { data: Vec<String> },
    Value { min: Option<f64>, max: Option<f64> },
    Time { min: Option<DateTime<Utc>>, max: Option<DateTime<Utc>> },
    Log { base: f64 },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SeriesOption {
    Bar(BarSeriesOption),
    Line(LineSeriesOption),
    Pie(PieSeriesOption),
    Scatter(ScatterSeriesOption),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BarSeriesOption {
    pub name: Option<String>,
    pub data: Vec<f64>,
    pub color: Option<Hsla>,
    pub stack: Option<String>,
    pub bar_width: Option<Pixels>,
}
```

### 3. 事件系统适配

#### 从 DOM 事件到 GPUI 事件
```rust
// ECharts 事件适配器
pub struct EChartsEventAdapter {
    chart: EChartsInstance,
    event_handlers: HashMap<String, Vec<Box<dyn Fn(EChartsEvent)>>>,
}

impl EChartsEventAdapter {
    pub fn handle_mouse_event(&mut self, event: &MouseEvent, bounds: Bounds<Pixels>) -> bool {
        let local_point = event.position - bounds.origin;
        
        // 转换为 ECharts 坐标系
        let echarts_event = EChartsEvent::Mouse {
            event_type: match event.button {
                MouseButton::Left => "click",
                MouseButton::Right => "contextmenu",
                _ => return false,
            },
            x: local_point.x.0,
            y: local_point.y.0,
            data_index: self.chart.get_data_index_at_point(local_point),
        };
        
        // 触发相应的事件处理器
        if let Some(handlers) = self.event_handlers.get("click") {
            for handler in handlers {
                handler(echarts_event.clone());
            }
        }
        
        true
    }
    
    pub fn on<F>(&mut self, event_type: &str, handler: F)
    where F: Fn(EChartsEvent) + 'static {
        self.event_handlers
            .entry(event_type.to_string())
            .or_default()
            .push(Box::new(handler));
    }
}
```

### 4. 主题系统适配

#### 统一的主题管理
```rust
pub struct EChartsThemeAdapter;

impl EChartsThemeAdapter {
    pub fn convert_gpui_theme_to_echarts(gpui_theme: &Theme) -> EChartsTheme {
        EChartsTheme {
            background_color: gpui_theme.background,
            text_style: TextStyle {
                color: gpui_theme.foreground,
                font_family: gpui_theme.font_family.clone(),
                font_size: gpui_theme.font_size.0 as u32,
                font_weight: "normal".to_string(),
            },
            color_palette: vec![
                gpui_theme.chart_1,
                gpui_theme.chart_2,
                gpui_theme.chart_3,
                gpui_theme.chart_4,
                gpui_theme.chart_5,
            ],
            axis: AxisTheme {
                axis_line: LineStyle {
                    color: gpui_theme.border,
                    width: 1.0,
                    type_: "solid".to_string(),
                },
                axis_label: TextStyle {
                    color: gpui_theme.muted_foreground,
                    font_family: gpui_theme.font_family.clone(),
                    font_size: (gpui_theme.font_size.0 * 0.9) as u32,
                    font_weight: "normal".to_string(),
                },
                split_line: LineStyle {
                    color: gpui_theme.border.with_alpha(0.3),
                    width: 1.0,
                    type_: "dashed".to_string(),
                },
            },
            legend: LegendTheme {
                text_style: TextStyle {
                    color: gpui_theme.foreground,
                    font_family: gpui_theme.font_family.clone(),
                    font_size: gpui_theme.font_size.0 as u32,
                    font_weight: "normal".to_string(),
                },
            },
            tooltip: TooltipTheme {
                background_color: gpui_theme.popover,
                border_color: gpui_theme.border,
                text_style: TextStyle {
                    color: gpui_theme.popover_foreground,
                    font_family: gpui_theme.font_family.clone(),
                    font_size: gpui_theme.font_size.0 as u32,
                    font_weight: "normal".to_string(),
                },
            },
        }
    }
    
    pub fn apply_theme_to_option(option: &mut ChartOption, theme: &EChartsTheme) {
        // 应用主题到图表配置
        if option.title.is_none() {
            option.title = Some(TitleOption::default());
        }
        
        if let Some(title) = &mut option.title {
            title.text_style.color = theme.text_style.color;
            title.text_style.font_family = theme.text_style.font_family.clone();
        }
        
        // 应用颜色调色板到系列
        for (i, series) in option.series.iter_mut().enumerate() {
            if let Some(color) = theme.color_palette.get(i % theme.color_palette.len()) {
                match series {
                    SeriesOption::Bar(ref mut bar) => bar.color = Some(*color),
                    SeriesOption::Line(ref mut line) => line.color = Some(*color),
                    SeriesOption::Pie(ref mut pie) => pie.color = Some(*color),
                    SeriesOption::Scatter(ref mut scatter) => scatter.color = Some(*color),
                }
            }
        }
    }
}
```

## 实施方案

### 阶段一：基础适配 (4-6周)

#### 1. 核心组件开发
```rust
// 1.1 基础图表容器
pub struct EChartsContainer {
    option: ChartOption,
    theme: EChartsTheme,
    size: Size<Pixels>,
    renderer: Box<dyn ChartRenderer>,
}

// 1.2 渲染器实现
pub trait ChartRenderer {
    fn render(&mut self, option: &ChartOption, bounds: Bounds<Pixels>, cx: &mut PaintContext);
    fn handle_interaction(&mut self, event: &InteractionEvent) -> bool;
}

// 1.3 基础图表类型
impl ChartRenderer for BarChartRenderer { /* ... */ }
impl ChartRenderer for LineChartRenderer { /* ... */ }
impl ChartRenderer for PieChartRenderer { /* ... */ }
```

#### 1.4 集成到 GPUI Component
```rust
impl RenderOnce for EChartsContainer {
    fn render(self, cx: &mut WindowContext) -> impl IntoElement {
        div()
            .size(self.size)
            .child(
                canvas(move |bounds, cx| {
                    let mut renderer = self.renderer;
                    renderer.render(&self.option, bounds, cx);
                })
            )
            .on_mouse_down(MouseButton::Left, |event, cx| {
                // 处理鼠标事件
            })
    }
}
```

### 阶段二：功能扩展 (6-8周)

#### 2.1 高级图表类型
- 散点图 (Scatter)
- 雷达图 (Radar)  
- 热力图 (Heatmap)
- 桑基图 (Sankey)
- 树图 (Tree/Treemap)

#### 2.2 交互功能
```rust
pub struct InteractiveChart {
    chart: EChartsContainer,
    zoom_enabled: bool,
    brush_enabled: bool,
    tooltip_enabled: bool,
}

impl InteractiveChart {
    pub fn enable_zoom(mut self) -> Self {
        self.zoom_enabled = true;
        self
    }
    
    pub fn enable_brush_selection(mut self) -> Self {
        self.brush_enabled = true;
        self
    }
    
    pub fn with_tooltip<F>(mut self, formatter: F) -> Self 
    where F: Fn(&DataPoint) -> String + 'static {
        self.tooltip_enabled = true;
        // 设置 tooltip 格式化器
        self
    }
}
```

#### 2.3 动画系统
```rust
pub struct ChartAnimation {
    duration: Duration,
    easing: EasingFunction,
    delay: Duration,
}

pub enum EasingFunction {
    Linear,
    EaseIn,
    EaseOut,
    EaseInOut,
    Bounce,
    Elastic,
}

impl EChartsContainer {
    pub fn with_animation(mut self, animation: ChartAnimation) -> Self {
        self.animation = Some(animation);
        self
    }
    
    pub fn animate_to_option(&mut self, new_option: ChartOption, cx: &mut WindowContext) {
        if let Some(animation) = &self.animation {
            // 创建补间动画
            let tween = Tween::new(
                self.option.clone(),
                new_option,
                animation.duration,
                animation.easing,
            );
            
            cx.spawn(|mut cx| async move {
                // 执行动画
                tween.animate(&mut cx).await;
            }).detach();
        } else {
            self.option = new_option;
        }
    }
}
```

### 阶段三：性能优化 (4-6周)

#### 3.1 渲染优化
```rust
pub struct ChartRenderCache {
    geometry_cache: HashMap<u64, Vec<Vertex>>,
    texture_cache: HashMap<u64, Texture>,
    last_option_hash: Option<u64>,
}

impl ChartRenderCache {
    pub fn get_or_render<F>(&mut self, option: &ChartOption, render_fn: F) -> &Vec<Vertex>
    where F: FnOnce() -> Vec<Vertex> {
        let hash = self.calculate_option_hash(option);
        self.geometry_cache.entry(hash).or_insert_with(render_fn)
    }
}
```

#### 3.2 大数据处理
```rust
pub struct DataProcessor {
    sampler: Box<dyn DataSampler>,
    aggregator: Box<dyn DataAggregator>,
}

pub trait DataSampler {
    fn sample(&self, data: &[DataPoint], target_count: usize) -> Vec<DataPoint>;
}

pub struct LTTBSampler; // Largest-Triangle-Three-Buckets

impl DataSampler for LTTBSampler {
    fn sample(&self, data: &[DataPoint], target_count: usize) -> Vec<DataPoint> {
        if data.len() <= target_count {
            return data.to_vec();
        }
        
        // LTTB 算法实现
        // 保持数据的视觉特征，减少数据点数量
        todo!()
    }
}
```

### 阶段四：生态集成 (2-4周)

#### 4.1 数据源集成
```rust
pub trait DataSource {
    type Item;
    fn fetch(&self) -> Result<Vec<Self::Item>, DataError>;
    fn subscribe(&self, callback: Box<dyn Fn(Vec<Self::Item>)>);
}

pub struct CSVDataSource {
    file_path: PathBuf,
}

pub struct DatabaseDataSource {
    connection: DatabaseConnection,
    query: String,
}

pub struct APIDataSource {
    endpoint: Url,
    headers: HashMap<String, String>,
}
```

#### 4.2 导出功能
```rust
pub struct ChartExporter;

impl ChartExporter {
    pub fn export_as_png(&self, chart: &EChartsContainer, path: &Path) -> Result<(), ExportError> {
        let image = self.render_to_image(chart)?;
        image.save_png(path)?;
        Ok(())
    }
    
    pub fn export_as_svg(&self, chart: &EChartsContainer, path: &Path) -> Result<(), ExportError> {
        let svg = self.render_to_svg(chart)?;
        std::fs::write(path, svg)?;
        Ok(())
    }
    
    pub fn export_as_pdf(&self, chart: &EChartsContainer, path: &Path) -> Result<(), ExportError> {
        // PDF 导出实现
        todo!()
    }
}
```

## 质量保证

### 1. 测试策略
```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_bar_chart_rendering() {
        let option = ChartOption {
            series: vec![SeriesOption::Bar(BarSeriesOption {
                name: Some("Test".to_string()),
                data: vec![1.0, 2.0, 3.0],
                color: None,
                stack: None,
                bar_width: None,
            })],
            x_axis: AxisOption::Category { 
                data: vec!["A".to_string(), "B".to_string(), "C".to_string()] 
            },
            y_axis: AxisOption::Value { min: None, max: None },
            title: None,
            legend: None,
            tooltip: None,
        };
        
        let container = EChartsContainer::new(option);
        // 测试渲染逻辑
    }
    
    #[test]
    fn test_theme_conversion() {
        let gpui_theme = Theme::default();
        let echarts_theme = EChartsThemeAdapter::convert_gpui_theme_to_echarts(&gpui_theme);
        
        assert_eq!(echarts_theme.background_color, gpui_theme.background);
        assert_eq!(echarts_theme.text_style.color, gpui_theme.foreground);
    }
}
```

### 2. 性能基准测试
```rust
#[cfg(test)]
mod benchmarks {
    use criterion::{black_box, criterion_group, criterion_main, Criterion};
    
    fn bench_large_dataset_rendering(c: &mut Criterion) {
        let data: Vec<f64> = (0..100_000).map(|i| i as f64).collect();
        
        c.bench_function("render 100k points", |b| {
            b.iter(|| {
                let chart = create_line_chart(black_box(&data));
                chart.render_to_texture()
            })
        });
    }
    
    criterion_group!(benches, bench_large_dataset_rendering);
    criterion_main!(benches);
}
```

## 文档和示例

### 1. API 文档
```rust
/// 创建一个新的柱状图
/// 
/// # 示例
/// 
/// ```rust
/// use echarts_gpui::*;
/// 
/// let data = vec![1.0, 2.0, 3.0, 4.0, 5.0];
/// let chart = BarChart::new(data)
///     .x_labels(vec!["A", "B", "C", "D", "E"])
///     .title("示例柱状图")
///     .color(Color::Blue);
/// ```
pub struct BarChart {
    // ...
}
```

### 2. 使用示例
```rust
// examples/basic_charts.rs
fn main() {
    App::new().run(|cx| {
        cx.open_window(WindowOptions::default(), |cx| {
            // 创建示例数据
            let sales_data = vec![120.0, 200.0, 150.0, 80.0, 70.0, 110.0, 130.0];
            let months = vec!["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul"];
            
            // 创建柱状图
            let bar_chart = BarChart::new(sales_data)
                .x_labels(months)
                .title("月度销售额")
                .color(cx.theme().primary);
            
            // 创建折线图
            let line_data = vec![
                (1.0, 10.0), (2.0, 15.0), (3.0, 13.0), 
                (4.0, 17.0), (5.0, 20.0), (6.0, 18.0)
            ];
            let line_chart = LineChart::new(line_data)
                .title("趋势图")
                .smooth(true);
            
            // 布局
            v_flex()
                .gap_4()
                .child(bar_chart)
                .child(line_chart)
        });
    });
}
```

这个适配方案提供了从概念到实施的完整路径，确保 ECharts 能够成功集成到 GPUI Component 框架中，同时保持高性能和良好的开发体验。
