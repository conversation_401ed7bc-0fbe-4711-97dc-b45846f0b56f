//! 简单的类型转换测试
//!
//! 验证 gpui_renderer 中的类型转换功能

use echarts_core::{Bounds as EchartsBounds, Point as EchartsPoint, Size as EchartsSize};
use gpui::{Bounds as GpuiBounds, Point as GpuiPoint, Size as GpuiSize, Pixels, px, point, size};
use gpui_renderer::{ToGpui, ToECharts, convert};

fn main() {
    println!("🔄 简单的类型转换测试");
    println!("使用 gpui_renderer 中的转换功能");
    
    // 1. 测试 EchartsBounds -> gpui::Bounds 转换
    println!("\n📊 1. EchartsBounds -> gpui::Bounds 转换");
    let echarts_bounds = EchartsBounds::new(10.0, 20.0, 300.0, 200.0);
    println!("  原始 EchartsBounds: {:?}", echarts_bounds);
    
    let gpui_bounds: GpuiBounds<Pixels> = echarts_bounds.to_gpui();
    println!("  转换后 gpui::Bounds: {:?}", gpui_bounds);
    
    // 2. 测试 gpui::Bounds -> EchartsBounds 转换
    println!("\n📊 2. gpui::Bounds -> EchartsBounds 转换");
    let gpui_bounds = GpuiBounds::new(
        point(px(50.0), px(60.0)),
        size(px(400.0), px(300.0))
    );
    println!("  原始 gpui::Bounds: {:?}", gpui_bounds);
    
    let echarts_bounds: EchartsBounds = gpui_bounds.to_echarts();
    println!("  转换后 EchartsBounds: {:?}", echarts_bounds);
    
    // 3. 测试便捷转换函数
    println!("\n📊 3. 使用便捷转换函数");
    let echarts_bounds2 = convert::gpui_bounds_to_echarts(gpui_bounds);
    println!("  便捷函数转换结果: {:?}", echarts_bounds2);
    
    // 4. 测试 Point 转换
    println!("\n📍 4. Point 转换测试");
    let echarts_point = EchartsPoint::new(100.0, 150.0);
    let gpui_point: GpuiPoint<Pixels> = echarts_point.to_gpui();
    let echarts_point_back: EchartsPoint = gpui_point.to_echarts();
    println!("  Point 往返转换: {:?} -> {:?}", echarts_point, echarts_point_back);
    
    // 5. 测试 Size 转换
    println!("\n📏 5. Size 转换测试");
    let echarts_size = EchartsSize::new(800.0, 600.0);
    let gpui_size: GpuiSize<Pixels> = echarts_size.to_gpui();
    let echarts_size_back: EchartsSize = gpui_size.to_echarts();
    println!("  Size 往返转换: {:?} -> {:?}", echarts_size, echarts_size_back);
    
    println!("\n✅ 所有转换测试完成！");
}
