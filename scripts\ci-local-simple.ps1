# ECharts-rs 简化版本 CI/CD 检查脚本
param(
    [string]$Action = "check"
)

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Show-Help {
    Write-Host "Usage: .\ci-local-simple.ps1 [option]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  check     Run full CI checks (default)"
    Write-Host "  clean     Clean build files"
    Write-Host "  format    Format code"
    Write-Host "  help      Show this help"
}

function Run-Check {
    Write-Host "========================================"
    Write-Host "    ECharts-rs Local CI/CD Check"
    Write-Host "========================================"
    
    Write-Info "Checking code format..."
    cargo fmt --all -- --check
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Code format check failed"
        return $false
    }
    
    Write-Info "Running Clippy..."
    cargo clippy --all-targets --all-features -- -D warnings
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Clippy check failed"
        return $false
    }
    
    Write-Info "Building project..."
    cargo build --workspace --all-features
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Build failed"
        return $false
    }
    
    Write-Info "Running tests..."
    cargo test --workspace --all-features
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Tests failed"
        return $false
    }
    
    Write-Success "All checks passed!"
    return $true
}

switch ($Action.ToLower()) {
    "check" { 
        if (Run-Check) {
            exit 0
        } else {
            exit 1
        }
    }
    "clean" { 
        Write-Info "Cleaning build files..."
        cargo clean
        Write-Success "Clean completed"
    }
    "format" { 
        Write-Info "Formatting code..."
        cargo fmt --all
        Write-Success "Code formatting completed"
    }
    "help" { 
        Show-Help 
    }
    default { 
        Write-Host "Unknown option: $Action" -ForegroundColor Yellow
        Show-Help
        exit 1
    }
}
