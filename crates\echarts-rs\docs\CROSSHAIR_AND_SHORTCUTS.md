# 十字线光标和快捷键功能

echarts-rs 现在支持十字线光标和快捷键功能，提供更好的交互体验。

## 🎯 十字线光标功能

### 功能特性

- **实时跟踪**：十字线跟随鼠标移动，显示当前位置的 x、y 坐标
- **数据点捕捉**：自动捕捉最近的数据点并显示详细信息
- **可配置样式**：支持自定义十字线的颜色、宽度、透明度等
- **坐标显示**：实时显示当前光标位置的数据坐标值

### 使用方法

```rust
use echarts_core::{Crosshair, CrosshairConfig};

// 创建十字线配置
let mut crosshair_config = CrosshairConfig::default();
crosshair_config.enabled = true;
crosshair_config.show_horizontal = true;
crosshair_config.show_vertical = true;
crosshair_config.show_labels = true;

// 创建十字线实例
let crosshair = Crosshair::with_config(crosshair_config);
```

### 配置选项

- `enabled`: 是否启用十字线
- `show_horizontal`: 是否显示水平线
- `show_vertical`: 是否显示垂直线
- `show_labels`: 是否显示坐标标签
- `snap_to_data`: 是否自动捕捉数据点
- `snap_threshold`: 捕捉距离阈值

## ⌨️ 快捷键功能

### 默认快捷键

| 快捷键 | 功能 | 描述 |
|--------|------|------|
| `C` | 切换十字线 | 显示/隐藏十字线光标 |
| `H` | 显示帮助 | 显示快捷键帮助面板 |
| `R` | 重置缩放 | 重置图表缩放到默认状态 |
| `L` | 切换图例 | 显示/隐藏图例 |
| `G` | 切换网格 | 显示/隐藏网格线 |
| `←` / `→` | 切换数据集 | 在不同数据集之间切换 |
| `=` / `-` | 缩放 | 放大/缩小图表 |
| `Shift + 方向键` | 平移 | 平移图表视图 |
| `F5` | 刷新数据 | 刷新图表数据 |
| `Ctrl + S` | 导出图表 | 导出图表（功能待实现） |

### 自定义快捷键

```rust
use echarts_core::{KeyboardShortcuts, KeyCombination, ChartAction};

let mut shortcuts = KeyboardShortcuts::new();

// 添加自定义快捷键
shortcuts.add_shortcut(
    KeyCombination::key("x"),
    ChartAction::Custom("my_action".to_string())
);

// 添加组合键
shortcuts.add_shortcut(
    KeyCombination::ctrl_key("z"),
    ChartAction::ResetZoom
);
```

## 🖱️ 鼠标交互

### 支持的鼠标操作

- **鼠标移动**：十字线跟随鼠标位置
- **数据点高亮**：鼠标靠近数据点时自动高亮
- **坐标显示**：实时显示鼠标位置对应的数据坐标

### 交互区域

- 十字线只在图表绘制区域内显示
- 鼠标离开图表区域时十字线自动隐藏
- 支持多种图表类型（线图、柱图、散点图等）

## 📊 实时信息显示

### 坐标信息

当十字线激活时，会显示：
- **X 坐标**：当前位置的 X 轴数值
- **Y 坐标**：当前位置的 Y 轴数值

### 数据点信息

当鼠标靠近数据点时，会显示：
- **系列名称**：数据所属的系列名称
- **数据值**：具体的数据点数值
- **索引信息**：数据点在系列中的位置

## 🎨 样式自定义

### 十字线样式

```rust
use echarts_core::{CrosshairLineStyle, Color};

let mut line_style = CrosshairLineStyle::default();
line_style.color = Color::rgb(1.0, 0.0, 0.0); // 红色
line_style.width = 2.0; // 线宽
line_style.opacity = 0.8; // 透明度
line_style.dash = vec![10.0, 5.0]; // 虚线样式
```

### 标签样式

```rust
use echarts_core::{CrosshairLabelStyle, Color};

let mut label_style = CrosshairLabelStyle::default();
label_style.background_color = Color::rgba(0.0, 0.0, 0.0, 0.8);
label_style.text_color = Color::rgb(1.0, 1.0, 1.0);
label_style.font_size = 12.0;
label_style.padding = 4.0;
label_style.border_radius = 3.0;
```

## 🚀 示例代码

完整的使用示例请参考 `examples/simple_gpui_line_chart.rs`，其中包含了：

- 十字线的完整集成
- 快捷键处理逻辑
- 鼠标事件处理
- 实时信息显示
- 帮助面板实现

## 🔧 技术实现

### 核心组件

1. **Crosshair**: 十字线核心组件
2. **KeyboardShortcuts**: 快捷键管理系统
3. **EventDispatcher**: 事件分发器
4. **ChartAction**: 图表动作枚举

### 集成方式

- 通过 GPUI 的事件系统处理键盘和鼠标事件
- 使用 Canvas API 绘制十字线
- 实时计算坐标转换和数据点查找
- 响应式 UI 更新

这些功能大大增强了 echarts-rs 的交互性和用户体验！
