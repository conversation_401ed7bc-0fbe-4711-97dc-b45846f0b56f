//! SVG 渲染器简单测试
//!
//! 测试 SVG 渲染器的基本功能

use echarts_core::{
    Bounds, Color, DrawCommand, Point,
    draw_commands::{LineStyle, CircleStyle, RectStyle, LineCap, LineJoin},
};
use svg_renderer::{SvgRenderer, SvgRenderSettings};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎨 测试 SVG 渲染器");

    // 创建 SVG 渲染器
    let mut renderer = SvgRenderer::with_size(400.0, 300.0);

    // 创建一些测试绘制命令
    let commands = vec![
        // 绘制一条红色线条
        DrawCommand::Line {
            from: Point::new(50.0, 50.0),
            to: Point::new(200.0, 100.0),
            style: LineStyle {
                color: Color::rgb(1.0, 0.0, 0.0), // 红色
                width: 3.0,
                opacity: 1.0,
                dash_pattern: None,
                cap: LineCap::Round,
                join: LineJoin::Round,
            },
        },
        
        // 绘制一个蓝色圆形
        DrawCommand::Circle {
            center: Point::new(100.0, 150.0),
            radius: 30.0,
            style: CircleStyle {
                fill: Some(Color::rgb(0.0, 0.0, 1.0)), // 蓝色填充
                stroke: Some(LineStyle {
                    color: Color::rgb(0.0, 0.0, 0.0), // 黑色边框
                    width: 2.0,
                    opacity: 1.0,
                    dash_pattern: None,
                    cap: LineCap::Round,
                    join: LineJoin::Round,
                }),
                opacity: 1.0,
            },
        },
        
        // 绘制一个绿色矩形
        DrawCommand::Rect {
            bounds: Bounds::new(250.0, 80.0, 80.0, 60.0),
            style: RectStyle {
                fill: Some(Color::rgb(0.0, 1.0, 0.0)), // 绿色填充
                stroke: Some(LineStyle {
                    color: Color::rgb(0.5, 0.5, 0.5), // 灰色边框
                    width: 1.5,
                    opacity: 1.0,
                    dash_pattern: None,
                    cap: LineCap::Butt,
                    join: LineJoin::Miter,
                }),
                opacity: 0.8,
                corner_radius: 5.0,
            },
        },
        
        // 绘制文本
        DrawCommand::Text {
            text: "Hello SVG!".to_string(),
            position: Point::new(200.0, 250.0),
            style: echarts_core::draw_commands::TextStyle {
                font_family: "Arial".to_string(),
                font_size: 18.0,
                font_weight: echarts_core::draw_commands::FontWeight::Bold,
                font_style: echarts_core::draw_commands::FontStyle::Normal,
                color: Color::rgb(0.2, 0.2, 0.8),
                opacity: 1.0,
                align: echarts_core::draw_commands::TextAlign::Center,
                baseline: echarts_core::draw_commands::TextBaseline::Middle,
                rotation: 0.0,
                letter_spacing: 0.0,
                line_height: 1.0,
            },
        },
    ];

    // 渲染为 SVG
    let svg_content = renderer.render_commands(commands)?;

    // 输出 SVG 内容
    println!("生成的 SVG 内容：");
    println!("{}", svg_content);

    // 保存到文件
    std::fs::write("test_output.svg", &svg_content)?;
    println!("✅ SVG 已保存到 test_output.svg");

    Ok(())
}
