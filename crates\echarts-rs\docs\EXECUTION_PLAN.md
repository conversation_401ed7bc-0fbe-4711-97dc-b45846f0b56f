# ECharts-rs 方案执行计划

## 🎯 **确定方案：集成真实的 charts 实现（方案 2）**

**决策时间**: 2025-07-20  
**执行状态**: 待执行  
**严格执行**: ✅ 禁止擅自更改任务

---

## 📋 **方案选择依据**

### ✅ **选择方案 2 的关键理由**

1. **Charts Crate 实现完整度极高**
   - LineSeries: 872 行代码，功能完整
   - BarSeries: 442 行代码，支持高级功能
   - PieSeries: 560+ 行代码，支持多种变体
   - 每个都有完整的 render 方法、数据处理、验证、测试

2. **Core Crate 的占位符性质**
   - PlaceholderLineSeries 等明确标注为"占位符实现"
   - 注释明确说明："在实际应用中，应该使用 charts crate 中的真实实现"

3. **项目架构文档支持**
   - ARCHITECTURE.md 明确定义职责分离
   - GPUI_INTEGRATION.md 展示完整集成方式
   - 所有文档都基于完整实现设计

4. **避免重复工作**
   - Charts crate 已投入大量开发工作
   - 包含动画、交互、优化等高级功能
   - 重新实现会浪费已有成果

### ❌ **拒绝方案 1 的原因**

1. **功能不完整**: 占位符实现缺少高级特性
2. **重复工作**: 已有完整实现，无需重新开发
3. **维护负担**: 需要同步两套实现
4. **违背设计**: 与项目架构文档不符

---

## 🚀 **严格执行计划**

### **第一阶段：统一 Series 接口** ⏳

#### 任务 1.1: 修复 core crate 中的 Series trait
- **文件**: `crates/echarts-rs/crates/core/src/chart.rs`
- **目标**: 定义 dyn 兼容的 Series trait
- **严格要求**: 
  ```rust
  pub trait Series: std::fmt::Debug + Send + Sync {
      fn name(&self) -> &str;
      fn series_type(&self) -> SeriesType;
      fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>>;
      fn bounds(&self) -> Option<Bounds>;
      fn clone_series(&self) -> Box<dyn Series>;
  }
  ```

#### 任务 1.2: 完善 DrawCommand 枚举
- **文件**: `crates/echarts-rs/crates/core/src/chart.rs`
- **目标**: 支持所有渲染操作的类型擦除
- **严格要求**: 包含 DrawPath, DrawRect, DrawText, DrawCircle 等

#### 任务 1.3: 实现 DrawCommand 执行器
- **文件**: `crates/echarts-rs/crates/core/src/chart.rs`
- **目标**: 在 render_series_with_coord 中执行 DrawCommand
- **严格要求**: 支持所有 DrawCommand 变体

### **第二阶段：charts crate 实现统一接口** ⏳

#### 任务 2.1: LineSeries 实现 core::Series
- **文件**: `crates/echarts-rs/crates/charts/src/line.rs`
- **目标**: 实现 echarts_core::Series trait
- **严格要求**: 
  - 保持所有现有功能（平滑曲线、符号、面积图等）
  - 将 render 逻辑转换为 DrawCommand 生成
  - 不删除任何现有功能

#### 任务 2.2: BarSeries 实现 core::Series
- **文件**: `crates/echarts-rs/crates/charts/src/bar.rs`
- **目标**: 实现 echarts_core::Series trait
- **严格要求**: 保持堆叠、边框、背景等所有功能

#### 任务 2.3: PieSeries 实现 core::Series
- **文件**: `crates/echarts-rs/crates/charts/src/pie.rs`
- **目标**: 实现 echarts_core::Series trait
- **严格要求**: 保持环形图、玫瑰图、标签等所有功能

#### 任务 2.4: ScatterSeries 实现 core::Series
- **文件**: `crates/echarts-rs/crates/charts/src/scatter.rs`
- **目标**: 实现 echarts_core::Series trait
- **严格要求**: 保持气泡图、符号映射等所有功能

### **第三阶段：主 crate 集成** ⏳

#### 任务 3.1: 更新主 crate 依赖
- **文件**: `crates/echarts-rs/src/lib.rs`
- **目标**: 重新导出 charts crate 的实现
- **严格要求**: 
  ```rust
  pub use echarts_charts::{LineSeries, BarSeries, PieSeries, ScatterSeries};
  ```

#### 任务 3.2: 修改 Chart 构建器
- **文件**: `crates/echarts-rs/crates/core/src/chart.rs`
- **目标**: 使用 charts crate 的实现
- **严格要求**: 
  - 修改 create_line_series 等方法返回 charts::LineSeries
  - 移除对占位符实现的依赖

### **第四阶段：清理和优化** ⏳

#### 任务 4.1: 移除占位符实现
- **文件**: `crates/echarts-rs/crates/core/src/chart.rs`
- **目标**: 删除所有 Placeholder* 系列
- **严格要求**: 
  - 删除 PlaceholderLineSeries
  - 删除 PlaceholderBarSeries
  - 删除 PlaceholderPieSeries
  - 删除 PlaceholderScatterSeries

#### 任务 4.2: 修复编译错误
- **目标**: 确保所有 crate 编译通过
- **严格要求**: 
  - 修复类型不匹配
  - 修复缺失的方法实现
  - 修复导入问题

#### 任务 4.3: 更新文档和示例
- **目标**: 确保文档与实现一致
- **严格要求**: 更新使用示例，确保 API 一致性

---

## ⚠️ **严格执行规则**

### 🚫 **禁止行为**
1. **禁止更改方案**: 不得从方案 2 改为方案 1
2. **禁止删除功能**: 不得删除 charts crate 中的任何现有功能
3. **禁止重新实现**: 不得在 core crate 中重新实现图表逻辑
4. **禁止跳过阶段**: 必须按顺序执行四个阶段

### ✅ **必须遵守**
1. **保持功能完整**: charts crate 的所有功能必须保留
2. **接口统一**: 所有图表类型必须实现统一的 Series trait
3. **架构清晰**: 保持 core → charts → main 的依赖关系
4. **文档同步**: 代码变更必须同步更新文档

---

## 📊 **执行进度跟踪**

### 阶段一：统一 Series 接口
- [ ] 任务 1.1: 修复 Series trait
- [ ] 任务 1.2: 完善 DrawCommand
- [ ] 任务 1.3: 实现执行器

### 阶段二：charts 实现接口
- [ ] 任务 2.1: LineSeries 实现
- [ ] 任务 2.2: BarSeries 实现
- [ ] 任务 2.3: PieSeries 实现
- [ ] 任务 2.4: ScatterSeries 实现

### 阶段三：主 crate 集成
- [ ] 任务 3.1: 更新依赖
- [ ] 任务 3.2: 修改构建器

### 阶段四：清理优化
- [ ] 任务 4.1: 移除占位符
- [ ] 任务 4.2: 修复编译
- [ ] 任务 4.3: 更新文档

---

## 🎯 **成功标准**

### 技术标准
1. ✅ 所有 crate 编译通过
2. ✅ 所有图表类型功能完整
3. ✅ 接口统一且类型安全
4. ✅ 无循环依赖

### 功能标准
1. ✅ LineSeries 支持平滑曲线、符号、面积图
2. ✅ BarSeries 支持堆叠、边框、背景
3. ✅ PieSeries 支持环形图、玫瑰图、标签
4. ✅ ScatterSeries 支持气泡图、符号映射

### 架构标准
1. ✅ core crate 只包含基础类型和 traits
2. ✅ charts crate 包含完整的图表实现
3. ✅ 主 crate 提供统一的用户 API
4. ✅ 依赖关系清晰合理

---

**执行开始时间**: 待定  
**预计完成时间**: 待定  
**执行负责人**: AI Assistant  
**监督要求**: 严格按照此文档执行，不得偏离
