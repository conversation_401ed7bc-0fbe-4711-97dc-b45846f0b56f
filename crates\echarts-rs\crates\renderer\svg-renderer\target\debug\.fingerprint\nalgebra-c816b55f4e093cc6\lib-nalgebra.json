{"rustc": 1842507548689473721, "features": "[\"default\", \"macros\", \"matrixmultiply\", \"nalgebra-macros\", \"std\"]", "declared_features": "[\"alga\", \"alloc\", \"arbitrary\", \"bytemuck\", \"compare\", \"convert-bytemuck\", \"convert-glam014\", \"convert-glam015\", \"convert-glam016\", \"convert-glam017\", \"convert-glam018\", \"convert-glam019\", \"convert-glam020\", \"convert-glam021\", \"convert-glam022\", \"convert-glam023\", \"convert-glam024\", \"convert-glam025\", \"convert-glam027\", \"convert-mint\", \"cuda\", \"cust_core\", \"debug\", \"default\", \"glam014\", \"glam015\", \"glam016\", \"glam017\", \"glam018\", \"glam019\", \"glam020\", \"glam021\", \"glam022\", \"glam023\", \"glam024\", \"glam025\", \"glam027\", \"io\", \"libm\", \"libm-force\", \"macros\", \"matrixcompare-core\", \"matrixmultiply\", \"mint\", \"nalgebra-macros\", \"pest\", \"pest_derive\", \"proptest\", \"proptest-support\", \"quickcheck\", \"rand\", \"rand-no-std\", \"rand-package\", \"rand_distr\", \"rayon\", \"rkyv\", \"rkyv-safe-deser\", \"rkyv-serialize\", \"rkyv-serialize-no-std\", \"serde\", \"serde-serialize\", \"serde-serialize-no-std\", \"slow-tests\", \"sparse\", \"std\"]", "target": 572955357253318494, "profile": 15657897354478470176, "path": 6645531167158671281, "deps": [[2819946551904607991, "num_rational", false, 1876325416527526024], [4462856585586636430, "simba", false, 15041006066312496777], [5157631553186200874, "num_traits", false, 8668028564112938398], [11394677342629719743, "nalgebra_macros", false, 4018063551740418787], [12319020793864570031, "num_complex", false, 3589429579049095055], [15677050387741058262, "approx", false, 11795322852823524182], [15826188163127377936, "matrixmultiply", false, 5302834522808268670], [17001665395952474378, "typenum", false, 5506445262107761391]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\nalgebra-c816b55f4e093cc6\\dep-lib-nalgebra", "checksum": false}}], "rustflags": ["-C", "link-arg=/STACK:16000000"], "config": 2069994364910194474, "compile_kind": 0}