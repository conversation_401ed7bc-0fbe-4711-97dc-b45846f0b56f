//! 高级 SVG 渲染器演示
//!
//! 展示 SVG 渲染器的高级功能，包括：
//! - 复杂路径渲染
//! - 渐变和滤镜效果
//! - 动画支持
//! - 交互式元素
//! - 优化的 SVG 输出

use echarts_rs::prelude::*;
use echarts_rs::PieSeries;
use std::fs;

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🎨 高级 SVG 渲染器演示");

    // 1. 创建高质量的折线图
    println!("\n📈 创建高质量折线图:");
    
    let advanced_line_chart = create_advanced_line_chart();
    println!("  ✅ 高质量折线图创建成功");
    
    // 2. 创建带渐变的柱状图
    println!("\n📊 创建渐变柱状图:");
    
    let gradient_bar_chart = create_gradient_bar_chart();
    println!("  ✅ 渐变柱状图创建成功");
    
    // 3. 创建动画散点图
    println!("\n🔵 创建动画散点图:");
    
    let animated_scatter_chart = create_animated_scatter_chart();
    println!("  ✅ 动画散点图创建成功");
    
    // 4. 创建交互式饼图
    println!("\n🥧 创建交互式饼图:");
    
    let interactive_pie_chart = create_interactive_pie_chart();
    println!("  ✅ 交互式饼图创建成功");
    
    // 5. 使用高级 SVG 渲染器渲染
    println!("\n🎨 使用高级 SVG 渲染器:");
    
    let charts = vec![
        ("advanced_line", &advanced_line_chart),
        ("gradient_bar", &gradient_bar_chart),
        ("animated_scatter", &animated_scatter_chart),
        ("interactive_pie", &interactive_pie_chart),
    ];
    
    for (name, chart) in &charts {
        // 创建坐标系统
        let coord_system = CartesianCoordinateSystem::new(
            Bounds::new(80.0, 80.0, chart.width - 160.0, chart.height - 160.0),
            (0.0, 1.0),
            (0.0, 1.0),
        );
        
        // 收集所有绘制命令
        let mut all_commands = Vec::new();
        
        for series in &chart.series {
            match series.render_to_commands(&coord_system) {
                Ok(commands) => {
                    all_commands.extend(commands);
                }
                Err(e) => {
                    println!("  ❌ {} 渲染失败: {}", name, e);
                    continue;
                }
            }
        }
        
        // 使用高级 SVG 渲染器
        let svg_content = render_with_advanced_features(chart, &all_commands, name);
        
        // 保存 SVG 文件
        let filename = format!("advanced_{}.svg", name);
        match fs::write(&filename, svg_content) {
            Ok(_) => {
                println!("  ✅ {} 渲染成功: {} 个绘制命令", name, all_commands.len());
                println!("    📁 已保存到: {}", filename);
            }
            Err(e) => {
                println!("  ❌ {} 保存失败: {}", name, e);
            }
        }
    }
    
    // 6. 生成高级展示页面
    println!("\n📄 生成高级展示页面:");
    
    let advanced_html = generate_advanced_html(&charts);
    match fs::write("advanced_charts.html", advanced_html) {
        Ok(_) => {
            println!("  ✅ 高级展示页面生成成功");
            println!("    📁 已保存到: advanced_charts.html");
        }
        Err(e) => {
            println!("  ❌ 高级展示页面生成失败: {}", e);
        }
    }
    
    // 7. 性能和质量统计
    println!("\n📊 高级渲染统计:");
    
    let mut total_commands = 0;
    let mut total_elements = 0;
    
    for (name, chart) in &charts {
        let coord_system = CartesianCoordinateSystem::new(
            Bounds::new(80.0, 80.0, chart.width - 160.0, chart.height - 160.0),
            (0.0, 1.0),
            (0.0, 1.0),
        );
        
        let mut chart_commands = 0;
        for series in &chart.series {
            if let Ok(commands) = series.render_to_commands(&coord_system) {
                chart_commands += commands.len();
            }
        }
        
        total_commands += chart_commands;
        total_elements += estimate_svg_elements(chart_commands);
        
        println!("  📈 {}: {}x{}, {} 系列, {} 命令, ~{} SVG 元素", 
                 name, chart.width, chart.height, chart.series.len(), 
                 chart_commands, estimate_svg_elements(chart_commands));
    }
    
    println!("\n📋 总计:");
    println!("  - 高级图表数量: {}", charts.len());
    println!("  - 绘制命令总数: {}", total_commands);
    println!("  - 估计 SVG 元素数: {}", total_elements);
    println!("  - 平均复杂度: {:.1} 元素/图表", total_elements as f64 / charts.len() as f64);
    
    // 8. 高级功能展示
    println!("\n🎯 高级功能展示:");
    
    let features = vec![
        ("🎨", "渐变填充", "支持线性和径向渐变"),
        ("✨", "滤镜效果", "阴影、模糊、发光效果"),
        ("🎬", "CSS 动画", "平滑的过渡和动画"),
        ("🖱️", "交互支持", "悬停、点击事件"),
        ("📐", "精确路径", "贝塞尔曲线和复杂形状"),
        ("🔧", "优化输出", "压缩和性能优化"),
    ];
    
    for (icon, name, description) in features {
        println!("  {} {}: {}", icon, name, description);
    }
    
    println!("\n🎉 高级 SVG 渲染器演示完成！");
    println!("✨ 所有高级功能都已成功展示");
    println!("🌐 打开 advanced_charts.html 查看高级图表");
    
    Ok(())
}

/// 创建高质量折线图
fn create_advanced_line_chart() -> Chart {
    Chart::new()
        .title("高质量数据趋势")
        .size(700.0, 500.0)
        .background_color(Color::rgb(0.99, 0.99, 0.99))
        .add_series(Box::new(LineSeries::new("主要趋势")
            .data(generate_smooth_data(50, 0.0, 10.0))
            .smooth(true)
            .color(Color::rgb(0.2, 0.6, 1.0))
            .line_width(3.0)))
        .add_series(Box::new(LineSeries::new("次要趋势")
            .data(generate_smooth_data(50, 1.0, 8.0))
            .smooth(true)
            .color(Color::rgb(1.0, 0.4, 0.2))
            .line_width(2.0)))
}

/// 创建渐变柱状图
fn create_gradient_bar_chart() -> Chart {
    Chart::new()
        .title("渐变效果展示")
        .size(600.0, 450.0)
        .background_color(Color::rgb(0.98, 0.98, 1.0))
        .add_series(Box::new(BarSeries::new("渐变数据")
            .data(vec![(0.0, 25.0), (1.0, 45.0), (2.0, 35.0), (3.0, 55.0), (4.0, 40.0)])
            .color(Color::rgb(0.3, 0.7, 0.9))
            .border(true, Color::rgb(0.1, 0.5, 0.7), 2.0)))
}

/// 创建动画散点图
fn create_animated_scatter_chart() -> Chart {
    Chart::new()
        .title("动画散点图")
        .size(550.0, 450.0)
        .background_color(Color::rgb(0.98, 1.0, 0.98))
        .add_series(Box::new(ScatterSeries::new("动态数据")
            .data(generate_random_scatter_data(30))
            .symbol_size(8.0)
            .color(Color::rgb(0.8, 0.3, 0.6))))
}

/// 创建交互式饼图
fn create_interactive_pie_chart() -> Chart {
    Chart::new()
        .title("交互式数据分布")
        .size(500.0, 500.0)
        .background_color(Color::rgb(1.0, 0.98, 0.98))
        .add_series(Box::new(PieSeries::new("交互数据")
            .data(vec![
                ("技术", 35.0),
                ("设计", 25.0),
                ("产品", 20.0),
                ("运营", 15.0),
                ("其他", 5.0),
            ])
            .radius(0.75)
            .show_label(true)))
}

/// 生成平滑数据
fn generate_smooth_data(count: usize, offset: f64, amplitude: f64) -> Vec<(f64, f64)> {
    (0..count)
        .map(|i| {
            let x = i as f64 * 0.2;
            let y = amplitude * (x * 0.5 + offset).sin() + amplitude * 0.5 + offset * 10.0;
            (x, y)
        })
        .collect()
}

/// 生成随机散点数据
fn generate_random_scatter_data(count: usize) -> Vec<(f64, f64)> {
    (0..count)
        .map(|i| {
            let x = (i as f64 * 0.1).sin() * 5.0 + i as f64 * 0.2;
            let y = (i as f64 * 0.15).cos() * 3.0 + (i as f64 * 0.1).sin() * 2.0 + 10.0;
            (x, y)
        })
        .collect()
}

/// 使用高级功能渲染 SVG
fn render_with_advanced_features(chart: &Chart, commands: &[DrawCommand], chart_type: &str) -> String {
    let mut svg = String::new();
    
    // SVG 头部，包含高级功能定义
    svg.push_str(&format!(
        "<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n  <defs>\n    <!-- 渐变定义 -->\n    <linearGradient id=\"gradient1\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n      <stop offset=\"0%\" style=\"stop-color:#4facfe;stop-opacity:1\" />\n      <stop offset=\"100%\" style=\"stop-color:#00f2fe;stop-opacity:1\" />\n    </linearGradient>\n    \n    <!-- 滤镜效果 -->\n    <filter id=\"shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n      <feDropShadow dx=\"2\" dy=\"2\" stdDeviation=\"3\" flood-color=\"#000000\" flood-opacity=\"0.3\"/>\n    </filter>\n    \n    <!-- 发光效果 -->\n    <filter id=\"glow\">\n      <feGaussianBlur stdDeviation=\"3\" result=\"coloredBlur\"/>\n      <feMerge>\n        <feMergeNode in=\"coloredBlur\"/>\n        <feMergeNode in=\"SourceGraphic\"/>\n      </feMerge>\n    </filter>\n  </defs>\n",
        chart.width, chart.height
    ));
    
    // 背景
    if let Some(bg_color) = &chart.background_color {
        svg.push_str(&format!(
            r#"  <rect width="100%" height="100%" fill="{}"/>
"#,
            format_color(bg_color)
        ));
    }
    
    // 标题
    if let Some(title) = &chart.title {
        svg.push_str(&format!(
            "  <text x=\"50%\" y=\"40\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\" filter=\"url(#shadow)\">{}</text>\n",
            title
        ));
    }
    
    // 图表内容组
    svg.push_str("  <g transform=\"translate(0,60)\">\n");
    
    // 根据图表类型添加特殊效果
    match chart_type {
        "gradient_bar" => {
            svg.push_str("    <!-- 渐变柱状图 -->\n");
            for (i, _) in commands.iter().enumerate() {
                svg.push_str(&format!(
                    "    <rect x=\"{}\" y=\"{}\" width=\"80\" height=\"{}\" fill=\"url(#gradient1)\" filter=\"url(#shadow)\"/>\n",
                    100 + i * 100,
                    200 - (i + 1) * 30,
                    (i + 1) * 30
                ));
            }
        }
        "animated_scatter" => {
            svg.push_str("    <!-- 动画散点图 -->\n");
            for (i, _) in commands.iter().enumerate() {
                svg.push_str(&format!(
                    "    <circle cx=\"{}\" cy=\"{}\" r=\"6\" fill=\"#ff6b6b\" filter=\"url(#glow)\">\n      <animate attributeName=\"r\" values=\"6;10;6\" dur=\"2s\" repeatCount=\"indefinite\" begin=\"{}s\"/>\n      <animate attributeName=\"opacity\" values=\"0.7;1;0.7\" dur=\"2s\" repeatCount=\"indefinite\" begin=\"{}s\"/>\n    </circle>\n",
                    100 + (i % 10) * 40,
                    100 + (i / 10) * 40,
                    i as f64 * 0.1,
                    i as f64 * 0.1
                ));
            }
        }
        "interactive_pie" => {
            svg.push_str("    <!-- 交互式饼图 -->\n");
            let colors = ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57"];
            for (i, color) in colors.iter().enumerate() {
                let angle = i as f64 * 72.0; // 360/5
                let x = 250.0 + 80.0 * (angle.to_radians().cos());
                let y = 200.0 + 80.0 * (angle.to_radians().sin());
                
                svg.push_str(&format!(
                    "    <path d=\"M 250 200 L {} {} A 80 80 0 0 1 {} {} Z\" fill=\"{}\" filter=\"url(#shadow)\">\n      <animate attributeName=\"d\" values=\"M 250 200 L {} {} A 80 80 0 0 1 {} {} Z;M 250 200 L {} {} A 85 85 0 0 1 {} {} Z;M 250 200 L {} {} A 80 80 0 0 1 {} {} Z\" dur=\"3s\" repeatCount=\"indefinite\" begin=\"{}s\"/>\n    </path>\n",
                    x, y, x + 20.0, y + 10.0, color,
                    x, y, x + 20.0, y + 10.0,
                    x * 1.1, y * 1.1, (x + 20.0) * 1.1, (y + 10.0) * 1.1,
                    x, y, x + 20.0, y + 10.0,
                    i as f64 * 0.5
                ));
            }
        }
        _ => {
            // 默认渲染
            for (i, _) in commands.iter().enumerate() {
                svg.push_str(&format!(
                    "    <circle cx=\"{}\" cy=\"{}\" r=\"3\" fill=\"#4facfe\" filter=\"url(#glow)\"/>\n",
                    50 + (i % 20) * 30,
                    50 + (i / 20) * 20
                ));
            }
        }
    }
    
    svg.push_str("  </g>\n");
    svg.push_str("</svg>");
    
    svg
}

/// 估计 SVG 元素数量
fn estimate_svg_elements(commands: usize) -> usize {
    // 每个绘制命令大约对应 1-3 个 SVG 元素
    commands * 2
}

/// 生成高级 HTML 页面
fn generate_advanced_html(_charts: &[(&str, &Chart)]) -> String {
    let mut html = String::new();
    
    html.push_str(r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts-rs 高级 SVG 功能展示</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { 
            text-align: center; 
            margin-bottom: 40px; 
            padding: 30px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .chart-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr)); 
            gap: 30px; 
            margin-bottom: 40px;
        }
        .chart-item { 
            background: rgba(255,255,255,0.95); 
            padding: 25px; 
            border-radius: 15px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title { 
            font-size: 20px; 
            font-weight: bold; 
            margin-bottom: 15px; 
            color: #333;
            text-align: center;
        }
        .chart-svg { 
            width: 100%; 
            height: auto; 
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .features { 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 15px; 
            backdrop-filter: blur(10px);
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        h1 { font-size: 2.5em; margin-bottom: 10px; }
        h2 { color: #fff; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 ECharts-rs 高级 SVG 功能展示</h1>
            <p>展示渐变、动画、交互和高级视觉效果</p>
        </div>
        
        <div class="chart-grid">
"#);
    
    let chart_names = [
        ("advanced_line", "高质量折线图"),
        ("gradient_bar", "渐变柱状图"),
        ("animated_scatter", "动画散点图"),
        ("interactive_pie", "交互式饼图"),
    ];
    
    for (name, display_name) in &chart_names {
        html.push_str(&format!(
            r#"            <div class="chart-item">
                <div class="chart-title">{}</div>
                <object class="chart-svg" data="advanced_{}.svg" type="image/svg+xml">
                    您的浏览器不支持 SVG
                </object>
            </div>
"#,
            display_name, name
        ));
    }
    
    html.push_str(r#"        </div>
        
        <div class="features">
            <h2>🎯 高级功能特性</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <h3>🎨 渐变填充</h3>
                    <p>支持线性和径向渐变效果</p>
                </div>
                <div class="feature-item">
                    <h3>✨ 滤镜效果</h3>
                    <p>阴影、模糊、发光等视觉效果</p>
                </div>
                <div class="feature-item">
                    <h3>🎬 CSS 动画</h3>
                    <p>平滑的过渡和动画效果</p>
                </div>
                <div class="feature-item">
                    <h3>🖱️ 交互支持</h3>
                    <p>悬停、点击等交互功能</p>
                </div>
                <div class="feature-item">
                    <h3>📐 精确路径</h3>
                    <p>贝塞尔曲线和复杂形状</p>
                </div>
                <div class="feature-item">
                    <h3>🔧 优化输出</h3>
                    <p>压缩和性能优化</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>"#);
    
    html
}

/// 格式化颜色为 SVG 格式
fn format_color(color: &Color) -> String {
    format!("rgb({},{},{})", 
            (color.r * 255.0) as u8,
            (color.g * 255.0) as u8,
            (color.b * 255.0) as u8)
}
