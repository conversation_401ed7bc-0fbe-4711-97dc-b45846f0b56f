
[workspace]
members = ["crates/app", "crates/usui", "crates/chart", "crates/tsdaq_protocol","crates/echarts-rs"]

default-members = ["crates/app"]
resolver = "2"


[workspace.dependencies]
usui = { path = "crates/usui" }
story = { path = "crates/story" }
chart = { path = "crates/chart" }
echarts-rs = { path = "crates/echarts-rs" }
tsdaq_protocol = { path = "crates/tsdaq_protocol" }

gpui = { git = "https://github.com/zed-industries/zed.git" }
gpui-component = { git = "https://github.com/longbridge/gpui-component.git" }
gpui-component-macros = { git = "https://github.com/longbridge/gpui-component.git" }

# 核心依赖（统一版本）
serde = { version = "1.0.203", features = ["derive"] }
serde_json = "1.0"
thiserror = "1.0"
anyhow = "1.0"

# 高效字节处理
byteorder = "1.4"
bytes = { version = "1", features = ["serde"] }

# 异步运行时
tokio = { version = "1.0", features = [
    "full",
    "rt-multi-thread",
    "time",
    "net",
    "io-util",
    "macros",
    "sync",
] }
tokio-stream = "0.1.17"
futures = "0.3.31"

# 时间处理
chrono = "0.4.33"

# Windows 特定
windows = { version = "0.58.0", features = [
    "Wdk",
    "Wdk_System",
    "Wdk_System_SystemServices",
] }

# 日志
strum = { version = "0.25", features = ["derive"] }
tracing = { version = "0.1.41", features = ["release_max_level_info"] }
opentelemetry = "0.21"
opentelemetry-jaeger = "0.20"
tracing-opentelemetry = "0.22"
tracing-error = "0.2"
tracing-subscriber = { version = "0.3.19", features = ["fmt", "env-filter", "chrono"] }
tracing-appender = "0.2.3"
log = "0.4"

# 其他工具
memchr = { version = "2.7.5", features = ["std"] }
rust-i18n = "3.1.5"

# echarts-rs 需要的额外依赖
uuid = { version = "1.0", features = ["v4"] }
indexmap = "2.0"
smallvec = "1.11"
nalgebra = "0.32"
euclid = "0.22"

[workspace.lints.clippy]
almost_complete_range = "allow"
arc_with_non_send_sync = "allow"
borrowed_box = "allow"
dbg_macro = "deny"
let_underscore_future = "allow"
map_entry = "allow"
module_inception = "allow"
non_canonical_partial_ord_impl = "allow"
reversed_empty_ranges = "allow"
single_range_in_vec_init = "allow"
style = { level = "allow", priority = -1 }
todo = "deny"
type_complexity = "allow"

[profile.dev]
codegen-units = 16
debug = "limited"
split-debuginfo = "unpacked"

[profile.dev.package]
resvg = { opt-level = 3 }
rustybuzz = { opt-level = 3 }
taffy = { opt-level = 3 }
ttf-parser = { opt-level = 3 }
