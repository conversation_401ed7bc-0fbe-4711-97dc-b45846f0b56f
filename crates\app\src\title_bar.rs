/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-06-07 17:15:29
 * @LastEditors: Artis
 * @LastEditTime: 2025-07-13 10:28:50
 * @FilePath: \FscDAQ_GPUI\crates\app\src\title_bar.rs
 * @Description:
 *
 * Copyright (c) 2025 by Artis, All Rights Reserved.
 */
use std::rc::Rc;

use gpui::{
    div, prelude::FluentBuilder as _, px, AnyElement, App, AppContext, ClickEvent, Context, Corner,
    Entity, FocusHandle, Hsla, InteractiveElement as _, IntoElement, MouseButton,
    ParentElement as _, Render, SharedString, Styled as _, Subscription, Window, WindowControlArea,
};
use gpui_component::{
    badge::Badge,
    button::{Button, ButtonVariants as _},
    color_picker::{ColorPicker, ColorPickerEvent, ColorPickerState},
    h_flex, locale,
    popup_menu::PopupMenuExt as _,
    scroll::ScrollbarShow,
    set_locale, ActiveTheme as _, ContextModal as _, IconName, Sizable as _, Theme, ThemeMode,
    TitleBar,
};

use crate::{device::DataCollection, SelectFont, SelectLocale};

pub struct AppTitleBar {
    title: SharedString,
    locale_selector: Entity<LocaleSelector>,
    data_collection: Entity<DataCollection>,
    font_size_selector: Entity<FontSizeSelector>,
    theme_color: Entity<ColorPickerState>,
    child: Rc<dyn Fn(&mut Window, &mut App) -> AnyElement>,
    _subscriptions: Vec<Subscription>,
    disable_double_click: bool,
}

impl AppTitleBar {
    pub fn new(
        title: impl Into<SharedString>,
        window: &mut Window,
        cx: &mut Context<Self>,
    ) -> Self {
        let locale_selector = cx.new(|cx| LocaleSelector::new(window, cx));
        let font_size_selector = cx.new(|cx| FontSizeSelector::new(window, cx));
        let data_collection = cx.new(|cx| DataCollection::new(window, cx));

        if cx.should_auto_hide_scrollbars() {
            Theme::global_mut(cx).scrollbar_show = ScrollbarShow::Scrolling;
        } else {
            Theme::global_mut(cx).scrollbar_show = ScrollbarShow::Hover;
        }

        let theme_color =
            cx.new(|cx| ColorPickerState::new(window, cx).default_value(cx.theme().primary));

        let _subscriptions = vec![cx.subscribe_in(
            &theme_color,
            window,
            |this, _, ev: &ColorPickerEvent, window, cx| match ev {
                ColorPickerEvent::Change(color) => {
                    this.set_theme_color(*color, window, cx);
                }
            },
        )];

        Self {
            title: title.into(),
            locale_selector,
            data_collection,
            font_size_selector,
            theme_color,
            child: Rc::new(|_, _| div().into_any_element()),
            _subscriptions,
            disable_double_click: false,
        }
    }

    pub fn child<F, E>(mut self, f: F) -> Self
    where
        E: IntoElement,
        F: Fn(&mut Window, &mut App) -> E + 'static,
    {
        self.child = Rc::new(move |window, cx| f(window, cx).into_any_element());
        self
    }

    // 添加设置禁用双击功能的方法
    pub fn disable_double_click(mut self, disable: bool) -> Self {
        self.disable_double_click = disable;
        self
    }

    // fn set_theme_color(
    //     &mut self,
    //     color: Option<Hsla>,
    //     window: &mut Window,
    //     cx: &mut Context<Self>,
    // ) {
    //     if let Some(color) = color {
    //         let theme = cx.global_mut::<Theme>();
    //         theme.apply_color(color);
    //         self.theme_color.update(cx, |state, cx| {
    //             state.set_value(color, window, cx);
    //         });
    //         window.refresh();
    //     }
    // }
    fn set_theme_color(
        &mut self,
        color: Option<Hsla>,
        window: &mut Window,
        cx: &mut Context<Self>,
    ) {
        if let Some(color) = color {
            let mut theme = cx.global_mut::<Theme>();
            theme.primary = color;
            self.theme_color.update(cx, |state, cx| {
                state.set_value(color, window, cx);
            });
            window.refresh();
        }
    }
    fn change_color_mode(&mut self, _: &ClickEvent, window: &mut Window, cx: &mut Context<Self>) {
        let mode = match cx.theme().mode.is_dark() {
            true => ThemeMode::Light,
            false => ThemeMode::Dark,
        };

        Theme::change(mode, None, cx);
    }
}

impl Render for AppTitleBar {
    fn render(&mut self, window: &mut Window, cx: &mut Context<Self>) -> impl IntoElement {
        let notifications_count = window.notifications(cx).len();
        let disable_double_click = self.disable_double_click;

        // 构建标题栏
        h_flex()
            .w_full()
            .h(px(34.))
            .border_b_1()
            .border_color(cx.theme().title_bar_border)
            .bg(cx.theme().title_bar)
            .pl_2()
            .child(
                div()
                    .w_full()
                    // 添加窗口控制区域标记
                    .window_control_area(WindowControlArea::Drag) // 标记为可拖动区域
                    // 处理双击和拖动
                    .on_mouse_down(MouseButton::Left, move |event, window, cx| {
                        // 先开始拖动
                        window.start_window_move();

                        // 如果是双击且未禁用，处理最大化/恢复
                        if event.click_count == 2 && !disable_double_click {
                            #[cfg(target_os = "macos")]
                            window.titlebar_double_click();

                            #[cfg(not(target_os = "macos"))]
                            window.zoom_window();
                        }
                    })
                    .child(self.title.clone()),
            )
            .child(
                div()
                    .flex()
                    .items_center()
                    .justify_end()
                    .child((self.child.clone())(window, cx))
                    .child(self.data_collection.clone())
                    .child(
                        ColorPicker::new(&self.theme_color)
                            .small()
                            .anchor(Corner::TopRight)
                            .icon(IconName::Palette),
                    )
                    .child(
                        Button::new("theme-mode")
                            .map(|this| {
                                if cx.theme().mode.is_dark() {
                                    this.icon(IconName::Sun).size(px(34.))
                                } else {
                                    this.icon(IconName::Moon)
                                }
                            })
                            .small()
                            .ghost()
                            .on_click(cx.listener(Self::change_color_mode)),
                    )
                    .child(self.locale_selector.clone())
                    .child(self.font_size_selector.clone())
                    .child(
                        div().relative().child(
                            Badge::new().count(notifications_count).max(99).child(
                                Button::new("bell")
                                    .small()
                                    .ghost()
                                    .compact()
                                    .icon(IconName::Bell),
                            ),
                        ),
                    )
                    .child(
                        div()
                            .mt_1() // 添加顶部margin 1像素
                            .mr_2()
                            .child(TitleBar::new()),
                    ),
            )
    }
}

struct LocaleSelector {
    focus_handle: FocusHandle,
}

impl LocaleSelector {
    pub fn new(_: &mut Window, cx: &mut Context<Self>) -> Self {
        Self {
            focus_handle: cx.focus_handle(),
        }
    }

    fn on_select_locale(
        &mut self,
        locale: &SelectLocale,
        window: &mut Window,
        _: &mut Context<Self>,
    ) {
        // rust_i18n::set_locale(&locale.0);
        // set_locale_fn(&locale.0);
        set_locale(&locale.0);
        window.refresh();
    }
}

impl Render for LocaleSelector {
    fn render(&mut self, _: &mut Window, cx: &mut Context<Self>) -> impl IntoElement {
        let focus_handle = self.focus_handle.clone();
        let locale = locale().to_string();

        div()
            .id("locale-selector")
            .track_focus(&focus_handle)
            .on_action(cx.listener(Self::on_select_locale))
            .child(
                Button::new("btn")
                    .small()
                    .ghost()
                    .icon(IconName::Globe)
                    .popup_menu(move |this, _, _| {
                        this.menu_with_check(
                            "English",
                            locale == "en",
                            Box::new(SelectLocale("en".into())),
                        )
                        .menu_with_check(
                            "简体中文",
                            locale == "zh-CN",
                            Box::new(SelectLocale("zh-CN".into())),
                        )
                    })
                    .anchor(Corner::TopRight),
            )
    }
}

struct FontSizeSelector {
    focus_handle: FocusHandle,
}

impl FontSizeSelector {
    pub fn new(_: &mut Window, cx: &mut Context<Self>) -> Self {
        Self {
            focus_handle: cx.focus_handle(),
        }
    }

    fn on_select_font(
        &mut self,
        font_size: &SelectFont,
        window: &mut Window,
        cx: &mut Context<Self>,
    ) {
        Theme::global_mut(cx).font_size = px(font_size.0 as f32);
        window.refresh();
    }

    // fn on_select_radius(
    //     &mut self,
    //     radius: &SelectRadius,
    //     window: &mut Window,
    //     cx: &mut Context<Self>,
    // ) {
    //     Theme::global_mut(cx).radius = px(radius.0 as f32);
    //     window.refresh();
    // }

    // fn on_select_scrollbar_show(
    //     &mut self,
    //     show: &SelectScrollbarShow,
    //     window: &mut Window,
    //     cx: &mut Context<Self>,
    // ) {
    //     Theme::global_mut(cx).scrollbar_show = show.0;
    //     window.refresh();
    // }
}

impl Render for FontSizeSelector {
    fn render(&mut self, _: &mut Window, cx: &mut Context<Self>) -> impl IntoElement {
        let focus_handle = self.focus_handle.clone();
        let font_size = cx.theme().font_size.0 as i32;
        let radius = cx.theme().radius.0 as i32;
        let scroll_show = cx.theme().scrollbar_show;

        div()
            .id("font-size-selector")
            .track_focus(&focus_handle)
            .on_action(cx.listener(Self::on_select_font))
            // .on_action(cx.listener(Self::on_select_radius))
            // .on_action(cx.listener(Self::on_select_scrollbar_show))
            .child(
                Button::new("btn")
                    .small()
                    .ghost()
                    .icon(IconName::Settings2)
                    .popup_menu(move |this, _, _| {
                        this.scrollable()
                            .max_h(px(480.))
                            .label("Font Size")
                            .menu_with_check("Large", font_size == 18, Box::new(SelectFont(18)))
                            .menu_with_check(
                                "Medium (default)",
                                font_size == 16,
                                Box::new(SelectFont(16)),
                            )
                            .menu_with_check("Small", font_size == 14, Box::new(SelectFont(14)))
                            .separator()
                        // .label("Border Radius")
                        // .menu_with_check("8px", radius == 8, Box::new(SelectRadius(8)))
                        // .menu_with_check(
                        //     "4px (default)",
                        //     radius == 4,
                        //     Box::new(SelectRadius(4)),
                        // )
                        // .menu_with_check("0px", radius == 0, Box::new(SelectRadius(0)))
                        // .separator()
                    })
                    .anchor(Corner::TopRight),
            )
    }
}
