//! Style definitions and utilities

use crate::Color;
use serde::{Deserialize, Serialize};

/// Text style configuration (统一版本)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextStyle {
    /// Font family
    pub font_family: String,

    /// Font size in pixels
    pub font_size: f64,

    /// Text color
    pub color: Color,

    /// Font weight
    pub font_weight: FontWeight,

    /// Font style
    pub font_style: FontStyle,

    /// Text alignment
    pub align: TextAlign,

    /// Text baseline
    pub baseline: TextBaseline,

    /// Text opacity (0.0 - 1.0)
    pub opacity: f64,

    /// Text rotation in degrees
    pub rotation: f64,

    /// Line height multiplier
    pub line_height: f64,

    /// Letter spacing
    pub letter_spacing: f64,
}

impl Default for TextStyle {
    fn default() -> Self {
        Self {
            font_family: "Arial".to_string(),
            font_size: 12.0,
            color: Color::BLACK,
            font_weight: FontWeight::Normal,
            font_style: FontStyle::Normal,
            align: TextAlign::Left,
            baseline: TextBaseline::Alphabetic,
            opacity: 1.0,
            rotation: 0.0,
            line_height: 1.2,
            letter_spacing: 0.0,
        }
    }
}

/// Font weight options
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum FontWeight {
    Thin,
    ExtraLight,
    Light,
    Normal,
    Medium,
    SemiBold,
    Bold,
    ExtraBold,
    Black,
}

impl FontWeight {
    pub fn to_css_value(&self) -> u16 {
        match self {
            FontWeight::Thin => 100,
            FontWeight::ExtraLight => 200,
            FontWeight::Light => 300,
            FontWeight::Normal => 400,
            FontWeight::Medium => 500,
            FontWeight::SemiBold => 600,
            FontWeight::Bold => 700,
            FontWeight::ExtraBold => 800,
            FontWeight::Black => 900,
        }
    }
}

/// Font style options
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum FontStyle {
    Normal,
    Italic,
    Oblique,
}

/// Text alignment options
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TextAlign {
    Left,
    Center,
    Right,
    Start,
    End,
}

/// Text baseline options
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TextBaseline {
    Top,
    Hanging,
    Middle,
    Alphabetic,
    Ideographic,
    Bottom,
}

/// Line style configuration (统一版本)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LineStyle {
    /// Line color
    pub color: Color,

    /// Line width
    pub width: f64,

    /// Line dash pattern
    pub dash_pattern: Option<Vec<f64>>,

    /// Line cap style
    pub cap: LineCap,

    /// Line join style
    pub join: LineJoin,

    /// Line opacity (0.0 - 1.0)
    pub opacity: f64,
}

impl Default for LineStyle {
    fn default() -> Self {
        Self {
            color: Color::BLACK,
            width: 1.0,
            dash_pattern: None,
            cap: LineCap::Butt,
            join: LineJoin::Miter,
            opacity: 1.0,
        }
    }
}

/// Line cap styles
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum LineCap {
    Butt,
    Round,
    Square,
}

/// Line join styles
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum LineJoin {
    Miter,
    Round,
    Bevel,
}

/// Fill style configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FillStyle {
    /// Fill color or gradient
    pub fill: Fill,

    /// Fill opacity (0.0 to 1.0)
    pub opacity: f64,
}

impl Default for FillStyle {
    fn default() -> Self {
        Self {
            fill: Fill::Solid(Color::BLACK),
            opacity: 1.0,
        }
    }
}

/// Fill types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Fill {
    /// Solid color fill
    Solid(Color),

    /// Linear gradient fill
    LinearGradient {
        start: crate::Point,
        end: crate::Point,
        stops: Vec<GradientStop>,
    },

    /// Radial gradient fill
    RadialGradient {
        center: crate::Point,
        radius: f64,
        stops: Vec<GradientStop>,
    },

    /// Pattern fill (not implemented yet)
    Pattern {
        image: String,
        repeat: PatternRepeat,
    },
}

/// Gradient stop
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GradientStop {
    /// Position (0.0 to 1.0)
    pub offset: f64,

    /// Color at this position
    pub color: Color,
}

/// Pattern repeat modes
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PatternRepeat {
    Repeat,
    RepeatX,
    RepeatY,
    NoRepeat,
}

/// Shadow style configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShadowStyle {
    /// Shadow color
    pub color: Color,

    /// Horizontal offset
    pub offset_x: f64,

    /// Vertical offset
    pub offset_y: f64,

    /// Blur radius
    pub blur: f64,

    /// Spread radius
    pub spread: f64,
}

impl Default for ShadowStyle {
    fn default() -> Self {
        Self {
            color: Color::BLACK.with_alpha(0.3),
            offset_x: 2.0,
            offset_y: 2.0,
            blur: 4.0,
            spread: 0.0,
        }
    }
}

/// Border style configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BorderStyle {
    /// Border color
    pub color: Color,

    /// Border width
    pub width: f64,

    /// Border radius
    pub radius: f64,

    /// Border dash pattern
    pub dash: Option<Vec<f64>>,
}

impl Default for BorderStyle {
    fn default() -> Self {
        Self {
            color: Color::BLACK,
            width: 1.0,
            radius: 0.0,
            dash: None,
        }
    }
}

/// Complete style configuration for chart elements
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ElementStyle {
    /// Fill style
    pub fill: Option<FillStyle>,

    /// Stroke/border style
    pub stroke: Option<LineStyle>,

    /// Shadow style
    pub shadow: Option<ShadowStyle>,

    /// Opacity (0.0 to 1.0)
    pub opacity: f64,

    /// Transform
    pub transform: Option<crate::Transform>,
}

impl Default for ElementStyle {
    fn default() -> Self {
        Self {
            fill: None,
            stroke: None,
            shadow: None,
            opacity: 1.0,
            transform: None,
        }
    }
}

impl ElementStyle {
    /// Create a style with solid fill
    pub fn filled(color: Color) -> Self {
        Self {
            fill: Some(FillStyle {
                fill: Fill::Solid(color),
                opacity: 1.0,
            }),
            ..Default::default()
        }
    }

    /// Create a style with stroke only
    pub fn stroked(color: Color, width: f64) -> Self {
        Self {
            stroke: Some(LineStyle {
                color,
                width,
                ..Default::default()
            }),
            ..Default::default()
        }
    }

    /// Create a style with both fill and stroke
    pub fn filled_and_stroked(fill_color: Color, stroke_color: Color, stroke_width: f64) -> Self {
        Self {
            fill: Some(FillStyle {
                fill: Fill::Solid(fill_color),
                opacity: 1.0,
            }),
            stroke: Some(LineStyle {
                color: stroke_color,
                width: stroke_width,
                ..Default::default()
            }),
            ..Default::default()
        }
    }

    /// Add shadow to the style
    pub fn with_shadow(mut self, shadow: ShadowStyle) -> Self {
        self.shadow = Some(shadow);
        self
    }

    /// Set opacity
    pub fn with_opacity(mut self, opacity: f64) -> Self {
        self.opacity = opacity.clamp(0.0, 1.0);
        self
    }
}

/// Symbol types for markers and legend items
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum Symbol {
    /// Circle symbol
    Circle,
    /// Square symbol
    Square,
    /// Diamond symbol
    Diamond,
    /// Triangle symbol
    Triangle,
    /// Inverted triangle symbol
    TriangleDown,
    /// Plus symbol
    Plus,
    /// Cross symbol
    Cross,
    /// Star symbol
    Star,
    /// Arrow symbol
    Arrow,
    /// Custom path symbol
    Custom,
}

impl Default for Symbol {
    fn default() -> Self {
        Symbol::Circle
    }
}

impl Symbol {
    /// Get the default size for this symbol
    pub fn default_size(&self) -> f64 {
        match self {
            Symbol::Circle | Symbol::Square | Symbol::Diamond => 8.0,
            Symbol::Triangle | Symbol::TriangleDown => 10.0,
            Symbol::Plus | Symbol::Cross => 12.0,
            Symbol::Star => 14.0,
            Symbol::Arrow => 16.0,
            Symbol::Custom => 8.0,
        }
    }
}
