name: CI/CD 流水线

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  schedule:
    # 每天凌晨2点运行，检查依赖更新
    - cron: '0 2 * * *'

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1

jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
    
    - name: 安装 Rust 工具链
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        components: rustfmt, clippy
        override: true
    
    - name: 缓存 Cargo 依赖
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-cargo-
    
    - name: 检查代码格式
      run: cargo fmt --all -- --check
    
    - name: 运行 Clippy 检查
      run: cargo clippy --all-targets --all-features -- -D warnings
    
    - name: 检查文档生成
      run: cargo doc --all-features --no-deps --document-private-items

  # 构建和测试
  build-test:
    name: 构建和测试
    strategy:
      matrix:
        os: [ubuntu-latest]
        rust: [stable, beta]
    runs-on: ${{ matrix.os }}
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
    
    - name: 安装 Rust 工具链
      uses: actions-rs/toolchain@v1
      with:
        toolchain: ${{ matrix.rust }}
        override: true
    
    - name: 安装系统依赖
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          libgtk-3-dev \
          libxcb-render0-dev \
          libxcb-shape0-dev \
          libxcb-xfixes0-dev \
          libxkbcommon-dev \
          libssl-dev \
          libfontconfig1-dev \
          pkg-config
    
    - name: 缓存 Cargo 依赖
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-${{ matrix.rust }}-cargo-${{ hashFiles('**/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-${{ matrix.rust }}-cargo-
    
    - name: 构建工作空间
      run: cargo build --workspace --all-features
    
    - name: 运行测试
      run: cargo test --workspace --all-features
    
    - name: 构建示例程序
      run: cargo build --examples --all-features

  # ECharts-rs 专项测试
  echarts-rs-tests:
    name: ECharts-rs 模块测试
    runs-on: ubuntu-latest
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
    
    - name: 安装 Rust 工具链
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
    
    - name: 安装系统依赖
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          libgtk-3-dev \
          libxcb-render0-dev \
          libxcb-shape0-dev \
          libxcb-xfixes0-dev \
          libxkbcommon-dev \
          libssl-dev \
          libfontconfig1-dev \
          pkg-config
    
    - name: 缓存 Cargo 依赖
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: echarts-rs-${{ hashFiles('**/Cargo.lock') }}
    
    - name: 测试核心模块
      run: cargo test --package rust-echarts-core
    
    - name: 测试图表模块
      run: cargo test --package rust-echarts-charts
    
    - name: 测试主题模块
      run: cargo test --package rust-echarts-themes
    
    - name: 测试组件模块
      run: cargo test --package rust-echarts-components
    
    - name: 测试渲染器模块
      run: cargo test --package rust-echarts-renderer --features gpui-backend
    
    - name: 构建GPU加速演示
      run: cargo build --example gpu_acceleration_demo --package rust-echarts-renderer --features gpui-backend

  # 安全审计
  security-audit:
    name: 安全审计
    runs-on: ubuntu-latest
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
    
    - name: 安装 Rust 工具链
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
    
    - name: 安装 cargo-audit
      run: cargo install cargo-audit
    
    - name: 运行安全审计
      run: cargo audit

  # 代码覆盖率
  coverage:
    name: 代码覆盖率
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
    
    - name: 安装 Rust 工具链
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        components: llvm-tools-preview
        override: true
    
    - name: 安装系统依赖
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          libgtk-3-dev \
          libxcb-render0-dev \
          libxcb-shape0-dev \
          libxcb-xfixes0-dev \
          libxkbcommon-dev \
          libssl-dev \
          libfontconfig1-dev \
          pkg-config
    
    - name: 安装 cargo-llvm-cov
      run: cargo install cargo-llvm-cov
    
    - name: 缓存 Cargo 依赖
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: coverage-${{ hashFiles('**/Cargo.lock') }}
    
    - name: 生成覆盖率报告
      run: |
        cargo llvm-cov --all-features --workspace --lcov --output-path lcov.info
    
    - name: 生成HTML覆盖率报告
      run: |
        cargo llvm-cov --all-features --workspace --html
    
    - name: 上传覆盖率报告
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: |
          lcov.info
          target/llvm-cov/html/

  # 发布检查（仅主分支）
  release-check:
    name: 发布检查
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    needs: [code-quality, build-test, echarts-rs-tests]
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
    
    - name: 安装 Rust 工具链
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
    
    - name: 安装系统依赖
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          libgtk-3-dev \
          libxcb-render0-dev \
          libxcb-shape0-dev \
          libxcb-xfixes0-dev \
          libxkbcommon-dev \
          libssl-dev \
          libfontconfig1-dev \
          pkg-config
    
    - name: 检查发布构建
      run: |
        cargo check --release --all-features
        cargo build --release --package rust-echarts-core
        cargo build --release --package rust-echarts-charts
        cargo build --release --package rust-echarts-themes
        cargo build --release --package rust-echarts-components
        cargo build --release --package rust-echarts-renderer --features gpui-backend
    
    - name: 生成发布文档
      run: |
        cargo doc --all-features --no-deps
        tar -czf documentation.tar.gz target/doc/
    
    - name: 上传发布制品
      uses: actions/upload-artifact@v3
      with:
        name: release-artifacts
        path: |
          documentation.tar.gz
          target/release/
