//! ECharts-rs 与 GPUI 集成验证
//!
//! 这个演示验证 ECharts-rs 可以在 GPUI 应用中正常工作
//! 重点展示图表数据处理和渲染命令生成功能

use echarts_rs::{LineSeries, BarSeries, PieSeries, ScatterSeries, Color, Series};
use echarts_core::{CartesianCoordinateSystem, Bounds as EchartsBounds};
use gpui::{
    div, px, rgb, size, Window, Application, 
    IntoElement, ParentElement, Render, Styled, WindowOptions, WindowBounds, 
    WindowKind, TitlebarOptions, WindowBackgroundAppearance, FontWeight,
    Context, FocusHandle, Focusable
};

fn main() {
    println!("🚀 启动 ECharts-rs GPUI 集成验证");
    
    Application::new().run(move |cx| {
        println!("📱 GPUI 应用上下文已创建");
        
        let window_size = size(px(1000.0), px(700.0));
        
        let _window = cx.open_window(
            WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(gpui::Bounds::centered(
                    None,
                    window_size,
                    cx,
                ))),
                titlebar: Some(TitlebarOptions {
                    title: Some("ECharts-rs GPUI 集成验证".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                window_background: WindowBackgroundAppearance::Opaque,
                app_id: None,
                window_min_size: Some(size(px(800.0), px(600.0))),
                window_decorations: None,
            },
            |_window, cx| {
                println!("🪟 验证窗口已创建");
                cx.new_view(|cx| EChartsIntegrationDemo::new(cx))
            },
        );
    });
}

/// ECharts 集成验证演示
pub struct EChartsIntegrationDemo {
    focus_handle: FocusHandle,
    line_series: LineSeries,
    bar_series: BarSeries,
    pie_series: PieSeries,
    scatter_series: ScatterSeries,
    validation_results: Vec<ValidationResult>,
}

#[derive(Debug, Clone)]
struct ValidationResult {
    chart_type: String,
    data_points: usize,
    render_commands: usize,
    success: bool,
    message: String,
}

impl EChartsIntegrationDemo {
    fn new(cx: &mut Context<Self>) -> Self {
        println!("🎯 初始化 ECharts 集成验证");
        
        // 创建测试数据
        let line_series = LineSeries::new("销售趋势")
            .data(vec![
                (1.0, 120.0), (2.0, 132.0), (3.0, 101.0), 
                (4.0, 134.0), (5.0, 90.0), (6.0, 230.0)
            ])
            .smooth(true)
            .color(Color::rgb(0.2, 0.6, 1.0));
        
        let bar_series = BarSeries::new("产品销量")
            .data(vec![
                (1.0, 320.0), (2.0, 280.0), (3.0, 250.0), 
                (4.0, 200.0), (5.0, 180.0)
            ])
            .color(Color::rgb(0.9, 0.4, 0.2))
            .bar_width(0.6);
        
        let pie_series = PieSeries::new("市场份额")
            .data(vec![
                ("移动端", 45.0), ("桌面端", 35.0), 
                ("平板端", 15.0), ("其他", 5.0)
            ])
            .radius(0.7)
            .center(0.5, 0.5);
        
        let scatter_data: Vec<(f64, f64)> = (0..15)
            .map(|i| {
                let x = i as f64;
                let y = (x * 0.5).sin() * 50.0 + 100.0;
                (x, y)
            })
            .collect();
        
        let scatter_series = ScatterSeries::new("数据分布")
            .data(scatter_data)
            .symbol_size(6.0)
            .color(Color::rgb(0.4, 0.8, 0.4));
        
        // 执行验证测试
        let mut validation_results = Vec::new();
        
        // 验证线图
        validation_results.push(Self::validate_series(&line_series, "线图"));
        
        // 验证柱图
        validation_results.push(Self::validate_series(&bar_series, "柱图"));
        
        // 验证饼图
        validation_results.push(Self::validate_series(&pie_series, "饼图"));
        
        // 验证散点图
        validation_results.push(Self::validate_series(&scatter_series, "散点图"));
        
        println!("✅ ECharts 集成验证完成");
        
        Self {
            focus_handle: cx.focus_handle(),
            line_series,
            bar_series,
            pie_series,
            scatter_series,
            validation_results,
        }
    }
    
    fn validate_series<T: Series>(series: &T, chart_type: &str) -> ValidationResult {
        println!("🔍 验证 {} 系列...", chart_type);
        
        // 创建坐标系统
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(50.0, 50.0, 600.0, 400.0),
            (0.0, 10.0),
            (0.0, 300.0),
        );
        
        // 尝试生成渲染命令
        match series.render_to_commands(&coord_system) {
            Ok(commands) => {
                let data_points = series.data_len();
                let render_commands = commands.len();
                
                println!("  ✅ {} 验证成功: {} 个数据点, {} 个渲染命令", 
                         chart_type, data_points, render_commands);
                
                ValidationResult {
                    chart_type: chart_type.to_string(),
                    data_points,
                    render_commands,
                    success: true,
                    message: format!("成功生成 {} 个渲染命令", render_commands),
                }
            }
            Err(e) => {
                println!("  ❌ {} 验证失败: {:?}", chart_type, e);
                
                ValidationResult {
                    chart_type: chart_type.to_string(),
                    data_points: series.data_len(),
                    render_commands: 0,
                    success: false,
                    message: format!("渲染失败: {:?}", e),
                }
            }
        }
    }
}

impl Focusable for EChartsIntegrationDemo {
    fn focus_handle(&self, _: &gpui::AppContext) -> FocusHandle {
        self.focus_handle.clone()
    }
}

impl Render for EChartsIntegrationDemo {
    fn render(&mut self, _window: &mut Window, cx: &mut Context<Self>) -> impl IntoElement {
        println!("🎨 渲染 ECharts 集成验证界面");
        
        div()
            .size_full()
            .bg(rgb(0xf8f9fa))
            .p_6()
            .child(
                div()
                    .w_full()
                    .max_w(px(1000.0))
                    .mx_auto()
                    .child(
                        // 标题
                        div()
                            .w_full()
                            .mb_8()
                            .text_center()
                            .child(
                                div()
                                    .text_3xl()
                                    .font_weight(FontWeight::BOLD)
                                    .text_color(rgb(0x1f2937))
                                    .mb_2()
                                    .child("🚀 ECharts-rs GPUI 集成验证")
                            )
                            .child(
                                div()
                                    .text_lg()
                                    .text_color(rgb(0x6b7280))
                                    .child("验证 ECharts-rs 核心功能在 GPUI 环境中的工作状态")
                            )
                    )
                    .child(
                        // 验证结果
                        div()
                            .w_full()
                            .bg(rgb(0xffffff))
                            .border_1()
                            .border_color(rgb(0xe5e7eb))
                            .rounded_lg()
                            .p_6()
                            .shadow_lg()
                            .child(
                                div()
                                    .text_xl()
                                    .font_weight(FontWeight::SEMIBOLD)
                                    .text_color(rgb(0x374151))
                                    .mb_4()
                                    .child("📊 验证结果")
                            )
                            .child(
                                div()
                                    .flex()
                                    .flex_col()
                                    .gap_4()
                                    .children(
                                        self.validation_results.iter().map(|result| {
                                            self.render_validation_result(result)
                                        })
                                    )
                            )
                    )
                    .child(
                        // 总结信息
                        div()
                            .w_full()
                            .mt_8()
                            .p_6()
                            .bg(if self.validation_results.iter().all(|r| r.success) {
                                rgb(0xd1fae5) // 绿色背景
                            } else {
                                rgb(0xfee2e2) // 红色背景
                            })
                            .border_1()
                            .border_color(if self.validation_results.iter().all(|r| r.success) {
                                rgb(0x10b981) // 绿色边框
                            } else {
                                rgb(0xef4444) // 红色边框
                            })
                            .rounded_lg()
                            .child(
                                div()
                                    .text_lg()
                                    .font_weight(FontWeight::SEMIBOLD)
                                    .text_color(if self.validation_results.iter().all(|r| r.success) {
                                        rgb(0x065f46) // 深绿色文字
                                    } else {
                                        rgb(0x991b1b) // 深红色文字
                                    })
                                    .child(if self.validation_results.iter().all(|r| r.success) {
                                        "🎉 所有验证测试通过！ECharts-rs 与 GPUI 集成成功"
                                    } else {
                                        "⚠️ 部分验证测试失败，需要进一步检查"
                                    })
                            )
                            .child(
                                div()
                                    .mt_2()
                                    .text_sm()
                                    .text_color(if self.validation_results.iter().all(|r| r.success) {
                                        rgb(0x047857)
                                    } else {
                                        rgb(0x7f1d1d)
                                    })
                                    .child(format!(
                                        "成功: {}/{} 个图表类型",
                                        self.validation_results.iter().filter(|r| r.success).count(),
                                        self.validation_results.len()
                                    ))
                            )
                    )
            )
    }
}

impl EChartsIntegrationDemo {
    fn render_validation_result(&self, result: &ValidationResult) -> impl IntoElement {
        div()
            .w_full()
            .p_4()
            .bg(if result.success { rgb(0xf0fdf4) } else { rgb(0xfef2f2) })
            .border_1()
            .border_color(if result.success { rgb(0x22c55e) } else { rgb(0xef4444) })
            .rounded_md()
            .child(
                div()
                    .flex()
                    .items_center()
                    .justify_between()
                    .child(
                        div()
                            .flex()
                            .items_center()
                            .gap_3()
                            .child(
                                div()
                                    .text_lg()
                                    .child(if result.success { "✅" } else { "❌" })
                            )
                            .child(
                                div()
                                    .font_weight(FontWeight::MEDIUM)
                                    .text_color(rgb(0x374151))
                                    .child(&result.chart_type)
                            )
                    )
                    .child(
                        div()
                            .text_sm()
                            .text_color(rgb(0x6b7280))
                            .child(format!("{} 数据点", result.data_points))
                    )
            )
            .child(
                div()
                    .mt_2()
                    .text_sm()
                    .text_color(if result.success { rgb(0x16a34a) } else { rgb(0xdc2626) })
                    .child(&result.message)
            )
    }
}
