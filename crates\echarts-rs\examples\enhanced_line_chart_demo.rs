//! 增强版折线图演示
//!
//! 这个演示专注于折线图的完善功能，包括：
//! - 多系列数据支持
//! - 交互功能（悬停、点击、缩放、平移）
//! - 数据点标记和标签
//! - 动画效果
//! - 自定义样式选项
//! - 实时数据更新

use echarts_rs::{
    LineSeries, Color, 
    CartesianCoordinateSystem, Bounds as EchartsBounds, Series
};
use gpui_renderer::GpuiRenderer;
use gpui::*;
use gpui_component::StyledExt;
use std::time::{Duration, Instant};

fn main() {
    println!("🚀 启动增强版折线图演示...");

    App::new().run(move |cx: &mut AppContext| {
        println!("📱 应用程序上下文已创建");
        
        let displays = cx.displays();
        let primary_display = displays.first().expect("需要至少一个显示器");
        let display_size = primary_display.bounds().size;
        
        let window_size = size(px(1400.0), px(900.0));
        println!("🖥️  显示器大小: {:?}, 窗口大小: {:?}", display_size, window_size);
        
        let window_options = WindowOptions {
            window_bounds: Some(WindowBounds::Windowed(Bounds::centered(
                None,
                window_size,
                cx,
            ))),
            titlebar: Some(TitlebarOptions {
                title: Some("📈 增强版折线图演示 - ECharts-rs".into()),
                appears_transparent: false,
                traffic_light_position: None,
            }),
            window_background: WindowBackgroundAppearance::Opaque,
            focus: true,
            show: true,
            kind: WindowKind::Normal,
            is_movable: true,
            display_id: None,
            app_id: "enhanced-line-chart".into(),
            window_decorations: None,
            window_min_size: Some(size(px(1000.0), px(700.0))),
        };
        
        cx.open_window(window_options, |cx| {
            println!("✅ 窗口已创建，正在初始化增强版折线图演示...");
            cx.new_view(|_cx| EnhancedLineChartDemo::new())
        }).expect("无法创建窗口");
        
        println!("🎉 增强版折线图演示启动成功！");
    });
}

/// 数据点结构
#[derive(Debug, Clone)]
struct DataPoint {
    x: f64,
    y: f64,
    label: Option<String>,
    highlighted: bool,
}

impl DataPoint {
    fn new(x: f64, y: f64) -> Self {
        Self {
            x,
            y,
            label: None,
            highlighted: false,
        }
    }
    
    fn with_label(mut self, label: String) -> Self {
        self.label = Some(label);
        self
    }
}

/// 折线图系列配置
#[derive(Debug, Clone)]
struct LineSeriesConfig {
    name: String,
    color: Color,
    line_width: f64,
    show_points: bool,
    point_size: f64,
    smooth: bool,
    dash_pattern: Option<Vec<f64>>,
    fill_area: bool,
    data: Vec<DataPoint>,
}

impl LineSeriesConfig {
    fn new(name: &str, color: Color) -> Self {
        Self {
            name: name.to_string(),
            color,
            line_width: 2.0,
            show_points: true,
            point_size: 4.0,
            smooth: false,
            dash_pattern: None,
            fill_area: false,
            data: Vec::new(),
        }
    }
    
    fn line_width(mut self, width: f64) -> Self {
        self.line_width = width;
        self
    }
    
    fn show_points(mut self, show: bool) -> Self {
        self.show_points = show;
        self
    }
    
    fn point_size(mut self, size: f64) -> Self {
        self.point_size = size;
        self
    }
    
    fn smooth(mut self, smooth: bool) -> Self {
        self.smooth = smooth;
        self
    }
    
    fn dash_pattern(mut self, pattern: Vec<f64>) -> Self {
        self.dash_pattern = Some(pattern);
        self
    }
    
    fn fill_area(mut self, fill: bool) -> Self {
        self.fill_area = fill;
        self
    }
    
    fn data(mut self, data: Vec<DataPoint>) -> Self {
        self.data = data;
        self
    }
}

/// 交互状态
#[derive(Debug, Clone)]
struct InteractionState {
    hovered_point: Option<(usize, usize)>, // (series_index, point_index)
    selected_point: Option<(usize, usize)>,
    zoom_level: f64,
    pan_offset: (f64, f64),
    is_panning: bool,
    last_mouse_pos: Option<Point<Pixels>>,
}

impl Default for InteractionState {
    fn default() -> Self {
        Self {
            hovered_point: None,
            selected_point: None,
            zoom_level: 1.0,
            pan_offset: (0.0, 0.0),
            is_panning: false,
            last_mouse_pos: None,
        }
    }
}

/// 增强版折线图演示应用
struct EnhancedLineChartDemo {
    series_configs: Vec<LineSeriesConfig>,
    coord_system: CartesianCoordinateSystem,
    renderer: GpuiRenderer,
    interaction_state: InteractionState,
    animation_start: Option<Instant>,
    show_grid: bool,
    show_legend: bool,
    show_tooltip: bool,
    auto_update: bool,
    update_interval: Duration,
    last_update: Instant,
}

impl EnhancedLineChartDemo {
    fn new() -> Self {
        println!("🎯 初始化增强版折线图演示...");
        
        // 创建多个系列数据
        let series_configs = Self::create_sample_series();
        
        // 设置坐标系统
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(0.0, 0.0, 20.0, 100.0),
            (0.0, 20.0),
            (0.0, 100.0)
        );
        
        println!("📊 创建了 {} 个折线图系列", series_configs.len());
        
        Self {
            series_configs,
            coord_system,
            renderer: GpuiRenderer::new().with_debug(true),
            interaction_state: InteractionState::default(),
            animation_start: Some(Instant::now()),
            show_grid: true,
            show_legend: true,
            show_tooltip: true,
            auto_update: false,
            update_interval: Duration::from_millis(100),
            last_update: Instant::now(),
        }
    }
    
    /// 创建示例系列数据
    fn create_sample_series() -> Vec<LineSeriesConfig> {
        vec![
            // 主要数据系列
            LineSeriesConfig::new("销售额", Color::BLUE)
                .line_width(3.0)
                .show_points(true)
                .point_size(6.0)
                .fill_area(true)
                .data(vec![
                    DataPoint::new(1.0, 20.0).with_label("1月".to_string()),
                    DataPoint::new(2.0, 35.0).with_label("2月".to_string()),
                    DataPoint::new(3.0, 25.0).with_label("3月".to_string()),
                    DataPoint::new(4.0, 60.0).with_label("4月".to_string()),
                    DataPoint::new(5.0, 45.0).with_label("5月".to_string()),
                    DataPoint::new(6.0, 80.0).with_label("6月".to_string()),
                    DataPoint::new(7.0, 65.0).with_label("7月".to_string()),
                    DataPoint::new(8.0, 90.0).with_label("8月".to_string()),
                    DataPoint::new(9.0, 75.0).with_label("9月".to_string()),
                    DataPoint::new(10.0, 85.0).with_label("10月".to_string()),
                ]),
            
            // 对比数据系列
            LineSeriesConfig::new("目标值", Color::RED)
                .line_width(2.0)
                .show_points(true)
                .point_size(4.0)
                .dash_pattern(vec![5.0, 5.0])
                .data(vec![
                    DataPoint::new(1.0, 30.0),
                    DataPoint::new(2.0, 40.0),
                    DataPoint::new(3.0, 35.0),
                    DataPoint::new(4.0, 50.0),
                    DataPoint::new(5.0, 55.0),
                    DataPoint::new(6.0, 70.0),
                    DataPoint::new(7.0, 75.0),
                    DataPoint::new(8.0, 80.0),
                    DataPoint::new(9.0, 85.0),
                    DataPoint::new(10.0, 90.0),
                ]),
            
            // 平滑曲线系列
            LineSeriesConfig::new("趋势线", Color::GREEN)
                .line_width(2.5)
                .show_points(false)
                .smooth(true)
                .data(vec![
                    DataPoint::new(1.0, 15.0),
                    DataPoint::new(2.0, 25.0),
                    DataPoint::new(3.0, 30.0),
                    DataPoint::new(4.0, 45.0),
                    DataPoint::new(5.0, 50.0),
                    DataPoint::new(6.0, 65.0),
                    DataPoint::new(7.0, 70.0),
                    DataPoint::new(8.0, 80.0),
                    DataPoint::new(9.0, 85.0),
                    DataPoint::new(10.0, 95.0),
                ]),
        ]
    }
    
    /// 更新数据（用于实时更新演示）
    fn update_data(&mut self) {
        if !self.auto_update {
            return;
        }
        
        let now = Instant::now();
        if now.duration_since(self.last_update) < self.update_interval {
            return;
        }
        
        // 模拟数据更新
        for series in &mut self.series_configs {
            for point in &mut series.data {
                // 添加小幅随机变化
                let variation = (rand::random::<f64>() - 0.5) * 2.0;
                point.y = (point.y + variation).max(0.0).min(100.0);
            }
        }
        
        self.last_update = now;
        println!("📊 数据已更新");
    }
}
