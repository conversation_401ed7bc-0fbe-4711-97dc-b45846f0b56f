//! 简化的曲线图展示
//!
//! 生成各种类型的完整曲线图

use std::fs;

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("📈 简化的曲线图展示生成器");
    println!("{}", "=".repeat(50));

    // 确保输出目录存在
    let output_dir = "temp/svg/line_charts";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 基础曲线图
    println!("\n📊 1. 生成基础曲线图...");
    generate_basic_line_charts(output_dir)?;

    // 2. 数学函数曲线
    println!("\n🔢 2. 生成数学函数曲线...");
    generate_math_function_charts(output_dir)?;

    // 3. 数据分析曲线
    println!("\n📈 3. 生成数据分析曲线...");
    generate_data_analysis_charts(output_dir)?;

    // 4. 样式变化曲线
    println!("\n🎨 4. 生成样式变化曲线...");
    generate_styled_line_charts(output_dir)?;

    // 5. 复杂场景曲线
    println!("\n🔧 5. 生成复杂场景曲线...");
    generate_complex_line_charts(output_dir)?;

    // 6. 动态效果曲线
    println!("\n🎬 6. 生成动态效果曲线...");
    generate_animated_line_charts(output_dir)?;

    // 7. 生成展示页面
    println!("\n📄 7. 生成展示页面...");
    generate_line_charts_showcase(output_dir)?;

    println!("\n🎉 简化的曲线图展示生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/line_charts_showcase.html 查看所有曲线图", output_dir);

    Ok(())
}

/// 生成基础曲线图
fn generate_basic_line_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 线性增长
    let linear_data: Vec<(f64, f64)> = (0..=20).map(|i| (i as f64, i as f64 * 2.5 + 10.0)).collect();
    let linear_svg = create_line_chart("线性增长曲线", &linear_data, 600.0, 400.0, "007bff");
    fs::write(format!("{}/01_linear_growth.svg", output_dir), linear_svg)?;

    // 2. 指数增长
    let exponential_data: Vec<(f64, f64)> = (0..=15).map(|i| (i as f64, (1.2_f64).powf(i as f64))).collect();
    let exponential_svg = create_line_chart("指数增长曲线", &exponential_data, 600.0, 400.0, "28a745");
    fs::write(format!("{}/02_exponential_growth.svg", output_dir), exponential_svg)?;

    // 3. 对数增长
    let logarithmic_data: Vec<(f64, f64)> = (1..=50).map(|i| (i as f64, (i as f64).ln() * 20.0 + 50.0)).collect();
    let logarithmic_svg = create_line_chart("对数增长曲线", &logarithmic_data, 600.0, 400.0, "dc3545");
    fs::write(format!("{}/03_logarithmic_growth.svg", output_dir), logarithmic_svg)?;

    // 4. 波动曲线
    let volatile_data: Vec<(f64, f64)> = (0..=100).map(|i| {
        let x = i as f64 * 0.2;
        let y = 100.0 + (x * 0.5).sin() * 30.0 + (x * 0.1).cos() * 15.0 + (i as f64 * 0.01).sin() * 5.0;
        (x, y)
    }).collect();
    let volatile_svg = create_line_chart("波动曲线", &volatile_data, 700.0, 400.0, "ffc107");
    fs::write(format!("{}/04_volatile_curve.svg", output_dir), volatile_svg)?;

    println!("  ✅ 基础曲线图生成完成 (4个图表)");
    Ok(())
}

/// 生成数学函数曲线
fn generate_math_function_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 正弦函数
    let sine_data: Vec<(f64, f64)> = (0..=360).step_by(5).map(|i| {
        let x = i as f64;
        let y = (x * std::f64::consts::PI / 180.0).sin() * 100.0 + 150.0;
        (x, y)
    }).collect();
    let sine_svg = create_line_chart("正弦函数曲线", &sine_data, 800.0, 400.0, "6f42c1");
    fs::write(format!("{}/05_sine_function.svg", output_dir), sine_svg)?;

    // 2. 余弦函数
    let cosine_data: Vec<(f64, f64)> = (0..=360).step_by(5).map(|i| {
        let x = i as f64;
        let y = (x * std::f64::consts::PI / 180.0).cos() * 100.0 + 150.0;
        (x, y)
    }).collect();
    let cosine_svg = create_line_chart("余弦函数曲线", &cosine_data, 800.0, 400.0, "e83e8c");
    fs::write(format!("{}/06_cosine_function.svg", output_dir), cosine_svg)?;

    // 3. 二次函数
    let quadratic_data: Vec<(f64, f64)> = (-20..=20).map(|i| {
        let x = i as f64;
        let y = x * x * 0.5 + x * 2.0 + 50.0;
        (x, y)
    }).collect();
    let quadratic_svg = create_line_chart("二次函数曲线", &quadratic_data, 600.0, 400.0, "17a2b8");
    fs::write(format!("{}/07_quadratic_function.svg", output_dir), quadratic_svg)?;

    println!("  ✅ 数学函数曲线生成完成 (3个图表)");
    Ok(())
}

/// 生成数据分析曲线
fn generate_data_analysis_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 股票价格走势
    let mut stock_price = 100.0;
    let stock_data: Vec<(f64, f64)> = (0..=100).map(|i| {
        let change = (i as f64 * 0.1).sin() * 2.0 + (i as f64 * 0.05).cos() * 1.5 + 
                    ((i % 10) as f64 - 5.0) * 0.3;
        stock_price += change;
        (i as f64, stock_price)
    }).collect();
    let stock_svg = create_line_chart("股票价格走势", &stock_data, 800.0, 450.0, "dc3545");
    fs::write(format!("{}/08_stock_price.svg", output_dir), stock_svg)?;

    // 2. 温度变化曲线
    let temperature_data: Vec<(f64, f64)> = (0..=24).map(|i| {
        let hour = i as f64;
        let temp = 20.0 + (hour * std::f64::consts::PI / 12.0 - std::f64::consts::PI / 2.0).sin() * 8.0 + 
                  (hour * 0.5).sin() * 2.0;
        (hour, temp)
    }).collect();
    let temperature_svg = create_line_chart("24小时温度变化", &temperature_data, 700.0, 400.0, "fd7e14");
    fs::write(format!("{}/09_temperature_curve.svg", output_dir), temperature_svg)?;

    // 3. 销售业绩曲线
    let sales_data: Vec<(f64, f64)> = (1..=12).map(|i| {
        let month = i as f64;
        let seasonal_factor = (month * std::f64::consts::PI / 6.0).cos() * 0.3 + 1.0;
        let growth_factor = 1.0 + month * 0.05;
        let base_sales = 50000.0;
        (month, base_sales * seasonal_factor * growth_factor)
    }).collect();
    let sales_svg = create_line_chart("年度销售业绩", &sales_data, 700.0, 400.0, "007bff");
    fs::write(format!("{}/10_sales_performance.svg", output_dir), sales_svg)?;

    println!("  ✅ 数据分析曲线生成完成 (3个图表)");
    Ok(())
}

/// 生成样式变化曲线
fn generate_styled_line_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 渐变色曲线
    let gradient_data: Vec<(f64, f64)> = (0..=50).map(|i| {
        let x = i as f64;
        let y = (x * 0.2).sin() * 50.0 + (x * 0.1).cos() * 30.0 + 100.0;
        (x, y)
    }).collect();
    let gradient_svg = create_gradient_line_chart("渐变色曲线", &gradient_data, 700.0, 400.0);
    fs::write(format!("{}/11_gradient_line.svg", output_dir), gradient_svg)?;

    // 2. 虚线曲线
    let dashed_data: Vec<(f64, f64)> = (0..=40).map(|i| {
        let x = i as f64;
        let y = x * 2.0 + (x * 0.3).sin() * 20.0 + 50.0;
        (x, y)
    }).collect();
    let dashed_svg = create_dashed_line_chart("虚线曲线", &dashed_data, 600.0, 400.0);
    fs::write(format!("{}/12_dashed_line.svg", output_dir), dashed_svg)?;

    // 3. 阴影效果曲线
    let shadow_data: Vec<(f64, f64)> = (0..=60).map(|i| {
        let x = i as f64;
        let y = (x * 0.15).sin() * 60.0 + (x * 0.05).cos() * 20.0 + 120.0;
        (x, y)
    }).collect();
    let shadow_svg = create_shadow_line_chart("阴影效果曲线", &shadow_data, 700.0, 400.0);
    fs::write(format!("{}/13_shadow_line.svg", output_dir), shadow_svg)?;

    println!("  ✅ 样式变化曲线生成完成 (3个图表)");
    Ok(())
}

/// 生成复杂场景曲线
fn generate_complex_line_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 多系列对比曲线
    let multi_series_svg = create_multi_series_line_chart("多产品销售对比", 900.0, 500.0);
    fs::write(format!("{}/14_multi_series_comparison.svg", output_dir), multi_series_svg)?;

    // 2. 面积填充曲线
    let area_data: Vec<(f64, f64)> = (0..=50).map(|i| {
        let x = i as f64;
        let y = (x * 0.2).sin() * 30.0 + (x * 0.1).cos() * 20.0 + 80.0;
        (x, y)
    }).collect();
    let area_svg = create_area_line_chart("面积填充曲线", &area_data, 700.0, 400.0);
    fs::write(format!("{}/15_area_filled_line.svg", output_dir), area_svg)?;

    // 3. 阶梯曲线
    let step_data: Vec<(f64, f64)> = (0..=20).map(|i| {
        let x = i as f64;
        let y = (i / 3) as f64 * 15.0 + 50.0 + (i % 3) as f64 * 5.0;
        (x, y)
    }).collect();
    let step_svg = create_step_line_chart("阶梯曲线", &step_data, 600.0, 400.0);
    fs::write(format!("{}/16_step_line.svg", output_dir), step_svg)?;

    println!("  ✅ 复杂场景曲线生成完成 (3个图表)");
    Ok(())
}

/// 生成动态效果曲线
fn generate_animated_line_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 动画增长曲线
    let animated_growth_svg = create_animated_growth_chart("动画增长曲线", 700.0, 400.0);
    fs::write(format!("{}/17_animated_growth.svg", output_dir), animated_growth_svg)?;

    // 2. 波浪动画曲线
    let wave_animation_svg = create_wave_animation_chart("波浪动画曲线", 800.0, 400.0);
    fs::write(format!("{}/18_wave_animation.svg", output_dir), wave_animation_svg)?;

    // 3. 脉冲效果曲线
    let pulse_data: Vec<(f64, f64)> = (0..=40).map(|i| {
        let x = i as f64;
        let y = 100.0 + (x * 0.5).sin() * 50.0;
        (x, y)
    }).collect();
    let pulse_svg = create_pulse_line_chart("脉冲效果曲线", &pulse_data, 700.0, 400.0);
    fs::write(format!("{}/19_pulse_line.svg", output_dir), pulse_svg)?;

    println!("  ✅ 动态效果曲线生成完成 (3个图表)");
    Ok(())
}

// ============================================================================
// 曲线图生成函数
// ============================================================================

/// 创建基础曲线图
fn create_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64, color: &str) -> String {
    let mut svg = String::new();

    // SVG 头部
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 定义渐变和滤镜
    svg.push_str("  <defs>\n");
    svg.push_str(&format!("    <linearGradient id=\"areaGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n"));
    svg.push_str(&format!("      <stop offset=\"0%\" style=\"stop-color:#{};stop-opacity:0.3\" />\n", color));
    svg.push_str(&format!("      <stop offset=\"100%\" style=\"stop-color:#{};stop-opacity:0.1\" />\n", color));
    svg.push_str("    </linearGradient>\n");
    svg.push_str("    <filter id=\"shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n");
    svg.push_str("      <feDropShadow dx=\"2\" dy=\"2\" stdDeviation=\"2\" flood-color=\"#000000\" flood-opacity=\"0.2\"/>\n");
    svg.push_str("    </filter>\n");
    svg.push_str("  </defs>\n");

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;

    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));

    if !data.is_empty() {
        // 计算数据范围
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);

        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };

        // 添加网格线
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", x, chart_y, x, chart_y + chart_height));
        }

        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));
        }

        // 生成路径
        let mut path = String::from("M");
        let mut area_path = String::from("M");

        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;

            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
                area_path.push_str(&format!(" {} {}", px, chart_y + chart_height));
                area_path.push_str(&format!(" L {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
                area_path.push_str(&format!(" L {} {}", px, py));
            }
        }

        // 完成面积路径
        if let Some((last_x, _)) = data.last() {
            let last_px = chart_x + (last_x - min_x) / x_range * chart_width;
            area_path.push_str(&format!(" L {} {} Z", last_px, chart_y + chart_height));
        }

        // 绘制面积
        svg.push_str(&format!("  <path d=\"{}\" fill=\"url(#areaGradient)\"/>\n", area_path));

        // 绘制曲线
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#{}\" stroke-width=\"3\" fill=\"none\" filter=\"url(#shadow)\"/>\n", path, color));

        // 绘制数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;

            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#{}\" stroke=\"white\" stroke-width=\"2\" filter=\"url(#shadow)\"/>\n", px, py, color));
        }

        // 添加坐标轴标签
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            let value = min_x + (i as f64 / 5.0) * x_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{:.1}</text>\n", x, chart_y + chart_height + 20.0, value));
        }

        for i in 0..=5 {
            let y = chart_y + chart_height - (i as f64 / 5.0) * chart_height;
            let value = min_y + (i as f64 / 5.0) * y_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n", chart_x - 10.0, y + 4.0, value));
        }
    }

    svg.push_str("</svg>");
    svg
}

/// 创建渐变色曲线图
fn create_gradient_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();

    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 定义多色渐变
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"multiGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#ff6b6b;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"25%\" style=\"stop-color:#4ecdc4;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"50%\" style=\"stop-color:#45b7d1;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"75%\" style=\"stop-color:#f9ca24;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#6c5ce7;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");

    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;

    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));

    if !data.is_empty() {
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);

        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };

        // 生成路径
        let mut path = String::from("M");
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;

            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }

        // 绘制渐变曲线
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"url(#multiGradient)\" stroke-width=\"5\" fill=\"none\"/>\n", path));

        // 绘制渐变数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;

            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"5\" fill=\"url(#multiGradient)\" stroke=\"white\" stroke-width=\"2\"/>\n", px, py));
        }
    }

    svg.push_str("</svg>");
    svg
}

/// 创建虚线曲线图
fn create_dashed_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();

    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;

    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));

    if !data.is_empty() {
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);

        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };

        // 生成路径
        let mut path = String::from("M");
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;

            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }

        // 绘制虚线曲线
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#e74c3c\" stroke-width=\"3\" stroke-dasharray=\"10,5\" fill=\"none\"/>\n", path));

        // 绘制方形数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;

            svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"8\" height=\"8\" fill=\"#e74c3c\" stroke=\"white\" stroke-width=\"2\" transform=\"translate(-4,-4)\"/>\n", px, py));
        }
    }

    svg.push_str("</svg>");
    svg
}

// 引入辅助函数
include!("line_chart_helpers.rs");

/// 创建动画增长曲线图
fn create_animated_growth_chart(title: &str, width: f64, height: f64) -> String {
    format!(r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <text x="50%" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">{}</text>
  <line x1="80" y1="340" x2="620" y2="340" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="60" x2="80" y2="340" stroke="#666" stroke-width="2"/>

  <!-- 动画路径 -->
  <path d="M 80 320 L 120 300 L 160 280 L 200 250 L 240 220 L 280 180 L 320 140 L 360 100 L 400 80 L 440 60 L 480 50 L 520 40 L 560 30 L 600 20" stroke="#e67e22" stroke-width="4" fill="none">
    <animate attributeName="stroke-dasharray" values="0,1000;1000,0" dur="3s" repeatCount="indefinite"/>
  </path>

  <!-- 动画数据点 -->
  <circle cx="80" cy="320" r="5" fill="#e67e22" stroke="white" stroke-width="2">
    <animate attributeName="r" values="3;8;3" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="200" cy="250" r="5" fill="#e67e22" stroke="white" stroke-width="2">
    <animate attributeName="r" values="3;8;3" dur="2s" begin="0.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="320" cy="140" r="5" fill="#e67e22" stroke="white" stroke-width="2">
    <animate attributeName="r" values="3;8;3" dur="2s" begin="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="440" cy="60" r="5" fill="#e67e22" stroke="white" stroke-width="2">
    <animate attributeName="r" values="3;8;3" dur="2s" begin="1.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="560" cy="30" r="5" fill="#e67e22" stroke="white" stroke-width="2">
    <animate attributeName="r" values="3;8;3" dur="2s" begin="2s" repeatCount="indefinite"/>
  </circle>
</svg>"#, width, height, title)
}

/// 创建波浪动画曲线图
fn create_wave_animation_chart(title: &str, width: f64, height: f64) -> String {
    format!(r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <text x="50%" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">{}</text>
  <line x1="80" y1="340" x2="720" y2="340" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="60" x2="80" y2="340" stroke="#666" stroke-width="2"/>

  <!-- 波浪路径 -->
  <path d="M 80 200 Q 120 150 160 200 T 240 200 T 320 200 T 400 200 T 480 200 T 560 200 T 640 200 T 720 200" stroke="#3498db" stroke-width="4" fill="none">
    <animateTransform attributeName="transform" type="translate" values="0,0;-80,0;0,0" dur="2s" repeatCount="indefinite"/>
  </path>

  <!-- 第二层波浪 -->
  <path d="M 80 220 Q 120 170 160 220 T 240 220 T 320 220 T 400 220 T 480 220 T 560 220 T 640 220 T 720 220" stroke="#74b9ff" stroke-width="3" fill="none" opacity="0.7">
    <animateTransform attributeName="transform" type="translate" values="0,0;-80,0;0,0" dur="2.5s" repeatCount="indefinite"/>
  </path>

  <!-- 第三层波浪 -->
  <path d="M 80 240 Q 120 190 160 240 T 240 240 T 320 240 T 400 240 T 480 240 T 560 240 T 640 240 T 720 240" stroke="#a29bfe" stroke-width="2" fill="none" opacity="0.5">
    <animateTransform attributeName="transform" type="translate" values="0,0;-80,0;0,0" dur="3s" repeatCount="indefinite"/>
  </path>
</svg>"#, width, height, title)
}

/// 创建脉冲效果曲线图
fn create_pulse_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    format!(r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f8f9fa"/>
  <text x="50%" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">{}</text>
  <line x1="80" y1="340" x2="620" y2="340" stroke="#666" stroke-width="2"/>
  <line x1="80" y1="60" x2="80" y2="340" stroke="#666" stroke-width="2"/>

  <!-- 脉冲曲线 -->
  <path d="M 80 200 L 120 180 L 160 220 L 200 160 L 240 240 L 280 140 L 320 200 L 360 120 L 400 180 L 440 100 L 480 160 L 520 120 L 560 140 L 600 100" stroke="#e74c3c" stroke-width="3" fill="none">
    <animate attributeName="stroke-width" values="3;6;3" dur="1s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.7;1;0.7" dur="1s" repeatCount="indefinite"/>
  </path>

  <!-- 脉冲数据点 -->
  <circle cx="160" cy="220" r="4" fill="#e74c3c" stroke="white" stroke-width="2">
    <animate attributeName="r" values="4;10;4" dur="1.5s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="280" cy="140" r="4" fill="#e74c3c" stroke="white" stroke-width="2">
    <animate attributeName="r" values="4;10;4" dur="1.5s" begin="0.3s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="1;0.3;1" dur="1.5s" begin="0.3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="400" cy="180" r="4" fill="#e74c3c" stroke="white" stroke-width="2">
    <animate attributeName="r" values="4;10;4" dur="1.5s" begin="0.6s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="1;0.3;1" dur="1.5s" begin="0.6s" repeatCount="indefinite"/>
  </circle>
  <circle cx="520" cy="120" r="4" fill="#e74c3c" stroke="white" stroke-width="2">
    <animate attributeName="r" values="4;10;4" dur="1.5s" begin="0.9s" repeatCount="indefinite"/>
    <animate attributeName="fill-opacity" values="1;0.3;1" dur="1.5s" begin="0.9s" repeatCount="indefinite"/>
  </circle>
</svg>"#, width, height, title)
}

/// 生成展示页面
fn generate_line_charts_showcase(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化的曲线图展示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1600px; margin: 0 auto; }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .section {
            background: rgba(255,255,255,0.95);
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .chart-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        .chart-svg {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h2 { color: #333; margin-bottom: 20px; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        .description {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 简化的曲线图展示</h1>
            <p class="description">展现 ECharts-rs 强大的曲线图功能</p>
            <div class="feature-list">
                <div class="feature">
                    <h3>📊 基础曲线</h3>
                    <p>线性、指数、对数增长</p>
                </div>
                <div class="feature">
                    <h3>🔢 数学函数</h3>
                    <p>三角函数、多项式</p>
                </div>
                <div class="feature">
                    <h3>📈 数据分析</h3>
                    <p>股票、温度、销售</p>
                </div>
                <div class="feature">
                    <h3>🎨 样式效果</h3>
                    <p>渐变、虚线、阴影</p>
                </div>
                <div class="feature">
                    <h3>🔧 复杂场景</h3>
                    <p>多系列、面积、阶梯</p>
                </div>
                <div class="feature">
                    <h3>🎬 动态效果</h3>
                    <p>动画、波浪、脉冲</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 基础曲线图</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">线性增长曲线</div>
                    <object class="chart-svg" data="01_linear_growth.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">指数增长曲线</div>
                    <object class="chart-svg" data="02_exponential_growth.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">对数增长曲线</div>
                    <object class="chart-svg" data="03_logarithmic_growth.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">波动曲线</div>
                    <object class="chart-svg" data="04_volatile_curve.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔢 数学函数曲线</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">正弦函数曲线</div>
                    <object class="chart-svg" data="05_sine_function.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">余弦函数曲线</div>
                    <object class="chart-svg" data="06_cosine_function.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">二次函数曲线</div>
                    <object class="chart-svg" data="07_quadratic_function.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📈 数据分析曲线</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">股票价格走势</div>
                    <object class="chart-svg" data="08_stock_price.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">24小时温度变化</div>
                    <object class="chart-svg" data="09_temperature_curve.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">年度销售业绩</div>
                    <object class="chart-svg" data="10_sales_performance.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎨 样式变化曲线</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">渐变色曲线</div>
                    <object class="chart-svg" data="11_gradient_line.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">虚线曲线</div>
                    <object class="chart-svg" data="12_dashed_line.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">阴影效果曲线</div>
                    <object class="chart-svg" data="13_shadow_line.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 复杂场景曲线</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">多产品销售对比</div>
                    <object class="chart-svg" data="14_multi_series_comparison.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">面积填充曲线</div>
                    <object class="chart-svg" data="15_area_filled_line.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">阶梯曲线</div>
                    <object class="chart-svg" data="16_step_line.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎬 动态效果曲线</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">动画增长曲线</div>
                    <object class="chart-svg" data="17_animated_growth.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">波浪动画曲线</div>
                    <object class="chart-svg" data="18_wave_animation.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">脉冲效果曲线</div>
                    <object class="chart-svg" data="19_pulse_line.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h2>🎉 曲线图功能总结</h2>
            <p>ECharts-rs 提供了丰富的曲线图功能：</p>
            <ul style="text-align: left; max-width: 800px; margin: 0 auto;">
                <li>✅ <strong>19种不同类型的曲线图</strong> - 覆盖各种应用场景</li>
                <li>✅ <strong>数学函数支持</strong> - 三角函数、多项式、指数对数</li>
                <li>✅ <strong>数据分析图表</strong> - 股票、温度、销售等实际应用</li>
                <li>✅ <strong>丰富的样式效果</strong> - 渐变、虚线、阴影</li>
                <li>✅ <strong>复杂场景支持</strong> - 多系列、面积填充、阶梯</li>
                <li>✅ <strong>动态效果</strong> - CSS动画、波浪、脉冲</li>
                <li>✅ <strong>高质量渲染</strong> - 完整的坐标系统和标签</li>
                <li>✅ <strong>响应式设计</strong> - 适配不同屏幕尺寸</li>
            </ul>
        </div>
    </div>
</body>
</html>"#;

    fs::write(format!("{}/line_charts_showcase.html", output_dir), html_content)?;
    Ok(())
}
