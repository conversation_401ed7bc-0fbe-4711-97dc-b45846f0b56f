[package]
name = "chart"
version = "0.1.0"
edition = "2021"

[dependencies]
gpui.workspace = true
gpui-component = { workspace = true, features = ["webview"] }
usui.workspace = true
log.workspace = true
tracing-subscriber.workspace = true
gpui-component-macros.workspace = true

chrono = "0.4"
lyon="1.0.1"

num-traits = "0.2"
rust_decimal = { version = "1.37.0", optional = true }

[lints]
workspace = true
