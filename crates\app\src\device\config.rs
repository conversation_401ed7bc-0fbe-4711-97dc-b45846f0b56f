use serde::{Deserialize, Serialize};
use std::time::Duration;

/// 设备配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct DeviceConfig {
    /// 串口配置
    pub serial: SerialConfig,
    /// 数据采集配置
    pub data_collection: DataCollectionConfig,
}

/// 串口配置
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SerialConfig {
    /// 串口名称（例如 "COM1" 或 "/dev/ttyUSB0"）
    pub port: String,
    /// 波特率
    pub baud_rate: u32,
    /// 数据位
    pub data_bits: u8,
    /// 校验位
    pub parity: Parity,
    /// 停止位
    pub stop_bits: u8,
    /// 流控制
    pub flow_control: FlowControl,
    /// 超时设置（毫秒）
    pub timeout_ms: u64,
}

impl Default for SerialConfig {
    fn default() -> Self {
        Self {
            port: String::new(),
            baud_rate: 115200,
            data_bits: 8,
            parity: Parity::None,
            stop_bits: 1,
            flow_control: FlowControl::None,
            timeout_ms: 1000,
        }
    }
}

/// 校验位配置
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialize, Deserialize, PartialEq, Eq)]
pub enum Parity {
    /// 无校验
    None,
    /// 奇校验
    Odd,
    /// 偶校验
    Even,
}

/// 流控制配置
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum FlowControl {
    /// 无流控制
    None,
    /// 硬件流控制
    Hardware,
    /// 软件流控制
    Software,
}

/// 数据采集配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataCollectionConfig {
    /// 采样率（Hz）
    pub sample_rate: u32,
    /// 缓冲区大小（字节）
    pub buffer_size: usize,
    /// 超时设置（毫秒）
    pub timeout_ms: u64,
    /// 自动重连设置
    pub auto_reconnect: bool,
    /// 重连间隔（毫秒）
    pub reconnect_interval_ms: u64,
}

impl Default for DataCollectionConfig {
    fn default() -> Self {
        Self {
            sample_rate: 1000,
            buffer_size: 8192,
            timeout_ms: 5000,
            auto_reconnect: true,
            reconnect_interval_ms: 5000,
        }
    }
}

impl SerialConfig {
    /// 获取超时设置的Duration
    pub fn timeout(&self) -> Duration {
        Duration::from_millis(self.timeout_ms)
    }
}

impl DataCollectionConfig {
    /// 获取超时设置的Duration
    pub fn timeout(&self) -> Duration {
        Duration::from_millis(self.timeout_ms)
    }

    /// 获取重连间隔的Duration
    pub fn reconnect_interval(&self) -> Duration {
        Duration::from_millis(self.reconnect_interval_ms)
    }
}
