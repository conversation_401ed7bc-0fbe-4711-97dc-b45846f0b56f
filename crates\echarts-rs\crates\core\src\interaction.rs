//! Chart interaction modes and handlers
//!
//! This module provides different interaction modes for charts,
//! including selection, zooming, panning, and data editing.

use crate::{Bounds, InteractionEvent, KeyEvent, MouseEvent, Point};
use serde::{Deserialize, Serialize};

/// Chart interaction modes
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>h, Serialize, Deserialize)]
pub enum InteractionMode {
    /// Default mode - basic interactions
    Default,

    /// Zoom mode - mouse interactions zoom the chart
    Zoom,

    /// Pan mode - mouse drag pans the chart
    Pan,

    /// Select mode - mouse drag selects data points
    Select,

    /// Brush mode - mouse drag creates selection brush
    Brush,

    /// Edit mode - allows editing data points
    Edit,

    /// Measure mode - allows measuring distances
    Measure,

    /// Custom mode with specific name
    Custom(String),
}

impl Default for InteractionMode {
    fn default() -> Self {
        Self::Default
    }
}

/// Interaction state
#[derive(Debug, Clone)]
pub struct InteractionState {
    /// Current interaction mode
    pub mode: InteractionMode,

    /// Whether mouse is currently pressed
    pub mouse_down: bool,

    /// Last mouse position
    pub last_mouse_position: Option<Point>,

    /// Mouse down position (for drag operations)
    pub mouse_down_position: Option<Point>,

    /// Current selection bounds
    pub selection_bounds: Option<Bounds>,

    /// Selected data points (series_index, data_index)
    pub selected_points: Vec<(usize, usize)>,

    /// Whether currently dragging
    pub is_dragging: bool,

    /// Drag threshold (minimum distance to start drag)
    pub drag_threshold: f64,
}

impl Default for InteractionState {
    fn default() -> Self {
        Self {
            mode: InteractionMode::Default,
            mouse_down: false,
            last_mouse_position: None,
            mouse_down_position: None,
            selection_bounds: None,
            selected_points: Vec::new(),
            is_dragging: false,
            drag_threshold: 5.0,
        }
    }
}

/// Interaction configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InteractionConfig {
    /// Default interaction mode
    pub default_mode: InteractionMode,

    /// Whether to allow mode switching
    pub allow_mode_switching: bool,

    /// Whether to show interaction hints
    pub show_hints: bool,

    /// Whether to enable keyboard shortcuts for mode switching
    pub keyboard_mode_switching: bool,

    /// Drag threshold in pixels
    pub drag_threshold: f64,

    /// Double click threshold in milliseconds
    pub double_click_threshold: u64,

    /// Whether to enable right-click context menu
    pub enable_context_menu: bool,
}

impl Default for InteractionConfig {
    fn default() -> Self {
        Self {
            default_mode: InteractionMode::Default,
            allow_mode_switching: true,
            show_hints: true,
            keyboard_mode_switching: true,
            drag_threshold: 5.0,
            double_click_threshold: 300,
            enable_context_menu: true,
        }
    }
}

/// Interaction result
#[derive(Debug, Clone)]
pub enum InteractionResult {
    /// No action needed
    None,

    /// Request chart redraw
    Redraw,

    /// Request data update
    UpdateData,

    /// Request mode change
    ChangeMode(InteractionMode),

    /// Request zoom operation
    Zoom { center: Point, factor: f64 },

    /// Request pan operation
    Pan { delta: Point },

    /// Request selection
    Select {
        bounds: Bounds,
        points: Vec<(usize, usize)>,
    },

    /// Request context menu
    ContextMenu { position: Point, items: Vec<String> },

    /// Custom result
    Custom {
        name: String,
        data: serde_json::Value,
    },
}

/// Interaction handler
#[derive(Debug, Clone)]
pub struct InteractionHandler {
    /// Configuration
    pub config: InteractionConfig,

    /// Current state
    pub state: InteractionState,

    /// Last click time for double-click detection
    last_click_time: Option<std::time::Instant>,
}

impl Default for InteractionHandler {
    fn default() -> Self {
        Self::new()
    }
}

impl InteractionHandler {
    /// Create a new interaction handler
    pub fn new() -> Self {
        Self {
            config: InteractionConfig::default(),
            state: InteractionState::default(),
            last_click_time: None,
        }
    }

    /// Create with custom configuration
    pub fn with_config(config: InteractionConfig) -> Self {
        Self {
            config,
            state: InteractionState::default(),
            last_click_time: None,
        }
    }

    /// Set interaction mode
    pub fn set_mode(&mut self, mode: InteractionMode) {
        if self.config.allow_mode_switching {
            self.state.mode = mode;
            self.reset_state();
        }
    }

    /// Get current interaction mode
    pub fn get_mode(&self) -> &InteractionMode {
        &self.state.mode
    }

    /// Reset interaction state
    pub fn reset_state(&mut self) {
        self.state.mouse_down = false;
        self.state.last_mouse_position = None;
        self.state.mouse_down_position = None;
        self.state.is_dragging = false;
        self.state.selection_bounds = None;
    }

    /// Handle interaction event
    pub fn handle_event(&mut self, event: &InteractionEvent) -> InteractionResult {
        match event {
            InteractionEvent::Mouse(mouse_event) => self.handle_mouse_event(mouse_event),
            InteractionEvent::Key(key_event) => self.handle_key_event(key_event),
            _ => InteractionResult::None,
        }
    }

    /// Handle mouse event
    fn handle_mouse_event(&mut self, event: &MouseEvent) -> InteractionResult {
        use crate::MouseEventType;

        match event.event_type {
            MouseEventType::MouseDown => self.handle_mouse_down(event),
            MouseEventType::MouseUp => self.handle_mouse_up(event),
            MouseEventType::MouseMove => self.handle_mouse_move(event),
            MouseEventType::Click => self.handle_mouse_click(event),
            MouseEventType::DoubleClick => self.handle_double_click(event),
            MouseEventType::ContextMenu => self.handle_context_menu(event),
            MouseEventType::Wheel => self.handle_mouse_wheel(event),
            _ => InteractionResult::None,
        }
    }

    /// Handle mouse down event
    fn handle_mouse_down(&mut self, event: &MouseEvent) -> InteractionResult {
        self.state.mouse_down = true;
        self.state.mouse_down_position = Some(event.position);
        self.state.last_mouse_position = Some(event.position);

        match self.state.mode {
            InteractionMode::Select | InteractionMode::Brush => {
                self.state.selection_bounds =
                    Some(Bounds::from_two_points(event.position, event.position));
                InteractionResult::Redraw
            }
            _ => InteractionResult::None,
        }
    }

    /// Handle mouse up event
    fn handle_mouse_up(&mut self, event: &MouseEvent) -> InteractionResult {
        let was_dragging = self.state.is_dragging;

        self.state.mouse_down = false;
        self.state.is_dragging = false;

        let result = match self.state.mode {
            InteractionMode::Select | InteractionMode::Brush => {
                if let Some(bounds) = self.state.selection_bounds.take() {
                    InteractionResult::Select {
                        bounds,
                        points: self.state.selected_points.clone(),
                    }
                } else {
                    InteractionResult::None
                }
            }
            InteractionMode::Zoom => {
                if was_dragging {
                    if let (Some(start), Some(end)) =
                        (self.state.mouse_down_position, Some(event.position))
                    {
                        let bounds = Bounds::from_two_points(start, end);
                        InteractionResult::Custom {
                            name: "zoom_to_selection".to_string(),
                            data: serde_json::json!({
                                "bounds": {
                                    "min": {"x": bounds.min().x, "y": bounds.min().y},
                                    "max": {"x": bounds.max().x, "y": bounds.max().y}
                                }
                            }),
                        }
                    } else {
                        InteractionResult::None
                    }
                } else {
                    InteractionResult::None
                }
            }
            _ => InteractionResult::None,
        };

        self.state.mouse_down_position = None;
        self.state.selection_bounds = None;

        result
    }

    /// Handle mouse move event
    fn handle_mouse_move(&mut self, event: &MouseEvent) -> InteractionResult {
        let current_pos = event.position;

        // Check if we should start dragging
        if self.state.mouse_down && !self.state.is_dragging {
            if let Some(down_pos) = self.state.mouse_down_position {
                let distance = ((current_pos.x - down_pos.x).powi(2)
                    + (current_pos.y - down_pos.y).powi(2))
                .sqrt();
                if distance > self.config.drag_threshold {
                    self.state.is_dragging = true;
                }
            }
        }

        let result = if self.state.is_dragging {
            match self.state.mode {
                InteractionMode::Pan => {
                    if let Some(last_pos) = self.state.last_mouse_position {
                        let delta =
                            Point::new(current_pos.x - last_pos.x, current_pos.y - last_pos.y);
                        InteractionResult::Pan { delta }
                    } else {
                        InteractionResult::None
                    }
                }
                InteractionMode::Select | InteractionMode::Brush => {
                    if let Some(ref mut bounds) = self.state.selection_bounds {
                        *bounds = Bounds::from_two_points(bounds.min(), current_pos);
                        InteractionResult::Redraw
                    } else {
                        InteractionResult::None
                    }
                }
                InteractionMode::Zoom => {
                    if let Some(ref mut bounds) = self.state.selection_bounds {
                        *bounds = Bounds::from_two_points(bounds.min(), current_pos);
                        InteractionResult::Redraw
                    } else if let Some(down_pos) = self.state.mouse_down_position {
                        self.state.selection_bounds =
                            Some(Bounds::from_two_points(down_pos, current_pos));
                        InteractionResult::Redraw
                    } else {
                        InteractionResult::None
                    }
                }
                _ => InteractionResult::None,
            }
        } else {
            InteractionResult::None
        };

        self.state.last_mouse_position = Some(current_pos);
        result
    }

    /// Handle mouse click event
    fn handle_mouse_click(&mut self, event: &MouseEvent) -> InteractionResult {
        let now = std::time::Instant::now();

        // Check for double click
        if let Some(last_time) = self.last_click_time {
            if now.duration_since(last_time).as_millis()
                < self.config.double_click_threshold as u128
            {
                self.last_click_time = None;
                return self.handle_double_click(event);
            }
        }

        self.last_click_time = Some(now);
        InteractionResult::None
    }

    /// Handle double click event
    fn handle_double_click(&mut self, _event: &MouseEvent) -> InteractionResult {
        match self.state.mode {
            InteractionMode::Default => {
                // Double click resets zoom
                InteractionResult::Custom {
                    name: "reset_zoom".to_string(),
                    data: serde_json::json!({}),
                }
            }
            _ => InteractionResult::None,
        }
    }

    /// Handle context menu event
    fn handle_context_menu(&mut self, event: &MouseEvent) -> InteractionResult {
        if self.config.enable_context_menu {
            let items = match self.state.mode {
                InteractionMode::Default => vec![
                    "重置缩放".to_string(),
                    "切换到缩放模式".to_string(),
                    "切换到平移模式".to_string(),
                    "切换到选择模式".to_string(),
                ],
                InteractionMode::Zoom => vec!["切换到默认模式".to_string(), "重置缩放".to_string()],
                InteractionMode::Pan => vec!["切换到默认模式".to_string(), "重置位置".to_string()],
                InteractionMode::Select => {
                    vec!["切换到默认模式".to_string(), "清除选择".to_string()]
                }
                _ => vec!["切换到默认模式".to_string()],
            };

            InteractionResult::ContextMenu {
                position: event.position,
                items,
            }
        } else {
            InteractionResult::None
        }
    }

    /// Handle mouse wheel event
    fn handle_mouse_wheel(&mut self, event: &MouseEvent) -> InteractionResult {
        if let Some(delta) = event.delta {
            let zoom_factor = if delta > 0.0 { 1.1 } else { 0.9 };
            InteractionResult::Zoom {
                center: event.position,
                factor: zoom_factor,
            }
        } else {
            InteractionResult::None
        }
    }

    /// Handle key event
    fn handle_key_event(&mut self, event: &KeyEvent) -> InteractionResult {
        if !self.config.keyboard_mode_switching {
            return InteractionResult::None;
        }

        use crate::KeyEventType;

        if event.event_type == KeyEventType::KeyDown {
            match event.key.as_str() {
                "z" => {
                    self.set_mode(InteractionMode::Zoom);
                    InteractionResult::ChangeMode(InteractionMode::Zoom)
                }
                "p" => {
                    self.set_mode(InteractionMode::Pan);
                    InteractionResult::ChangeMode(InteractionMode::Pan)
                }
                "s" => {
                    self.set_mode(InteractionMode::Select);
                    InteractionResult::ChangeMode(InteractionMode::Select)
                }
                "Escape" => {
                    self.set_mode(InteractionMode::Default);
                    InteractionResult::ChangeMode(InteractionMode::Default)
                }
                _ => InteractionResult::None,
            }
        } else {
            InteractionResult::None
        }
    }

    /// Get current selection bounds
    pub fn get_selection_bounds(&self) -> Option<&Bounds> {
        self.state.selection_bounds.as_ref()
    }

    /// Get selected points
    pub fn get_selected_points(&self) -> &[(usize, usize)] {
        &self.state.selected_points
    }

    /// Clear selection
    pub fn clear_selection(&mut self) {
        self.state.selected_points.clear();
        self.state.selection_bounds = None;
    }
}
