/*!
 * ECharts GPUI 简单案例
 *
 * 本示例展示了如何在GPUI应用程序中集成ECharts图表。
 */

use echarts_rs::{prelude::*, ChartExt};
use gpui::Bounds as GpuiBounds;
use gpui::{
    div, px, rgb, size, AppContext, Application, Context, FontWeight, IntoElement, ParentElement,
    Render, Styled, TitlebarOptions, Window, WindowBackgroundAppearance, WindowBounds, WindowKind,
    WindowOptions,
};
use serde_json::json;

fn main() {
    println!("🚀 启动 ECharts GPUI 仪表板");

    let app = Application::new();

    app.run(move |cx| {
        let window_size = size(px(1200.0), px(800.0));

        let _window = cx.open_window(
            WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(GpuiBounds::centered(
                    None,
                    window_size,
                    cx,
                ))),
                titlebar: Some(TitlebarOptions {
                    title: Some("ECharts 数据仪表板".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                window_background: WindowBackgroundAppearance::Opaque,
                app_id: None,
                window_min_size: Some(size(px(800.0), px(600.0))),
                window_decorations: None,
            },
            |_window, cx| {
                println!("🪟 仪表板窗口已创建");
                cx.new(|cx| Dashboard::new(cx))
            },
        );
    });
}

/// 仪表板主结构
struct Dashboard {
    /// 销售趋势图表
    sales_chart: Chart,
    /// 产品分布饼图
    product_chart: Chart,
    /// 用户活跃度柱状图
    activity_chart: Chart,
    /// 实时数据散点图
    realtime_chart: Chart,
    /// 面积图 - 网站流量
    area_chart: Chart,
    /// 混合图表 - 收入与利润
    mixed_chart: Chart,
    /// 堆叠柱状图 - 季度对比
    stacked_bar_chart: Chart,
    /// 多系列折线图 - 多产品对比
    multi_line_chart: Chart,
}

impl Dashboard {
    fn new(cx: &mut Context<Self>) -> Self {
        println!("📊 初始化仪表板数据 - 创建8种不同类型的图表同时展示");

        Self {
            sales_chart: Self::create_sales_chart(),
            product_chart: Self::create_product_chart(),
            activity_chart: Self::create_activity_chart(),
            realtime_chart: Self::create_realtime_chart(),
            area_chart: Self::create_area_chart(),
            mixed_chart: Self::create_mixed_chart(),
            stacked_bar_chart: Self::create_stacked_bar_chart(),
            multi_line_chart: Self::create_multi_line_chart(),
        }
    }

    /// 创建销售趋势折线图
    fn create_sales_chart() -> Chart {
        let mut chart = Chart::new();

        let option = json!({
            "title": {
                "text": "月度销售趋势",
                "textStyle": {
                    "fontSize": 16,
                    "color": "#333"
                }
            },
            "tooltip": {
                "trigger": "axis"
            },
            "xAxis": {
                "type": "category",
                "data": ["1月", "2月", "3月", "4月", "5月", "6月"]
            },
            "yAxis": {
                "type": "value"
            },
            "series": [{
                "name": "销售额",
                "type": "line",
                "data": [120, 200, 150, 80, 70, 110],
                "smooth": true,
                "itemStyle": {
                    "color": "#5470c6"
                },
                "areaStyle": {
                    "opacity": 0.3
                }
            }]
        });

        // 设置图表配置到custom字段
        chart.custom.insert("option".to_string(), option);
        chart
    }

    /// 创建产品分布饼图
    fn create_product_chart() -> Chart {
        let mut chart = Chart::new();

        let option = json!({
            "title": {
                "text": "产品销量分布",
                "textStyle": {
                    "fontSize": 16,
                    "color": "#333"
                }
            },
            "tooltip": {
                "trigger": "item",
                "formatter": "{b}: {c} ({d}%)"
            },
            "series": [{
                "type": "pie",
                "radius": ["40%", "70%"],
                "data": [
                    {"value": 335, "name": "产品A"},
                    {"value": 310, "name": "产品B"},
                    {"value": 234, "name": "产品C"},
                    {"value": 135, "name": "产品D"}
                ],
                "emphasis": {
                    "itemStyle": {
                        "shadowBlur": 10,
                        "shadowOffsetX": 0,
                        "shadowColor": "rgba(0, 0, 0, 0.5)"
                    }
                }
            }]
        });

        chart.custom.insert("option".to_string(), option);
        chart
    }

    /// 创建用户活跃度柱状图
    fn create_activity_chart() -> Chart {
        let mut chart = Chart::new();

        let option = json!({
            "title": {
                "text": "用户活跃度",
                "textStyle": {
                    "fontSize": 16,
                    "color": "#333"
                }
            },
            "tooltip": {
                "trigger": "axis"
            },
            "xAxis": {
                "type": "category",
                "data": ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
            },
            "yAxis": {
                "type": "value"
            },
            "series": [{
                "name": "活跃用户",
                "type": "bar",
                "data": [820, 932, 901, 934, 1290, 1330, 1320],
                "itemStyle": {
                    "color": "#91cc75"
                }
            }]
        });

        chart.custom.insert("option".to_string(), option);
        chart
    }

    /// 创建实时数据散点图
    fn create_realtime_chart() -> Chart {
        let mut chart = Chart::new();

        // 生成模拟实时数据
        let data: Vec<[f64; 2]> = (0..30)
            .map(|i| {
                let x = i as f64;
                let y = (x * 0.5).sin() * 30.0 + 50.0;
                [x, y]
            })
            .collect();

        let option = json!({
            "title": {
                "text": "实时数据监控",
                "textStyle": {
                    "fontSize": 16,
                    "color": "#333"
                }
            },
            "tooltip": {
                "trigger": "item"
            },
            "xAxis": {
                "type": "value"
            },
            "yAxis": {
                "type": "value"
            },
            "series": [{
                "type": "scatter",
                "data": data,
                "symbolSize": 6,
                "itemStyle": {
                    "color": "#ee6666"
                }
            }]
        });

        chart.custom.insert("option".to_string(), option);
        chart
    }

    /// 创建网站流量面积图
    fn create_area_chart() -> Chart {
        let mut chart = Chart::new();

        let option = json!({
            "title": {
                "text": "网站流量趋势",
                "left": "center",
                "textStyle": {
                    "fontSize": 16,
                    "color": "#2c3e50"
                }
            },
            "tooltip": {
                "trigger": "axis",
                "axisPointer": {
                    "type": "cross"
                }
            },
            "xAxis": {
                "type": "category",
                "data": ["00:00", "04:00", "08:00", "12:00", "16:00", "20:00", "24:00"],
                "axisLabel": {
                    "color": "#666"
                }
            },
            "yAxis": {
                "type": "value",
                "name": "访问量",
                "axisLabel": {
                    "color": "#666"
                }
            },
            "series": [{
                "name": "页面访问量",
                "type": "area",
                "data": [120, 200, 150, 800, 700, 1100, 300],
                "smooth": true,
                "areaStyle": {
                    "color": {
                        "type": "linear",
                        "x": 0, "y": 0, "x2": 0, "y2": 1,
                        "colorStops": [
                            {"offset": 0, "color": "rgba(58, 77, 233, 0.8)"},
                            {"offset": 1, "color": "rgba(58, 77, 233, 0.1)"}
                        ]
                    }
                },
                "itemStyle": {
                    "color": "#3a4de9"
                }
            }]
        });

        chart.custom.insert("option".to_string(), option);
        chart
    }

    /// 创建收入利润混合图表
    fn create_mixed_chart() -> Chart {
        let mut chart = Chart::new();

        let option = json!({
            "title": {
                "text": "收入与利润分析",
                "left": "center",
                "textStyle": {
                    "fontSize": 16,
                    "color": "#2c3e50"
                }
            },
            "tooltip": {
                "trigger": "axis"
            },
            "legend": {
                "data": ["收入", "利润", "利润率"],
                "top": "8%"
            },
            "xAxis": {
                "type": "category",
                "data": ["1月", "2月", "3月", "4月", "5月", "6月"]
            },
            "yAxis": [
                {
                    "type": "value",
                    "name": "金额(万元)",
                    "position": "left"
                },
                {
                    "type": "value",
                    "name": "利润率(%)",
                    "position": "right"
                }
            ],
            "series": [
                {
                    "name": "收入",
                    "type": "bar",
                    "data": [200, 300, 280, 400, 350, 450],
                    "itemStyle": {
                        "color": "#5470c6"
                    }
                },
                {
                    "name": "利润",
                    "type": "bar",
                    "data": [50, 80, 70, 120, 100, 140],
                    "itemStyle": {
                        "color": "#91cc75"
                    }
                },
                {
                    "name": "利润率",
                    "type": "line",
                    "yAxisIndex": 1,
                    "data": [25, 27, 25, 30, 29, 31],
                    "itemStyle": {
                        "color": "#fac858"
                    }
                }
            ]
        });

        chart.custom.insert("option".to_string(), option);
        chart
    }

    /// 创建季度对比堆叠柱状图
    fn create_stacked_bar_chart() -> Chart {
        let mut chart = Chart::new();

        let option = json!({
            "title": {
                "text": "季度销售对比",
                "left": "center",
                "textStyle": {
                    "fontSize": 16,
                    "color": "#2c3e50"
                }
            },
            "tooltip": {
                "trigger": "axis",
                "axisPointer": {
                    "type": "shadow"
                }
            },
            "legend": {
                "data": ["Q1", "Q2", "Q3", "Q4"],
                "top": "8%"
            },
            "xAxis": {
                "type": "category",
                "data": ["产品A", "产品B", "产品C", "产品D", "产品E"]
            },
            "yAxis": {
                "type": "value",
                "name": "销售额(万)"
            },
            "series": [
                {
                    "name": "Q1",
                    "type": "bar",
                    "stack": "季度",
                    "data": [120, 132, 101, 134, 90],
                    "itemStyle": {
                        "color": "#5470c6"
                    }
                },
                {
                    "name": "Q2",
                    "type": "bar",
                    "stack": "季度",
                    "data": [220, 182, 191, 234, 290],
                    "itemStyle": {
                        "color": "#91cc75"
                    }
                },
                {
                    "name": "Q3",
                    "type": "bar",
                    "stack": "季度",
                    "data": [150, 232, 201, 154, 190],
                    "itemStyle": {
                        "color": "#fac858"
                    }
                },
                {
                    "name": "Q4",
                    "type": "bar",
                    "stack": "季度",
                    "data": [98, 77, 101, 99, 40],
                    "itemStyle": {
                        "color": "#ee6666"
                    }
                }
            ]
        });

        chart.custom.insert("option".to_string(), option);
        chart
    }

    /// 创建多产品对比折线图
    fn create_multi_line_chart() -> Chart {
        let mut chart = Chart::new();

        let option = json!({
            "title": {
                "text": "多产品销售对比",
                "left": "center",
                "textStyle": {
                    "fontSize": 16,
                    "color": "#2c3e50"
                }
            },
            "tooltip": {
                "trigger": "axis"
            },
            "legend": {
                "data": ["产品A", "产品B", "产品C", "产品D"],
                "top": "8%"
            },
            "xAxis": {
                "type": "category",
                "data": ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月"]
            },
            "yAxis": {
                "type": "value",
                "name": "销售量"
            },
            "series": [
                {
                    "name": "产品A",
                    "type": "line",
                    "data": [120, 132, 101, 134, 90, 230, 210, 182],
                    "smooth": true,
                    "itemStyle": {
                        "color": "#5470c6"
                    }
                },
                {
                    "name": "产品B",
                    "type": "line",
                    "data": [220, 182, 191, 234, 290, 330, 310, 201],
                    "smooth": true,
                    "itemStyle": {
                        "color": "#91cc75"
                    }
                },
                {
                    "name": "产品C",
                    "type": "line",
                    "data": [150, 232, 201, 154, 190, 330, 410, 250],
                    "smooth": true,
                    "itemStyle": {
                        "color": "#fac858"
                    }
                },
                {
                    "name": "产品D",
                    "type": "line",
                    "data": [98, 77, 101, 99, 40, 130, 160, 120],
                    "smooth": true,
                    "itemStyle": {
                        "color": "#ee6666"
                    }
                }
            ]
        });

        chart.custom.insert("option".to_string(), option);
        chart
    }
}

impl Render for Dashboard {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        div()
            .flex()
            .flex_col()
            .w_full()
            .h_full()
            .bg(rgb(0xf8f9fa))

            .child(
                // 标题栏
                div()
                    .flex()
                    .justify_between()
                    .items_center()
                    .h_16()
                    .bg(rgb(0x2c3e50))
                    .text_color(rgb(0xffffff))
                    .px_6()
                    .child(
                        div()
                            .text_xl()
                            .font_weight(FontWeight::BOLD)
                            .child("📊 ECharts 8种图表类型全展示")
                    )
                    .child(
                        div()
                            .flex()
                            .items_center()
                            .gap_4()
                            .child(
                                div()
                                    .text_sm()
                                    .child("同时验证所有图表类型")
                            )
                    )
            )

            .child(
                // 主要图表显示区域 - 4x2网格布局
                div()
                    .flex_1()
                    .flex()
                    .flex_col()
                    .p_4()
                    .gap_4()
                    .child(
                        // 第一行 - 4个图表
                        div()
                            .flex()
                            .flex_1()
                            .gap_4()
                            .child(
                                // 1. 销售趋势折线图
                                div()
                                    .flex_1()
                                    .bg(rgb(0xffffff))
                                    .rounded_lg()
                                    .shadow_lg()
                                    .p_3()
                                    .child(
                                        div()
                                            .text_sm()
                                            .font_weight(FontWeight::BOLD)
                                            .text_color(rgb(0x2c3e50))
                                            .mb_2()
                                            .child("📈 折线图")
                                    )
                                    .child(self.sales_chart.draw())
                            )
                            .child(
                                // 2. 产品分布饼图
                                div()
                                    .flex_1()
                                    .bg(rgb(0xffffff))
                                    .rounded_lg()
                                    .shadow_lg()
                                    .p_3()
                                    .child(
                                        div()
                                            .text_sm()
                                            .font_weight(FontWeight::BOLD)
                                            .text_color(rgb(0x2c3e50))
                                            .mb_2()
                                            .child("🥧 饼图")
                                    )
                                    .child(self.product_chart.draw())
                            )
                            .child(
                                // 3. 用户活跃度柱状图
                                div()
                                    .flex_1()
                                    .bg(rgb(0xffffff))
                                    .rounded_lg()
                                    .shadow_lg()
                                    .p_3()
                                    .child(
                                        div()
                                            .text_sm()
                                            .font_weight(FontWeight::BOLD)
                                            .text_color(rgb(0x2c3e50))
                                            .mb_2()
                                            .child("📊 柱状图")
                                    )
                                    .child(self.activity_chart.draw())
                            )
                            .child(
                                // 4. 实时数据散点图
                                div()
                                    .flex_1()
                                    .bg(rgb(0xffffff))
                                    .rounded_lg()
                                    .shadow_lg()
                                    .p_3()
                                    .child(
                                        div()
                                            .text_sm()
                                            .font_weight(FontWeight::BOLD)
                                            .text_color(rgb(0x2c3e50))
                                            .mb_2()
                                            .child("🔴 散点图")
                                    )
                                    .child(self.realtime_chart.draw())
                            )
                    )
                    .child(
                        // 第二行 - 4个图表
                        div()
                            .flex()
                            .flex_1()
                            .gap_4()
                            .child(
                                // 5. 网站流量面积图
                                div()
                                    .flex_1()
                                    .bg(rgb(0xffffff))
                                    .rounded_lg()
                                    .shadow_lg()
                                    .p_3()
                                    .child(
                                        div()
                                            .text_sm()
                                            .font_weight(FontWeight::BOLD)
                                            .text_color(rgb(0x2c3e50))
                                            .mb_2()
                                            .child("🌊 面积图")
                                    )
                                    .child(self.area_chart.draw())
                            )
                            .child(
                                // 6. 收入利润混合图
                                div()
                                    .flex_1()
                                    .bg(rgb(0xffffff))
                                    .rounded_lg()
                                    .shadow_lg()
                                    .p_3()
                                    .child(
                                        div()
                                            .text_sm()
                                            .font_weight(FontWeight::BOLD)
                                            .text_color(rgb(0x2c3e50))
                                            .mb_2()
                                            .child("📊📈 混合图")
                                    )
                                    .child(self.mixed_chart.draw())
                            )
                            .child(
                                // 7. 季度对比堆叠图
                                div()
                                    .flex_1()
                                    .bg(rgb(0xffffff))
                                    .rounded_lg()
                                    .shadow_lg()
                                    .p_3()
                                    .child(
                                        div()
                                            .text_sm()
                                            .font_weight(FontWeight::BOLD)
                                            .text_color(rgb(0x2c3e50))
                                            .mb_2()
                                            .child("📚 堆叠图")
                                    )
                                    .child(self.stacked_bar_chart.draw())
                            )
                            .child(
                                // 8. 多产品对比线图
                                div()
                                    .flex_1()
                                    .bg(rgb(0xffffff))
                                    .rounded_lg()
                                    .shadow_lg()
                                    .p_3()
                                    .child(
                                        div()
                                            .text_sm()
                                            .font_weight(FontWeight::BOLD)
                                            .text_color(rgb(0x2c3e50))
                                            .mb_2()
                                            .child("📈📈 多线图")
                                    )
                                    .child(self.multi_line_chart.draw())
                            )
                    )
            )

            .child(
                // 状态栏
                div()
                    .h_12()
                    .bg(rgb(0x495057))
                    .text_color(rgb(0xffffff))
                    .flex()
                    .items_center()
                    .justify_center()
                    .px_6()
                    .child(
                        div()
                            .text_sm()
                            .child("💡 ECharts-rs 全功能验证 | 8种图表类型同时展示 | Processor + Renderer 架构完整性测试")
                    )
            )
    }
}
