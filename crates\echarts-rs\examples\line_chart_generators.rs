//! 曲线图生成器函数
//!
//! 包含各种特殊样式和效果的曲线图生成函数

use std::fs;

/// 创建渐变色曲线图
pub fn create_gradient_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 定义多色渐变
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"multiGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#ff6b6b;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"25%\" style=\"stop-color:#4ecdc4;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"50%\" style=\"stop-color:#45b7d1;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"75%\" style=\"stop-color:#f9ca24;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#6c5ce7;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");
    
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));
    
    if !data.is_empty() {
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        
        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };
        
        // 生成路径
        let mut path = String::from("M");
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        // 绘制渐变曲线
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"url(#multiGradient)\" stroke-width=\"5\" fill=\"none\"/>\n", path));
        
        // 绘制渐变数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"5\" fill=\"url(#multiGradient)\" stroke=\"white\" stroke-width=\"2\"/>\n", px, py));
        }
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建虚线曲线图
pub fn create_dashed_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));
    
    if !data.is_empty() {
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        
        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };
        
        // 生成路径
        let mut path = String::from("M");
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        // 绘制虚线曲线
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#e74c3c\" stroke-width=\"3\" stroke-dasharray=\"10,5\" fill=\"none\"/>\n", path));
        
        // 绘制方形数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"8\" height=\"8\" fill=\"#e74c3c\" stroke=\"white\" stroke-width=\"2\" transform=\"translate(-4,-4)\"/>\n", px, py));
        }
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建粗细变化曲线图
pub fn create_thick_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));
    
    if !data.is_empty() && data.len() > 1 {
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        
        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };
        
        // 绘制变粗细的线段
        for i in 0..data.len() - 1 {
            let (x1, y1) = data[i];
            let (x2, y2) = data[i + 1];
            
            let px1 = chart_x + (x1 - min_x) / x_range * chart_width;
            let py1 = chart_y + chart_height - (y1 - min_y) / y_range * chart_height;
            let px2 = chart_x + (x2 - min_x) / x_range * chart_width;
            let py2 = chart_y + chart_height - (y2 - min_y) / y_range * chart_height;
            
            // 根据位置变化线条粗细
            let thickness = 2.0 + (i as f64 / data.len() as f64) * 8.0;
            
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#9b59b6\" stroke-width=\"{}\"/>\n", px1, py1, px2, py2, thickness));
        }
        
        // 绘制数据点
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            let radius = 3.0 + (i as f64 / data.len() as f64) * 4.0;
            
            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"{}\" fill=\"#9b59b6\" stroke=\"white\" stroke-width=\"2\"/>\n", px, py, radius));
        }
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建阴影效果曲线图
pub fn create_shadow_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 定义阴影滤镜
    svg.push_str("  <defs>\n");
    svg.push_str("    <filter id=\"bigShadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n");
    svg.push_str("      <feDropShadow dx=\"4\" dy=\"4\" stdDeviation=\"4\" flood-color=\"#000000\" flood-opacity=\"0.4\"/>\n");
    svg.push_str("    </filter>\n");
    svg.push_str("    <filter id=\"glow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n");
    svg.push_str("      <feGaussianBlur stdDeviation=\"3\" result=\"coloredBlur\"/>\n");
    svg.push_str("      <feMerge>\n");
    svg.push_str("        <feMergeNode in=\"coloredBlur\"/>\n");
    svg.push_str("        <feMergeNode in=\"SourceGraphic\"/>\n");
    svg.push_str("      </feMerge>\n");
    svg.push_str("    </filter>\n");
    svg.push_str("  </defs>\n");
    
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));
    
    if !data.is_empty() {
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        
        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };
        
        // 生成路径
        let mut path = String::from("M");
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        // 绘制带阴影的曲线
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#3498db\" stroke-width=\"4\" fill=\"none\" filter=\"url(#bigShadow)\"/>\n", path));
        
        // 绘制发光数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"6\" fill=\"#3498db\" stroke=\"white\" stroke-width=\"2\" filter=\"url(#glow)\"/>\n", px, py));
        }
    }
    
    svg.push_str("</svg>");
    svg
}
