//! 快速完整图表生成器
//!
//! 快速生成真实完整的图表内容

use std::fs;

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🚀 快速完整图表生成器");
    println!("{}", "=".repeat(40));

    // 确保输出目录存在
    let output_dir = "temp/svg/quick_complete";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 生成各种完整图表
    generate_all_charts(output_dir)?;
    
    // 生成展示页面
    generate_showcase_html(output_dir)?;

    println!("\n🎉 快速完整图表生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/showcase.html 查看完整图表", output_dir);

    Ok(())
}

/// 生成所有图表
fn generate_all_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 完整折线图
    let line_svg = create_line_chart("销售趋势图", 600.0, 400.0);
    fs::write(format!("{}/01_line_chart.svg", output_dir), line_svg)?;
    println!("  ✅ 折线图已生成");

    // 2. 完整柱状图
    let bar_svg = create_bar_chart("季度业绩", 600.0, 400.0);
    fs::write(format!("{}/02_bar_chart.svg", output_dir), bar_svg)?;
    println!("  ✅ 柱状图已生成");

    // 3. 完整散点图
    let scatter_svg = create_scatter_chart("数据分布", 600.0, 400.0);
    fs::write(format!("{}/03_scatter_chart.svg", output_dir), scatter_svg)?;
    println!("  ✅ 散点图已生成");

    // 4. 完整饼图
    let pie_svg = create_pie_chart("市场份额", 500.0, 500.0);
    fs::write(format!("{}/04_pie_chart.svg", output_dir), pie_svg)?;
    println!("  ✅ 饼图已生成");

    // 5. 多系列图表
    let multi_svg = create_multi_series_chart("产品对比", 700.0, 450.0);
    fs::write(format!("{}/05_multi_series.svg", output_dir), multi_svg)?;
    println!("  ✅ 多系列图表已生成");

    // 6. 混合图表
    let mixed_svg = create_mixed_chart("综合分析", 800.0, 500.0);
    fs::write(format!("{}/06_mixed_chart.svg", output_dir), mixed_svg)?;
    println!("  ✅ 混合图表已生成");

    Ok(())
}

/// 创建折线图
fn create_line_chart(title: &str, width: f64, height: f64) -> String {
    let data = vec![
        (0.0, 120.0), (1.0, 132.0), (2.0, 101.0), (3.0, 134.0), 
        (4.0, 90.0), (5.0, 230.0), (6.0, 210.0), (7.0, 180.0)
    ];
    
    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));
    
    // 网格线
    for i in 0..=5 {
        let x = chart_x + (i as f64 / 5.0) * chart_width;
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", x, chart_y, x, chart_y + chart_height));
    }
    
    // 数据范围
    let min_x = 0.0;
    let max_x = 7.0;
    let min_y = 80.0;
    let max_y = 240.0;
    let x_range = max_x - min_x;
    let y_range = max_y - min_y;
    
    // 生成路径
    let mut path = String::from("M");
    for (i, (x, y)) in data.iter().enumerate() {
        let px = chart_x + (x - min_x) / x_range * chart_width;
        let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
        
        if i == 0 {
            path.push_str(&format!(" {} {}", px, py));
        } else {
            path.push_str(&format!(" L {} {}", px, py));
        }
    }
    
    // 绘制折线
    svg.push_str(&format!("  <path d=\"{}\" stroke=\"#007bff\" stroke-width=\"3\" fill=\"none\"/>\n", path));
    
    // 绘制数据点
    for (x, y) in data {
        let px = chart_x + (x - min_x) / x_range * chart_width;
        let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
        svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#007bff\" stroke=\"white\" stroke-width=\"2\"/>\n", px, py));
    }
    
    // 坐标轴标签
    for i in 0..=7 {
        let x = chart_x + (i as f64 / 7.0) * chart_width;
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">月{}</text>\n", x, chart_y + chart_height + 20.0, i + 1));
    }
    
    for i in 0..=5 {
        let y = chart_y + chart_height - (i as f64 / 5.0) * chart_height;
        let value = min_y + (i as f64 / 5.0) * y_range;
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n", chart_x - 10.0, y + 4.0, value));
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建柱状图
fn create_bar_chart(title: &str, width: f64, height: f64) -> String {
    let data = vec![("Q1", 85.0), ("Q2", 120.0), ("Q3", 95.0), ("Q4", 140.0)];
    
    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));
    
    let max_value = 150.0;
    let bar_width = chart_width / data.len() as f64 * 0.8;
    let bar_spacing = chart_width / data.len() as f64;
    
    // 绘制柱子
    for (i, (label, value)) in data.iter().enumerate() {
        let x = chart_x + i as f64 * bar_spacing + bar_spacing * 0.1;
        let bar_height = (value / max_value) * chart_height;
        let y = chart_y + chart_height - bar_height;
        
        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"#28a745\" stroke=\"#1e7e34\" stroke-width=\"1\" rx=\"3\"/>\n", x, y, bar_width, bar_height));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" font-weight=\"bold\" fill=\"#333\">{:.0}</text>\n", x + bar_width / 2.0, y - 8.0, value));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{}</text>\n", x + bar_width / 2.0, chart_y + chart_height + 20.0, label));
    }
    
    // Y轴标签
    for i in 0..=5 {
        let y = chart_y + chart_height - (i as f64 / 5.0) * chart_height;
        let value = (i as f64 / 5.0) * max_value;
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n", chart_x - 10.0, y + 4.0, value));
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建散点图
fn create_scatter_chart(title: &str, width: f64, height: f64) -> String {
    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));
    
    // 生成散点数据
    for i in 0..30 {
        let x = chart_x + (i as f64 * 0.1).cos() * chart_width * 0.3 + chart_width * 0.5;
        let y = chart_y + (i as f64 * 0.1).sin() * chart_height * 0.3 + chart_height * 0.5;
        svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"5\" fill=\"#dc3545\" fill-opacity=\"0.8\" stroke=\"white\" stroke-width=\"1\"/>\n", x, y));
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建饼图
fn create_pie_chart(title: &str, width: f64, height: f64) -> String {
    let data = vec![("产品A", 335.0), ("产品B", 310.0), ("产品C", 234.0), ("产品D", 135.0), ("产品E", 548.0)];

    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    let center_x = width / 2.0;
    let center_y = height / 2.0 + 20.0;
    let radius = (width.min(height) - 120.0) / 2.5;

    let total: f64 = data.iter().map(|(_, v)| *v).sum();
    let mut current_angle = -std::f64::consts::PI / 2.0;

    let colors = ["#007bff", "#28a745", "#dc3545", "#ffc107", "#17a2b8"];

    for (i, (label, value)) in data.iter().enumerate() {
        let angle = (value / total) * 2.0 * std::f64::consts::PI;
        let end_angle = current_angle + angle;

        let x1 = center_x + radius * current_angle.cos();
        let y1 = center_y + radius * current_angle.sin();
        let x2 = center_x + radius * end_angle.cos();
        let y2 = center_y + radius * end_angle.sin();

        let large_arc = if angle > std::f64::consts::PI { 1 } else { 0 };
        let color = colors[i % colors.len()];

        // 绘制扇形
        svg.push_str(&format!("  <path d=\"M {} {} L {} {} A {} {} 0 {} 1 {} {} Z\" fill=\"{}\" stroke=\"white\" stroke-width=\"3\"/>\n", center_x, center_y, x1, y1, radius, radius, large_arc, x2, y2, color));

        // 添加标签
        let label_angle = current_angle + angle / 2.0;
        let label_x = center_x + (radius + 30.0) * label_angle.cos();
        let label_y = center_y + (radius + 30.0) * label_angle.sin();

        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", label_x, label_y, label));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"10\" fill=\"#666\">{:.1}%</text>\n", label_x, label_y + 15.0, (value / total) * 100.0));

        current_angle = end_angle;
    }

    svg.push_str("</svg>");
    svg
}

/// 创建多系列图表
fn create_multi_series_chart(title: &str, width: f64, height: f64) -> String {
    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;

    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));

    // 多个系列数据
    let series_data = vec![
        ("产品A", vec![(0.0, 120.0), (1.0, 132.0), (2.0, 101.0), (3.0, 134.0), (4.0, 90.0), (5.0, 230.0)], "#007bff"),
        ("产品B", vec![(0.0, 220.0), (1.0, 182.0), (2.0, 191.0), (3.0, 234.0), (4.0, 290.0), (5.0, 330.0)], "#28a745"),
        ("产品C", vec![(0.0, 150.0), (1.0, 160.0), (2.0, 140.0), (3.0, 180.0), (4.0, 170.0), (5.0, 200.0)], "#dc3545"),
    ];

    let max_value = 350.0;
    let max_x = 5.0;

    // 绘制每个系列
    for (name, data, color) in &series_data {
        let mut path = String::from("M");

        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x / max_x) * chart_width;
            let py = chart_y + chart_height - (y / max_value) * chart_height;

            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }

        svg.push_str(&format!("  <path d=\"{}\" stroke=\"{}\" stroke-width=\"3\" fill=\"none\"/>\n", path, color));

        for (x, y) in data {
            let px = chart_x + (x / max_x) * chart_width;
            let py = chart_y + chart_height - (y / max_value) * chart_height;
            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"{}\" stroke=\"white\" stroke-width=\"2\"/>\n", px, py, color));
        }
    }

    // 图例
    for (i, (name, _, color)) in series_data.iter().enumerate() {
        let y = chart_y + 30.0 + i as f64 * 25.0;
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"{}\" stroke-width=\"3\"/>\n", chart_x + chart_width - 120.0, y, chart_x + chart_width - 100.0, y, color));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">{}</text>\n", chart_x + chart_width - 95.0, y + 4.0, name));
    }

    svg.push_str("</svg>");
    svg
}

/// 创建混合图表
fn create_mixed_chart(title: &str, width: f64, height: f64) -> String {
    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;

    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));

    // 柱状图数据
    let bar_data = vec![("Q1", 100.0), ("Q2", 120.0), ("Q3", 90.0), ("Q4", 150.0)];
    let bar_width = chart_width / bar_data.len() as f64 * 0.6;
    let bar_spacing = chart_width / bar_data.len() as f64;
    let max_bar_value = 160.0;

    // 绘制柱状图
    for (i, (label, value)) in bar_data.iter().enumerate() {
        let x = chart_x + i as f64 * bar_spacing + bar_spacing * 0.2;
        let bar_height = (value / max_bar_value) * chart_height * 0.8;
        let y = chart_y + chart_height - bar_height;

        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"#28a745\" fill-opacity=\"0.7\" stroke=\"#1e7e34\" stroke-width=\"1\"/>\n", x, y, bar_width, bar_height));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{}</text>\n", x + bar_width / 2.0, chart_y + chart_height + 20.0, label));
    }

    // 折线图数据
    let line_data = vec![(0.0, 10.0), (1.0, 15.0), (2.0, 8.0), (3.0, 20.0)];
    let mut path = String::from("M");

    for (i, (x, y)) in line_data.iter().enumerate() {
        let px = chart_x + (*x / 3.0) * chart_width;
        let py = chart_y + chart_height * 0.2 + (1.0 - (y / 25.0)) * chart_height * 0.6;

        if i == 0 {
            path.push_str(&format!(" {} {}", px, py));
        } else {
            path.push_str(&format!(" L {} {}", px, py));
        }
    }

    svg.push_str(&format!("  <path d=\"{}\" stroke=\"#dc3545\" stroke-width=\"3\" fill=\"none\"/>\n", path));

    for (x, y) in line_data {
        let px = chart_x + (x / 3.0) * chart_width;
        let py = chart_y + chart_height * 0.2 + (1.0 - (y / 25.0)) * chart_height * 0.6;
        svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#dc3545\"/>\n", px, py));
    }

    // 图例
    svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"15\" height=\"15\" fill=\"#28a745\"/>\n", chart_x + chart_width - 120.0, chart_y + 20.0));
    svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">销售额</text>\n", chart_x + chart_width - 100.0, chart_y + 32.0));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#dc3545\" stroke-width=\"3\"/>\n", chart_x + chart_width - 120.0, chart_y + 50.0, chart_x + chart_width - 105.0, chart_y + 50.0));
    svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">增长率</text>\n", chart_x + chart_width - 100.0, chart_y + 55.0));

    svg.push_str("</svg>");
    svg
}

/// 生成展示页面
fn generate_showcase_html(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速完整图表展示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1400px; margin: 0 auto; }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        .chart-item {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        .chart-svg {
            width: 100%;
            height: auto;
            border-radius: 8px;
            background: white;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .description {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 快速完整图表展示</h1>
            <p class="description">真实完整的图表内容，包含完整的数据可视化</p>
        </div>

        <div class="chart-grid">
            <div class="chart-item">
                <div class="chart-title">📈 销售趋势图</div>
                <object class="chart-svg" data="01_line_chart.svg" type="image/svg+xml">
                    您的浏览器不支持 SVG
                </object>
            </div>

            <div class="chart-item">
                <div class="chart-title">📊 季度业绩</div>
                <object class="chart-svg" data="02_bar_chart.svg" type="image/svg+xml">
                    您的浏览器不支持 SVG
                </object>
            </div>

            <div class="chart-item">
                <div class="chart-title">🔵 数据分布</div>
                <object class="chart-svg" data="03_scatter_chart.svg" type="image/svg+xml">
                    您的浏览器不支持 SVG
                </object>
            </div>

            <div class="chart-item">
                <div class="chart-title">🥧 市场份额</div>
                <object class="chart-svg" data="04_pie_chart.svg" type="image/svg+xml">
                    您的浏览器不支持 SVG
                </object>
            </div>

            <div class="chart-item">
                <div class="chart-title">📈 产品对比</div>
                <object class="chart-svg" data="05_multi_series.svg" type="image/svg+xml">
                    您的浏览器不支持 SVG
                </object>
            </div>

            <div class="chart-item">
                <div class="chart-title">🔧 综合分析</div>
                <object class="chart-svg" data="06_mixed_chart.svg" type="image/svg+xml">
                    您的浏览器不支持 SVG
                </object>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h2>🎉 完整图表内容特性</h2>
            <p>这些 SVG 图表包含完整的图表元素：</p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>✅ 完整的坐标轴和网格线</li>
                <li>✅ 真实的数据可视化</li>
                <li>✅ 准确的数据标签</li>
                <li>✅ 专业的图表样式</li>
                <li>✅ 多系列数据支持</li>
                <li>✅ 混合图表类型</li>
            </ul>
        </div>
    </div>
</body>
</html>"#;

    fs::write(format!("{}/showcase.html", output_dir), html_content)?;
    Ok(())
}
