# 预计算绘制命令使用指南

## 🎯 概述

预计算绘制命令是一种性能优化技术，通过提前生成 `DrawCommand` 并缓存，避免在每次渲染时重复计算，特别适用于：

- 📊 **静态图表** - 数据不经常变化的图表
- 🔄 **频繁重绘** - 需要高频率刷新的场景
- 📱 **移动设备** - CPU 资源有限的环境
- 🎮 **实时应用** - 对渲染性能要求极高的场景

## 🚀 基本使用方法

### 1. 标准方式（实时渲染）

```rust
use gpui_renderer::EChartsCanvas;
use echarts_rs::{LineSeries, Color, CartesianCoordinateSystem};

// 创建系列和坐标系统
let series = LineSeries::new("数据")
    .data(vec![(1.0, 100.0), (2.0, 200.0)])
    .color(Color::BLUE);

let coord_system = CartesianCoordinateSystem::new(
    Bounds::new(0.0, 0.0, 800.0, 600.0),
    (0.0, 10.0),
    (0.0, 300.0),
);

// 标准方式：每次 paint 都会重新生成绘制命令
let canvas = EChartsCanvas::new(Box::new(series), coord_system);
let element = canvas.into_element(); // 转换为 EChartsElement
```

### 2. 预计算方式（推荐）

```rust
use gpui_renderer::EChartsElement;

// 方法 A: 使用便捷方法
let element = EChartsElement::precompute_from_series(
    Box::new(series),
    coord_system,
    true, // 启用调试
)?;

// 方法 B: 手动预计算
let commands = series.render_to_commands(&coord_system)?;
let canvas = EChartsCanvas::new(Box::new(series), coord_system);
let element = EChartsElement::with_commands(canvas, commands);
```

## 📊 性能对比

| 渲染方式 | 首次渲染 | 后续渲染 | 内存使用 | 适用场景 |
|---------|---------|---------|---------|---------|
| 实时渲染 | 快 | 慢 | 低 | 动态数据 |
| 预计算 | 慢 | 极快 | 高 | 静态数据 |

## 🛠️ 高级用法

### 1. 缓存管理

```rust
struct ChartManager {
    cache: HashMap<String, Vec<DrawCommand>>,
}

impl ChartManager {
    fn get_or_create_chart(&mut self, key: &str) -> EChartsElement {
        if let Some(commands) = self.cache.get(key) {
            // 使用缓存的命令
            EChartsElement::with_commands(canvas, commands.clone())
        } else {
            // 预计算并缓存
            let commands = series.render_to_commands(&coord_system)?;
            self.cache.insert(key.to_string(), commands.clone());
            EChartsElement::with_commands(canvas, commands)
        }
    }
}
```

### 2. 动态更新

```rust
impl MyChartView {
    fn update_data(&mut self, new_data: Vec<(f64, f64)>) {
        // 更新系列数据
        let new_series = LineSeries::new("更新数据")
            .data(new_data)
            .color(Color::RED);
        
        // 重新计算绘制命令
        if let Ok(new_commands) = new_series.render_to_commands(&self.coord_system) {
            self.chart_element.update_commands(new_commands);
        }
    }
}
```

### 3. 条件缓存

```rust
impl Render for MyView {
    fn render(&mut self, cx: &mut ViewContext<Self>) -> impl IntoElement {
        let element = if self.data_is_static {
            // 静态数据使用预计算
            self.get_precomputed_chart()
        } else {
            // 动态数据使用实时渲染
            self.get_realtime_chart()
        };
        
        div().child(element)
    }
}
```

## ⚡ 最佳实践

### 1. 何时使用预计算

✅ **适合的场景：**
- 数据更新频率低（< 1次/秒）
- 图表复杂度高（> 1000个数据点）
- 需要频繁重绘（动画、交互）
- 移动设备或低性能环境

❌ **不适合的场景：**
- 实时数据流（> 10次/秒更新）
- 简单图表（< 100个数据点）
- 内存受限环境
- 数据经常变化

### 2. 内存管理

```rust
// 定期清理缓存
impl ChartCache {
    fn cleanup_old_cache(&mut self) {
        let now = Instant::now();
        self.cache.retain(|_, (commands, timestamp)| {
            now.duration_since(*timestamp) < Duration::from_secs(300) // 5分钟过期
        });
    }
}
```

### 3. 错误处理

```rust
fn create_chart_safely(&self) -> EChartsElement {
    match EChartsElement::precompute_from_series(series, coord_system, true) {
        Ok(element) => element,
        Err(e) => {
            eprintln!("预计算失败，回退到实时渲染: {:?}", e);
            // 回退到标准方式
            EChartsCanvas::new(series, coord_system).into_element()
        }
    }
}
```

## 🔧 API 参考

### EChartsElement 方法

```rust
impl EChartsElement {
    // 创建方法
    pub fn new(canvas: EChartsCanvas) -> Self;
    pub fn with_commands(canvas: EChartsCanvas, commands: Vec<DrawCommand>) -> Self;
    pub fn precompute_from_series(
        series: Box<dyn Series>,
        coord_system: CartesianCoordinateSystem,
        debug: bool,
    ) -> Result<Self, Error>;
    
    // 管理方法
    pub fn update_commands(&mut self, commands: Vec<DrawCommand>);
    pub fn clear_cache(&mut self);
    pub fn is_using_cache(&self) -> bool;
}
```

## 📝 示例代码

完整的示例代码请参考：
- `examples/precomputed_commands_demo.rs` - 基本演示
- `examples/practical_precomputed_usage.rs` - 实用示例

## 🐛 常见问题

### Q: 预计算的命令会过期吗？
A: 不会自动过期，需要手动管理。建议在数据更新时调用 `update_commands()`。

### Q: 可以混合使用预计算和实时渲染吗？
A: 可以。调用 `clear_cache()` 即可切换回实时渲染模式。

### Q: 预计算会占用多少内存？
A: 取决于图表复杂度。一般来说，1000个数据点的折线图约占用 50-100KB。

### Q: 如何调试预计算的问题？
A: 启用调试模式：`EChartsElement::precompute_from_series(..., true)`，会输出详细日志。
