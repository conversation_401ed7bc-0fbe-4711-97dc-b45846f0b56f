//! Canvas渲染器演示
//!
//! 展示HTML5 Canvas渲染器的功能，生成可在浏览器中运行的图表

use std::fs;
use echarts_rs::{LineSeries, BarSeries, ScatterSeries, PieSeries, CandlestickSeries, CandlestickDataItem, Color};
use echarts_rs::renderer::{HtmlCanvasRenderer, Renderer};
use echarts_core::{CartesianCoordinateSystem, Bounds, Point, Size, Series};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🎨 Canvas渲染器演示系统");
    println!("{}", "=".repeat(50));

    // 确保输出目录存在
    let output_dir = "temp/canvas/charts";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 折线图Canvas演示
    println!("\n📈 1. 生成折线图Canvas演示...");
    generate_line_chart_canvas(output_dir)?;

    // 2. 柱状图Canvas演示
    println!("\n📊 2. 生成柱状图Canvas演示...");
    generate_bar_chart_canvas(output_dir)?;

    // 3. 散点图Canvas演示
    println!("\n🔵 3. 生成散点图Canvas演示...");
    generate_scatter_chart_canvas(output_dir)?;

    // 4. 饼图Canvas演示
    println!("\n🥧 4. 生成饼图Canvas演示...");
    generate_pie_chart_canvas(output_dir)?;

    // 5. 蜡烛图Canvas演示
    println!("\n📈 5. 生成蜡烛图Canvas演示...");
    generate_candlestick_chart_canvas(output_dir)?;

    // 6. 混合图表Canvas演示
    println!("\n🎭 6. 生成混合图表Canvas演示...");
    generate_mixed_chart_canvas(output_dir)?;

    // 7. 生成展示页面
    println!("\n📄 7. 生成展示页面...");
    generate_canvas_showcase(output_dir)?;

    println!("\n🎉 Canvas渲染器演示生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/canvas_demo.html 查看演示", output_dir);

    Ok(())
}

/// 生成折线图Canvas演示
fn generate_line_chart_canvas(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        (0.0, 120.0), (1.0, 132.0), (2.0, 101.0), (3.0, 134.0), (4.0, 90.0),
        (5.0, 230.0), (6.0, 210.0), (7.0, 180.0), (8.0, 200.0), (9.0, 250.0),
    ];

    let line_series = LineSeries::new("销售数据")
        .data(data)
        .color(Color::rgb(0.2, 0.6, 1.0))
        .smooth(true)
        .area(true)
        .area_color(Color::rgba(0.2, 0.6, 1.0, 0.3));

    let bounds = Bounds {
        origin: Point { x: 0.0, y: 0.0 },
        size: Size { width: 800.0, height: 500.0 },
    };

    let mut renderer = HtmlCanvasRenderer::new("lineChart", bounds);
    
    // 创建坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        Bounds {
            origin: Point { x: 60.0, y: 60.0 },
            size: Size { width: 680.0, height: 380.0 },
        },
        (0.0, 10.0),
        (80.0, 260.0),
    );

    // 渲染图表
    let commands = line_series.render_to_commands(&coord_system)?;
    renderer.render_commands(commands, bounds)?;

    let html = renderer.get_html_page("折线图Canvas演示");
    fs::write(format!("{}/01_line_chart_canvas.html", output_dir), html)?;
    
    println!("  ✅ 折线图Canvas演示生成完成");
    Ok(())
}

/// 生成柱状图Canvas演示
fn generate_bar_chart_canvas(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        (0.0, 320.0), (1.0, 302.0), (2.0, 301.0), (3.0, 334.0), (4.0, 390.0),
        (5.0, 330.0), (6.0, 320.0), (7.0, 280.0), (8.0, 300.0), (9.0, 350.0),
    ];

    let bar_series = BarSeries::new("月度销售")
        .data(data)
        .color(Color::rgb(0.3, 0.8, 0.3))
        .bar_width(0.6)
        .border(true, Color::rgb(0.2, 0.6, 0.2), 1.0);

    let bounds = Bounds {
        origin: Point { x: 0.0, y: 0.0 },
        size: Size { width: 800.0, height: 500.0 },
    };

    let mut renderer = HtmlCanvasRenderer::new("barChart", bounds);
    
    let coord_system = CartesianCoordinateSystem::new(
        Bounds {
            origin: Point { x: 60.0, y: 60.0 },
            size: Size { width: 680.0, height: 380.0 },
        },
        (0.0, 10.0),
        (250.0, 400.0),
    );

    let commands = bar_series.render_to_commands(&coord_system)?;
    renderer.render_commands(commands, bounds)?;

    let html = renderer.get_html_page("柱状图Canvas演示");
    fs::write(format!("{}/02_bar_chart_canvas.html", output_dir), html)?;
    
    println!("  ✅ 柱状图Canvas演示生成完成");
    Ok(())
}

/// 生成散点图Canvas演示
fn generate_scatter_chart_canvas(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        (10.0, 8.04), (8.0, 6.95), (13.0, 7.58), (9.0, 8.81), (11.0, 8.33),
        (14.0, 9.96), (6.0, 7.24), (4.0, 4.26), (12.0, 10.84), (7.0, 4.82),
        (5.0, 5.68), (15.0, 12.5), (16.0, 11.2), (17.0, 13.1), (18.0, 14.8),
    ];

    let scatter_series = ScatterSeries::new("数据分布")
        .data(data)
        .color(Color::rgb(0.8, 0.3, 0.3))
        .symbol_size(8.0);

    let bounds = Bounds {
        origin: Point { x: 0.0, y: 0.0 },
        size: Size { width: 800.0, height: 500.0 },
    };

    let mut renderer = HtmlCanvasRenderer::new("scatterChart", bounds);
    
    let coord_system = CartesianCoordinateSystem::new(
        Bounds {
            origin: Point { x: 60.0, y: 60.0 },
            size: Size { width: 680.0, height: 380.0 },
        },
        (0.0, 20.0),
        (0.0, 16.0),
    );

    let commands = scatter_series.render_to_commands(&coord_system)?;
    renderer.render_commands(commands, bounds)?;

    let html = renderer.get_html_page("散点图Canvas演示");
    fs::write(format!("{}/03_scatter_chart_canvas.html", output_dir), html)?;
    
    println!("  ✅ 散点图Canvas演示生成完成");
    Ok(())
}

/// 生成饼图Canvas演示
fn generate_pie_chart_canvas(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        ("直接访问", 335.0),
        ("邮件营销", 310.0),
        ("联盟广告", 234.0),
        ("视频广告", 135.0),
        ("搜索引擎", 1548.0),
    ];

    let pie_series = PieSeries::new("访问来源")
        .data(data)
        .center(0.5, 0.5)
        .radius(0.6);

    let bounds = Bounds {
        origin: Point { x: 0.0, y: 0.0 },
        size: Size { width: 800.0, height: 500.0 },
    };

    let mut renderer = HtmlCanvasRenderer::new("pieChart", bounds);
    
    let coord_system = CartesianCoordinateSystem::new(bounds, (0.0, 1.0), (0.0, 1.0));

    let commands = pie_series.render_to_commands(&coord_system)?;
    renderer.render_commands(commands, bounds)?;

    let html = renderer.get_html_page("饼图Canvas演示");
    fs::write(format!("{}/04_pie_chart_canvas.html", output_dir), html)?;
    
    println!("  ✅ 饼图Canvas演示生成完成");
    Ok(())
}

/// 生成蜡烛图Canvas演示
fn generate_candlestick_chart_canvas(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        CandlestickDataItem::new(1.0, 2320.26, 2320.26, 2287.3, 2308.38).volume(23000000.0),
        CandlestickDataItem::new(2.0, 2300.0, 2291.3, 2288.26, 2308.38).volume(18500000.0),
        CandlestickDataItem::new(3.0, 2295.35, 2346.5, 2295.35, 2346.92).volume(32000000.0),
        CandlestickDataItem::new(4.0, 2347.22, 2358.98, 2337.35, 2363.8).volume(28000000.0),
        CandlestickDataItem::new(5.0, 2360.75, 2382.48, 2347.89, 2383.76).volume(35000000.0),
    ];

    let candlestick_series = CandlestickSeries::new("股票K线")
        .data(data)
        .colors(Color::rgb(0.2, 0.8, 0.2), Color::rgb(0.8, 0.2, 0.2))
        .candle_width(0.7);

    let bounds = Bounds {
        origin: Point { x: 0.0, y: 0.0 },
        size: Size { width: 800.0, height: 500.0 },
    };

    let mut renderer = HtmlCanvasRenderer::new("candlestickChart", bounds);
    
    let coord_system = CartesianCoordinateSystem::new(
        Bounds {
            origin: Point { x: 60.0, y: 60.0 },
            size: Size { width: 680.0, height: 380.0 },
        },
        (0.0, 6.0),
        (2280.0, 2390.0),
    );

    let commands = candlestick_series.render_to_commands(&coord_system)?;
    renderer.render_commands(commands, bounds)?;

    let html = renderer.get_html_page("蜡烛图Canvas演示");
    fs::write(format!("{}/05_candlestick_chart_canvas.html", output_dir), html)?;
    
    println!("  ✅ 蜡烛图Canvas演示生成完成");
    Ok(())
}

/// 生成混合图表Canvas演示
fn generate_mixed_chart_canvas(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建多个系列的混合图表
    let line_data = vec![(0.0, 150.0), (1.0, 230.0), (2.0, 224.0), (3.0, 218.0), (4.0, 135.0)];
    let bar_data = vec![(0.0, 120.0), (1.0, 200.0), (2.0, 150.0), (3.0, 80.0), (4.0, 70.0)];

    let line_series = LineSeries::new("趋势线")
        .data(line_data)
        .color(Color::rgb(1.0, 0.4, 0.4))
        .smooth(true);

    let bar_series = BarSeries::new("柱状数据")
        .data(bar_data)
        .color(Color::rgba(0.4, 0.6, 1.0, 0.8))
        .bar_width(0.4);

    let bounds = Bounds {
        origin: Point { x: 0.0, y: 0.0 },
        size: Size { width: 800.0, height: 500.0 },
    };

    let mut renderer = HtmlCanvasRenderer::new("mixedChart", bounds);
    
    let coord_system = CartesianCoordinateSystem::new(
        Bounds {
            origin: Point { x: 60.0, y: 60.0 },
            size: Size { width: 680.0, height: 380.0 },
        },
        (0.0, 5.0),
        (60.0, 250.0),
    );

    // 渲染柱状图
    let bar_commands = bar_series.render_to_commands(&coord_system)?;
    renderer.render_commands(bar_commands, bounds)?;

    // 渲染折线图
    let line_commands = line_series.render_to_commands(&coord_system)?;
    renderer.render_commands(line_commands, bounds)?;

    let html = renderer.get_html_page("混合图表Canvas演示");
    fs::write(format!("{}/06_mixed_chart_canvas.html", output_dir), html)?;
    
    println!("  ✅ 混合图表Canvas演示生成完成");
    Ok(())
}

/// 生成Canvas展示页面
fn generate_canvas_showcase(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts-rs Canvas渲染器演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1400px; margin: 0 auto; }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .section {
            background: rgba(255,255,255,0.95);
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .chart-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        .chart-link {
            display: block;
            text-decoration: none;
            color: inherit;
        }
        .chart-preview {
            width: 100%;
            height: 200px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h2 { color: #333; margin-bottom: 20px; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        .description {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .technical-info {
            background: rgba(255,255,255,0.05);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .renderer-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .renderer-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 ECharts-rs Canvas渲染器演示</h1>
            <p class="description">展现HTML5 Canvas渲染器的强大功能和高质量图表渲染能力</p>
            <div class="feature-list">
                <div class="feature">
                    <h3>🎨 Canvas渲染</h3>
                    <p>基于HTML5 Canvas的高质量渲染</p>
                </div>
                <div class="feature">
                    <h3>📊 多图表支持</h3>
                    <p>支持所有ECharts-rs图表类型</p>
                </div>
                <div class="feature">
                    <h3>🌐 Web兼容</h3>
                    <p>完美兼容现代Web浏览器</p>
                </div>
                <div class="feature">
                    <h3>⚡ 高性能</h3>
                    <p>优化的渲染算法和批量处理</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📈 基础图表Canvas渲染</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <a href="01_line_chart_canvas.html" class="chart-link">
                        <div class="chart-title">折线图Canvas演示</div>
                        <div class="chart-preview">📈 点击查看折线图Canvas渲染</div>
                        <p>展示平滑曲线、面积填充等高级特性</p>
                    </a>
                </div>
                <div class="chart-item">
                    <a href="02_bar_chart_canvas.html" class="chart-link">
                        <div class="chart-title">柱状图Canvas演示</div>
                        <div class="chart-preview">📊 点击查看柱状图Canvas渲染</div>
                        <p>展示柱状图的精确渲染和边框效果</p>
                    </a>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔵 高级图表Canvas渲染</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <a href="03_scatter_chart_canvas.html" class="chart-link">
                        <div class="chart-title">散点图Canvas演示</div>
                        <div class="chart-preview">🔵 点击查看散点图Canvas渲染</div>
                        <p>展示数据点的精确定位和符号渲染</p>
                    </a>
                </div>
                <div class="chart-item">
                    <a href="04_pie_chart_canvas.html" class="chart-link">
                        <div class="chart-title">饼图Canvas演示</div>
                        <div class="chart-preview">🥧 点击查看饼图Canvas渲染</div>
                        <p>展示扇形的精确绘制和颜色渐变</p>
                    </a>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>💹 专业图表Canvas渲染</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <a href="05_candlestick_chart_canvas.html" class="chart-link">
                        <div class="chart-title">蜡烛图Canvas演示</div>
                        <div class="chart-preview">📈 点击查看蜡烛图Canvas渲染</div>
                        <p>展示金融K线图的专业渲染效果</p>
                    </a>
                </div>
                <div class="chart-item">
                    <a href="06_mixed_chart_canvas.html" class="chart-link">
                        <div class="chart-title">混合图表Canvas演示</div>
                        <div class="chart-preview">🎭 点击查看混合图表Canvas渲染</div>
                        <p>展示多种图表类型的组合渲染</p>
                    </a>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 Canvas渲染器技术特性</h2>
            <div class="technical-info">
                <h3>🎨 渲染技术</h3>
                <div class="renderer-comparison">
                    <div class="renderer-item">
                        <h4>HTML5 Canvas</h4>
                        <ul>
                            <li>✅ 基于像素的精确渲染</li>
                            <li>✅ 支持复杂路径和曲线</li>
                            <li>✅ 高质量文本渲染</li>
                            <li>✅ 渐变和阴影效果</li>
                            <li>✅ 优秀的性能表现</li>
                        </ul>
                    </div>
                    <div class="renderer-item">
                        <h4>渲染优化</h4>
                        <ul>
                            <li>🚀 批量命令处理</li>
                            <li>🚀 样式缓存机制</li>
                            <li>🚀 智能变换管理</li>
                            <li>🚀 内存使用优化</li>
                            <li>🚀 渲染统计监控</li>
                        </ul>
                    </div>
                    <div class="renderer-item">
                        <h4>Web集成</h4>
                        <ul>
                            <li>🌐 现代浏览器兼容</li>
                            <li>🌐 响应式设计支持</li>
                            <li>🌐 JavaScript代码生成</li>
                            <li>🌐 HTML页面自动生成</li>
                            <li>🌐 交互功能预留</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h2>🎉 Canvas渲染器功能总结</h2>
            <p>ECharts-rs Canvas渲染器完整功能展示：</p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>✅ <strong>HTML5 Canvas支持</strong> - 基于标准Canvas API的高质量渲染</li>
                <li>✅ <strong>完整图表支持</strong> - 支持所有ECharts-rs图表类型</li>
                <li>✅ <strong>JavaScript代码生成</strong> - 自动生成可执行的Canvas代码</li>
                <li>✅ <strong>HTML页面生成</strong> - 完整的Web页面自动生成</li>
                <li>✅ <strong>样式系统完整</strong> - 支持颜色、线条、文本等所有样式</li>
                <li>✅ <strong>性能优化</strong> - 批量处理、缓存机制、统计监控</li>
                <li>✅ <strong>Web兼容性</strong> - 完美兼容现代Web浏览器</li>
                <li>✅ <strong>扩展性设计</strong> - 易于扩展和定制的架构</li>
            </ul>
        </div>
    </div>
</body>
</html>"#;

    fs::write(format!("{}/canvas_demo.html", output_dir), html_content)?;
    Ok(())
}
