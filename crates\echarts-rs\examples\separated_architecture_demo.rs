/*!
 * ECharts GPUI 分离架构演示
 *
 * 本示例展示了优化后的架构：
 * 1. 数据处理层：在Dashboard中使用SeriesProcessor处理原始数据
 * 2. 渲染层：GpuiRenderer只负责渲染已处理的Chart数据
 * 3. 缓存机制：避免重复的数据转换，提高性能
 */

use echarts_rs::{prelude::*, GpuiChartRenderer};
use gpui::Bounds as GpuiBounds;
use gpui::{
    div, px, rgb, size, AppContext, Application, Context, FontWeight, IntoElement, ParentElement,
    Render, Styled, TitlebarOptions, Window, WindowBackgroundAppearance, WindowBounds, WindowKind,
    WindowOptions,
};
use serde_json::json;
use std::collections::HashMap;

fn main() {
    println!("🚀 启动 ECharts GPUI 分离架构演示");

    let app = Application::new();

    app.run(move |cx| {
        let window_size = size(px(1200.0), px(800.0));

        let _window = cx.open_window(
            WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(GpuiBounds::centered(
                    None,
                    window_size,
                    cx,
                ))),
                titlebar: Some(TitlebarOptions {
                    title: Some("ECharts 分离架构演示".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                window_background: WindowBackgroundAppearance::Opaque,
                app_id: None,
                window_min_size: Some(size(px(800.0), px(600.0))),
                window_decorations: None,
            },
            |_window, cx| {
                println!("🪟 分离架构演示窗口已创建");
                cx.new(|cx| SeparatedArchitectureDemo::new(cx))
            },
        );
    });
}

/// 分离架构演示
struct SeparatedArchitectureDemo {
    /// 数据处理器
    data_processor: DataProcessor,
    /// 图表渲染器
    chart_renderer: GpuiChartRenderer,
    /// 已处理的图表缓存
    chart_cache: HashMap<String, Chart>,
    /// 当前显示的图表
    current_charts: Vec<String>,
}

/// 数据处理器 - 负责原始数据到Chart的转换
struct DataProcessor {
    /// 原始数据存储
    raw_data: HashMap<String, RawChartData>,
}

/// 原始图表数据
#[derive(Clone)]
struct RawChartData {
    title: String,
    chart_type: String,
    data: serde_json::Value,
    config: serde_json::Value,
}

impl SeparatedArchitectureDemo {
    fn new(_cx: &mut Context<Self>) -> Self {
        println!("📊 初始化分离架构演示");

        let mut demo = Self {
            data_processor: DataProcessor::new(),
            chart_renderer: GpuiChartRenderer::new(),
            chart_cache: HashMap::new(),
            current_charts: vec![
                "sales_trend".to_string(),
                "product_distribution".to_string(),
                "user_activity".to_string(),
                "website_traffic".to_string(),
            ],
        };

        // 预处理所有图表数据
        demo.preprocess_all_charts();

        println!("✅ 分离架构演示初始化完成");
        demo
    }

    /// 预处理所有图表数据
    fn preprocess_all_charts(&mut self) {
        println!("🔄 开始预处理所有图表数据");

        for chart_id in &self.current_charts {
            if let Some(raw_data) = self.data_processor.get_raw_data(chart_id) {
                match self.data_processor.process_chart_data(raw_data) {
                    Ok(chart) => {
                        self.chart_cache.insert(chart_id.clone(), chart);
                        println!("✅ 图表 {} 预处理完成", chart_id);
                    }
                    Err(e) => {
                        println!("❌ 图表 {} 预处理失败: {}", chart_id, e);
                    }
                }
            }
        }

        println!("🎯 所有图表预处理完成，缓存了 {} 个图表", self.chart_cache.len());
    }

    /// 获取已处理的图表
    fn get_processed_chart(&self, chart_id: &str) -> Option<&Chart> {
        self.chart_cache.get(chart_id)
    }
}

impl DataProcessor {
    fn new() -> Self {
        let mut processor = Self {
            raw_data: HashMap::new(),
        };

        // 初始化原始数据
        processor.initialize_raw_data();
        processor
    }

    /// 初始化原始数据
    fn initialize_raw_data(&mut self) {
        println!("📥 初始化原始数据");

        // 销售趋势数据
        self.raw_data.insert("sales_trend".to_string(), RawChartData {
            title: "月度销售趋势".to_string(),
            chart_type: "line".to_string(),
            data: json!([120, 200, 150, 80, 70, 110]),
            config: json!({
                "color": "#5470c6",
                "smooth": true
            }),
        });

        // 产品分布数据
        self.raw_data.insert("product_distribution".to_string(), RawChartData {
            title: "产品分布".to_string(),
            chart_type: "pie".to_string(),
            data: json!([
                {"name": "产品A", "value": 335},
                {"name": "产品B", "value": 310},
                {"name": "产品C", "value": 234},
                {"name": "产品D", "value": 135},
                {"name": "产品E", "value": 154}
            ]),
            config: json!({
                "radius": ["40%", "70%"]
            }),
        });

        // 用户活跃度数据
        self.raw_data.insert("user_activity".to_string(), RawChartData {
            title: "用户活跃度".to_string(),
            chart_type: "bar".to_string(),
            data: json!([320, 302, 301, 334, 390, 330, 320]),
            config: json!({
                "color": "#91cc75"
            }),
        });

        // 网站流量数据
        self.raw_data.insert("website_traffic".to_string(), RawChartData {
            title: "网站流量".to_string(),
            chart_type: "area".to_string(),
            data: json!([120, 132, 101, 134, 90, 230, 210]),
            config: json!({
                "color": "#fac858",
                "areaStyle": {"opacity": 0.6}
            }),
        });

        println!("✅ 原始数据初始化完成，共 {} 个数据集", self.raw_data.len());
    }

    /// 获取原始数据
    fn get_raw_data(&self, chart_id: &str) -> Option<&RawChartData> {
        self.raw_data.get(chart_id)
    }

    /// 处理图表数据 - 将原始数据转换为Chart对象
    fn process_chart_data(&self, raw_data: &RawChartData) -> Result<Chart> {
        println!("🔄 处理图表数据: {}", raw_data.title);

        let mut chart = Chart::new();
        chart.title = Some(raw_data.title.clone());
        chart.background_color = Some(Color::WHITE);

        // 根据图表类型创建系列数据
        let series_data = match raw_data.chart_type.as_str() {
            "line" => json!({
                "type": "line",
                "name": raw_data.title,
                "data": raw_data.data,
                "color": raw_data.config.get("color").unwrap_or(&json!("#5470c6")),
                "smooth": raw_data.config.get("smooth").unwrap_or(&json!(false))
            }),
            "pie" => json!({
                "type": "pie",
                "name": raw_data.title,
                "data": raw_data.data,
                "radius": raw_data.config.get("radius").unwrap_or(&json!(["0%", "70%"]))
            }),
            "bar" => json!({
                "type": "bar",
                "name": raw_data.title,
                "data": raw_data.data,
                "color": raw_data.config.get("color").unwrap_or(&json!("#91cc75"))
            }),
            "area" => json!({
                "type": "area",
                "name": raw_data.title,
                "data": raw_data.data,
                "color": raw_data.config.get("color").unwrap_or(&json!("#fac858")),
                "areaStyle": raw_data.config.get("areaStyle").unwrap_or(&json!({"opacity": 0.8}))
            }),
            _ => {
                return Err(ChartError::rendering(format!("不支持的图表类型: {}", raw_data.chart_type)));
            }
        };

        chart.series.push(series_data);

        println!("✅ 图表数据处理完成: {}", raw_data.title);
        Ok(chart)
    }
}

impl Render for SeparatedArchitectureDemo {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        div()
            .flex()
            .flex_col()
            .size_full()
            .bg(rgb(0xf5f5f5))
            .child(
                // 标题栏
                div()
                    .flex()
                    .justify_center()
                    .items_center()
                    .h(px(80.0))
                    .bg(rgb(0x2c3e50))
                    .child(
                        div()
                            .text_xl()
                            .font_weight(FontWeight::BOLD)
                            .text_color(rgb(0xffffff))
                            .child("ECharts 分离架构演示 - 数据处理与渲染分离"),
                    ),
            )
            .child(
                // 图表网格
                div()
                    .flex()
                    .flex_wrap()
                    .gap_4()
                    .p_4()
                    .children(
                        self.current_charts
                            .iter()
                            .filter_map(|chart_id| {
                                self.get_processed_chart(chart_id)
                                    .map(|chart| self.render_chart_panel(chart_id, chart))
                            })
                            .collect::<Vec<_>>(),
                    ),
            )
            .child(
                // 状态栏
                div()
                    .h(px(60.0))
                    .bg(rgb(0x495057))
                    .text_color(rgb(0xffffff))
                    .flex()
                    .items_center()
                    .justify_center()
                    .px_6()
                    .child(
                        div()
                            .text_sm()
                            .child(format!(
                                "💡 分离架构演示 | 缓存了 {} 个图表 | 数据处理与渲染完全分离",
                                self.chart_cache.len()
                            )),
                    ),
            )
    }
}

impl SeparatedArchitectureDemo {
    /// 渲染单个图表面板
    fn render_chart_panel(&self, chart_id: &str, chart: &Chart) -> impl IntoElement {
        div()
            .flex()
            .flex_col()
            .w(px(280.0))
            .h(px(320.0))
            .bg(rgb(0xffffff))
            .border_1()
            .border_color(rgb(0xe0e0e0))
            .rounded_lg()
            .shadow_sm()
            .child(
                // 图表标题
                div()
                    .flex()
                    .justify_center()
                    .items_center()
                    .h(px(40.0))
                    .bg(rgb(0xf8f9fa))
                    .border_b_1()
                    .border_color(rgb(0xe0e0e0))
                    .child(
                        div()
                            .font_weight(FontWeight::MEDIUM)
                            .text_color(rgb(0x333333))
                            .child(chart.title.clone().unwrap_or_else(|| "未知图表".to_string())),
                    ),
            )
            .child(
                // 图表内容区域
                div()
                    .flex_1()
                    .p_2()
                    .child(
                        div()
                            .size_full()
                            .flex()
                            .items_center()
                            .justify_center()
                            .child(format!("图表ID: {} (已缓存)", chart_id))
                    ),
            )
    }
}
