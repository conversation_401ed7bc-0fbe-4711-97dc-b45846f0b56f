//! 蜡烛图（K线图）实现
//!
//! 提供完整的蜡烛图功能，包括股票分析、期货交易、外汇分析等金融应用场景

use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};
use echarts_core::{
    Color, DrawCommand, Point, Series, CoordinateSystem, SeriesType, Bounds, Size, DataSet,
    draw_commands::{RectStyle},
    TextStyle, LineStyle, FontWeight, FontStyle, TextAlign, TextBaseline, LineCap, LineJoin,
    Result
};

/// 蜡烛图数据项
#[derive(Debug, Clone)]
pub struct CandlestickDataItem {
    /// 时间戳或索引
    pub timestamp: f64,
    /// 开盘价
    pub open: f64,
    /// 最高价
    pub high: f64,
    /// 最低价
    pub low: f64,
    /// 收盘价
    pub close: f64,
    /// 成交量（可选）
    pub volume: Option<f64>,
    /// 自定义标签
    pub label: Option<String>,
    /// 是否可见
    pub visible: bool,
}

impl CandlestickDataItem {
    /// 创建新的蜡烛图数据项
    pub fn new(timestamp: f64, open: f64, high: f64, low: f64, close: f64) -> Self {
        Self {
            timestamp,
            open,
            high,
            low,
            close,
            volume: None,
            label: None,
            visible: true,
        }
    }

    /// 设置成交量
    pub fn volume(mut self, volume: f64) -> Self {
        self.volume = Some(volume);
        self
    }

    /// 设置标签
    pub fn label(mut self, label: impl Into<String>) -> Self {
        self.label = Some(label.into());
        self
    }

    /// 设置可见性
    pub fn visible(mut self, visible: bool) -> Self {
        self.visible = visible;
        self
    }

    /// 判断是否为阳线（收盘价高于开盘价）
    pub fn is_bullish(&self) -> bool {
        self.close > self.open
    }

    /// 判断是否为阴线（收盘价低于开盘价）
    pub fn is_bearish(&self) -> bool {
        self.close < self.open
    }

    /// 判断是否为十字星（开盘价等于收盘价）
    pub fn is_doji(&self) -> bool {
        (self.close - self.open).abs() < f64::EPSILON
    }

    /// 获取实体高度
    pub fn body_height(&self) -> f64 {
        (self.close - self.open).abs()
    }

    /// 获取上影线长度
    pub fn upper_shadow(&self) -> f64 {
        self.high - self.open.max(self.close)
    }

    /// 获取下影线长度
    pub fn lower_shadow(&self) -> f64 {
        self.open.min(self.close) - self.low
    }
}

/// 蜡烛图样式配置
#[derive(Debug, Clone)]
pub struct CandlestickStyle {
    /// 阳线颜色（上涨）
    pub bullish_color: Color,
    /// 阴线颜色（下跌）
    pub bearish_color: Color,
    /// 十字星颜色
    pub doji_color: Color,
    /// 边框颜色
    pub border_color: Color,
    /// 边框宽度
    pub border_width: f64,
    /// 蜡烛宽度占比
    pub candle_width: f64,
    /// 影线宽度
    pub shadow_width: f64,
    /// 是否填充实体
    pub fill_body: bool,
}

impl Default for CandlestickStyle {
    fn default() -> Self {
        Self {
            bullish_color: Color::rgb(0.2, 0.8, 0.2), // 绿色（上涨）
            bearish_color: Color::rgb(0.8, 0.2, 0.2), // 红色（下跌）
            doji_color: Color::rgb(0.5, 0.5, 0.5),    // 灰色（十字星）
            border_color: Color::rgb(0.1, 0.1, 0.1),
            border_width: 1.0,
            candle_width: 0.6,
            shadow_width: 1.0,
            fill_body: true,
        }
    }
}

/// 蜡烛图标签配置
#[derive(Debug, Clone)]
pub struct CandlestickLabel {
    /// 是否显示标签
    pub show: bool,
    /// 字体大小
    pub font_size: f64,
    /// 标签颜色
    pub color: Color,
    /// 标签位置
    pub position: CandlestickLabelPosition,
    /// 显示内容
    pub content: CandlestickLabelContent,
}

impl Default for CandlestickLabel {
    fn default() -> Self {
        Self {
            show: false,
            font_size: 10.0,
            color: Color::rgb(0.2, 0.2, 0.2),
            position: CandlestickLabelPosition::Top,
            content: CandlestickLabelContent::Close,
        }
    }
}

/// 标签位置
#[derive(Debug, Clone, Copy)]
pub enum CandlestickLabelPosition {
    /// 顶部
    Top,
    /// 底部
    Bottom,
    /// 内部
    Inside,
}

/// 标签内容
#[derive(Debug, Clone, Copy)]
pub enum CandlestickLabelContent {
    /// 收盘价
    Close,
    /// 开盘价
    Open,
    /// 最高价
    High,
    /// 最低价
    Low,
    /// 成交量
    Volume,
    /// 涨跌幅
    Change,
}

/// 蜡烛图节点（用于布局计算）
#[derive(Debug, Clone)]
struct CandlestickNode {
    /// 数据项引用
    item: CandlestickDataItem,
    /// X坐标位置
    x: f64,
    /// 蜡烛宽度
    width: f64,
    /// 开盘价Y坐标
    open_y: f64,
    /// 最高价Y坐标
    high_y: f64,
    /// 最低价Y坐标
    low_y: f64,
    /// 收盘价Y坐标
    close_y: f64,
}

/// 蜡烛图系列
#[derive(Debug, Clone)]
pub struct CandlestickSeries {
    /// 基础配置
    config: ChartConfig,
    /// 数据项
    data: Vec<CandlestickDataItem>,
    /// 样式配置
    style: CandlestickStyle,
    /// 标签配置
    label: CandlestickLabel,
    /// 是否显示成交量
    show_volume: bool,
    /// 成交量高度占比
    volume_height_ratio: f64,
}

impl CandlestickSeries {
    /// 创建新的蜡烛图系列
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            config: ChartConfig {
                name: name.into(),
                ..ChartConfig::default()
            },
            data: Vec::new(),
            style: CandlestickStyle::default(),
            label: CandlestickLabel::default(),
            show_volume: false,
            volume_height_ratio: 0.3,
        }
    }

    /// 设置数据
    pub fn data(mut self, data: Vec<CandlestickDataItem>) -> Self {
        self.data = data;
        self
    }

    /// 添加数据项
    pub fn add_data(mut self, item: CandlestickDataItem) -> Self {
        self.data.push(item);
        self
    }

    /// 设置样式
    pub fn style(mut self, style: CandlestickStyle) -> Self {
        self.style = style;
        self
    }

    /// 设置阳线和阴线颜色
    pub fn colors(mut self, bullish: Color, bearish: Color) -> Self {
        self.style.bullish_color = bullish;
        self.style.bearish_color = bearish;
        self
    }

    /// 设置蜡烛宽度
    pub fn candle_width(mut self, width: f64) -> Self {
        self.style.candle_width = width.clamp(0.1, 1.0);
        self
    }

    /// 设置标签配置
    pub fn label(mut self, label: CandlestickLabel) -> Self {
        self.label = label;
        self
    }

    /// 设置是否显示成交量
    pub fn show_volume(mut self, show: bool) -> Self {
        self.show_volume = show;
        self
    }

    /// 设置成交量高度占比
    pub fn volume_height_ratio(mut self, ratio: f64) -> Self {
        self.volume_height_ratio = ratio.clamp(0.1, 0.5);
        self
    }

    /// 获取数据项数量
    pub fn data_len(&self) -> usize {
        self.data.len()
    }

    /// 计算蜡烛图布局
    fn calculate_layout(&self, bounds: Bounds) -> Vec<CandlestickNode> {
        if self.data.is_empty() {
            return Vec::new();
        }

        // 计算价格范围
        let mut min_price = f64::INFINITY;
        let mut max_price = f64::NEG_INFINITY;

        for item in &self.data {
            min_price = min_price.min(item.low);
            max_price = max_price.max(item.high);
        }

        // 添加一些边距
        let price_range = max_price - min_price;
        let margin = price_range * 0.05;
        min_price -= margin;
        max_price += margin;

        // 计算蜡烛图区域（如果显示成交量，需要预留空间）
        let chart_height = if self.show_volume {
            bounds.size.height * (1.0 - self.volume_height_ratio)
        } else {
            bounds.size.height
        };

        // 计算每个蜡烛的位置和尺寸
        let candle_count = self.data.len();
        let total_width = bounds.size.width;
        let candle_spacing = total_width / candle_count as f64;
        let candle_width = candle_spacing * self.style.candle_width;

        let mut nodes = Vec::new();

        for (i, item) in self.data.iter().enumerate() {
            if !item.visible {
                continue;
            }

            let x = bounds.origin.x + (i as f64 + 0.5) * candle_spacing;

            // 将价格转换为Y坐标
            let price_to_y = |price: f64| {
                bounds.origin.y + chart_height * (1.0 - (price - min_price) / (max_price - min_price))
            };

            nodes.push(CandlestickNode {
                item: item.clone(),
                x,
                width: candle_width,
                open_y: price_to_y(item.open),
                high_y: price_to_y(item.high),
                low_y: price_to_y(item.low),
                close_y: price_to_y(item.close),
            });
        }

        nodes
    }

    /// 生成蜡烛图和标签
    fn generate_candles_and_labels(&self, nodes: &[CandlestickNode]) -> Vec<DrawCommand> {
        let mut commands = Vec::new();

        for node in nodes {
            // 生成蜡烛图形
            commands.extend(self.generate_candle_shape(node));

            // 生成标签
            if self.label.show {
                commands.extend(self.generate_candle_label(node));
            }
        }

        commands
    }

    /// 生成单个蜡烛的图形
    fn generate_candle_shape(&self, node: &CandlestickNode) -> Vec<DrawCommand> {
        let mut commands = Vec::new();

        // 确定颜色
        let color = if node.item.is_doji() {
            self.style.doji_color
        } else if node.item.is_bullish() {
            self.style.bullish_color
        } else {
            self.style.bearish_color
        };

        // 绘制影线（上影线和下影线）
        let shadow_x = node.x;

        // 上影线
        if node.item.upper_shadow() > 0.0 {
            commands.push(DrawCommand::Line {
                from: Point { x: shadow_x, y: node.high_y },
                to: Point { x: shadow_x, y: node.open_y.max(node.close_y) },
                style: LineStyle {
                    color: self.style.border_color,
                    width: self.style.shadow_width,
                    dash_pattern: None,
                    cap: LineCap::Round,
                    join: LineJoin::Round,
                    opacity: 1.0,
                },
            });
        }

        // 下影线
        if node.item.lower_shadow() > 0.0 {
            commands.push(DrawCommand::Line {
                from: Point { x: shadow_x, y: node.open_y.min(node.close_y) },
                to: Point { x: shadow_x, y: node.low_y },
                style: LineStyle {
                    color: self.style.border_color,
                    width: self.style.shadow_width,
                    dash_pattern: None,
                    cap: LineCap::Round,
                    join: LineJoin::Round,
                    opacity: 1.0,
                },
            });
        }

        // 绘制实体
        let body_left = node.x - node.width / 2.0;
        let body_right = node.x + node.width / 2.0;
        let body_top = node.open_y.max(node.close_y);
        let body_bottom = node.open_y.min(node.close_y);
        let body_height = (body_top - body_bottom).abs();

        if body_height > 0.0 {
            // 实体矩形
            commands.push(DrawCommand::Rect {
                bounds: Bounds {
                    origin: Point { x: body_left, y: body_bottom },
                    size: Size { width: node.width, height: body_height },
                },
                style: RectStyle {
                    fill: if self.style.fill_body { Some(color) } else { None },
                    stroke: Some(LineStyle {
                        color: self.style.border_color,
                        width: self.style.border_width,
                        dash_pattern: None,
                        cap: LineCap::Round,
                        join: LineJoin::Round,
                        opacity: 1.0,
                    }),
                    opacity: 0.8,
                    corner_radius: 0.0,
                },
            });
        } else {
            // 十字星 - 绘制水平线
            commands.push(DrawCommand::Line {
                from: Point { x: body_left, y: node.close_y },
                to: Point { x: body_right, y: node.close_y },
                style: LineStyle {
                    color: self.style.doji_color,
                    width: self.style.border_width,
                    dash_pattern: None,
                    cap: LineCap::Round,
                    join: LineJoin::Round,
                    opacity: 1.0,
                },
            });
        }

        commands
    }

    /// 生成蜡烛标签
    fn generate_candle_label(&self, node: &CandlestickNode) -> Vec<DrawCommand> {
        let mut commands = Vec::new();

        // 计算标签文本
        let label_text = self.format_label_text(node);

        // 计算标签位置
        let label_position = self.calculate_label_position(node);

        commands.push(DrawCommand::Text {
            text: label_text,
            position: label_position,
            style: TextStyle {
                font_family: "Arial".to_string(),
                font_size: self.label.font_size,
                font_weight: FontWeight::Normal,
                font_style: FontStyle::Normal,
                color: self.label.color,
                opacity: 1.0,
                align: TextAlign::Center,
                baseline: TextBaseline::Middle,
                rotation: 0.0,
                letter_spacing: 0.0,
                line_height: 1.2,
            },
        });

        commands
    }

    /// 格式化标签文本
    fn format_label_text(&self, node: &CandlestickNode) -> String {
        match self.label.content {
            CandlestickLabelContent::Close => format!("{:.2}", node.item.close),
            CandlestickLabelContent::Open => format!("{:.2}", node.item.open),
            CandlestickLabelContent::High => format!("{:.2}", node.item.high),
            CandlestickLabelContent::Low => format!("{:.2}", node.item.low),
            CandlestickLabelContent::Volume => {
                if let Some(volume) = node.item.volume {
                    format!("{:.0}", volume)
                } else {
                    "N/A".to_string()
                }
            }
            CandlestickLabelContent::Change => {
                let change = node.item.close - node.item.open;
                let change_percent = (change / node.item.open) * 100.0;
                format!("{:+.2}%", change_percent)
            }
        }
    }

    /// 计算标签位置
    fn calculate_label_position(&self, node: &CandlestickNode) -> Point {
        match self.label.position {
            CandlestickLabelPosition::Top => Point {
                x: node.x,
                y: node.high_y - 5.0,
            },
            CandlestickLabelPosition::Bottom => Point {
                x: node.x,
                y: node.low_y + 15.0,
            },
            CandlestickLabelPosition::Inside => Point {
                x: node.x,
                y: (node.open_y + node.close_y) / 2.0,
            },
        }
    }

    /// 生成成交量柱状图
    fn generate_volume_bars(&self, nodes: &[CandlestickNode], bounds: Bounds) -> Vec<DrawCommand> {
        let mut commands = Vec::new();

        if !self.show_volume {
            return commands;
        }

        // 计算成交量范围
        let max_volume = nodes.iter()
            .filter_map(|node| node.item.volume)
            .fold(0.0, f64::max);

        if max_volume <= 0.0 {
            return commands;
        }

        // 成交量区域
        let volume_area_height = bounds.size.height * self.volume_height_ratio;
        let volume_area_top = bounds.origin.y + bounds.size.height - volume_area_height;

        for node in nodes {
            if let Some(volume) = node.item.volume {
                let volume_height = (volume / max_volume) * volume_area_height;
                let volume_bottom = bounds.origin.y + bounds.size.height;
                let volume_top = volume_bottom - volume_height;

                // 成交量颜色与蜡烛颜色一致
                let volume_color = if node.item.is_bullish() {
                    self.style.bullish_color
                } else {
                    self.style.bearish_color
                };

                commands.push(DrawCommand::Rect {
                    bounds: Bounds {
                        origin: Point {
                            x: node.x - node.width / 2.0,
                            y: volume_top
                        },
                        size: Size {
                            width: node.width,
                            height: volume_height
                        },
                    },
                    style: RectStyle {
                        fill: Some(volume_color),
                        stroke: None,
                        opacity: 0.6,
                        corner_radius: 0.0,
                    },
                });
            }
        }

        commands
    }
}

impl Series for CandlestickSeries {
    fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        if self.data.is_empty() {
            return Ok(commands);
        }

        // 获取绘图区域
        let bounds = coord_system.bounds();

        // 计算布局
        let nodes = self.calculate_layout(bounds);

        // 生成蜡烛图和标签
        commands.extend(self.generate_candles_and_labels(&nodes));

        // 生成成交量柱状图
        commands.extend(self.generate_volume_bars(&nodes, bounds));

        Ok(commands)
    }

    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Candlestick
    }

    fn bounds(&self) -> Option<Bounds> {
        // 蜡烛图使用整个可用空间
        None
    }

    fn is_visible(&self) -> bool {
        true
    }

    fn z_index(&self) -> i32 {
        0
    }

    fn clone_series(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }
}

#[cfg(test)]
mod tests {
use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};
    use super::*;
    use echarts_core::{CartesianCoordinateSystem, Point, Size};

    #[test]
    fn test_candlestick_data_item() {
        let item = CandlestickDataItem::new(1.0, 100.0, 110.0, 95.0, 105.0)
            .volume(1000.0)
            .label("测试K线")
            .visible(true);

        assert_eq!(item.timestamp, 1.0);
        assert_eq!(item.open, 100.0);
        assert_eq!(item.high, 110.0);
        assert_eq!(item.low, 95.0);
        assert_eq!(item.close, 105.0);
        assert_eq!(item.volume, Some(1000.0));
        assert_eq!(item.label, Some("测试K线".to_string()));
        assert!(item.visible);
        assert!(item.is_bullish());
        assert!(!item.is_bearish());
        assert!(!item.is_doji());
    }

    #[test]
    fn test_candlestick_data_item_bearish() {
        let item = CandlestickDataItem::new(1.0, 100.0, 105.0, 90.0, 95.0);

        assert!(!item.is_bullish());
        assert!(item.is_bearish());
        assert!(!item.is_doji());
        assert_eq!(item.body_height(), 5.0);
        assert_eq!(item.upper_shadow(), 5.0);
        assert_eq!(item.lower_shadow(), 5.0);
    }

    #[test]
    fn test_candlestick_data_item_doji() {
        let item = CandlestickDataItem::new(1.0, 100.0, 105.0, 95.0, 100.0);

        assert!(!item.is_bullish());
        assert!(!item.is_bearish());
        assert!(item.is_doji());
        assert_eq!(item.body_height(), 0.0);
        assert_eq!(item.upper_shadow(), 5.0);
        assert_eq!(item.lower_shadow(), 5.0);
    }

    #[test]
    fn test_candlestick_style() {
        let style = CandlestickStyle {
            bullish_color: Color::rgb(0.0, 1.0, 0.0),
            bearish_color: Color::rgb(1.0, 0.0, 0.0),
            doji_color: Color::rgb(0.5, 0.5, 0.5),
            border_color: Color::rgb(0.0, 0.0, 0.0),
            border_width: 2.0,
            candle_width: 0.8,
            shadow_width: 1.5,
            fill_body: true,
        };

        assert_eq!(style.bullish_color, Color::rgb(0.0, 1.0, 0.0));
        assert_eq!(style.bearish_color, Color::rgb(1.0, 0.0, 0.0));
        assert_eq!(style.border_width, 2.0);
        assert_eq!(style.candle_width, 0.8);
        assert!(style.fill_body);
    }

    #[test]
    fn test_candlestick_label() {
        let label = CandlestickLabel {
            show: true,
            font_size: 12.0,
            color: Color::rgb(0.1, 0.1, 0.1),
            position: CandlestickLabelPosition::Top,
            content: CandlestickLabelContent::Close,
        };

        assert!(label.show);
        assert_eq!(label.font_size, 12.0);
        assert!(matches!(label.position, CandlestickLabelPosition::Top));
        assert!(matches!(label.content, CandlestickLabelContent::Close));
    }

    #[test]
    fn test_candlestick_series_creation() {
        let series = CandlestickSeries::new("股票K线")
            .candle_width(0.7)
            .show_volume(true)
            .volume_height_ratio(0.25);

        assert_eq!(series.name(), "股票K线");
        assert_eq!(series.style.candle_width, 0.7);
        assert!(series.show_volume);
        assert_eq!(series.volume_height_ratio, 0.25);
    }

    #[test]
    fn test_candlestick_series_with_data() {
        let data = vec![
            CandlestickDataItem::new(1.0, 100.0, 110.0, 95.0, 105.0).volume(1000.0),
            CandlestickDataItem::new(2.0, 105.0, 115.0, 100.0, 110.0).volume(1200.0),
            CandlestickDataItem::new(3.0, 110.0, 108.0, 102.0, 104.0).volume(800.0),
        ];

        let series = CandlestickSeries::new("测试数据")
            .data(data)
            .colors(Color::rgb(0.0, 0.8, 0.0), Color::rgb(0.8, 0.0, 0.0));

        assert_eq!(series.data.len(), 3);
        assert_eq!(series.data[0].open, 100.0);
        assert_eq!(series.data[1].close, 110.0);
        assert_eq!(series.data[2].volume, Some(800.0));
        assert_eq!(series.style.bullish_color, Color::rgb(0.0, 0.8, 0.0));
        assert_eq!(series.style.bearish_color, Color::rgb(0.8, 0.0, 0.0));
    }

    #[test]
    fn test_candlestick_series_rendering() {
        let data = vec![
            CandlestickDataItem::new(1.0, 100.0, 110.0, 95.0, 105.0),
            CandlestickDataItem::new(2.0, 105.0, 115.0, 100.0, 110.0),
            CandlestickDataItem::new(3.0, 110.0, 108.0, 102.0, 104.0),
        ];

        let series = CandlestickSeries::new("K线测试")
            .data(data)
            .show_volume(false);

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 600.0, height: 400.0 },
            },
            (0.0, 4.0),
            (90.0, 120.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();

        // 应该生成蜡烛图命令
        assert!(!commands.is_empty());

        // 检查命令类型
        let mut line_count = 0;
        let mut rect_count = 0;

        for cmd in &commands {
            match cmd {
                DrawCommand::Line { .. } => line_count += 1,
                DrawCommand::Rect { .. } => rect_count += 1,
                _ => {}
            }
        }

        assert!(line_count > 0); // 影线
        assert!(rect_count > 0); // 实体
    }

    #[test]
    fn test_candlestick_series_empty_data() {
        let series = CandlestickSeries::new("空K线图");

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 600.0, height: 400.0 },
            },
            (0.0, 10.0),
            (0.0, 100.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();
        assert!(commands.is_empty());
    }

    #[test]
    fn test_label_formatting() {
        let series = CandlestickSeries::new("测试");
        let node = CandlestickNode {
            item: CandlestickDataItem::new(1.0, 100.0, 110.0, 95.0, 105.0).volume(1000.0),
            x: 50.0,
            width: 10.0,
            open_y: 200.0,
            high_y: 150.0,
            low_y: 250.0,
            close_y: 180.0,
        };

        // 测试收盘价格式化
        let mut series_close = series.clone();
        series_close.label.content = CandlestickLabelContent::Close;
        let formatted = series_close.format_label_text(&node);
        assert_eq!(formatted, "105.00");

        // 测试成交量格式化
        let mut series_volume = series.clone();
        series_volume.label.content = CandlestickLabelContent::Volume;
        let formatted = series_volume.format_label_text(&node);
        assert_eq!(formatted, "1000");

        // 测试涨跌幅格式化
        let mut series_change = series.clone();
        series_change.label.content = CandlestickLabelContent::Change;
        let formatted = series_change.format_label_text(&node);
        assert_eq!(formatted, "+5.00%");
    }

    #[test]
    fn test_candlestick_with_volume() {
        let data = vec![
            CandlestickDataItem::new(1.0, 100.0, 110.0, 95.0, 105.0).volume(1000.0),
            CandlestickDataItem::new(2.0, 105.0, 115.0, 100.0, 110.0).volume(1500.0),
        ];

        let series = CandlestickSeries::new("带成交量K线")
            .data(data)
            .show_volume(true)
            .volume_height_ratio(0.3);

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 400.0, height: 400.0 },
            },
            (0.0, 3.0),
            (90.0, 120.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();

        // 应该包含成交量柱状图
        let volume_rects = commands.iter().filter(|cmd| {
            matches!(cmd, DrawCommand::Rect { .. })
        }).count();

        assert!(volume_rects >= 4); // 至少包含2个蜡烛实体 + 2个成交量柱
    }
}
// ============================================================================
// 图表基础架构集成 - ChartBase 实现
// ============================================================================

/// 为 CandlestickSeries 实现 ChartBase trait
impl ChartBase for CandlestickSeries {
    type DataType = Vec<CandlestickDataItem>;

    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn raw_data(&self) -> &Self::DataType {
        &self.data
    }

    fn to_dataset(&self) -> DataSet {
        // TODO: 实现 Vec<CandlestickDataItem> 到 DataSet 的转换
        DataSet::new()
    }
    
    fn visible(&self) -> bool {
        self.config.visible
    }
    
    fn z_index(&self) -> i32 {
        self.config.z_index
    }
    
    fn bounds(&self) -> Option<Bounds> {
        // TODO: 为 CandlestickSeries 实现边界计算
        None
    }
    
    fn config(&self) -> &ChartConfig {
        &self.config
    }
    
    fn config_mut(&mut self) -> &mut ChartConfig {
        &mut self.config
    }
}

/// 为 CandlestickSeries 实现 ChartSeries trait
impl ChartSeries for CandlestickSeries {
    // 所有方法都由默认实现提供，基于 ChartBase
}
