//! Image renderer for ECharts-rs

use echarts_core::*;
use echarts_renderer::{RenderContext, Renderer};
use serde::{Deserialize, Serialize};

use image::{ImageBuffer, Rgba, RgbaImage};
use imageproc::drawing::{
    draw_filled_circle_mut, draw_filled_rect_mut, draw_line_segment_mut, draw_text_mut,
};
use imageproc::rect::Rect;
use rusttype::{Font, Scale};

/// Image renderer implementation
pub struct ImageRenderer {
    /// Image buffer
    image: RgbaImage,
    /// Render settings
    settings: ImageRenderSettings,
    /// Font for text rendering
    font: Option<Font<'static>>,
}

/// Image rendering settings
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ImageRenderSettings {
    /// Image width
    pub width: u32,
    /// Image height
    pub height: u32,
    /// Background color
    pub background_color: Color,
    /// Image format
    pub format: ImageFormat,
    /// Quality (for JPEG)
    pub quality: u8,
    /// Anti-aliasing
    pub anti_aliasing: bool,
    /// DPI scaling
    pub dpi_scale: f32,
}

/// Supported image formats
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum ImageFormat {
    Png,
    Jpeg,
    Bmp,
    Tiff,
}

impl Default for ImageRenderSettings {
    fn default() -> Self {
        Self {
            width: 800,
            height: 600,
            background_color: Color::WHITE,
            format: ImageFormat::Png,
            quality: 90,
            anti_aliasing: true,
            dpi_scale: 1.0,
        }
    }
}

impl ImageRenderer {
    /// Create a new image renderer
    pub fn new(settings: ImageRenderSettings) -> Self {
        let image = ImageBuffer::from_pixel(
            settings.width,
            settings.height,
            Self::color_to_rgba(&settings.background_color),
        );

        Self {
            image,
            settings,
            font: Self::load_default_font(),
        }
    }

    /// Create a new image renderer with default settings
    pub fn with_size(width: u32, height: u32) -> Self {
        let settings = ImageRenderSettings {
            width,
            height,
            ..Default::default()
        };
        Self::new(settings)
    }

    /// Load default font
    fn load_default_font() -> Option<Font<'static>> {
        // In a real implementation, you would load a font file
        // For now, we'll use a placeholder
        None
    }

    /// Convert Color to image::Rgba
    fn color_to_rgba(color: &Color) -> Rgba<u8> {
        Rgba([color.r, color.g, color.b, color.a])
    }

    /// Draw a rectangle
    pub fn draw_rect(&mut self, bounds: Bounds, fill: Option<Color>, stroke: Option<(Color, f64)>) {
        let rect = Rect::at(bounds.origin.x as i32, bounds.origin.y as i32)
            .of_size(bounds.width() as u32, bounds.height() as u32);

        // Draw fill
        if let Some(fill_color) = fill {
            draw_filled_rect_mut(&mut self.image, rect, Self::color_to_rgba(&fill_color));
        }

        // Draw stroke
        if let Some((stroke_color, stroke_width)) = stroke {
            let stroke_rgba = Self::color_to_rgba(&stroke_color);
            let width = stroke_width as i32;

            // Draw stroke as multiple rectangles (simplified approach)
            for i in 0..width {
                // Top
                let top_rect = Rect::at(rect.left() - i, rect.top() - i)
                    .of_size(rect.width() + 2 * i as u32, 1);
                if top_rect.top() >= 0 {
                    draw_filled_rect_mut(&mut self.image, top_rect, stroke_rgba);
                }

                // Bottom
                let bottom_rect = Rect::at(rect.left() - i, rect.bottom() + i)
                    .of_size(rect.width() + 2 * i as u32, 1);
                if bottom_rect.bottom() < self.settings.height as i32 {
                    draw_filled_rect_mut(&mut self.image, bottom_rect, stroke_rgba);
                }

                // Left
                let left_rect = Rect::at(rect.left() - i, rect.top() - i)
                    .of_size(1, rect.height() + 2 * i as u32);
                if left_rect.left() >= 0 {
                    draw_filled_rect_mut(&mut self.image, left_rect, stroke_rgba);
                }

                // Right
                let right_rect = Rect::at(rect.right() + i, rect.top() - i)
                    .of_size(1, rect.height() + 2 * i as u32);
                if right_rect.right() < self.settings.width as i32 {
                    draw_filled_rect_mut(&mut self.image, right_rect, stroke_rgba);
                }
            }
        }
    }

    /// Draw a line
    pub fn draw_line(&mut self, from: Point, to: Point, color: Color, width: f64) {
        let start = (from.x as f32, from.y as f32);
        let end = (to.x as f32, to.y as f32);
        let rgba = Self::color_to_rgba(&color);

        // For thick lines, draw multiple parallel lines
        if width > 1.0 {
            let half_width = width / 2.0;
            for i in 0..(width as i32) {
                let offset = i as f32 - half_width as f32;

                // Calculate perpendicular offset
                let dx = end.0 - start.0;
                let dy = end.1 - start.1;
                let length = (dx * dx + dy * dy).sqrt();

                if length > 0.0 {
                    let perp_x = -dy / length * offset;
                    let perp_y = dx / length * offset;

                    let offset_start = (start.0 + perp_x, start.1 + perp_y);
                    let offset_end = (end.0 + perp_x, end.1 + perp_y);

                    draw_line_segment_mut(&mut self.image, offset_start, offset_end, rgba);
                }
            }
        } else {
            draw_line_segment_mut(&mut self.image, start, end, rgba);
        }
    }

    /// Draw a circle
    pub fn draw_circle(
        &mut self,
        center: Point,
        radius: f64,
        fill: Option<Color>,
        stroke: Option<(Color, f64)>,
    ) {
        let center_point = (center.x as i32, center.y as i32);
        let radius_i32 = radius as i32;

        // Draw fill
        if let Some(fill_color) = fill {
            draw_filled_circle_mut(
                &mut self.image,
                center_point,
                radius_i32,
                Self::color_to_rgba(&fill_color),
            );
        }

        // Draw stroke (simplified - draw multiple circles)
        if let Some((stroke_color, stroke_width)) = stroke {
            let stroke_rgba = Self::color_to_rgba(&stroke_color);
            let width = stroke_width as i32;

            for i in 0..width {
                let stroke_radius = radius_i32 + i;
                if stroke_radius > 0 {
                    // This is a simplified stroke - in a real implementation,
                    // you would draw only the outline
                    draw_filled_circle_mut(
                        &mut self.image,
                        center_point,
                        stroke_radius,
                        stroke_rgba,
                    );
                }
            }
        }
    }

    /// Draw text
    pub fn draw_text(&mut self, text: &str, position: Point, style: &TextStyle) {
        if let Some(ref font) = self.font {
            let scale = Scale::uniform(style.font_size as f32 * self.settings.dpi_scale);
            let color = Self::color_to_rgba(&style.color);

            draw_text_mut(
                &mut self.image,
                color,
                position.x as i32,
                position.y as i32,
                scale,
                font,
                text,
            );
        } else {
            // Fallback: draw a simple rectangle to represent text
            let text_width = text.len() as f64 * style.font_size * 0.6;
            let text_height = style.font_size;
            let bounds = Bounds::new(position.x, position.y, text_width, text_height);

            self.draw_rect(bounds, Some(Color::from_rgba(200, 200, 200, 100)), None);
        }
    }

    /// Draw a path (simplified implementation)
    pub fn draw_path(&mut self, path: &Path, fill: Option<Color>, stroke: Option<(Color, f64)>) {
        // Simplified path rendering - convert to line segments
        let mut current_point = Point::new(0.0, 0.0);
        let mut path_points = Vec::new();

        for command in &path.commands {
            match command {
                PathCommand::MoveTo(point) => {
                    current_point = *point;
                    path_points.clear();
                    path_points.push(current_point);
                }
                PathCommand::LineTo(point) => {
                    if let Some((stroke_color, stroke_width)) = stroke {
                        self.draw_line(current_point, *point, stroke_color, stroke_width);
                    }
                    current_point = *point;
                    path_points.push(current_point);
                }
                PathCommand::CurveTo {
                    control1: _,
                    control2: _,
                    to,
                } => {
                    // Simplified: draw straight line to end point
                    if let Some((stroke_color, stroke_width)) = stroke {
                        self.draw_line(current_point, *to, stroke_color, stroke_width);
                    }
                    current_point = *to;
                    path_points.push(current_point);
                }
                PathCommand::QuadTo { control: _, to } => {
                    // Simplified: draw straight line to end point
                    if let Some((stroke_color, stroke_width)) = stroke {
                        self.draw_line(current_point, *to, stroke_color, stroke_width);
                    }
                    current_point = *to;
                    path_points.push(current_point);
                }
                PathCommand::Close => {
                    if let Some(first_point) = path_points.first() {
                        if let Some((stroke_color, stroke_width)) = stroke {
                            self.draw_line(current_point, *first_point, stroke_color, stroke_width);
                        }
                    }
                }
            }
        }

        // Fill path (simplified - fill bounding box)
        if let Some(fill_color) = fill {
            if !path_points.is_empty() {
                let min_x = path_points
                    .iter()
                    .map(|p| p.x)
                    .fold(f64::INFINITY, f64::min);
                let max_x = path_points
                    .iter()
                    .map(|p| p.x)
                    .fold(f64::NEG_INFINITY, f64::max);
                let min_y = path_points
                    .iter()
                    .map(|p| p.y)
                    .fold(f64::INFINITY, f64::min);
                let max_y = path_points
                    .iter()
                    .map(|p| p.y)
                    .fold(f64::NEG_INFINITY, f64::max);

                let bounds = Bounds::new(min_x, min_y, max_x - min_x, max_y - min_y);
                self.draw_rect(bounds, Some(fill_color), None);
            }
        }
    }

    /// Get the image buffer
    pub fn get_image(&self) -> &RgbaImage {
        &self.image
    }

    /// Save image to file
    pub fn save_to_file(&self, path: &str) -> Result<()> {
        match self.settings.format {
            ImageFormat::Png => self.image.save_with_format(path, image::ImageFormat::Png),
            ImageFormat::Jpeg => {
                // Convert RGBA to RGB for JPEG
                let rgb_image = image::DynamicImage::ImageRgba8(self.image.clone()).to_rgb8();
                rgb_image.save_with_format(path, image::ImageFormat::Jpeg)
            }
            ImageFormat::Bmp => self.image.save_with_format(path, image::ImageFormat::Bmp),
            ImageFormat::Tiff => self.image.save_with_format(path, image::ImageFormat::Tiff),
        }
        .map_err(|e| {
            ChartError::Io(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!("Failed to save image: {}", e),
            ))
        })?;

        Ok(())
    }

    /// Get image as bytes
    pub fn to_bytes(&self) -> Result<Vec<u8>> {
        let mut bytes = Vec::new();

        match self.settings.format {
            ImageFormat::Png => {
                use image::codecs::png::PngEncoder;
                use image::ImageEncoder;

                let encoder = PngEncoder::new(&mut bytes);
                encoder
                    .write_image(
                        &self.image,
                        self.settings.width,
                        self.settings.height,
                        image::ColorType::Rgba8,
                    )
                    .map_err(|e| {
                        ChartError::Io(std::io::Error::new(
                            std::io::ErrorKind::Other,
                            format!("Failed to encode PNG: {}", e),
                        ))
                    })?;
            }
            ImageFormat::Jpeg => {
                use image::codecs::jpeg::JpegEncoder;
                use image::ImageEncoder;

                // Convert to RGB
                let rgb_image = image::DynamicImage::ImageRgba8(self.image.clone()).to_rgb8();
                let encoder = JpegEncoder::new_with_quality(&mut bytes, self.settings.quality);
                encoder
                    .write_image(
                        &rgb_image,
                        self.settings.width,
                        self.settings.height,
                        image::ColorType::Rgb8,
                    )
                    .map_err(|e| {
                        ChartError::Io(std::io::Error::new(
                            std::io::ErrorKind::Other,
                            format!("Failed to encode JPEG: {}", e),
                        ))
                    })?;
            }
            _ => {
                return Err(ChartError::Io(std::io::Error::new(
                    std::io::ErrorKind::InvalidInput,
                    "Unsupported format for byte export",
                )));
            }
        }

        Ok(bytes)
    }
}

impl Renderer for ImageRenderer {
    fn render_chart(&mut self, _chart: &Chart, bounds: Bounds) -> Result<()> {
        self.settings.width = bounds.width() as u32;
        self.settings.height = bounds.height() as u32;

        // Recreate image buffer with new size
        self.image = ImageBuffer::from_pixel(
            self.settings.width,
            self.settings.height,
            Self::color_to_rgba(&self.settings.background_color),
        );

        // Rendering logic would be implemented here
        Ok(())
    }

    fn clear(&mut self) {
        self.image = ImageBuffer::from_pixel(
            self.settings.width,
            self.settings.height,
            Self::color_to_rgba(&self.settings.background_color),
        );
    }

    fn bounds(&self) -> Bounds {
        Bounds::new(
            0.0,
            0.0,
            self.settings.width as f64,
            self.settings.height as f64,
        )
    }

    fn set_bounds(&mut self, bounds: Bounds) {
        self.settings.width = bounds.width() as u32;
        self.settings.height = bounds.height() as u32;

        // Recreate image buffer with new size
        self.image = ImageBuffer::from_pixel(
            self.settings.width,
            self.settings.height,
            Self::color_to_rgba(&self.settings.background_color),
        );
    }
}

/// Image export utilities
pub struct ImageExporter;

impl ImageExporter {
    /// Export chart to image bytes
    pub fn export_to_bytes(
        chart: &Chart,
        width: u32,
        height: u32,
        format: ImageFormat,
    ) -> Result<Vec<u8>> {
        let settings = ImageRenderSettings {
            width,
            height,
            format,
            ..Default::default()
        };

        let mut renderer = ImageRenderer::new(settings);

        // TODO: Implement actual chart rendering

        renderer.to_bytes()
    }

    /// Export chart to image file
    pub fn export_to_file(
        chart: &Chart,
        path: &str,
        width: u32,
        height: u32,
        format: ImageFormat,
    ) -> Result<()> {
        let settings = ImageRenderSettings {
            width,
            height,
            format,
            ..Default::default()
        };

        let renderer = ImageRenderer::new(settings);

        // TODO: Implement actual chart rendering

        renderer.save_to_file(path)
    }

    /// Export chart with custom settings
    pub fn export_with_settings(chart: &Chart, settings: ImageRenderSettings) -> Result<Vec<u8>> {
        let mut renderer = ImageRenderer::new(settings);

        // TODO: Implement actual chart rendering

        renderer.to_bytes()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_image_renderer_creation() {
        let renderer = ImageRenderer::with_size(800, 600);
        assert_eq!(renderer.get_size(), (800.0, 600.0));
    }

    #[test]
    fn test_image_basic_rendering() {
        let mut renderer = ImageRenderer::with_size(400, 300);

        // Draw a simple rectangle
        let bounds = Bounds::new(10.0, 10.0, 100.0, 50.0);
        renderer.draw_rect(bounds, Some(Color::RED), Some((Color::BLACK, 2.0)));

        // Check that image was modified
        let image = renderer.get_image();
        assert_eq!(image.width(), 400);
        assert_eq!(image.height(), 300);
    }

    #[test]
    fn test_color_conversion() {
        let rgba = ImageRenderer::color_to_rgba(&Color::RED);
        assert_eq!(rgba, Rgba([255, 0, 0, 255]));

        let rgba_transparent = ImageRenderer::color_to_rgba(&Color::from_rgba(255, 0, 0, 128));
        assert_eq!(rgba_transparent, Rgba([255, 0, 0, 128]));
    }

    #[test]
    fn test_image_formats() {
        let settings = ImageRenderSettings {
            width: 100,
            height: 100,
            format: ImageFormat::Png,
            ..Default::default()
        };

        let renderer = ImageRenderer::new(settings);
        assert!(renderer.to_bytes().is_ok());
    }
}
