{"rustc": 1842507548689473721, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 2793069214174217655, "deps": [[1988483478007900009, "unicode_ident", false, 14068132524805388180], [3060637413840920116, "proc_macro2", false, 14205022684772405266], [17990358020177143287, "quote", false, 11630545452024400737]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-c2681b99a7eaa5c2\\dep-lib-syn", "checksum": false}}], "rustflags": ["-C", "link-arg=/STACK:16000000"], "config": 2069994364910194474, "compile_kind": 0}