//! 交互事件定义
//!
//! 定义所有交互相关的事件类型和数据结构

use echarts_core::Point;
use serde::{Serialize, Deserialize};

/// 交互事件
#[derive(Debug, Clone)]
pub enum InteractionEvent {
    /// 鼠标事件
    Mouse(MouseEvent),
    /// 键盘事件
    Key(KeyEvent),
    /// 触摸事件
    Touch(TouchEvent),
    /// 滚轮事件
    Wheel(WheelEvent),
    /// 自定义事件
    Custom {
        name: String,
        data: serde_json::Value,
    },
}

/// 鼠标事件
#[derive(Debug, Clone)]
pub struct MouseEvent {
    /// 鼠标位置
    pub position: Point,
    /// 事件类型
    pub event_type: MouseEventType,
    /// 鼠标按键
    pub button: MouseButton,
    /// 修饰键状态
    pub modifiers: KeyModifiers,
}

/// 鼠标事件类型
#[derive(Debug, Clone, PartialEq)]
pub enum MouseEventType {
    /// 鼠标移动
    Move,
    /// 鼠标按下
    Down,
    /// 鼠标释放
    Up,
    /// 鼠标点击
    Click,
    /// 鼠标双击
    DoubleClick,
    /// 鼠标进入
    Enter,
    /// 鼠标离开
    Leave,
}

/// 鼠标按键
#[derive(Debug, Clone, PartialEq)]
pub enum MouseButton {
    /// 左键
    Left,
    /// 右键
    Right,
    /// 中键
    Middle,
    /// 其他按键
    Other(u8),
}

/// 键盘事件
#[derive(Debug, Clone)]
pub struct KeyEvent {
    /// 按键
    pub key: String,
    /// 事件类型
    pub event_type: KeyEventType,
    /// 修饰键状态
    pub modifiers: KeyModifiers,
}

/// 键盘事件类型
#[derive(Debug, Clone, PartialEq)]
pub enum KeyEventType {
    /// 按键按下
    Down,
    /// 按键释放
    Up,
    /// 按键输入（字符）
    Input,
}

/// 修饰键状态
#[derive(Debug, Clone, Default)]
pub struct KeyModifiers {
    /// Ctrl键
    pub ctrl: bool,
    /// Shift键
    pub shift: bool,
    /// Alt键
    pub alt: bool,
    /// Meta键（Windows键/Cmd键）
    pub meta: bool,
}

/// 触摸事件
#[derive(Debug, Clone)]
pub struct TouchEvent {
    /// 触摸位置
    pub position: Point,
    /// 事件类型
    pub event_type: TouchEventType,
    /// 触摸点ID
    pub touch_id: u32,
    /// 触摸压力（0.0-1.0）
    pub pressure: f64,
}

/// 触摸事件类型
#[derive(Debug, Clone, PartialEq)]
pub enum TouchEventType {
    /// 触摸开始
    Start,
    /// 触摸移动
    Move,
    /// 触摸结束
    End,
    /// 触摸取消
    Cancel,
}

/// 滚轮事件
#[derive(Debug, Clone)]
pub struct WheelEvent {
    /// 鼠标位置
    pub position: Point,
    /// 水平滚动量
    pub delta_x: f64,
    /// 垂直滚动量
    pub delta_y: f64,
    /// 滚动模式
    pub delta_mode: WheelDeltaMode,
    /// 修饰键状态
    pub modifiers: KeyModifiers,
}

/// 滚轮滚动模式
#[derive(Debug, Clone, PartialEq)]
pub enum WheelDeltaMode {
    /// 像素模式
    Pixel,
    /// 行模式
    Line,
    /// 页面模式
    Page,
}

/// 交互结果
#[derive(Debug, Clone)]
pub enum InteractionResult {
    /// 无操作
    None,
    /// 请求重绘
    Redraw,
    /// 请求数据更新
    UpdateData,
    /// 缩放操作
    Zoom {
        center: Point,
        factor: f64,
    },
    /// 平移操作
    Pan {
        delta: Point,
    },
    /// 选择操作
    Select {
        elements: Vec<SelectedElement>,
    },
    /// 显示工具提示
    ShowTooltip {
        position: Point,
        content: TooltipContent,
    },
    /// 隐藏工具提示
    HideTooltip,
    /// 显示上下文菜单
    ShowContextMenu {
        position: Point,
        items: Vec<ContextMenuItem>,
    },
    /// 自定义结果
    Custom {
        name: String,
        data: serde_json::Value,
    },
}

/// 选中元素
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SelectedElement {
    /// 系列索引
    pub series_index: usize,
    /// 数据点索引
    pub data_index: usize,
    /// 元素类型
    pub element_type: String,
    /// 附加数据
    pub data: serde_json::Value,
}

/// 工具提示内容
#[derive(Debug, Clone)]
pub struct TooltipContent {
    /// 标题
    pub title: Option<String>,
    /// 内容项
    pub items: Vec<TooltipItem>,
    /// 自定义HTML内容
    pub html: Option<String>,
}

/// 工具提示项
#[derive(Debug, Clone)]
pub struct TooltipItem {
    /// 标签
    pub label: String,
    /// 值
    pub value: String,
    /// 颜色标记
    pub color: Option<echarts_core::Color>,
    /// 系列名称
    pub series_name: Option<String>,
}

/// 上下文菜单项
#[derive(Debug, Clone)]
pub struct ContextMenuItem {
    /// 项目ID
    pub id: String,
    /// 显示标签
    pub label: String,
    /// 图标
    pub icon: Option<String>,
    /// 是否启用
    pub enabled: bool,
    /// 子菜单
    pub submenu: Option<Vec<ContextMenuItem>>,
    /// 分隔符
    pub separator: bool,
}

impl MouseEvent {
    /// 创建鼠标移动事件
    pub fn move_event(position: Point) -> Self {
        Self {
            position,
            event_type: MouseEventType::Move,
            button: MouseButton::Left,
            modifiers: KeyModifiers::default(),
        }
    }

    /// 创建鼠标点击事件
    pub fn click_event(position: Point, button: MouseButton) -> Self {
        Self {
            position,
            event_type: MouseEventType::Click,
            button,
            modifiers: KeyModifiers::default(),
        }
    }

    /// 创建鼠标按下事件
    pub fn down_event(position: Point, button: MouseButton) -> Self {
        Self {
            position,
            event_type: MouseEventType::Down,
            button,
            modifiers: KeyModifiers::default(),
        }
    }

    /// 创建鼠标释放事件
    pub fn up_event(position: Point, button: MouseButton) -> Self {
        Self {
            position,
            event_type: MouseEventType::Up,
            button,
            modifiers: KeyModifiers::default(),
        }
    }
}

impl KeyEvent {
    /// 创建按键按下事件
    pub fn down_event(key: impl Into<String>) -> Self {
        Self {
            key: key.into(),
            event_type: KeyEventType::Down,
            modifiers: KeyModifiers::default(),
        }
    }

    /// 创建按键释放事件
    pub fn up_event(key: impl Into<String>) -> Self {
        Self {
            key: key.into(),
            event_type: KeyEventType::Up,
            modifiers: KeyModifiers::default(),
        }
    }
}

impl WheelEvent {
    /// 创建滚轮事件
    pub fn new(position: Point, delta_x: f64, delta_y: f64) -> Self {
        Self {
            position,
            delta_x,
            delta_y,
            delta_mode: WheelDeltaMode::Pixel,
            modifiers: KeyModifiers::default(),
        }
    }
}

impl TouchEvent {
    /// 创建触摸开始事件
    pub fn start_event(position: Point, touch_id: u32) -> Self {
        Self {
            position,
            event_type: TouchEventType::Start,
            touch_id,
            pressure: 1.0,
        }
    }

    /// 创建触摸移动事件
    pub fn move_event(position: Point, touch_id: u32) -> Self {
        Self {
            position,
            event_type: TouchEventType::Move,
            touch_id,
            pressure: 1.0,
        }
    }

    /// 创建触摸结束事件
    pub fn end_event(position: Point, touch_id: u32) -> Self {
        Self {
            position,
            event_type: TouchEventType::End,
            touch_id,
            pressure: 0.0,
        }
    }
}

impl TooltipContent {
    /// 创建简单的工具提示内容
    pub fn simple(title: impl Into<String>, value: impl Into<String>) -> Self {
        Self {
            title: Some(title.into()),
            items: vec![TooltipItem {
                label: "值".to_string(),
                value: value.into(),
                color: None,
                series_name: None,
            }],
            html: None,
        }
    }

    /// 创建多项工具提示内容
    pub fn multi(title: impl Into<String>, items: Vec<TooltipItem>) -> Self {
        Self {
            title: Some(title.into()),
            items,
            html: None,
        }
    }

    /// 创建HTML工具提示内容
    pub fn html(html: impl Into<String>) -> Self {
        Self {
            title: None,
            items: Vec::new(),
            html: Some(html.into()),
        }
    }
}

impl ContextMenuItem {
    /// 创建普通菜单项
    pub fn item(id: impl Into<String>, label: impl Into<String>) -> Self {
        Self {
            id: id.into(),
            label: label.into(),
            icon: None,
            enabled: true,
            submenu: None,
            separator: false,
        }
    }

    /// 创建分隔符
    pub fn separator() -> Self {
        Self {
            id: String::new(),
            label: String::new(),
            icon: None,
            enabled: false,
            submenu: None,
            separator: true,
        }
    }

    /// 设置图标
    pub fn icon(mut self, icon: impl Into<String>) -> Self {
        self.icon = Some(icon.into());
        self
    }

    /// 设置启用状态
    pub fn enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }

    /// 设置子菜单
    pub fn submenu(mut self, submenu: Vec<ContextMenuItem>) -> Self {
        self.submenu = Some(submenu);
        self
    }
}
