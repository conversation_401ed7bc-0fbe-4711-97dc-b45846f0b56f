#!/usr/bin/env python3
"""
快速修复剩余文件的导入
"""

import os
import re

def quick_fix_file(file_path):
    """快速修复单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有基础架构导入
        if "use crate::base::" in content:
            return False
        
        # 在第一个 use std:: 或文件开头添加导入
        lines = content.split('\n')
        insert_index = -1
        
        for i, line in enumerate(lines):
            if line.strip().startswith('use std::') or line.strip().startswith('use serde::'):
                insert_index = i
                break
            elif line.strip().startswith('//!') or line.strip().startswith('///') or line.strip() == '':
                continue
            elif line.strip().startswith('use '):
                # 在第一个非 echarts_core 的 use 语句前插入
                if 'echarts_core' not in line:
                    insert_index = i
                    break
        
        if insert_index == -1:
            # 找到第一个非注释、非空行
            for i, line in enumerate(lines):
                if line.strip() and not line.strip().startswith('//'):
                    insert_index = i
                    break
        
        if insert_index >= 0:
            lines.insert(insert_index, "use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            return True
        
        return False
        
    except Exception as e:
        print(f"错误: {e}")
        return False

def main():
    """主函数"""
    files = [
        "crates/charts/src/treemap.rs",
        "crates/charts/src/sunburst.rs", 
        "crates/charts/src/funnel.rs",
        "crates/charts/src/candlestick.rs"
    ]
    
    for file_path in files:
        if os.path.exists(file_path):
            print(f"修复 {file_path}...")
            if quick_fix_file(file_path):
                print(f"  ✅ 已修复")
            else:
                print(f"  ⏭️  跳过")
        else:
            print(f"  ❌ 文件不存在")

if __name__ == "__main__":
    main()
