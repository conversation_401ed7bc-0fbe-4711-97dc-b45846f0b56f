//! 3D chart implementations for ECharts-rs

use echarts_core::*;
use echarts_charts::*;
use echarts_renderer::{RenderContext, Renderer};
use serde::{Deserialize, Serialize};
use nalgebra::{Matrix4, Point3, Vector3};
use glam::{Vec3, Mat4, <PERSON>ua<PERSON>};

pub mod surface;
pub mod scatter3d;
pub mod bar3d;
pub mod line3d;

/// 3D coordinate system
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Coordinate3D {
    /// X-axis configuration
    pub x_axis: Axis3D,
    /// Y-axis configuration
    pub y_axis: Axis3D,
    /// Z-axis configuration
    pub z_axis: Axis3D,
    /// Grid configuration
    pub grid: Grid3D,
    /// Camera configuration
    pub camera: Camera3D,
    /// Lighting configuration
    pub lighting: Lighting3D,
}

/// 3D axis configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Axis3D {
    /// Axis name
    pub name: String,
    /// Minimum value
    pub min: Option<f64>,
    /// Maximum value
    pub max: Option<f64>,
    /// Axis type
    pub axis_type: AxisType,
    /// Show axis line
    pub show_line: bool,
    /// Show axis labels
    pub show_labels: bool,
    /// Show tick marks
    pub show_ticks: bool,
    /// Axis color
    pub color: Color,
    /// Label style
    pub label_style: TextStyle,
}

/// 3D grid configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Grid3D {
    /// Show grid lines
    pub show_grid: bool,
    /// Grid line color
    pub grid_color: Color,
    /// Grid line width
    pub grid_width: f64,
    /// Show grid planes
    pub show_planes: bool,
    /// Grid plane opacity
    pub plane_opacity: f64,
    /// Bounding box
    pub bounding_box: BoundingBox3D,
}

/// 3D bounding box
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BoundingBox3D {
    /// Minimum point
    pub min: Point3D,
    /// Maximum point
    pub max: Point3D,
}

/// 3D point
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Point3D {
    pub x: f64,
    pub y: f64,
    pub z: f64,
}

/// 3D camera configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Camera3D {
    /// Camera position
    pub position: Point3D,
    /// Look-at target
    pub target: Point3D,
    /// Up vector
    pub up: Vector3D,
    /// Field of view (in degrees)
    pub fov: f64,
    /// Near clipping plane
    pub near: f64,
    /// Far clipping plane
    pub far: f64,
    /// Camera type
    pub camera_type: CameraType,
}

/// 3D vector
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Vector3D {
    pub x: f64,
    pub y: f64,
    pub z: f64,
}

/// Camera types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum CameraType {
    Perspective,
    Orthographic,
}

/// 3D lighting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Lighting3D {
    /// Ambient light
    pub ambient: AmbientLight,
    /// Directional lights
    pub directional: Vec<DirectionalLight>,
    /// Point lights
    pub point: Vec<PointLight>,
    /// Spot lights
    pub spot: Vec<SpotLight>,
}

/// Ambient light
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AmbientLight {
    /// Light color
    pub color: Color,
    /// Light intensity
    pub intensity: f64,
}

/// Directional light
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DirectionalLight {
    /// Light color
    pub color: Color,
    /// Light intensity
    pub intensity: f64,
    /// Light direction
    pub direction: Vector3D,
    /// Cast shadows
    pub cast_shadows: bool,
}

/// Point light
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PointLight {
    /// Light color
    pub color: Color,
    /// Light intensity
    pub intensity: f64,
    /// Light position
    pub position: Point3D,
    /// Attenuation distance
    pub distance: f64,
    /// Cast shadows
    pub cast_shadows: bool,
}

/// Spot light
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpotLight {
    /// Light color
    pub color: Color,
    /// Light intensity
    pub intensity: f64,
    /// Light position
    pub position: Point3D,
    /// Light direction
    pub direction: Vector3D,
    /// Inner cone angle (in degrees)
    pub inner_angle: f64,
    /// Outer cone angle (in degrees)
    pub outer_angle: f64,
    /// Attenuation distance
    pub distance: f64,
    /// Cast shadows
    pub cast_shadows: bool,
}

/// 3D transformation matrix
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Transform3D {
    /// Translation
    pub translation: Vector3D,
    /// Rotation (quaternion)
    pub rotation: Quaternion,
    /// Scale
    pub scale: Vector3D,
}

/// Quaternion for 3D rotations
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Quaternion {
    pub x: f64,
    pub y: f64,
    pub z: f64,
    pub w: f64,
}

/// 3D material properties
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Material3D {
    /// Base color
    pub color: Color,
    /// Metallic factor
    pub metallic: f64,
    /// Roughness factor
    pub roughness: f64,
    /// Emission color
    pub emission: Color,
    /// Normal map intensity
    pub normal_intensity: f64,
    /// Opacity
    pub opacity: f64,
    /// Double-sided rendering
    pub double_sided: bool,
}

impl Default for Coordinate3D {
    fn default() -> Self {
        Self {
            x_axis: Axis3D::default(),
            y_axis: Axis3D::default(),
            z_axis: Axis3D::default(),
            grid: Grid3D::default(),
            camera: Camera3D::default(),
            lighting: Lighting3D::default(),
        }
    }
}

impl Default for Axis3D {
    fn default() -> Self {
        Self {
            name: String::new(),
            min: None,
            max: None,
            axis_type: AxisType::Value,
            show_line: true,
            show_labels: true,
            show_ticks: true,
            color: Color::BLACK,
            label_style: TextStyle::default(),
        }
    }
}

impl Default for Grid3D {
    fn default() -> Self {
        Self {
            show_grid: true,
            grid_color: Color::from_rgba(128, 128, 128, 128),
            grid_width: 1.0,
            show_planes: false,
            plane_opacity: 0.1,
            bounding_box: BoundingBox3D::default(),
        }
    }
}

impl Default for BoundingBox3D {
    fn default() -> Self {
        Self {
            min: Point3D::new(-1.0, -1.0, -1.0),
            max: Point3D::new(1.0, 1.0, 1.0),
        }
    }
}

impl Default for Camera3D {
    fn default() -> Self {
        Self {
            position: Point3D::new(5.0, 5.0, 5.0),
            target: Point3D::new(0.0, 0.0, 0.0),
            up: Vector3D::new(0.0, 1.0, 0.0),
            fov: 45.0,
            near: 0.1,
            far: 1000.0,
            camera_type: CameraType::Perspective,
        }
    }
}

impl Default for Lighting3D {
    fn default() -> Self {
        Self {
            ambient: AmbientLight {
                color: Color::WHITE,
                intensity: 0.3,
            },
            directional: vec![DirectionalLight {
                color: Color::WHITE,
                intensity: 0.8,
                direction: Vector3D::new(-1.0, -1.0, -1.0),
                cast_shadows: true,
            }],
            point: Vec::new(),
            spot: Vec::new(),
        }
    }
}

impl Default for Transform3D {
    fn default() -> Self {
        Self {
            translation: Vector3D::new(0.0, 0.0, 0.0),
            rotation: Quaternion::identity(),
            scale: Vector3D::new(1.0, 1.0, 1.0),
        }
    }
}

impl Default for Material3D {
    fn default() -> Self {
        Self {
            color: Color::WHITE,
            metallic: 0.0,
            roughness: 0.5,
            emission: Color::BLACK,
            normal_intensity: 1.0,
            opacity: 1.0,
            double_sided: false,
        }
    }
}

impl Point3D {
    /// Create a new 3D point
    pub fn new(x: f64, y: f64, z: f64) -> Self {
        Self { x, y, z }
    }

    /// Origin point
    pub fn origin() -> Self {
        Self::new(0.0, 0.0, 0.0)
    }

    /// Convert to nalgebra Point3
    pub fn to_nalgebra(&self) -> Point3<f64> {
        Point3::new(self.x, self.y, self.z)
    }

    /// Convert to glam Vec3
    pub fn to_glam(&self) -> Vec3 {
        Vec3::new(self.x as f32, self.y as f32, self.z as f32)
    }

    /// Distance to another point
    pub fn distance_to(&self, other: &Point3D) -> f64 {
        let dx = self.x - other.x;
        let dy = self.y - other.y;
        let dz = self.z - other.z;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }
}

impl Vector3D {
    /// Create a new 3D vector
    pub fn new(x: f64, y: f64, z: f64) -> Self {
        Self { x, y, z }
    }

    /// Zero vector
    pub fn zero() -> Self {
        Self::new(0.0, 0.0, 0.0)
    }

    /// Unit X vector
    pub fn unit_x() -> Self {
        Self::new(1.0, 0.0, 0.0)
    }

    /// Unit Y vector
    pub fn unit_y() -> Self {
        Self::new(0.0, 1.0, 0.0)
    }

    /// Unit Z vector
    pub fn unit_z() -> Self {
        Self::new(0.0, 0.0, 1.0)
    }

    /// Vector length
    pub fn length(&self) -> f64 {
        (self.x * self.x + self.y * self.y + self.z * self.z).sqrt()
    }

    /// Normalize the vector
    pub fn normalize(&self) -> Self {
        let len = self.length();
        if len > 0.0 {
            Self::new(self.x / len, self.y / len, self.z / len)
        } else {
            *self
        }
    }

    /// Dot product
    pub fn dot(&self, other: &Vector3D) -> f64 {
        self.x * other.x + self.y * other.y + self.z * other.z
    }

    /// Cross product
    pub fn cross(&self, other: &Vector3D) -> Vector3D {
        Vector3D::new(
            self.y * other.z - self.z * other.y,
            self.z * other.x - self.x * other.z,
            self.x * other.y - self.y * other.x,
        )
    }
}

impl Quaternion {
    /// Identity quaternion
    pub fn identity() -> Self {
        Self { x: 0.0, y: 0.0, z: 0.0, w: 1.0 }
    }

    /// Create quaternion from axis and angle
    pub fn from_axis_angle(axis: Vector3D, angle: f64) -> Self {
        let half_angle = angle * 0.5;
        let sin_half = half_angle.sin();
        let cos_half = half_angle.cos();
        let normalized_axis = axis.normalize();

        Self {
            x: normalized_axis.x * sin_half,
            y: normalized_axis.y * sin_half,
            z: normalized_axis.z * sin_half,
            w: cos_half,
        }
    }

    /// Convert to rotation matrix
    pub fn to_matrix(&self) -> Matrix4<f64> {
        let x2 = self.x * self.x;
        let y2 = self.y * self.y;
        let z2 = self.z * self.z;
        let xy = self.x * self.y;
        let xz = self.x * self.z;
        let yz = self.y * self.z;
        let wx = self.w * self.x;
        let wy = self.w * self.y;
        let wz = self.w * self.z;

        Matrix4::new(
            1.0 - 2.0 * (y2 + z2), 2.0 * (xy - wz), 2.0 * (xz + wy), 0.0,
            2.0 * (xy + wz), 1.0 - 2.0 * (x2 + z2), 2.0 * (yz - wx), 0.0,
            2.0 * (xz - wy), 2.0 * (yz + wx), 1.0 - 2.0 * (x2 + y2), 0.0,
            0.0, 0.0, 0.0, 1.0,
        )
    }
}

impl Transform3D {
    /// Create a new transform
    pub fn new() -> Self {
        Self::default()
    }

    /// Set translation
    pub fn with_translation(mut self, translation: Vector3D) -> Self {
        self.translation = translation;
        self
    }

    /// Set rotation
    pub fn with_rotation(mut self, rotation: Quaternion) -> Self {
        self.rotation = rotation;
        self
    }

    /// Set scale
    pub fn with_scale(mut self, scale: Vector3D) -> Self {
        self.scale = scale;
        self
    }

    /// Convert to transformation matrix
    pub fn to_matrix(&self) -> Matrix4<f64> {
        let translation_matrix = Matrix4::new_translation(&nalgebra::Vector3::new(
            self.translation.x,
            self.translation.y,
            self.translation.z,
        ));

        let rotation_matrix = self.rotation.to_matrix();

        let scale_matrix = Matrix4::new_nonuniform_scaling(&nalgebra::Vector3::new(
            self.scale.x,
            self.scale.y,
            self.scale.z,
        ));

        translation_matrix * rotation_matrix * scale_matrix
    }
}

/// 3D rendering utilities
pub struct Renderer3D;

impl Renderer3D {
    /// Project 3D point to 2D screen coordinates
    pub fn project_point(
        point: Point3D,
        view_matrix: &Matrix4<f64>,
        projection_matrix: &Matrix4<f64>,
        viewport: (f64, f64, f64, f64), // x, y, width, height
    ) -> Point {
        let world_point = nalgebra::Vector4::new(point.x, point.y, point.z, 1.0);
        let view_point = view_matrix * world_point;
        let clip_point = projection_matrix * view_point;

        // Perspective divide
        let ndc_x = clip_point.x / clip_point.w;
        let ndc_y = clip_point.y / clip_point.w;

        // Convert to screen coordinates
        let screen_x = viewport.0 + (ndc_x + 1.0) * 0.5 * viewport.2;
        let screen_y = viewport.1 + (1.0 - ndc_y) * 0.5 * viewport.3;

        Point::new(screen_x, screen_y)
    }

    /// Create view matrix from camera
    pub fn create_view_matrix(camera: &Camera3D) -> Matrix4<f64> {
        let eye = camera.position.to_nalgebra();
        let target = camera.target.to_nalgebra();
        let up = nalgebra::Vector3::new(camera.up.x, camera.up.y, camera.up.z);

        Matrix4::look_at_rh(&eye, &target, &up)
    }

    /// Create projection matrix from camera
    pub fn create_projection_matrix(camera: &Camera3D, aspect_ratio: f64) -> Matrix4<f64> {
        match camera.camera_type {
            CameraType::Perspective => {
                Matrix4::new_perspective(aspect_ratio, camera.fov.to_radians(), camera.near, camera.far)
            }
            CameraType::Orthographic => {
                let height = 10.0; // Fixed orthographic height
                let width = height * aspect_ratio;
                Matrix4::new_orthographic(-width, width, -height, height, camera.near, camera.far)
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use approx::assert_relative_eq;

    #[test]
    fn test_point3d_creation() {
        let point = Point3D::new(1.0, 2.0, 3.0);
        assert_eq!(point.x, 1.0);
        assert_eq!(point.y, 2.0);
        assert_eq!(point.z, 3.0);
    }

    #[test]
    fn test_vector3d_operations() {
        let v1 = Vector3D::new(1.0, 0.0, 0.0);
        let v2 = Vector3D::new(0.0, 1.0, 0.0);

        assert_relative_eq!(v1.length(), 1.0);
        assert_relative_eq!(v1.dot(&v2), 0.0);

        let cross = v1.cross(&v2);
        assert_relative_eq!(cross.x, 0.0);
        assert_relative_eq!(cross.y, 0.0);
        assert_relative_eq!(cross.z, 1.0);
    }

    #[test]
    fn test_quaternion_identity() {
        let q = Quaternion::identity();
        assert_eq!(q.x, 0.0);
        assert_eq!(q.y, 0.0);
        assert_eq!(q.z, 0.0);
        assert_eq!(q.w, 1.0);
    }

    #[test]
    fn test_transform_matrix() {
        let transform = Transform3D::new()
            .with_translation(Vector3D::new(1.0, 2.0, 3.0));

        let matrix = transform.to_matrix();
        assert_relative_eq!(matrix[(0, 3)], 1.0);
        assert_relative_eq!(matrix[(1, 3)], 2.0);
        assert_relative_eq!(matrix[(2, 3)], 3.0);
    }

    #[test]
    fn test_camera_matrices() {
        let camera = Camera3D::default();
        let view_matrix = Renderer3D::create_view_matrix(&camera);
        let projection_matrix = Renderer3D::create_projection_matrix(&camera, 16.0 / 9.0);

        // Basic sanity checks
        assert_eq!(view_matrix.nrows(), 4);
        assert_eq!(view_matrix.ncols(), 4);
        assert_eq!(projection_matrix.nrows(), 4);
        assert_eq!(projection_matrix.ncols(), 4);
    }
}
