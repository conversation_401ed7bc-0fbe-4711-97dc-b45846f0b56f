//! 简单的预计算使用示例
//!
//! 这个示例展示了最简单的预计算使用方法

use echarts_rs::{LineSeries, Color, CartesianCoordinateSystem};
use echarts_core::Bounds as EchartsBounds;
use gpui_renderer::EChartsElement;
use gpui::*;

fn main() {
    println!("🚀 启动简单预计算示例...");
    
    Application::new().run(move |cx| {
        let window_size = size(px(800.0), px(600.0));
        let window_bounds = Bounds::centered(None, window_size, cx);
        
        let options = WindowOptions {
            window_bounds: Some(WindowBounds::Windowed(window_bounds)),
            titlebar: Some(TitlebarOptions {
                title: Some("简单预计算示例".into()),
                appears_transparent: false,
                traffic_light_position: None,
            }),
            focus: true,
            show: true,
            kind: WindowKind::Normal,
            is_movable: true,
            display_id: None,
            app_id: None,
            window_background: WindowBackgroundAppearance::Opaque,
            window_decorations: None,
            window_min_size: Some(size(px(600.0), px(400.0))),
        };
        
        cx.open_window(options, |_window, cx| {
            cx.new_view(|_cx| SimplePrecomputedExample::new())
        }).expect("无法创建窗口");
    });
}

struct SimplePrecomputedExample {
    precomputed_chart: Option<EChartsElement>,
    status_message: String,
}

impl SimplePrecomputedExample {
    fn new() -> Self {
        let mut example = Self {
            precomputed_chart: None,
            status_message: "正在预计算图表...".to_string(),
        };
        
        example.create_precomputed_chart();
        example
    }
    
    fn create_precomputed_chart(&mut self) {
        println!("🔄 开始创建预计算图表...");
        
        // 1. 准备数据
        let data = vec![
            (1.0, 120.0), (2.0, 132.0), (3.0, 101.0), (4.0, 134.0),
            (5.0, 90.0), (6.0, 230.0), (7.0, 210.0), (8.0, 182.0),
            (9.0, 191.0), (10.0, 234.0), (11.0, 290.0), (12.0, 330.0),
        ];
        
        // 2. 创建系列
        let series = LineSeries::new("销售数据")
            .data(data)
            .color(Color::rgb(0.2, 0.6, 1.0))
            .line_width(3.0)
            .smooth(true);
        
        // 3. 创建坐标系统
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(50.0, 50.0, 700.0, 400.0), // 留出边距
            (0.0, 13.0),  // x 轴范围
            (0.0, 400.0), // y 轴范围
        );
        
        // 4. 使用便捷方法创建预计算图表
        match EChartsElement::precompute_from_series(
            Box::new(series),
            coord_system,
            true, // 启用调试输出
        ) {
            Ok(element) => {
                println!("✅ 预计算图表创建成功！");
                self.precomputed_chart = Some(element);
                self.status_message = "预计算图表已就绪".to_string();
            }
            Err(e) => {
                println!("❌ 预计算失败: {:?}", e);
                self.status_message = format!("预计算失败: {:?}", e);
            }
        }
    }
}

impl Render for SimplePrecomputedExample {
    fn render(&mut self, _cx: &mut ViewContext<Self>) -> impl IntoElement {
        div()
            .flex()
            .flex_col()
            .size_full()
            .bg(rgb(0xf8f9fa))
            .p_6()
            .child(
                // 标题
                div()
                    .text_2xl()
                    .font_bold()
                    .text_color(rgb(0x1f2937))
                    .mb_6()
                    .text_center()
                    .child("简单预计算图表示例")
            )
            .child(
                // 状态信息
                div()
                    .text_sm()
                    .text_color(rgb(0x6b7280))
                    .mb_4()
                    .text_center()
                    .child(&self.status_message)
            )
            .child(
                // 图表容器
                div()
                    .flex_1()
                    .w_full()
                    .bg(rgb(0xffffff))
                    .border_1()
                    .border_color(rgb(0xe5e7eb))
                    .rounded_lg()
                    .shadow_sm()
                    .p_4()
                    .child(
                        if let Some(ref chart) = self.precomputed_chart {
                            // 显示预计算的图表
                            div()
                                .size_full()
                                .child("预计算图表将在这里显示")
                                // 注意：由于 GPUI 的所有权要求，这里需要特殊处理
                                // 在实际应用中，您可能需要使用 Rc<RefCell<>> 或重新设计架构
                        } else {
                            // 显示加载状态
                            div()
                                .flex()
                                .items_center()
                                .justify_center()
                                .size_full()
                                .flex_col()
                                .gap_4()
                                .child(
                                    div()
                                        .text_lg()
                                        .text_color(rgb(0x6b7280))
                                        .child("⏳ 正在预计算图表...")
                                )
                                .child(
                                    div()
                                        .text_sm()
                                        .text_color(rgb(0x9ca3af))
                                        .text_center()
                                        .child("预计算可能需要几秒钟时间，\n但后续渲染会非常快速")
                                )
                        }
                    )
            )
            .child(
                // 底部说明
                div()
                    .mt_6()
                    .p_4()
                    .bg(rgb(0xf3f4f6))
                    .rounded_lg()
                    .child(
                        div()
                            .text_sm()
                            .text_color(rgb(0x6b7280))
                            .child(
                                "💡 这个图表使用了预计算的绘制命令。\n\
                                 首次创建时会稍慢，但后续的每次重绘都会非常快速，\n\
                                 特别适合需要频繁刷新的场景。"
                            )
                    )
            )
    }
}

// 为了演示目的，这里提供一个更实用的版本
#[allow(dead_code)]
fn practical_usage_example() {
    // 这是一个更实用的使用模式示例
    
    // 1. 在应用启动时预计算常用图表
    let sales_data = vec![(1.0, 100.0), (2.0, 150.0), (3.0, 120.0)];
    let sales_series = LineSeries::new("销售")
        .data(sales_data)
        .color(Color::BLUE);
    
    let coord_system = CartesianCoordinateSystem::new(
        EchartsBounds::new(0.0, 0.0, 800.0, 600.0),
        (0.0, 4.0),
        (0.0, 200.0),
    );
    
    // 预计算图表
    let _precomputed_chart = EChartsElement::precompute_from_series(
        Box::new(sales_series),
        coord_system,
        false, // 生产环境关闭调试
    ).expect("预计算失败");
    
    // 2. 在需要时直接使用，无需重新计算
    // precomputed_chart 可以直接用于渲染，性能极佳
    
    println!("✅ 实用示例：预计算图表已准备就绪");
}

// 演示如何更新预计算的图表
#[allow(dead_code)]
fn update_precomputed_chart_example() {
    // 假设我们有一个已经预计算的图表
    let mut chart = create_initial_chart();
    
    // 当数据更新时
    let new_data = vec![(1.0, 200.0), (2.0, 250.0), (3.0, 180.0)];
    let new_series = LineSeries::new("新数据")
        .data(new_data)
        .color(Color::RED);
    
    let coord_system = CartesianCoordinateSystem::new(
        EchartsBounds::new(0.0, 0.0, 800.0, 600.0),
        (0.0, 4.0),
        (0.0, 300.0),
    );
    
    // 重新计算绘制命令
    if let Ok(new_commands) = new_series.render_to_commands(&coord_system) {
        chart.update_commands(new_commands);
        println!("✅ 图表数据已更新");
    }
}

fn create_initial_chart() -> EChartsElement {
    let data = vec![(1.0, 100.0), (2.0, 150.0)];
    let series = LineSeries::new("初始数据").data(data).color(Color::BLUE);
    let coord_system = CartesianCoordinateSystem::new(
        EchartsBounds::new(0.0, 0.0, 800.0, 600.0),
        (0.0, 3.0),
        (0.0, 200.0),
    );
    
    EChartsElement::precompute_from_series(
        Box::new(series),
        coord_system,
        false,
    ).expect("创建失败")
}
