/// GPU硬件加速演示程序
/// 展示ECharts-rs的GPU优化功能，包括批量渲染、实例化渲染等
use echarts_core::*;
use echarts_renderer::*;

fn main() -> Result<()> {
    println!("🚀 ECharts-rs GPU硬件加速演示");
    println!("================================");

    // 创建GPU优化的渲染器
    let mut renderer = create_optimized_renderer();

    // 演示1: 大量散点图的实例化渲染
    demo_scatter_plot_optimization(&mut renderer)?;

    // 演示2: 批量线条渲染优化
    demo_line_batch_optimization(&mut renderer)?;

    // 演示3: 性能监控和自动优化
    demo_performance_monitoring(&mut renderer)?;

    println!("\n✅ GPU硬件加速演示完成！");
    Ok(())
}

/// 创建优化的渲染器
fn create_optimized_renderer() -> GpuiRenderer {
    let mut renderer = GpuiRenderer::new();

    // 配置GPU性能优化
    let performance_config = GpuPerformanceConfig {
        instancing_threshold: 50, // 50个以上圆形使用实例化渲染
        enable_texture_cache: true,
        enable_lod: true,
        max_batch_size: 2000,
    };

    renderer.configure_performance(performance_config);
    println!("🔧 GPU性能优化配置完成");

    renderer
}

/// 演示散点图的实例化渲染优化
fn demo_scatter_plot_optimization(renderer: &mut GpuiRenderer) -> Result<()> {
    println!("\n📊 演示1: 散点图实例化渲染优化");
    println!("生成大量散点数据...");

    // 生成大量散点数据（模拟大数据集）
    let mut circles = Vec::new();
    for i in 0..500 {
        let x = (i as f64 * 0.1).sin() * 200.0 + 400.0;
        let y = (i as f64 * 0.1).cos() * 200.0 + 300.0;
        let radius = 3.0 + (i as f64 * 0.05).sin().abs() * 2.0;

        circles.push((
            Point::new(x, y),
            radius,
            echarts_core::draw_commands::CircleStyle {
                fill: Some(Color::rgb(0.2, 0.6, 0.9)),
                stroke: None,
                opacity: 0.8,
            },
        ));
    }

    println!("📈 渲染 {} 个散点（将触发实例化渲染优化）", circles.len());

    // 重置统计信息
    renderer.reset_render_stats();

    // 批量渲染圆形
    renderer.render_circles_batch(&circles)?;

    // 显示性能统计
    let stats = renderer.get_render_stats();
    println!("📊 渲染统计:");
    println!("   - 总绘制调用: {}", stats.draw_calls);
    println!("   - 实例化渲染: {}", stats.instanced_draws);
    println!("   - 节省调用: {}", stats.saved_calls);
    println!("   - 总图元数: {}", stats.total_primitives);

    if stats.instanced_draws > 0 {
        println!("✅ 实例化渲染优化生效！");
    }

    Ok(())
}

/// 演示线条批量渲染优化
fn demo_line_batch_optimization(renderer: &mut GpuiRenderer) -> Result<()> {
    println!("\n📈 演示2: 线条批量渲染优化");

    // 生成多条线段（模拟复杂图表）
    let mut lines = Vec::new();

    // 生成网格线
    for i in 0..20 {
        let x = 50.0 + i as f64 * 30.0;
        lines.push((
            Point::new(x, 50.0),
            Point::new(x, 550.0),
            echarts_core::draw_commands::LineStyle {
                color: Color::rgb(0.8, 0.8, 0.8),
                width: 1.0,
                dash: None,
                cap: LineCap::Round,
                join: LineJoin::Round,
                miter_limit: 10.0,
            },
        ));
    }

    for i in 0..15 {
        let y = 50.0 + i as f64 * 35.0;
        lines.push((
            Point::new(50.0, y),
            Point::new(650.0, y),
            echarts_core::draw_commands::LineStyle {
                color: Color::rgb(0.8, 0.8, 0.8),
                width: 1.0,
                dash: None,
                cap: LineCap::Round,
                join: LineJoin::Round,
                miter_limit: 10.0,
            },
        ));
    }

    // 生成数据线
    for series in 0..3 {
        let color = match series {
            0 => Color::rgb(0.9, 0.2, 0.2),
            1 => Color::rgb(0.2, 0.9, 0.2),
            _ => Color::rgb(0.2, 0.2, 0.9),
        };

        for i in 0..50 {
            let x1 = 50.0 + i as f64 * 12.0;
            let x2 = 50.0 + (i + 1) as f64 * 12.0;
            let y1 = 300.0 + (i as f64 * 0.2 + series as f64).sin() * 100.0;
            let y2 = 300.0 + ((i + 1) as f64 * 0.2 + series as f64).sin() * 100.0;

            lines.push((
                Point::new(x1, y1),
                Point::new(x2, y2),
                echarts_core::draw_commands::LineStyle {
                    color,
                    width: 2.0,
                    dash: None,
                    cap: LineCap::Round,
                    join: LineJoin::Round,
                    miter_limit: 10.0,
                },
            ));
        }
    }

    println!("📈 渲染 {} 条线段（将触发批量渲染优化）", lines.len());

    // 重置统计信息
    renderer.reset_render_stats();

    // 批量渲染线条
    renderer.render_lines_batch(&lines)?;

    // 显示性能统计
    let stats = renderer.get_render_stats();
    println!("📊 渲染统计:");
    println!("   - 总绘制调用: {}", stats.draw_calls);
    println!("   - 节省调用: {}", stats.saved_calls);

    if stats.saved_calls > 0 {
        let efficiency =
            (stats.saved_calls as f64 / (stats.draw_calls + stats.saved_calls) as f64) * 100.0;
        println!("✅ 批量渲染优化生效！效率提升: {:.1}%", efficiency);
    }

    Ok(())
}

/// 演示性能监控和自动优化
fn demo_performance_monitoring(renderer: &mut GpuiRenderer) -> Result<()> {
    println!("\n🔍 演示3: 性能监控和自动优化");

    // 获取性能建议
    let recommendations = renderer.get_performance_recommendations();
    println!("💡 性能优化建议:");
    for (i, rec) in recommendations.iter().enumerate() {
        println!("   {}. {}", i + 1, rec);
    }

    if recommendations.is_empty() {
        println!("   ✅ 当前性能配置良好，无需调整");
    }

    // 自动优化性能配置
    println!("\n🚀 执行自动性能优化...");
    renderer.auto_optimize_performance();

    // 显示最终统计
    let final_stats = renderer.get_render_stats();
    println!("\n📊 最终渲染统计:");
    println!("   - 总绘制调用: {}", final_stats.draw_calls);
    println!("   - 实例化渲染: {}", final_stats.instanced_draws);
    println!("   - 节省调用: {}", final_stats.saved_calls);
    println!("   - 总图元数: {}", final_stats.total_primitives);

    if final_stats.saved_calls > 0 {
        let total_calls = final_stats.draw_calls + final_stats.saved_calls;
        let efficiency = (final_stats.saved_calls as f64 / total_calls as f64) * 100.0;
        println!("🎯 GPU优化总效率: {:.1}%", efficiency);
        println!("💾 内存节省: 减少了 {} 个绘制对象", final_stats.saved_calls);
    }

    Ok(())
}
