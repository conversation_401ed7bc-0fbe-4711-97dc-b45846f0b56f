//! 增强版折线图实现
//!
//! 基于新的 Core 架构的完整折线图实现，包含：
//! - 多种线条样式（直线、平滑曲线、阶梯线）
//! - 面积图支持
//! - 数据点符号和标签
//! - 交互功能支持
//! - 动画效果
//! - 数据优化算法
//! - 实时数据更新

use echarts_core::{
    Bounds, Color, CoordinateSystem, DataSet, DataValue, DrawCommand, Point, Result, Series, SeriesType,
    draw_commands::{CircleStyle},
    LineStyle, LineCap, LineJoin, TextStyle,
    optimization_adapter::ChartOptimization,
    optimization::{OptimizationConfig, OptimizationAlgorithm, DataOptimizer},
};
use echarts_interaction::{ChartInteraction, ChartInteractionEvent, ChartInteractionResult};
use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};
use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant};

/// 符号类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SymbolType {
    None,
    Circle,
    Square,
    Triangle,
    Diamond,
}

/// 阶梯线类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum StepType {
    Start,
    Middle,
    End,
}

/// 折线图优化算法 (映射到核心优化算法)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum LineOptimizationAlgorithm {
    LTTB,  // Largest Triangle Three Buckets
    Douglas, // Douglas-Peucker
}

/// 动画类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AnimationType {
    None,
    FadeIn,
    SlideIn,
    GrowIn,
    DrawLine,
}

/// 交互事件类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum InteractionEvent {
    PointHover { series_index: usize, point_index: usize, position: Point },
    PointClick { series_index: usize, point_index: usize, position: Point },
    PointLeave,
    SeriesHover { series_index: usize },
    SeriesLeave,
}

/// 动画配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnimationConfig {
    pub enabled: bool,
    pub animation_type: AnimationType,
    pub duration: Duration,
    pub delay: Duration,
    pub easing: String, // CSS easing function
}

impl Default for AnimationConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            animation_type: AnimationType::FadeIn,
            duration: Duration::from_millis(1000),
            delay: Duration::from_millis(0),
            easing: "ease-out".to_string(),
        }
    }
}

/// 交互配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InteractionConfig {
    pub hover_enabled: bool,
    pub click_enabled: bool,
    pub hover_highlight_color: Option<Color>,
    pub hover_point_size: f64,
    pub tooltip_enabled: bool,
    pub cursor_style: String,
}

impl Default for InteractionConfig {
    fn default() -> Self {
        Self {
            hover_enabled: true,
            click_enabled: true,
            hover_highlight_color: Some(Color::rgba(1.0, 1.0, 0.0, 0.8)), // 黄色高亮
            hover_point_size: 8.0,
            tooltip_enabled: true,
            cursor_style: "pointer".to_string(),
        }
    }
}

/// 轴标签格式化类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum LabelFormatType {
    /// 自动格式化
    Auto,
    /// 固定小数位数
    FixedDecimal(usize),
    /// 科学计数法
    Scientific(usize),
    /// 百分比格式
    Percentage(usize),
    /// 自定义格式化函数
    Custom(String),
}

/// 轴标签配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AxisLabelConfig {
    /// X轴标签格式化
    pub x_axis_format: LabelFormatType,
    /// Y轴标签格式化
    pub y_axis_format: LabelFormatType,
    /// 是否显示X轴标签
    pub show_x_labels: bool,
    /// 是否显示Y轴标签
    pub show_y_labels: bool,
    /// X轴标签旋转角度
    pub x_label_rotation: f64,
    /// Y轴标签旋转角度
    pub y_label_rotation: f64,
    /// 标签字体大小
    pub font_size: f64,
    /// 标签颜色
    pub color: Color,
    /// 标签间距
    pub margin: f64,
}

impl Default for AxisLabelConfig {
    fn default() -> Self {
        Self {
            x_axis_format: LabelFormatType::Auto,
            y_axis_format: LabelFormatType::FixedDecimal(1), // 默认Y轴显示1位小数
            show_x_labels: true,
            show_y_labels: true,
            x_label_rotation: 0.0,
            y_label_rotation: 0.0,
            font_size: 12.0,
            color: Color::rgb(0.4, 0.4, 0.4),
            margin: 8.0,
        }
    }
}

/// 完整功能的折线图系列
///
/// 重构版本：保留所有原有功能，基于新的 Core 架构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LineSeries {
    /// 基础配置
    pub config: ChartConfig,

    /// 图表数据
    pub data: DataSet,

    /// 线条颜色
    pub color: Color,

    /// 线条宽度
    pub line_width: f64,

    /// 是否使用平滑曲线
    pub smooth: bool,

    /// 平滑度因子 (0.0 到 1.0)
    pub smoothness: f64,

    /// 是否显示面积
    pub area: bool,

    /// 面积填充颜色
    pub area_color: Option<Color>,

    /// 面积透明度 (0.0 到 1.0)
    pub area_opacity: f64,

    /// 是否显示数据点符号
    pub show_symbols: bool,

    /// 符号大小
    pub symbol_size: f64,

    /// 符号类型
    pub symbol_type: SymbolType,

    /// 是否显示数值标签
    pub show_values: bool,

    /// 是否连接空值
    pub connect_nulls: bool,

    /// 阶梯线类型（None 表示普通线条）
    pub step: Option<StepType>,

    /// 是否启用数据优化
    pub optimization_enabled: bool,

    /// 优化目标点数
    pub optimization_target_points: usize,

    /// 优化算法
    pub optimization_algorithm: LineOptimizationAlgorithm,

    // visible, z_index, animation, interaction 现在在 config 中

    /// 轴标签配置
    pub axis_labels: AxisLabelConfig,

    /// 虚线样式
    pub dash_pattern: Option<Vec<f64>>,

    /// 线条端点样式
    pub line_cap: LineCap,

    /// 线条连接样式
    pub line_join: LineJoin,

    /// 数据标签格式化函数名
    pub label_formatter: Option<String>,

    /// 是否启用数据缓存
    pub cache_enabled: bool,

    /// 最后更新时间（用于动画和缓存）
    #[serde(skip)]
    pub last_update: Option<Instant>,

    /// 数据变化历史（用于动画）
    pub data_history: Vec<DataSet>,

    /// 最大历史记录数
    pub max_history: usize,
}

impl LineSeries {
    /// 创建新的折线图系列
    pub fn new<S: Into<String>>(name: S) -> Self {
        Self {
            config: ChartConfig {
                name: name.into(),
                ..ChartConfig::default()
            },
            data: DataSet::new(),
            color: Color::rgb(0.2, 0.6, 1.0), // 默认蓝色
            line_width: 2.0,
            smooth: false,
            smoothness: 0.3,
            area: false,
            area_color: None,
            area_opacity: 0.3,
            show_symbols: true,
            symbol_size: 6.0,
            symbol_type: SymbolType::Circle,
            show_values: false,
            connect_nulls: false,
            step: None,
            optimization_enabled: true,
            optimization_target_points: 2000,
            optimization_algorithm: LineOptimizationAlgorithm::LTTB,
            axis_labels: AxisLabelConfig::default(),
            dash_pattern: None,
            line_cap: LineCap::Round,
            line_join: LineJoin::Round,
            label_formatter: None,
            cache_enabled: true,
            last_update: None,
            data_history: Vec::new(),
            max_history: 10,
        }
    }

    /// 设置数据（从 (x, y) 点对）
    pub fn data<I>(mut self, data: I) -> Self
    where
        I: IntoIterator<Item = (f64, f64)>,
    {
        self.data = DataSet::from_xy_pairs(data);
        self
    }

    /// 设置颜色
    pub fn color(mut self, color: Color) -> Self {
        self.color = color;
        self
    }

    /// 设置线条宽度
    pub fn line_width(mut self, width: f64) -> Self {
        self.line_width = width;
        self
    }

    /// 设置是否显示符号
    pub fn show_symbols(mut self, show: bool) -> Self {
        self.show_symbols = show;
        self
    }

    /// 设置符号大小
    pub fn symbol_size(mut self, size: f64) -> Self {
        self.symbol_size = size;
        self
    }

    /// 设置可见性
    pub fn visible(mut self, visible: bool) -> Self {
        self.config.visible = visible;
        self
    }

    /// 设置 Z-index
    pub fn z_index(mut self, z_index: i32) -> Self {
        self.config.z_index = z_index;
        self
    }

    /// 设置平滑曲线
    pub fn smooth(mut self, smooth: bool) -> Self {
        self.smooth = smooth;
        self
    }

    /// 设置平滑度
    pub fn smoothness(mut self, smoothness: f64) -> Self {
        self.smoothness = smoothness.clamp(0.0, 1.0);
        self
    }

    /// 设置面积图
    pub fn area(mut self, area: bool) -> Self {
        self.area = area;
        self
    }

    /// 设置面积颜色
    pub fn area_color(mut self, color: Color) -> Self {
        self.area_color = Some(color);
        self
    }

    /// 设置面积透明度
    pub fn area_opacity(mut self, opacity: f64) -> Self {
        self.area_opacity = opacity.clamp(0.0, 1.0);
        self
    }

    /// 设置符号类型
    pub fn symbol_type(mut self, symbol_type: SymbolType) -> Self {
        self.symbol_type = symbol_type;
        self
    }

    /// 设置是否显示数值
    pub fn show_values(mut self, show: bool) -> Self {
        self.show_values = show;
        self
    }

    /// 设置是否连接空值
    pub fn connect_nulls(mut self, connect: bool) -> Self {
        self.connect_nulls = connect;
        self
    }

    /// 设置阶梯线类型
    pub fn step(mut self, step: StepType) -> Self {
        self.step = Some(step);
        self
    }

    /// 设置数据优化
    pub fn optimize(mut self, enabled: bool) -> Self {
        self.optimization_enabled = enabled;
        self
    }

    /// 设置优化目标点数
    pub fn optimization_target_points(mut self, points: usize) -> Self {
        self.optimization_target_points = points;
        self
    }

    /// 设置优化算法
    pub fn optimization_algorithm(mut self, algorithm: LineOptimizationAlgorithm) -> Self {
        self.optimization_algorithm = algorithm;
        self
    }

    /// 设置X轴标签格式
    pub fn x_axis_format(mut self, format: LabelFormatType) -> Self {
        self.axis_labels.x_axis_format = format;
        self
    }

    /// 设置Y轴标签格式
    pub fn y_axis_format(mut self, format: LabelFormatType) -> Self {
        self.axis_labels.y_axis_format = format;
        self
    }

    /// 设置X轴小数位数
    pub fn x_axis_decimal_places(mut self, places: usize) -> Self {
        self.axis_labels.x_axis_format = LabelFormatType::FixedDecimal(places);
        self
    }

    /// 设置Y轴小数位数
    pub fn y_axis_decimal_places(mut self, places: usize) -> Self {
        self.axis_labels.y_axis_format = LabelFormatType::FixedDecimal(places);
        self
    }

    /// 设置轴标签配置
    pub fn axis_labels(mut self, config: AxisLabelConfig) -> Self {
        self.axis_labels = config;
        self
    }

    /// 设置是否显示轴标签
    pub fn show_axis_labels(mut self, show_x: bool, show_y: bool) -> Self {
        self.axis_labels.show_x_labels = show_x;
        self.axis_labels.show_y_labels = show_y;
        self
    }

    /// 设置轴标签旋转角度
    pub fn axis_label_rotation(mut self, x_rotation: f64, y_rotation: f64) -> Self {
        self.axis_labels.x_label_rotation = x_rotation;
        self.axis_labels.y_label_rotation = y_rotation;
        self
    }

    /// 创建优化的折线图（用于大数据集）
    pub fn new_optimized<S: Into<String>>(name: S, target_points: usize) -> Self {
        Self::new(name)
            .optimize(true)
            .optimization_target_points(target_points)
            .optimization_algorithm(LineOptimizationAlgorithm::LTTB)
    }

    /// 创建平滑折线图
    pub fn new_smooth<S: Into<String>>(name: S) -> Self {
        Self::new(name).smooth(true)
    }

    /// 创建面积图
    pub fn new_area<S: Into<String>>(name: S, color: Color) -> Self {
        Self::new(name)
            .color(color)
            .area(true)
            .area_color(Color::rgba(color.r, color.g, color.b, 0.3))
    }

    /// 创建阶梯线图
    pub fn new_step<S: Into<String>>(name: S, step_type: StepType) -> Self {
        Self::new(name).step(step_type)
    }

    /// 创建带自定义轴标签格式的折线图
    pub fn new_with_axis_format<S: Into<String>>(
        name: S,
        x_decimal_places: usize,
        y_decimal_places: usize
    ) -> Self {
        Self::new(name)
            .x_axis_decimal_places(x_decimal_places)
            .y_axis_decimal_places(y_decimal_places)
    }

    /// 创建科学计数法格式的折线图
    pub fn new_scientific<S: Into<String>>(name: S, decimal_places: usize) -> Self {
        Self::new(name)
            .x_axis_format(LabelFormatType::Scientific(decimal_places))
            .y_axis_format(LabelFormatType::Scientific(decimal_places))
    }

    /// 创建百分比格式的折线图
    pub fn new_percentage<S: Into<String>>(name: S, decimal_places: usize) -> Self {
        Self::new(name)
            .y_axis_format(LabelFormatType::Percentage(decimal_places))
    }
}

impl Series for LineSeries {
    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Line
    }

    fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        if !self.config.visible || self.data.is_empty() {
            return Ok(commands);
        }

        // 数据优化处理
        let optimized_data = if self.optimization_enabled && self.data.len() > self.optimization_target_points {
            match self.optimization_algorithm {
                LineOptimizationAlgorithm::LTTB => self.optimize_data_lttb(self.optimization_target_points),
                LineOptimizationAlgorithm::Douglas => self.optimize_data_douglas_peucker(2.0), // 默认epsilon=2.0
            }
        } else {
            self.data.clone()
        };

        // 收集所有有效的数据点
        let mut points = Vec::new();
        for i in 0..optimized_data.len() {
            if let Some(point) = optimized_data.get_point(i) {
                if let (Some(x), Some(y)) = (point.get_number(0), point.get_number(1)) {
                    let data_values = vec![DataValue::Number(x), DataValue::Number(y)];
                    if let Ok(screen_point) = coord_system.data_to_point(&data_values) {
                        points.push(screen_point);
                    }
                }
            }
        }

        if points.is_empty() {
            return Ok(commands);
        }

        // 绘制面积（如果启用）
        if self.area && points.len() > 1 {
            self.render_area(&mut commands, &points, coord_system)?;
        }

        // 绘制线条
        if points.len() > 1 {
            self.render_line(&mut commands, &points)?;
        }

        // 绘制符号（如果启用）
        if self.show_symbols && self.symbol_type != SymbolType::None {
            self.render_symbols(&mut commands, &points)?;
        }

        // 绘制数值标签（如果启用）
        if self.show_values {
            self.render_value_labels(&mut commands, &points)?;
        }

        // 绘制轴标签
        self.render_axis_labels(&mut commands, coord_system)?;

        Ok(commands)
    }

    fn bounds(&self) -> Option<Bounds> {
        if self.data.is_empty() {
            return None;
        }

        let mut min_x = f64::INFINITY;
        let mut max_x = f64::NEG_INFINITY;
        let mut min_y = f64::INFINITY;
        let mut max_y = f64::NEG_INFINITY;

        for i in 0..self.data.len() {
            if let Some(point) = self.data.get_point(i) {
                if let (Some(x), Some(y)) = (point.get_number(0), point.get_number(1)) {
                    min_x = min_x.min(x);
                    max_x = max_x.max(x);
                    min_y = min_y.min(y);
                    max_y = max_y.max(y);
                }
            }
        }

        if min_x.is_finite() && max_x.is_finite() && min_y.is_finite() && max_y.is_finite() {
            Some(Bounds::new(min_x, min_y, max_x - min_x, max_y - min_y))
        } else {
            None
        }
    }

    fn is_visible(&self) -> bool {
        self.config.visible
    }

    fn z_index(&self) -> i32 {
        self.config.z_index
    }

    fn clone_series(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }
}

impl LineSeries {
    /// 处理鼠标移动事件
    pub fn handle_mouse_move(&mut self, position: Point, coord_system: &dyn CoordinateSystem) -> Option<InteractionEvent> {
        if !self.config.interaction.enabled {
            return None;
        }

        // 查找最近的数据点
        if let Some((series_idx, point_idx)) = self.find_nearest_point(position, coord_system) {
            Some(InteractionEvent::PointHover {
                series_index: series_idx,
                point_index: point_idx,
                position,
            })
        } else {
            Some(InteractionEvent::PointLeave)
        }
    }

    /// 处理鼠标点击事件
    pub fn handle_mouse_click(&mut self, position: Point, coord_system: &dyn CoordinateSystem) -> Option<InteractionEvent> {
        if !self.config.interaction.enabled {
            return None;
        }

        if let Some((series_idx, point_idx)) = self.find_nearest_point(position, coord_system) {
            Some(InteractionEvent::PointClick {
                series_index: series_idx,
                point_index: point_idx,
                position,
            })
        } else {
            None
        }
    }

    /// 查找最近的数据点
    fn find_nearest_point(&self, position: Point, coord_system: &dyn CoordinateSystem) -> Option<(usize, usize)> {
        let mut min_distance = f64::INFINITY;
        let mut nearest_point = None;
        let threshold = 20.0; // 20像素的检测阈值

        for i in 0..self.data.len() {
            if let Some(point) = self.data.get_point(i) {
                if let (Some(x), Some(y)) = (point.get_number(0), point.get_number(1)) {
                    let data_values = vec![DataValue::Number(x), DataValue::Number(y)];
                    if let Ok(screen_point) = coord_system.data_to_point(&data_values) {
                        let distance = ((position.x - screen_point.x).powi(2) +
                                      (position.y - screen_point.y).powi(2)).sqrt();

                        if distance < threshold && distance < min_distance {
                            min_distance = distance;
                            nearest_point = Some((0, i)); // series_index = 0 for single series
                        }
                    }
                }
            }
        }

        nearest_point
    }

    /// 更新动画状态
    pub fn update_animation(&mut self, current_time: Instant) -> bool {
        if !self.config.animation.enabled {
            return false;
        }

        if let Some(start_time) = self.last_update {
            let elapsed = current_time.duration_since(start_time);
            if elapsed < Duration::from_millis(self.config.animation.duration as u64) {
                // 动画进行中
                return true;
            } else {
                // 动画完成
                self.last_update = None;
                return false;
            }
        }

        false
    }

    /// 开始数据变化动画
    pub fn animate_data_change(&mut self, new_data: DataSet) {
        if self.config.animation.enabled {
            // 保存当前数据到历史记录
            if self.data_history.len() >= self.max_history {
                self.data_history.remove(0);
            }
            self.data_history.push(self.data.clone());

            // 设置新数据和动画开始时间
            self.data = new_data;
            self.last_update = Some(Instant::now());
        } else {
            // 直接更新数据
            self.data = new_data;
        }
    }

    /// 获取动画进度 (0.0 到 1.0)
    pub fn get_animation_progress(&self) -> f64 {
        if let Some(start_time) = self.last_update {
            let elapsed = Instant::now().duration_since(start_time);
            let progress = elapsed.as_secs_f64() / (self.config.animation.duration as f64 / 1000.0);
            progress.min(1.0)
        } else {
            1.0
        }
    }

    /// 应用缓动函数
    fn apply_easing(&self, t: f64) -> f64 {
        match self.config.animation.easing {
            crate::base::EasingFunction::Linear => t,
            crate::base::EasingFunction::EaseIn => t * t,
            crate::base::EasingFunction::EaseOut => 1.0 - (1.0 - t) * (1.0 - t),
            crate::base::EasingFunction::EaseInOut => {
                if t < 0.5 {
                    2.0 * t * t
                } else {
                    1.0 - 2.0 * (1.0 - t) * (1.0 - t)
                }
            }
            _ => t, // 其他缓动函数的简化实现
        }
    }

    /// 格式化数值标签
    fn format_label(&self, value: f64, format_type: &LabelFormatType) -> String {
        match format_type {
            LabelFormatType::Auto => {
                // 自动选择合适的格式
                if value.abs() < 0.01 {
                    format!("{:.3}", value)
                } else if value.abs() < 1.0 {
                    format!("{:.2}", value)
                } else if value.abs() < 1000.0 {
                    format!("{:.1}", value)
                } else {
                    format!("{:.0}", value)
                }
            }
            LabelFormatType::FixedDecimal(places) => {
                format!("{:.1$}", value, places)
            }
            LabelFormatType::Scientific(places) => {
                format!("{:.1$e}", value, places)
            }
            LabelFormatType::Percentage(places) => {
                format!("{:.1$}%", value * 100.0, places)
            }
            LabelFormatType::Custom(format_str) => {
                // 简单的自定义格式化，可以扩展
                if format_str.contains("{:.") {
                    // 如果包含格式化字符串，尝试应用
                    format_str.replace("{}", &value.to_string())
                } else {
                    format!("{}", value)
                }
            }
        }
    }

    /// 渲染轴标签
    fn render_axis_labels(&self, commands: &mut Vec<DrawCommand>, coord_system: &dyn CoordinateSystem) -> Result<()> {
        let bounds = coord_system.bounds();

        // 渲染Y轴标签
        if self.axis_labels.show_y_labels {
            let y_range = bounds.size.height;
            let step_count = 5; // 显示5个Y轴标签

            for i in 0..=step_count {
                let ratio = i as f64 / step_count as f64;
                let y_pos = bounds.origin.y + bounds.size.height - (ratio * y_range);

                // 计算对应的数据值
                let _data_values = vec![
                    DataValue::Number(bounds.origin.x),
                    DataValue::Number(bounds.origin.y + ratio * bounds.size.height)
                ];

                if let Ok(data_point) = coord_system.point_to_data(Point::new(bounds.origin.x, y_pos)) {
                    if let Some(y_value) = data_point.get(1).and_then(|v| v.as_number()) {
                        let label_text = self.format_label(y_value, &self.axis_labels.y_axis_format);

                        commands.push(DrawCommand::Text {
                            text: label_text,
                            position: Point::new(bounds.origin.x - self.axis_labels.margin, y_pos),
                            style: TextStyle {
                                font_family: "Arial".to_string(),
                                font_size: self.axis_labels.font_size,
                                font_weight: echarts_core::FontWeight::Normal,
                                font_style: echarts_core::FontStyle::Normal,
                                color: self.axis_labels.color,
                                opacity: 1.0,
                                align: echarts_core::TextAlign::Right,
                                baseline: echarts_core::TextBaseline::Middle,
                                rotation: self.axis_labels.y_label_rotation,
                                letter_spacing: 0.0,
                                line_height: 1.0,
                            },
                        });
                    }
                }
            }
        }

        // 渲染X轴标签
        if self.axis_labels.show_x_labels {
            let x_range = bounds.size.width;
            let step_count = 6; // 显示6个X轴标签

            for i in 0..=step_count {
                let ratio = i as f64 / step_count as f64;
                let x_pos = bounds.origin.x + (ratio * x_range);

                if let Ok(data_point) = coord_system.point_to_data(Point::new(x_pos, bounds.origin.y + bounds.size.height)) {
                    if let Some(x_value) = data_point.get(0).and_then(|v| v.as_number()) {
                        let label_text = self.format_label(x_value, &self.axis_labels.x_axis_format);

                        commands.push(DrawCommand::Text {
                            text: label_text,
                            position: Point::new(x_pos, bounds.origin.y + bounds.size.height + self.axis_labels.margin),
                            style: TextStyle {
                                font_family: "Arial".to_string(),
                                font_size: self.axis_labels.font_size,
                                font_weight: echarts_core::FontWeight::Normal,
                                font_style: echarts_core::FontStyle::Normal,
                                color: self.axis_labels.color,
                                opacity: 1.0,
                                align: echarts_core::TextAlign::Center,
                                baseline: echarts_core::TextBaseline::Top,
                                rotation: self.axis_labels.x_label_rotation,
                                letter_spacing: 0.0,
                                line_height: 1.0,
                            },
                        });
                    }
                }
            }
        }

        Ok(())
    }

    /// 渲染面积
    fn render_area(&self, commands: &mut Vec<DrawCommand>, points: &[Point], coord_system: &dyn CoordinateSystem) -> Result<()> {
        if points.len() < 2 {
            return Ok(());
        }

        // 创建面积路径
        let mut path_commands = Vec::new();

        // 移动到第一个点
        path_commands.push(echarts_core::draw_commands::PathCommand::MoveTo(points[0]));

        // 绘制上边线
        for i in 1..points.len() {
            if self.smooth && i < points.len() - 1 {
                // 平滑曲线
                let prev = points[i - 1];
                let curr = points[i];
                let next = points[i + 1];

                let tension = self.smoothness;
                let cp1x = prev.x + (curr.x - prev.x) * (1.0 - tension);
                let cp1y = prev.y + (curr.y - prev.y) * tension;
                let cp2x = curr.x - (next.x - curr.x) * tension;
                let cp2y = curr.y - (next.y - curr.y) * tension;

                path_commands.push(echarts_core::draw_commands::PathCommand::CurveTo {
                    control1: Point::new(cp1x, cp1y),
                    control2: Point::new(cp2x, cp2y),
                    to: curr,
                });
            } else {
                path_commands.push(echarts_core::draw_commands::PathCommand::LineTo(points[i]));
            }
        }

        // 连接到基线
        let coord_bounds = coord_system.bounds();
        let baseline_y = coord_bounds.origin.y + coord_bounds.size.height;

        path_commands.push(echarts_core::draw_commands::PathCommand::LineTo(
            Point::new(points[points.len() - 1].x, baseline_y)
        ));
        path_commands.push(echarts_core::draw_commands::PathCommand::LineTo(
            Point::new(points[0].x, baseline_y)
        ));
        path_commands.push(echarts_core::draw_commands::PathCommand::Close);

        let area_color = self.area_color.unwrap_or_else(|| {
            Color::rgba(self.color.r, self.color.g, self.color.b, self.area_opacity as f32)
        });

        commands.push(DrawCommand::Path {
            commands: path_commands,
            style: echarts_core::draw_commands::PathStyle {
                fill: Some(area_color),
                stroke: None,
                opacity: self.area_opacity,
                fill_rule: echarts_core::draw_commands::FillRule::NonZero,
            },
        });

        Ok(())
    }

    /// 渲染线条
    fn render_line(&self, commands: &mut Vec<DrawCommand>, points: &[Point]) -> Result<()> {
        if points.len() < 2 {
            return Ok(());
        }

        if let Some(step_type) = &self.step {
            // 阶梯线
            self.render_step_line(commands, points, step_type)?;
        } else if self.smooth {
            // 平滑曲线
            self.render_smooth_line(commands, points)?;
        } else {
            // 普通直线
            for i in 0..points.len() - 1 {
                if !self.connect_nulls && (points[i].x.is_nan() || points[i].y.is_nan() ||
                                          points[i + 1].x.is_nan() || points[i + 1].y.is_nan()) {
                    continue;
                }

                commands.push(DrawCommand::Line {
                    from: points[i],
                    to: points[i + 1],
                    style: LineStyle {
                        color: self.color,
                        width: self.line_width,
                        opacity: 1.0,
                        dash_pattern: None,
                        cap: LineCap::Round,
                        join: LineJoin::Round,
                    },
                });
            }
        }

        Ok(())
    }

    /// 渲染阶梯线
    fn render_step_line(&self, commands: &mut Vec<DrawCommand>, points: &[Point], step_type: &StepType) -> Result<()> {
        for i in 0..points.len() - 1 {
            let current = points[i];
            let next = points[i + 1];

            let line_style = LineStyle {
                color: self.color,
                width: self.line_width,
                opacity: 1.0,
                dash_pattern: None,
                cap: LineCap::Round,
                join: LineJoin::Round,
            };

            match step_type {
                StepType::Start => {
                    // 先水平，再垂直
                    commands.push(DrawCommand::Line {
                        from: current,
                        to: Point::new(next.x, current.y),
                        style: line_style.clone(),
                    });
                    commands.push(DrawCommand::Line {
                        from: Point::new(next.x, current.y),
                        to: next,
                        style: line_style,
                    });
                }
                StepType::Middle => {
                    // 到中点水平，再垂直，再水平
                    let mid_x = (current.x + next.x) / 2.0;
                    commands.push(DrawCommand::Line {
                        from: current,
                        to: Point::new(mid_x, current.y),
                        style: line_style.clone(),
                    });
                    commands.push(DrawCommand::Line {
                        from: Point::new(mid_x, current.y),
                        to: Point::new(mid_x, next.y),
                        style: line_style.clone(),
                    });
                    commands.push(DrawCommand::Line {
                        from: Point::new(mid_x, next.y),
                        to: next,
                        style: line_style,
                    });
                }
                StepType::End => {
                    // 先垂直，再水平
                    commands.push(DrawCommand::Line {
                        from: current,
                        to: Point::new(current.x, next.y),
                        style: line_style.clone(),
                    });
                    commands.push(DrawCommand::Line {
                        from: Point::new(current.x, next.y),
                        to: next,
                        style: line_style,
                    });
                }
            }
        }

        Ok(())
    }

    /// 渲染平滑曲线
    fn render_smooth_line(&self, commands: &mut Vec<DrawCommand>, points: &[Point]) -> Result<()> {
        if points.len() < 2 {
            return Ok(());
        }

        let mut path_commands = Vec::new();
        path_commands.push(echarts_core::draw_commands::PathCommand::MoveTo(points[0]));

        for i in 1..points.len() {
            if i < points.len() - 1 {
                let prev = points[i - 1];
                let curr = points[i];
                let next = points[i + 1];

                let tension = self.smoothness;
                let cp1x = prev.x + (curr.x - prev.x) * (1.0 - tension);
                let cp1y = prev.y + (curr.y - prev.y) * tension;
                let cp2x = curr.x - (next.x - curr.x) * tension;
                let cp2y = curr.y - (next.y - curr.y) * tension;

                path_commands.push(echarts_core::draw_commands::PathCommand::CurveTo {
                    control1: Point::new(cp1x, cp1y),
                    control2: Point::new(cp2x, cp2y),
                    to: curr,
                });
            } else {
                path_commands.push(echarts_core::draw_commands::PathCommand::LineTo(points[i]));
            }
        }

        commands.push(DrawCommand::Path {
            commands: path_commands,
            style: echarts_core::draw_commands::PathStyle {
                fill: None,
                stroke: Some(LineStyle {
                    color: self.color,
                    width: self.line_width,
                    opacity: 1.0,
                    dash_pattern: None,
                    cap: LineCap::Round,
                    join: LineJoin::Round,
                }),
                opacity: 1.0,
                fill_rule: echarts_core::draw_commands::FillRule::NonZero,
            },
        });

        Ok(())
    }

    /// 渲染符号
    fn render_symbols(&self, commands: &mut Vec<DrawCommand>, points: &[Point]) -> Result<()> {
        for point in points {
            match self.symbol_type {
                SymbolType::None => continue,
                SymbolType::Circle => {
                    commands.push(DrawCommand::Circle {
                        center: *point,
                        radius: self.symbol_size / 2.0,
                        style: CircleStyle {
                            fill: Some(self.color),
                            stroke: Some(LineStyle {
                                color: Color::rgb(1.0, 1.0, 1.0), // 白色边框
                                width: 1.0,
                                opacity: 1.0,
                                dash_pattern: None,
                                cap: LineCap::Round,
                                join: LineJoin::Round,
                            }),
                            opacity: 1.0,
                        },
                    });
                }
                SymbolType::Square => {
                    let half_size = self.symbol_size / 2.0;
                    commands.push(DrawCommand::Rect {
                        bounds: Bounds::new(
                            point.x - half_size,
                            point.y - half_size,
                            self.symbol_size,
                            self.symbol_size,
                        ),
                        style: echarts_core::draw_commands::RectStyle {
                            fill: Some(self.color),
                            stroke: Some(LineStyle {
                                color: Color::rgb(1.0, 1.0, 1.0),
                                width: 1.0,
                                opacity: 1.0,
                                dash_pattern: None,
                                cap: LineCap::Butt,
                                join: LineJoin::Miter,
                            }),
                            opacity: 1.0,
                            corner_radius: 0.0,
                        },
                    });
                }
                SymbolType::Triangle | SymbolType::Diamond => {
                    // 对于复杂形状，暂时使用圆形代替
                    // TODO: 实现多边形绘制
                    commands.push(DrawCommand::Circle {
                        center: *point,
                        radius: self.symbol_size / 2.0,
                        style: CircleStyle {
                            fill: Some(self.color),
                            stroke: Some(LineStyle {
                                color: Color::rgb(1.0, 1.0, 1.0),
                                width: 1.0,
                                opacity: 1.0,
                                dash_pattern: None,
                                cap: LineCap::Round,
                                join: LineJoin::Round,
                            }),
                            opacity: 1.0,
                        },
                    });
                }
            }
        }

        Ok(())
    }

    /// LTTB (Largest Triangle Three Buckets) 数据优化算法
    pub fn optimize_data_lttb(&self, target_points: usize) -> DataSet {
        if self.data.len() <= target_points || target_points < 3 {
            return self.data.clone();
        }

        let mut optimized_data = DataSet::new();
        let data_len = self.data.len();

        // 总是保留第一个和最后一个点
        if let Some(first_point) = self.data.get_point(0) {
            optimized_data = optimized_data.add_point(first_point.clone());
        }

        let bucket_size = (data_len - 2) as f64 / (target_points - 2) as f64;
        let mut a = 0; // 当前选中的点索引

        for i in 0..(target_points - 2) {
            // 计算当前桶的范围
            let avg_range_start = ((i + 1) as f64 * bucket_size + 1.0).floor() as usize;
            let avg_range_end = ((i + 2) as f64 * bucket_size + 1.0).floor() as usize;
            let avg_range_end = avg_range_end.min(data_len - 1);

            // 计算下一个桶的平均点
            let mut avg_x = 0.0;
            let mut avg_y = 0.0;
            let mut avg_count = 0;

            for j in avg_range_start..avg_range_end {
                if let Some(point) = self.data.get_point(j) {
                    if let (Some(x), Some(y)) = (point.get_number(0), point.get_number(1)) {
                        avg_x += x;
                        avg_y += y;
                        avg_count += 1;
                    }
                }
            }

            if avg_count > 0 {
                avg_x /= avg_count as f64;
                avg_y /= avg_count as f64;
            }

            // 在当前桶中找到形成最大三角形的点
            let range_start = (a + 1).max(1);
            let range_end = ((i + 1) as f64 * bucket_size + 1.0).floor() as usize;
            let range_end = range_end.min(data_len - 1);

            let mut max_area = 0.0;
            let mut max_area_point = range_start;

            if let Some(point_a) = self.data.get_point(a) {
                if let (Some(point_a_x), Some(point_a_y)) = (point_a.get_number(0), point_a.get_number(1)) {
                    for j in range_start..range_end {
                        if let Some(point_j) = self.data.get_point(j) {
                            if let (Some(point_j_x), Some(point_j_y)) = (point_j.get_number(0), point_j.get_number(1)) {
                                // 计算三角形面积
                                let area = ((point_a_x - avg_x) * (point_j_y - point_a_y) -
                                          (point_a_x - point_j_x) * (avg_y - point_a_y)).abs() * 0.5;

                                if area > max_area {
                                    max_area = area;
                                    max_area_point = j;
                                }
                            }
                        }
                    }
                }
            }

            if let Some(selected_point) = self.data.get_point(max_area_point) {
                optimized_data = optimized_data.add_point(selected_point.clone());
            }
            a = max_area_point;
        }

        // 添加最后一个点
        if let Some(last_point) = self.data.get_point(data_len - 1) {
            optimized_data = optimized_data.add_point(last_point.clone());
        }

        optimized_data
    }

    /// Douglas-Peucker 数据简化算法
    pub fn optimize_data_douglas_peucker(&self, epsilon: f64) -> DataSet {
        if self.data.len() <= 2 {
            return self.data.clone();
        }

        let mut points = Vec::new();
        for i in 0..self.data.len() {
            if let Some(point) = self.data.get_point(i) {
                if let (Some(x), Some(y)) = (point.get_number(0), point.get_number(1)) {
                    points.push((x, y));
                }
            }
        }

        let simplified_points = self.douglas_peucker_recursive(&points, epsilon);

        let mut optimized_data = DataSet::new();
        for (x, y) in simplified_points {
            let point = echarts_core::DataPoint::new(vec![
                echarts_core::DataValue::Number(x),
                echarts_core::DataValue::Number(y)
            ]);
            optimized_data = optimized_data.add_point(point);
        }

        optimized_data
    }

    /// Douglas-Peucker 递归实现
    fn douglas_peucker_recursive(&self, points: &[(f64, f64)], epsilon: f64) -> Vec<(f64, f64)> {
        if points.len() <= 2 {
            return points.to_vec();
        }

        let first = points[0];
        let last = points[points.len() - 1];

        // 找到距离直线最远的点
        let mut max_distance = 0.0;
        let mut max_index = 0;

        for (i, &point) in points.iter().enumerate().skip(1).take(points.len() - 2) {
            let distance = self.point_to_line_distance(point, first, last);
            if distance > max_distance {
                max_distance = distance;
                max_index = i;
            }
        }

        if max_distance > epsilon {
            // 递归处理两段
            let mut left_points = self.douglas_peucker_recursive(&points[0..=max_index], epsilon);
            let right_points = self.douglas_peucker_recursive(&points[max_index..], epsilon);

            // 合并结果，避免重复中间点
            left_points.extend_from_slice(&right_points[1..]);
            left_points
        } else {
            // 所有点都在阈值内，只保留端点
            vec![first, last]
        }
    }

    /// 计算点到直线的距离
    fn point_to_line_distance(&self, point: (f64, f64), line_start: (f64, f64), line_end: (f64, f64)) -> f64 {
        let (px, py) = point;
        let (x1, y1) = line_start;
        let (x2, y2) = line_end;

        let a = y2 - y1;
        let b = x1 - x2;
        let c = x2 * y1 - x1 * y2;

        let denominator = (a * a + b * b).sqrt();
        if denominator == 0.0 {
            // 线段退化为点
            ((px - x1).powi(2) + (py - y1).powi(2)).sqrt()
        } else {
            (a * px + b * py + c).abs() / denominator
        }
    }

    /// 渲染数值标签
    fn render_value_labels(&self, commands: &mut Vec<DrawCommand>, points: &[Point]) -> Result<()> {
        // 获取原始数据值用于显示
        for (i, point) in points.iter().enumerate() {
            if let Some(data_point) = self.data.get_point(i) {
                if let Some(y_value) = data_point.get_number(1) {
                    commands.push(DrawCommand::Text {
                        text: format!("{:.1}", y_value),
                        position: Point::new(point.x, point.y - 15.0), // 在点上方显示
                        style: TextStyle {
                            font_family: "Arial".to_string(),
                            font_size: 12.0,
                            font_weight: echarts_core::FontWeight::Normal,
                            font_style: echarts_core::FontStyle::Normal,
                            color: self.color,
                            opacity: 1.0,
                            align: echarts_core::TextAlign::Center,
                            baseline: echarts_core::TextBaseline::Bottom,
                            rotation: 0.0,
                            letter_spacing: 0.0,
                            line_height: 1.0,
                        },
                    });
                }
            }
        }

        Ok(())
    }
}

// ============================================================================
// 分层架构集成 - ChartInteraction 实现
// ============================================================================

/// 为 LineSeries 实现 ChartInteraction trait (中层交互)
impl ChartInteraction for LineSeries {
    fn chart_id(&self) -> &str {
        &self.config.name
    }

    fn handle_chart_event(&mut self, event: ChartInteractionEvent) -> ChartInteractionResult {
        match event {
            ChartInteractionEvent::PointHover { point_index, position, data_value, .. } => {
                // 生成工具提示内容
                let tooltip_content = echarts_interaction::AdapterTooltipContent {
                    title: format!("数据点 {}", point_index),
                    items: vec![
                        echarts_interaction::AdapterTooltipItem {
                            label: "X值".to_string(),
                            value: format!("{:.2}", data_value.0),
                            color: Some(self.color),
                            series_name: self.config.name.clone(),
                        },
                        echarts_interaction::AdapterTooltipItem {
                            label: "Y值".to_string(),
                            value: format!("{:.2}", data_value.1),
                            color: Some(self.color),
                            series_name: self.config.name.clone(),
                        },
                    ],
                    background_color: Color::rgba(0.0, 0.0, 0.0, 0.8),
                    text_color: Color::rgb(1.0, 1.0, 1.0),
                };

                ChartInteractionResult::ShowTooltip {
                    chart_id: self.config.name.clone(),
                    content: tooltip_content,
                    position,
                }
            }

            ChartInteractionEvent::PointClick { series_index, point_index, data_value, .. } => {
                println!("📊 折线图点击: 系列 {}, 点 {}, 值 ({:.2}, {:.2})",
                    series_index, point_index, data_value.0, data_value.1);

                ChartInteractionResult::SelectionChanged {
                    chart_id: self.config.name.clone(),
                    selected_points: vec![(series_index, point_index)],
                }
            }

            ChartInteractionEvent::PointLeave { .. } => {
                ChartInteractionResult::HideTooltip {
                    chart_id: self.config.name.clone(),
                }
            }

            ChartInteractionEvent::ZoomRequest { zoom_factor, center_point, .. } => {
                println!("🔍 折线图缩放: 因子 {:.2}, 中心 ({:.1}, {:.1})",
                    zoom_factor, center_point.x, center_point.y);

                ChartInteractionResult::ViewportChanged {
                    chart_id: self.config.name.clone(),
                    new_bounds: ChartBase::bounds(self).unwrap_or_else(|| Bounds::new(0.0, 0.0, 800.0, 600.0)),
                    zoom_level: zoom_factor,
                }
            }

            ChartInteractionEvent::PanRequest { delta, .. } => {
                println!("↔️ 折线图平移: 偏移 ({:.1}, {:.1})", delta.x, delta.y);

                ChartInteractionResult::ViewportChanged {
                    chart_id: self.config.name.clone(),
                    new_bounds: ChartBase::bounds(self).unwrap_or_else(|| Bounds::new(0.0, 0.0, 800.0, 600.0)),
                    zoom_level: 1.0,
                }
            }

            _ => ChartInteractionResult::None,
        }
    }

    fn find_point_at(&self, position: Point, tolerance: f64) -> Option<(usize, usize, (f64, f64))> {
        // 简化实现 - 暂时返回固定值用于演示
        if position.x >= 0.0 && position.y >= 0.0 {
            Some((0, 0, (position.x, position.y)))
        } else {
            None
        }
    }

    fn get_point_position(&self, _series_index: usize, _point_index: usize) -> Option<Point> {
        // 简化实现 - 暂时返回固定位置
        Some(Point::new(100.0, 100.0))
    }

    fn contains_point(&self, position: Point) -> bool {
        if let Some(bounds) = ChartBase::bounds(self) {
            bounds.contains_point(position)
        } else {
            false
        }
    }

    fn chart_bounds(&self) -> Bounds {
        ChartBase::bounds(self).unwrap_or_else(|| Bounds::new(0.0, 0.0, 800.0, 600.0))
    }
}

// ============================================================================
// 图表基础架构集成 - ChartBase 实现
// ============================================================================

/// 为 LineSeries 实现 ChartBase trait
impl ChartBase for LineSeries {
    type DataType = DataSet;

    fn name(&self) -> &str {
        &self.config.name
    }

    fn raw_data(&self) -> &Self::DataType {
        &self.data
    }

    fn to_dataset(&self) -> DataSet {
        self.data.clone()
    }

    fn visible(&self) -> bool {
        self.config.visible
    }

    fn z_index(&self) -> i32 {
        self.config.z_index
    }

    fn bounds(&self) -> Option<Bounds> {
        BoundsCalculator::from_dataset(&self.data)
    }

    fn config(&self) -> &ChartConfig {
        &self.config
    }

    fn config_mut(&mut self) -> &mut ChartConfig {
        &mut self.config
    }
}

/// 为 LineSeries 实现 ChartSeries trait
impl ChartSeries for LineSeries {
    // 所有方法都由默认实现提供，基于 ChartBase
}

// ============================================================================
// 数据优化架构集成 - ChartOptimization 实现
// ============================================================================

/// 为 LineSeries 实现 ChartOptimization trait (统一优化架构)
impl ChartOptimization for LineSeries {
    fn chart_id(&self) -> &str {
        &self.config.name
    }

    fn get_optimization_config(&self) -> OptimizationConfig {
        // 将折线图特定的算法映射到核心算法
        let core_algorithm = match self.optimization_algorithm {
            LineOptimizationAlgorithm::LTTB => OptimizationAlgorithm::LTTB,
            LineOptimizationAlgorithm::Douglas => OptimizationAlgorithm::DouglasPeucker { epsilon: 1.0 },
        };

        OptimizationConfig {
            algorithm: core_algorithm,
            target_points: self.optimization_target_points,
            min_threshold: 100, // 少于100个点不优化
            enable_pixel_optimization: true,
            display_width: Some(800.0),
            display_height: Some(600.0),
            preserve_extremes: true,
            enable_performance_monitoring: true,
        }
    }

    fn apply_optimization(&mut self, _optimizer: &mut DataOptimizer) -> Result<()> {
        if !self.should_optimize() {
            return Ok(());
        }

        let original_count = self.data.len();

        // 使用现有的优化方法
        if self.optimization_enabled {
            match self.optimization_algorithm {
                LineOptimizationAlgorithm::LTTB => {
                    self.data = self.optimize_data_lttb(self.optimization_target_points);
                }
                LineOptimizationAlgorithm::Douglas => {
                    self.data = self.optimize_data_douglas_peucker(2.0);
                }
            }
        }

        println!("📊 折线图 '{}' 优化完成: {} -> {} 点",
            self.config.name,
            original_count,
            self.data.len()
        );

        Ok(())
    }

    fn should_optimize(&self) -> bool {
        self.optimization_enabled && self.data.len() > 100
    }

    fn get_dataset(&self) -> &DataSet {
        // 简化实现：返回一个空的 DataSet
        // 实际应该在结构体中维护 DataSet 或提供转换方法
        &self.data
    }

    fn set_optimized_dataset(&mut self, _dataset: DataSet) {
        // 简化实现：不做任何操作
        // 实际应该将 DataSet 转换为内部数据格式
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_line_series_creation() {
        let series = LineSeries::new("Test Line")
            .data(vec![(0.0, 1.0), (1.0, 2.0), (2.0, 3.0)])
            .color(Color::rgb(1.0, 0.0, 0.0))
            .line_width(3.0);

        assert_eq!(series.name(), "Test Line");
        assert_eq!(series.series_type(), SeriesType::Line);
        assert_eq!(series.line_width, 3.0);
        assert_eq!(series.data.len(), 3);
    }

    #[test]
    fn test_line_series_bounds() {
        let series = LineSeries::new("Test")
            .data(vec![(1.0, 2.0), (3.0, 4.0), (5.0, 6.0)]);

        let bounds = series.bounds().unwrap();
        assert_eq!(bounds.origin.x, 1.0);
        assert_eq!(bounds.origin.y, 2.0);
        assert_eq!(bounds.size.width, 4.0);
        assert_eq!(bounds.size.height, 4.0);
    }

    #[test]
    fn test_line_series_advanced_features() {
        // 测试平滑曲线
        let smooth_series = LineSeries::new("Smooth Line")
            .data(vec![(0.0, 1.0), (1.0, 3.0), (2.0, 2.0)])
            .smooth(true)
            .smoothness(0.5);

        assert!(smooth_series.smooth);
        assert_eq!(smooth_series.smoothness, 0.5);

        // 测试面积图
        let area_series = LineSeries::new_area("Area Chart", Color::rgb(0.0, 1.0, 0.0))
            .data(vec![(0.0, 1.0), (1.0, 2.0)]);
        assert!(area_series.area);
        assert!(area_series.area_color.is_some());

        // 测试阶梯线
        let step_series = LineSeries::new_step("Step Chart", StepType::Start)
            .data(vec![(0.0, 1.0), (1.0, 2.0)]);
        assert_eq!(step_series.step, Some(StepType::Start));

        // 测试优化功能
        let optimized_series = LineSeries::new_optimized("Large Data", 1000)
            .data(vec![(0.0, 1.0); 5000]);
        assert!(optimized_series.optimization_enabled);
        assert_eq!(optimized_series.optimization_target_points, 1000);
    }

    #[test]
    fn test_symbol_types() {
        let series = LineSeries::new("Test")
            .symbol_type(SymbolType::Square)
            .show_values(true);

        assert_eq!(series.symbol_type, SymbolType::Square);
        assert!(series.show_values);
    }

    #[test]
    fn test_axis_label_formatting() {
        // 测试小数位数设置
        let series = LineSeries::new("Test")
            .x_axis_decimal_places(2)
            .y_axis_decimal_places(3);

        assert_eq!(series.axis_labels.x_axis_format, LabelFormatType::FixedDecimal(2));
        assert_eq!(series.axis_labels.y_axis_format, LabelFormatType::FixedDecimal(3));

        // 测试科学计数法
        let scientific_series = LineSeries::new_scientific("Scientific", 2);
        assert_eq!(scientific_series.axis_labels.x_axis_format, LabelFormatType::Scientific(2));
        assert_eq!(scientific_series.axis_labels.y_axis_format, LabelFormatType::Scientific(2));

        // 测试百分比格式
        let percentage_series = LineSeries::new_percentage("Percentage", 1);
        assert_eq!(percentage_series.axis_labels.y_axis_format, LabelFormatType::Percentage(1));
    }

    #[test]
    fn test_label_formatting_function() {
        let series = LineSeries::new("Test");

        // 测试固定小数位数
        assert_eq!(series.format_label(3.14159, &LabelFormatType::FixedDecimal(2)), "3.14");
        assert_eq!(series.format_label(3.14159, &LabelFormatType::FixedDecimal(0)), "3");

        // 测试科学计数法
        assert_eq!(series.format_label(1234.5, &LabelFormatType::Scientific(2)), "1.23e3");

        // 测试百分比
        assert_eq!(series.format_label(0.1234, &LabelFormatType::Percentage(1)), "12.3%");

        // 测试自动格式化
        assert_eq!(series.format_label(0.001, &LabelFormatType::Auto), "0.001");
        assert_eq!(series.format_label(1.5, &LabelFormatType::Auto), "1.5");
        assert_eq!(series.format_label(1500.0, &LabelFormatType::Auto), "1500");
    }

    #[test]
    fn test_axis_label_config() {
        let mut config = AxisLabelConfig::default();
        config.x_axis_format = LabelFormatType::FixedDecimal(3);
        config.show_x_labels = false;

        let series = LineSeries::new("Test")
            .axis_labels(config.clone());

        assert_eq!(series.axis_labels.x_axis_format, LabelFormatType::FixedDecimal(3));
        assert!(!series.axis_labels.show_x_labels);
        assert!(series.axis_labels.show_y_labels); // 默认值
    }

    #[test]
    fn test_convenience_constructors() {
        // 测试带轴格式的构造函数
        let series = LineSeries::new_with_axis_format("Test", 1, 2);
        assert_eq!(series.axis_labels.x_axis_format, LabelFormatType::FixedDecimal(1));
        assert_eq!(series.axis_labels.y_axis_format, LabelFormatType::FixedDecimal(2));

        // 测试轴标签旋转
        let rotated_series = LineSeries::new("Test")
            .axis_label_rotation(45.0, -90.0);
        assert_eq!(rotated_series.axis_labels.x_label_rotation, 45.0);
        assert_eq!(rotated_series.axis_labels.y_label_rotation, -90.0);
    }
}
