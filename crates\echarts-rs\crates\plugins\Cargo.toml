[package]
name = "rust-echarts-plugins"
version = "0.1.0"
edition = "2021"
description = "Plugin system for ECharts-rs"
license = "MIT"
repository = "https://github.com/your-repo/echarts-rs"

[dependencies]
rust-echarts-core = { path = "../core" }
rust-echarts-charts = { path = "../charts" }
rust-echarts-components = { path = "../components" }
rust-echarts-renderer = { path = "../renderer" }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Dynamic loading
libloading = "0.8"
dlopen = "0.1"

# Plugin interface
abi_stable = "0.11"

# Scripting support
rhai = { version = "1.15", optional = true }
mlua = { version = "0.9", optional = true, features = ["lua54"] }

# Hot reloading
notify = { version = "6.0", optional = true }
parking_lot = "0.12"

[features]
default = []
scripting-rhai = ["dep:rhai"]
scripting-lua = ["dep:mlua"]
hot-reload = ["dep:notify"]
full = ["scripting-rhai", "scripting-lua", "hot-reload"]

[dev-dependencies]
tempfile = "3.0"
