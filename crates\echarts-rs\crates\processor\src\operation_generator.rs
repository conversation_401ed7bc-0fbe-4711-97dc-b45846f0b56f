/*!
 * 操作生成器
 * 
 * 负责根据布局信息和ECharts配置生成具体的绘制操作队列
 */

use crate::{
    ProcessedChart, DrawOperation, ChartLayout, Point, Bounds, Color, TextAlign,
    Result, ProcessorError,
    chart_processor::{SeriesProcessor, TitleProcessor},
};
use serde_json::Value;

/// 操作生成器
pub struct OperationGenerator {
    /// 是否启用调试模式
    debug_mode: bool,
}

impl OperationGenerator {
    /// 创建新的操作生成器
    pub fn new() -> Self {
        Self {
            debug_mode: false,
        }
    }

    /// 启用调试模式
    pub fn with_debug(mut self) -> Self {
        self.debug_mode = true;
        self
    }

    /// 生成绘制操作
    pub fn generate_operations(
        &self,
        echarts_option: &Value,
        layout: &ChartLayout,
        processed: &mut ProcessedChart,
    ) -> Result<()> {
        println!("🎨 生成绘制操作");

        // 1. 生成背景操作
        self.generate_background_operations(layout, processed)?;

        // 2. 生成标题操作
        self.generate_title_operations(echarts_option, layout, processed)?;

        // 3. 生成坐标轴操作
        self.generate_axis_operations(echarts_option, layout, processed)?;

        // 4. 生成图表边框操作
        self.generate_chart_border_operations(layout, processed)?;

        // 5. 生成系列数据操作
        self.generate_series_operations(echarts_option, layout, processed)?;

        // 6. 生成图例操作
        self.generate_legend_operations(echarts_option, layout, processed)?;

        // 7. 如果启用调试模式，生成调试信息
        if self.debug_mode {
            self.generate_debug_operations(layout, processed)?;
        }

        Ok(())
    }

    /// 生成背景操作
    fn generate_background_operations(
        &self,
        layout: &ChartLayout,
        processed: &mut ProcessedChart,
    ) -> Result<()> {
        let bg_color = processed.background_color.unwrap_or(Color::WHITE);

        processed.draw_operations.push(DrawOperation::Rect {
            bounds: layout.total_bounds,
            fill_color: bg_color,
            stroke_color: None,
            stroke_width: 0.0,
        });

        Ok(())
    }

    /// 生成标题操作
    fn generate_title_operations(
        &self,
        echarts_option: &Value,
        layout: &ChartLayout,
        processed: &mut ProcessedChart,
    ) -> Result<()> {
        if let Some(title_config) = echarts_option.get("title") {
            if let Some(_title_area) = &layout.title_area {
                TitleProcessor::process_title(
                    title_config,
                    layout.total_bounds,
                    &mut processed.draw_operations,
                )?;
            }
        }

        Ok(())
    }

    /// 生成坐标轴操作
    fn generate_axis_operations(
        &self,
        echarts_option: &Value,
        layout: &ChartLayout,
        processed: &mut ProcessedChart,
    ) -> Result<()> {
        // 生成X轴
        if let Some(_x_axis) = echarts_option.get("xAxis") {
            if let Some(x_axis_area) = layout.axis_areas.get("xAxis") {
                self.generate_x_axis_operations(x_axis_area, &layout.plot_area, processed)?;
            }
        }

        // 生成Y轴
        if let Some(_y_axis) = echarts_option.get("yAxis") {
            if let Some(y_axis_area) = layout.axis_areas.get("yAxis") {
                self.generate_y_axis_operations(y_axis_area, &layout.plot_area, processed)?;
            }
        }

        Ok(())
    }

    /// 生成X轴操作
    fn generate_x_axis_operations(
        &self,
        _x_axis_area: &Bounds,
        plot_area: &Bounds,
        processed: &mut ProcessedChart,
    ) -> Result<()> {
        // 绘制X轴线
        processed.draw_operations.push(DrawOperation::Line {
            from: Point::new(plot_area.x, plot_area.y + plot_area.height),
            to: Point::new(plot_area.x + plot_area.width, plot_area.y + plot_area.height),
            color: Color::BLACK,
            width: 1.0,
            dash_pattern: None,
        });

        // 生成X轴标签和刻度
        self.generate_x_axis_labels_and_ticks(plot_area, processed)?;

        Ok(())
    }

    /// 生成X轴标签和刻度
    fn generate_x_axis_labels_and_ticks(
        &self,
        plot_area: &Bounds,
        processed: &mut ProcessedChart,
    ) -> Result<()> {
        let tick_count = 6; // 6个刻度点
        let tick_length = 5.0;

        for i in 0..tick_count {
            let t = i as f64 / (tick_count - 1) as f64;
            let x = plot_area.x + plot_area.width * t;
            let y_axis_bottom = plot_area.y + plot_area.height;

            // 绘制刻度线
            processed.draw_operations.push(DrawOperation::Line {
                from: Point::new(x, y_axis_bottom),
                to: Point::new(x, y_axis_bottom + tick_length),
                color: Color::BLACK,
                width: 1.0,
                dash_pattern: None,
            });

            // 绘制标签
            let label = match i {
                0 => "1月".to_string(),
                1 => "2月".to_string(),
                2 => "3月".to_string(),
                3 => "4月".to_string(),
                4 => "5月".to_string(),
                5 => "6月".to_string(),
                _ => format!("{}月", i + 1),
            };

            processed.draw_operations.push(DrawOperation::Text {
                text: label,
                position: Point::new(x, y_axis_bottom + tick_length + 15.0),
                color: Color::BLACK,
                font_size: 12.0,
                font_family: "Arial".to_string(),
                text_align: TextAlign::Center,
            });
        }

        Ok(())
    }

    /// 生成Y轴操作
    fn generate_y_axis_operations(
        &self,
        _y_axis_area: &Bounds,
        plot_area: &Bounds,
        processed: &mut ProcessedChart,
    ) -> Result<()> {
        // 绘制Y轴线
        processed.draw_operations.push(DrawOperation::Line {
            from: Point::new(plot_area.x, plot_area.y),
            to: Point::new(plot_area.x, plot_area.y + plot_area.height),
            color: Color::BLACK,
            width: 1.0,
            dash_pattern: None,
        });

        // 生成Y轴标签和刻度
        self.generate_y_axis_labels_and_ticks(plot_area, processed)?;

        Ok(())
    }

    /// 生成Y轴标签和刻度
    fn generate_y_axis_labels_and_ticks(
        &self,
        plot_area: &Bounds,
        processed: &mut ProcessedChart,
    ) -> Result<()> {
        let tick_count = 6; // 6个刻度点
        let tick_length = 5.0;

        for i in 0..tick_count {
            let t = i as f64 / (tick_count - 1) as f64;
            let y = plot_area.y + plot_area.height * (1.0 - t); // 从上到下
            let x_axis_left = plot_area.x;

            // 绘制刻度线
            processed.draw_operations.push(DrawOperation::Line {
                from: Point::new(x_axis_left - tick_length, y),
                to: Point::new(x_axis_left, y),
                color: Color::BLACK,
                width: 1.0,
                dash_pattern: None,
            });

            // 绘制标签 - 根据数据范围计算值
            let value = i as f64 * 100.0; // 假设范围是0-500
            let label = format!("{}", value as i32);

            processed.draw_operations.push(DrawOperation::Text {
                text: label,
                position: Point::new(x_axis_left - tick_length - 25.0, y),
                color: Color::BLACK,
                font_size: 12.0,
                font_family: "Arial".to_string(),
                text_align: TextAlign::Right,
            });
        }

        Ok(())
    }

    /// 生成图表边框操作
    fn generate_chart_border_operations(
        &self,
        layout: &ChartLayout,
        processed: &mut ProcessedChart,
    ) -> Result<()> {
        // 绘制绘图区域边框
        processed.draw_operations.push(DrawOperation::Rect {
            bounds: layout.plot_area,
            fill_color: Color::rgba_u8(240, 240, 240, 50), // 半透明背景
            stroke_color: Some(Color::rgba_u8(200, 200, 200, 255)),
            stroke_width: 1.0,
        });

        Ok(())
    }

    /// 生成系列数据操作
    fn generate_series_operations(
        &self,
        echarts_option: &Value,
        layout: &ChartLayout,
        processed: &mut ProcessedChart,
    ) -> Result<()> {
        if let Some(series_array) = echarts_option.get("series").and_then(|s| s.as_array()) {
            for series in series_array {
                SeriesProcessor::process_series(
                    series,
                    layout.plot_area,
                    &mut processed.draw_operations,
                )?;
            }
        }

        Ok(())
    }

    /// 生成图例操作
    fn generate_legend_operations(
        &self,
        echarts_option: &Value,
        layout: &ChartLayout,
        processed: &mut ProcessedChart,
    ) -> Result<()> {
        if let Some(legend_config) = echarts_option.get("legend") {
            if let Some(legend_area) = &layout.legend_area {
                self.generate_legend_items(legend_config, legend_area, processed)?;
            }
        }

        Ok(())
    }

    /// 生成图例项
    fn generate_legend_items(
        &self,
        legend_config: &Value,
        legend_area: &Bounds,
        processed: &mut ProcessedChart,
    ) -> Result<()> {
        if let Some(legend_data) = legend_config.get("data").and_then(|d| d.as_array()) {
            let item_width = legend_area.width / legend_data.len() as f64;
            
            for (i, item) in legend_data.iter().enumerate() {
                if let Some(name) = item.as_str() {
                    let x = legend_area.x + i as f64 * item_width;
                    let y = legend_area.y + legend_area.height / 2.0;

                    // 绘制图例标记
                    processed.draw_operations.push(DrawOperation::Rect {
                        bounds: Bounds::new(x, y - 5.0, 10.0, 10.0),
                        fill_color: Color::BLUE,
                        stroke_color: None,
                        stroke_width: 0.0,
                    });

                    // 绘制图例文本
                    processed.draw_operations.push(DrawOperation::Text {
                        text: name.to_string(),
                        position: Point::new(x + 15.0, y),
                        font_size: 12.0,
                        color: Color::BLACK,
                        font_family: "Arial".to_string(),
                        text_align: TextAlign::Left,
                    });
                }
            }
        }

        Ok(())
    }

    /// 生成调试操作
    fn generate_debug_operations(
        &self,
        layout: &ChartLayout,
        processed: &mut ProcessedChart,
    ) -> Result<()> {
        // 绘制布局边界（调试用）
        let debug_color = Color::rgba_u8(255, 0, 0, 100); // 半透明红色

        // 标题区域边界
        if let Some(title_area) = &layout.title_area {
            processed.draw_operations.push(DrawOperation::Rect {
                bounds: *title_area,
                fill_color: Color::rgba_u8(255, 255, 0, 50),
                stroke_color: Some(debug_color),
                stroke_width: 1.0,
            });
        }

        // 绘图区域边界
        processed.draw_operations.push(DrawOperation::Rect {
            bounds: layout.plot_area,
            fill_color: Color::rgba_u8(0, 255, 0, 30),
            stroke_color: Some(debug_color),
            stroke_width: 2.0,
        });

        // 图例区域边界
        if let Some(legend_area) = &layout.legend_area {
            processed.draw_operations.push(DrawOperation::Rect {
                bounds: *legend_area,
                fill_color: Color::rgba_u8(0, 0, 255, 50),
                stroke_color: Some(debug_color),
                stroke_width: 1.0,
            });
        }

        Ok(())
    }

    /// 生成默认示例操作（当没有有效数据时）
    pub fn generate_default_example_operations(
        &self,
        layout: &ChartLayout,
        processed: &mut ProcessedChart,
    ) -> Result<()> {
        println!("📊 生成默认示例操作");

        let plot_area = layout.plot_area;
        let bar_width = plot_area.width / 6.0;
        let values = [10.0, 20.0, 30.0, 40.0, 50.0];

        for (i, &value) in values.iter().enumerate() {
            let x = plot_area.x + (i as f64 + 0.5) * bar_width;
            let height = (value / 50.0) * plot_area.height * 0.8;
            let y = plot_area.y + plot_area.height - height;

            let bar_bounds = Bounds::new(x - bar_width * 0.3, y, bar_width * 0.6, height);

            processed.draw_operations.push(DrawOperation::Rect {
                bounds: bar_bounds,
                fill_color: Color::BLUE,
                stroke_color: None,
                stroke_width: 0.0,
            });
        }

        Ok(())
    }
}

impl Default for OperationGenerator {
    fn default() -> Self {
        Self::new()
    }
}
