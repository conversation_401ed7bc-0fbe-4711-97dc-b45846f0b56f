//! Core data structures and traits for Rust ECharts

pub mod animation;
pub mod chart;
pub mod color;
pub mod coord;
pub mod crosshair;
pub mod data;
pub mod error;
pub mod event;
pub mod geometry;
pub mod interaction;
pub mod optimization;
pub mod optimization_adapter;
pub mod performance;
pub mod scale;
pub mod shortcuts;
pub mod style;
pub mod utils;
pub mod viewport;

// 测试模块
#[cfg(test)]
pub mod tests;

// 新的高性能架构模块
pub mod draw_commands;
pub mod geometry_compiler;
pub mod professional_interactions;
pub mod render_cache;
pub mod render_context;
pub mod simple_canvas;

// Re-export core types (avoiding conflicts)
pub use animation::*;
pub use chart::{
    Chart, Series, SeriesType, CartesianCoordinateSystem,
};
// 导出统一的 DrawCommand 和新的 RenderContext
pub use draw_commands::DrawCommand;
pub use render_context::{RenderContext, StyleState, Transform, PerformanceHint, RenderTheme};
pub use color::*;
pub use coord::CoordinateSystem;
pub use crosshair::*;
pub use data::*;
pub use error::*;
pub use event::*;
pub use geometry::*;
pub use interaction::*;
pub use optimization::*;
pub use scale::*;
pub use shortcuts::*;
pub use style::*;
pub use viewport::*;

// 新架构模块导出（避免命名冲突）
pub use draw_commands::{
    CircleStyle, DrawBatch, FillRule, PathCommand, PathStyle, PointShape, PointStyle, PolygonStyle,
    RectStyle, DrawCommand as LowLevelDrawCommand, // 重命名以避免冲突
};

// 样式系统统一导出（从 style 模块）
pub use style::{
    TextStyle, LineStyle, FontWeight, FontStyle, TextAlign, TextBaseline, LineCap, LineJoin,
    FillStyle, ShadowStyle, BorderStyle, ElementStyle,
};
pub use geometry_compiler::{
    CompileContext, CompileResult, CompileStats, GeometryCompiler, LineCompiler, LodLevel,
    StyleConfig as GeometryStyleConfig, ViewportInfo,
};
pub use professional_interactions::{
    ArrowKey, ContextMenuItem, GestureData, GestureType, InteractionController,
    InteractionEvent as ProfInteractionEvent, InteractionResponse, InteractionState, Key,
    KeyModifiers, MouseButton, SelectionController, SelectionItem, SelectionMode, TooltipContent,
    TooltipItem, TooltipStyle, TouchPoint, Viewport as ProfViewport, ZoomPanController,
};
pub use render_cache::{
    calculate_hash, CacheConfig, CacheItem, CacheKey, CacheKeyGenerator, CacheStats, RenderCache,
};
pub use simple_canvas::{
    BatchOptimizer, CanvasRenderer, PerformanceProfiler, RenderStats, SimpleCanvas,
};

/// Core traits for chart components
pub mod traits {

    /// Trait for objects that can handle interactions
    pub trait Interactive {
        fn on_mouse_move(&mut self, x: f64, y: f64) -> bool;
        fn on_mouse_click(&mut self, x: f64, y: f64) -> bool;
        fn on_mouse_wheel(&mut self, delta: f64) -> bool;
    }

    /// Trait for configurable objects
    pub trait Configurable<T> {
        fn configure(&mut self, config: T);
        fn get_config(&self) -> &T;
    }

    /// Trait for data binding
    pub trait DataBound<T> {
        fn bind_data(&mut self, data: T);
        fn get_data(&self) -> &T;
        fn update_data(&mut self, data: T);
    }
}

// Re-export traits
pub use traits::*;
