use crate::device::error::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use crate::device::types::<PERSON><PERSON><PERSON>and<PERSON>;
use crate::AppState;
use anyhow::Context as _;
use futures::{stream::FuturesUnordered, StreamExt};
use gpui::{BackgroundExecutor, Context, Timer};
use std::sync::Arc;
use tokio::sync::Mutex;
use tokio::time::Duration;
use tsdaq_protocol::*;

/// 设备超时设置（毫秒）
const DEVICE_OPERATION_TIMEOUT: u64 = 1000;

/// 设备管理器，负责管理所有设备的连接和数据采集
pub struct DeviceManager {
    /// 已连接的设备列表
    devices: Arc<Mutex<Vec<DeviceHandle>>>,
    bg_exec: Arc<BackgroundExecutor>,
}

impl DeviceManager {
    /// 创建新的设备管理器实例
    pub fn new(bg_exec: Arc<BackgroundExecutor>) -> Self {
        Self {
            devices: Arc::new(Mutex::new(Vec::new())),
            bg_exec,
        }
    }

    /// 获取当前连接的所有设备
    pub async fn get_devices(&self) -> Vec<DeviceHandle> {
        self.devices.lock().await.clone()
    }

    /// 检查是否有任何设备正在运行
    pub async fn is_any_device_running(&self) -> bool {
        let devices = self.devices.lock().await;
        for device in devices.iter() {
            if device.is_running().await {
                return true;
            }
        }
        false
    }

    /// 连接所有可用的设备
    pub async fn connect_all_devices(&self) -> DeviceResult<()> {
        let serial_ports = SerialTransport::available_ports();
        if serial_ports.is_empty() {
            return Err(DeviceError::NoSerialPortsAvailable);
        }

        let mut found_devices = Vec::new();
        for port in serial_ports {
            let port_name = port.port_name.clone();
            let bg_exec = self.bg_exec.clone();
            match bg_exec.block_with_timeout(
                Duration::from_millis(DEVICE_OPERATION_TIMEOUT),
                self.try_connect_device(port_name.clone()),
            ) {
                Ok(Some(device)) => found_devices.push(device),
                Ok(None) => {}
                Err(_) => {
                    println!("[{}] 连接超时", port_name);
                }
            }
        }

        println!("成功连接 {} 个设备", found_devices.len());
        *self.devices.lock().await = found_devices;
        Ok(())
    }

    /// 尝试连接单个设备
    async fn try_connect_device(&self, port_name: String) -> Option<DeviceHandle> {
        let transport = match SerialTransport::new(&port_name, 115_200) {
            Ok(t) => Box::new(t),
            Err(e) => {
                println!("[{}] 创建传输层失败: {:?}", port_name, e);
                return None;
            }
        };

        let codec = Box::new(StandardCodec::new(CheckMethod::Crc16));
        let device = Box::new(TSDevice::new(transport, codec));
        let mut client = TSDAQClient::new(device, self.bg_exec.clone());

        println!("[{}] 尝试查询设备", port_name);
        let query_result = client.query_device().await;
        if let Err(e) = &query_result {
            println!("[{}] 查询设备失败: {:?}", port_name, e);
        }

        println!("[{}] 尝试连接设备", port_name);
        let connect_result = client.connect().await;
        if let Err(e) = &connect_result {
            println!("[{}] 连接设备失败: {:?}", port_name, e);
        }

        if query_result.is_ok() && connect_result.is_ok() {
            println!("[{}] 连接成功", port_name);
            Some(DeviceHandle::new(port_name.to_string(), client))
        } else {
            None
        }
    }

    /// 断开所有设备的连接
    pub async fn disconnect_all_devices(&self) -> DeviceResult<()> {
        let mut devices = self.devices.lock().await;
        println!("断开 {} 个设备", devices.len());

        let mut tasks = FuturesUnordered::new();

        // 并发断开所有设备
        for device in devices.iter() {
            let device = device.clone();
            tasks.push(async move {
                println!("[{}] 停止采集", device.port);
                let stop_result = device.stop_collection().await;
                if let Err(e) = &stop_result {
                    println!("[{}] 停止采集失败: {:?}", device.port, e);
                }

                println!("[{}] 断开连接", device.port);
                let disconnect_result = device.disconnect().await;
                if let Err(e) = &disconnect_result {
                    println!("[{}] 断开连接失败: {:?}", device.port, e);
                    return Err(DeviceError::DisconnectionFailed(format!(
                        "设备 {} 断开失败: {}",
                        device.port, e
                    )));
                }
                Ok(())
            });
        }

        // 等待所有断开操作完成
        while let Some(result) = tasks.next().await {
            if let Err(e) = result {
                eprintln!("断开设备失败: {}", e);
                // 继续断开其他设备
            }
        }

        devices.clear();
        println!("已清空设备列表");
        Ok(())
    }

    /// 开始所有设备的数据采集
    pub async fn start_data_collection(&self) -> DeviceResult<()> {
        let devices = self.devices.lock().await.clone();
        println!("开始对 {} 个设备进行数据采集", devices.len());

        if devices.is_empty() {
            println!("没有可用设备，无法开始采集");
            return Err(DeviceError::NoSerialPortsAvailable);
        }

        // 创建FuturesUnordered集合，用于并行处理所有设备
        let bg_exec = self.bg_exec.clone();
        let mut tasks = FuturesUnordered::new();

        // 第一阶段：并行停止所有设备的采集
        for device in &devices {
            let device = device.clone();
            let bg_exec_inner = bg_exec.clone();
            tasks.push(async move {
                println!("[{}] 启动采集前先停止采集", device.port);

                // 重试机制：最多重试3次
                let mut retry_count = 0;
                let max_retries = 3;

                loop {
                    // 先停止采集
                    match bg_exec_inner.block_with_timeout(
                        Duration::from_millis(DEVICE_OPERATION_TIMEOUT),
                        device.stop_collection(),
                    ) {
                        Ok(Ok(_)) => {
                            println!("[{}] 预先停止采集成功", device.port);
                            break;
                        }
                        Ok(Err(e)) => {
                            retry_count += 1;
                            eprintln!(
                                "[{}] 预先停止采集失败 (第{}次): {:?}",
                                device.port, retry_count, e
                            );
                            if retry_count >= max_retries {
                                eprintln!(
                                    "[{}] 停止采集重试{}次后仍然失败，继续尝试启动",
                                    device.port, max_retries
                                );
                                break;
                            }
                            // 重试前等待100ms
                            Timer::after(Duration::from_millis(100)).await;
                        }
                        Err(_) => {
                            retry_count += 1;
                            eprintln!("[{}] 预先停止采集超时 (第{}次)", device.port, retry_count);
                            if retry_count >= max_retries {
                                eprintln!(
                                    "[{}] 停止采集重试{}次后仍然超时，继续尝试启动",
                                    device.port, max_retries
                                );
                                break;
                            }
                            // 重试前等待100ms
                            Timer::after(Duration::from_millis(100)).await;
                        }
                    }
                }

                Ok::<DeviceHandle, DeviceError>(device.clone())
            });
        }

        // 等待所有停止操作完成
        let mut stopped_devices = Vec::new();
        while let Some(result) = tasks.next().await {
            match result {
                Ok(device) => stopped_devices.push(device),
                Err(e) => eprintln!("停止设备采集失败: {}", e),
            }
        }

        // 全部设备等待100ms
        println!("所有设备停止后，统一延迟100ms");
        Timer::after(Duration::from_millis(100)).await;

        // 第二阶段：并行启动所有设备的采集
        let mut start_tasks = FuturesUnordered::new();

        for device in stopped_devices {
            let device = device.clone();
            let bg_exec_inner = bg_exec.clone();
            start_tasks.push(async move {
                println!("[{}] 开始启动采集", device.port);
                match bg_exec_inner.block_with_timeout(
                    Duration::from_millis(DEVICE_OPERATION_TIMEOUT),
                    device.start_collection(),
                ) {
                    Ok(Ok(_)) => {
                        println!("[{}] 启动采集成功，准备启动数据流处理", device.port);
                        let device_for_data = device.clone();
                        bg_exec_inner
                            .spawn(async move {
                                println!("[{}] 数据流处理任务已spawn", device_for_data.port);
                                Self::handle_data_stream(
                                    &device_for_data.port,
                                    &device_for_data.client,
                                    &device_for_data.running,
                                )
                                .await;
                                println!("[{}] 数据流处理任务已结束", device_for_data.port);
                            })
                            .detach();
                        Ok(())
                    }
                    Ok(Err(e)) => {
                        eprintln!("[{}] 启动采集失败: {:?}", device.port, e);
                        Err(DeviceError::DataCollectionFailed(format!(
                            "启动采集失败: {}",
                            e
                        )))
                    }
                    Err(_) => {
                        eprintln!("[{}] 启动采集超时", device.port);
                        Err(DeviceError::TimeoutError(format!(
                            "设备 {} 启动采集超时",
                            device.port
                        )))
                    }
                }
            });
        }

        // 等待所有启动操作完成
        while let Some(result) = start_tasks.next().await {
            if let Err(e) = result {
                eprintln!("2: {:?}", e);
                // 继续处理其他设备，不中断整个过程
            }
        }

        Ok(())
    }

    /// 处理数据流
    async fn handle_data_stream(
        port: &str,
        client: &Arc<Mutex<TSDAQClient>>,
        running: &Arc<Mutex<bool>>,
    ) {
        println!("[{}] ------开始采集数据流------", port);
        // 获取数据流
        let mut client_locked = client.lock().await;
        let mut data_stream = Box::pin(client_locked.stream_data().await);
        let mut received_data = false;

        // 处理当前数据流中的所有数据
        while let Some(result) = data_stream.next().await {
            received_data = true;

            // match result {
            //     Ok(channels) => {
            //         println!("[{}] 收到数据: {} channels", port, channels.len());
            //     }
            //     Err(e) => {
            //         eprintln!("[{}] 采集数据出错: {}", port, e);
            //     }
            // }

            // 每次处理完一批数据后检查运行状态
            if !*running.lock().await {
                println!("[{}] 处理数据中检测到停止信号", port);
                break;
            }
        }
        println!("[{}] 数据流处理任务已结束", port);
    }

    /// 停止所有设备的数据采集
    pub async fn stop_data_collection(&self) -> DeviceResult<()> {
        let devices = self.devices.lock().await;
        println!("停止 {} 个设备的数据采集", devices.len());

        let bg_exec = self.bg_exec.clone();
        let mut tasks = FuturesUnordered::new();

        for device in devices.iter() {
            let device = device.clone();
            let bg_exec_inner = bg_exec.clone();
            tasks.push(async move {
                println!("[{}] 准备停止采集", device.port);

                // 先设置running状态为false，确保数据流处理循环可以退出
                println!("[{}] 设置运行状态为false", device.port);
                device.set_running(false).await;

                // 等待一段时间让数据流处理循环有机会检测到状态变化
                Timer::after(Duration::from_millis(50)).await;

                println!("[{}] 发送停止采集命令", device.port);

                // 重试参数
                let mut retry_count = 0;
                let max_retries = 3;

                while retry_count <= max_retries {
                    match bg_exec_inner.block_with_timeout(
                        Duration::from_millis(DEVICE_OPERATION_TIMEOUT),
                        device.stop_collection(),
                    ) {
                        Ok(Ok(_)) => {
                            println!("[{}] 发送停止采集命令成功", device.port);
                            return Ok(());
                        }
                        Ok(Err(e)) => {
                            retry_count += 1;
                            eprintln!(
                                "[{}] 发送停止采集命令失败 (第{}次): {:?}",
                                device.port, retry_count, e
                            );
                            if retry_count > max_retries {
                                return Err(DeviceError::DataCollectionFailed(format!(
                                    "停止采集失败: {}",
                                    e
                                )));
                            }
                        }
                        Err(_) => {
                            retry_count += 1;
                            eprintln!("[{}] 停止采集超时 (第{}次)", device.port, retry_count);
                            if retry_count > max_retries {
                                return Err(DeviceError::TimeoutError(format!(
                                    "设备 {} 停止采集超时",
                                    device.port
                                )));
                            }
                        }
                    }
                    // 重试前等待
                    Timer::after(Duration::from_millis(100)).await;
                }

                Err(DeviceError::DataCollectionFailed(format!(
                    "设备 {} 停止失败，超出最大重试次数",
                    device.port
                )))
            });
        }

        // 等待所有任务完成
        while let Some(result) = tasks.next().await {
            if let Err(e) = result {
                eprintln!("停止采集失败: {:?}", e);
                // 继续处理其他设备，不中断整个过程
            }
        }

        Ok(())
    }
}
