/*!
 * ECharts GPUI 优化仪表板示例
 *
 * 本示例展示了优化后的架构：
 * - 数据处理在Dashboard中进行（使用SeriesProcessor）
 * - 渲染器只负责渲染已处理的Chart数据
 * - 避免重复的数据转换，提高性能
 */

use echarts_rs::prelude::*;
use gpui::Bounds as GpuiBounds;
use gpui::{
    div, px, rgb, size, AppContext, Application, Context, FontWeight, IntoElement, ParentElement,
    Render, Styled, TitlebarOptions, Window, WindowBackgroundAppearance, WindowBounds, WindowKind,
    WindowOptions,
};
use serde_json::json;

fn main() {
    println!("🚀 启动 ECharts GPUI 优化仪表板");

    let app = Application::new();

    app.run(move |cx| {
        let window_size = size(px(1200.0), px(800.0));

        let _window = cx.open_window(
            WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(GpuiBounds::centered(
                    None,
                    window_size,
                    cx,
                ))),
                titlebar: Some(TitlebarOptions {
                    title: Some("ECharts 优化仪表板".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                window_background: WindowBackgroundAppearance::Opaque,
                app_id: None,
                window_min_size: Some(size(px(800.0), px(600.0))),
                window_decorations: None,
            },
            |_window, cx| {
                println!("🪟 优化仪表板窗口已创建");
                cx.new(|cx| OptimizedDashboard::new(cx))
            },
        );
    });
}

/// 优化的仪表板结构
struct OptimizedDashboard {
    /// 已处理的图表数据
    processed_charts: Vec<ProcessedChartData>,
}

/// 已处理的图表数据
struct ProcessedChartData {
    /// 图表标题
    title: String,
    /// 处理好的Chart对象
    chart: Chart,
    /// 图表类型
    chart_type: String,
}

impl OptimizedDashboard {
    fn new(_cx: &mut Context<Self>) -> Self {
        println!("📊 初始化优化仪表板 - 预处理所有图表数据");

        let mut processed_charts = Vec::new();

        // 预处理所有图表数据
        processed_charts.push(Self::process_sales_chart());
        processed_charts.push(Self::process_product_chart());
        processed_charts.push(Self::process_activity_chart());
        processed_charts.push(Self::process_area_chart());

        println!("✅ 预处理完成，共处理了 {} 个图表", processed_charts.len());

        Self { processed_charts }
    }

    /// 处理销售趋势图表数据
    fn process_sales_chart() -> ProcessedChartData {
        println!("🔄 处理销售趋势图表数据");

        // 原始数据
        let raw_data = vec![
            ("1月", 120.0),
            ("2月", 200.0),
            ("3月", 150.0),
            ("4月", 80.0),
            ("5月", 70.0),
            ("6月", 110.0),
        ];

        // 创建Chart对象
        let mut chart = Chart::new();
        chart.title = Some("月度销售趋势".to_string());
        chart.background_color = Some(Color::WHITE);

        // 转换数据为ECharts格式
        let data_values: Vec<f64> = raw_data.iter().map(|(_, value)| *value).collect();
        
        let series_data = json!({
            "type": "line",
            "name": "销售额",
            "data": data_values,
            "color": "#5470c6"
        });

        chart.series.push(series_data);

        ProcessedChartData {
            title: "销售趋势".to_string(),
            chart,
            chart_type: "line".to_string(),
        }
    }

    /// 处理产品分布饼图数据
    fn process_product_chart() -> ProcessedChartData {
        println!("🔄 处理产品分布饼图数据");

        let mut chart = Chart::new();
        chart.title = Some("产品分布".to_string());
        chart.background_color = Some(Color::WHITE);

        let series_data = json!({
            "type": "pie",
            "name": "产品分布",
            "data": [
                {"name": "产品A", "value": 335},
                {"name": "产品B", "value": 310},
                {"name": "产品C", "value": 234},
                {"name": "产品D", "value": 135},
                {"name": "产品E", "value": 154}
            ]
        });

        chart.series.push(series_data);

        ProcessedChartData {
            title: "产品分布".to_string(),
            chart,
            chart_type: "pie".to_string(),
        }
    }

    /// 处理用户活跃度柱状图数据
    fn process_activity_chart() -> ProcessedChartData {
        println!("🔄 处理用户活跃度柱状图数据");

        let mut chart = Chart::new();
        chart.title = Some("用户活跃度".to_string());
        chart.background_color = Some(Color::WHITE);

        let series_data = json!({
            "type": "bar",
            "name": "活跃用户",
            "data": [320, 302, 301, 334, 390, 330, 320]
        });

        chart.series.push(series_data);

        ProcessedChartData {
            title: "用户活跃度".to_string(),
            chart,
            chart_type: "bar".to_string(),
        }
    }

    /// 处理面积图数据
    fn process_area_chart() -> ProcessedChartData {
        println!("🔄 处理面积图数据");

        let mut chart = Chart::new();
        chart.title = Some("网站流量".to_string());
        chart.background_color = Some(Color::WHITE);

        let series_data = json!({
            "type": "area",
            "name": "访问量",
            "data": [120, 132, 101, 134, 90, 230, 210]
        });

        chart.series.push(series_data);

        ProcessedChartData {
            title: "网站流量".to_string(),
            chart,
            chart_type: "area".to_string(),
        }
    }
}

impl Render for OptimizedDashboard {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        div()
            .flex()
            .flex_col()
            .size_full()
            .bg(rgb(0xf5f5f5))
            .child(
                // 标题栏
                div()
                    .flex()
                    .justify_center()
                    .items_center()
                    .h(px(80.0))
                    .bg(rgb(0x2c3e50))
                    .child(
                        div()
                            .text_xl()
                            .font_weight(FontWeight::BOLD)
                            .text_color(rgb(0xffffff))
                            .child("ECharts 优化仪表板 - 预处理架构"),
                    ),
            )
            .child(
                // 图表网格
                div()
                    .flex()
                    .flex_wrap()
                    .gap_4()
                    .p_4()
                    .children(
                        self.processed_charts
                            .iter()
                            .map(|chart_data| self.render_chart_panel(chart_data))
                            .collect::<Vec<_>>(),
                    ),
            )
    }
}

impl OptimizedDashboard {
    /// 渲染单个图表面板
    fn render_chart_panel(&self, chart_data: &ProcessedChartData) -> impl IntoElement {
        div()
            .flex()
            .flex_col()
            .w(px(300.0))
            .h(px(350.0))
            .bg(rgb(0xffffff))
            .border_1()
            .border_color(rgb(0xe0e0e0))
            .rounded_lg()
            .shadow_sm()
            .child(
                // 图表标题
                div()
                    .flex()
                    .justify_center()
                    .items_center()
                    .h(px(40.0))
                    .bg(rgb(0xf8f9fa))
                    .border_b_1()
                    .border_color(rgb(0xe0e0e0))
                    .child(
                        div()
                            .font_weight(FontWeight::MEDIUM)
                            .text_color(rgb(0x333333))
                            .child(chart_data.title.clone()),
                    ),
            )
            .child(
                // 图表内容区域
                div()
                    .flex_1()
                    .p_2()
                    .child(
                        div()
                            .size_full()
                            .child(format!("图表类型: {}", chart_data.chart_type))
                    ),
            )
    }
}
