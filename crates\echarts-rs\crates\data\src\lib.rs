//! Data processing and analysis utilities for ECharts-rs

use echarts_core::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

pub mod csv_source;
pub mod json_source;
pub mod transform;
pub mod aggregation;
pub mod statistics;

#[cfg(feature = "sql")]
pub mod sql;

#[cfg(feature = "http")]
pub mod http;

/// Data source trait for different data providers
pub trait DataSource {
    /// Load data from the source
    fn load(&self) -> Result<DataSet>;
    
    /// Get metadata about the data source
    fn metadata(&self) -> DataSourceMetadata;
    
    /// Check if the data source is available
    fn is_available(&self) -> bool;
}

/// Metadata about a data source
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataSourceMetadata {
    /// Source type (CSV, JSON, SQL, etc.)
    pub source_type: String,
    /// Source location (file path, URL, etc.)
    pub location: String,
    /// Estimated row count
    pub estimated_rows: Option<usize>,
    /// Column information
    pub columns: Vec<ColumnInfo>,
    /// Last modified timestamp
    pub last_modified: Option<chrono::DateTime<chrono::Utc>>,
}

/// Information about a data column
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ColumnInfo {
    /// Column name
    pub name: String,
    /// Data type
    pub data_type: DataType,
    /// Whether the column can contain null values
    pub nullable: bool,
    /// Sample values
    pub sample_values: Vec<String>,
}

/// Data types supported by the data processing system
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum DataType {
    String,
    Integer,
    Float,
    Boolean,
    DateTime,
    Date,
    Time,
    Json,
    Binary,
}

/// Data processor for transforming and analyzing data
pub struct DataProcessor {
    /// Registered data sources
    sources: HashMap<String, Box<dyn DataSource>>,
    /// Cached datasets
    cache: HashMap<String, DataSet>,
    /// Processing configuration
    config: ProcessorConfig,
}

/// Configuration for data processing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessorConfig {
    /// Maximum cache size in MB
    pub max_cache_size_mb: usize,
    /// Enable automatic type inference
    pub auto_type_inference: bool,
    /// Default date format
    pub default_date_format: String,
    /// Missing value handling strategy
    pub missing_value_strategy: MissingValueStrategy,
}

/// Strategies for handling missing values
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum MissingValueStrategy {
    /// Skip rows with missing values
    Skip,
    /// Fill with default value
    FillDefault,
    /// Fill with previous value
    FillForward,
    /// Fill with next value
    FillBackward,
    /// Fill with interpolated value
    Interpolate,
    /// Fill with mean value (for numeric columns)
    FillMean,
    /// Fill with median value (for numeric columns)
    FillMedian,
}

impl Default for ProcessorConfig {
    fn default() -> Self {
        Self {
            max_cache_size_mb: 100,
            auto_type_inference: true,
            default_date_format: "%Y-%m-%d".to_string(),
            missing_value_strategy: MissingValueStrategy::Skip,
        }
    }
}

impl DataProcessor {
    /// Create a new data processor
    pub fn new() -> Self {
        Self {
            sources: HashMap::new(),
            cache: HashMap::new(),
            config: ProcessorConfig::default(),
        }
    }

    /// Create a data processor with custom configuration
    pub fn with_config(config: ProcessorConfig) -> Self {
        Self {
            sources: HashMap::new(),
            cache: HashMap::new(),
            config,
        }
    }

    /// Register a data source
    pub fn register_source<S: Into<String>>(&mut self, name: S, source: Box<dyn DataSource>) {
        self.sources.insert(name.into(), source);
    }

    /// Load data from a registered source
    pub fn load_data<S: AsRef<str>>(&mut self, source_name: S) -> Result<&DataSet> {
        let name = source_name.as_ref();
        
        // Check cache first
        if let Some(dataset) = self.cache.get(name) {
            return Ok(dataset);
        }

        // Load from source
        if let Some(source) = self.sources.get(name) {
            let dataset = source.load()?;
            self.cache.insert(name.to_string(), dataset);
            Ok(self.cache.get(name).unwrap())
        } else {
            Err(ChartError::Configuration(format!("Data source '{}' not found", name)))
        }
    }

    /// Transform data using a transformation pipeline
    pub fn transform_data(&self, dataset: &DataSet, transformations: &[DataTransformation]) -> Result<DataSet> {
        let mut result = dataset.clone();
        
        for transformation in transformations {
            result = self.apply_transformation(result, transformation)?;
        }
        
        Ok(result)
    }

    /// Apply a single transformation
    fn apply_transformation(&self, dataset: DataSet, transformation: &DataTransformation) -> Result<DataSet> {
        match transformation {
            DataTransformation::Filter { condition } => {
                self.filter_data(dataset, condition)
            }
            DataTransformation::Sort { column, ascending } => {
                self.sort_data(dataset, column, *ascending)
            }
            DataTransformation::GroupBy { columns, aggregations } => {
                self.group_data(dataset, columns, aggregations)
            }
            DataTransformation::Select { columns } => {
                self.select_columns(dataset, columns)
            }
            DataTransformation::Rename { mappings } => {
                self.rename_columns(dataset, mappings)
            }
            DataTransformation::AddColumn { name, expression } => {
                self.add_computed_column(dataset, name, expression)
            }
        }
    }

    /// Filter data based on a condition
    fn filter_data(&self, mut dataset: DataSet, condition: &FilterCondition) -> Result<DataSet> {
        dataset.points.retain(|point| self.evaluate_condition(point, condition));
        Ok(dataset)
    }

    /// Sort data by a column
    fn sort_data(&self, mut dataset: DataSet, column: &str, ascending: bool) -> Result<DataSet> {
        if let Some(column_index) = dataset.dimensions.iter().position(|d| d == column) {
            dataset.points.sort_by(|a, b| {
                let a_val = a.get_number(column_index).unwrap_or(0.0);
                let b_val = b.get_number(column_index).unwrap_or(0.0);
                
                if ascending {
                    a_val.partial_cmp(&b_val).unwrap_or(std::cmp::Ordering::Equal)
                } else {
                    b_val.partial_cmp(&a_val).unwrap_or(std::cmp::Ordering::Equal)
                }
            });
        }
        Ok(dataset)
    }

    /// Group data and apply aggregations
    fn group_data(&self, dataset: DataSet, group_columns: &[String], aggregations: &[Aggregation]) -> Result<DataSet> {
        // Simplified grouping implementation
        let mut groups: HashMap<Vec<String>, Vec<DataPoint>> = HashMap::new();
        
        // Group points by the specified columns
        for point in dataset.points {
            let mut group_key = Vec::new();
            for col in group_columns {
                if let Some(index) = dataset.dimensions.iter().position(|d| d == col) {
                    let value = point.get_string(index).unwrap_or_default();
                    group_key.push(value);
                }
            }
            groups.entry(group_key).or_insert_with(Vec::new).push(point);
        }

        // Apply aggregations to each group
        let mut result_points = Vec::new();
        let mut result_dimensions = group_columns.clone();
        
        // Add aggregation columns to dimensions
        for agg in aggregations {
            result_dimensions.push(format!("{}_{}", agg.function, agg.column));
        }

        for (group_key, group_points) in groups {
            let mut result_values = Vec::new();
            
            // Add group key values
            for key_value in group_key {
                result_values.push(DataValue::String(key_value));
            }
            
            // Add aggregated values
            for agg in aggregations {
                let agg_value = self.calculate_aggregation(&group_points, agg, &dataset.dimensions)?;
                result_values.push(DataValue::Number(agg_value));
            }
            
            result_points.push(DataPoint::new(result_values));
        }

        Ok(DataSet {
            dimensions: result_dimensions,
            points: result_points,
            metadata: dataset.metadata,
        })
    }

    /// Select specific columns
    fn select_columns(&self, dataset: DataSet, columns: &[String]) -> Result<DataSet> {
        let mut selected_indices = Vec::new();
        let mut new_dimensions = Vec::new();
        
        for col in columns {
            if let Some(index) = dataset.dimensions.iter().position(|d| d == col) {
                selected_indices.push(index);
                new_dimensions.push(col.clone());
            }
        }

        let new_points: Vec<DataPoint> = dataset.points
            .into_iter()
            .map(|point| {
                let selected_values: Vec<DataValue> = selected_indices
                    .iter()
                    .map(|&i| point.values.get(i).cloned().unwrap_or(DataValue::Null))
                    .collect();
                DataPoint::new(selected_values)
            })
            .collect();

        Ok(DataSet {
            dimensions: new_dimensions,
            points: new_points,
            metadata: dataset.metadata,
        })
    }

    /// Rename columns
    fn rename_columns(&self, mut dataset: DataSet, mappings: &HashMap<String, String>) -> Result<DataSet> {
        for (old_name, new_name) in mappings {
            if let Some(index) = dataset.dimensions.iter().position(|d| d == old_name) {
                dataset.dimensions[index] = new_name.clone();
            }
        }
        Ok(dataset)
    }

    /// Add a computed column
    fn add_computed_column(&self, mut dataset: DataSet, name: &str, expression: &str) -> Result<DataSet> {
        // Simplified expression evaluation
        // In a real implementation, you would parse and evaluate the expression
        dataset.dimensions.push(name.to_string());
        
        for point in &mut dataset.points {
            // For now, just add a constant value
            let computed_value = DataValue::Number(42.0);
            point.values.push(computed_value);
        }
        
        Ok(dataset)
    }

    /// Evaluate a filter condition
    fn evaluate_condition(&self, point: &DataPoint, condition: &FilterCondition) -> bool {
        match condition {
            FilterCondition::Equals { column, value } => {
                // Simplified condition evaluation
                true
            }
            FilterCondition::GreaterThan { column, value } => {
                // Simplified condition evaluation
                true
            }
            FilterCondition::LessThan { column, value } => {
                // Simplified condition evaluation
                true
            }
            FilterCondition::Contains { column, value } => {
                // Simplified condition evaluation
                true
            }
            FilterCondition::And { left, right } => {
                self.evaluate_condition(point, left) && self.evaluate_condition(point, right)
            }
            FilterCondition::Or { left, right } => {
                self.evaluate_condition(point, left) || self.evaluate_condition(point, right)
            }
            FilterCondition::Not { condition } => {
                !self.evaluate_condition(point, condition)
            }
        }
    }

    /// Calculate aggregation value
    fn calculate_aggregation(&self, points: &[DataPoint], agg: &Aggregation, dimensions: &[String]) -> Result<f64> {
        if let Some(column_index) = dimensions.iter().position(|d| d == &agg.column) {
            let values: Vec<f64> = points
                .iter()
                .filter_map(|p| p.get_number(column_index))
                .collect();

            match agg.function {
                AggregationFunction::Sum => Ok(values.iter().sum()),
                AggregationFunction::Average => {
                    if values.is_empty() {
                        Ok(0.0)
                    } else {
                        Ok(values.iter().sum::<f64>() / values.len() as f64)
                    }
                }
                AggregationFunction::Count => Ok(points.len() as f64),
                AggregationFunction::Min => Ok(values.iter().fold(f64::INFINITY, |a, &b| a.min(b))),
                AggregationFunction::Max => Ok(values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b))),
                AggregationFunction::Median => {
                    let mut sorted_values = values;
                    sorted_values.sort_by(|a, b| a.partial_cmp(b).unwrap());
                    let len = sorted_values.len();
                    if len == 0 {
                        Ok(0.0)
                    } else if len % 2 == 0 {
                        Ok((sorted_values[len / 2 - 1] + sorted_values[len / 2]) / 2.0)
                    } else {
                        Ok(sorted_values[len / 2])
                    }
                }
                AggregationFunction::StandardDeviation => {
                    if values.len() < 2 {
                        Ok(0.0)
                    } else {
                        let mean = values.iter().sum::<f64>() / values.len() as f64;
                        let variance = values.iter()
                            .map(|v| (v - mean).powi(2))
                            .sum::<f64>() / (values.len() - 1) as f64;
                        Ok(variance.sqrt())
                    }
                }
            }
        } else {
            Err(ChartError::Configuration(format!("Column '{}' not found", agg.column)))
        }
    }

    /// Clear the data cache
    pub fn clear_cache(&mut self) {
        self.cache.clear();
    }

    /// Get cache statistics
    pub fn cache_stats(&self) -> CacheStats {
        CacheStats {
            entry_count: self.cache.len(),
            estimated_size_mb: self.cache.len() * 10, // Rough estimation
        }
    }
}

/// Cache statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    pub entry_count: usize,
    pub estimated_size_mb: usize,
}

/// Data transformation operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DataTransformation {
    Filter { condition: FilterCondition },
    Sort { column: String, ascending: bool },
    GroupBy { columns: Vec<String>, aggregations: Vec<Aggregation> },
    Select { columns: Vec<String> },
    Rename { mappings: HashMap<String, String> },
    AddColumn { name: String, expression: String },
}

/// Filter conditions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FilterCondition {
    Equals { column: String, value: DataValue },
    GreaterThan { column: String, value: f64 },
    LessThan { column: String, value: f64 },
    Contains { column: String, value: String },
    And { left: Box<FilterCondition>, right: Box<FilterCondition> },
    Or { left: Box<FilterCondition>, right: Box<FilterCondition> },
    Not { condition: Box<FilterCondition> },
}

/// Aggregation operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Aggregation {
    pub column: String,
    pub function: AggregationFunction,
}

/// Aggregation functions
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AggregationFunction {
    Sum,
    Average,
    Count,
    Min,
    Max,
    Median,
    StandardDeviation,
}

impl Default for DataProcessor {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_data_processor_creation() {
        let processor = DataProcessor::new();
        assert_eq!(processor.cache.len(), 0);
        assert_eq!(processor.sources.len(), 0);
    }

    #[test]
    fn test_data_transformation() {
        let processor = DataProcessor::new();
        
        // Create sample dataset
        let dataset = DataSet {
            dimensions: vec!["name".to_string(), "value".to_string()],
            points: vec![
                DataPoint::new(vec![DataValue::String("A".to_string()), DataValue::Number(10.0)]),
                DataPoint::new(vec![DataValue::String("B".to_string()), DataValue::Number(20.0)]),
            ],
            metadata: std::collections::HashMap::new(),
        };

        // Test column selection
        let transformations = vec![
            DataTransformation::Select { columns: vec!["name".to_string()] }
        ];
        
        let result = processor.transform_data(&dataset, &transformations);
        assert!(result.is_ok());
        
        let result_dataset = result.unwrap();
        assert_eq!(result_dataset.dimensions.len(), 1);
        assert_eq!(result_dataset.dimensions[0], "name");
    }

    #[test]
    fn test_aggregation_calculation() {
        let processor = DataProcessor::new();
        
        let points = vec![
            DataPoint::new(vec![DataValue::Number(10.0)]),
            DataPoint::new(vec![DataValue::Number(20.0)]),
            DataPoint::new(vec![DataValue::Number(30.0)]),
        ];
        
        let dimensions = vec!["value".to_string()];
        
        let agg = Aggregation {
            column: "value".to_string(),
            function: AggregationFunction::Average,
        };
        
        let result = processor.calculate_aggregation(&points, &agg, &dimensions);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 20.0);
    }
}
