# ECharts-rs Makefile for CI/CD tasks

.PHONY: help check format clean build test docs audit examples echarts-test

# Default target
all: check

# Show help
help:
	@echo "ECharts-rs CI/CD Tasks"
	@echo "======================"
	@echo ""
	@echo "Available targets:"
	@echo "  check        - Run full CI checks"
	@echo "  format       - Format code"
	@echo "  clean        - Clean build files"
	@echo "  build        - Build all packages"
	@echo "  test         - Run all tests"
	@echo "  docs         - Generate documentation"
	@echo "  audit        - Run security audit"
	@echo "  examples     - Build examples"
	@echo "  echarts-test - Test ECharts-rs modules"
	@echo ""
	@echo "Usage: make <target>"

# Full CI check
check: format-check clippy build test echarts-test docs examples
	@echo "✅ All CI checks passed!"

# Check code formatting
format-check:
	@echo "🔍 Checking code format..."
	@cargo fmt --all -- --check

# Format code
format:
	@echo "🎨 Formatting code..."
	@cargo fmt --all

# Run Clippy
clippy:
	@echo "📎 Running Clippy..."
	@cargo clippy --all-targets --all-features -- -D warnings

# Clean build files
clean:
	@echo "🧹 Cleaning build files..."
	@cargo clean

# Build all packages
build:
	@echo "🔨 Building all packages..."
	@cargo build --workspace --all-features

# Run all tests
test:
	@echo "🧪 Running tests..."
	@cargo test --workspace --all-features

# Test ECharts-rs modules specifically
echarts-test:
	@echo "📊 Testing ECharts-rs modules..."
	@echo "  - Testing core module..."
	@cargo test --package rust-echarts-core
	@echo "  - Testing charts module..."
	@cargo test --package rust-echarts-charts
	@echo "  - Testing themes module..."
	@cargo test --package rust-echarts-themes
	@echo "  - Testing components module..."
	@cargo test --package rust-echarts-components
	@echo "  - Testing renderer module..."
	@cargo test --package rust-echarts-renderer --features gpui-backend

# Generate documentation
docs:
	@echo "📚 Generating documentation..."
	@cargo doc --all-features --no-deps --document-private-items

# Run security audit
audit:
	@echo "🔒 Running security audit..."
	@cargo audit || echo "⚠️  cargo-audit not installed, run: cargo install cargo-audit"

# Build examples
examples:
	@echo "🎯 Building examples..."
	@cargo build --examples --all-features
	@echo "  - Building GPU acceleration demo..."
	@cargo build --example gpu_acceleration_demo --package rust-echarts-renderer --features gpui-backend

# Install development tools
install-tools:
	@echo "🛠️  Installing development tools..."
	@rustup component add rustfmt clippy
	@cargo install cargo-audit || echo "Failed to install cargo-audit"
	@cargo install cargo-llvm-cov || echo "Failed to install cargo-llvm-cov"

# Generate coverage report
coverage:
	@echo "📈 Generating coverage report..."
	@cargo llvm-cov --all-features --workspace --html || echo "⚠️  cargo-llvm-cov not installed"

# Quick development check (faster than full check)
dev-check: format-check clippy build
	@echo "✅ Quick development check passed!"

# Release preparation
release-prep: check docs
	@echo "🚀 Release preparation complete!"
	@echo "  - All checks passed"
	@echo "  - Documentation generated"
	@echo "  - Ready for release"

# Benchmark (when available)
bench:
	@echo "⚡ Running benchmarks..."
	@echo "📝 Benchmarks will be added in future versions"

# Fix common issues
fix:
	@echo "🔧 Attempting to fix common issues..."
	@cargo fmt --all
	@cargo clippy --all-targets --all-features --fix --allow-dirty || echo "Some issues may need manual fixing"
