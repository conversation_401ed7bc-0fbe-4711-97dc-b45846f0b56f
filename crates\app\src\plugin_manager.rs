// fscdaq/crates/app/src/plugin_manager.rs
use gpui::*;
use std::{path::Path, sync::Mutex};
use tsdaq_protocol::*;

// 插件 trait
pub trait Plugin {
    fn init(&self, cx: &mut App, client: &TSDAQClient) -> Result<(), anyhow::Error>;
    fn name(&self) -> &'static str;
}

// 插件管理器
pub struct PluginManager {
    plugins: Mutex<Vec<Box<dyn Plugin>>>,
}

impl PluginManager {
    pub fn new() -> Self {
        PluginManager {
            plugins: Mutex::new(Vec::new()),
        }
    }

    pub fn load_plugins(&self, cx: &mut App, client: &TSDAQClient) {
        // 加载内置插件
        self.load_builtin_plugins(cx, client);

        // 加载外部插件
        self.load_external_plugins(cx, client);
    }

    fn load_builtin_plugins(&self, cx: &mut App, client: &TSDAQClient) {
        // 示例：加载温度传感器插件
        let temp_plugin = TemperatureSensorPlugin;
        if let Err(e) = temp_plugin.init(cx, client) {
            eprintln!("[ERROR] Failed to initialize temperature plugin: {}", e);
        }

        // 可以添加更多内置插件...
    }

    fn load_external_plugins(&self, _cx: &mut App, _client: &TSDAQClient) {
        // 假设插件都在 plugins 目录下
        let plugins_dir = Path::new("plugins");
        if plugins_dir.exists() && plugins_dir.is_dir() {
            for entry in std::fs::read_dir(plugins_dir).unwrap() {
                let entry = entry.unwrap();
                if entry.path().is_dir() {
                    // 这里可以使用动态链接库或者其他方式加载插件
                    println!("Loading plugin from: {:?}", entry.path());

                    // 示例：假设每个插件目录都有一个lib.rs，实现了Plugin trait
                    // 实际实现中需要使用动态链接库或其他机制
                }
            }
        }
    }
}

// 初始化插件
pub fn init_plugins(cx: &mut App, client: &TSDAQClient) {
    let manager = PluginManager::new();
    manager.load_plugins(cx, client);
}

// 内置温度传感器插件示例
struct TemperatureSensorPlugin;

impl Plugin for TemperatureSensorPlugin {
    fn init(&self, _cx: &mut App, _client: &TSDAQClient) -> Result<(), anyhow::Error> {
        // // 创建温度传感器实例
        // let temp_sensor = Arc::new(TemperatureSensor::new(SensorConfig {
        //     sample_rate: 10, // Hz
        //     ..Default::default()
        // }));

        // // 注册传感器
        // client.register_sensor("temp-1".to_string(), temp_sensor);

        // println!("[INFO] Temperature Sensor Plugin initialized");
        Ok(())
    }

    fn name(&self) -> &'static str {
        "Temperature Sensor Plugin"
    }
}
