//! 矩形树图实现
//!
//! 提供层次数据的矩形树图可视化功能

use echarts_core::{
    Color, DrawCommand, Point, Series, CoordinateSystem, SeriesType, Bounds, Size, DataSet,
    draw_commands::{RectStyle},
    TextStyle, LineStyle, FontWeight, FontStyle, TextAlign, TextBaseline, LineCap, LineJoin,
    Result
};
use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};

/// 矩形树图数据项
#[derive(Debug, Clone)]
pub struct TreemapDataItem {
    /// 数据名称
    pub name: String,
    /// 数值（用于计算面积）
    pub value: f64,
    /// 子项
    pub children: Vec<TreemapDataItem>,
    /// 颜色
    pub color: Option<Color>,
    /// 标签
    pub label: Option<String>,
    /// 是否可见
    pub visible: bool,
}

impl TreemapDataItem {
    /// 创建新的矩形树图数据项
    pub fn new(name: impl Into<String>, value: f64) -> Self {
        Self {
            name: name.into(),
            value,
            children: Vec::new(),
            color: None,
            label: None,
            visible: true,
        }
    }

    /// 设置颜色
    pub fn color(mut self, color: Color) -> Self {
        self.color = Some(color);
        self
    }

    /// 设置标签
    pub fn label(mut self, label: impl Into<String>) -> Self {
        self.label = Some(label.into());
        self
    }

    /// 添加子项
    pub fn add_child(mut self, child: TreemapDataItem) -> Self {
        self.children.push(child);
        self
    }

    /// 设置子项
    pub fn children(mut self, children: Vec<TreemapDataItem>) -> Self {
        self.children = children;
        self
    }

    /// 设置可见性
    pub fn visible(mut self, visible: bool) -> Self {
        self.visible = visible;
        self
    }

    /// 获取总值（包括子项）
    pub fn total_value(&self) -> f64 {
        if self.children.is_empty() {
            self.value
        } else {
            self.children.iter().map(|child| child.total_value()).sum()
        }
    }

    /// 是否为叶子节点
    pub fn is_leaf(&self) -> bool {
        self.children.is_empty()
    }
}

/// 矩形树图布局算法
#[derive(Debug, Clone, Copy)]
pub enum TreemapAlgorithm {
    /// 二分法布局
    Binary,
    /// 条带布局
    Strip,
    /// 方形化布局（默认）
    Squarify,
}

impl Default for TreemapAlgorithm {
    fn default() -> Self {
        TreemapAlgorithm::Squarify
    }
}

/// 矩形树图标签配置
#[derive(Debug, Clone)]
pub struct TreemapLabel {
    /// 是否显示标签
    pub show: bool,
    /// 字体大小
    pub font_size: f64,
    /// 标签颜色
    pub color: Color,
    /// 标签位置
    pub position: TreemapLabelPosition,
    /// 最小显示面积
    pub min_area: f64,
}

impl Default for TreemapLabel {
    fn default() -> Self {
        Self {
            show: true,
            font_size: 12.0,
            color: Color::rgb(0.2, 0.2, 0.2),
            position: TreemapLabelPosition::Center,
            min_area: 100.0,
        }
    }
}

/// 标签位置
#[derive(Debug, Clone, Copy)]
pub enum TreemapLabelPosition {
    /// 中心
    Center,
    /// 左上角
    TopLeft,
    /// 右上角
    TopRight,
    /// 左下角
    BottomLeft,
    /// 右下角
    BottomRight,
}

/// 矩形节点（用于布局计算）
#[derive(Debug, Clone)]
struct TreemapNode {
    /// 数据项引用
    item: TreemapDataItem,
    /// 矩形区域
    bounds: Bounds,
    /// 层级
    level: usize,
    /// 父节点索引
    parent: Option<usize>,
}

/// 矩形树图系列
#[derive(Debug, Clone)]
pub struct TreemapSeries {
    /// 基础配置
    config: ChartConfig,
    /// 数据项
    data: Vec<TreemapDataItem>,
    /// 布局算法
    algorithm: TreemapAlgorithm,
    /// 标签配置
    label: TreemapLabel,
    /// 边框宽度
    border_width: f64,
    /// 边框颜色
    border_color: Color,
    /// 层级颜色
    level_colors: Vec<Color>,
    /// 最大显示层级
    max_depth: usize,
    /// 间隙大小
    gap: f64,
}

impl TreemapSeries {
    /// 创建新的矩形树图系列
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            config: ChartConfig {
                name: name.into(),
                ..ChartConfig::default()
            },
            data: Vec::new(),
            algorithm: TreemapAlgorithm::default(),
            label: TreemapLabel::default(),
            border_width: 1.0,
            border_color: Color::rgb(0.8, 0.8, 0.8),
            level_colors: vec![
                Color::rgb(0.3, 0.6, 1.0),
                Color::rgb(0.6, 0.8, 0.4),
                Color::rgb(1.0, 0.6, 0.3),
                Color::rgb(0.8, 0.4, 0.8),
                Color::rgb(0.4, 0.8, 0.8),
            ],
            max_depth: 3,
            gap: 1.0,
        }
    }

    /// 设置数据
    pub fn data(mut self, data: Vec<TreemapDataItem>) -> Self {
        self.data = data;
        self
    }

    /// 添加数据项
    pub fn add_data(mut self, item: TreemapDataItem) -> Self {
        self.data.push(item);
        self
    }

    /// 设置布局算法
    pub fn algorithm(mut self, algorithm: TreemapAlgorithm) -> Self {
        self.algorithm = algorithm;
        self
    }

    /// 设置标签配置
    pub fn label(mut self, label: TreemapLabel) -> Self {
        self.label = label;
        self
    }

    /// 设置边框样式
    pub fn border(mut self, width: f64, color: Color) -> Self {
        self.border_width = width;
        self.border_color = color;
        self
    }

    /// 设置层级颜色
    pub fn level_colors(mut self, colors: Vec<Color>) -> Self {
        self.level_colors = colors;
        self
    }

    /// 设置最大显示层级
    pub fn max_depth(mut self, depth: usize) -> Self {
        self.max_depth = depth;
        self
    }

    /// 设置间隙大小
    pub fn gap(mut self, gap: f64) -> Self {
        self.gap = gap.max(0.0);
        self
    }

    /// 获取系列名称
    pub fn name(&self) -> &str {
        &self.config.name
    }

    /// 计算矩形树图布局
    fn calculate_layout(&self, bounds: Bounds) -> Vec<TreemapNode> {
        let mut nodes = Vec::new();
        
        if self.data.is_empty() {
            return nodes;
        }

        // 计算总值
        let total_value: f64 = self.data.iter().map(|item| item.total_value()).sum();
        if total_value <= 0.0 {
            return nodes;
        }

        // 为根级数据项创建节点
        let mut current_bounds = bounds;
        current_bounds.origin.x += self.gap;
        current_bounds.origin.y += self.gap;
        current_bounds.size.width -= 2.0 * self.gap;
        current_bounds.size.height -= 2.0 * self.gap;

        self.layout_items(&self.data, current_bounds, 0, None, &mut nodes);
        nodes
    }

    /// 递归布局数据项（非递归实现）
    fn layout_items(
        &self,
        items: &[TreemapDataItem],
        bounds: Bounds,
        level: usize,
        parent: Option<usize>,
        nodes: &mut Vec<TreemapNode>,
    ) {
        if items.is_empty() || level >= self.max_depth {
            return;
        }

        let total_value: f64 = items.iter().map(|item| item.total_value()).sum();
        if total_value <= 0.0 {
            return;
        }

        // 使用简化的条带布局避免递归
        self.simple_strip_layout(items, bounds, level, parent, nodes);
    }

    /// 方形化布局算法
    fn squarify_layout(
        &self,
        items: &[TreemapDataItem],
        bounds: Bounds,
        level: usize,
        parent: Option<usize>,
        nodes: &mut Vec<TreemapNode>,
    ) {
        // 简化的方形化算法实现
        let total_value: f64 = items.iter().map(|item| item.total_value()).sum();
        let area = bounds.size.width * bounds.size.height;
        
        let mut current_y = bounds.origin.y;
        let mut remaining_height = bounds.size.height;
        
        for item in items {
            if !item.visible {
                continue;
            }
            
            let ratio = item.total_value() / total_value;
            let item_area = area * ratio;
            let item_height = (item_area / bounds.size.width).min(remaining_height);
            
            let item_bounds = Bounds {
                origin: Point {
                    x: bounds.origin.x,
                    y: current_y,
                },
                size: Size {
                    width: bounds.size.width,
                    height: item_height,
                },
            };
            
            let node_index = nodes.len();
            nodes.push(TreemapNode {
                item: item.clone(),
                bounds: item_bounds,
                level,
                parent,
            });
            
            // 递归处理子项
            if !item.children.is_empty() && level + 1 < self.max_depth {
                let child_bounds = Bounds {
                    origin: Point {
                        x: item_bounds.origin.x + self.gap,
                        y: item_bounds.origin.y + self.gap,
                    },
                    size: Size {
                        width: item_bounds.size.width - 2.0 * self.gap,
                        height: item_bounds.size.height - 2.0 * self.gap,
                    },
                };
                self.layout_items(&item.children, child_bounds, level + 1, Some(node_index), nodes);
            }
            
            current_y += item_height;
            remaining_height -= item_height;
            
            if remaining_height <= 0.0 {
                break;
            }
        }
    }

    /// 二分法布局算法
    fn binary_layout(
        &self,
        items: &[TreemapDataItem],
        bounds: Bounds,
        level: usize,
        parent: Option<usize>,
        nodes: &mut Vec<TreemapNode>,
    ) {
        if items.len() <= 1 {
            if let Some(item) = items.first() {
                if item.visible {
                    let node_index = nodes.len();
                    nodes.push(TreemapNode {
                        item: item.clone(),
                        bounds,
                        level,
                        parent,
                    });
                    
                    if !item.children.is_empty() && level + 1 < self.max_depth {
                        let child_bounds = Bounds {
                            origin: Point {
                                x: bounds.origin.x + self.gap,
                                y: bounds.origin.y + self.gap,
                            },
                            size: Size {
                                width: bounds.size.width - 2.0 * self.gap,
                                height: bounds.size.height - 2.0 * self.gap,
                            },
                        };
                        self.layout_items(&item.children, child_bounds, level + 1, Some(node_index), nodes);
                    }
                }
            }
            return;
        }

        // 计算分割点
        let total_value: f64 = items.iter().map(|item| item.total_value()).sum();
        let mut left_value = 0.0;
        let mut split_index = 0;
        
        for (i, item) in items.iter().enumerate() {
            left_value += item.total_value();
            if left_value >= total_value / 2.0 {
                split_index = i + 1;
                break;
            }
        }
        
        let left_items = &items[..split_index];
        let right_items = &items[split_index..];
        
        // 决定分割方向（水平或垂直）
        let horizontal_split = bounds.size.width > bounds.size.height;
        
        if horizontal_split {
            // 垂直分割
            let left_width = bounds.size.width * (left_value / total_value);
            
            let left_bounds = Bounds {
                origin: bounds.origin,
                size: Size {
                    width: left_width,
                    height: bounds.size.height,
                },
            };
            
            let right_bounds = Bounds {
                origin: Point {
                    x: bounds.origin.x + left_width,
                    y: bounds.origin.y,
                },
                size: Size {
                    width: bounds.size.width - left_width,
                    height: bounds.size.height,
                },
            };
            
            self.layout_items(left_items, left_bounds, level, parent, nodes);
            self.layout_items(right_items, right_bounds, level, parent, nodes);
        } else {
            // 水平分割
            let left_height = bounds.size.height * (left_value / total_value);
            
            let left_bounds = Bounds {
                origin: bounds.origin,
                size: Size {
                    width: bounds.size.width,
                    height: left_height,
                },
            };
            
            let right_bounds = Bounds {
                origin: Point {
                    x: bounds.origin.x,
                    y: bounds.origin.y + left_height,
                },
                size: Size {
                    width: bounds.size.width,
                    height: bounds.size.height - left_height,
                },
            };
            
            self.layout_items(left_items, left_bounds, level, parent, nodes);
            self.layout_items(right_items, right_bounds, level, parent, nodes);
        }
    }

    /// 条带布局算法
    fn strip_layout(
        &self,
        items: &[TreemapDataItem],
        bounds: Bounds,
        level: usize,
        parent: Option<usize>,
        nodes: &mut Vec<TreemapNode>,
    ) {
        // 简化的条带布局实现
        let total_value: f64 = items.iter().map(|item| item.total_value()).sum();
        let area = bounds.size.width * bounds.size.height;
        
        let mut current_x = bounds.origin.x;
        let mut remaining_width = bounds.size.width;
        
        for item in items {
            if !item.visible {
                continue;
            }
            
            let ratio = item.total_value() / total_value;
            let item_area = area * ratio;
            let item_width = (item_area / bounds.size.height).min(remaining_width);
            
            let item_bounds = Bounds {
                origin: Point {
                    x: current_x,
                    y: bounds.origin.y,
                },
                size: Size {
                    width: item_width,
                    height: bounds.size.height,
                },
            };
            
            let node_index = nodes.len();
            nodes.push(TreemapNode {
                item: item.clone(),
                bounds: item_bounds,
                level,
                parent,
            });
            
            // 递归处理子项
            if !item.children.is_empty() && level + 1 < self.max_depth {
                let child_bounds = Bounds {
                    origin: Point {
                        x: item_bounds.origin.x + self.gap,
                        y: item_bounds.origin.y + self.gap,
                    },
                    size: Size {
                        width: item_bounds.size.width - 2.0 * self.gap,
                        height: item_bounds.size.height - 2.0 * self.gap,
                    },
                };
                self.layout_items(&item.children, child_bounds, level + 1, Some(node_index), nodes);
            }
            
            current_x += item_width;
            remaining_width -= item_width;
            
            if remaining_width <= 0.0 {
                break;
            }
        }
    }

    /// 生成矩形和标签
    fn generate_rectangles_and_labels(&self, nodes: &[TreemapNode]) -> Vec<DrawCommand> {
        let mut commands = Vec::new();

        for node in nodes {
            // 只渲染叶子节点或指定层级的节点
            if node.item.is_leaf() || node.level == 0 {
                // 获取颜色
                let color = node.item.color.unwrap_or_else(|| {
                    self.level_colors[node.level % self.level_colors.len()]
                });

                // 绘制矩形
                commands.push(DrawCommand::Rect {
                    bounds: node.bounds,
                    style: RectStyle {
                        fill: Some(color),
                        stroke: Some(LineStyle {
                            color: self.border_color,
                            width: self.border_width,
                            dash_pattern: None,
                            cap: LineCap::Round,
                            join: LineJoin::Round,
                            opacity: 1.0,
                        }),
                        opacity: 0.8,
                        corner_radius: 0.0,
                    },
                });

                // 绘制标签
                if self.label.show {
                    let area = node.bounds.size.width * node.bounds.size.height;
                    if area >= self.label.min_area {
                        let label_text = node.item.label.as_ref().unwrap_or(&node.item.name);
                        let label_position = self.calculate_label_position(&node.bounds);

                        commands.push(DrawCommand::Text {
                            text: label_text.clone(),
                            position: label_position,
                            style: TextStyle {
                                font_family: "Arial".to_string(),
                                font_size: self.label.font_size,
                                font_weight: FontWeight::Normal,
                                font_style: FontStyle::Normal,
                                color: self.label.color,
                                opacity: 1.0,
                                align: TextAlign::Center,
                                baseline: TextBaseline::Middle,
                                rotation: 0.0,
                                letter_spacing: 0.0,
                                line_height: 1.2,
                            },
                        });
                    }
                }
            }
        }

        commands
    }

    /// 计算标签位置
    fn calculate_label_position(&self, bounds: &Bounds) -> Point {
        match self.label.position {
            TreemapLabelPosition::Center => Point {
                x: bounds.origin.x + bounds.size.width / 2.0,
                y: bounds.origin.y + bounds.size.height / 2.0,
            },
            TreemapLabelPosition::TopLeft => Point {
                x: bounds.origin.x + 5.0,
                y: bounds.origin.y + 15.0,
            },
            TreemapLabelPosition::TopRight => Point {
                x: bounds.origin.x + bounds.size.width - 5.0,
                y: bounds.origin.y + 15.0,
            },
            TreemapLabelPosition::BottomLeft => Point {
                x: bounds.origin.x + 5.0,
                y: bounds.origin.y + bounds.size.height - 5.0,
            },
            TreemapLabelPosition::BottomRight => Point {
                x: bounds.origin.x + bounds.size.width - 5.0,
                y: bounds.origin.y + bounds.size.height - 5.0,
            },
        }
    }

    /// 简化的条带布局算法（非递归）
    fn simple_strip_layout(
        &self,
        items: &[TreemapDataItem],
        bounds: Bounds,
        level: usize,
        parent: Option<usize>,
        nodes: &mut Vec<TreemapNode>,
    ) {
        let total_value: f64 = items.iter().map(|item| item.total_value()).sum();
        let area = bounds.size.width * bounds.size.height;

        let mut current_x = bounds.origin.x;
        let mut remaining_width = bounds.size.width;

        for item in items {
            if !item.visible || remaining_width <= 0.0 {
                continue;
            }

            let ratio = item.total_value() / total_value;
            let item_area = area * ratio;
            let item_width = (item_area / bounds.size.height).min(remaining_width);

            let item_bounds = Bounds {
                origin: Point {
                    x: current_x,
                    y: bounds.origin.y,
                },
                size: Size {
                    width: item_width,
                    height: bounds.size.height,
                },
            };

            nodes.push(TreemapNode {
                item: item.clone(),
                bounds: item_bounds,
                level,
                parent,
            });

            current_x += item_width;
            remaining_width -= item_width;
        }
    }
}

impl Series for TreemapSeries {
    fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        if self.data.is_empty() {
            return Ok(commands);
        }

        // 获取绘图区域
        let bounds = coord_system.bounds();

        // 计算布局
        let nodes = self.calculate_layout(bounds);

        // 生成矩形和标签
        commands.extend(self.generate_rectangles_and_labels(&nodes));

        Ok(commands)
    }

    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Tree
    }

    fn bounds(&self) -> Option<Bounds> {
        // 矩形树图使用整个可用空间
        None
    }

    fn is_visible(&self) -> bool {
        true
    }

    fn z_index(&self) -> i32 {
        0
    }

    fn clone_series(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }
}

#[cfg(test)]
mod tests {
use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};
    use super::*;
    use echarts_core::{CartesianCoordinateSystem, Point, Size};

    #[test]
    fn test_treemap_data_item() {
        let item = TreemapDataItem::new("根节点", 100.0)
            .color(Color::rgb(1.0, 0.0, 0.0))
            .label("自定义标签")
            .add_child(TreemapDataItem::new("子节点1", 30.0))
            .add_child(TreemapDataItem::new("子节点2", 70.0));

        assert_eq!(item.name, "根节点");
        assert_eq!(item.value, 100.0);
        assert_eq!(item.total_value(), 100.0); // 子节点值之和
        assert_eq!(item.children.len(), 2);
        assert!(!item.is_leaf());
        assert_eq!(item.color, Some(Color::rgb(1.0, 0.0, 0.0)));
        assert_eq!(item.label, Some("自定义标签".to_string()));
    }

    #[test]
    fn test_treemap_data_item_leaf() {
        let item = TreemapDataItem::new("叶子节点", 50.0);

        assert_eq!(item.total_value(), 50.0);
        assert!(item.is_leaf());
        assert!(item.children.is_empty());
    }

    #[test]
    fn test_treemap_series_creation() {
        let series = TreemapSeries::new("测试矩形树图")
            .algorithm(TreemapAlgorithm::Binary)
            .max_depth(2)
            .gap(2.0);

        assert_eq!(series.name(), "测试矩形树图");
        assert!(matches!(series.algorithm, TreemapAlgorithm::Binary));
        assert_eq!(series.max_depth, 2);
        assert_eq!(series.gap, 2.0);
    }

    #[test]
    fn test_treemap_label_config() {
        let label = TreemapLabel {
            show: true,
            font_size: 14.0,
            color: Color::rgb(0.1, 0.1, 0.1),
            position: TreemapLabelPosition::TopLeft,
            min_area: 200.0,
        };

        assert!(label.show);
        assert_eq!(label.font_size, 14.0);
        assert_eq!(label.color, Color::rgb(0.1, 0.1, 0.1));
        assert!(matches!(label.position, TreemapLabelPosition::TopLeft));
        assert_eq!(label.min_area, 200.0);
    }

    #[test]
    fn test_treemap_series_with_data() {
        let data = vec![
            TreemapDataItem::new("分类A", 0.0)
                .add_child(TreemapDataItem::new("A1", 20.0))
                .add_child(TreemapDataItem::new("A2", 30.0)),
            TreemapDataItem::new("分类B", 0.0)
                .add_child(TreemapDataItem::new("B1", 25.0))
                .add_child(TreemapDataItem::new("B2", 25.0)),
        ];

        let series = TreemapSeries::new("层次数据")
            .data(data)
            .algorithm(TreemapAlgorithm::Squarify);

        assert_eq!(series.data.len(), 2);
        assert_eq!(series.data[0].children.len(), 2);
        assert_eq!(series.data[1].children.len(), 2);
        assert!(matches!(series.algorithm, TreemapAlgorithm::Squarify));
    }

    #[test]
    fn test_treemap_algorithms() {
        let algorithms = [
            TreemapAlgorithm::Binary,
            TreemapAlgorithm::Strip,
            TreemapAlgorithm::Squarify,
        ];

        for algorithm in &algorithms {
            let series = TreemapSeries::new("测试").algorithm(*algorithm);
            assert!(matches!(series.algorithm, algorithm));
        }
    }

    #[test]
    fn test_treemap_series_rendering() {
        let data = vec![
            TreemapDataItem::new("项目1", 40.0),
            TreemapDataItem::new("项目2", 30.0),
            TreemapDataItem::new("项目3", 30.0),
        ];

        let series = TreemapSeries::new("项目分布")
            .data(data)
            .gap(1.0);

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 400.0, height: 300.0 },
            },
            (0.0, 100.0),
            (0.0, 100.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();

        // 应该生成矩形和标签命令
        assert!(!commands.is_empty());

        // 检查命令类型
        let mut rect_count = 0;
        let mut text_count = 0;

        for cmd in &commands {
            match cmd {
                DrawCommand::Rect { .. } => rect_count += 1,
                DrawCommand::Text { .. } => text_count += 1,
                _ => {}
            }
        }

        assert!(rect_count > 0); // 矩形
        assert!(text_count > 0); // 标签
    }

    #[test]
    fn test_treemap_series_empty_data() {
        let series = TreemapSeries::new("空矩形树图");

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 400.0, height: 300.0 },
            },
            (0.0, 100.0),
            (0.0, 100.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();
        assert!(commands.is_empty());
    }

    #[test]
    fn test_label_position_calculation() {
        let series = TreemapSeries::new("测试");
        let bounds = Bounds {
            origin: Point { x: 10.0, y: 20.0 },
            size: Size { width: 100.0, height: 80.0 },
        };

        let center_pos = series.calculate_label_position(&bounds);
        assert_eq!(center_pos.x, 60.0); // 10 + 100/2
        assert_eq!(center_pos.y, 60.0); // 20 + 80/2
    }
}
// ============================================================================
// 图表基础架构集成 - ChartBase 实现
// ============================================================================

/// 为 TreemapSeries 实现 ChartBase trait
impl ChartBase for TreemapSeries {
    type DataType = Vec<TreemapDataItem>;

    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn raw_data(&self) -> &Self::DataType {
        &self.data
    }

    fn to_dataset(&self) -> DataSet {
        // TODO: 实现 Vec<TreemapDataItem> 到 DataSet 的转换
        DataSet::new()
    }
    
    fn visible(&self) -> bool {
        self.config.visible
    }
    
    fn z_index(&self) -> i32 {
        self.config.z_index
    }
    
    fn bounds(&self) -> Option<Bounds> {
        // TODO: 为 TreemapSeries 实现边界计算
        None
    }
    
    fn config(&self) -> &ChartConfig {
        &self.config
    }
    
    fn config_mut(&mut self) -> &mut ChartConfig {
        &mut self.config
    }
}

/// 为 TreemapSeries 实现 ChartSeries trait
impl ChartSeries for TreemapSeries {
    // 所有方法都由默认实现提供，基于 ChartBase
}
