# ECharts-rs 交互系统分层架构

## 🏗️ **架构概览**

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
│                     用户交互处理                              │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 上层：统一交互管理器                          │
│              (interaction/lib.rs)                           │
│  • InteractionManager - 统一事件分发                        │
│  • 事件路由和协调                                           │
│  • 全局交互状态管理                                         │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   适配层 (Adapter Layer)                    │
│                 事件转换和协调                               │
│  • 通用事件 ↔ 图表特定事件                                  │
│  • 事件优先级处理                                           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                中层：图表特定交互                            │
│           (line.rs, bar.rs, pie.rs等)                      │
│  • 图表级别的交互逻辑                                       │
│  • 数据点查找和选择                                         │
│  • 图表特定的事件类型                                       │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│              底层：通用交互事件系统                          │
│           (professional_interactions.rs)                    │
│  • 基础交互事件定义                                         │
│  • 通用交互控制器                                           │
│  • 缩放、平移、选择等通用功能                               │
└─────────────────────────────────────────────────────────────┘
```

## 📋 **各层职责**

### 🔧 **底层：通用交互事件系统**
**文件**: `professional_interactions.rs`

**职责**:
- 定义基础交互事件类型 (鼠标、键盘、触摸、手势)
- 提供通用交互控制器 (缩放、平移、选择)
- 处理底层输入事件
- 管理视口和变换

**核心类型**:
```rust
pub enum BaseInteractionEvent {
    MouseMove { position: Point, modifiers: KeyModifiers },
    MouseClick { position: Point, button: MouseButton, modifiers: KeyModifiers },
    MouseWheel { position: Point, delta: f64, modifiers: KeyModifiers },
    // ...
}

pub trait InteractionController {
    fn handle_event(&mut self, event: BaseInteractionEvent) -> InteractionResponse;
}
```

### 📊 **中层：图表特定交互**
**文件**: `line.rs`, `bar.rs`, `pie.rs` 等

**职责**:
- 实现图表特定的交互逻辑
- 数据点查找和高亮
- 图表级别的事件处理
- 工具提示内容生成

**核心类型**:
```rust
pub enum ChartInteractionEvent {
    PointHover { series_index: usize, point_index: usize, position: Point },
    PointClick { series_index: usize, point_index: usize, position: Point },
    SeriesHover { series_index: usize },
    // ...
}

pub trait ChartInteraction {
    fn handle_chart_event(&mut self, event: ChartInteractionEvent) -> ChartInteractionResult;
    fn find_nearest_point(&self, position: Point) -> Option<(usize, usize)>;
}
```

### 🎯 **适配层：事件转换和协调**
**文件**: `interaction_adapter.rs`

**职责**:
- 将底层通用事件转换为图表特定事件
- 处理事件优先级和冲突
- 协调多个交互控制器
- 事件过滤和预处理

**核心类型**:
```rust
pub struct InteractionAdapter {
    chart_controllers: Vec<Box<dyn ChartInteraction>>,
    base_controllers: Vec<Box<dyn InteractionController>>,
}

impl InteractionAdapter {
    pub fn convert_event(&self, base_event: BaseInteractionEvent) 
        -> Vec<ChartInteractionEvent>;
}
```

### 🎮 **上层：统一交互管理器**
**文件**: `interaction/lib.rs`

**职责**:
- 统一的交互事件分发
- 全局交互状态管理
- 交互策略协调
- 与渲染系统集成

**核心类型**:
```rust
pub struct InteractionManager {
    adapter: InteractionAdapter,
    global_state: GlobalInteractionState,
    config: InteractionConfig,
}

impl InteractionManager {
    pub fn handle_input(&mut self, input: InputEvent) -> Vec<InteractionResult>;
}
```

## 🔄 **事件流程**

### 输入事件处理流程
```
用户输入 → 底层事件 → 适配转换 → 图表事件 → 交互结果 → 渲染更新
    ↓           ↓           ↓           ↓           ↓           ↓
InputEvent → BaseEvent → Adapter → ChartEvent → Result → Render
```

### 具体示例：鼠标悬停
```
1. 用户移动鼠标
2. 底层捕获 MouseMove 事件
3. 适配器转换为 PointHover 事件
4. 折线图处理 PointHover，查找最近点
5. 返回高亮结果和工具提示
6. 管理器协调渲染更新
```

## 🎯 **重构优势**

### ✅ **清晰的职责分离**
- 每层专注于特定的交互级别
- 避免代码重复和混乱
- 易于维护和扩展

### ✅ **灵活的扩展性**
- 新图表类型只需实现中层接口
- 新交互功能可在任意层添加
- 支持复杂的交互组合

### ✅ **强类型安全**
- 每层都有明确的类型定义
- 编译时检查交互逻辑
- 减少运行时错误

### ✅ **高性能**
- 事件过滤和优先级处理
- 避免不必要的计算
- 支持大数据集交互

## 🚀 **实施计划**

### Phase 1: 重构底层 ✅
- 完善 professional_interactions.rs
- 定义基础事件类型
- 实现通用控制器

### Phase 2: 创建适配层 🔄
- 实现 InteractionAdapter
- 事件转换逻辑
- 优先级处理

### Phase 3: 重构中层 📊
- 更新图表交互接口
- 统一事件类型
- 优化性能

### Phase 4: 统一上层 🎮
- 重构 InteractionManager
- 全局状态管理
- 集成测试

## 📝 **迁移指南**

### 现有代码迁移
1. **保持向后兼容**: 现有API继续工作
2. **渐进式迁移**: 逐步采用新架构
3. **性能优化**: 利用新架构的性能优势

### 新功能开发
1. **遵循分层原则**: 在正确的层实现功能
2. **使用标准接口**: 实现定义的trait
3. **考虑扩展性**: 设计可复用的组件

---

**这个分层架构将使 ECharts-rs 的交互系统更加专业、高效和可维护！** 🎉
