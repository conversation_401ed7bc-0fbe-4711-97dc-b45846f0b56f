//! 最终的曲线图展示
//!
//! 生成各种类型的完整曲线图，展现 ECharts-rs 的曲线图功能

use std::fs;

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("📈 最终的曲线图展示生成器");
    println!("{}", "=".repeat(50));

    // 确保输出目录存在
    let output_dir = "temp/svg/final_line_charts";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 基础曲线图
    println!("\n📊 1. 生成基础曲线图...");
    generate_basic_line_charts(output_dir)?;

    // 2. 数学函数曲线
    println!("\n🔢 2. 生成数学函数曲线...");
    generate_math_function_charts(output_dir)?;

    // 3. 数据分析曲线
    println!("\n📈 3. 生成数据分析曲线...");
    generate_data_analysis_charts(output_dir)?;

    // 4. 样式变化曲线
    println!("\n🎨 4. 生成样式变化曲线...");
    generate_styled_line_charts(output_dir)?;

    // 5. 复杂场景曲线
    println!("\n🔧 5. 生成复杂场景曲线...");
    generate_complex_line_charts(output_dir)?;

    // 6. 动态效果曲线
    println!("\n🎬 6. 生成动态效果曲线...");
    generate_animated_line_charts(output_dir)?;

    // 7. 生成展示页面
    println!("\n📄 7. 生成展示页面...");
    generate_line_charts_showcase(output_dir)?;

    println!("\n🎉 最终的曲线图展示生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/line_charts_showcase.html 查看所有曲线图", output_dir);

    Ok(())
}

/// 生成基础曲线图
fn generate_basic_line_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 线性增长
    let linear_data: Vec<(f64, f64)> = (0..=20).map(|i| (i as f64, i as f64 * 2.5 + 10.0)).collect();
    let linear_svg = create_line_chart("线性增长曲线", &linear_data, 600.0, 400.0, "007bff");
    fs::write(format!("{}/01_linear_growth.svg", output_dir), linear_svg)?;

    // 2. 指数增长
    let exponential_data: Vec<(f64, f64)> = (0..=15).map(|i| (i as f64, (1.2_f64).powf(i as f64))).collect();
    let exponential_svg = create_line_chart("指数增长曲线", &exponential_data, 600.0, 400.0, "28a745");
    fs::write(format!("{}/02_exponential_growth.svg", output_dir), exponential_svg)?;

    // 3. 对数增长
    let logarithmic_data: Vec<(f64, f64)> = (1..=50).map(|i| (i as f64, (i as f64).ln() * 20.0 + 50.0)).collect();
    let logarithmic_svg = create_line_chart("对数增长曲线", &logarithmic_data, 600.0, 400.0, "dc3545");
    fs::write(format!("{}/03_logarithmic_growth.svg", output_dir), logarithmic_svg)?;

    // 4. 波动曲线
    let volatile_data: Vec<(f64, f64)> = (0..=100).map(|i| {
        let x = i as f64 * 0.2;
        let y = 100.0 + (x * 0.5).sin() * 30.0 + (x * 0.1).cos() * 15.0 + (i as f64 * 0.01).sin() * 5.0;
        (x, y)
    }).collect();
    let volatile_svg = create_line_chart("波动曲线", &volatile_data, 700.0, 400.0, "ffc107");
    fs::write(format!("{}/04_volatile_curve.svg", output_dir), volatile_svg)?;

    println!("  ✅ 基础曲线图生成完成 (4个图表)");
    Ok(())
}

/// 生成数学函数曲线
fn generate_math_function_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 正弦函数
    let sine_data: Vec<(f64, f64)> = (0..=360).step_by(5).map(|i| {
        let x = i as f64;
        let y = (x * std::f64::consts::PI / 180.0).sin() * 100.0 + 150.0;
        (x, y)
    }).collect();
    let sine_svg = create_line_chart("正弦函数曲线", &sine_data, 800.0, 400.0, "6f42c1");
    fs::write(format!("{}/05_sine_function.svg", output_dir), sine_svg)?;

    // 2. 余弦函数
    let cosine_data: Vec<(f64, f64)> = (0..=360).step_by(5).map(|i| {
        let x = i as f64;
        let y = (x * std::f64::consts::PI / 180.0).cos() * 100.0 + 150.0;
        (x, y)
    }).collect();
    let cosine_svg = create_line_chart("余弦函数曲线", &cosine_data, 800.0, 400.0, "e83e8c");
    fs::write(format!("{}/06_cosine_function.svg", output_dir), cosine_svg)?;

    // 3. 二次函数
    let quadratic_data: Vec<(f64, f64)> = (-20..=20).map(|i| {
        let x = i as f64;
        let y = x * x * 0.5 + x * 2.0 + 50.0;
        (x, y)
    }).collect();
    let quadratic_svg = create_line_chart("二次函数曲线", &quadratic_data, 600.0, 400.0, "17a2b8");
    fs::write(format!("{}/07_quadratic_function.svg", output_dir), quadratic_svg)?;

    println!("  ✅ 数学函数曲线生成完成 (3个图表)");
    Ok(())
}

/// 生成数据分析曲线
fn generate_data_analysis_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 股票价格走势
    let mut stock_price = 100.0;
    let stock_data: Vec<(f64, f64)> = (0..=100).map(|i| {
        let change = (i as f64 * 0.1).sin() * 2.0 + (i as f64 * 0.05).cos() * 1.5 + 
                    ((i % 10) as f64 - 5.0) * 0.3;
        stock_price += change;
        (i as f64, stock_price)
    }).collect();
    let stock_svg = create_line_chart("股票价格走势", &stock_data, 800.0, 450.0, "dc3545");
    fs::write(format!("{}/08_stock_price.svg", output_dir), stock_svg)?;

    // 2. 温度变化曲线
    let temperature_data: Vec<(f64, f64)> = (0..=24).map(|i| {
        let hour = i as f64;
        let temp = 20.0 + (hour * std::f64::consts::PI / 12.0 - std::f64::consts::PI / 2.0).sin() * 8.0 + 
                  (hour * 0.5).sin() * 2.0;
        (hour, temp)
    }).collect();
    let temperature_svg = create_line_chart("24小时温度变化", &temperature_data, 700.0, 400.0, "fd7e14");
    fs::write(format!("{}/09_temperature_curve.svg", output_dir), temperature_svg)?;

    // 3. 销售业绩曲线
    let sales_data: Vec<(f64, f64)> = (1..=12).map(|i| {
        let month = i as f64;
        let seasonal_factor = (month * std::f64::consts::PI / 6.0).cos() * 0.3 + 1.0;
        let growth_factor = 1.0 + month * 0.05;
        let base_sales = 50000.0;
        (month, base_sales * seasonal_factor * growth_factor)
    }).collect();
    let sales_svg = create_line_chart("年度销售业绩", &sales_data, 700.0, 400.0, "007bff");
    fs::write(format!("{}/10_sales_performance.svg", output_dir), sales_svg)?;

    println!("  ✅ 数据分析曲线生成完成 (3个图表)");
    Ok(())
}

/// 生成样式变化曲线
fn generate_styled_line_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 渐变色曲线
    let gradient_data: Vec<(f64, f64)> = (0..=50).map(|i| {
        let x = i as f64;
        let y = (x * 0.2).sin() * 50.0 + (x * 0.1).cos() * 30.0 + 100.0;
        (x, y)
    }).collect();
    let gradient_svg = create_gradient_line_chart("渐变色曲线", &gradient_data, 700.0, 400.0);
    fs::write(format!("{}/11_gradient_line.svg", output_dir), gradient_svg)?;

    // 2. 虚线曲线
    let dashed_data: Vec<(f64, f64)> = (0..=40).map(|i| {
        let x = i as f64;
        let y = x * 2.0 + (x * 0.3).sin() * 20.0 + 50.0;
        (x, y)
    }).collect();
    let dashed_svg = create_dashed_line_chart("虚线曲线", &dashed_data, 600.0, 400.0);
    fs::write(format!("{}/12_dashed_line.svg", output_dir), dashed_svg)?;

    // 3. 阴影效果曲线
    let shadow_data: Vec<(f64, f64)> = (0..=60).map(|i| {
        let x = i as f64;
        let y = (x * 0.15).sin() * 60.0 + (x * 0.05).cos() * 20.0 + 120.0;
        (x, y)
    }).collect();
    let shadow_svg = create_shadow_line_chart("阴影效果曲线", &shadow_data, 700.0, 400.0);
    fs::write(format!("{}/13_shadow_line.svg", output_dir), shadow_svg)?;

    println!("  ✅ 样式变化曲线生成完成 (3个图表)");
    Ok(())
}

/// 生成复杂场景曲线
fn generate_complex_line_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 多系列对比曲线
    let multi_series_svg = create_multi_series_line_chart("多产品销售对比", 900.0, 500.0);
    fs::write(format!("{}/14_multi_series_comparison.svg", output_dir), multi_series_svg)?;

    // 2. 面积填充曲线
    let area_data: Vec<(f64, f64)> = (0..=50).map(|i| {
        let x = i as f64;
        let y = (x * 0.2).sin() * 30.0 + (x * 0.1).cos() * 20.0 + 80.0;
        (x, y)
    }).collect();
    let area_svg = create_area_line_chart("面积填充曲线", &area_data, 700.0, 400.0);
    fs::write(format!("{}/15_area_filled_line.svg", output_dir), area_svg)?;

    // 3. 阶梯曲线
    let step_data: Vec<(f64, f64)> = (0..=20).map(|i| {
        let x = i as f64;
        let y = (i / 3) as f64 * 15.0 + 50.0 + (i % 3) as f64 * 5.0;
        (x, y)
    }).collect();
    let step_svg = create_step_line_chart("阶梯曲线", &step_data, 600.0, 400.0);
    fs::write(format!("{}/16_step_line.svg", output_dir), step_svg)?;

    println!("  ✅ 复杂场景曲线生成完成 (3个图表)");
    Ok(())
}

/// 生成动态效果曲线
fn generate_animated_line_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 1. 动画增长曲线
    let animated_growth_svg = create_animated_growth_chart("动画增长曲线", 700.0, 400.0);
    fs::write(format!("{}/17_animated_growth.svg", output_dir), animated_growth_svg)?;

    // 2. 波浪动画曲线
    let wave_animation_svg = create_wave_animation_chart("波浪动画曲线", 800.0, 400.0);
    fs::write(format!("{}/18_wave_animation.svg", output_dir), wave_animation_svg)?;

    // 3. 脉冲效果曲线
    let pulse_data: Vec<(f64, f64)> = (0..=40).map(|i| {
        let x = i as f64;
        let y = 100.0 + (x * 0.5).sin() * 50.0;
        (x, y)
    }).collect();
    let pulse_svg = create_pulse_line_chart("脉冲效果曲线", &pulse_data, 700.0, 400.0);
    fs::write(format!("{}/19_pulse_line.svg", output_dir), pulse_svg)?;

    println!("  ✅ 动态效果曲线生成完成 (3个图表)");
    Ok(())
}

// ============================================================================
// 曲线图生成函数
// ============================================================================

/// 创建基础曲线图
fn create_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64, color: &str) -> String {
    let mut svg = String::new();

    // SVG 头部
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 定义渐变和滤镜
    svg.push_str("  <defs>\n");
    svg.push_str(&format!("    <linearGradient id=\"areaGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n"));
    svg.push_str(&format!("      <stop offset=\"0%\" style=\"stop-color:#{};stop-opacity:0.3\" />\n", color));
    svg.push_str(&format!("      <stop offset=\"100%\" style=\"stop-color:#{};stop-opacity:0.1\" />\n", color));
    svg.push_str("    </linearGradient>\n");
    svg.push_str("    <filter id=\"shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n");
    svg.push_str("      <feDropShadow dx=\"2\" dy=\"2\" stdDeviation=\"2\" flood-color=\"#000000\" flood-opacity=\"0.2\"/>\n");
    svg.push_str("    </filter>\n");
    svg.push_str("  </defs>\n");

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;

    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));

    if !data.is_empty() {
        // 计算数据范围
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);

        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };

        // 添加网格线
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", x, chart_y, x, chart_y + chart_height));
        }

        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));
        }

        // 生成路径
        let mut path = String::from("M");
        let mut area_path = String::from("M");

        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;

            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
                area_path.push_str(&format!(" {} {}", px, chart_y + chart_height));
                area_path.push_str(&format!(" L {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
                area_path.push_str(&format!(" L {} {}", px, py));
            }
        }

        // 完成面积路径
        if let Some((last_x, _)) = data.last() {
            let last_px = chart_x + (last_x - min_x) / x_range * chart_width;
            area_path.push_str(&format!(" L {} {} Z", last_px, chart_y + chart_height));
        }

        // 绘制面积
        svg.push_str(&format!("  <path d=\"{}\" fill=\"url(#areaGradient)\"/>\n", area_path));

        // 绘制曲线
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#{}\" stroke-width=\"3\" fill=\"none\" filter=\"url(#shadow)\"/>\n", path, color));

        // 绘制数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;

            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#{}\" stroke=\"white\" stroke-width=\"2\" filter=\"url(#shadow)\"/>\n", px, py, color));
        }

        // 添加坐标轴标签
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            let value = min_x + (i as f64 / 5.0) * x_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{:.1}</text>\n", x, chart_y + chart_height + 20.0, value));
        }

        for i in 0..=5 {
            let y = chart_y + chart_height - (i as f64 / 5.0) * chart_height;
            let value = min_y + (i as f64 / 5.0) * y_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n", chart_x - 10.0, y + 4.0, value));
        }
    }

    svg.push_str("</svg>");
    svg
}
