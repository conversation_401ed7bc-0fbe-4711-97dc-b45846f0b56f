use crate::processor::{Channel<PERSON>ata, DataProcessor, DeviceConfig};
use crate::TSDevice;
use crate::{<PERSON>Length, Device, DeviceCommand, DeviceResponse, ProtocolError};
use crossbeam_queue::ArrayQueue;
use futures::stream;
use gpui::BackgroundExecutor;
use std::collections::VecDeque;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::mpsc;
use tokio::sync::watch;
use tokio::sync::Mutex;
use tokio_stream::{wrappers::ReceiverStream, Stream};
use tracing::{debug, error, info, warn};

/// 动态数据缓冲区
#[derive(Debug)]
struct AdaptiveBuffer {
    queue: Arc<ArrayQueue<Vec<ChannelData>>>,
    min_capacity: usize,
    max_capacity: usize,
    current_capacity: usize,
    sample_rate: f32,
}

impl Clone for AdaptiveBuffer {
    fn clone(&self) -> Self {
        Self {
            queue: Arc::clone(&self.queue),
            min_capacity: self.min_capacity,
            max_capacity: self.max_capacity,
            current_capacity: self.current_capacity,
            sample_rate: self.sample_rate,
        }
    }
}

impl AdaptiveBuffer {
    /// 创建新的自适应缓冲区
    fn new(min_capacity: usize, max_capacity: usize, sample_rate: f32) -> Self {
        debug!(
            "创建自适应缓冲区: 最小容量={}, 最大容量={}, 采样率={}Hz",
            min_capacity, max_capacity, sample_rate
        );
        Self {
            queue: Arc::new(ArrayQueue::new(min_capacity)),
            min_capacity,
            max_capacity,
            current_capacity: min_capacity,
            sample_rate,
        }
    }

    /// 将数据添加到缓冲区
    fn push(&self, data: Vec<ChannelData>) -> Result<(), Vec<ChannelData>> {
        self.queue.push(data).map_err(|e| e)
    }

    /// 从缓冲区获取数据
    fn pop(&self) -> Option<Vec<ChannelData>> {
        self.queue.pop()
    }

    /// 根据当前数据速率调整缓冲区大小
    fn adjust_capacity(&mut self, current_rate: f32) {
        let target_capacity = if current_rate > 1_000_000.0 {
            // 高速模式：大缓冲区
            (self.sample_rate * 0.1) as usize // 100ms缓冲区
        } else if current_rate > 100_000.0 {
            // 中速模式
            (self.sample_rate * 0.05) as usize // 50ms缓冲区
        } else {
            // 低速模式：小缓冲区
            self.min_capacity
        };

        let old_capacity = self.current_capacity;
        self.current_capacity = target_capacity.clamp(self.min_capacity, self.max_capacity);

        if old_capacity != self.current_capacity {
            debug!(
                "调整缓冲区容量: {} -> {} (数据速率: {:.2} Hz)",
                old_capacity, self.current_capacity, current_rate
            );
        }
    }
}

/// 自适应背压控制器
#[derive(Debug)]
struct AdaptiveBackpressure {
    window_size: usize,
    timestamps: VecDeque<Instant>,
    target_interval: Duration,
    sample_rate: f32,
    buffer: AdaptiveBuffer,
    overflow_counter: u32,
    last_adjustment: Instant,
}

impl Clone for AdaptiveBackpressure {
    fn clone(&self) -> Self {
        Self {
            window_size: self.window_size,
            timestamps: self.timestamps.clone(),
            target_interval: self.target_interval,
            sample_rate: self.sample_rate,
            buffer: self.buffer.clone(),
            overflow_counter: self.overflow_counter,
            last_adjustment: self.last_adjustment,
        }
    }
}

impl AdaptiveBackpressure {
    /// 创建自适应背压控制器
    fn new(sample_rate: f32, min_capacity: usize, max_capacity: usize) -> Self {
        let target_interval = if sample_rate > 0.0 {
            Duration::from_secs_f64(1.0 / sample_rate as f64)
        } else {
            Duration::from_micros(100) // 默认100μs
        };

        debug!(
            "创建自适应背压控制器: 采样率={}Hz, 目标间隔={:?}",
            sample_rate, target_interval
        );

        Self {
            window_size: 10,
            timestamps: VecDeque::with_capacity(20),
            target_interval,
            sample_rate,
            buffer: AdaptiveBuffer::new(min_capacity, max_capacity, sample_rate),
            overflow_counter: 0,
            last_adjustment: Instant::now(),
        }
    }

    /// 记录数据点并更新背压状态
    fn record_data(&mut self, data: Vec<ChannelData>) -> Result<(), ProtocolError> {
        let now = Instant::now();

        // 定期调整缓冲区大小
        if now.duration_since(self.last_adjustment) > Duration::from_secs(1) {
            let current_rate = self.current_data_rate();
            self.buffer.adjust_capacity(current_rate);
            self.last_adjustment = now;
        }

        // 尝试存储数据
        if let Err(_failed_data) = self.buffer.push(data) {
            self.overflow_counter += 1;
            warn!("数据缓冲区溢出: 计数器={}", self.overflow_counter);

            // 严重溢出处理
            if self.overflow_counter > 5 {
                error!("数据缓冲区严重溢出: {} 个缓冲区丢失", self.overflow_counter);
                return Err(ProtocolError::DataOverflow(format!(
                    "数据缓冲区严重溢出! {} 个缓冲区丢失",
                    self.overflow_counter
                )));
            }
        }

        self.timestamps.push_back(now);
        if self.timestamps.len() > self.window_size {
            self.timestamps.pop_front();
        }

        Ok(())
    }

    /// 计算当前数据速率（样本/秒）
    fn current_data_rate(&self) -> f32 {
        if self.timestamps.len() < 2 {
            return self.sample_rate;
        }

        let total_time = self
            .timestamps
            .back()
            .unwrap()
            .duration_since(*self.timestamps.front().unwrap());

        if total_time.as_secs_f32() > 0.0 {
            (self.timestamps.len() as f32) / total_time.as_secs_f32()
        } else {
            self.sample_rate
        }
    }

    /// 计算需要睡眠的时间以维持目标速率
    fn calculate_sleep_duration(&self) -> Option<Duration> {
        if self.timestamps.len() < 2 {
            return None;
        }

        let last = *self.timestamps.back().unwrap();
        let second_last = *self.timestamps.get(self.timestamps.len() - 2).unwrap();

        let actual_interval = last.duration_since(second_last);
        if actual_interval < self.target_interval {
            Some(self.target_interval - actual_interval)
        } else {
            None
        }
    }

    /// 获取缓冲数据
    fn get_buffered_data(&self) -> Option<Vec<ChannelData>> {
        self.buffer.pop()
    }
}

/// TSDAQClient 管理设备通信、数据处理和自适应背压
pub struct TSDAQClient {
    device: Arc<Mutex<Box<dyn Device>>>,
    processor: DataProcessor,
    channel_table: Vec<ChannelLength>,
    backpressure: AdaptiveBackpressure,
    config: Option<DeviceConfig>,
    bg_exec: Arc<BackgroundExecutor>,
    cancel_tx: Arc<Mutex<Option<watch::Sender<bool>>>>,
}

impl std::fmt::Debug for TSDAQClient {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("TSDAQClient")
            .field("processor", &self.processor)
            .field("channel_table", &self.channel_table)
            .field("backpressure", &self.backpressure)
            .field("config", &self.config)
            .finish_non_exhaustive()
    }
}

impl Clone for TSDAQClient {
    fn clone(&self) -> Self {
        Self {
            device: Arc::clone(&self.device),
            processor: self.processor.clone(),
            channel_table: self.channel_table.clone(),
            backpressure: self.backpressure.clone(),
            config: self.config.clone(),
            bg_exec: self.bg_exec.clone(),
            cancel_tx: Arc::clone(&self.cancel_tx),
        }
    }
}

impl TSDAQClient {
    /// 创建新的TSDAQClient实例
    pub fn new(device: Box<dyn Device>, bg_exec: Arc<BackgroundExecutor>) -> Self {
        info!("创建TSDAQ客户端实例");
        // 初始采样率设为0，将在读取配置后更新
        Self {
            device: Arc::new(Mutex::new(device)),
            processor: DataProcessor::new(),
            channel_table: Vec::new(),
            backpressure: AdaptiveBackpressure::new(0.0, 10, 1000),
            config: None,
            bg_exec,
            cancel_tx: Arc::new(Mutex::new(None)),
        }
    }

    /// 执行命令并检查响应
    async fn execute_and_check(
        &mut self,
        command: DeviceCommand,
        expected_command: u8,
    ) -> Result<(), ProtocolError> {
        let command_desc = command.description();
        debug!("执行命令: {} (0x{:02X})", command_desc, expected_command);

        let mut device_guard = self.device.lock().await;

        // 清空任何待处理的响应
        let drain_timeout = Duration::from_millis(5);
        for _ in 0..10 {
            match device_guard.recv_frame(drain_timeout).await {
                Ok(_) => continue,
                Err(ProtocolError::Timeout) => break,
                Err(_) => break,
            }
        }

        match device_guard.execute_command(command).await? {
            DeviceResponse::Ack {
                command: cmd,
                status: 0x00,
            } if cmd == expected_command => {
                info!(
                    "命令执行成功: {} (0x{:02X})",
                    command_desc, expected_command
                );
                Ok(())
            }
            res => {
                error!(
                    "命令执行失败: {} (0x{:02X}) - 响应: {:?}",
                    command_desc, expected_command, res
                );
                Err(ProtocolError::DeviceError(format!(
                    "命令 {} (0x{:02X}) 执行失败: {:?}",
                    command_desc, expected_command, res
                )))
            }
        }
    }

    /// 查询设备
    pub async fn query_device(&mut self) -> Result<(), ProtocolError> {
        info!("查询设备信息");
        self.execute_and_check(DeviceCommand::Query, 0x00).await
    }

    /// 连接设备
    pub async fn connect(&mut self) -> Result<(), ProtocolError> {
        info!("连接设备");
        self.execute_and_check(DeviceCommand::Connect, 0x01).await
    }

    /// 断开设备连接
    pub async fn disconnect(&mut self) -> Result<(), ProtocolError> {
        info!("断开设备连接");
        self.execute_and_check(DeviceCommand::Disconnect, 0x02)
            .await
    }

    /// 开始数据采集
    pub async fn start_collection(&mut self) -> Result<(), ProtocolError> {
        info!("开始数据采集");
        self.execute_and_check(DeviceCommand::StartCollection, 0x03)
            .await
    }

    /// 停止数据采集
    pub async fn stop_collection(&mut self) -> Result<(), ProtocolError> {
        info!("停止数据采集");

        // 先发送取消信号到数据流
        let cancel_tx_guard = self.cancel_tx.lock().await;
        if let Some(tx) = &*cancel_tx_guard {
            let _ = tx.send(true);
        }
        drop(cancel_tx_guard);

        // 等待一段时间让数据流处理停止
        gpui::Timer::after(Duration::from_millis(50)).await;

        // 发送停止命令到设备并检查响应
        debug!("发送停止命令到设备");
        self.execute_and_check(DeviceCommand::StopCollection, 0x04)
            .await
    }

    /// 读取设备配置
    pub async fn read_configuration(&mut self) -> Result<DeviceConfig, ProtocolError> {
        info!("读取设备配置");

        let config = {
            let mut device_guard = self.device.lock().await;
            match device_guard
                .execute_command(DeviceCommand::ReadParameters)
                .await?
            {
                DeviceResponse::Data(data) => {
                    debug!("解析设备配置数据: {} 字节", data.len());
                    self.processor.parse_configuration(&data)?
                }
                res => {
                    error!("读取配置失败: 意外的响应类型 {:?}", res);
                    return Err(ProtocolError::DeviceError(format!(
                        "读取配置失败: 意外的响应类型 {:?}",
                        res
                    )));
                }
            }
        };

        // 根据配置更新背压控制器
        self.backpressure = AdaptiveBackpressure::new(
            config.sampling_rate,
            5,    // 最小缓冲区
            1000, // 最大缓冲区
        );

        self.config = Some(config.clone());
        info!(
            "设备配置读取成功: 产品={}, 采样率={}Hz",
            config.product_name, config.sampling_rate
        );
        Ok(config)
    }

    /// 读取通道表
    pub async fn channel_table(&mut self) -> Result<(), ProtocolError> {
        info!("读取通道表");

        let mut device_guard = self.device.lock().await;
        match device_guard
            .execute_command(DeviceCommand::ReadChannelTable)
            .await?
        {
            DeviceResponse::Data(data) => {
                debug!("解析通道表数据: {} 字节", data.len());
                self.channel_table = self.processor.parse_channel_table(&data)?;
                info!("通道表读取成功: {} 个通道", self.channel_table.len());
                Ok(())
            }
            res => {
                error!("读取通道表失败: 意外的响应类型 {:?}", res);
                Err(ProtocolError::DeviceError(format!(
                    "读取通道表失败: 意外的响应类型 {:?}",
                    res
                )))
            }
        }
    }

    /// 设置采样率
    ///
    /// 允许外部设置采样率，更新背压控制器参数
    ///
    /// # 参数
    /// * `sample_rate` - 采样率（样本/秒）
    pub fn set_sample_rate(&mut self, sample_rate: f32) {
        info!("设置采样率: {}Hz", sample_rate);

        // 更新背压控制器
        self.backpressure = AdaptiveBackpressure::new(
            sample_rate,
            5,    // 最小缓冲区
            1000, // 最大缓冲区
        );

        // 如果配置存在，更新配置中的采样率
        if let Some(config) = &mut self.config {
            config.sampling_rate = sample_rate;
        }
    }

    /// 自适应数据流处理（兼容1kHz-10MHz）
    ///
    /// # 注意
    /// 使用 GPUI BackgroundExecutor 进行异步处理
    pub async fn stream_data(&mut self) -> ReceiverStream<Result<Vec<ChannelData>, ProtocolError>> {
        info!("启动数据流处理");

        let (tx, rx) = mpsc::channel(1024);

        // 清空背压缓冲区
        self.backpressure = AdaptiveBackpressure::new(
            self.backpressure.sample_rate,
            5,    // 最小缓冲区
            1000, // 最大缓冲区
        );

        // 创建一个新的取消通道
        let (cancel_tx, cancel_rx) = watch::channel(false);
        {
            let mut cancel_guard = self.cancel_tx.lock().await;
            *cancel_guard = Some(cancel_tx);
        }

        let device = self.device.clone();
        let processor = self.processor.clone();
        let channel_table = self.channel_table.clone();
        let mut backpressure = self.backpressure.clone();
        let device_clone = device.clone();
        let bg_exec_outer = self.bg_exec.clone();
        let bg_exec_inner = self.bg_exec.clone();

        bg_exec_outer
            .spawn(async move {
                // 确定超时时间：高速采样用短超时，低速采样用长超时
                let timeout_duration = if backpressure.sample_rate > 1_000_000.0 {
                    debug!("高速采样模式: 超时100μs");
                    Duration::from_micros(100)
                } else {
                    debug!("低速采样模式: 超时100ms");
                    Duration::from_millis(100)
                };

                loop {
                    // 检查取消信号
                    if *cancel_rx.borrow() {
                        debug!("收到取消信号，停止数据流");
                        tx.send(Err(ProtocolError::Cancelled)).await.ok();
                        break;
                    }

                    // 1. 尝试从背压缓冲区获取数据
                    if let Some(buffered_data) = backpressure.get_buffered_data() {
                        debug!("从缓冲区获取数据: {} 个通道", buffered_data.len());
                        if tx.send(Ok(buffered_data)).await.is_err() {
                            break;
                        }
                        continue;
                    }

                    // 2. 异步获取新数据（带超时）
                    let frame_result = bg_exec_inner.block_with_timeout(timeout_duration, async {
                        let mut device_guard = device_clone.lock().await;
                        device_guard.recv_frame(timeout_duration).await
                    });

                    match frame_result {
                        Ok(Ok(frame)) => {
                            debug!(
                                "接收到帧: 功能码=0x{:02X}, 数据长度={}",
                                frame.function,
                                frame.data.len()
                            );

                            // 处理不同的帧类型
                            let data = match frame.function {
                                0x08 | 0x0A => {
                                    debug!("处理组包数据");
                                    processor.grouped_data(&frame.data)
                                }
                                0x0B => {
                                    debug!("处理通道分组数据");
                                    processor.channel_grouped_data(&frame.data, &channel_table)
                                }
                                0x00 if frame.header == [0, 0xDA] => {
                                    debug!("处理单包数据");
                                    processor.single_packet_data(&frame.data)
                                }
                                _ => {
                                    warn!("未知帧类型: 0x{:02X}", frame.function);
                                    let err = Err(ProtocolError::DeviceError(format!(
                                        "未知帧类型: 0x{:02X}",
                                        frame.function
                                    )));
                                    tx.send(err).await.ok();
                                    continue;
                                }
                            };

                            // 存储数据并应用背压
                            if let Err(e) = backpressure.record_data(data.clone()) {
                                error!("背压控制失败: {:?}", e);
                                tx.send(Err(e)).await.ok();
                                break;
                            }

                            if let Some(sleep_duration) = backpressure.calculate_sleep_duration() {
                                debug!("应用背压延迟: {:?}", sleep_duration);
                                gpui::Timer::after(sleep_duration).await;
                            }

                            // 发送处理后的数据
                            debug!("发送处理后的数据: {} 个通道", data.len());
                            if tx.send(Ok(data)).await.is_err() {
                                break;
                            }
                        }
                        Ok(Err(ProtocolError::Io(e)))
                            if e.kind() == std::io::ErrorKind::TimedOut =>
                        {
                            // 超时处理 - 发送警告但不停止
                            debug!("数据接收超时");
                            tx.send(Err(ProtocolError::Timeout)).await.ok();
                        }
                        Ok(Err(e)) => {
                            // 设备错误
                            error!("设备通信错误: {:?}", e);
                            tx.send(Err(e)).await.ok();
                            break;
                        }
                        Err(_) => {
                            // 超时错误
                            debug!("数据接收超时");
                            tx.send(Err(ProtocolError::Timeout)).await.ok();
                        }
                    }
                }
            })
            .detach();

        ReceiverStream::new(rx)
    }
}
