[package]
name = "tsdaq_protocol"
version = "0.1.0"
edition = "2021"

[lib]
name = "tsdaq_protocol"
crate-type = ["cdylib", "rlib"]

[dependencies]
gpui.workspace = true
# log.workspace = true
serde.workspace = true
serde_json.workspace = true
anyhow.workspace = true
memchr.workspace = true
tokio-stream.workspace = true
futures.workspace = true
tokio.workspace = true
bytes.workspace = true
byteorder.workspace = true

tracing-error.workspace = true
tracing-subscriber.workspace = true
tracing.workspace =true

# 核心异步运行时
# tokio = { version = "1.0", features = ["full", "rt-multi-thread", "time", "net", "io-util", "macros", "sync"] }




# 串口通信（支持Windows/Linux/macOS）
serialport = { version = "4.0", features = ["libudev"] }


# 网络通信
socket2 = "0.4"
async-trait = "0.1"


# 高效数据结构和队列
crossbeam-queue = "0.3"
crossbeam-utils = "0.8"

# 错误处理
thiserror = "1.0"

# # 序列化
# serde = { version = "1.0", features = ["derive"] }
# serde_json = "1.0"

# 快速字节搜索
# memchr = "2.4"

# 流处理
# futures = "0.3"
# tokio-stream = "0.1"

# 浮点数据处理
half = "1.8"  # 可选，用于FP16支持
ieee754 = "0.2"  # IEEE754浮点工具

# 高效内存管理
bumpalo = "3.9"  # 用于高速数据路径

# 可选：SIMD加速
packed_simd = { version = "0.3", features = ["into_bits"], optional = true }

[lints]
workspace = true

[[example]]
name = "tsdaq"
path = "example/tsdaq.rs"


# 测试依赖
[dev-dependencies]
tokio-test = "0.4"
proptest = "1.0"
criterion = "0.3"

# [[bench]]
# name = "high_speed_bench"
# harness = false

# [features]
# default = ["std"]
# std = []  # 标准库支持
# simd = ["packed_simd"]  # SIMD加速
# fp16 = ["half"]  # 16位浮点支持

# 优化编译设置
# [profile.release]
# opt-level = 3
# lto = "thin"
# codegen-units = 1
# incremental = false
# panic = "abort"


# [profile.dev]
# opt-level = 0

# [profile.bench]
# opt-level = 3
# lto = "thin"
# codegen-units = 1
# incremental = false



