# ECharts-rs 本地 CI/CD 检查脚本 (PowerShell)
param(
    [string]$Action = "check"
)

# 日志函数
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# 检查必要工具
function Test-Prerequisites {
    Write-Info "检查必要工具..."

    try {
        $null = Get-Command cargo -ErrorAction Stop
    } catch {
        Write-Error "Cargo 未安装，请先安装 Rust"
        return $false
    }

    try {
        $null = Get-Command rustfmt -ErrorAction Stop
    } catch {
        Write-Warning "rustfmt 未安装，正在安装..."
        rustup component add rustfmt
    }

    try {
        $null = Get-Command clippy -ErrorAction Stop
    } catch {
        Write-Warning "clippy 未安装，正在安装..."
        rustup component add clippy
    }

    Write-Success "必要工具检查完成"
    return $true
}

# 简化版本 - 移除复杂函数，保持脚本简洁

# 显示帮助
function Show-Help {
    Write-Host "用法: .\ci-local.ps1 [选项]"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  check     运行完整的CI检查 (默认)"
    Write-Host "  clean     清理构建文件"
    Write-Host "  format    格式化代码"
    Write-Host "  help      显示此帮助信息"
}

# 简化的主检查函数
function Invoke-SimpleCheck {
    Write-Host "========================================"
    Write-Host "    ECharts-rs 本地 CI/CD 检查"
    Write-Host "========================================"

    Write-Info "运行基本检查..."

    # 检查格式
    Write-Info "检查代码格式..."
    cargo fmt --all -- --check
    if ($LASTEXITCODE -ne 0) {
        Write-Error "代码格式检查失败"
        return $false
    }

    # 运行Clippy
    Write-Info "运行 Clippy 检查..."
    cargo clippy --all-targets --all-features -- -D warnings
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Clippy 检查失败"
        return $false
    }

    # 构建项目
    Write-Info "构建项目..."
    cargo build --workspace --all-features
    if ($LASTEXITCODE -ne 0) {
        Write-Error "构建失败"
        return $false
    }

    # 运行测试
    Write-Info "运行测试..."
    cargo test --workspace --all-features
    if ($LASTEXITCODE -ne 0) {
        Write-Error "测试失败"
        return $false
    }

    Write-Success "所有检查都通过了！"
    return $true
}

# 主逻辑
switch ($Action.ToLower()) {
    "check" {
        if (Invoke-SimpleCheck) {
            exit 0
        } else {
            exit 1
        }
    }
    "clean" {
        Write-Info "清理构建文件..."
        cargo clean
        Write-Success "清理完成"
    }
    "format" {
        Write-Info "格式化代码..."
        cargo fmt --all
        Write-Success "代码格式化完成"
    }
    "help" { Show-Help }
    default {
        Write-Warning "未知选项: $Action"
        Show-Help
        exit 1
    }
}
