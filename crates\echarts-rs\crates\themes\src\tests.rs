//! 主题模块测试套件

#[cfg(test)]
mod tests {
    use crate::Theme;
    use echarts_core::Color;

    #[test]
    fn test_theme_creation() {
        let theme = Theme::default();

        // 验证默认主题有基本属性
        assert!(!theme.name.is_empty());
        assert!(!theme.color_palette.is_empty());

        println!("✅ 主题创建测试通过");
    }

    #[test]
    fn test_light_theme() {
        let light_theme = Theme::light();

        assert_eq!(light_theme.name, "light");
        assert!(!light_theme.color_palette.is_empty());

        // 验证浅色主题的背景色
        assert_eq!(light_theme.background_color, Color::WHITE);

        println!("✅ 浅色主题测试通过");
    }

    #[test]
    fn test_dark_theme() {
        let dark_theme = Theme::dark();

        assert_eq!(dark_theme.name, "dark");
        assert!(!dark_theme.color_palette.is_empty());

        // 验证深色主题的背景色（不检查具体值，只检查是否为深色）
        assert!(dark_theme.background_color.r < 0.5);
        assert!(dark_theme.background_color.g < 0.5);
        assert!(dark_theme.background_color.b < 0.5);

        println!("✅ 深色主题测试通过");
    }

    #[test]
    fn test_vintage_theme() {
        let vintage_theme = Theme::vintage();

        assert_eq!(vintage_theme.name, "vintage");
        assert!(!vintage_theme.color_palette.is_empty());

        println!("✅ 复古主题测试通过");
    }

    #[test]
    fn test_macarons_theme() {
        let macarons_theme = Theme::macarons();

        assert_eq!(macarons_theme.name, "macarons");
        assert!(!macarons_theme.color_palette.is_empty());

        println!("✅ 马卡龙主题测试通过");
    }

    #[test]
    fn test_theme_colors() {
        let theme = Theme::light();

        // 验证主题至少有一些颜色
        assert!(theme.color_palette.len() >= 3);

        // 验证颜色有效性
        for color in &theme.color_palette {
            assert!(color.r >= 0.0 && color.r <= 1.0);
            assert!(color.g >= 0.0 && color.g <= 1.0);
            assert!(color.b >= 0.0 && color.b <= 1.0);
            assert!(color.a >= 0.0 && color.a <= 1.0);
        }

        println!("✅ 主题颜色测试通过");
    }

    #[test]
    fn test_theme_text_styles() {
        let theme = Theme::light();

        // 验证文本样式存在
        assert!(theme.text_style.font_size > 0.0);

        println!("✅ 主题文本样式测试通过");
    }

    #[test]
    fn test_theme_comparison() {
        let light = Theme::light();
        let dark = Theme::dark();
        
        // 验证不同主题有不同的名称
        assert_ne!(light.name, dark.name);
        
        // 验证不同主题有不同的背景色
        assert_ne!(light.background_color, dark.background_color);
        
        println!("✅ 主题比较测试通过");
    }

    #[test]
    fn test_custom_theme_creation() {
        let mut custom_theme = Theme::light();
        custom_theme.name = "custom".to_string();
        custom_theme.background_color = Color::rgb(0.95, 0.95, 0.95);

        // 添加自定义颜色
        custom_theme.color_palette = vec![
            Color::rgb(1.0, 0.0, 0.0),  // 红色
            Color::rgb(0.0, 1.0, 0.0),  // 绿色
            Color::rgb(0.0, 0.0, 1.0),  // 蓝色
        ];

        assert_eq!(custom_theme.name, "custom");
        assert_eq!(custom_theme.color_palette.len(), 3);

        println!("✅ 自定义主题创建测试通过");
    }

    #[test]
    fn test_theme_color_cycling() {
        let theme = Theme::light();

        // 测试颜色循环获取
        let color_count = theme.color_palette.len();
        assert!(color_count > 0);

        // 获取第一个颜色
        let first_color = &theme.color_palette[0];

        // 在实际实现中，应该使用 index % color_count 来循环
        let safe_index = 0 % color_count;
        let safe_color = &theme.color_palette[safe_index];

        assert_eq!(first_color, safe_color);

        println!("✅ 主题颜色循环测试通过");
    }

    #[test]
    fn test_theme_serialization() {
        let theme = Theme::light();
        
        // 这里可以添加序列化/反序列化测试
        // 当实现了serde支持后
        assert_eq!(theme.name, "light");
        
        println!("✅ 主题序列化测试通过（基础版本）");
    }

    #[test]
    fn test_all_builtin_themes() {
        let themes = vec![
            Theme::light(),
            Theme::dark(),
            Theme::vintage(),
            Theme::macarons(),
        ];

        // 验证所有内置主题都有效
        for theme in &themes {
            assert!(!theme.name.is_empty());
            assert!(!theme.color_palette.is_empty());
            assert!(theme.text_style.font_size > 0.0);
        }

        // 验证主题名称唯一
        let mut names = Vec::new();
        for theme in &themes {
            assert!(!names.contains(&theme.name));
            names.push(theme.name.clone());
        }

        println!("✅ 所有内置主题测试通过");
    }

    #[test]
    fn test_theme_performance() {
        use std::time::Instant;

        // 测试主题创建性能
        let start = Instant::now();
        for _ in 0..100 {
            let _theme = Theme::light();
        }
        let creation_time = start.elapsed();

        // 测试主题访问性能
        let theme = Theme::light();
        let start = Instant::now();
        for i in 0..100 {
            let _color = &theme.color_palette[i % theme.color_palette.len()];
        }
        let access_time = start.elapsed();

        println!("✅ 主题性能测试通过");
        println!("   - 主题创建时间: {:?}", creation_time);
        println!("   - 颜色访问时间: {:?}", access_time);
    }
}
