# ECharts 组件模块分析

## 模块概述

`src/component/` 目录包含了 ECharts 的所有交互组件和辅助组件，这些组件为图表提供了丰富的交互功能和视觉辅助。

## 组件分类

### 1. 交互组件
- **tooltip**: 提示框组件
- **legend**: 图例组件
- **brush**: 刷选组件
- **dataZoom**: 数据缩放组件
- **axisPointer**: 坐标轴指示器

### 2. 布局组件
- **grid**: 直角坐标系网格
- **polar**: 极坐标系
- **parallel**: 平行坐标系
- **radar**: 雷达坐标系
- **singleAxis**: 单轴坐标系
- **calendar**: 日历坐标系

### 3. 标记组件
- **markPoint**: 标记点
- **markLine**: 标记线
- **markArea**: 标记区域

### 4. 工具组件
- **toolbox**: 工具箱
- **title**: 标题组件
- **timeline**: 时间轴组件

### 5. 视觉组件
- **visualMap**: 视觉映射组件
- **graphic**: 图形组件

### 6. 数据组件
- **dataset**: 数据集组件
- **transform**: 数据变换组件

### 7. 地理组件
- **geo**: 地理坐标系组件

### 8. 辅助组件
- **aria**: 无障碍访问组件
- **matrix**: 矩阵组件
- **thumbnail**: 缩略图组件

## 组件架构模式

### 1. 标准组件结构

每个组件都遵循 Model-View 架构：

```
component/[componentName]/
├── [ComponentName]Model.ts    # 数据模型
├── [ComponentName]View.ts     # 视图渲染
├── install.ts                 # 安装注册
├── helper.ts                  # 辅助函数（可选）
├── action.ts                  # 动作处理（可选）
└── preprocessor.ts            # 预处理器（可选）
```

### 2. 核心组件详细分析

#### Tooltip 组件 (提示框)

**文件结构**:
```
tooltip/
├── TooltipModel.ts           # 提示框模型
├── TooltipView.ts            # 提示框视图
├── TooltipHTMLContent.ts     # HTML 内容渲染
├── TooltipRichContent.ts     # 富文本内容渲染
├── helper.ts                 # 辅助函数
├── seriesFormatTooltip.ts    # 系列格式化
├── tooltipMarkup.ts          # 标记语言处理
└── install.ts                # 安装文件
```

**主要功能**:
- 支持 HTML 和 Canvas 两种渲染模式
- 支持自定义格式化函数
- 支持跟随鼠标和固定位置
- 支持多系列数据显示
- 支持富文本样式

**配置选项**:
```typescript
interface TooltipOption {
    trigger?: 'item' | 'axis' | 'none'
    renderMode?: 'auto' | 'html' | 'richText'
    showContent?: boolean
    alwaysShowContent?: boolean
    triggerOn?: 'mousemove' | 'click' | 'none'
    showDelay?: number
    hideDelay?: number
    enterable?: boolean
    confine?: boolean
    appendTo?: string | HTMLElement | Function
    position?: string | number[] | Function
    formatter?: string | Function
}
```

#### Legend 组件 (图例)

**文件结构**:
```
legend/
├── LegendModel.ts            # 图例模型
├── LegendView.ts             # 图例视图
├── ScrollableLegendModel.ts  # 可滚动图例模型
├── ScrollableLegendView.ts   # 可滚动图例视图
├── legendAction.ts           # 图例动作
├── legendFilter.ts           # 图例过滤
├── scrollableLegendAction.ts # 滚动图例动作
├── install.ts                # 标准图例安装
├── installLegendPlain.ts     # 普通图例安装
└── installLegendScroll.ts    # 滚动图例安装
```

**主要功能**:
- 支持水平和垂直布局
- 支持滚动和分页
- 支持图例选择和高亮
- 支持自定义图例项
- 支持图例联动

**配置选项**:
```typescript
interface LegendOption {
    type?: 'plain' | 'scroll'
    orient?: 'horizontal' | 'vertical'
    align?: 'auto' | 'left' | 'right'
    padding?: number | number[]
    itemGap?: number
    itemWidth?: number
    itemHeight?: number
    selectedMode?: boolean | 'single' | 'multiple'
    inactiveColor?: string
    selected?: Record<string, boolean>
    textStyle?: LabelOption
    tooltip?: CommonTooltipOption
}
```

#### DataZoom 组件 (数据缩放)

**文件结构**:
```
dataZoom/
├── DataZoomModel.ts          # 基础模型
├── DataZoomView.ts           # 基础视图
├── InsideZoomModel.ts        # 内置缩放模型
├── InsideZoomView.ts         # 内置缩放视图
├── SliderZoomModel.ts        # 滑块缩放模型
├── SliderZoomView.ts         # 滑块缩放视图
├── SelectZoomModel.ts        # 框选缩放模型
├── SelectZoomView.ts         # 框选缩放视图
├── AxisProxy.ts              # 轴代理
├── dataZoomAction.ts         # 缩放动作
├── dataZoomProcessor.ts      # 缩放处理器
├── helper.ts                 # 辅助函数
├── history.ts                # 历史记录
├── roams.ts                  # 漫游控制
└── install*.ts               # 各种安装文件
```

**主要功能**:
- 支持滑块、内置、框选三种缩放方式
- 支持多轴联动缩放
- 支持数据过滤和采样
- 支持缩放历史记录
- 支持实时和延迟更新

#### Grid 组件 (网格)

**主要功能**:
- 提供直角坐标系布局
- 支持多网格布局
- 支持网格背景和边框
- 支持坐标轴配置

#### VisualMap 组件 (视觉映射)

**文件结构**:
```
visualMap/
├── VisualMapModel.ts         # 基础模型
├── VisualMapView.ts          # 基础视图
├── ContinuousModel.ts        # 连续型模型
├── ContinuousView.ts         # 连续型视图
├── PiecewiseModel.ts         # 分段型模型
├── PiecewiseView.ts          # 分段型视图
├── helper.ts                 # 辅助函数
├── visualEncoding.ts         # 视觉编码
├── visualMapAction.ts        # 视觉映射动作
├── preprocessor.ts           # 预处理器
└── install*.ts               # 各种安装文件
```

**主要功能**:
- 支持连续型和分段型映射
- 支持颜色、大小、透明度映射
- 支持自定义映射范围
- 支持交互式调整

## 组件通信机制

### 1. 事件系统
```typescript
// 组件间通过事件通信
api.dispatchAction({
    type: 'legendSelect',
    name: 'series1'
});

// 监听事件
chart.on('legendselectchanged', function(params) {
    // 处理图例选择变化
});
```

### 2. 数据共享
```typescript
// 通过全局模型共享数据
const globalModel = api.getModel();
const seriesModels = globalModel.getSeriesByType('bar');
```

### 3. 坐标系集成
```typescript
// 组件与坐标系的集成
const grid = api.getCoordinateSystems()[0];
const xAxis = grid.getAxis('x');
const yAxis = grid.getAxis('y');
```

## 性能优化策略

### 1. 渲染优化
- **增量更新**: 只更新变化的部分
- **虚拟化**: 大数据量时使用虚拟滚动
- **缓存机制**: 缓存计算结果和渲染内容

### 2. 交互优化
- **防抖节流**: 高频交互事件的优化
- **事件委托**: 减少事件监听器数量
- **异步处理**: 复杂计算异步执行

### 3. 内存管理
- **资源清理**: 组件销毁时清理资源
- **事件解绑**: 避免内存泄漏
- **对象复用**: 复用临时对象

## 扩展机制

### 1. 自定义组件
```typescript
// 1. 定义组件模型
class CustomComponentModel extends ComponentModel {
    static type = 'customComponent'
    static defaultOption = { /* ... */ }
}

// 2. 定义组件视图
class CustomComponentView extends ComponentView {
    type = 'customComponent'
    render(componentModel, ecModel, api) {
        // 渲染逻辑
    }
}

// 3. 注册组件
echarts.registerComponent(CustomComponentModel, CustomComponentView)
```

### 2. 组件配置扩展
```typescript
// 扩展现有组件的配置选项
interface ExtendedTooltipOption extends TooltipOption {
    customProperty?: any
}
```

## 组件依赖关系

### 1. 核心依赖
- **Model**: 所有组件都依赖基础模型类
- **View**: 所有组件都依赖基础视图类
- **ExtensionAPI**: 提供统一的 API 接口

### 2. 组件间依赖
- **AxisPointer** ← **Tooltip**: 坐标轴指示器依赖提示框
- **Legend** ← **Series**: 图例依赖系列数据
- **DataZoom** ← **Axis**: 数据缩放依赖坐标轴

### 3. 坐标系依赖
- **Grid** ← **CartesianAxis**: 网格依赖直角坐标轴
- **Polar** ← **AngleAxis, RadiusAxis**: 极坐标依赖角度轴和径向轴

## 重构建议

### 1. 组件解耦
- **接口抽象**: 定义更清晰的组件接口
- **依赖注入**: 减少硬编码依赖
- **事件总线**: 统一的事件通信机制

### 2. 代码复用
- **基础组件**: 提取更多通用基础组件
- **Mixin 模式**: 共享通用功能
- **工具函数**: 统一常用操作

### 3. 性能优化
- **懒加载**: 按需加载组件
- **虚拟化**: 大数据场景优化
- **WebWorker**: 复杂计算移到后台

### 4. 开发体验
- **类型安全**: 加强 TypeScript 类型约束
- **文档完善**: 详细的组件使用文档
- **调试工具**: 组件状态调试工具

### 5. 测试覆盖
- **单元测试**: 每个组件的单元测试
- **集成测试**: 组件间交互测试
- **视觉测试**: 渲染结果的视觉回归测试
