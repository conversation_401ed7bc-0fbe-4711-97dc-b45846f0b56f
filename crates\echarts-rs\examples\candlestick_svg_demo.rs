//! 蜡烛图（K线图）SVG演示
//!
//! 生成各种蜡烛图的SVG文件，展示CandlestickSeries的完整功能

use std::fs;
use echarts_rs::{CandlestickSeries, CandlestickDataItem, CandlestickStyle, CandlestickLabel, CandlestickLabelPosition, CandlestickLabelContent, Color};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("📈 蜡烛图（K线图）SVG演示系统");
    println!("{}", "=".repeat(50));

    // 确保输出目录存在
    let output_dir = "temp/svg/candlestick_charts";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 基础蜡烛图
    println!("\n📈 1. 生成基础蜡烛图...");
    generate_basic_candlestick(output_dir)?;

    // 2. 股票K线图
    println!("\n💹 2. 生成股票K线图...");
    generate_stock_candlestick(output_dir)?;

    // 3. 带成交量的K线图
    println!("\n📊 3. 生成带成交量的K线图...");
    generate_volume_candlestick(output_dir)?;

    // 4. 期货K线图
    println!("\n🏦 4. 生成期货K线图...");
    generate_futures_candlestick(output_dir)?;

    // 5. 外汇K线图
    println!("\n💱 5. 生成外汇K线图...");
    generate_forex_candlestick(output_dir)?;

    // 6. 生成展示页面
    println!("\n📄 6. 生成展示页面...");
    generate_candlestick_showcase(output_dir)?;

    println!("\n🎉 蜡烛图SVG演示生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/candlestick_demo.html 查看演示", output_dir);

    Ok(())
}

/// 生成基础蜡烛图
fn generate_basic_candlestick(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        CandlestickDataItem::new(1.0, 100.0, 110.0, 95.0, 105.0),
        CandlestickDataItem::new(2.0, 105.0, 115.0, 100.0, 110.0),
        CandlestickDataItem::new(3.0, 110.0, 108.0, 102.0, 104.0),
        CandlestickDataItem::new(4.0, 104.0, 112.0, 98.0, 108.0),
        CandlestickDataItem::new(5.0, 108.0, 118.0, 106.0, 115.0),
    ];

    let candlestick_series = CandlestickSeries::new("基础K线图")
        .data(data)
        .candle_width(0.6)
        .colors(Color::rgb(0.2, 0.8, 0.2), Color::rgb(0.8, 0.2, 0.2));

    let svg = create_candlestick_svg(&candlestick_series, "基础蜡烛图演示", 600.0, 400.0)?;
    fs::write(format!("{}/01_basic_candlestick.svg", output_dir), svg)?;
    
    println!("  ✅ 基础蜡烛图生成完成");
    Ok(())
}

/// 生成股票K线图
fn generate_stock_candlestick(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        CandlestickDataItem::new(1.0, 2320.26, 2320.26, 2287.3, 2308.38).volume(23000000.0),
        CandlestickDataItem::new(2.0, 2300.0, 2291.3, 2288.26, 2308.38).volume(18500000.0),
        CandlestickDataItem::new(3.0, 2295.35, 2346.5, 2295.35, 2346.92).volume(32000000.0),
        CandlestickDataItem::new(4.0, 2347.22, 2358.98, 2337.35, 2363.8).volume(28000000.0),
        CandlestickDataItem::new(5.0, 2360.75, 2382.48, 2347.89, 2383.76).volume(35000000.0),
        CandlestickDataItem::new(6.0, 2383.43, 2385.42, 2371.23, 2391.82).volume(22000000.0),
        CandlestickDataItem::new(7.0, 2377.41, 2419.02, 2369.57, 2421.15).volume(41000000.0),
        CandlestickDataItem::new(8.0, 2425.92, 2428.15, 2417.58, 2440.38).volume(26000000.0),
    ];

    let style = CandlestickStyle {
        bullish_color: Color::rgb(0.0, 0.8, 0.0), // 绿色上涨
        bearish_color: Color::rgb(0.8, 0.0, 0.0), // 红色下跌
        doji_color: Color::rgb(0.5, 0.5, 0.5),
        border_color: Color::rgb(0.1, 0.1, 0.1),
        border_width: 1.0,
        candle_width: 0.7,
        shadow_width: 1.0,
        fill_body: true,
    };

    let label = CandlestickLabel {
        show: true,
        font_size: 10.0,
        color: Color::rgb(0.2, 0.2, 0.2),
        position: CandlestickLabelPosition::Top,
        content: CandlestickLabelContent::Close,
    };

    let candlestick_series = CandlestickSeries::new("股票K线")
        .data(data)
        .style(style)
        .label(label)
        .show_volume(false);

    let svg = create_candlestick_svg(&candlestick_series, "股票K线图", 700.0, 400.0)?;
    fs::write(format!("{}/02_stock_candlestick.svg", output_dir), svg)?;
    
    println!("  ✅ 股票K线图生成完成");
    Ok(())
}

/// 生成带成交量的K线图
fn generate_volume_candlestick(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        CandlestickDataItem::new(1.0, 50.0, 55.0, 48.0, 52.0).volume(1200000.0),
        CandlestickDataItem::new(2.0, 52.0, 58.0, 51.0, 56.0).volume(1500000.0),
        CandlestickDataItem::new(3.0, 56.0, 54.0, 49.0, 51.0).volume(1800000.0),
        CandlestickDataItem::new(4.0, 51.0, 59.0, 50.0, 57.0).volume(2100000.0),
        CandlestickDataItem::new(5.0, 57.0, 62.0, 55.0, 60.0).volume(1900000.0),
        CandlestickDataItem::new(6.0, 60.0, 58.0, 53.0, 55.0).volume(1600000.0),
    ];

    let candlestick_series = CandlestickSeries::new("带成交量K线")
        .data(data)
        .colors(Color::rgb(0.1, 0.7, 0.1), Color::rgb(0.7, 0.1, 0.1))
        .candle_width(0.8)
        .show_volume(true)
        .volume_height_ratio(0.3);

    let svg = create_candlestick_svg(&candlestick_series, "带成交量K线图", 600.0, 500.0)?;
    fs::write(format!("{}/03_volume_candlestick.svg", output_dir), svg)?;
    
    println!("  ✅ 带成交量K线图生成完成");
    Ok(())
}

/// 生成期货K线图
fn generate_futures_candlestick(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        CandlestickDataItem::new(1.0, 3800.0, 3850.0, 3780.0, 3820.0).volume(45000.0),
        CandlestickDataItem::new(2.0, 3820.0, 3880.0, 3810.0, 3860.0).volume(52000.0),
        CandlestickDataItem::new(3.0, 3860.0, 3840.0, 3790.0, 3810.0).volume(48000.0),
        CandlestickDataItem::new(4.0, 3810.0, 3890.0, 3800.0, 3870.0).volume(61000.0),
        CandlestickDataItem::new(5.0, 3870.0, 3920.0, 3850.0, 3900.0).volume(55000.0),
    ];

    let label = CandlestickLabel {
        show: true,
        font_size: 11.0,
        color: Color::rgb(0.1, 0.1, 0.1),
        position: CandlestickLabelPosition::Inside,
        content: CandlestickLabelContent::Change,
    };

    let candlestick_series = CandlestickSeries::new("期货K线")
        .data(data)
        .colors(Color::rgb(0.2, 0.6, 1.0), Color::rgb(1.0, 0.4, 0.2))
        .candle_width(0.75)
        .label(label);

    let svg = create_candlestick_svg(&candlestick_series, "期货K线图", 600.0, 400.0)?;
    fs::write(format!("{}/04_futures_candlestick.svg", output_dir), svg)?;
    
    println!("  ✅ 期货K线图生成完成");
    Ok(())
}

/// 生成外汇K线图
fn generate_forex_candlestick(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        CandlestickDataItem::new(1.0, 1.2345, 1.2380, 1.2320, 1.2365),
        CandlestickDataItem::new(2.0, 1.2365, 1.2390, 1.2340, 1.2375),
        CandlestickDataItem::new(3.0, 1.2375, 1.2360, 1.2330, 1.2340),
        CandlestickDataItem::new(4.0, 1.2340, 1.2400, 1.2335, 1.2385),
        CandlestickDataItem::new(5.0, 1.2385, 1.2420, 1.2370, 1.2410),
        CandlestickDataItem::new(6.0, 1.2410, 1.2395, 1.2350, 1.2360),
        CandlestickDataItem::new(7.0, 1.2360, 1.2430, 1.2355, 1.2420),
    ];

    let style = CandlestickStyle {
        bullish_color: Color::rgb(0.3, 0.8, 0.3),
        bearish_color: Color::rgb(0.8, 0.3, 0.3),
        doji_color: Color::rgb(0.6, 0.6, 0.6),
        border_color: Color::rgb(0.2, 0.2, 0.2),
        border_width: 1.5,
        candle_width: 0.65,
        shadow_width: 1.2,
        fill_body: true,
    };

    let label = CandlestickLabel {
        show: true,
        font_size: 9.0,
        color: Color::rgb(0.0, 0.0, 0.0),
        position: CandlestickLabelPosition::Bottom,
        content: CandlestickLabelContent::Close,
    };

    let candlestick_series = CandlestickSeries::new("外汇K线")
        .data(data)
        .style(style)
        .label(label);

    let svg = create_candlestick_svg(&candlestick_series, "外汇K线图 (EUR/USD)", 650.0, 400.0)?;
    fs::write(format!("{}/05_forex_candlestick.svg", output_dir), svg)?;
    
    println!("  ✅ 外汇K线图生成完成");
    Ok(())
}

/// 创建蜡烛图SVG
fn create_candlestick_svg(series: &CandlestickSeries, title: &str, width: f64, height: f64) -> std::result::Result<String, Box<dyn std::error::Error>> {
    use echarts_core::{CartesianCoordinateSystem, Bounds, Series, Point, Size};
    
    // 创建坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        Bounds {
            origin: Point { x: 50.0, y: 50.0 },
            size: Size { width: width - 100.0, height: height - 100.0 },
        },
        (0.0, series.data_len() as f64),
        (0.0, 100.0), // 价格范围会自动调整
    );
    
    // 获取渲染命令
    let commands = series.render_to_commands(&coord_system)?;
    
    // 生成SVG
    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 渲染命令
    for command in commands {
        render_candlestick_svg_command(&mut svg, &command);
    }
    
    svg.push_str("</svg>");
    Ok(svg)
}

/// 渲染蜡烛图SVG命令
fn render_candlestick_svg_command(svg: &mut String, command: &echarts_core::DrawCommand) {
    use echarts_core::DrawCommand;
    
    match command {
        DrawCommand::Line { from, to, style } => {
            let color = format!("#{:02x}{:02x}{:02x}", 
                (style.color.r * 255.0) as u8, (style.color.g * 255.0) as u8, (style.color.b * 255.0) as u8);
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"{}\" stroke-width=\"{}\" opacity=\"{}\"/>\n", 
                from.x, from.y, to.x, to.y, color, style.width, style.opacity));
        }
        DrawCommand::Rect { bounds, style } => {
            let fill = style.fill.map(|c| format!("#{:02x}{:02x}{:02x}", 
                (c.r * 255.0) as u8, (c.g * 255.0) as u8, (c.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke = style.stroke.as_ref().map(|s| format!("#{:02x}{:02x}{:02x}", 
                (s.color.r * 255.0) as u8, (s.color.g * 255.0) as u8, (s.color.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke_width = style.stroke.as_ref().map(|s| s.width).unwrap_or(0.0);
            
            svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"{}\" stroke=\"{}\" stroke-width=\"{}\" opacity=\"{}\"/>\n", 
                bounds.origin.x, bounds.origin.y, bounds.size.width, bounds.size.height, fill, stroke, stroke_width, style.opacity));
        }
        DrawCommand::Text { text, position, style } => {
            let color = format!("#{:02x}{:02x}{:02x}", 
                (style.color.r * 255.0) as u8, (style.color.g * 255.0) as u8, (style.color.b * 255.0) as u8);
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"{}\" fill=\"{}\" text-anchor=\"middle\" opacity=\"{}\">{}</text>\n", 
                position.x, position.y, style.font_size, color, style.opacity, text));
        }
        _ => {} // 忽略其他命令类型
    }
}

/// 生成展示页面
fn generate_candlestick_showcase(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts-rs 蜡烛图（K线图）演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .section {
            background: rgba(255,255,255,0.95);
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .chart-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        .chart-svg {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h2 { color: #333; margin-bottom: 20px; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        .description {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .technical-info {
            background: rgba(255,255,255,0.05);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .candle-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .candle-type {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 ECharts-rs 蜡烛图（K线图）演示</h1>
            <p class="description">展现 CandlestickSeries 的强大功能和金融数据分析的专业可视化能力</p>
            <div class="feature-list">
                <div class="feature">
                    <h3>📈 基础K线图</h3>
                    <p>经典的OHLC数据展示</p>
                </div>
                <div class="feature">
                    <h3>💹 股票分析</h3>
                    <p>专业的股票价格走势</p>
                </div>
                <div class="feature">
                    <h3>📊 成交量分析</h3>
                    <p>价格与成交量联合展示</p>
                </div>
                <div class="feature">
                    <h3>🏦 期货外汇</h3>
                    <p>期货和外汇市场分析</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📈 基础蜡烛图功能</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">基础蜡烛图</div>
                    <object class="chart-svg" data="01_basic_candlestick.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">股票K线图</div>
                    <object class="chart-svg" data="02_stock_candlestick.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 高级功能展示</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">带成交量K线图</div>
                    <object class="chart-svg" data="03_volume_candlestick.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">期货K线图</div>
                    <object class="chart-svg" data="04_futures_candlestick.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>💱 专业市场应用</h2>
            <div class="chart-item">
                <div class="chart-title">外汇K线图 (EUR/USD)</div>
                <object class="chart-svg" data="05_forex_candlestick.svg" type="image/svg+xml">SVG不支持</object>
            </div>

            <div class="technical-info">
                <h3>🕯️ 蜡烛图技术分析</h3>
                <div class="candle-types">
                    <div class="candle-type">
                        <h4>🟢 阳线（看涨）</h4>
                        <p>收盘价 > 开盘价<br>表示价格上涨</p>
                    </div>
                    <div class="candle-type">
                        <h4>🔴 阴线（看跌）</h4>
                        <p>收盘价 < 开盘价<br>表示价格下跌</p>
                    </div>
                    <div class="candle-type">
                        <h4>⚪ 十字星</h4>
                        <p>收盘价 = 开盘价<br>表示市场犹豫</p>
                    </div>
                    <div class="candle-type">
                        <h4>📏 影线分析</h4>
                        <p>上影线：最高价压力<br>下影线：最低价支撑</p>
                    </div>
                </div>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h2>🎉 CandlestickSeries 功能总结</h2>
            <p>ECharts-rs CandlestickSeries 完整功能展示：</p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>✅ <strong>OHLC数据支持</strong> - 完整的开高低收价格数据处理</li>
                <li>✅ <strong>蜡烛图形绘制</strong> - 精确的实体和影线渲染</li>
                <li>✅ <strong>成交量集成</strong> - 价格与成交量的联合展示</li>
                <li>✅ <strong>多种标签模式</strong> - 灵活的标签位置和内容配置</li>
                <li>✅ <strong>专业样式配置</strong> - 阳线、阴线、十字星的颜色设置</li>
                <li>✅ <strong>金融市场适配</strong> - 股票、期货、外汇等市场支持</li>
                <li>✅ <strong>技术分析支持</strong> - 涨跌幅、变化率等指标计算</li>
                <li>✅ <strong>高质量渲染</strong> - 优化的SVG输出和视觉效果</li>
            </ul>
        </div>
    </div>
</body>
</html>"#;

    fs::write(format!("{}/candlestick_demo.html", output_dir), html_content)?;
    Ok(())
}
