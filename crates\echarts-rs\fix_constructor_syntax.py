#!/usr/bin/env python3
"""
修复构造函数语法错误
"""

import os
import re
import glob

def fix_constructor_syntax():
    """修复构造函数中的语法错误"""
    
    chart_files = glob.glob("crates/charts/src/*.rs")
    
    for file_path in chart_files:
        if not os.path.exists(file_path):
            continue
            
        print(f"修复文件: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 修复重复的 Self { 语句
            pattern = r'(\s+Self \{\s+config: ChartConfig \{\s+)\n\s+Self \{\s+(name: name\.into\(\),)\s+\n\s+Self \{\s+(\.\.ChartConfig::default\(\))\s+\n\s+Self \{\s+(\},)'
            replacement = r'\1\2\n\1    \3\n\1\4'
            
            new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
            
            if new_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                print(f"  ✅ 已修复 {file_path}")
            else:
                print(f"  ⏭️  无需修复 {file_path}")
                
        except Exception as e:
            print(f"  ❌ 修复失败 {file_path}: {e}")

if __name__ == "__main__":
    print("🚀 开始修复构造函数语法错误...")
    fix_constructor_syntax()
    print("✅ 修复完成！")
