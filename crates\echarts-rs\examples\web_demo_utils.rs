//! Web演示工具函数
//!
//! 提供HTML生成和页面创建的工具函数

use std::fs;
use echarts_core::{DrawCommand, Bounds, Color};

/// 生成图表HTML页面
pub fn generate_chart_html(
    title: &str,
    description: &str,
    commands: &[DrawCommand],
    bounds: Bounds,
    info: &str
) -> String {
    // 将DrawCommand转换为JavaScript代码
    let mut js_commands = Vec::new();
    
    for command in commands {
        match command {
            DrawCommand::Line { from, to, style } => {
                js_commands.push(format!(
                    "ctx.strokeStyle = '{}'; ctx.lineWidth = {}; ctx.beginPath(); ctx.moveTo({}, {}); ctx.lineTo({}, {}); ctx.stroke();",
                    color_to_css(&style.color),
                    style.width,
                    from.x, from.y,
                    to.x, to.y
                ));
            }
            DrawCommand::Rect { bounds: rect_bounds, style } => {
                if let Some(fill) = &style.fill {
                    js_commands.push(format!(
                        "ctx.fillStyle = '{}'; ctx.fillRect({}, {}, {}, {});",
                        color_to_css(fill),
                        rect_bounds.origin.x, rect_bounds.origin.y,
                        rect_bounds.size.width, rect_bounds.size.height
                    ));
                }
                if let Some(stroke) = &style.stroke {
                    js_commands.push(format!(
                        "ctx.strokeStyle = '{}'; ctx.lineWidth = {}; ctx.strokeRect({}, {}, {}, {});",
                        color_to_css(&stroke.color),
                        stroke.width,
                        rect_bounds.origin.x, rect_bounds.origin.y,
                        rect_bounds.size.width, rect_bounds.size.height
                    ));
                }
            }
            DrawCommand::Circle { center, radius, style } => {
                js_commands.push(format!(
                    "ctx.beginPath(); ctx.arc({}, {}, {}, 0, 2 * Math.PI);",
                    center.x, center.y, radius
                ));
                if let Some(fill) = &style.fill {
                    js_commands.push(format!(
                        "ctx.fillStyle = '{}'; ctx.fill();",
                        color_to_css(fill)
                    ));
                }
                if let Some(stroke) = &style.stroke {
                    js_commands.push(format!(
                        "ctx.strokeStyle = '{}'; ctx.lineWidth = {}; ctx.stroke();",
                        color_to_css(&stroke.color),
                        stroke.width
                    ));
                }
            }
            DrawCommand::Path { commands: path_commands, style } => {
                js_commands.push("ctx.beginPath();".to_string());
                for path_cmd in path_commands {
                    match path_cmd {
                        echarts_core::draw_commands::PathCommand::MoveTo(point) => {
                            js_commands.push(format!("ctx.moveTo({}, {});", point.x, point.y));
                        }
                        echarts_core::draw_commands::PathCommand::LineTo(point) => {
                            js_commands.push(format!("ctx.lineTo({}, {});", point.x, point.y));
                        }
                        echarts_core::draw_commands::PathCommand::CurveTo { control1, control2, to } => {
                            js_commands.push(format!(
                                "ctx.bezierCurveTo({}, {}, {}, {}, {}, {});",
                                control1.x, control1.y, control2.x, control2.y, to.x, to.y
                            ));
                        }
                        echarts_core::draw_commands::PathCommand::QuadTo { control, to } => {
                            js_commands.push(format!(
                                "ctx.quadraticCurveTo({}, {}, {}, {});",
                                control.x, control.y, to.x, to.y
                            ));
                        }
                        echarts_core::draw_commands::PathCommand::Close => {
                            js_commands.push("ctx.closePath();".to_string());
                        }
                        _ => {}
                    }
                }
                if let Some(fill) = &style.fill {
                    js_commands.push(format!(
                        "ctx.fillStyle = '{}'; ctx.fill();",
                        color_to_css(fill)
                    ));
                }
                if let Some(stroke) = &style.stroke {
                    js_commands.push(format!(
                        "ctx.strokeStyle = '{}'; ctx.lineWidth = {}; ctx.stroke();",
                        color_to_css(&stroke.color),
                        stroke.width
                    ));
                }
            }
            _ => {}
        }
    }
    
    let js_code = js_commands.join("\n        ");
    
    format!(r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }}
        .container {{
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }}
        h1 {{
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        .description {{
            text-align: center;
            margin-bottom: 30px;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        .chart-container {{
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }}
        canvas {{
            display: block;
            margin: 0 auto;
            border: 1px solid #ddd;
            border-radius: 8px;
        }}
        .info {{
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: center;
        }}
        .back-link {{
            display: inline-block;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            margin-top: 20px;
            transition: background 0.3s ease;
        }}
        .back-link:hover {{
            background: rgba(255,255,255,0.3);
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{}</h1>
        <p class="description">{}</p>
        
        <div class="chart-container">
            <canvas id="chartCanvas" width="{}" height="{}"></canvas>
        </div>
        
        <div class="info">
            <p>{}</p>
            <a href="index.html" class="back-link">← 返回主页</a>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('chartCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置高DPI支持
        const dpr = window.devicePixelRatio || 1;
        const rect = canvas.getBoundingClientRect();
        canvas.width = rect.width * dpr;
        canvas.height = rect.height * dpr;
        ctx.scale(dpr, dpr);
        canvas.style.width = rect.width + 'px';
        canvas.style.height = rect.height + 'px';
        
        // 渲染图表
        {}
    </script>
</body>
</html>"#, 
        title, title, description,
        bounds.size.width as i32 + 100, bounds.size.height as i32 + 100,
        info, js_code
    )
}

/// 将Color转换为CSS颜色字符串
pub fn color_to_css(color: &Color) -> String {
    format!("rgba({}, {}, {}, {})", 
        (color.r * 255.0) as u8,
        (color.g * 255.0) as u8, 
        (color.b * 255.0) as u8,
        color.a
    )
}

/// 生成综合演示页面
pub fn generate_comprehensive_demo(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts-rs 综合演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        .chart-item {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        canvas {
            width: 100%;
            height: 300px;
            border-radius: 8px;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h3 { margin-bottom: 15px; color: #333; }
        .back-link {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            margin-top: 20px;
            transition: background 0.3s ease;
        }
        .back-link:hover {
            background: rgba(255,255,255,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 ECharts-rs 综合演示</h1>
            <p>展示所有图表类型的渲染效果</p>
            <a href="index.html" class="back-link">← 返回主页</a>
        </div>
        
        <div class="charts-grid">
            <div class="chart-item">
                <h3>📈 线图演示</h3>
                <canvas id="lineChart"></canvas>
            </div>
            <div class="chart-item">
                <h3>📊 柱图演示</h3>
                <canvas id="barChart"></canvas>
            </div>
            <div class="chart-item">
                <h3>🥧 饼图演示</h3>
                <canvas id="pieChart"></canvas>
            </div>
            <div class="chart-item">
                <h3>🔵 散点图演示</h3>
                <canvas id="scatterChart"></canvas>
            </div>
        </div>
    </div>

    <script>
        // 渲染线图
        const lineCanvas = document.getElementById('lineChart');
        const lineCtx = lineCanvas.getContext('2d');
        lineCtx.strokeStyle = 'rgba(51, 153, 255, 1)';
        lineCtx.lineWidth = 2;
        lineCtx.beginPath();
        lineCtx.moveTo(50, 250);
        lineCtx.lineTo(100, 200);
        lineCtx.lineTo(150, 180);
        lineCtx.lineTo(200, 220);
        lineCtx.lineTo(250, 160);
        lineCtx.lineTo(300, 140);
        lineCtx.lineTo(350, 120);
        lineCtx.stroke();

        // 渲染柱图
        const barCanvas = document.getElementById('barChart');
        const barCtx = barCanvas.getContext('2d');
        const barData = [320, 280, 250, 200, 180];
        const barWidth = 60;
        barCtx.fillStyle = 'rgba(230, 102, 51, 1)';
        for (let i = 0; i < barData.length; i++) {
            const x = 50 + i * 80;
            const height = barData[i] * 0.6;
            const y = 250 - height;
            barCtx.fillRect(x, y, barWidth, height);
        }

        // 渲染饼图
        const pieCanvas = document.getElementById('pieChart');
        const pieCtx = pieCanvas.getContext('2d');
        const pieData = [60, 30, 10];
        const colors = ['rgba(51, 153, 255, 1)', 'rgba(230, 102, 51, 1)', 'rgba(102, 204, 102, 1)'];
        let startAngle = 0;
        const centerX = 200, centerY = 150, radius = 80;
        
        for (let i = 0; i < pieData.length; i++) {
            const sliceAngle = (pieData[i] / 100) * 2 * Math.PI;
            pieCtx.beginPath();
            pieCtx.moveTo(centerX, centerY);
            pieCtx.arc(centerX, centerY, radius, startAngle, startAngle + sliceAngle);
            pieCtx.closePath();
            pieCtx.fillStyle = colors[i];
            pieCtx.fill();
            pieCtx.strokeStyle = 'white';
            pieCtx.lineWidth = 2;
            pieCtx.stroke();
            startAngle += sliceAngle;
        }

        // 渲染散点图
        const scatterCanvas = document.getElementById('scatterChart');
        const scatterCtx = scatterCanvas.getContext('2d');
        const scatterData = [[10, 20], [15, 35], [20, 25], [25, 45], [30, 40], [35, 55], [40, 50]];
        scatterCtx.fillStyle = 'rgba(102, 204, 102, 1)';
        for (const [x, y] of scatterData) {
            scatterCtx.beginPath();
            scatterCtx.arc(x * 8 + 50, 250 - y * 3, 4, 0, 2 * Math.PI);
            scatterCtx.fill();
        }
    </script>
</body>
</html>"#;

    fs::write(format!("{}/comprehensive.html", output_dir), html_content)?;
    println!("  ✅ 综合演示页面已生成");
    Ok(())
}

/// 生成主页面
pub fn generate_index_page(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts-rs Web演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 50px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        .demo-card {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 30px;
            color: #333;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .demo-card:hover {
            transform: translateY(-5px);
        }
        .demo-icon {
            font-size: 3em;
            margin-bottom: 20px;
        }
        .demo-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            margin-top: 15px;
            transition: background 0.3s ease;
        }
        .demo-link:hover {
            background: #0056b3;
        }
        h1 { font-size: 3em; margin-bottom: 20px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .subtitle { font-size: 1.3em; opacity: 0.9; margin-bottom: 30px; }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 ECharts-rs</h1>
            <p class="subtitle">高性能的Rust数据可视化库</p>
            <div class="features">
                <div class="feature">
                    <h3>⚡ 高性能</h3>
                    <p>Rust零成本抽象</p>
                </div>
                <div class="feature">
                    <h3>🎨 丰富图表</h3>
                    <p>多种图表类型</p>
                </div>
                <div class="feature">
                    <h3>🌐 Web支持</h3>
                    <p>Canvas渲染</p>
                </div>
                <div class="feature">
                    <h3>🔧 易于使用</h3>
                    <p>链式API设计</p>
                </div>
            </div>
        </div>
        
        <div class="demo-grid">
            <div class="demo-card">
                <div class="demo-icon">📈</div>
                <h3>线图演示</h3>
                <p>展示数据趋势和变化，支持平滑曲线和多系列数据</p>
                <a href="line_chart.html" class="demo-link">查看演示</a>
            </div>
            
            <div class="demo-card">
                <div class="demo-icon">📊</div>
                <h3>柱图演示</h3>
                <p>对比不同类别的数据，支持分组和堆叠柱图</p>
                <a href="bar_chart.html" class="demo-link">查看演示</a>
            </div>
            
            <div class="demo-card">
                <div class="demo-icon">🥧</div>
                <h3>饼图演示</h3>
                <p>展示数据占比关系，支持环形图和玫瑰图</p>
                <a href="pie_chart.html" class="demo-link">查看演示</a>
            </div>
            
            <div class="demo-card">
                <div class="demo-icon">🔵</div>
                <h3>散点图演示</h3>
                <p>展示数据点分布和相关性，支持气泡图效果</p>
                <a href="scatter_chart.html" class="demo-link">查看演示</a>
            </div>
            
            <div class="demo-card">
                <div class="demo-icon">🎨</div>
                <h3>综合演示</h3>
                <p>在一个页面中展示所有图表类型的渲染效果</p>
                <a href="comprehensive.html" class="demo-link">查看演示</a>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 50px; padding: 30px; background: rgba(255,255,255,0.1); border-radius: 15px;">
            <h2>🎉 ECharts-rs 特性</h2>
            <p>✅ 类型安全 • ✅ 高性能渲染 • ✅ 丰富图表 • ✅ Web支持 • ✅ 易于扩展</p>
            <p style="margin-top: 20px; opacity: 0.8;">
                基于Rust构建的现代化数据可视化解决方案
            </p>
        </div>
    </div>
</body>
</html>"#;

    fs::write(format!("{}/index.html", output_dir), html_content)?;
    println!("  ✅ 主页面已生成");
    Ok(())
}
