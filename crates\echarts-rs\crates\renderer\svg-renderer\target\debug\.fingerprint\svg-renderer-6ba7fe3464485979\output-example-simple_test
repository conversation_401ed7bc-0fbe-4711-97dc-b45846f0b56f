{"$message_type":"diagnostic","message":"unused import: `SvgRenderSettings`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"examples\\simple_test.rs","byte_start":244,"byte_end":261,"line_start":9,"line_end":9,"column_start":33,"column_end":50,"is_primary":true,"text":[{"text":"use svg_renderer::{Svg<PERSON><PERSON><PERSON>, SvgRenderSettings};","highlight_start":33,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"examples\\simple_test.rs","byte_start":242,"byte_end":261,"line_start":9,"line_end":9,"column_start":31,"column_end":50,"is_primary":true,"text":[{"text":"use svg_renderer::{SvgRenderer, SvgRenderSettings};","highlight_start":31,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"examples\\simple_test.rs","byte_start":230,"byte_end":231,"line_start":9,"line_end":9,"column_start":19,"column_end":20,"is_primary":true,"text":[{"text":"use svg_renderer::{SvgRenderer, SvgRenderSettings};","highlight_start":19,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"examples\\simple_test.rs","byte_start":261,"byte_end":262,"line_start":9,"line_end":9,"column_start":50,"column_end":51,"is_primary":true,"text":[{"text":"use svg_renderer::{SvgRenderer, SvgRenderSettings};","highlight_start":50,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `SvgRenderSettings`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\simple_test.rs:9:33\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse svg_renderer::{SvgRenderer, SvgRenderSettings};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"1 warning emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 1 warning emitted\u001b[0m\n\n"}
