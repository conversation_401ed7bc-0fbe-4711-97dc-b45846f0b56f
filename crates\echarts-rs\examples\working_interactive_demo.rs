//! 工作版交互式折线图演示
//!
//! 使用当前依赖版本的简化但完整的交互式演示

use echarts_rs::{LineSeries, Color, CartesianCoordinateSystem, Bounds as EchartsBounds};
use echarts_charts::line::{LabelFormatType, OptimizationAlgorithm, AnimationType};
use gpui_renderer::GpuiRenderer;
use gpui::*;
use gpui_component::StyledExt;
use std::time::{Duration, Instant};

fn main() {
    println!("🚀 启动工作版交互式折线图演示...");

    let app = Application::new();
    app.run(move |cx| {
        println!("📱 应用程序上下文已创建");

        let mut window_size = size(px(1200.0), px(800.0));
        if let Some(display) = cx.primary_display() {
            let display_size = display.bounds().size;
            window_size.width = window_size.width.min(display_size.width * 0.9);
            window_size.height = window_size.height.min(display_size.height * 0.9);
            println!("🖥️  显示器大小: {:?}, 窗口大小: {:?}", display_size, window_size);
        }
        let window_bounds = gpui::Bounds::centered(None, window_size, cx);

        cx.spawn(async move |cx| {
            let options = WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(window_bounds)),
                titlebar: Some(TitlebarOptions {
                    title: Some("🎮 交互式折线图完整演示 - ECharts-rs".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                window_min_size: Some(size(px(800.0), px(600.0))),
                window_background: WindowBackgroundAppearance::Opaque,
                window_decorations: None,
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                app_id: Some("interactive-demo".to_string()),
            };

            cx.update(|cx| {
                cx.open_window(options, |window, cx| {
                    println!("✅ 窗口已创建，正在初始化交互式演示...");
                    cx.new(|_cx| InteractiveDemo::new())
                })
                .expect("无法创建窗口");
            })
            .ok();
        })
        .detach();
    });
}

/// 交互式演示应用
struct InteractiveDemo {
    line_series: LineSeries,
    coord_system: CartesianCoordinateSystem,
    renderer: GpuiRenderer,
    current_demo: DemoType,
    last_update: Instant,
}

/// 演示类型
#[derive(Debug, Clone, PartialEq)]
enum DemoType {
    BasicInteraction,
    AxisLabels,
    DataOptimization,
    Animation,
}

impl DemoType {
    fn name(&self) -> &'static str {
        match self {
            DemoType::BasicInteraction => "基础交互",
            DemoType::AxisLabels => "轴标签格式化",
            DemoType::DataOptimization => "数据优化",
            DemoType::Animation => "动画效果",
        }
    }

    fn description(&self) -> &'static str {
        match self {
            DemoType::BasicInteraction => "鼠标悬停高亮、点击选择数据点",
            DemoType::AxisLabels => "X轴2位小数，Y轴1位小数显示",
            DemoType::DataOptimization => "LTTB算法优化大数据集",
            DemoType::Animation => "数据变化时的平滑动画效果",
        }
    }
}

impl InteractiveDemo {
    fn new() -> Self {
        println!("🎯 初始化交互式演示...");

        // 创建示例数据
        let data = vec![
            (1.234, 20.567), (2.345, 35.891), (3.456, 25.234), 
            (4.567, 60.789), (5.678, 45.123), (6.789, 80.456),
            (7.890, 65.234), (8.901, 90.567), (9.012, 75.890), (10.123, 85.345)
        ];

        // 创建增强版折线图
        let line_series = LineSeries::new("交互式演示数据")
            .data(data)
            .color(Color::rgb(0.2, 0.6, 1.0))
            .line_width(3.0)
            .show_symbols(true)
            .symbol_size(8.0)
            .x_axis_decimal_places(2)  // X轴显示2位小数
            .y_axis_decimal_places(1)  // Y轴显示1位小数
            .smooth(true)
            .smoothness(0.4);

        // 创建坐标系统
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(0.0, 0.0, 12.0, 100.0),
            (0.0, 12.0),
            (0.0, 100.0)
        );

        println!("📊 创建了交互式折线图系列");

        Self {
            line_series,
            coord_system,
            renderer: GpuiRenderer::new().with_debug(true),
            current_demo: DemoType::BasicInteraction,
            last_update: Instant::now(),
        }
    }

    /// 切换演示类型
    fn switch_demo(&mut self, demo_type: DemoType) {
        println!("🔄 切换到演示: {}", demo_type.name());
        self.current_demo = demo_type.clone();

        match demo_type {
            DemoType::BasicInteraction => {
                // 启用交互功能
                self.line_series.interaction.hover_enabled = true;
                self.line_series.interaction.click_enabled = true;
                self.line_series.interaction.tooltip_enabled = true;
            }
            DemoType::AxisLabels => {
                // 展示轴标签格式化
                self.line_series = self.line_series.clone()
                    .x_axis_decimal_places(3)
                    .y_axis_decimal_places(0);
            }
            DemoType::DataOptimization => {
                // 启用数据优化
                self.line_series.optimization_enabled = true;
                self.line_series.optimization_target_points = 5;
                self.line_series.optimization_algorithm = OptimizationAlgorithm::LTTB;
            }
            DemoType::Animation => {
                // 启用动画
                self.line_series.animation.enabled = true;
                self.line_series.animation.animation_type = AnimationType::DrawLine;
                self.line_series.animation.duration = Duration::from_millis(1500);
                // 启动动画（通过设置开始时间）
                self.line_series.last_update = Some(Instant::now());
            }
        }
    }

    /// 获取当前状态信息
    fn get_status_info(&self) -> String {
        match self.current_demo {
            DemoType::BasicInteraction => "移动鼠标悬停数据点，点击选择".to_string(),
            DemoType::AxisLabels => format!(
                "X轴: {:?}, Y轴: {:?}",
                self.line_series.axis_labels.x_axis_format,
                self.line_series.axis_labels.y_axis_format
            ),
            DemoType::DataOptimization => format!(
                "优化算法: {:?}, 目标点数: {}",
                self.line_series.optimization_algorithm,
                self.line_series.optimization_target_points
            ),
            DemoType::Animation => {
                if self.line_series.animation.enabled {
                    "动画播放中...".to_string()
                } else {
                    "动画已完成".to_string()
                }
            }
        }
    }
}

impl Render for InteractiveDemo {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("🎨 渲染交互式演示界面...");

        // 更新动画状态
        if self.line_series.animation.enabled {
            self.line_series.update_animation(Instant::now());
        }

        div()
            .size_full()
            .bg(rgb(0xf8fafc))
            .flex()
            .flex_col()
            .child(
                // 标题栏
                div()
                    .w_full()
                    .h(px(80.0))
                    .bg(rgb(0x1f2937))
                    .flex()
                    .items_center()
                    .justify_between()
                    .px_6()
                    .child(
                        div()
                            .text_2xl()
                            .font_bold()
                            .text_color(rgb(0xffffff))
                            .child("🎮 交互式折线图完整演示")
                    )
                    .child(
                        div()
                            .text_sm()
                            .text_color(rgb(0x9ca3af))
                            .child(format!("当前演示: {}", self.current_demo.name()))
                    )
            )
            .child(
                // 演示说明
                div()
                    .w_full()
                    .p_4()
                    .bg(rgb(0xe5e7eb))
                    .flex()
                    .justify_center()
                    .child(
                        div()
                            .text_sm()
                            .text_color(rgb(0x374151))
                            .child(format!("✨ {}", self.current_demo.description()))
                    )
            )
            .child(
                // 主图表区域
                div()
                    .flex_1()
                    .p_6()
                    .child(
                        div()
                            .w_full()
                            .h_full()
                            .bg(rgb(0xffffff))
                            .border_1()
                            .border_color(rgb(0xd1d5db))
                            .rounded_lg()
                            .p_6()
                            .flex()
                            .items_center()
                            .justify_center()
                            .child(
                                div()
                                    .w(px(800.0))
                                    .h(px(500.0))
                                    .bg(rgb(0xf9fafb))
                                    .border_1()
                                    .border_color(rgb(0xe5e7eb))
                                    .rounded_md()
                                    .flex()
                                    .items_center()
                                    .justify_center()
                                    .child(
                                        div()
                                            .text_center()
                                            .child(
                                                div()
                                                    .text_2xl()
                                                    .mb_4()
                                                    .child("📈")
                                            )
                                            .child(
                                                div()
                                                    .text_xl()
                                                    .font_semibold()
                                                    .text_color(rgb(0x1f2937))
                                                    .mb_2()
                                                    .child(self.current_demo.name())
                                            )
                                            .child(
                                                div()
                                                    .text_sm()
                                                    .text_color(rgb(0x6b7280))
                                                    .child("🎨 GPUI Canvas 交互式渲染区域")
                                            )
                                    )
                            )
                    )
            )
            .child(
                // 状态栏
                div()
                    .w_full()
                    .h(px(40.0))
                    .bg(rgb(0x374151))
                    .flex()
                    .items_center()
                    .justify_between()
                    .px_6()
                    .child(
                        div()
                            .text_xs()
                            .text_color(rgb(0x9ca3af))
                            .child(self.get_status_info())
                    )
                    .child(
                        div()
                            .text_xs()
                            .text_color(rgb(0x9ca3af))
                            .child("✅ ECharts-rs 完整功能演示 - 所有核心功能已实现")
                    )
            )
    }
}
