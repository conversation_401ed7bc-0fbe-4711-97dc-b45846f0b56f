//! 热力图SVG演示
//!
//! 生成各种热力图的SVG文件，展示HeatmapSeries的完整功能

use std::fs;
use echarts_rs::{HeatmapSeries, ColorMap, Color};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🔥 热力图SVG演示系统");
    println!("{}", "=".repeat(50));

    // 确保输出目录存在
    let output_dir = "temp/svg/heatmap_charts";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 基础矩阵热力图
    println!("\n🔥 1. 生成基础矩阵热力图...");
    generate_basic_matrix_heatmap(output_dir)?;

    // 2. 自定义颜色映射热力图
    println!("\n🎨 2. 生成自定义颜色映射热力图...");
    generate_custom_color_heatmap(output_dir)?;

    // 3. 数据点热力图
    println!("\n📊 3. 生成数据点热力图...");
    generate_data_point_heatmap(output_dir)?;

    // 4. 带标签的热力图
    println!("\n🏷️ 4. 生成带标签的热力图...");
    generate_labeled_heatmap(output_dir)?;

    // 5. 大型数据热力图
    println!("\n📈 5. 生成大型数据热力图...");
    generate_large_data_heatmap(output_dir)?;

    // 6. 生成展示页面
    println!("\n📄 6. 生成展示页面...");
    generate_heatmap_showcase(output_dir)?;

    println!("\n🎉 热力图SVG演示生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/heatmap_demo.html 查看演示", output_dir);

    Ok(())
}

/// 生成基础矩阵热力图
fn generate_basic_matrix_heatmap(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建一个简单的矩阵数据
    let matrix = vec![
        vec![1.0, 2.0, 3.0, 4.0, 5.0],
        vec![2.0, 4.0, 6.0, 8.0, 10.0],
        vec![3.0, 6.0, 9.0, 12.0, 15.0],
        vec![4.0, 8.0, 12.0, 16.0, 20.0],
        vec![5.0, 10.0, 15.0, 20.0, 25.0],
    ];

    let heatmap_series = HeatmapSeries::new("基础矩阵热力图")
        .from_matrix(matrix)
        .gap(1.0)
        .show_label(true);

    let svg = create_heatmap_svg(&heatmap_series, "基础矩阵热力图演示", 600.0, 400.0)?;
    fs::write(format!("{}/01_basic_matrix_heatmap.svg", output_dir), svg)?;
    
    println!("  ✅ 基础矩阵热力图生成完成");
    Ok(())
}

/// 生成自定义颜色映射热力图
fn generate_custom_color_heatmap(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建温度数据矩阵
    let matrix = vec![
        vec![-10.0, -5.0, 0.0, 5.0, 10.0],
        vec![-8.0, -3.0, 2.0, 7.0, 12.0],
        vec![-6.0, -1.0, 4.0, 9.0, 14.0],
        vec![-4.0, 1.0, 6.0, 11.0, 16.0],
        vec![-2.0, 3.0, 8.0, 13.0, 18.0],
    ];

    // 创建蓝-白-红颜色映射（温度色彩）
    let colors = vec![
        Color::rgb(0.0, 0.0, 1.0),   // 蓝色（冷）
        Color::rgb(1.0, 1.0, 1.0),   // 白色（中性）
        Color::rgb(1.0, 0.0, 0.0),   // 红色（热）
    ];
    let color_map = ColorMap::new(colors, (-10.0, 18.0));

    let heatmap_series = HeatmapSeries::new("温度分布热力图")
        .from_matrix(matrix)
        .color_map(color_map)
        .gap(2.0)
        .border(1.0, Color::rgb(0.5, 0.5, 0.5))
        .show_label(true);

    let svg = create_heatmap_svg(&heatmap_series, "自定义颜色映射热力图", 600.0, 400.0)?;
    fs::write(format!("{}/02_custom_color_heatmap.svg", output_dir), svg)?;
    
    println!("  ✅ 自定义颜色映射热力图生成完成");
    Ok(())
}

/// 生成数据点热力图
fn generate_data_point_heatmap(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建稀疏数据点
    let data = vec![
        (0, 0, 10.0), (1, 0, 15.0), (2, 0, 20.0), (3, 0, 25.0),
        (0, 1, 12.0), (1, 1, 18.0), (2, 1, 24.0), (3, 1, 30.0),
        (0, 2, 14.0), (1, 2, 21.0), (2, 2, 28.0), (3, 2, 35.0),
        (0, 3, 16.0), (1, 3, 24.0), (2, 3, 32.0), (3, 3, 40.0),
    ];

    let heatmap_series = HeatmapSeries::new("数据点热力图")
        .data(data)
        .gap(3.0)
        .border_radius(5.0)
        .show_label(false);

    let svg = create_heatmap_svg(&heatmap_series, "数据点热力图演示", 600.0, 400.0)?;
    fs::write(format!("{}/03_data_point_heatmap.svg", output_dir), svg)?;
    
    println!("  ✅ 数据点热力图生成完成");
    Ok(())
}

/// 生成带标签的热力图
fn generate_labeled_heatmap(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建相关性矩阵数据
    let matrix = vec![
        vec![1.00, 0.85, 0.72, 0.43],
        vec![0.85, 1.00, 0.91, 0.56],
        vec![0.72, 0.91, 1.00, 0.68],
        vec![0.43, 0.56, 0.68, 1.00],
    ];

    let heatmap_series = HeatmapSeries::new("相关性矩阵")
        .from_matrix(matrix)
        .gap(2.0)
        .show_label(true)
        .label_formatter("{:.2}");

    let svg = create_heatmap_svg(&heatmap_series, "带标签的相关性热力图", 600.0, 400.0)?;
    fs::write(format!("{}/04_labeled_heatmap.svg", output_dir), svg)?;
    
    println!("  ✅ 带标签的热力图生成完成");
    Ok(())
}

/// 生成大型数据热力图
fn generate_large_data_heatmap(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建10x10的随机数据矩阵
    let mut matrix = Vec::new();
    for i in 0..10 {
        let mut row = Vec::new();
        for j in 0..10 {
            // 使用简单的函数生成数据
            let value = ((i as f64 * 0.5).sin() + (j as f64 * 0.3).cos()) * 50.0 + 50.0;
            row.push(value);
        }
        matrix.push(row);
    }

    // 创建渐变颜色映射
    let colors = vec![
        Color::rgb(0.2, 0.0, 0.8),   // 深蓝
        Color::rgb(0.0, 0.8, 0.8),   // 青色
        Color::rgb(0.0, 0.8, 0.0),   // 绿色
        Color::rgb(0.8, 0.8, 0.0),   // 黄色
        Color::rgb(0.8, 0.0, 0.0),   // 红色
    ];
    let color_map = ColorMap::new(colors, (0.0, 100.0));

    let heatmap_series = HeatmapSeries::new("大型数据热力图")
        .from_matrix(matrix)
        .color_map(color_map)
        .gap(1.0)
        .show_label(false);

    let svg = create_heatmap_svg(&heatmap_series, "大型数据热力图演示", 800.0, 600.0)?;
    fs::write(format!("{}/05_large_data_heatmap.svg", output_dir), svg)?;
    
    println!("  ✅ 大型数据热力图生成完成");
    Ok(())
}

/// 创建热力图SVG
fn create_heatmap_svg(series: &HeatmapSeries, title: &str, width: f64, height: f64) -> std::result::Result<String, Box<dyn std::error::Error>> {
    use echarts_core::{CartesianCoordinateSystem, Bounds, Series};
    
    // 创建坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        echarts_core::Bounds {
            origin: echarts_core::Point { x: 50.0, y: 50.0 },
            size: echarts_core::Size { width: width - 100.0, height: height - 100.0 },
        },
        (0.0, 1.0),
        (0.0, 1.0),
    );
    
    // 获取渲染命令
    let commands = series.render_to_commands(&coord_system)?;
    
    // 生成SVG
    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 渲染命令
    for command in commands {
        render_heatmap_svg_command(&mut svg, &command);
    }
    
    svg.push_str("</svg>");
    Ok(svg)
}

/// 渲染热力图SVG命令
fn render_heatmap_svg_command(svg: &mut String, command: &echarts_core::DrawCommand) {
    use echarts_core::DrawCommand;
    
    match command {
        DrawCommand::Rect { bounds, style } => {
            let fill = style.fill.map(|c| format!("#{:02x}{:02x}{:02x}", 
                (c.r * 255.0) as u8, (c.g * 255.0) as u8, (c.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke = style.stroke.as_ref().map(|s| format!("#{:02x}{:02x}{:02x}", 
                (s.color.r * 255.0) as u8, (s.color.g * 255.0) as u8, (s.color.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke_width = style.stroke.as_ref().map(|s| s.width).unwrap_or(0.0);
            
            svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"{}\" stroke=\"{}\" stroke-width=\"{}\" opacity=\"{}\"/>\n",
                bounds.origin.x, bounds.origin.y, bounds.size.width, bounds.size.height, fill, stroke, stroke_width, style.opacity));
        }
        DrawCommand::Text { text, position, style } => {
            let color = format!("#{:02x}{:02x}{:02x}", 
                (style.color.r * 255.0) as u8, (style.color.g * 255.0) as u8, (style.color.b * 255.0) as u8);
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"{}\" fill=\"{}\" text-anchor=\"middle\" opacity=\"{}\">{}</text>\n", 
                position.x, position.y, style.font_size, color, style.opacity, text));
        }
        _ => {} // 忽略其他命令类型
    }
}

/// 生成展示页面
fn generate_heatmap_showcase(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts-rs 热力图演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .section {
            background: rgba(255,255,255,0.95);
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .chart-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        .chart-svg {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h2 { color: #333; margin-bottom: 20px; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        .description {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 ECharts-rs 热力图演示</h1>
            <p class="description">展现 HeatmapSeries 的强大功能和灵活配置</p>
            <div class="feature-list">
                <div class="feature">
                    <h3>🔥 矩阵热力图</h3>
                    <p>二维数据矩阵可视化</p>
                </div>
                <div class="feature">
                    <h3>🎨 颜色映射</h3>
                    <p>自定义颜色渐变方案</p>
                </div>
                <div class="feature">
                    <h3>📊 数据点热力图</h3>
                    <p>稀疏数据点展示</p>
                </div>
                <div class="feature">
                    <h3>🏷️ 标签系统</h3>
                    <p>数值标签和格式化</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔥 基础热力图功能</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">基础矩阵热力图</div>
                    <object class="chart-svg" data="01_basic_matrix_heatmap.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">自定义颜色映射热力图</div>
                    <object class="chart-svg" data="02_custom_color_heatmap.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 高级热力图功能</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">数据点热力图</div>
                    <object class="chart-svg" data="03_data_point_heatmap.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">带标签的相关性热力图</div>
                    <object class="chart-svg" data="04_labeled_heatmap.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📈 大型数据展示</h2>
            <div class="chart-item">
                <div class="chart-title">大型数据热力图</div>
                <object class="chart-svg" data="05_large_data_heatmap.svg" type="image/svg+xml">SVG不支持</object>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h2>🎉 HeatmapSeries 功能总结</h2>
            <p>ECharts-rs HeatmapSeries 完整功能展示：</p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>✅ <strong>矩阵热力图</strong> - 二维数据矩阵的直观展示</li>
                <li>✅ <strong>数据点热力图</strong> - 稀疏坐标数据的可视化</li>
                <li>✅ <strong>颜色映射系统</strong> - 灵活的颜色渐变配置</li>
                <li>✅ <strong>标签系统</strong> - 数值显示和精度控制</li>
                <li>✅ <strong>样式配置</strong> - 间隙、边框、圆角等</li>
                <li>✅ <strong>大数据支持</strong> - 高效的大型矩阵渲染</li>
                <li>✅ <strong>数据验证</strong> - 完整的数据处理和验证</li>
                <li>✅ <strong>高性能渲染</strong> - 优化的SVG输出</li>
            </ul>
        </div>
    </div>
</body>
</html>"#;

    fs::write(format!("{}/heatmap_demo.html", output_dir), html_content)?;
    Ok(())
}
