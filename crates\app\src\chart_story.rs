use echarts_rs::{
    RuntimeChart, LineSeries, BarSeries, PieSeries, ScatterSeries, Color,
    CartesianCoordinateSystem, Bounds as EchartsBounds, Series
};
use gpui::{
    div, prelude::FluentBuilder, px, App, AppContext,
    Context, Entity, FocusHandle, Focusable, IntoElement, ParentElement,
    Pixels, Render, ScrollHandle, Styled, Window,
};
use gpui_component::{
    dock::PanelControl, v_flex, h_flex, ActiveTheme, StyledExt,
};

pub struct ChartStory {
    focus_handle: FocusHandle,
    line_chart_basic: LineChart,
    line_chart_dots: LineChart,
    line_chart_multi: LineChart,
    line_chart_optimized: LineChart,
    line_chart_custom_style: LineChart,
    line_chart_outlier: LineChart, // 带异常值的大数据图表
    // 新增图表案例
    line_chart_ultra_dense: LineChart, // 超高密度数据
    line_chart_solid_grid: LineChart, // 实线网格样式
    line_chart_dashed_border: LineChart, // 虚线边框样式
    line_chart_grid_modes: LineChart, // 固定网格线数量
    line_chart_no_grid: LineChart, // 无网格线
}

impl ChartStory {
    fn new(_: &mut Window, cx: &mut Context<Self>) -> Self {
        // 生成基础测试数据
        let data_len = 100;
        let data = generate_test_data(data_len);
        // 使用100万数据测试性能，同时启用优化
        let large_data_len = 1000000; 
        
        // 优化方案1: 使用降采样后的数据而不是完整数据
        // 先生成完整数据，然后对其进行降采样
        let full_large_data = generate_test_data(large_data_len);
        // 降采样到4000点 - 保持视觉效果但大幅提高性能
        let large_data = optimize_data_pixels(&full_large_data, 4000);
        
        // 生成带异常值的大数据，同样使用降采样
        let mut full_outlier_data = generate_test_data(large_data_len);
        // 在中间位置添加一个非常明显的异常值(增大值以确保在降采样后仍然可见)
        let outlier_index = large_data_len / 2;
        if full_outlier_data.len() > outlier_index {
            // 使用更大的异常值，确保在降采样后能被保留并清晰显示
            full_outlier_data[outlier_index] = px(50.0); // 增大异常值
        }
        
        // 使用通用降采样函数处理带异常值的数据
        let outlier_data = optimize_data_pixels(&full_outlier_data, 4000);
        
        // 超高密度数据 - 百万级数据点
        let ultra_dense_len = 2000000; // 两百万数据点
        let full_ultra_dense = generate_test_data(ultra_dense_len);
        let ultra_dense_data = optimize_data_pixels(&full_ultra_dense, 5000);
        
        // 基本线图
        let basic_line_style = LineStyle {
            gap: 10.0 / data_len as f64,
            ..Default::default()
        };
        let basic_line_info = LineInfo {
            line_style: Some(basic_line_style),
            plot_data: Some(data.clone()),
            cached_path: None,
        };
        
        // 带点的线图
        let dots_line_info = LineInfo {
            line_style: Some(basic_line_style),
            plot_data: Some(data.clone()),
            cached_path: None,
        };
        
        // 多线图
        let mut multi_line_info: Vec<LineInfo> = Vec::new();
        multi_line_info.push(LineInfo {
            line_style: Some(LineStyle {
                color: gpui::blue(),
                gap: 10.0 / data_len as f64,
                width: 2,
            }),
            plot_data: Some(data.clone()),
            cached_path: None,
        });
        
        multi_line_info.push(LineInfo {
            line_style: Some(LineStyle {
                color: gpui::red(),
                gap: 10.0 / data_len as f64,
                width: 2,
            }),
            plot_data: Some(generate_cosine_data(data_len)),
            cached_path: None,
        });
        
        // 优化线图（大数据量，但已降采样）
        let optimized_line_info = LineInfo {
            line_style: Some(LineStyle {
                gap: 10.0 / large_data.len() as f64, // 使用降采样后的数据长度
                ..Default::default()
            }),
            plot_data: Some(large_data.clone()),
            cached_path: None,
        };
        
        // 带异常值的大数据线图（已降采样）
        let outlier_line_info = LineInfo {
            line_style: Some(LineStyle {
                color: gpui::blue(), // 使用不同颜色
                gap: 10.0 / outlier_data.len() as f64, // 使用降采样后的数据长度
                width: 2,
            }),
            plot_data: Some(outlier_data),
            cached_path: None,
        };
        
        // 自定义样式线图
        let custom_style_info = LineInfo {
            line_style: Some(LineStyle {
                color: gpui::green(),
                gap: 10.0 / data_len as f64,
                width: 3,
            }),
            plot_data: Some(generate_quadratic_data(data_len)),
            cached_path: None,
        };
        
        // 超高密度数据线图
        let ultra_dense_info = LineInfo {
            line_style: Some(LineStyle {
                color: gpui::blue(),
                gap: 10.0 / ultra_dense_data.len() as f64,
                width: 2,
            }),
            plot_data: Some(ultra_dense_data),
            cached_path: None,
        };
        
        // 实线网格样式线图
        let solid_grid_info = LineInfo {
            line_style: Some(LineStyle {
                color: gpui::blue(),
                gap: 10.0 / data_len as f64,
                width: 2,
            }),
            plot_data: Some(data.clone()),
            cached_path: None,
        };
        
        // 虚线边框样式线图
        let dashed_border_info = LineInfo {
            line_style: Some(LineStyle {
                color: gpui::green(),
                gap: 10.0 / data_len as f64,
                width: 2,
            }),
            plot_data: Some(data.clone()),
            cached_path: None,
        };
        
        // 固定网格线数量线图
        let grid_modes_info = LineInfo {
            line_style: Some(LineStyle {
                color: gpui::red(),
                gap: 10.0 / data_len as f64,
                width: 2,
            }),
            plot_data: Some(data.clone()),
            cached_path: None,
        };
        
        // 无网格线线图
        let no_grid_info = LineInfo {
            line_style: Some(LineStyle {
                color: gpui::blue(),
                gap: 10.0 / data_len as f64,
                width: 2,
            }),
            plot_data: Some(data.clone()),
            cached_path: None,
        };
        
        Self {
            focus_handle: cx.focus_handle(),
            line_chart_basic: LineChart::new(vec![basic_line_info]).optimize(false),
            line_chart_dots: LineChart::new(vec![dots_line_info]).dot().optimize(false),
            line_chart_multi: LineChart::new(multi_line_info).optimize(false),
            // 添加更多优化参数，提高大数据渲染性能
            line_chart_optimized: LineChart::new(vec![optimized_line_info])
                .optimize(true) // 启用数据降采样
                .max_fps(10)    // 进一步降低帧率以减轻负担
                .async_data_processing(true), // 启用异步数据处理
            line_chart_custom_style: LineChart::new(vec![custom_style_info]).optimize(false),
            // 带异常值的大数据图表
            line_chart_outlier: LineChart::new(vec![outlier_line_info])
                .optimize(true)
                .max_fps(10)    // 进一步降低帧率以减轻负担
                .async_data_processing(true),
            // 新增案例
            // 超高密度数据线图
            line_chart_ultra_dense: LineChart::new(vec![ultra_dense_info])
                .optimize(true)
                .max_fps(10)
                .async_data_processing(true),
            // 实线网格样式
            line_chart_solid_grid: LineChart::new(vec![solid_grid_info])
                .grid_solid(true) // 使用实线网格
                .optimize(false),
            // 虚线边框样式
            line_chart_dashed_border: LineChart::new(vec![dashed_border_info])
                .border_solid(false) // 使用虚线边框
                .optimize(false),
            // 固定网格线数量
            line_chart_grid_modes: LineChart::new(vec![grid_modes_info])
                .x_grid_mode(GridMode::Fixed(6)) // X轴固定6条网格线
                .y_grid_mode(GridMode::Fixed(8)) // Y轴固定8条网格线
                .optimize(false),
            // 无网格线
            line_chart_no_grid: LineChart::new(vec![no_grid_info])
                .x_grid_mode(GridMode::None) // X轴无网格线
                .y_grid_mode(GridMode::None) // Y轴无网格线
                .optimize(false),
        }
    }

    pub fn view(window: &mut Window, cx: &mut App) -> Entity<Self> {
        cx.new(|cx| Self::new(window, cx))
    }
}

impl super::Story for ChartStory {
    fn title() -> &'static str {
        "Chart"
    }

    fn description() -> &'static str {
        "Beautiful Charts & Graphs."
    }

    fn new_view(window: &mut Window, cx: &mut App) -> Entity<impl Render + Focusable> {
        Self::view(window, cx)
    }

    fn zoomable() -> Option<PanelControl> {
        None
    }
}

impl Focusable for ChartStory {
    fn focus_handle(&self, _: &App) -> FocusHandle {
        self.focus_handle.clone()
    }
}

fn chart_container(
    title: &str,
    chart: impl IntoElement,
    center: bool,
    cx: &mut Context<ChartStory>,
) -> impl IntoElement {
    v_flex()
        .h(px(400.0)) // 设置固定高度，让每个图表高度足够
        .w_full() // 使用全宽
        .border_1()
        .border_color(cx.theme().border)
        .rounded_lg()
        .p_4()
        .mb_8() // 添加底部外边距，增加图表之间的间隔
        .child(
            div()
                .when(center, |this| this.text_center())
                .font_semibold()
                .text_lg() // 增大标题字体
                .pb_2()
                .child(title.to_string()),
        )
        .child(
            div()
                .when(center, |this| this.text_center())
                .text_color(cx.theme().muted_foreground)
                .text_sm()
                .child("January-June 2025"),
        )
        .child(div().flex_1().py_4().child(chart))
        .child(
            div()
                .when(center, |this| this.text_center())
                .font_semibold()
                .text_sm()
                .child("Trending up by 5.2% this month"),
        )
        .child(
            div()
                .when(center, |this| this.text_center())
                .text_color(cx.theme().muted_foreground)
                .text_sm()
                .child("Showing total visitors for the last 6 months"),
        )
}

// 生成正弦波测试数据
fn generate_test_data(n: usize) -> Vec<Pixels> {
    (0..n)
        .map(|i| {
            let x = i as f32 * 0.1;
            px(x.sin())
        })
        .collect()
}

// 生成余弦波测试数据
fn generate_cosine_data(n: usize) -> Vec<Pixels> {
    (0..n)
        .map(|i| {
            let x = i as f32 * 0.1;
            px(x.cos())
        })
        .collect()
}

// 生成二次函数测试数据
fn generate_quadratic_data(n: usize) -> Vec<Pixels> {
    (0..n)
        .map(|i| {
            let x = (i as f32 - n as f32 / 2.0) * 0.01;
            px(x * x)
        })
        .collect()
}

impl Render for ChartStory {
    fn render(&mut self, _: &mut Window, cx: &mut Context<Self>) -> impl IntoElement {
        div()
            .size_full()
            .overflow_hidden()
            .child(
                v_flex()
                    .w_full()
                    .p_6()
                    .gap_y_4()
                    .bg(cx.theme().background)
                    .child(
                        chart_container(
                            "基础线图",
                            self.line_chart_basic.clone(),
                            false,
                            cx,
                        )
                    )
                    .child(
                        chart_container(
                            "带点线图",
                            self.line_chart_dots.clone(),
                            false,
                            cx,
                        )
                    )
                    .child(
                        chart_container(
                            "多线图表",
                            self.line_chart_multi.clone(),
                            false,
                            cx,
                        )
                    )
                    .child(
                        chart_container(
                            "优化大数据线图 (降采样后4000点，原始100万)",
                            self.line_chart_optimized.clone(),
                            false,
                            cx,
                        )
                    )
                    .child(
                        chart_container(
                            "带异常值的大数据线图 (降采样后4000点，中间有异常值)",
                            self.line_chart_outlier.clone(),
                            false,
                            cx,
                        )
                    )
                    .child(
                        chart_container(
                            "自定义样式线图",
                            self.line_chart_custom_style.clone(),
                            false,
                            cx,
                        )
                    )
                    // 新增案例
                    .child(
                        chart_container(
                            "超高密度数据线图 (降采样后5000点，原始200万)",
                            self.line_chart_ultra_dense.clone(),
                            false,
                            cx,
                        )
                    )
                    .child(
                        chart_container(
                            "实线网格样式",
                            self.line_chart_solid_grid.clone(),
                            false,
                            cx,
                        )
                    )
                    .child(
                        chart_container(
                            "虚线边框样式",
                            self.line_chart_dashed_border.clone(),
                            false,
                            cx,
                        )
                    )
                    .child(
                        chart_container(
                            "固定网格线数量 (X轴6条，Y轴8条)",
                            self.line_chart_grid_modes.clone(),
                            false,
                            cx,
                        )
                    )
                    .child(
                        chart_container(
                            "无网格线样式",
                            self.line_chart_no_grid.clone(),
                            false,
                            cx,
                        )
                    )
            )
    }
}
