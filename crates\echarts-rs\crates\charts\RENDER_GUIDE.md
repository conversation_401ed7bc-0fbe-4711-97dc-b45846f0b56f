# LineSeries render 方法调用指南

## 🎯 概述

`LineSeries` 实现了 `Series` trait，其中的 `render` 方法是核心的渲染函数。本指南详细说明如何正确调用这个方法。

## 📋 方法签名

```rust
fn render(&self, ctx: &mut RenderContext, coord: &dyn CoordinateSystem) -> Result<()>
```

### 参数说明

1. **`ctx: &mut RenderContext`** - 渲染上下文
   - 包含渲染器、主题、绘制方法等
   - 提供 `draw_path()`, `draw_circle()`, `draw_text()` 等绘制方法

2. **`coord: &dyn CoordinateSystem`** - 坐标系统
   - 负责数据坐标到屏幕坐标的转换
   - 提供 `data_to_point()` 方法进行坐标转换

## 🚀 基本调用流程

### 1. 创建 LineSeries

```rust
use echarts_charts::LineSeries;
use echarts_core::*;

// 创建数据
let data = vec![
    (1.0, 120.0),
    (2.0, 200.0),
    (3.0, 150.0),
    (4.0, 80.0),
    (5.0, 110.0),
];

// 创建折线图系列
let line_series = LineSeries::new("销售数据")
    .data(data)
    .color(Color::BLUE)
    .line_width(2.0)
    .smooth(true)
    .show_symbols(true)
    .show_values(true);
```

### 2. 验证系列配置

```rust
// 验证系列配置是否正确
line_series.validate()?;
```

### 3. 准备渲染环境

```rust
// 创建渲染上下文（伪代码，需要具体实现）
let mut render_context = RenderContext::new(renderer, theme);

// 创建坐标系统（伪代码，需要具体实现）
let coordinate_system = CartesianCoordinateSystem::new(
    bounds,     // 绘制区域
    x_axis,     // X轴配置
    y_axis,     // Y轴配置
);
```

### 4. 调用 render 方法

```rust
// 执行渲染
line_series.render(&mut render_context, &coordinate_system)?;
```

## 🎨 render 方法内部流程

### 1. 获取颜色配置
```rust
let color = self.config.color.unwrap_or_else(|| ctx.theme.primary_color());
```

### 2. 计算屏幕坐标点
```rust
let points = self.calculate_points(coord)?;
```

### 3. 绘制面积（如果启用）
```rust
if self.area {
    let baseline_y = coord.bounds().bottom();
    let area_path = self.create_area_path(&points, baseline_y);
    let area_color = self.area_color.unwrap_or_else(|| color.with_alpha(self.area_opacity));
    ctx.draw_path(area_path, Some(area_color), None);
}
```

### 4. 绘制线条
```rust
let line_path = self.create_line_path(&points);
ctx.draw_path(line_path, None, Some((color, self.line_width)));
```

### 5. 绘制符号点
```rust
if self.show_symbols {
    for point in &points {
        if !point.x.is_nan() && !point.y.is_nan() {
            self.draw_symbol(ctx, *point, color);
        }
    }
}
```

### 6. 绘制数值标签
```rust
if self.show_values {
    for (i, point) in points.iter().enumerate() {
        if let Some(y_value) = self.data.points[i].get_number(1) {
            let label_pos = Point::new(point.x, point.y - self.symbol_size - 5.0);
            ctx.draw_text(format!("{:.1}", y_value), label_pos, text_style.clone());
        }
    }
}
```

## 📊 完整使用示例

### 示例1：简单折线图

```rust
pub fn render_simple_line_chart() -> Result<()> {
    // 1. 创建数据
    let data = vec![(1.0, 120.0), (2.0, 200.0), (3.0, 150.0)];

    // 2. 创建系列
    let line_series = LineSeries::new("销售数据")
        .data(data)
        .color(Color::BLUE)
        .smooth(true);

    // 3. 验证
    line_series.validate()?;

    // 4. 渲染（需要具体的渲染上下文和坐标系）
    // line_series.render(&mut render_context, &coordinate_system)?;

    Ok(())
}
```

### 示例2：面积图

```rust
pub fn render_area_chart() -> Result<()> {
    let data = vec![(1.0, 100.0), (2.0, 180.0), (3.0, 140.0)];

    let area_series = LineSeries::new("面积图")
        .data(data)
        .color(Color::GREEN)
        .area(true)
        .area_color(Color::GREEN.with_alpha(0.3))
        .smooth(true);

    area_series.validate()?;
    // area_series.render(&mut render_context, &coordinate_system)?;

    Ok(())
}
```

### 示例3：阶梯线图

```rust
pub fn render_step_chart() -> Result<()> {
    let data = vec![(1.0, 50.0), (2.0, 80.0), (3.0, 60.0)];

    let step_series = LineSeries::new("阶梯图")
        .data(data)
        .color(Color::RED)
        .step(StepType::Start)
        .line_width(3.0);

    step_series.validate()?;
    // step_series.render(&mut render_context, &coordinate_system)?;

    Ok(())
}
```

### 示例4：大数据集优化

```rust
pub fn render_optimized_chart() -> Result<()> {
    // 生成大量数据
    let large_data: Vec<(f64, f64)> = (0..10000)
        .map(|i| (i as f64, (i as f64 * 0.1).sin() * 100.0))
        .collect();

    let optimized_series = LineSeries::new_optimized("大数据", 1000)
        .data_optimized(large_data, Some(800.0))
        .color(Color::PURPLE)
        .show_symbols(false);

    optimized_series.validate()?;
    // optimized_series.render(&mut render_context, &coordinate_system)?;

    Ok(())
}
```

## 🔧 在图表引擎中的集成

### 多系列渲染

```rust
pub fn render_multiple_series() -> Result<()> {
    let series_list: Vec<Box<dyn Series>> = vec![
        Box::new(LineSeries::new("系列1").data(vec![(1.0, 100.0), (2.0, 150.0)])),
        Box::new(LineSeries::new("系列2").data(vec![(1.0, 80.0), (2.0, 130.0)])),
    ];

    // 渲染所有系列
    for series in &series_list {
        series.render(&mut render_context, &coordinate_system)?;
    }

    Ok(())
}
```

### 与 GPUI 渲染器集成

```rust
use echarts_renderer_gpui::GpuiRenderer;

pub fn render_with_gpui() -> Result<()> {
    let line_series = LineSeries::new("GPUI示例")
        .data(vec![(1.0, 100.0), (2.0, 150.0), (3.0, 120.0)])
        .color(Color::BLUE);

    // 使用 GPUI 渲染器
    let mut gpui_renderer = GpuiRenderer::new();
    
    // 创建渲染上下文
    let mut render_context = RenderContext::new(&mut gpui_renderer, theme);
    
    // 执行渲染
    line_series.render(&mut render_context, &coordinate_system)?;

    Ok(())
}
```

## ⚠️ 注意事项

1. **数据验证**：调用 `render` 前务必先调用 `validate()`
2. **坐标系统**：确保坐标系统正确配置了数据范围
3. **渲染上下文**：确保渲染器和主题正确初始化
4. **性能优化**：大数据集使用 `data_optimized()` 方法
5. **错误处理**：正确处理 `Result<()>` 返回值

## 🎉 总结

`LineSeries::render()` 方法是一个功能完整的渲染实现，支持：
- ✅ 基础线条绘制
- ✅ 平滑曲线
- ✅ 面积填充
- ✅ 阶梯线
- ✅ 符号点
- ✅ 数值标签
- ✅ 大数据优化
- ✅ 交互处理

通过正确的参数配置和调用流程，可以渲染出高质量的折线图表！
