#!/usr/bin/env python3
"""
批量修复 ChartBase 实现以支持泛型数据
"""

import os
import re

def fix_chart_base_impl(file_path, series_name, data_type, data_field="data"):
    """修复单个图表的 ChartBase 实现"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找 ChartBase 实现
        pattern = rf'/// 为 {series_name} 实现 ChartBase trait\nimpl ChartBase for {series_name} \{{'
        match = re.search(pattern, content)
        
        if not match:
            print(f"  ❌ 未找到 ChartBase 实现: {series_name}")
            return False
        
        # 构建新的实现
        new_impl = f"""/// 为 {series_name} 实现 ChartBase trait
impl ChartBase for {series_name} {{
    type DataType = {data_type};

    fn name(&self) -> &str {{
        &self.config.name
    }}
    
    fn raw_data(&self) -> &Self::DataType {{
        &self.{data_field}
    }}

    fn to_dataset(&self) -> DataSet {{
        // TODO: 实现 {data_type} 到 DataSet 的转换
        DataSet::new()
    }}
    
    fn visible(&self) -> bool {{
        self.config.visible
    }}
    
    fn z_index(&self) -> i32 {{
        self.config.z_index
    }}
    
    fn bounds(&self) -> Option<Bounds> {{
        // TODO: 为 {series_name} 实现边界计算
        None
    }}
    
    fn config(&self) -> &ChartConfig {{
        &self.config
    }}
    
    fn config_mut(&mut self) -> &mut ChartConfig {{
        &mut self.config
    }}
}}"""
        
        # 查找实现的结束位置
        start_pos = match.start()
        brace_count = 0
        end_pos = start_pos
        in_impl = False
        
        for i, char in enumerate(content[start_pos:], start_pos):
            if char == '{':
                brace_count += 1
                in_impl = True
            elif char == '}' and in_impl:
                brace_count -= 1
                if brace_count == 0:
                    end_pos = i + 1
                    break
        
        # 替换实现
        new_content = content[:start_pos] + new_impl + content[end_pos:]
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"  ✅ 已修复 {series_name}")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复失败 {series_name}: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复 ChartBase 实现...")
    
    # 需要修复的图表类型
    charts_to_fix = [
        ("crates/charts/src/gauge.rs", "GaugeSeries", "Vec<GaugeDataItem>"),
        ("crates/charts/src/treemap.rs", "TreemapSeries", "Vec<TreemapDataItem>"),
        ("crates/charts/src/sunburst.rs", "SunburstSeries", "Vec<SunburstDataItem>"),
        ("crates/charts/src/funnel.rs", "FunnelSeries", "Vec<FunnelDataItem>"),
        ("crates/charts/src/candlestick.rs", "CandlestickSeries", "Vec<CandlestickDataItem>"),
        ("crates/charts/src/heatmap.rs", "HeatmapSeries", "DataSet"),
        ("crates/charts/src/surface3d.rs", "Surface3DSeries", "DataSet"),
    ]
    
    fixed_count = 0
    for file_path, series_name, data_type in charts_to_fix:
        if os.path.exists(file_path):
            print(f"修复 {series_name}...")
            if fix_chart_base_impl(file_path, series_name, data_type):
                fixed_count += 1
        else:
            print(f"  ❌ 文件不存在: {file_path}")
    
    print(f"✅ 修复完成！共修复 {fixed_count} 个图表类型")

if __name__ == "__main__":
    main()
