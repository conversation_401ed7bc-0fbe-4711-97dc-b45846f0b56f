//! 专业交互系统
//!
//! 提供专业级的图表交互功能，包括缩放、平移、选择、测量等。
//! 专为大数据量和复杂交互场景设计。

use crate::{Bounds, Color, Point};
use std::collections::HashMap;

/// 交互事件类型
#[derive(Debug, Clone)]
pub enum InteractionEvent {
    /// 鼠标移动
    MouseMove {
        position: Point,
        modifiers: KeyModifiers,
    },

    /// 鼠标点击
    MouseClick {
        position: Point,
        button: MouseButton,
        modifiers: KeyModifiers,
    },

    /// 鼠标双击
    MouseDoubleClick {
        position: Point,
        button: MouseButton,
    },

    /// 鼠标滚轮
    MouseWheel {
        position: Point,
        delta: f64,
        modifiers: KeyModifiers,
    },

    /// 鼠标拖拽
    MouseDrag {
        start: Point,
        current: Point,
        button: MouseButton,
    },

    /// 键盘按键
    KeyPress { key: Key, modifiers: KeyModifiers },

    /// 触摸事件
    Touch { touches: Vec<TouchPoint> },

    /// 手势事件
    Gesture {
        gesture_type: GestureType,
        data: GestureData,
    },
}

/// 鼠标按键
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum MouseButton {
    Left,
    Right,
    Middle,
}

/// 键盘按键
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum Key {
    Escape,
    Space,
    Enter,
    Delete,
    Backspace,
    Arrow(ArrowKey),
    Function(u8), // F1-F12
    Character(char),
    Control,
    Shift,
    Alt,
    Meta,
}

/// 方向键
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum ArrowKey {
    Up,
    Down,
    Left,
    Right,
}

/// 键盘修饰键
#[derive(Debug, Clone, Copy, Default)]
pub struct KeyModifiers {
    pub ctrl: bool,
    pub shift: bool,
    pub alt: bool,
    pub meta: bool,
}

/// 触摸点
#[derive(Debug, Clone)]
pub struct TouchPoint {
    pub id: u64,
    pub position: Point,
    pub pressure: f64,
}

/// 手势类型
#[derive(Debug, Clone)]
pub enum GestureType {
    Pinch,
    Rotate,
    Swipe,
    LongPress,
}

/// 手势数据
#[derive(Debug, Clone)]
pub struct GestureData {
    pub center: Point,
    pub scale: f64,
    pub rotation: f64,
    pub velocity: Point,
}

/// 交互响应
#[derive(Debug, Clone)]
pub enum InteractionResponse {
    /// 无响应
    None,

    /// 需要重绘
    Redraw,

    /// 视口变化
    ViewportChanged { new_viewport: Viewport },

    /// 选择变化
    SelectionChanged { selected_items: Vec<SelectionItem> },

    /// 显示工具提示
    ShowTooltip {
        content: TooltipContent,
        position: Point,
    },

    /// 隐藏工具提示
    HideTooltip,

    /// 显示上下文菜单
    ShowContextMenu {
        items: Vec<ContextMenuItem>,
        position: Point,
    },

    /// 自定义响应
    Custom { data: HashMap<String, String> },
}

/// 视口信息
#[derive(Debug, Clone)]
pub struct Viewport {
    pub bounds: Bounds,
    pub zoom: f64,
    pub offset: Point,
    pub rotation: f64,
}

/// 选择项
#[derive(Debug, Clone)]
pub struct SelectionItem {
    pub id: String,
    pub item_type: String,
    pub bounds: Bounds,
    pub data: HashMap<String, String>,
}

/// 工具提示内容
#[derive(Debug, Clone)]
pub struct TooltipContent {
    pub title: String,
    pub items: Vec<TooltipItem>,
    pub style: TooltipStyle,
}

/// 工具提示项
#[derive(Debug, Clone)]
pub struct TooltipItem {
    pub label: String,
    pub value: String,
    pub color: Option<Color>,
}

/// 工具提示样式
#[derive(Debug, Clone)]
pub struct TooltipStyle {
    pub background_color: Color,
    pub text_color: Color,
    pub border_color: Color,
    pub font_size: f64,
    pub padding: f64,
    pub border_radius: f64,
}

/// 上下文菜单项
#[derive(Debug, Clone)]
pub struct ContextMenuItem {
    pub id: String,
    pub label: String,
    pub icon: Option<String>,
    pub enabled: bool,
    pub submenu: Option<Vec<ContextMenuItem>>,
}

/// 交互控制器特征
pub trait InteractionController {
    /// 处理交互事件
    fn handle_event(&mut self, event: InteractionEvent) -> InteractionResponse;

    /// 获取当前状态
    fn get_state(&self) -> InteractionState;

    /// 重置状态
    fn reset(&mut self);

    /// 是否启用
    fn is_enabled(&self) -> bool;

    /// 设置启用状态
    fn set_enabled(&mut self, enabled: bool);
}

/// 交互状态
#[derive(Debug, Clone)]
pub struct InteractionState {
    pub viewport: Viewport,
    pub selection: Vec<SelectionItem>,
    pub hover_item: Option<SelectionItem>,
    pub active_tool: Option<String>,
    pub cursor_position: Point,
}

/// 缩放平移控制器
pub struct ZoomPanController {
    enabled: bool,
    viewport: Viewport,
    min_zoom: f64,
    max_zoom: f64,
    zoom_sensitivity: f64,
    pan_sensitivity: f64,
    is_dragging: bool,
    drag_start: Point,
    last_position: Point,
}

impl ZoomPanController {
    pub fn new() -> Self {
        Self {
            enabled: true,
            viewport: Viewport {
                bounds: Bounds::zero(),
                zoom: 1.0,
                offset: Point::zero(),
                rotation: 0.0,
            },
            min_zoom: 0.1,
            max_zoom: 100.0,
            zoom_sensitivity: 0.1,
            pan_sensitivity: 1.0,
            is_dragging: false,
            drag_start: Point::zero(),
            last_position: Point::zero(),
        }
    }

    pub fn set_zoom_range(&mut self, min: f64, max: f64) {
        self.min_zoom = min;
        self.max_zoom = max;
    }

    pub fn set_sensitivity(&mut self, zoom: f64, pan: f64) {
        self.zoom_sensitivity = zoom;
        self.pan_sensitivity = pan;
    }

    pub fn zoom_to_fit(&mut self, content_bounds: Bounds, viewport_bounds: Bounds) {
        let scale_x = viewport_bounds.size.width / content_bounds.size.width;
        let scale_y = viewport_bounds.size.height / content_bounds.size.height;
        let scale = scale_x.min(scale_y) * 0.9; // 留一些边距

        self.viewport.zoom = scale.clamp(self.min_zoom, self.max_zoom);

        // 居中显示
        let scaled_content_width = content_bounds.size.width * self.viewport.zoom;
        let scaled_content_height = content_bounds.size.height * self.viewport.zoom;

        self.viewport.offset = Point::new(
            (viewport_bounds.size.width - scaled_content_width) / 2.0,
            (viewport_bounds.size.height - scaled_content_height) / 2.0,
        );
    }

    pub fn zoom_to_point(&mut self, point: Point, zoom_factor: f64) {
        let old_zoom = self.viewport.zoom;
        let new_zoom = (old_zoom * zoom_factor).clamp(self.min_zoom, self.max_zoom);

        if (new_zoom - old_zoom).abs() < f64::EPSILON {
            return;
        }

        // 计算缩放后的偏移，使指定点保持在相同位置
        let zoom_ratio = new_zoom / old_zoom;
        self.viewport.offset = Point::new(
            point.x - (point.x - self.viewport.offset.x) * zoom_ratio,
            point.y - (point.y - self.viewport.offset.y) * zoom_ratio,
        );

        self.viewport.zoom = new_zoom;
    }
}

impl InteractionController for ZoomPanController {
    fn handle_event(&mut self, event: InteractionEvent) -> InteractionResponse {
        if !self.enabled {
            return InteractionResponse::None;
        }

        match event {
            InteractionEvent::MouseWheel {
                position,
                delta,
                modifiers,
            } => {
                if modifiers.ctrl {
                    // Ctrl + 滚轮 = 缩放
                    let zoom_factor = if delta > 0.0 {
                        1.0 + self.zoom_sensitivity
                    } else {
                        1.0 - self.zoom_sensitivity
                    };
                    self.zoom_to_point(position, zoom_factor);
                    InteractionResponse::ViewportChanged {
                        new_viewport: self.viewport.clone(),
                    }
                } else {
                    // 普通滚轮 = 垂直平移
                    self.viewport.offset.y += delta * self.pan_sensitivity;
                    InteractionResponse::ViewportChanged {
                        new_viewport: self.viewport.clone(),
                    }
                }
            }

            InteractionEvent::MouseClick {
                position,
                button: MouseButton::Left,
                ..
            } => {
                self.is_dragging = true;
                self.drag_start = position;
                self.last_position = position;
                InteractionResponse::None
            }

            InteractionEvent::MouseDrag {
                current,
                button: MouseButton::Left,
                ..
            } => {
                if self.is_dragging {
                    let delta = Point::new(
                        current.x - self.last_position.x,
                        current.y - self.last_position.y,
                    );

                    self.viewport.offset.x += delta.x * self.pan_sensitivity;
                    self.viewport.offset.y += delta.y * self.pan_sensitivity;
                    self.last_position = current;

                    InteractionResponse::ViewportChanged {
                        new_viewport: self.viewport.clone(),
                    }
                } else {
                    InteractionResponse::None
                }
            }

            InteractionEvent::MouseMove { position, .. } => {
                if !self.is_dragging {
                    self.last_position = position;
                }
                InteractionResponse::None
            }

            _ => InteractionResponse::None,
        }
    }

    fn get_state(&self) -> InteractionState {
        InteractionState {
            viewport: self.viewport.clone(),
            selection: Vec::new(),
            hover_item: None,
            active_tool: Some("zoom_pan".to_string()),
            cursor_position: self.last_position,
        }
    }

    fn reset(&mut self) {
        self.viewport.zoom = 1.0;
        self.viewport.offset = Point::zero();
        self.is_dragging = false;
    }

    fn is_enabled(&self) -> bool {
        self.enabled
    }

    fn set_enabled(&mut self, enabled: bool) {
        self.enabled = enabled;
        if !enabled {
            self.is_dragging = false;
        }
    }
}

/// 选择控制器
pub struct SelectionController {
    enabled: bool,
    selection: Vec<SelectionItem>,
    hover_item: Option<SelectionItem>,
    selection_mode: SelectionMode,
    selection_bounds: Option<Bounds>,
}

/// 选择模式
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum SelectionMode {
    /// 单选
    Single,
    /// 多选
    Multiple,
    /// 矩形选择
    Rectangle,
    /// 套索选择
    Lasso,
}

impl SelectionController {
    pub fn new() -> Self {
        Self {
            enabled: true,
            selection: Vec::new(),
            hover_item: None,
            selection_mode: SelectionMode::Single,
            selection_bounds: None,
        }
    }

    pub fn set_selection_mode(&mut self, mode: SelectionMode) {
        self.selection_mode = mode;
    }

    pub fn clear_selection(&mut self) {
        self.selection.clear();
    }

    pub fn add_to_selection(&mut self, item: SelectionItem) {
        match self.selection_mode {
            SelectionMode::Single => {
                self.selection.clear();
                self.selection.push(item);
            }
            SelectionMode::Multiple => {
                if !self.selection.iter().any(|s| s.id == item.id) {
                    self.selection.push(item);
                }
            }
            _ => {}
        }
    }

    pub fn remove_from_selection(&mut self, item_id: &str) {
        self.selection.retain(|item| item.id != item_id);
    }
}

impl InteractionController for SelectionController {
    fn handle_event(&mut self, event: InteractionEvent) -> InteractionResponse {
        if !self.enabled {
            return InteractionResponse::None;
        }

        match event {
            InteractionEvent::MouseClick {
                position,
                button: MouseButton::Left,
                modifiers,
            } => {
                // 这里需要实际的命中测试逻辑
                // 简化示例：假设点击了某个项目
                let clicked_item = SelectionItem {
                    id: format!("item_at_{:.0}_{:.0}", position.x, position.y),
                    item_type: "data_point".to_string(),
                    bounds: Bounds::new(position.x - 5.0, position.y - 5.0, 10.0, 10.0),
                    data: HashMap::new(),
                };

                if modifiers.ctrl && self.selection_mode == SelectionMode::Multiple {
                    // Ctrl+点击 = 切换选择
                    if self.selection.iter().any(|s| s.id == clicked_item.id) {
                        self.remove_from_selection(&clicked_item.id);
                    } else {
                        self.add_to_selection(clicked_item);
                    }
                } else {
                    self.add_to_selection(clicked_item);
                }

                InteractionResponse::SelectionChanged {
                    selected_items: self.selection.clone(),
                }
            }

            InteractionEvent::MouseMove { position, .. } => {
                // 简化的悬停检测
                self.hover_item = Some(SelectionItem {
                    id: format!("hover_at_{:.0}_{:.0}", position.x, position.y),
                    item_type: "data_point".to_string(),
                    bounds: Bounds::new(position.x - 5.0, position.y - 5.0, 10.0, 10.0),
                    data: HashMap::new(),
                });

                InteractionResponse::None
            }

            _ => InteractionResponse::None,
        }
    }

    fn get_state(&self) -> InteractionState {
        InteractionState {
            viewport: Viewport {
                bounds: Bounds::zero(),
                zoom: 1.0,
                offset: Point::zero(),
                rotation: 0.0,
            },
            selection: self.selection.clone(),
            hover_item: self.hover_item.clone(),
            active_tool: Some("selection".to_string()),
            cursor_position: Point::zero(),
        }
    }

    fn reset(&mut self) {
        self.selection.clear();
        self.hover_item = None;
        self.selection_bounds = None;
    }

    fn is_enabled(&self) -> bool {
        self.enabled
    }

    fn set_enabled(&mut self, enabled: bool) {
        self.enabled = enabled;
    }
}
