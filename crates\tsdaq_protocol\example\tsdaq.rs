use std::time::Duration;

use futures::StreamExt;
use tsdaq_protocol::*; // 添加 StreamExt 导入

#[tokio::main]
async fn main() -> Result<(), ProtocolError> {
    // 创建传输层
    let serial_port_info = SerialTransport::available_ports();
    let transport = Box::new(SerialTransport::new(
        serial_port_info.last().unwrap().port_name.as_str(),
        115_200,
    )?);

    // 创建编解码器
    let codec = Box::new(StandardCodec::new(CheckMethod::Crc16));

    // 创建设备
    let device = Box::new(TSDevice::new(transport, codec));

    // 创建高级客户端
    let mut client = TSDAQClient::new(device);

    // 查询设备
    client.query_device().await?;
    println!("[INFO] Device queried successfully");

    for _ in 0..5 {
        let result = client.stop_collection().await;
        if result.is_ok() {
            break;
        }
    }

    tokio::time::sleep(Duration::from_millis(100)).await;

    // 连接设备
    client.connect().await?;
    println!("[INFO] Device connected");

    // // 读取配置
    // let config = client.read_configuration().await?;
    // println!("[CONFIG] Product: {}", config.product_name);
    // println!("[CONFIG] Serial: {}", config.serial_number);
    // println!("[CONFIG] Version: {}", config.software_version);
    // println!("[CONFIG] Sampling Rate: {} Hz", config.sampling_rate);
    // println!("[CONFIG] IP Config: {}", config.ip_address);

    // 开始采集
    client.start_collection().await?;
    println!("[INFO] Data collection started");

    // 处理数据流 - 使用 StreamExt 的方法

    // Collect data batches up to 10, then stop collection
    // let mut data_stream = client.stream_data().await;
    let mut data_stream = Box::pin(client.stream_data().await);
    let mut count = 0;

    while let Some(result) = data_stream.next().await {
        match result {
            Ok(channels) => {
                count += 1;
                // println!("[DATA] Batch #{}: {} channels", count, channels.len());

                for channel in &channels {
                    let f32_data = convert_bytes_to_f32(&channel.data, true);
                    println!(
                        " cnt:{} Channel {}: {} bytes, first value: {:?}",
                        count,
                        channel.ch,
                        channel.data.len(),
                        match &f32_data {
                            Ok(data) => data.get(0),
                            Err(e) => {
                                eprintln!("[ERROR] Failed to convert bytes to f32: {}", e);
                                None
                            }
                        }
                    );
                }
                if count >= 1000 {
                    eprintln!("停止采集:");
                    break;
                }
            }
            Err(e) => {
                eprintln!("[ERROR] Failed to read data: {}", e);
                // break;
            }
        }
    }

    // 这里不再发生 move 错误，可以安全调用 stop_collection
    client.stop_collection().await?;

    println!("[INFO] Data collection stopped");
    println!("------------------");

    Ok(())
}
