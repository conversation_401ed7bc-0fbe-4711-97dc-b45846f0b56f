//! 核心模块测试套件

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{Chart, Color, Point, Bounds, DataPoint, DataValue};

    #[test]
    fn test_chart_creation() {
        let chart = Chart::new();
        assert!(chart.title.is_none());
        assert!(chart.series.is_empty());
        assert_eq!(chart.background_color, None);
        
        println!("✅ 图表创建测试通过");
    }

    #[test]
    fn test_chart_with_title() {
        let chart = Chart::new().title("测试图表");

        assert!(chart.get_title().is_some());

        println!("✅ 图表标题设置测试通过");
    }

    #[test]
    fn test_color_creation() {
        // 测试RGB颜色创建
        let red = Color::rgb(1.0, 0.0, 0.0);
        assert_eq!(red.r, 1.0);
        assert_eq!(red.g, 0.0);
        assert_eq!(red.b, 0.0);
        assert_eq!(red.a, 1.0);

        // 测试RGBA颜色创建
        let transparent_blue = Color::rgba(0.0, 0.0, 1.0, 0.5);
        assert_eq!(transparent_blue.r, 0.0);
        assert_eq!(transparent_blue.g, 0.0);
        assert_eq!(transparent_blue.b, 1.0);
        assert_eq!(transparent_blue.a, 0.5);

        // 测试预定义颜色
        let white = Color::WHITE;
        assert_eq!(white.r, 1.0);
        assert_eq!(white.g, 1.0);
        assert_eq!(white.b, 1.0);
        assert_eq!(white.a, 1.0);

        println!("✅ 颜色创建测试通过");
    }

    #[test]
    fn test_point_operations() {
        let p1 = Point::new(10.0, 20.0);
        let p2 = Point::new(5.0, 15.0);

        // 测试点的基本属性
        assert_eq!(p1.x, 10.0);
        assert_eq!(p1.y, 20.0);

        // 测试点的距离计算
        let distance = p1.distance_to(p2);
        let expected = ((10.0_f64 - 5.0_f64).powi(2) + (20.0_f64 - 15.0_f64).powi(2)).sqrt();
        assert!((distance - expected).abs() < f64::EPSILON);

        println!("✅ 点操作测试通过");
    }

    #[test]
    fn test_bounds_operations() {
        let bounds = Bounds::new(10.0, 20.0, 100.0, 80.0);

        // 测试边界基本属性
        assert_eq!(bounds.origin.x, 10.0);
        assert_eq!(bounds.origin.y, 20.0);
        assert_eq!(bounds.width(), 100.0);
        assert_eq!(bounds.height(), 80.0);

        // 测试边界计算
        assert_eq!(bounds.right(), 110.0);
        assert_eq!(bounds.bottom(), 100.0);
        assert_eq!(bounds.center(), Point::new(60.0, 60.0));

        // 测试点是否在边界内
        let inside_point = Point::new(50.0, 50.0);
        let outside_point = Point::new(150.0, 150.0);

        assert!(bounds.contains_point(inside_point));
        assert!(!bounds.contains_point(outside_point));

        println!("✅ 边界操作测试通过");
    }

    #[test]
    fn test_data_point_creation() {
        // 测试数值数据点
        let numeric_point = DataPoint::new(vec![DataValue::Number(10.0), DataValue::Number(20.0)]);
        assert_eq!(numeric_point.x(), 10.0);
        assert_eq!(numeric_point.y(), 20.0);

        // 测试字符串数据点
        let string_point = DataPoint::new(vec![DataValue::String("A".to_string()), DataValue::Number(15.0)]);
        assert_eq!(string_point.y(), 15.0);

        println!("✅ 数据点创建测试通过");
    }

    #[test]
    fn test_data_value_operations() {
        let num_value = DataValue::Number(42.0);
        let str_value = DataValue::String("test".to_string());

        // 测试数值转换
        assert_eq!(num_value.as_number(), Some(42.0));
        assert_eq!(str_value.as_number(), None);

        // 测试字符串转换
        if let DataValue::String(s) = &str_value {
            assert_eq!(s, "test");
        }

        println!("✅ 数据值操作测试通过");
    }

    #[test]
    fn test_chart_data_operations() {
        let _chart = Chart::new();

        // 添加测试数据
        let test_data = vec![
            DataPoint::new(vec![DataValue::Number(1.0), DataValue::Number(10.0)]),
            DataPoint::new(vec![DataValue::Number(2.0), DataValue::Number(20.0)]),
            DataPoint::new(vec![DataValue::Number(3.0), DataValue::Number(15.0)]),
        ];

        // 验证数据范围计算
        let x_values: Vec<f64> = test_data.iter().map(|p| p.x()).collect();
        let y_values: Vec<f64> = test_data.iter().map(|p| p.y()).collect();

        let x_min = x_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let x_max = x_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let y_min = y_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let y_max = y_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));

        assert_eq!(x_min, 1.0);
        assert_eq!(x_max, 3.0);
        assert_eq!(y_min, 10.0);
        assert_eq!(y_max, 20.0);

        println!("✅ 图表数据操作测试通过");
    }

    #[test]
    fn test_color_operations() {
        let red = Color::rgb(1.0, 0.0, 0.0);
        let blue = Color::rgb(0.0, 0.0, 1.0);

        // 测试颜色基本属性
        assert_eq!(red.r, 1.0);
        assert_eq!(red.g, 0.0);
        assert_eq!(red.b, 0.0);

        assert_eq!(blue.r, 0.0);
        assert_eq!(blue.g, 0.0);
        assert_eq!(blue.b, 1.0);

        println!("✅ 颜色操作测试通过");
    }

    #[test]
    fn test_bounds_intersection() {
        let bounds1 = Bounds::new(0.0, 0.0, 100.0, 100.0);
        let bounds2 = Bounds::new(50.0, 50.0, 100.0, 100.0);
        let bounds3 = Bounds::new(200.0, 200.0, 100.0, 100.0);

        // 测试相交边界
        let intersection = bounds1.intersect(bounds2);
        // intersect方法返回Bounds，不是Option，所以直接测试结果
        assert_eq!(intersection.origin.x, 50.0);
        assert_eq!(intersection.origin.y, 50.0);
        assert_eq!(intersection.width(), 50.0);
        assert_eq!(intersection.height(), 50.0);

        // 测试不相交边界 - 这里简化测试，因为intersect总是返回Bounds
        let _no_intersection = bounds1.intersect(bounds3);

        println!("✅ 边界相交测试通过");
    }

    #[test]
    fn test_performance_data_processing() {
        use std::time::Instant;

        // 创建大量测试数据
        let start = Instant::now();
        let large_dataset: Vec<DataPoint> = (0..10000)
            .map(|i| DataPoint::new(vec![
                DataValue::Number(i as f64),
                DataValue::Number((i as f64).sin() * 100.0)
            ]))
            .collect();

        let creation_time = start.elapsed();

        // 测试数据处理性能
        let start = Instant::now();
        let _x_values: Vec<f64> = large_dataset.iter().map(|p| p.x()).collect();
        let _y_values: Vec<f64> = large_dataset.iter().map(|p| p.y()).collect();
        let processing_time = start.elapsed();

        assert_eq!(large_dataset.len(), 10000);

        println!("✅ 性能测试通过");
        println!("   - 数据创建时间: {:?}", creation_time);
        println!("   - 数据处理时间: {:?}", processing_time);
        println!("   - 数据点数量: {}", large_dataset.len());
    }

    #[test]
    fn test_error_handling() {
        // 测试无效数据处理
        let invalid_bounds = Bounds::new(0.0, 0.0, -10.0, -10.0);
        assert!(invalid_bounds.width() < 0.0);
        assert!(invalid_bounds.height() < 0.0);

        // 测试边界情况
        let zero_bounds = Bounds::new(0.0, 0.0, 0.0, 0.0);
        assert_eq!(zero_bounds.size.area(), 0.0);

        println!("✅ 错误处理测试通过");
    }

    #[test]
    fn test_chart_serialization() {
        let chart = Chart::new()
            .title("序列化测试")
            .background_color(Color::WHITE);

        // 测试序列化
        let json = chart.to_json().expect("Should serialize");
        assert!(json.is_object());

        println!("✅ 图表序列化测试通过");
    }
}
