//! Utility functions and helpers

use crate::{<PERSON><PERSON>, <PERSON>Error, DataV<PERSON>ue, Point};

/// Calculate the distance between two points
pub fn distance(p1: Point, p2: Point) -> f64 {
    ((p1.x - p2.x).powi(2) + (p1.y - p2.y).powi(2)).sqrt()
}

/// Check if a point is inside a rectangle
pub fn point_in_rect(point: Point, bounds: Bounds) -> bool {
    point.x >= bounds.origin.x
        && point.x <= bounds.origin.x + bounds.size.width
        && point.y >= bounds.origin.y
        && point.y <= bounds.origin.y + bounds.size.height
}

/// Clamp a value between min and max
pub fn clamp<T: PartialOrd>(value: T, min: T, max: T) -> T {
    if value < min {
        min
    } else if value > max {
        max
    } else {
        value
    }
}

/// Linear interpolation between two values
pub fn lerp(a: f64, b: f64, t: f64) -> f64 {
    a + (b - a) * t
}

/// Inverse linear interpolation (find t given a, b, and result)
pub fn inverse_lerp(a: f64, b: f64, value: f64) -> f64 {
    if (b - a).abs() < f64::EPSILON {
        0.5
    } else {
        (value - a) / (b - a)
    }
}

/// Map a value from one range to another
pub fn map_range(value: f64, from_min: f64, from_max: f64, to_min: f64, to_max: f64) -> f64 {
    let t = inverse_lerp(from_min, from_max, value);
    lerp(to_min, to_max, t)
}

/// Calculate the angle between two points in radians
pub fn angle_between_points(p1: Point, p2: Point) -> f64 {
    (p2.y - p1.y).atan2(p2.x - p1.x)
}

/// Rotate a point around another point by the given angle (in radians)
pub fn rotate_point(point: Point, center: Point, angle: f64) -> Point {
    let cos_a = angle.cos();
    let sin_a = angle.sin();

    let dx = point.x - center.x;
    let dy = point.y - center.y;

    Point::new(
        center.x + dx * cos_a - dy * sin_a,
        center.y + dx * sin_a + dy * cos_a,
    )
}

/// Calculate the centroid of a set of points
pub fn centroid(points: &[Point]) -> Point {
    if points.is_empty() {
        return Point::zero();
    }

    let sum = points
        .iter()
        .fold(Point::zero(), |acc, p| Point::new(acc.x + p.x, acc.y + p.y));

    Point::new(sum.x / points.len() as f64, sum.y / points.len() as f64)
}

/// Calculate the bounding box of a set of points
pub fn bounding_box(points: &[Point]) -> Option<Bounds> {
    if points.is_empty() {
        return None;
    }

    let mut min_x = f64::INFINITY;
    let mut max_x = f64::NEG_INFINITY;
    let mut min_y = f64::INFINITY;
    let mut max_y = f64::NEG_INFINITY;

    for point in points {
        min_x = min_x.min(point.x);
        max_x = max_x.max(point.x);
        min_y = min_y.min(point.y);
        max_y = max_y.max(point.y);
    }

    Some(Bounds::new(min_x, min_y, max_x - min_x, max_y - min_y))
}

/// Format a number for display
pub fn format_number(value: f64, precision: Option<usize>) -> String {
    match precision {
        Some(p) => format!("{:.1$}", value, p),
        None => {
            if value.fract() == 0.0 && value.abs() < 1e10 {
                format!("{:.0}", value)
            } else {
                format!("{:.2}", value)
            }
        }
    }
}

/// Format a data value for display
pub fn format_data_value(value: &DataValue) -> String {
    match value {
        DataValue::Number(n) => format_number(*n, None),
        DataValue::String(s) => s.clone(),
        DataValue::DateTime(dt) => dt.format("%Y-%m-%d %H:%M:%S").to_string(),
        DataValue::Boolean(b) => b.to_string(),
        DataValue::Point(x, y) => format!("({}, {})", x, y),
        DataValue::NameValue(name, value) => format!("{}: {}", name, value),
        DataValue::Null => "null".to_string(),
    }
}

/// Parse a CSS-style color string
pub fn parse_color(color_str: &str) -> Result<crate::Color, ChartError> {
    let color_str = color_str.trim();

    if color_str.starts_with('#') {
        crate::Color::from_hex(color_str)
            .map_err(|e| ChartError::Parse(format!("Invalid hex color: {}", e)))
    } else if color_str.starts_with("rgb(") && color_str.ends_with(')') {
        parse_rgb_color(color_str)
    } else if color_str.starts_with("rgba(") && color_str.ends_with(')') {
        parse_rgba_color(color_str)
    } else {
        // Try to parse as a named color
        parse_named_color(color_str)
    }
}

fn parse_rgb_color(color_str: &str) -> Result<crate::Color, ChartError> {
    let inner = &color_str[4..color_str.len() - 1];
    let parts: Vec<&str> = inner.split(',').map(|s| s.trim()).collect();

    if parts.len() != 3 {
        return Err(ChartError::Parse("RGB color must have 3 components".into()));
    }

    let r: u8 = parts[0]
        .parse()
        .map_err(|_| ChartError::Parse("Invalid red component".into()))?;
    let g: u8 = parts[1]
        .parse()
        .map_err(|_| ChartError::Parse("Invalid green component".into()))?;
    let b: u8 = parts[2]
        .parse()
        .map_err(|_| ChartError::Parse("Invalid blue component".into()))?;

    Ok(crate::Color::rgb_u8(r, g, b))
}

fn parse_rgba_color(color_str: &str) -> Result<crate::Color, ChartError> {
    let inner = &color_str[5..color_str.len() - 1];
    let parts: Vec<&str> = inner.split(',').map(|s| s.trim()).collect();

    if parts.len() != 4 {
        return Err(ChartError::Parse(
            "RGBA color must have 4 components".into(),
        ));
    }

    let r: u8 = parts[0]
        .parse()
        .map_err(|_| ChartError::Parse("Invalid red component".into()))?;
    let g: u8 = parts[1]
        .parse()
        .map_err(|_| ChartError::Parse("Invalid green component".into()))?;
    let b: u8 = parts[2]
        .parse()
        .map_err(|_| ChartError::Parse("Invalid blue component".into()))?;
    let a: f32 = parts[3]
        .parse()
        .map_err(|_| ChartError::Parse("Invalid alpha component".into()))?;

    Ok(crate::Color::rgba_u8(r, g, b, (a * 255.0) as u8))
}

fn parse_named_color(name: &str) -> Result<crate::Color, ChartError> {
    match name.to_lowercase().as_str() {
        "black" => Ok(crate::Color::BLACK),
        "white" => Ok(crate::Color::WHITE),
        "red" => Ok(crate::Color::RED),
        "green" => Ok(crate::Color::GREEN),
        "blue" => Ok(crate::Color::BLUE),
        "yellow" => Ok(crate::Color::YELLOW),
        "cyan" => Ok(crate::Color::CYAN),
        "magenta" => Ok(crate::Color::MAGENTA),
        "gray" | "grey" => Ok(crate::Color::GRAY),
        "lightgray" | "lightgrey" => Ok(crate::Color::LIGHT_GRAY),
        "darkgray" | "darkgrey" => Ok(crate::Color::DARK_GRAY),
        "transparent" => Ok(crate::Color::TRANSPARENT),
        _ => Err(ChartError::Parse(format!("Unknown color name: {}", name))),
    }
}

/// Generate a sequence of evenly spaced values
pub fn linspace(start: f64, end: f64, count: usize) -> Vec<f64> {
    if count == 0 {
        return Vec::new();
    }

    if count == 1 {
        return vec![start];
    }

    let step = (end - start) / (count - 1) as f64;
    (0..count).map(|i| start + i as f64 * step).collect()
}

/// Calculate the greatest common divisor
pub fn gcd(mut a: u64, mut b: u64) -> u64 {
    while b != 0 {
        let temp = b;
        b = a % b;
        a = temp;
    }
    a
}

/// Calculate the least common multiple
pub fn lcm(a: u64, b: u64) -> u64 {
    if a == 0 || b == 0 {
        0
    } else {
        (a * b) / gcd(a, b)
    }
}

/// Check if a number is approximately equal to another (within epsilon)
pub fn approx_equal(a: f64, b: f64, epsilon: f64) -> bool {
    (a - b).abs() < epsilon
}

/// Round a number to a specified number of decimal places
pub fn round_to_places(value: f64, places: u32) -> f64 {
    let multiplier = 10.0_f64.powi(places as i32);
    (value * multiplier).round() / multiplier
}

/// Convert degrees to radians
pub fn deg_to_rad(degrees: f64) -> f64 {
    degrees * std::f64::consts::PI / 180.0
}

/// Convert radians to degrees
pub fn rad_to_deg(radians: f64) -> f64 {
    radians * 180.0 / std::f64::consts::PI
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_distance() {
        let p1 = Point::new(0.0, 0.0);
        let p2 = Point::new(3.0, 4.0);
        assert_eq!(distance(p1, p2), 5.0);
    }

    #[test]
    fn test_point_in_rect() {
        let bounds = Bounds::new(10.0, 10.0, 20.0, 20.0);
        assert!(point_in_rect(Point::new(15.0, 15.0), bounds));
        assert!(!point_in_rect(Point::new(5.0, 5.0), bounds));
    }

    #[test]
    fn test_lerp() {
        assert_eq!(lerp(0.0, 10.0, 0.5), 5.0);
        assert_eq!(lerp(10.0, 20.0, 0.0), 10.0);
        assert_eq!(lerp(10.0, 20.0, 1.0), 20.0);
    }

    #[test]
    fn test_map_range() {
        assert_eq!(map_range(5.0, 0.0, 10.0, 0.0, 100.0), 50.0);
        assert_eq!(map_range(0.0, 0.0, 10.0, 100.0, 200.0), 100.0);
    }

    #[test]
    fn test_format_number() {
        assert_eq!(format_number(123.0, None), "123");
        assert_eq!(format_number(123.456, Some(2)), "123.46");
    }

    #[test]
    fn test_parse_color() {
        assert!(parse_color("#FF0000").is_ok());
        assert!(parse_color("rgb(255, 0, 0)").is_ok());
        assert!(parse_color("red").is_ok());
        assert!(parse_color("invalid").is_err());
    }

    #[test]
    fn test_linspace() {
        let values = linspace(0.0, 10.0, 5);
        assert_eq!(values, vec![0.0, 2.5, 5.0, 7.5, 10.0]);
    }
}
