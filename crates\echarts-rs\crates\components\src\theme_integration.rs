//! 主题集成系统
//!
//! 将坐标轴和网格样式与主题系统集成，支持一键切换主题

use crate::{Axis, Grid, ResponsiveGrid};
use echarts_core::{Color, LineStyle, TextStyle};
use echarts_themes::Theme;

/// 组件主题配置
#[derive(Debug, Clone)]
pub struct ComponentTheme {
    /// 坐标轴主题
    pub axis_theme: AxisTheme,
    /// 网格主题
    pub grid_theme: GridTheme,
    /// 文本主题
    pub text_theme: TextTheme,
}

/// 坐标轴主题配置
#[derive(Debug, Clone)]
pub struct AxisTheme {
    /// 轴线样式
    pub line_style: LineStyle,
    /// 刻度线样式
    pub tick_style: LineStyle,
    /// 标签文本样式
    pub label_style: TextStyle,
    /// 轴名称文本样式
    pub name_style: TextStyle,
}

/// 网格主题配置
#[derive(Debug, Clone)]
pub struct GridTheme {
    /// 网格线样式
    pub grid_line_style: LineStyle,
    /// 背景颜色
    pub background_color: Option<Color>,
    /// 边框颜色
    pub border_color: Option<Color>,
    /// 边框宽度
    pub border_width: f64,
}

/// 文本主题配置
#[derive(Debug, Clone)]
pub struct TextTheme {
    /// 主要文本样式
    pub primary_text: TextStyle,
    /// 次要文本样式
    pub secondary_text: TextStyle,
    /// 标题文本样式
    pub title_text: TextStyle,
}

impl ComponentTheme {
    /// 从 ECharts 主题创建组件主题
    pub fn from_echarts_theme(theme: &Theme) -> Self {
        Self {
            axis_theme: AxisTheme::from_echarts_theme(theme),
            grid_theme: GridTheme::from_echarts_theme(theme),
            text_theme: TextTheme::from_echarts_theme(theme),
        }
    }

    /// 应用到坐标轴
    pub fn apply_to_axis(&self, axis: &mut Axis) {
        axis.line_style = self.axis_theme.line_style.clone();
        axis.tick_style = self.axis_theme.tick_style.clone();
        axis.label_text_style = self.axis_theme.label_style.clone();
        axis.name_text_style = self.axis_theme.name_style.clone();
    }

    /// 应用到网格
    pub fn apply_to_grid(&self, grid: &mut Grid) {
        grid.grid_line_style = self.grid_theme.grid_line_style.clone();
        grid.background_color = self.grid_theme.background_color;
        grid.border_color = self.grid_theme.border_color;
        grid.border_width = self.grid_theme.border_width;
    }

    /// 应用到响应式网格
    pub fn apply_to_responsive_grid(&self, grid: &mut ResponsiveGrid) {
        self.apply_to_grid(grid.base_grid_mut());
    }
}

impl AxisTheme {
    /// 从 ECharts 主题创建坐标轴主题
    pub fn from_echarts_theme(theme: &Theme) -> Self {
        // 从主题的调色板中选择合适的颜色
        let primary_color = theme.color_palette.get(0).cloned()
            .unwrap_or(Color::rgb(0.2, 0.2, 0.2));
        let secondary_color = theme.color_palette.get(1).cloned()
            .unwrap_or(Color::rgb(0.5, 0.5, 0.5));

        Self {
            line_style: LineStyle {
                color: primary_color,
                width: 2.0,
                ..Default::default()
            },
            tick_style: LineStyle {
                color: primary_color,
                width: 1.5,
                ..Default::default()
            },
            label_style: TextStyle {
                color: secondary_color,
                font_size: theme.text_style.font_size * 0.9,
                ..theme.text_style.clone()
            },
            name_style: TextStyle {
                color: primary_color,
                font_size: theme.text_style.font_size * 1.1,
                ..theme.text_style.clone()
            },
        }
    }

    /// 创建亮色主题坐标轴
    pub fn light() -> Self {
        Self {
            line_style: LineStyle {
                color: Color::rgb(0.2, 0.2, 0.2),
                width: 2.0,
                ..Default::default()
            },
            tick_style: LineStyle {
                color: Color::rgb(0.3, 0.3, 0.3),
                width: 1.5,
                ..Default::default()
            },
            label_style: TextStyle {
                color: Color::rgb(0.4, 0.4, 0.4),
                font_size: 12.0,
                ..Default::default()
            },
            name_style: TextStyle {
                color: Color::rgb(0.2, 0.2, 0.2),
                font_size: 14.0,
                ..Default::default()
            },
        }
    }

    /// 创建暗色主题坐标轴
    pub fn dark() -> Self {
        Self {
            line_style: LineStyle {
                color: Color::rgb(0.8, 0.8, 0.8),
                width: 2.0,
                ..Default::default()
            },
            tick_style: LineStyle {
                color: Color::rgb(0.7, 0.7, 0.7),
                width: 1.5,
                ..Default::default()
            },
            label_style: TextStyle {
                color: Color::rgb(0.6, 0.6, 0.6),
                font_size: 12.0,
                ..Default::default()
            },
            name_style: TextStyle {
                color: Color::rgb(0.8, 0.8, 0.8),
                font_size: 14.0,
                ..Default::default()
            },
        }
    }
}

impl GridTheme {
    /// 从 ECharts 主题创建网格主题
    pub fn from_echarts_theme(theme: &Theme) -> Self {
        let grid_color = if theme.background_color.is_light() {
            Color::rgba(0.0, 0.0, 0.0, 0.1) // 亮色背景用深色网格
        } else {
            Color::rgba(1.0, 1.0, 1.0, 0.1) // 暗色背景用浅色网格
        };

        Self {
            grid_line_style: LineStyle {
                color: grid_color,
                width: 0.5,
                ..Default::default()
            },
            background_color: Some(theme.background_color.with_alpha(0.05)),
            border_color: Some(theme.color_palette.get(0).cloned()
                .unwrap_or(Color::rgb(0.3, 0.3, 0.3))),
            border_width: 1.0,
        }
    }

    /// 创建亮色主题网格
    pub fn light() -> Self {
        Self {
            grid_line_style: LineStyle {
                color: Color::rgba(0.0, 0.0, 0.0, 0.1),
                width: 0.5,
                ..Default::default()
            },
            background_color: Some(Color::rgba(0.95, 0.95, 0.95, 0.3)),
            border_color: Some(Color::rgb(0.8, 0.8, 0.8)),
            border_width: 1.0,
        }
    }

    /// 创建暗色主题网格
    pub fn dark() -> Self {
        Self {
            grid_line_style: LineStyle {
                color: Color::rgba(1.0, 1.0, 1.0, 0.1),
                width: 0.5,
                ..Default::default()
            },
            background_color: Some(Color::rgba(0.1, 0.1, 0.1, 0.3)),
            border_color: Some(Color::rgb(0.4, 0.4, 0.4)),
            border_width: 1.0,
        }
    }
}

impl TextTheme {
    /// 从 ECharts 主题创建文本主题
    pub fn from_echarts_theme(theme: &Theme) -> Self {
        Self {
            primary_text: theme.text_style.clone(),
            secondary_text: TextStyle {
                color: theme.text_style.color.with_alpha(0.7),
                font_size: theme.text_style.font_size * 0.9,
                ..theme.text_style.clone()
            },
            title_text: TextStyle {
                color: theme.text_style.color,
                font_size: theme.text_style.font_size * 1.2,
                ..theme.text_style.clone()
            },
        }
    }
}

/// 主题化的组件构建器
pub struct ThemedComponentBuilder {
    theme: ComponentTheme,
}

impl ThemedComponentBuilder {
    /// 创建新的主题化构建器
    pub fn new(theme: ComponentTheme) -> Self {
        Self { theme }
    }

    /// 从 ECharts 主题创建
    pub fn from_echarts_theme(theme: &Theme) -> Self {
        Self::new(ComponentTheme::from_echarts_theme(theme))
    }

    /// 创建主题化的坐标轴
    pub fn create_axis(&self, mut axis: Axis) -> Axis {
        self.theme.apply_to_axis(&mut axis);
        axis
    }

    /// 创建主题化的网格
    pub fn create_grid(&self, mut grid: Grid) -> Grid {
        self.theme.apply_to_grid(&mut grid);
        grid
    }

    /// 创建主题化的响应式网格
    pub fn create_responsive_grid(&self, mut grid: ResponsiveGrid) -> ResponsiveGrid {
        self.theme.apply_to_responsive_grid(&mut grid);
        grid
    }

    /// 获取主题
    pub fn theme(&self) -> &ComponentTheme {
        &self.theme
    }
}

/// 预定义的主题化组件
pub struct ThemedComponents;

impl ThemedComponents {
    /// 亮色主题的标准坐标轴对
    pub fn light_standard_axes() -> (Axis, Axis) {
        let theme = ComponentTheme {
            axis_theme: AxisTheme::light(),
            grid_theme: GridTheme::light(),
            text_theme: TextTheme::from_echarts_theme(&Theme::light()),
        };
        let builder = ThemedComponentBuilder::new(theme);
        
        let x_axis = builder.create_axis(crate::axis_presets::AxisPresets::standard_x_axis());
        let y_axis = builder.create_axis(crate::axis_presets::AxisPresets::standard_y_axis());
        
        (x_axis, y_axis)
    }

    /// 暗色主题的标准坐标轴对
    pub fn dark_standard_axes() -> (Axis, Axis) {
        let theme = ComponentTheme {
            axis_theme: AxisTheme::dark(),
            grid_theme: GridTheme::dark(),
            text_theme: TextTheme::from_echarts_theme(&Theme::dark()),
        };
        let builder = ThemedComponentBuilder::new(theme);
        
        let x_axis = builder.create_axis(crate::axis_presets::AxisPresets::standard_x_axis());
        let y_axis = builder.create_axis(crate::axis_presets::AxisPresets::standard_y_axis());
        
        (x_axis, y_axis)
    }

    /// 亮色主题的响应式网格
    pub fn light_responsive_grid() -> ResponsiveGrid {
        let theme = ComponentTheme {
            axis_theme: AxisTheme::light(),
            grid_theme: GridTheme::light(),
            text_theme: TextTheme::from_echarts_theme(&Theme::light()),
        };
        let builder = ThemedComponentBuilder::new(theme);
        
        builder.create_responsive_grid(ResponsiveGrid::new())
    }

    /// 暗色主题的响应式网格
    pub fn dark_responsive_grid() -> ResponsiveGrid {
        let theme = ComponentTheme {
            axis_theme: AxisTheme::dark(),
            grid_theme: GridTheme::dark(),
            text_theme: TextTheme::from_echarts_theme(&Theme::dark()),
        };
        let builder = ThemedComponentBuilder::new(theme);
        
        builder.create_responsive_grid(ResponsiveGrid::new())
    }
}
