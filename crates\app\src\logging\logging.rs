// src/logging.rs
use chrono::{NaiveDate, Utc};
use std::{
    fs::{self, File, OpenOptions},
    io::{self, LineWriter, Write},
    path::{Path, PathBuf},
    sync::mpsc,
    thread,
    time::Duration,
};
use tracing::{debug, error, info, warn, Level};
use tracing_appender::non_blocking;
use tracing_appender::non_blocking::WorkerGuard;
use tracing_subscriber::{
    fmt, layer::SubscriberExt, util::SubscriberInitExt, EnvFilter, Layer as _, Registry,
};

/// 日志系统配置
#[derive(Debug, Clone)]
pub struct LogConfig {
    pub log_dir: PathBuf,
    pub max_files: usize,
    pub terminal_level: Level,
    pub file_level: Level,
    pub high_freq_targets: Vec<String>,
}

/// 高频日志事件结构体
#[derive(Debug)]
struct HighFreqEvent {
    level: Level,
    message: String,
}

/// 自定义通道写入器
struct ChannelWriter(mpsc::Sender<HighFreqEvent>);

impl Write for ChannelWriter {
    fn write(&mut self, buf: &[u8]) -> io::Result<usize> {
        let message = String::from_utf8_lossy(buf).trim().to_string();

        if !message.is_empty() {
            self.0
                .send(HighFreqEvent {
                    level: Level::INFO,
                    message,
                })
                .map_err(|e| {
                    eprintln!("ChannelWriter发送失败: {}", e);
                    io::Error::new(io::ErrorKind::Other, e)
                })?;
        }
        Ok(buf.len())
    }

    fn flush(&mut self) -> io::Result<()> {
        Ok(())
    }
}

/// 自定义滚动文件追加器
struct DailyFileAppender {
    directory: PathBuf,
    prefix: String,
    current_date: Option<NaiveDate>,
    writer: Option<LineWriter<File>>,
}

impl DailyFileAppender {
    fn new(directory: PathBuf, prefix: &str) -> Self {
        Self {
            directory,
            prefix: prefix.to_string(),
            current_date: None,
            writer: None,
        }
    }

    fn roll(&mut self, date: NaiveDate) -> io::Result<()> {
        if self.current_date == Some(date) {
            return Ok(());
        }

        // 关闭当前写入器
        if let Some(mut writer) = self.writer.take() {
            let _ = writer.flush();
        }

        // 创建新文件名：prefix-YYYY-MM-DD.log
        let filename = format!("{}-{}.log", self.prefix, date.format("%Y-%m-%d"));
        let path = self.directory.join(&filename);

        // 打开新文件（追加模式）
        let file = OpenOptions::new().create(true).append(true).open(&path)?;

        self.writer = Some(LineWriter::new(file));
        self.current_date = Some(date);

        debug!("滚动到新日志文件: {}", filename);
        Ok(())
    }
}

impl Write for DailyFileAppender {
    fn write(&mut self, buf: &[u8]) -> io::Result<usize> {
        let current_date = Utc::now().date_naive();
        self.roll(current_date)?;

        match self.writer.as_mut() {
            Some(writer) => writer.write(buf),
            None => Err(io::Error::new(io::ErrorKind::Other, "no writer available")),
        }
    }

    fn flush(&mut self) -> io::Result<()> {
        if let Some(writer) = self.writer.as_mut() {
            writer.flush()
        } else {
            Ok(())
        }
    }
}

/// 清理旧的日志文件
fn cleanup_old_logs(log_dir: &Path, retain: usize) {
    if let Ok(entries) = fs::read_dir(log_dir) {
        let mut log_files = Vec::new();

        for entry in entries.filter_map(|e| e.ok()) {
            let path = entry.path();
            if let Some(ext) = path.extension() {
                if ext == "log" {
                    // 解析文件名中的日期
                    if let Some(file_stem) = path.file_stem().and_then(|s| s.to_str()) {
                        if let Some(date_str) = file_stem.split('-').last() {
                            if let Ok(date) = NaiveDate::parse_from_str(date_str, "%Y-%m-%d") {
                                log_files.push((path, date));
                            }
                        }
                    }
                }
            }
        }

        // 按日期排序（从旧到新）
        log_files.sort_by_key(|(_, date)| *date);

        let total = log_files.len();
        if total > retain {
            for (old_file, date) in log_files.into_iter().take(total - retain) {
                if let Err(e) = fs::remove_file(&old_file) {
                    eprintln!("删除旧日志失败 {}: {}", old_file.display(), e);
                } else {
                    debug!("已删除旧日志: {} (日期: {})", old_file.display(), date);
                }
            }
        }
    }
}

/// 初始化日志系统
pub fn init_logging(config: LogConfig) -> Vec<WorkerGuard> {
    // 1. 确保日志目录存在
    if let Err(e) = fs::create_dir_all(&config.log_dir) {
        eprintln!("无法创建日志目录 {}: {}", config.log_dir.display(), e);
        panic!("日志目录创建失败");
    }

    // 2. 直接测试日志目录写入权限
    let test_file = config.log_dir.join("write_test.log");
    if let Err(e) = fs::write(&test_file, "日志目录写入测试 - UTF-8: 你好世界\n") {
        eprintln!("无法写入日志目录 {}: {}", config.log_dir.display(), e);
        panic!("日志目录不可写");
    }
    fs::remove_file(&test_file).ok();

    let mut guards = Vec::new();
    let log_dir_clone = config.log_dir.clone();

    // 3. 文件输出层 - 使用自定义滚动
    let file_appender = DailyFileAppender::new(config.log_dir.clone(), "app");
    let (non_blocking_writer, file_guard) = non_blocking(file_appender);
    guards.push(file_guard);

    let file_layer = fmt::Layer::default()
        .with_ansi(false)
        .with_writer(non_blocking_writer)
        .with_timer(tracing_subscriber::fmt::time::uptime())
        .with_filter(
            EnvFilter::builder()
                .with_default_directive(config.file_level.into())
                .from_env_lossy(),
        );

    // 4. 高频日志层
    let (high_freq_tx, high_freq_rx) = mpsc::channel::<HighFreqEvent>();

    thread::spawn(move || {
        let mut file_appender = DailyFileAppender::new(log_dir_clone.clone(), "high_freq");

        while let Ok(event) = high_freq_rx.recv() {
            let result = writeln!(
                &mut file_appender,
                "[{}] [{}] {}",
                Utc::now().format("%Y-%m-%dT%H:%M:%S%.3fZ"),
                event.level,
                event.message
            );

            if let Err(e) = result {
                eprintln!("高频日志写入失败: {}", e);
            } else if let Err(e) = file_appender.flush() {
                eprintln!("高频日志刷新失败: {}", e);
            }
        }
    });

    let high_freq_layer = fmt::layer()
        .with_writer(move || ChannelWriter(high_freq_tx.clone()))
        .with_filter(
            EnvFilter::builder()
                .parse(&config.high_freq_targets.join(","))
                .unwrap_or_default(),
        );

    // 5. 控制台输出层
    let console_layer = fmt::layer()
        .compact()
        .with_timer(tracing_subscriber::fmt::time::uptime())
        .with_level(true)
        .with_target(true)
        .with_filter(
            EnvFilter::builder()
                .with_default_directive(config.terminal_level.into())
                .from_env_lossy(),
        );

    // 6. 初始化日志系统
    let subscriber = Registry::default()
        .with(file_layer) // 文件层优先
        .with(high_freq_layer) // 高频层次之
        .with(console_layer); // 控制台最后

    if let Err(e) = subscriber.try_init() {
        eprintln!("日志系统初始化失败: {}", e);
        panic!("无法初始化日志系统: {}", e);
    }

    // 7. 记录初始化成功消息
    info!("日志系统初始化完成 - UTF-8: 系统启动完成");
    debug!("日志目录: {:?}", config.log_dir);
    debug!("终端日志级别: {:?}", config.terminal_level);
    debug!("文件日志级别: {:?}", config.file_level);

    // 8. 清理旧日志文件
    cleanup_old_logs(&config.log_dir, config.max_files);

    guards
}

/// 多语言日志测试
pub(crate) fn test_logging() {
    // 测试不同语言的日志消息
    error!("错误测试 - 中文: 这是一个错误消息");
    warn!("警告测试 - 日本語: これは警告メッセージです");
    info!("信息测试 - English: This is an info message");
    debug!("调试测试 - Русский: Это отладочное сообщение");
    tracing::trace!("跟踪测试 - العربية: هذه رسالة تتبع");

    // 测试高频日志
    tracing::event!(
        target: "high_freq",
        Level::DEBUG,
        "高频日志测试 - 中文: 你好世界"
    );

    // 添加延迟确保写入完成
    thread::sleep(Duration::from_millis(500));
}

/// 检查日志文件内容
pub(crate) fn check_log_files(log_dir: &Path) {
    check_log_file_content(log_dir.join("app.log"));
    check_log_file_content(log_dir.join("high_freq.log"));
}

/// 检查单个日志文件内容
fn check_log_file_content(path: PathBuf) {
    match fs::read_to_string(&path) {
        Ok(content) => {
            if content.is_empty() {
                eprintln!("文件 {} 创建但内容为空", path.display());
            } else {
                println!("文件 {} 包含内容: {} 字节", path.display(), content.len());

                // 打印文件内容
                println!("文件内容:\n{}", content);
            }
        }
        Err(e) => {
            eprintln!("无法读取文件 {}: {}", path.display(), e);
        }
    }
}

/// 高频日志宏
#[macro_export]
macro_rules! hf_log {
    ($level:expr, $($arg:tt)+) => {
        if tracing::enabled!($level) {
            tracing::event!(
                target: "high_freq",
                $level,
                $($arg)+
            );
        }
    };
}
