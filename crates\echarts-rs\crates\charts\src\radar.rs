//! 雷达图实现
//!
//! 提供多维数据的雷达图可视化功能

use echarts_core::{
    Color, DrawCommand, PathCommand, Point, Series, CoordinateSystem, SeriesType, DataSet, Bounds,
    draw_commands::{PathStyle, CircleStyle, FillRule},
    TextStyle, LineStyle, LineCap, LineJoin, FontWeight, FontStyle, TextAlign, TextBaseline,
    Result
};
use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};
use std::f64::consts::PI;

/// 雷达图数据项
#[derive(Debug, Clone)]
pub struct RadarDataItem {
    /// 数据名称
    pub name: String,
    /// 各维度的数值
    pub values: Vec<f64>,
    /// 颜色
    pub color: Option<Color>,
    /// 透明度
    pub opacity: f64,
}

impl RadarDataItem {
    /// 创建新的雷达图数据项
    pub fn new(name: impl Into<String>, values: Vec<f64>) -> Self {
        Self {
            name: name.into(),
            values,
            color: None,
            opacity: 1.0,
        }
    }

    /// 设置颜色
    pub fn color(mut self, color: Color) -> Self {
        self.color = Some(color);
        self
    }

    /// 设置透明度
    pub fn opacity(mut self, opacity: f64) -> Self {
        self.opacity = opacity.clamp(0.0, 1.0);
        self
    }
}

/// 雷达图指标配置
#[derive(Debug, Clone)]
pub struct RadarIndicator {
    /// 指标名称
    pub name: String,
    /// 最小值
    pub min: f64,
    /// 最大值
    pub max: f64,
    /// 颜色
    pub color: Option<Color>,
}

impl RadarIndicator {
    /// 创建新的雷达图指标
    pub fn new(name: impl Into<String>, min: f64, max: f64) -> Self {
        Self {
            name: name.into(),
            min,
            max,
            color: None,
        }
    }

    /// 设置颜色
    pub fn color(mut self, color: Color) -> Self {
        self.color = Some(color);
        self
    }
}

/// 雷达图系列
#[derive(Debug, Clone)]
pub struct RadarSeries {
    /// 基础配置
    config: ChartConfig,
    /// 数据项
    data: Vec<RadarDataItem>,
    /// 指标配置
    indicators: Vec<RadarIndicator>,
    /// 中心点位置 (x, y) 相对坐标 [0, 1]
    center: (f64, f64),
    /// 半径
    radius: f64,
    /// 是否显示标签
    show_label: bool,
    /// 网格线数量
    split_number: usize,
    /// 是否填充区域
    area_style: bool,
    /// 线条宽度
    line_width: f64,
    /// 透明度
    opacity: f64,
}

impl RadarSeries {
    /// 创建新的雷达图系列
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            config: ChartConfig {
                name: name.into(),
                ..ChartConfig::default()
            },
            data: Vec::new(),
            indicators: Vec::new(),
            center: (0.5, 0.5),
            radius: 0.75,
            show_label: true,
            split_number: 5,
            area_style: true,
            line_width: 2.0,
            opacity: 1.0,
        }
    }

    /// 设置数据
    pub fn data(mut self, data: Vec<RadarDataItem>) -> Self {
        self.data = data;
        self
    }

    /// 添加数据项
    pub fn add_data(mut self, item: RadarDataItem) -> Self {
        self.data.push(item);
        self
    }

    /// 设置指标
    pub fn indicators(mut self, indicators: Vec<RadarIndicator>) -> Self {
        self.indicators = indicators;
        self
    }

    /// 添加指标
    pub fn add_indicator(mut self, indicator: RadarIndicator) -> Self {
        self.indicators.push(indicator);
        self
    }

    /// 设置中心点
    pub fn center(mut self, x: f64, y: f64) -> Self {
        self.center = (x.clamp(0.0, 1.0), y.clamp(0.0, 1.0));
        self
    }

    /// 设置半径
    pub fn radius(mut self, radius: f64) -> Self {
        self.radius = radius.clamp(0.1, 1.0);
        self
    }

    /// 设置是否显示标签
    pub fn show_label(mut self, show: bool) -> Self {
        self.show_label = show;
        self
    }

    /// 设置网格线数量
    pub fn split_number(mut self, number: usize) -> Self {
        self.split_number = number.max(1);
        self
    }

    /// 设置是否填充区域
    pub fn area_style(mut self, area: bool) -> Self {
        self.area_style = area;
        self
    }

    /// 设置线条宽度
    pub fn line_width(mut self, width: f64) -> Self {
        self.line_width = width.max(0.0);
        self
    }

    /// 设置透明度
    pub fn opacity(mut self, opacity: f64) -> Self {
        self.opacity = opacity.clamp(0.0, 1.0);
        self
    }

    /// 获取系列名称
    pub fn name(&self) -> &str {
        &self.config.name
    }

    /// 计算雷达图点的坐标
    fn calculate_radar_point(&self, center: Point, radius: f64, angle: f64, value: f64, indicator: &RadarIndicator) -> Point {
        let normalized_value = (value - indicator.min) / (indicator.max - indicator.min);
        let actual_radius = radius * normalized_value.clamp(0.0, 1.0);
        
        Point {
            x: center.x + actual_radius * angle.cos(),
            y: center.y + actual_radius * angle.sin(),
        }
    }

    /// 生成雷达网格
    fn generate_radar_grid(&self, center: Point, radius: f64) -> Vec<DrawCommand> {
        let mut commands = Vec::new();
        
        if self.indicators.is_empty() {
            return commands;
        }

        let indicator_count = self.indicators.len();
        let angle_step = 2.0 * PI / indicator_count as f64;

        // 绘制同心圆网格
        for i in 1..=self.split_number {
            let grid_radius = radius * (i as f64 / self.split_number as f64);
            let mut path_commands = Vec::new();
            
            for j in 0..indicator_count {
                let angle = j as f64 * angle_step - PI / 2.0; // 从顶部开始
                let point = Point {
                    x: center.x + grid_radius * angle.cos(),
                    y: center.y + grid_radius * angle.sin(),
                };
                
                if j == 0 {
                    path_commands.push(PathCommand::MoveTo(point));
                } else {
                    path_commands.push(PathCommand::LineTo(point));
                }
            }
            path_commands.push(PathCommand::Close);
            
            commands.push(DrawCommand::Path {
                commands: path_commands,
                style: PathStyle {
                    fill: None,
                    stroke: Some(LineStyle {
                        color: Color::rgb(0.8, 0.8, 0.8),
                        width: 1.0,
                        dash_pattern: None,
                        cap: LineCap::Round,
                        join: LineJoin::Round,
                        opacity: 1.0,
                    }),
                    opacity: 0.5,
                    fill_rule: FillRule::NonZero,
                },
            });
        }

        // 绘制径向线
        for i in 0..indicator_count {
            let angle = i as f64 * angle_step - PI / 2.0;
            let end_point = Point {
                x: center.x + radius * angle.cos(),
                y: center.y + radius * angle.sin(),
            };
            
            commands.push(DrawCommand::Path {
                commands: vec![
                    PathCommand::MoveTo(center),
                    PathCommand::LineTo(end_point),
                ],
                style: PathStyle {
                    fill: None,
                    stroke: Some(LineStyle {
                        color: Color::rgb(0.8, 0.8, 0.8),
                        width: 1.0,
                        dash_pattern: None,
                        cap: LineCap::Round,
                        join: LineJoin::Round,
                        opacity: 1.0,
                    }),
                    opacity: 0.5,
                    fill_rule: FillRule::NonZero,
                },
            });
        }

        commands
    }

    /// 生成指标标签
    fn generate_indicator_labels(&self, center: Point, radius: f64) -> Vec<DrawCommand> {
        let mut commands = Vec::new();
        
        if !self.show_label || self.indicators.is_empty() {
            return commands;
        }

        let indicator_count = self.indicators.len();
        let angle_step = 2.0 * PI / indicator_count as f64;
        let label_radius = radius * 1.1; // 标签距离中心稍远一些

        for (i, indicator) in self.indicators.iter().enumerate() {
            let angle = i as f64 * angle_step - PI / 2.0;
            let label_point = Point {
                x: center.x + label_radius * angle.cos(),
                y: center.y + label_radius * angle.sin(),
            };
            
            commands.push(DrawCommand::Text {
                text: indicator.name.clone(),
                position: label_point,
                style: TextStyle {
                    font_family: "Arial".to_string(),
                    font_size: 12.0,
                    font_weight: FontWeight::Normal,
                    font_style: FontStyle::Normal,
                    color: indicator.color.unwrap_or(Color::rgb(0.3, 0.3, 0.3)),
                    opacity: self.opacity,
                    align: TextAlign::Center,
                    baseline: TextBaseline::Middle,
                    rotation: 0.0,
                    letter_spacing: 0.0,
                    line_height: 1.2,
                },
            });
        }

        commands
    }
}

impl Series for RadarSeries {
    fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();
        
        if self.indicators.is_empty() {
            return Err(echarts_core::ChartError::InvalidData("雷达图需要至少一个指标".to_string()).into());
        }

        // 获取绘图区域
        let bounds = coord_system.bounds();
        let chart_center = Point {
            x: bounds.origin.x + bounds.size.width * self.center.0,
            y: bounds.origin.y + bounds.size.height * self.center.1,
        };
        let chart_radius = (bounds.size.width.min(bounds.size.height) / 2.0) * self.radius;

        // 生成雷达网格
        commands.extend(self.generate_radar_grid(chart_center, chart_radius));

        // 生成指标标签
        commands.extend(self.generate_indicator_labels(chart_center, chart_radius));

        // 绘制数据系列
        let indicator_count = self.indicators.len();
        let angle_step = 2.0 * PI / indicator_count as f64;
        
        for (series_idx, data_item) in self.data.iter().enumerate() {
            if data_item.values.len() != indicator_count {
                continue; // 跳过数据不匹配的项
            }

            let mut path_commands = Vec::new();
            let series_color = data_item.color.unwrap_or_else(|| {
                // 默认颜色序列
                let colors = [
                    Color::rgb(0.2, 0.6, 1.0),
                    Color::rgb(1.0, 0.4, 0.4),
                    Color::rgb(0.4, 0.8, 0.4),
                    Color::rgb(1.0, 0.8, 0.2),
                    Color::rgb(0.8, 0.4, 1.0),
                ];
                colors[series_idx % colors.len()]
            });

            // 计算雷达图各点坐标
            for (i, &value) in data_item.values.iter().enumerate() {
                let angle = i as f64 * angle_step - PI / 2.0;
                let point = self.calculate_radar_point(chart_center, chart_radius, angle, value, &self.indicators[i]);
                
                if i == 0 {
                    path_commands.push(PathCommand::MoveTo(point));
                } else {
                    path_commands.push(PathCommand::LineTo(point));
                }
            }
            path_commands.push(PathCommand::Close);

            // 绘制填充区域
            if self.area_style {
                commands.push(DrawCommand::Path {
                    commands: path_commands.clone(),
                    style: PathStyle {
                        fill: Some(series_color),
                        stroke: None,
                        opacity: data_item.opacity * 0.3, // 填充区域半透明
                        fill_rule: FillRule::NonZero,
                    },
                });
            }

            // 绘制边框线
            commands.push(DrawCommand::Path {
                commands: path_commands,
                style: PathStyle {
                    fill: None,
                    stroke: Some(LineStyle {
                        color: series_color,
                        width: self.line_width,
                        dash_pattern: None,
                        cap: LineCap::Round,
                        join: LineJoin::Round,
                        opacity: 1.0,
                    }),
                    opacity: data_item.opacity,
                    fill_rule: FillRule::NonZero,
                },
            });

            // 绘制数据点
            for (i, &value) in data_item.values.iter().enumerate() {
                let angle = i as f64 * angle_step - PI / 2.0;
                let point = self.calculate_radar_point(chart_center, chart_radius, angle, value, &self.indicators[i]);
                
                commands.push(DrawCommand::Circle {
                    center: point,
                    radius: 3.0,
                    style: CircleStyle {
                        fill: Some(series_color),
                        stroke: Some(LineStyle {
                            color: Color::rgb(1.0, 1.0, 1.0),
                            width: 1.0,
                            dash_pattern: None,
                            cap: LineCap::Round,
                            join: LineJoin::Round,
                            opacity: 1.0,
                        }),
                        opacity: data_item.opacity,
                    },
                });
            }
        }

        Ok(commands)
    }

    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Radar
    }

    fn bounds(&self) -> Option<echarts_core::Bounds> {
        // 雷达图通常使用整个可用空间
        None
    }

    fn is_visible(&self) -> bool {
        true
    }

    fn z_index(&self) -> i32 {
        0
    }

    fn clone_series(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use echarts_core::{CartesianCoordinateSystem, Bounds, Size};

    #[test]
    fn test_radar_series_creation() {
        let series = RadarSeries::new("测试雷达图")
            .center(0.5, 0.5)
            .radius(0.8)
            .split_number(4)
            .show_label(true);

        assert_eq!(series.name(), "测试雷达图");
        assert_eq!(series.center, (0.5, 0.5));
        assert_eq!(series.radius, 0.8);
        assert_eq!(series.split_number, 4);
        assert!(series.show_label);
    }

    #[test]
    fn test_radar_data_item() {
        let data_item = RadarDataItem::new("系列1", vec![80.0, 90.0, 70.0, 85.0])
            .color(Color::rgb(1.0, 0.0, 0.0))
            .opacity(0.8);

        assert_eq!(data_item.name, "系列1");
        assert_eq!(data_item.values, vec![80.0, 90.0, 70.0, 85.0]);
        assert_eq!(data_item.color, Some(Color::rgb(1.0, 0.0, 0.0)));
        assert_eq!(data_item.opacity, 0.8);
    }

    #[test]
    fn test_radar_indicator() {
        let indicator = RadarIndicator::new("指标1", 0.0, 100.0)
            .color(Color::rgb(0.0, 1.0, 0.0));

        assert_eq!(indicator.name, "指标1");
        assert_eq!(indicator.min, 0.0);
        assert_eq!(indicator.max, 100.0);
        assert_eq!(indicator.color, Some(Color::rgb(0.0, 1.0, 0.0)));
    }

    #[test]
    fn test_radar_series_with_data() {
        let indicators = vec![
            RadarIndicator::new("指标1", 0.0, 100.0),
            RadarIndicator::new("指标2", 0.0, 100.0),
            RadarIndicator::new("指标3", 0.0, 100.0),
        ];

        let data = vec![
            RadarDataItem::new("系列1", vec![80.0, 90.0, 70.0]),
            RadarDataItem::new("系列2", vec![60.0, 70.0, 85.0]),
        ];

        let series = RadarSeries::new("雷达图")
            .indicators(indicators)
            .data(data)
            .area_style(true)
            .line_width(2.0);

        assert_eq!(series.indicators.len(), 3);
        assert_eq!(series.data.len(), 2);
        assert!(series.area_style);
        assert_eq!(series.line_width, 2.0);
    }

    #[test]
    fn test_radar_series_rendering() {
        let indicators = vec![
            RadarIndicator::new("指标1", 0.0, 100.0),
            RadarIndicator::new("指标2", 0.0, 100.0),
            RadarIndicator::new("指标3", 0.0, 100.0),
        ];

        let data = vec![
            RadarDataItem::new("系列1", vec![80.0, 90.0, 70.0]),
        ];

        let series = RadarSeries::new("雷达图")
            .indicators(indicators)
            .data(data)
            .show_label(true);

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 400.0, height: 400.0 },
            },
            (0.0, 100.0),
            (0.0, 100.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();

        // 应该生成多个命令：网格线 + 标签 + 数据系列
        assert!(!commands.is_empty());

        // 检查命令类型
        let mut path_count = 0;
        let mut text_count = 0;
        let mut circle_count = 0;

        for cmd in &commands {
            match cmd {
                DrawCommand::Path { .. } => path_count += 1,
                DrawCommand::Text { .. } => text_count += 1,
                DrawCommand::Circle { .. } => circle_count += 1,
                _ => {}
            }
        }

        assert!(path_count > 0); // 网格线和数据路径
        assert!(text_count > 0); // 指标标签
        assert!(circle_count > 0); // 数据点
    }

    #[test]
    fn test_radar_series_empty_indicators() {
        let series = RadarSeries::new("空雷达图");

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 400.0, height: 400.0 },
            },
            (0.0, 100.0),
            (0.0, 100.0),
        );

        let result = series.render_to_commands(&coord_system);
        assert!(result.is_err());
    }

    #[test]
    fn test_radar_point_calculation() {
        let indicator = RadarIndicator::new("测试", 0.0, 100.0);
        let series = RadarSeries::new("测试");

        let center = Point { x: 200.0, y: 200.0 };
        let radius = 100.0;
        let angle = 0.0; // 水平向右
        let value = 50.0; // 中间值

        let point = series.calculate_radar_point(center, radius, angle, value, &indicator);

        // 50% 的值应该在半径的一半位置
        assert!((point.x - 250.0).abs() < 1.0); // center.x + radius * 0.5
        assert!((point.y - 200.0).abs() < 1.0); // center.y (角度为0时y不变)
    }
}
// ============================================================================
// 图表基础架构集成 - ChartBase 实现
// ============================================================================

/// 为 RadarSeries 实现 ChartBase trait
impl ChartBase for RadarSeries {
    type DataType = Vec<RadarDataItem>;

    fn name(&self) -> &str {
        &self.config.name
    }

    fn raw_data(&self) -> &Self::DataType {
        &self.data
    }

    fn to_dataset(&self) -> DataSet {
        // 将雷达图数据转换为标准 DataSet
        let mut dataset = DataSet::new();
        for item in &self.data {
            let values: Vec<echarts_core::DataValue> = item.values.iter()
                .map(|v| echarts_core::DataValue::Number(*v))
                .collect();

            dataset = dataset.add_point(echarts_core::DataPoint {
                values,
                name: Some(item.name.clone()),
                properties: std::collections::HashMap::new(),
            });
        }
        dataset
    }

    fn visible(&self) -> bool {
        self.config.visible
    }

    fn z_index(&self) -> i32 {
        self.config.z_index
    }

    fn bounds(&self) -> Option<Bounds> {
        // 为雷达图计算边界
        if self.data.is_empty() {
            return None;
        }

        let mut min_val = f64::INFINITY;
        let mut max_val = f64::NEG_INFINITY;

        for item in &self.data {
            for value in &item.values {
                min_val = min_val.min(*value);
                max_val = max_val.max(*value);
            }
        }

        Some(Bounds {
            origin: Point { x: min_val, y: min_val },
            size: echarts_core::Size {
                width: max_val - min_val,
                height: max_val - min_val
            },
        })
    }

    fn config(&self) -> &ChartConfig {
        &self.config
    }

    fn config_mut(&mut self) -> &mut ChartConfig {
        &mut self.config
    }
}

/// 为 RadarSeries 实现 ChartSeries trait
impl ChartSeries for RadarSeries {
    // 所有方法都由默认实现提供，基于 ChartBase
}
