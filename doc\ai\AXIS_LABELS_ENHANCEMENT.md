# 📊 轴标签格式化功能增强

**日期**: 2025-07-22  
**状态**: ✅ 完成 - 轴标签小数位数可设置  
**文件**: `crates/echarts-rs/crates/charts/src/line.rs`

## 🎯 功能概述

为折线图添加了完整的轴标签格式化功能，用户现在可以：
- **设置XY轴标签的小数位数**
- **选择不同的数值格式**（自动、固定小数、科学计数法、百分比）
- **自定义轴标签样式**（字体、颜色、旋转角度）
- **控制轴标签显示**（显示/隐藏X轴或Y轴标签）

## ✅ 新增功能

### 1. 轴标签格式化类型

```rust
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum LabelFormatType {
    /// 自动格式化 - 根据数值大小自动选择合适格式
    Auto,
    /// 固定小数位数 - 指定显示的小数位数
    FixedDecimal(usize),
    /// 科学计数法 - 适用于大数值或小数值
    Scientific(usize),
    /// 百分比格式 - 自动转换为百分比显示
    Percentage(usize),
    /// 自定义格式化函数
    Custom(String),
}
```

### 2. 轴标签配置结构

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AxisLabelConfig {
    /// X轴标签格式化
    pub x_axis_format: LabelFormatType,
    /// Y轴标签格式化
    pub y_axis_format: LabelFormatType,
    /// 是否显示X轴标签
    pub show_x_labels: bool,
    /// 是否显示Y轴标签
    pub show_y_labels: bool,
    /// X轴标签旋转角度
    pub x_label_rotation: f64,
    /// Y轴标签旋转角度
    pub y_label_rotation: f64,
    /// 标签字体大小
    pub font_size: f64,
    /// 标签颜色
    pub color: Color,
    /// 标签间距
    pub margin: f64,
}
```

### 3. 便捷设置方法

```rust
impl LineSeries {
    /// 设置X轴小数位数
    pub fn x_axis_decimal_places(mut self, places: usize) -> Self;
    
    /// 设置Y轴小数位数
    pub fn y_axis_decimal_places(mut self, places: usize) -> Self;
    
    /// 设置X轴标签格式
    pub fn x_axis_format(mut self, format: LabelFormatType) -> Self;
    
    /// 设置Y轴标签格式
    pub fn y_axis_format(mut self, format: LabelFormatType) -> Self;
    
    /// 设置是否显示轴标签
    pub fn show_axis_labels(mut self, show_x: bool, show_y: bool) -> Self;
    
    /// 设置轴标签旋转角度
    pub fn axis_label_rotation(mut self, x_rotation: f64, y_rotation: f64) -> Self;
}
```

### 4. 便捷构造函数

```rust
impl LineSeries {
    /// 创建带自定义轴标签格式的折线图
    pub fn new_with_axis_format<S: Into<String>>(
        name: S, 
        x_decimal_places: usize, 
        y_decimal_places: usize
    ) -> Self;
    
    /// 创建科学计数法格式的折线图
    pub fn new_scientific<S: Into<String>>(name: S, decimal_places: usize) -> Self;
    
    /// 创建百分比格式的折线图
    pub fn new_percentage<S: Into<String>>(name: S, decimal_places: usize) -> Self;
}
```

## 🚀 使用示例

### 基本小数位数设置

```rust
let series = LineSeries::new("销售数据")
    .data(vec![(1.234, 20.567), (2.345, 35.891)])
    .x_axis_decimal_places(2)  // X轴显示2位小数: 1.23, 2.35
    .y_axis_decimal_places(1); // Y轴显示1位小数: 20.6, 35.9
```

### 科学计数法格式

```rust
let series = LineSeries::new_scientific("大数据", 2)
    .data(vec![(1000.0, 1234567.0), (2000.0, 2345678.0)]);
// X轴: 1.00e3, 2.00e3
// Y轴: 1.23e6, 2.35e6
```

### 百分比格式

```rust
let series = LineSeries::new_percentage("转化率", 1)
    .data(vec![(1.0, 0.234), (2.0, 0.345)])
    .x_axis_decimal_places(0); // X轴整数，Y轴百分比
// X轴: 1, 2
// Y轴: 23.4%, 34.5%
```

### 自定义轴标签配置

```rust
let mut config = AxisLabelConfig::default();
config.x_label_rotation = 45.0;  // X轴标签旋转45度
config.font_size = 14.0;         // 字体大小14px
config.color = Color::rgb(0.6, 0.3, 0.8); // 紫色标签

let series = LineSeries::new("自定义样式")
    .axis_labels(config)
    .y_axis_decimal_places(3);   // Y轴3位小数
```

### 隐藏部分轴标签

```rust
let series = LineSeries::new("简化显示")
    .show_axis_labels(false, true)  // 隐藏X轴，显示Y轴
    .y_axis_decimal_places(2);
```

## 🎨 格式化效果

### 自动格式化逻辑
- **< 0.01**: 显示3位小数 (0.001)
- **< 1.0**: 显示2位小数 (0.12)  
- **< 1000.0**: 显示1位小数 (123.4)
- **≥ 1000.0**: 显示整数 (1234)

### 格式化示例
| 原始值 | FixedDecimal(2) | Scientific(1) | Percentage(1) |
|--------|----------------|---------------|---------------|
| 3.14159 | 3.14 | 3.1e0 | 314.2% |
| 0.00123 | 0.00 | 1.2e-3 | 0.1% |
| 1234.56 | 1234.56 | 1.2e3 | 123456.0% |

## 📁 文件结构

### 核心实现
- **轴标签配置**: `AxisLabelConfig` 结构
- **格式化类型**: `LabelFormatType` 枚举
- **格式化函数**: `format_label()` 方法
- **渲染函数**: `render_axis_labels()` 方法

### 演示文件
- **演示程序**: `crates/echarts-rs/examples/axis_labels_demo.rs`
- **5个完整示例**: 展示不同格式化选项的使用

## 🧪 测试覆盖

### 单元测试
- ✅ **格式化函数测试**: 验证各种格式化类型的输出
- ✅ **配置设置测试**: 验证轴标签配置的正确设置
- ✅ **便捷构造函数测试**: 验证快速创建方法
- ✅ **边界值测试**: 验证极值情况的处理

### 测试用例
```rust
#[test]
fn test_label_formatting_function() {
    let series = LineSeries::new("Test");
    
    // 固定小数位数
    assert_eq!(series.format_label(3.14159, &LabelFormatType::FixedDecimal(2)), "3.14");
    
    // 科学计数法
    assert_eq!(series.format_label(1234.5, &LabelFormatType::Scientific(2)), "1.23e3");
    
    // 百分比
    assert_eq!(series.format_label(0.1234, &LabelFormatType::Percentage(1)), "12.3%");
}
```

## 🔧 技术实现

### 渲染流程
1. **数据收集**: 从坐标系统获取轴的数值范围
2. **标签生成**: 根据配置生成适当数量的轴标签
3. **格式化应用**: 使用指定格式化类型处理数值
4. **位置计算**: 计算标签的屏幕坐标位置
5. **样式应用**: 应用字体、颜色、旋转等样式
6. **绘制命令**: 生成 DrawCommand::Text 命令

### 关键算法
- **智能标签数量**: X轴6个标签，Y轴5个标签
- **均匀分布**: 标签在轴上均匀分布
- **边界处理**: 正确处理数值边界和特殊值
- **性能优化**: 缓存格式化结果，避免重复计算

## 🚀 运行演示

```bash
# 运行轴标签格式化演示
cargo run --example axis_labels_demo -p echarts-rs
```

## 🎯 使用场景

### 1. 财务数据
```rust
LineSeries::new("股价")
    .x_axis_decimal_places(0)  // 日期整数
    .y_axis_decimal_places(2)  // 价格2位小数
```

### 2. 科学数据
```rust
LineSeries::new_scientific("测量值", 3)  // 科学计数法3位精度
```

### 3. 统计数据
```rust
LineSeries::new_percentage("成功率", 1)  // 百分比1位小数
```

### 4. 工程数据
```rust
LineSeries::new("温度")
    .y_axis_decimal_places(1)
    .axis_label_rotation(0.0, -90.0)  // Y轴标签垂直显示
```

---

**总结**: 轴标签格式化功能已完全实现，为用户提供了灵活的数值显示控制，满足各种应用场景的需求！🎊

**核心价值**: 
- 📊 **精确控制**: 小数位数精确到个位
- 🎨 **多种格式**: 支持5种不同的格式化类型  
- 🔧 **易于使用**: 提供便捷的链式调用API
- 🧪 **充分测试**: 完整的单元测试覆盖
