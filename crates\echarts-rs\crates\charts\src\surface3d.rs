//! 3D曲面图系列实现
//!
//! 提供简化但实用的3D曲面图功能，使用等距投影和基本光照

use echarts_core::{
    Bounds, Color, CoordinateSystem, DataSet, DataValue, DataPoint, DrawCommand, Point, Result, Series, SeriesType,
    draw_commands::{PathCommand, PathStyle, FillRule},
    LineStyle, LineCap, LineJoin,
};
use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};
use serde::{Deserialize, Serialize};
use std::f64::consts::PI;

/// 3D点坐标
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct Point3D {
    pub x: f64,
    pub y: f64,
    pub z: f64,
}

impl Point3D {
    /// 创建新的3D点
    pub fn new(x: f64, y: f64, z: f64) -> Self {
        Self { x, y, z }
    }
    
    /// 零点
    pub fn zero() -> Self {
        Self::new(0.0, 0.0, 0.0)
    }
    
    /// 向量减法
    pub fn sub(&self, other: &Point3D) -> Point3D {
        Point3D::new(self.x - other.x, self.y - other.y, self.z - other.z)
    }
    
    /// 叉积（用于计算法向量）
    pub fn cross(&self, other: &Point3D) -> Point3D {
        Point3D::new(
            self.y * other.z - self.z * other.y,
            self.z * other.x - self.x * other.z,
            self.x * other.y - self.y * other.x,
        )
    }
    
    /// 点积（用于光照计算）
    pub fn dot(&self, other: &Point3D) -> f64 {
        self.x * other.x + self.y * other.y + self.z * other.z
    }
    
    /// 向量长度
    pub fn length(&self) -> f64 {
        (self.x * self.x + self.y * self.y + self.z * self.z).sqrt()
    }
    
    /// 单位向量
    pub fn normalize(&self) -> Point3D {
        let len = self.length();
        if len > 0.0 {
            Point3D::new(self.x / len, self.y / len, self.z / len)
        } else {
            *self
        }
    }
    
    /// 等距投影到2D（简化的3D投影）
    pub fn isometric_project(&self, scale: f64, offset: Point) -> Point {
        // 等距投影公式
        let x_2d = (self.x - self.z) * scale * 0.866; // cos(30°)
        let y_2d = (self.y + (self.x + self.z) * 0.5) * scale;
        
        Point::new(offset.x + x_2d, offset.y - y_2d)
    }
}

/// 3D三角形面片
#[derive(Debug, Clone)]
pub struct Triangle3D {
    /// 三个顶点
    pub vertices: [Point3D; 3],
    
    /// 法向量
    pub normal: Point3D,
    
    /// 中心点Z坐标（用于深度排序）
    pub center_z: f64,
}

impl Triangle3D {
    /// 创建新的三角形
    pub fn new(v1: Point3D, v2: Point3D, v3: Point3D) -> Self {
        // 计算法向量
        let edge1 = v2.sub(&v1);
        let edge2 = v3.sub(&v1);
        let normal = edge1.cross(&edge2).normalize();
        
        // 计算中心点Z坐标
        let center_z = (v1.z + v2.z + v3.z) / 3.0;
        
        Self {
            vertices: [v1, v2, v3],
            normal,
            center_z,
        }
    }
}

/// 简单光照配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SimpleLighting {
    /// 光源方向（单位向量）
    pub direction: Point3D,
    
    /// 环境光强度
    pub ambient: f64,
    
    /// 漫反射强度
    pub diffuse: f64,
}

impl Default for SimpleLighting {
    fn default() -> Self {
        Self {
            direction: Point3D::new(-1.0, -1.0, -1.0).normalize(),
            ambient: 0.3,
            diffuse: 0.7,
        }
    }
}

impl SimpleLighting {
    /// 计算光照强度
    pub fn calculate_intensity(&self, normal: &Point3D) -> f64 {
        let dot_product = (-self.direction.dot(normal)).max(0.0);
        (self.ambient + self.diffuse * dot_product).min(1.0)
    }
}

/// 3D曲面图系列
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Surface3DSeries {
    /// 基础配置

    pub config: ChartConfig,

    

    /// 图表数据

    pub data: DataSet,
    
    /// 网格分辨率 (width, height)
    pub grid_resolution: (usize, usize),
    
    /// 基础颜色
    pub base_color: Color,
    
    /// 光照配置
    pub lighting: SimpleLighting,
    
    /// 投影缩放
    pub scale: f64,
    
    /// X轴旋转角度（弧度）
    pub rotation_x: f64,
    
    /// Z轴旋转角度（弧度）
    pub rotation_z: f64,
    
    /// 是否显示线框
    pub wireframe: bool,
    
    /// 线框颜色
    pub wireframe_color: Color,
    
    /// 透明度
    pub opacity: f64,

    // visible, z_index 现在在 config 中
}

impl Surface3DSeries {
    /// 创建新的3D曲面图系列
    pub fn new<S: Into<String>>(name: S) -> Self {
        Self {
            config: ChartConfig {
                name: name.into(),
                ..ChartConfig::default()
            },
            data: DataSet::new(),
            grid_resolution: (20, 20),
            base_color: Color::rgb(0.5, 0.7, 1.0),
            lighting: SimpleLighting::default(),
            scale: 100.0,
            rotation_x: PI / 6.0, // 30度
            rotation_z: PI / 4.0, // 45度
            wireframe: false,
            wireframe_color: Color::rgb(0.2, 0.2, 0.2),
            opacity: 0.8,
        }
    }
    
    /// 从数学函数创建曲面
    pub fn from_function<F>(mut self, func: F, x_range: (f64, f64), y_range: (f64, f64)) -> Self
    where
        F: Fn(f64, f64) -> f64,
    {
        let mut dataset = DataSet::new();
        let (x_min, x_max) = x_range;
        let (y_min, y_max) = y_range;
        
        let x_step = (x_max - x_min) / (self.grid_resolution.0 - 1) as f64;
        let y_step = (y_max - y_min) / (self.grid_resolution.1 - 1) as f64;
        
        for j in 0..self.grid_resolution.1 {
            for i in 0..self.grid_resolution.0 {
                let x = x_min + i as f64 * x_step;
                let y = y_min + j as f64 * y_step;
                let z = func(x, y);
                
                let point = DataPoint::new(vec![
                    DataValue::Number(x),
                    DataValue::Number(y),
                    DataValue::Number(z),
                ]);
                dataset = dataset.add_point(point);
            }
        }
        
        self.data = dataset;
        self
    }
    
    /// 设置网格分辨率
    pub fn resolution(mut self, width: usize, height: usize) -> Self {
        self.grid_resolution = (width, height);
        self
    }
    
    /// 设置基础颜色
    pub fn color(mut self, color: Color) -> Self {
        self.base_color = color;
        self
    }
    
    /// 设置投影缩放
    pub fn scale(mut self, scale: f64) -> Self {
        self.scale = scale;
        self
    }
    
    /// 设置旋转角度（度数）
    pub fn rotation(mut self, x_degrees: f64, z_degrees: f64) -> Self {
        self.rotation_x = x_degrees * PI / 180.0;
        self.rotation_z = z_degrees * PI / 180.0;
        self
    }
    
    /// 设置线框显示
    pub fn wireframe(mut self, show: bool, color: Color) -> Self {
        self.wireframe = show;
        self.wireframe_color = color;
        self
    }
    
    /// 设置透明度
    pub fn opacity(mut self, opacity: f64) -> Self {
        self.opacity = opacity;
        self
    }
    
    /// 设置可见性
    pub fn visible(mut self, visible: bool) -> Self {
        self.config.visible = visible;
        self
    }
    
    /// 设置Z轴索引
    pub fn z_index(mut self, z_index: i32) -> Self {
        self.config.z_index = z_index;
        self
    }

    /// 应用3D旋转变换
    fn apply_rotation(&self, point: &Point3D) -> Point3D {
        // 先绕X轴旋转
        let cos_x = self.rotation_x.cos();
        let sin_x = self.rotation_x.sin();
        let rotated_x = Point3D::new(
            point.x,
            point.y * cos_x - point.z * sin_x,
            point.y * sin_x + point.z * cos_x,
        );

        // 再绕Z轴旋转
        let cos_z = self.rotation_z.cos();
        let sin_z = self.rotation_z.sin();
        Point3D::new(
            rotated_x.x * cos_z - rotated_x.y * sin_z,
            rotated_x.x * sin_z + rotated_x.y * cos_z,
            rotated_x.z,
        )
    }

    /// 生成三角形网格
    fn generate_triangles(&self) -> Vec<Triangle3D> {
        let mut triangles = Vec::new();
        let (width, height) = self.grid_resolution;

        if width < 2 || height < 2 || self.data.len() != width * height {
            return triangles;
        }

        // 从数据点创建3D点数组
        let mut points_3d = Vec::new();
        for point in self.data.points() {
            if let (Some(x), Some(y), Some(z)) = (
                point.get_number(0),
                point.get_number(1),
                point.get_number(2),
            ) {
                let point_3d = Point3D::new(x, y, z);
                let rotated = self.apply_rotation(&point_3d);
                points_3d.push(rotated);
            }
        }

        // 生成三角形（每个网格单元生成两个三角形）
        for j in 0..height - 1 {
            for i in 0..width - 1 {
                let idx_tl = j * width + i;         // 左上
                let idx_tr = j * width + i + 1;     // 右上
                let idx_bl = (j + 1) * width + i;   // 左下
                let idx_br = (j + 1) * width + i + 1; // 右下

                if idx_br < points_3d.len() {
                    // 第一个三角形：左上-右上-左下
                    triangles.push(Triangle3D::new(
                        points_3d[idx_tl],
                        points_3d[idx_tr],
                        points_3d[idx_bl],
                    ));

                    // 第二个三角形：右上-右下-左下
                    triangles.push(Triangle3D::new(
                        points_3d[idx_tr],
                        points_3d[idx_br],
                        points_3d[idx_bl],
                    ));
                }
            }
        }

        // 按深度排序（画家算法）
        triangles.sort_by(|a, b| b.center_z.partial_cmp(&a.center_z).unwrap_or(std::cmp::Ordering::Equal));

        triangles
    }

    /// 创建三角形路径命令
    fn create_triangle_path(&self, triangle: &Triangle3D, center: Point) -> Vec<PathCommand> {
        let mut commands = Vec::new();

        // 投影三个顶点到2D
        let p1 = triangle.vertices[0].isometric_project(self.scale, center);
        let p2 = triangle.vertices[1].isometric_project(self.scale, center);
        let p3 = triangle.vertices[2].isometric_project(self.scale, center);

        // 创建三角形路径
        commands.push(PathCommand::MoveTo(p1));
        commands.push(PathCommand::LineTo(p2));
        commands.push(PathCommand::LineTo(p3));
        commands.push(PathCommand::Close);

        commands
    }
}

/// 实现 Series trait
impl Series for Surface3DSeries {
    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Surface3D
    }

    fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        if !self.config.visible || self.data.is_empty() {
            return Ok(commands);
        }

        // 计算投影中心点
        let bounds = coord_system.bounds();
        let center = Point::new(
            bounds.origin.x + bounds.size.width / 2.0,
            bounds.origin.y + bounds.size.height / 2.0,
        );

        // 生成三角形网格
        let triangles = self.generate_triangles();

        // 渲染每个三角形
        for triangle in &triangles {
            // 计算光照强度
            let light_intensity = self.lighting.calculate_intensity(&triangle.normal);

            // 计算最终颜色
            let lit_color = Color::rgb(
                (self.base_color.r * light_intensity as f32).min(1.0),
                (self.base_color.g * light_intensity as f32).min(1.0),
                (self.base_color.b * light_intensity as f32).min(1.0),
            );

            // 创建三角形路径
            let path_commands = self.create_triangle_path(triangle, center);

            // 添加填充三角形
            commands.push(DrawCommand::Path {
                commands: path_commands.clone(),
                style: PathStyle {
                    fill: Some(lit_color),
                    stroke: if self.wireframe {
                        Some(LineStyle {
                            color: self.wireframe_color,
                            width: 1.0,
                            opacity: self.opacity,
                            dash_pattern: None,
                            cap: LineCap::Butt,
                            join: LineJoin::Round,
                        })
                    } else {
                        None
                    },
                    opacity: self.opacity,
                    fill_rule: FillRule::NonZero,
                },
            });
        }

        Ok(commands)
    }

    fn bounds(&self) -> Option<Bounds> {
        // 3D曲面图的边界难以预先计算，使用整个可用空间
        None
    }

    fn is_visible(&self) -> bool {
        self.config.visible
    }

    fn z_index(&self) -> i32 {
        self.config.z_index
    }

    fn clone_series(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use echarts_core::CartesianCoordinateSystem;

    #[test]
    fn test_point3d_operations() {
        let p1 = Point3D::new(1.0, 2.0, 3.0);
        let p2 = Point3D::new(4.0, 5.0, 6.0);

        // 测试向量运算
        let diff = p2.sub(&p1);
        assert_eq!(diff.x, 3.0);
        assert_eq!(diff.y, 3.0);
        assert_eq!(diff.z, 3.0);

        // 测试点积
        let dot = p1.dot(&p2);
        assert_eq!(dot, 32.0); // 1*4 + 2*5 + 3*6 = 32

        // 测试长度
        let len = Point3D::new(3.0, 4.0, 0.0).length();
        assert_eq!(len, 5.0);
    }

    #[test]
    fn test_surface3d_series_creation() {
        let series = Surface3DSeries::new("测试3D曲面")
            .resolution(10, 10)
            .scale(50.0)
            .rotation(30.0, 45.0)
            .wireframe(true, Color::rgb(0.0, 0.0, 0.0));

        assert_eq!(series.name, "测试3D曲面");
        assert_eq!(series.series_type(), SeriesType::Surface3D);
        assert_eq!(series.grid_resolution, (10, 10));
        assert_eq!(series.scale, 50.0);
        assert!(series.wireframe);
        assert!(series.is_visible());
    }

    #[test]
    fn test_surface3d_from_function() {
        // 创建一个简单的抛物面 z = x² + y²
        let series = Surface3DSeries::new("抛物面")
            .resolution(5, 5)
            .from_function(|x, y| x * x + y * y, (-2.0, 2.0), (-2.0, 2.0));

        assert_eq!(series.grid_resolution, (5, 5));
        assert_eq!(series.data.len(), 25); // 5x5 = 25个点
    }

    #[test]
    fn test_triangle3d_creation() {
        let v1 = Point3D::new(0.0, 0.0, 0.0);
        let v2 = Point3D::new(1.0, 0.0, 0.0);
        let v3 = Point3D::new(0.0, 1.0, 0.0);

        let triangle = Triangle3D::new(v1, v2, v3);

        // 法向量应该指向Z轴正方向
        assert!(triangle.normal.z > 0.0);
        assert_eq!(triangle.center_z, 0.0);
    }

    #[test]
    fn test_simple_lighting() {
        let lighting = SimpleLighting::default();

        // 测试垂直向上的法向量
        let normal_up = Point3D::new(0.0, 0.0, 1.0);
        let intensity_up = lighting.calculate_intensity(&normal_up);

        // 测试垂直向下的法向量
        let normal_down = Point3D::new(0.0, 0.0, -1.0);
        let intensity_down = lighting.calculate_intensity(&normal_down);

        // 向上的面应该比向下的面亮
        assert!(intensity_up > intensity_down);

        // 强度应该在合理范围内
        assert!(intensity_up >= 0.0 && intensity_up <= 1.0);
        assert!(intensity_down >= 0.0 && intensity_down <= 1.0);
    }

    #[test]
    fn test_isometric_projection() {
        let point = Point3D::new(1.0, 1.0, 1.0);
        let center = Point::new(100.0, 100.0);
        let projected = point.isometric_project(50.0, center);

        // 投影点应该在中心点附近
        assert!((projected.x - center.x).abs() < 100.0);
        assert!((projected.y - center.y).abs() < 100.0);
    }

    #[test]
    fn test_render_to_commands() {
        // 创建一个简单的平面
        let series = Surface3DSeries::new("渲染测试")
            .resolution(3, 3)
            .from_function(|_x, _y| 0.0, (-1.0, 1.0), (-1.0, 1.0));

        let coord_system = CartesianCoordinateSystem::new(
            Bounds::new(0.0, 0.0, 400.0, 400.0),
            (0.0, 1.0),
            (0.0, 1.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();

        // 3x3网格应该生成8个三角形（2x2个网格单元，每个2个三角形）
        assert_eq!(commands.len(), 8);

        // 所有命令都应该是路径命令
        for cmd in &commands {
            match cmd {
                DrawCommand::Path { .. } => {},
                _ => panic!("期望路径命令"),
            }
        }
    }

    #[test]
    fn test_empty_data() {
        let series = Surface3DSeries::new("空数据测试");

        let coord_system = CartesianCoordinateSystem::new(
            Bounds::new(0.0, 0.0, 400.0, 400.0),
            (0.0, 1.0),
            (0.0, 1.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();

        // 空数据应该不生成任何命令
        assert!(commands.is_empty());
    }

    #[test]
    fn test_series_trait_object() {
        // 测试类型擦除
        let series: Box<dyn Series> = Box::new(Surface3DSeries::new("类型擦除测试")
            .from_function(|x, y| x + y, (0.0, 1.0), (0.0, 1.0)));

        assert_eq!(series.name(), "类型擦除测试");
        assert_eq!(series.series_type(), SeriesType::Surface3D);
        assert!(series.is_visible());
    }
}
// ============================================================================
// 图表基础架构集成 - ChartBase 实现
// ============================================================================

/// 为 Surface3DSeries 实现 ChartBase trait
impl ChartBase for Surface3DSeries {
    type DataType = DataSet;

    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn raw_data(&self) -> &Self::DataType {
        &self.data
    }

    fn to_dataset(&self) -> DataSet {
        // TODO: 实现 DataSet 到 DataSet 的转换
        DataSet::new()
    }
    
    fn visible(&self) -> bool {
        self.config.visible
    }
    
    fn z_index(&self) -> i32 {
        self.config.z_index
    }
    
    fn bounds(&self) -> Option<Bounds> {
        // TODO: 为 Surface3DSeries 实现边界计算
        None
    }
    
    fn config(&self) -> &ChartConfig {
        &self.config
    }
    
    fn config_mut(&mut self) -> &mut ChartConfig {
        &mut self.config
    }
}

/// 为 Surface3DSeries 实现 ChartSeries trait
impl ChartSeries for Surface3DSeries {
    // 所有方法都由默认实现提供，基于 ChartBase
}
