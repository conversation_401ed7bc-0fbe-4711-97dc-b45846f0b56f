//! 完整的 SVG 图表生成器函数
//!
//! 包含所有类型图表的完整生成函数

use std::fs;

/// 生成完整的饼图
pub fn generate_complete_pie_chart(title: &str, data: &[(&str, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    // SVG 头部
    svg.push_str(&format!(
        "<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n",
        width, height
    ));
    
    // 定义滤镜
    svg.push_str("  <defs>\n");
    svg.push_str("    <filter id=\"pieShadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n");
    svg.push_str("      <feDropShadow dx=\"3\" dy=\"3\" stdDeviation=\"3\" flood-color=\"#000000\" flood-opacity=\"0.3\"/>\n");
    svg.push_str("    </filter>\n");
    svg.push_str("  </defs>\n");
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    
    // 标题
    svg.push_str(&format!(
        "  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
        title
    ));
    
    if !data.is_empty() {
        let center_x = width / 2.0;
        let center_y = height / 2.0 + 20.0;
        let radius = (width.min(height) - 120.0) / 2.5;
        
        let total: f64 = data.iter().map(|(_, v)| *v).sum();
        let mut current_angle = -std::f64::consts::PI / 2.0; // 从顶部开始
        
        let colors = [
            "#007bff", "#28a745", "#dc3545", "#ffc107", "#17a2b8", 
            "#6f42c1", "#e83e8c", "#fd7e14", "#20c997", "#6c757d"
        ];
        
        for (i, (label, value)) in data.iter().enumerate() {
            let angle = (value / total) * 2.0 * std::f64::consts::PI;
            let end_angle = current_angle + angle;
            
            let x1 = center_x + radius * current_angle.cos();
            let y1 = center_y + radius * current_angle.sin();
            let x2 = center_x + radius * end_angle.cos();
            let y2 = center_y + radius * end_angle.sin();
            
            let large_arc = if angle > std::f64::consts::PI { 1 } else { 0 };
            
            let color = colors[i % colors.len()];
            
            // 绘制扇形
            svg.push_str(&format!(
                "  <path d=\"M {} {} L {} {} A {} {} 0 {} 1 {} {} Z\" fill=\"{}\" stroke=\"white\" stroke-width=\"3\" filter=\"url(#pieShadow)\"/>\n",
                center_x, center_y, x1, y1, radius, radius, large_arc, x2, y2, color
            ));
            
            // 添加标签
            let label_angle = current_angle + angle / 2.0;
            let label_radius = radius + 40.0;
            let label_x = center_x + label_radius * label_angle.cos();
            let label_y = center_y + label_radius * label_angle.sin();
            
            // 连接线
            let line_start_x = center_x + (radius + 5.0) * label_angle.cos();
            let line_start_y = center_y + (radius + 5.0) * label_angle.sin();
            let line_mid_x = center_x + (radius + 25.0) * label_angle.cos();
            let line_mid_y = center_y + (radius + 25.0) * label_angle.sin();
            
            svg.push_str(&format!(
                "  <polyline points=\"{},{} {},{} {},{}\" stroke=\"#666\" stroke-width=\"1\" fill=\"none\"/>\n",
                line_start_x, line_start_y, line_mid_x, line_mid_y, label_x, label_y
            ));
            
            // 标签文本
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
                label_x, label_y - 5.0, label
            ));
            
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"10\" fill=\"#666\">{:.1}%</text>\n",
                label_x, label_y + 10.0, (value / total) * 100.0
            ));
            
            current_angle = end_angle;
        }
        
        // 添加图例
        let legend_x = 20.0;
        let legend_y = height - 100.0;
        
        svg.push_str(&format!(
            "  <text x=\"{}\" y=\"{}\" font-size=\"14\" font-weight=\"bold\" fill=\"#333\">图例</text>\n",
            legend_x, legend_y - 10.0
        ));
        
        for (i, (label, value)) in data.iter().enumerate() {
            let y = legend_y + i as f64 * 20.0;
            let color = colors[i % colors.len()];
            
            svg.push_str(&format!(
                "  <rect x=\"{}\" y=\"{}\" width=\"15\" height=\"15\" fill=\"{}\"/>\n",
                legend_x, y - 10.0, color
            ));
            
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">{}: {:.0}</text>\n",
                legend_x + 20.0, y, label, value
            ));
        }
    }
    
    svg.push_str("</svg>");
    svg
}

/// 生成多系列折线图
pub fn generate_multi_line_chart(title: &str, width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    // SVG 头部
    svg.push_str(&format!(
        "<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n",
        width, height
    ));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    
    // 标题
    svg.push_str(&format!(
        "  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
        title
    ));
    
    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height
    ));
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y, chart_x, chart_y + chart_height
    ));
    
    // 网格线
    for i in 0..=5 {
        let x = chart_x + (i as f64 / 5.0) * chart_width;
        svg.push_str(&format!(
            "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
            x, chart_y, x, chart_y + chart_height
        ));
    }
    
    for i in 0..=5 {
        let y = chart_y + (i as f64 / 5.0) * chart_height;
        svg.push_str(&format!(
            "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
            chart_x, y, chart_x + chart_width, y
        ));
    }
    
    // 多个系列数据
    let series_data = vec![
        ("产品A", vec![(0.0, 120.0), (1.0, 132.0), (2.0, 101.0), (3.0, 134.0), (4.0, 90.0), (5.0, 230.0), (6.0, 210.0)], "#007bff"),
        ("产品B", vec![(0.0, 220.0), (1.0, 182.0), (2.0, 191.0), (3.0, 234.0), (4.0, 290.0), (5.0, 330.0), (6.0, 310.0)], "#28a745"),
        ("产品C", vec![(0.0, 150.0), (1.0, 160.0), (2.0, 140.0), (3.0, 180.0), (4.0, 170.0), (5.0, 200.0), (6.0, 190.0)], "#dc3545"),
    ];
    
    let max_value = 350.0;
    let max_x = 6.0;
    
    // 绘制每个系列
    for (name, data, color) in &series_data {
        // 生成路径
        let mut path = String::from("M");
        
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x / max_x) * chart_width;
            let py = chart_y + chart_height - (y / max_value) * chart_height;
            
            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        // 绘制折线
        svg.push_str(&format!(
            "  <path d=\"{}\" stroke=\"{}\" stroke-width=\"3\" fill=\"none\"/>\n",
            path, color
        ));
        
        // 绘制数据点
        for (x, y) in data {
            let px = chart_x + (x / max_x) * chart_width;
            let py = chart_y + chart_height - (y / max_value) * chart_height;
            
            svg.push_str(&format!(
                "  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"{}\" stroke=\"white\" stroke-width=\"2\"/>\n",
                px, py, color
            ));
        }
    }
    
    // 添加图例
    let legend_x = chart_x + chart_width - 150.0;
    let legend_y = chart_y + 30.0;
    
    for (i, (name, _, color)) in series_data.iter().enumerate() {
        let y = legend_y + i as f64 * 25.0;
        
        svg.push_str(&format!(
            "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"{}\" stroke-width=\"3\"/>\n",
            legend_x, y, legend_x + 20.0, y, color
        ));
        
        svg.push_str(&format!(
            "  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">{}</text>\n",
            legend_x + 25.0, y + 4.0, name
        ));
    }
    
    // 坐标轴标签
    for i in 0..=6 {
        let x = chart_x + (i as f64 / 6.0) * chart_width;
        svg.push_str(&format!(
            "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">月{}</text>\n",
            x, chart_y + chart_height + 20.0, i + 1
        ));
    }
    
    for i in 0..=5 {
        let y = chart_y + chart_height - (i as f64 / 5.0) * chart_height;
        let value = (i as f64 / 5.0) * max_value;
        svg.push_str(&format!(
            "  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n",
            chart_x - 10.0, y + 4.0, value
        ));
    }
    
    svg.push_str("</svg>");
    svg
}
