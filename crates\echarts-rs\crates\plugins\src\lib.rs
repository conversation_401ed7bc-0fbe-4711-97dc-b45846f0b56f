//! Plugin system for ECharts-rs

use echarts_core::*;
use echarts_charts::*;
use echarts_components::*;
use echarts_renderer::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::{Arc, RwLock};

pub mod registry;
pub mod loader;
pub mod interface;

#[cfg(feature = "scripting-rhai")]
pub mod rhai_engine;

#[cfg(feature = "scripting-lua")]
pub mod lua_engine;

#[cfg(feature = "hot-reload")]
pub mod hot_reload;

/// Plugin manager for ECharts-rs
pub struct PluginManager {
    /// Registered plugins
    plugins: Arc<RwLock<HashMap<String, Box<dyn Plugin>>>>,
    /// Plugin registry
    registry: PluginRegistry,
    /// Plugin loader
    loader: PluginLoader,
    /// Configuration
    config: PluginConfig,
    /// Event system
    events: EventSystem,
}

/// Plugin configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginConfig {
    /// Plugin directories to search
    pub plugin_dirs: Vec<PathBuf>,
    /// Enable hot reloading
    pub hot_reload: bool,
    /// Plugin load timeout (seconds)
    pub load_timeout: u64,
    /// Maximum memory usage per plugin (MB)
    pub max_memory_mb: usize,
    /// Enable sandboxing
    pub sandbox: bool,
    /// Allowed permissions
    pub permissions: PluginPermissions,
}

/// Plugin permissions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginPermissions {
    /// Allow file system access
    pub file_system: bool,
    /// Allow network access
    pub network: bool,
    /// Allow system calls
    pub system_calls: bool,
    /// Allow chart modification
    pub chart_modification: bool,
    /// Allow renderer access
    pub renderer_access: bool,
    /// Allowed file extensions
    pub allowed_extensions: Vec<String>,
}

/// Plugin trait that all plugins must implement
pub trait Plugin: Send + Sync {
    /// Plugin metadata
    fn metadata(&self) -> PluginMetadata;
    
    /// Initialize the plugin
    fn initialize(&mut self, context: &PluginContext) -> Result<()>;
    
    /// Process a chart before rendering
    fn process_chart(&self, chart: &mut Chart, context: &PluginContext) -> Result<()>;
    
    /// Process rendering context
    fn process_render_context(&self, context: &mut RenderContext) -> Result<()>;
    
    /// Handle events
    fn handle_event(&self, event: &PluginEvent, context: &PluginContext) -> Result<()>;
    
    /// Cleanup resources
    fn cleanup(&mut self) -> Result<()>;
    
    /// Check if plugin is compatible with current version
    fn is_compatible(&self, version: &str) -> bool;
}

/// Plugin metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginMetadata {
    /// Plugin name
    pub name: String,
    /// Plugin version
    pub version: String,
    /// Plugin description
    pub description: String,
    /// Plugin author
    pub author: String,
    /// Plugin license
    pub license: String,
    /// Required ECharts-rs version
    pub required_version: String,
    /// Plugin dependencies
    pub dependencies: Vec<PluginDependency>,
    /// Plugin capabilities
    pub capabilities: Vec<PluginCapability>,
    /// Plugin tags
    pub tags: Vec<String>,
}

/// Plugin dependency
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginDependency {
    /// Dependency name
    pub name: String,
    /// Required version
    pub version: String,
    /// Whether dependency is optional
    pub optional: bool,
}

/// Plugin capabilities
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PluginCapability {
    /// Can create new chart types
    ChartCreation,
    /// Can modify existing charts
    ChartModification,
    /// Can create new components
    ComponentCreation,
    /// Can modify rendering
    RenderModification,
    /// Can handle events
    EventHandling,
    /// Can export data
    DataExport,
    /// Can import data
    DataImport,
    /// Can create themes
    ThemeCreation,
    /// Can create animations
    AnimationCreation,
}

/// Plugin context provided to plugins
#[derive(Debug)]
pub struct PluginContext {
    /// Plugin configuration
    pub config: PluginConfig,
    /// Current chart being processed
    pub chart_id: Option<String>,
    /// Render settings
    pub render_settings: HashMap<String, serde_json::Value>,
    /// User data
    pub user_data: HashMap<String, serde_json::Value>,
    /// Plugin communication channel
    pub communication: PluginCommunication,
}

/// Plugin communication system
#[derive(Debug)]
pub struct PluginCommunication {
    /// Send messages to other plugins
    sender: std::sync::mpsc::Sender<PluginMessage>,
    /// Receive messages from other plugins
    receiver: std::sync::mpsc::Receiver<PluginMessage>,
}

/// Plugin message for inter-plugin communication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginMessage {
    /// Source plugin
    pub from: String,
    /// Target plugin (None for broadcast)
    pub to: Option<String>,
    /// Message type
    pub message_type: String,
    /// Message payload
    pub payload: serde_json::Value,
    /// Timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Plugin events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PluginEvent {
    /// Chart created
    ChartCreated { chart_id: String },
    /// Chart modified
    ChartModified { chart_id: String, changes: Vec<String> },
    /// Chart rendered
    ChartRendered { chart_id: String, render_time: f64 },
    /// Theme changed
    ThemeChanged { old_theme: String, new_theme: String },
    /// Data updated
    DataUpdated { source: String, row_count: usize },
    /// User interaction
    UserInteraction { interaction_type: String, data: serde_json::Value },
    /// Plugin loaded
    PluginLoaded { plugin_name: String },
    /// Plugin unloaded
    PluginUnloaded { plugin_name: String },
    /// Custom event
    Custom { event_type: String, data: serde_json::Value },
}

/// Plugin registry for managing available plugins
pub struct PluginRegistry {
    /// Available plugins
    available: HashMap<String, PluginInfo>,
    /// Plugin search paths
    search_paths: Vec<PathBuf>,
}

/// Plugin information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginInfo {
    /// Plugin metadata
    pub metadata: PluginMetadata,
    /// Plugin file path
    pub path: PathBuf,
    /// Plugin status
    pub status: PluginStatus,
    /// Load time
    pub load_time: Option<chrono::DateTime<chrono::Utc>>,
    /// Error message if failed to load
    pub error: Option<String>,
}

/// Plugin status
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PluginStatus {
    Available,
    Loaded,
    Failed,
    Disabled,
}

/// Plugin loader for dynamic loading
pub struct PluginLoader {
    /// Loaded libraries
    libraries: HashMap<String, libloading::Library>,
    /// Loading configuration
    config: LoaderConfig,
}

/// Loader configuration
#[derive(Debug, Clone)]
pub struct LoaderConfig {
    /// Enable symbol validation
    pub validate_symbols: bool,
    /// Required symbols
    pub required_symbols: Vec<String>,
    /// Library search paths
    pub library_paths: Vec<PathBuf>,
}

/// Event system for plugin communication
pub struct EventSystem {
    /// Event listeners
    listeners: HashMap<String, Vec<Box<dyn EventListener>>>,
    /// Event queue
    queue: std::sync::mpsc::Sender<PluginEvent>,
    /// Event processor
    processor: std::sync::mpsc::Receiver<PluginEvent>,
}

/// Event listener trait
pub trait EventListener: Send + Sync {
    /// Handle an event
    fn handle_event(&self, event: &PluginEvent) -> Result<()>;
    
    /// Get listener priority (higher = earlier execution)
    fn priority(&self) -> i32 {
        0
    }
}

impl Default for PluginConfig {
    fn default() -> Self {
        Self {
            plugin_dirs: vec![
                PathBuf::from("plugins"),
                PathBuf::from("./target/debug"),
                PathBuf::from("./target/release"),
            ],
            hot_reload: false,
            load_timeout: 30,
            max_memory_mb: 100,
            sandbox: true,
            permissions: PluginPermissions::default(),
        }
    }
}

impl Default for PluginPermissions {
    fn default() -> Self {
        Self {
            file_system: false,
            network: false,
            system_calls: false,
            chart_modification: true,
            renderer_access: true,
            allowed_extensions: vec![
                "dll".to_string(),
                "so".to_string(),
                "dylib".to_string(),
            ],
        }
    }
}

impl PluginManager {
    /// Create a new plugin manager
    pub fn new(config: PluginConfig) -> Self {
        let (event_sender, event_receiver) = std::sync::mpsc::channel();
        
        Self {
            plugins: Arc::new(RwLock::new(HashMap::new())),
            registry: PluginRegistry::new(config.plugin_dirs.clone()),
            loader: PluginLoader::new(LoaderConfig::default()),
            config,
            events: EventSystem {
                listeners: HashMap::new(),
                queue: event_sender,
                processor: event_receiver,
            },
        }
    }

    /// Load a plugin by name
    pub fn load_plugin(&mut self, name: &str) -> Result<()> {
        if let Some(plugin_info) = self.registry.get_plugin_info(name) {
            let plugin = self.loader.load_plugin(&plugin_info.path)?;
            
            let mut plugins = self.plugins.write().unwrap();
            plugins.insert(name.to_string(), plugin);
            
            self.emit_event(PluginEvent::PluginLoaded {
                plugin_name: name.to_string(),
            })?;
            
            Ok(())
        } else {
            Err(ChartError::Configuration(format!("Plugin '{}' not found", name)))
        }
    }

    /// Unload a plugin
    pub fn unload_plugin(&mut self, name: &str) -> Result<()> {
        let mut plugins = self.plugins.write().unwrap();
        if let Some(mut plugin) = plugins.remove(name) {
            plugin.cleanup()?;
            
            self.emit_event(PluginEvent::PluginUnloaded {
                plugin_name: name.to_string(),
            })?;
            
            Ok(())
        } else {
            Err(ChartError::Configuration(format!("Plugin '{}' not loaded", name)))
        }
    }

    /// Process a chart through all loaded plugins
    pub fn process_chart(&self, chart: &mut Chart) -> Result<()> {
        let plugins = self.plugins.read().unwrap();
        let context = self.create_plugin_context(Some(chart.id.clone()));
        
        for plugin in plugins.values() {
            plugin.process_chart(chart, &context)?;
        }
        
        Ok(())
    }

    /// List all available plugins
    pub fn list_available_plugins(&self) -> Vec<PluginInfo> {
        self.registry.list_plugins()
    }

    /// List all loaded plugins
    pub fn list_loaded_plugins(&self) -> Vec<String> {
        let plugins = self.plugins.read().unwrap();
        plugins.keys().cloned().collect()
    }

    /// Emit an event
    pub fn emit_event(&self, event: PluginEvent) -> Result<()> {
        self.events.queue.send(event)
            .map_err(|e| ChartError::Configuration(format!("Failed to emit event: {}", e)))?;
        Ok(())
    }

    /// Add event listener
    pub fn add_event_listener<L: EventListener + 'static>(&mut self, event_type: String, listener: L) {
        self.events.listeners
            .entry(event_type)
            .or_insert_with(Vec::new)
            .push(Box::new(listener));
    }

    /// Create plugin context
    fn create_plugin_context(&self, chart_id: Option<String>) -> PluginContext {
        let (sender, receiver) = std::sync::mpsc::channel();
        
        PluginContext {
            config: self.config.clone(),
            chart_id,
            render_settings: HashMap::new(),
            user_data: HashMap::new(),
            communication: PluginCommunication { sender, receiver },
        }
    }

    /// Process events
    pub fn process_events(&mut self) -> Result<()> {
        while let Ok(event) = self.events.processor.try_recv() {
            self.handle_event(&event)?;
        }
        Ok(())
    }

    /// Handle a single event
    fn handle_event(&self, event: &PluginEvent) -> Result<()> {
        // Notify plugins
        let plugins = self.plugins.read().unwrap();
        let context = self.create_plugin_context(None);
        
        for plugin in plugins.values() {
            plugin.handle_event(event, &context)?;
        }
        
        // Notify event listeners
        let event_type = match event {
            PluginEvent::ChartCreated { .. } => "chart_created",
            PluginEvent::ChartModified { .. } => "chart_modified",
            PluginEvent::ChartRendered { .. } => "chart_rendered",
            PluginEvent::ThemeChanged { .. } => "theme_changed",
            PluginEvent::DataUpdated { .. } => "data_updated",
            PluginEvent::UserInteraction { .. } => "user_interaction",
            PluginEvent::PluginLoaded { .. } => "plugin_loaded",
            PluginEvent::PluginUnloaded { .. } => "plugin_unloaded",
            PluginEvent::Custom { event_type, .. } => event_type,
        };
        
        if let Some(listeners) = self.events.listeners.get(event_type) {
            for listener in listeners {
                listener.handle_event(event)?;
            }
        }
        
        Ok(())
    }
}

impl PluginRegistry {
    /// Create a new plugin registry
    pub fn new(search_paths: Vec<PathBuf>) -> Self {
        Self {
            available: HashMap::new(),
            search_paths,
        }
    }

    /// Scan for available plugins
    pub fn scan_plugins(&mut self) -> Result<()> {
        for path in &self.search_paths {
            if path.exists() && path.is_dir() {
                self.scan_directory(path)?;
            }
        }
        Ok(())
    }

    /// Scan a directory for plugins
    fn scan_directory(&mut self, dir: &PathBuf) -> Result<()> {
        for entry in std::fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_file() {
                if let Some(extension) = path.extension() {
                    if matches!(extension.to_str(), Some("dll") | Some("so") | Some("dylib")) {
                        self.try_register_plugin(path)?;
                    }
                }
            }
        }
        Ok(())
    }

    /// Try to register a plugin
    fn try_register_plugin(&mut self, path: PathBuf) -> Result<()> {
        // In a real implementation, you would load the plugin temporarily
        // to get its metadata, then unload it
        let metadata = PluginMetadata {
            name: path.file_stem().unwrap().to_string_lossy().to_string(),
            version: "1.0.0".to_string(),
            description: "Plugin description".to_string(),
            author: "Unknown".to_string(),
            license: "MIT".to_string(),
            required_version: "0.1.0".to_string(),
            dependencies: Vec::new(),
            capabilities: vec![PluginCapability::ChartModification],
            tags: Vec::new(),
        };

        let plugin_info = PluginInfo {
            metadata: metadata.clone(),
            path,
            status: PluginStatus::Available,
            load_time: None,
            error: None,
        };

        self.available.insert(metadata.name.clone(), plugin_info);
        Ok(())
    }

    /// Get plugin information
    pub fn get_plugin_info(&self, name: &str) -> Option<&PluginInfo> {
        self.available.get(name)
    }

    /// List all plugins
    pub fn list_plugins(&self) -> Vec<PluginInfo> {
        self.available.values().cloned().collect()
    }
}

impl PluginLoader {
    /// Create a new plugin loader
    pub fn new(config: LoaderConfig) -> Self {
        Self {
            libraries: HashMap::new(),
            config,
        }
    }

    /// Load a plugin from file
    pub fn load_plugin(&mut self, path: &PathBuf) -> Result<Box<dyn Plugin>> {
        // In a real implementation, you would use libloading to load the plugin
        // and call its initialization function
        
        // For now, return a dummy plugin
        Ok(Box::new(DummyPlugin::new()))
    }

    /// Unload a plugin
    pub fn unload_plugin(&mut self, name: &str) -> Result<()> {
        self.libraries.remove(name);
        Ok(())
    }
}

/// Dummy plugin for testing
struct DummyPlugin {
    metadata: PluginMetadata,
}

impl DummyPlugin {
    fn new() -> Self {
        Self {
            metadata: PluginMetadata {
                name: "Dummy Plugin".to_string(),
                version: "1.0.0".to_string(),
                description: "A dummy plugin for testing".to_string(),
                author: "ECharts-rs".to_string(),
                license: "MIT".to_string(),
                required_version: "0.1.0".to_string(),
                dependencies: Vec::new(),
                capabilities: vec![PluginCapability::ChartModification],
                tags: vec!["test".to_string()],
            },
        }
    }
}

impl Plugin for DummyPlugin {
    fn metadata(&self) -> PluginMetadata {
        self.metadata.clone()
    }

    fn initialize(&mut self, _context: &PluginContext) -> Result<()> {
        Ok(())
    }

    fn process_chart(&self, _chart: &mut Chart, _context: &PluginContext) -> Result<()> {
        Ok(())
    }

    fn process_render_context(&self, _context: &mut RenderContext) -> Result<()> {
        Ok(())
    }

    fn handle_event(&self, _event: &PluginEvent, _context: &PluginContext) -> Result<()> {
        Ok(())
    }

    fn cleanup(&mut self) -> Result<()> {
        Ok(())
    }

    fn is_compatible(&self, _version: &str) -> bool {
        true
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_plugin_manager_creation() {
        let config = PluginConfig::default();
        let manager = PluginManager::new(config);
        assert_eq!(manager.list_loaded_plugins().len(), 0);
    }

    #[test]
    fn test_plugin_registry() {
        let mut registry = PluginRegistry::new(vec![]);
        assert_eq!(registry.list_plugins().len(), 0);
    }

    #[test]
    fn test_dummy_plugin() {
        let plugin = DummyPlugin::new();
        assert_eq!(plugin.metadata().name, "Dummy Plugin");
        assert!(plugin.is_compatible("0.1.0"));
    }

    #[test]
    fn test_plugin_permissions() {
        let permissions = PluginPermissions::default();
        assert!(!permissions.file_system);
        assert!(!permissions.network);
        assert!(permissions.chart_modification);
    }
}
