//! Charts 集成测试
//!
//! 验证主 crate 能正确导出和使用 Charts 模块的实现

use echarts_rs::prelude::*;

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("📊 Charts 集成测试");

    // 测试 LineSeries 导出和功能
    println!("\n🔵 测试 LineSeries:");
    let line_series = LineSeries::new("销售数据")
        .data(vec![(1.0, 120.0), (2.0, 200.0), (3.0, 150.0), (4.0, 80.0), (5.0, 170.0)])
        .color(Color::rgb(0.2, 0.6, 1.0))
        .line_width(3.0)
        .smooth(true)
        .show_symbols(true);

    println!("  ✅ LineSeries 创建成功");
    println!("  - 名称: {}", line_series.name());
    println!("  - 类型: {:?}", line_series.series_type());
    println!("  - 数据点数: {}", line_series.data.len());
    println!("  - 平滑曲线: {}", line_series.smooth);
    println!("  - 显示符号: {}", line_series.show_symbols);

    // 测试 BarSeries 导出和功能
    println!("\n🟠 测试 BarSeries:");
    let bar_series = BarSeries::new("目标数据")
        .data(vec![(1.0, 100.0), (2.0, 180.0), (3.0, 140.0), (4.0, 90.0), (5.0, 160.0)])
        .color(Color::rgb(1.0, 0.4, 0.2))
        .bar_width(0.6)
        .border(true, Color::rgb(0.0, 0.0, 0.0), 1.0);

    println!("  ✅ BarSeries 创建成功");
    println!("  - 名称: {}", bar_series.name());
    println!("  - 类型: {:?}", bar_series.series_type());
    println!("  - 数据点数: {}", bar_series.data.len());
    println!("  - 柱子宽度: {}", bar_series.bar_width);
    println!("  - 显示边框: {}", bar_series.show_border);

    // 测试 ScatterSeries 导出和功能
    println!("\n🟢 测试 ScatterSeries:");
    let scatter_series = ScatterSeries::new("散点数据")
        .data(vec![(1.0, 2.0), (3.0, 4.0), (5.0, 6.0), (7.0, 8.0)])
        .color(Color::rgb(0.0, 1.0, 0.0))
        .symbol_size(12.0)
        .symbol_type(SymbolType::Circle);

    println!("  ✅ ScatterSeries 创建成功");
    println!("  - 名称: {}", scatter_series.name());
    println!("  - 类型: {:?}", scatter_series.series_type());
    println!("  - 数据点数: {}", scatter_series.data.len());
    println!("  - 符号大小: {}", scatter_series.symbol_size);
    println!("  - 符号类型: {:?}", scatter_series.symbol_type);

    // 测试类型擦除 - 这是重构的核心目标
    println!("\n🔄 测试类型擦除:");
    let series_list: Vec<Box<dyn Series>> = vec![
        Box::new(line_series),
        Box::new(bar_series),
        Box::new(scatter_series),
    ];

    println!("  ✅ 类型擦除成功 - 可以在同一容器中存储不同类型的 Series");
    for (i, series) in series_list.iter().enumerate() {
        println!("    {}. {} ({})", i + 1, series.name(), series.series_type().as_str());
        if let Some(bounds) = series.bounds() {
            println!("       边界: ({}, {}) - {}x{}", 
                     bounds.origin.x, bounds.origin.y, 
                     bounds.size.width, bounds.size.height);
        }
    }

    // 测试坐标系统和渲染命令生成
    println!("\n🎨 测试渲染命令生成:");
    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 700.0, 500.0),
        (0.0, 6.0),  // X 轴范围
        (0.0, 200.0), // Y 轴范围
    );

    for (i, series) in series_list.iter().enumerate() {
        match series.render_to_commands(&coord_system) {
            Ok(commands) => {
                println!("  ✅ {} 生成了 {} 个绘制命令", series.name(), commands.len());
            }
            Err(e) => {
                println!("  ❌ {} 渲染失败: {}", series.name(), e);
            }
        }
    }

    // 测试主题系统
    println!("\n🌈 测试主题系统:");
    let theme = SimpleTheme::default();
    println!("  ✅ 默认主题加载成功");
    println!("  - 主题名称: {}", theme.name);
    println!("  - 调色板颜色数: {}", theme.color_palette.len());
    println!("  - 背景色: RGB({:.2}, {:.2}, {:.2})", 
             theme.background_color.r, theme.background_color.g, theme.background_color.b);

    // 测试 RenderContext
    println!("\n🖼️ 测试 RenderContext:");
    let mut ctx = RenderContext::new();
    ctx.draw_line_styled(Point::new(0.0, 0.0), Point::new(100.0, 100.0));
    ctx.draw_circle_styled(Point::new(50.0, 50.0), 25.0);
    
    println!("  ✅ RenderContext 创建成功");
    println!("  - 生成的绘制命令数: {}", ctx.commands().len());
    println!("  - 画布大小: {}x{}", ctx.bounds().size.width, ctx.bounds().size.height);

    println!("\n🎉 所有 Charts 集成测试通过！");
    println!("✨ 主 crate 成功导出并使用了 Charts 模块的完整实现");
    println!("🔗 类型擦除功能正常，架构统一目标达成");

    Ok(())
}
