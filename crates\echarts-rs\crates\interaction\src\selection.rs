//! 选择系统
//!
//! 提供数据点和系列的选择功能

use crate::{InteractionResult, SelectedElement, KeyModifiers};
use echarts_core::{Point, Bounds, Series, Result};
use serde::{Serialize, Deserialize};

/// 选择管理器
#[derive(Debug)]
pub struct SelectionManager {
    /// 配置选项
    config: SelectionConfig,
    /// 当前选择状态
    state: SelectionState,
}

/// 选择配置
#[derive(Debug, Clone)]
pub struct SelectionConfig {
    /// 是否启用选择
    pub enabled: bool,
    /// 选择模式
    pub mode: SelectionMode,
    /// 多选模式
    pub multi_select: bool,
    /// 选择类型
    pub select_type: SelectionType,
    /// 选择样式
    pub style: SelectionStyle,
    /// 框选配置
    pub box_select: BoxSelectConfig,
}

/// 选择状态
#[derive(Debug, Clone)]
pub struct SelectionState {
    /// 当前选中的元素
    pub selected_elements: Vec<SelectedElement>,
    /// 是否正在框选
    pub is_box_selecting: bool,
    /// 框选起始位置
    pub box_select_start: Option<Point>,
    /// 框选当前位置
    pub box_select_current: Option<Point>,
    /// 框选区域
    pub box_select_bounds: Option<Bounds>,
}

/// 选择模式
#[derive(Debug, Clone, PartialEq)]
pub enum SelectionMode {
    /// 单选
    Single,
    /// 多选
    Multiple,
    /// 框选
    Box,
    /// 套索选择
    Lasso,
}

/// 选择类型
#[derive(Debug, Clone, PartialEq)]
pub enum SelectionType {
    /// 数据点
    DataPoint,
    /// 系列
    Series,
    /// 区域
    Area,
    /// 所有类型
    All,
}

/// 选择样式
#[derive(Debug, Clone)]
pub struct SelectionStyle {
    /// 选中状态的颜色
    pub selected_color: echarts_core::Color,
    /// 选中状态的边框颜色
    pub selected_border_color: echarts_core::Color,
    /// 选中状态的边框宽度
    pub selected_border_width: f64,
    /// 选中状态的透明度
    pub selected_opacity: f64,
    /// 高亮效果
    pub highlight: bool,
    /// 高亮颜色
    pub highlight_color: echarts_core::Color,
}

/// 框选配置
#[derive(Debug, Clone)]
pub struct BoxSelectConfig {
    /// 是否启用框选
    pub enabled: bool,
    /// 框选样式
    pub style: BoxSelectStyle,
    /// 最小框选区域
    pub min_size: f64,
}

/// 框选样式
#[derive(Debug, Clone)]
pub struct BoxSelectStyle {
    /// 边框颜色
    pub border_color: echarts_core::Color,
    /// 边框宽度
    pub border_width: f64,
    /// 填充颜色
    pub fill_color: echarts_core::Color,
    /// 边框样式
    pub border_dash: Option<Vec<f64>>,
}

impl Default for SelectionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            mode: SelectionMode::Single,
            multi_select: true,
            select_type: SelectionType::All,
            style: SelectionStyle::default(),
            box_select: BoxSelectConfig::default(),
        }
    }
}

impl Default for SelectionStyle {
    fn default() -> Self {
        Self {
            selected_color: echarts_core::Color::rgb(0.2, 0.6, 1.0),
            selected_border_color: echarts_core::Color::rgb(0.1, 0.4, 0.8),
            selected_border_width: 2.0,
            selected_opacity: 0.8,
            highlight: true,
            highlight_color: echarts_core::Color::rgba(0.2, 0.6, 1.0, 0.3),
        }
    }
}

impl Default for BoxSelectConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            style: BoxSelectStyle::default(),
            min_size: 10.0,
        }
    }
}

impl Default for BoxSelectStyle {
    fn default() -> Self {
        Self {
            border_color: echarts_core::Color::rgb(0.2, 0.6, 1.0),
            border_width: 1.0,
            fill_color: echarts_core::Color::rgba(0.2, 0.6, 1.0, 0.1),
            border_dash: Some(vec![5.0, 5.0]),
        }
    }
}

impl Default for SelectionState {
    fn default() -> Self {
        Self {
            selected_elements: Vec::new(),
            is_box_selecting: false,
            box_select_start: None,
            box_select_current: None,
            box_select_bounds: None,
        }
    }
}

impl SelectionManager {
    /// 创建新的选择管理器
    pub fn new(config: SelectionConfig) -> Self {
        Self {
            config,
            state: SelectionState::default(),
        }
    }
    
    /// 处理点击选择
    pub fn handle_click(
        &mut self, 
        position: Point, 
        chart_bounds: Bounds, 
        series: &[Box<dyn Series>], 
        modifiers: &KeyModifiers
    ) -> Result<InteractionResult> {
        if !self.config.enabled {
            return Ok(InteractionResult::None);
        }
        
        // 查找点击位置的元素
        let clicked_element = self.find_element_at_position(position, chart_bounds, series)?;
        
        if let Some(element) = clicked_element {
            // 处理选择逻辑
            self.handle_element_selection(element, modifiers)?;
            
            Ok(InteractionResult::Select {
                elements: self.state.selected_elements.clone(),
            })
        } else {
            // 点击空白区域，清除选择（除非按住Ctrl）
            if !modifiers.ctrl && !modifiers.shift {
                self.clear_selection();
                Ok(InteractionResult::Select {
                    elements: Vec::new(),
                })
            } else {
                Ok(InteractionResult::None)
            }
        }
    }
    
    /// 开始框选
    pub fn start_box_select(&mut self, position: Point) -> Result<InteractionResult> {
        if !self.config.enabled || !self.config.box_select.enabled {
            return Ok(InteractionResult::None);
        }
        
        self.state.is_box_selecting = true;
        self.state.box_select_start = Some(position);
        self.state.box_select_current = Some(position);
        self.state.box_select_bounds = Some(Bounds {
            origin: position,
            size: echarts_core::Size { width: 0.0, height: 0.0 },
        });
        
        Ok(InteractionResult::Redraw)
    }
    
    /// 更新框选
    pub fn update_box_select(&mut self, position: Point) -> Result<InteractionResult> {
        if !self.state.is_box_selecting {
            return Ok(InteractionResult::None);
        }
        
        if let Some(start) = self.state.box_select_start {
            self.state.box_select_current = Some(position);
            
            // 计算框选区域
            let min_x = start.x.min(position.x);
            let min_y = start.y.min(position.y);
            let max_x = start.x.max(position.x);
            let max_y = start.y.max(position.y);
            
            self.state.box_select_bounds = Some(Bounds {
                origin: Point { x: min_x, y: min_y },
                size: echarts_core::Size {
                    width: max_x - min_x,
                    height: max_y - min_y,
                },
            });
            
            Ok(InteractionResult::Redraw)
        } else {
            Ok(InteractionResult::None)
        }
    }
    
    /// 完成框选
    pub fn finish_box_select(
        &mut self, 
        chart_bounds: Bounds, 
        series: &[Box<dyn Series>], 
        modifiers: &KeyModifiers
    ) -> Result<InteractionResult> {
        if !self.state.is_box_selecting {
            return Ok(InteractionResult::None);
        }
        
        let result = if let Some(select_bounds) = self.state.box_select_bounds {
            // 检查框选区域大小
            if select_bounds.size.width >= self.config.box_select.min_size && 
               select_bounds.size.height >= self.config.box_select.min_size {
                
                // 查找框选区域内的元素
                let selected_elements = self.find_elements_in_bounds(select_bounds, chart_bounds, series)?;
                
                // 处理多选逻辑
                if !modifiers.ctrl && !modifiers.shift {
                    self.state.selected_elements.clear();
                }
                
                // 添加新选中的元素
                for element in selected_elements {
                    if !self.is_element_selected(&element) {
                        self.state.selected_elements.push(element);
                    }
                }
                
                InteractionResult::Select {
                    elements: self.state.selected_elements.clone(),
                }
            } else {
                InteractionResult::None
            }
        } else {
            InteractionResult::None
        };
        
        // 重置框选状态
        self.state.is_box_selecting = false;
        self.state.box_select_start = None;
        self.state.box_select_current = None;
        self.state.box_select_bounds = None;
        
        Ok(result)
    }
    
    /// 处理元素选择逻辑
    fn handle_element_selection(&mut self, element: SelectedElement, modifiers: &KeyModifiers) -> Result<()> {
        match self.config.mode {
            SelectionMode::Single => {
                // 单选模式，清除之前的选择
                self.state.selected_elements.clear();
                self.state.selected_elements.push(element);
            }
            SelectionMode::Multiple => {
                if modifiers.ctrl {
                    // Ctrl+点击：切换选择状态
                    if let Some(index) = self.find_selected_element_index(&element) {
                        self.state.selected_elements.remove(index);
                    } else {
                        self.state.selected_elements.push(element);
                    }
                } else if modifiers.shift {
                    // Shift+点击：范围选择（暂时简单实现为添加）
                    if !self.is_element_selected(&element) {
                        self.state.selected_elements.push(element);
                    }
                } else {
                    // 普通点击：清除之前的选择，选择当前元素
                    self.state.selected_elements.clear();
                    self.state.selected_elements.push(element);
                }
            }
            _ => {
                // 其他模式暂时按多选处理
                if !self.is_element_selected(&element) {
                    self.state.selected_elements.push(element);
                }
            }
        }
        
        Ok(())
    }
    
    /// 查找指定位置的元素
    fn find_element_at_position(
        &self, 
        _position: Point, 
        _chart_bounds: Bounds, 
        _series: &[Box<dyn Series>]
    ) -> Result<Option<SelectedElement>> {
        // 这里需要实现具体的点击测试逻辑
        // 暂时返回None，具体实现需要在各个系列中添加hit_test方法
        Ok(None)
    }
    
    /// 查找指定区域内的元素
    fn find_elements_in_bounds(
        &self, 
        _bounds: Bounds, 
        _chart_bounds: Bounds, 
        _series: &[Box<dyn Series>]
    ) -> Result<Vec<SelectedElement>> {
        // 这里需要实现具体的区域测试逻辑
        // 暂时返回空向量，具体实现需要在各个系列中添加bounds_test方法
        Ok(Vec::new())
    }
    
    /// 检查元素是否已选中
    fn is_element_selected(&self, element: &SelectedElement) -> bool {
        self.state.selected_elements.iter().any(|selected| {
            selected.series_index == element.series_index && 
            selected.data_index == element.data_index &&
            selected.element_type == element.element_type
        })
    }
    
    /// 查找选中元素的索引
    fn find_selected_element_index(&self, element: &SelectedElement) -> Option<usize> {
        self.state.selected_elements.iter().position(|selected| {
            selected.series_index == element.series_index && 
            selected.data_index == element.data_index &&
            selected.element_type == element.element_type
        })
    }
    
    /// 清除所有选择
    pub fn clear_selection(&mut self) {
        self.state.selected_elements.clear();
    }
    
    /// 清除选择
    pub fn clear(&mut self) {
        self.clear_selection();
        self.state.is_box_selecting = false;
        self.state.box_select_start = None;
        self.state.box_select_current = None;
        self.state.box_select_bounds = None;
    }
    
    /// 获取选中的元素
    pub fn get_selected_elements(&self) -> &[SelectedElement] {
        &self.state.selected_elements
    }
    
    /// 获取当前状态
    pub fn get_state(&self) -> &SelectionState {
        &self.state
    }
    
    /// 是否有选中的元素
    pub fn has_selection(&self) -> bool {
        !self.state.selected_elements.is_empty()
    }
    
    /// 是否正在框选
    pub fn is_box_selecting(&self) -> bool {
        self.state.is_box_selecting
    }
}
