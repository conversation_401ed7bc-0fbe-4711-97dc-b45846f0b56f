//! 实用的预计算命令使用示例
//!
//! 展示在实际应用中如何有效使用预计算的 DrawCommand

use echarts_rs::{LineSeries, BarSeries, Color, CartesianCoordinateSystem, Series};
use echarts_core::{Bounds as EchartsBounds, DrawCommand};
use gpui_renderer::{EChartsCanvas, EChartsElement};
use gpui::*;
use std::collections::HashMap;

fn main() {
    println!("🚀 启动实用预计算演示...");
    
    Application::new().run(move |cx| {
        let window_size = size(px(1200.0), px(800.0));
        let window_bounds = Bounds::centered(None, window_size, cx);
        
        let options = WindowOptions {
            window_bounds: Some(WindowBounds::Windowed(window_bounds)),
            titlebar: Some(TitlebarOptions {
                title: Some("实用预计算演示".into()),
                appears_transparent: false,
                traffic_light_position: None,
            }),
            focus: true,
            show: true,
            kind: WindowKind::Normal,
            is_movable: true,
            display_id: None,
            app_id: None,
            window_background: WindowBackgroundAppearance::Opaque,
            window_decorations: None,
            window_min_size: Some(size(px(1000.0), px(600.0))),
        };
        
        cx.open_window(options, |_window, cx| {
            cx.new_view(|_cx| PracticalDemo::new())
        }).expect("无法创建窗口");
    });
}

/// 图表缓存管理器
struct ChartCache {
    /// 缓存的绘制命令
    commands_cache: HashMap<String, Vec<DrawCommand>>,
    /// 缓存的画布
    canvas_cache: HashMap<String, EChartsCanvas>,
}

impl ChartCache {
    fn new() -> Self {
        Self {
            commands_cache: HashMap::new(),
            canvas_cache: HashMap::new(),
        }
    }
    
    /// 预计算并缓存图表
    fn precompute_chart(&mut self, key: &str, series: Box<dyn Series>, coord_system: CartesianCoordinateSystem) -> Result<(), String> {
        println!("🔄 预计算图表: {}", key);
        
        // 生成绘制命令
        match series.render_to_commands(&coord_system) {
            Ok(commands) => {
                println!("✅ 图表 {} 预计算完成，生成 {} 个命令", key, commands.len());
                
                // 创建画布
                let canvas = EChartsCanvas::new(series, coord_system).with_debug(true);
                
                // 缓存命令和画布
                self.commands_cache.insert(key.to_string(), commands);
                self.canvas_cache.insert(key.to_string(), canvas);
                
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("预计算失败: {:?}", e);
                println!("❌ {}", error_msg);
                Err(error_msg)
            }
        }
    }
    
    /// 获取预计算的图表元素
    fn get_precomputed_element(&self, key: &str) -> Option<EChartsElement> {
        if let (Some(commands), Some(canvas)) = (
            self.commands_cache.get(key),
            self.canvas_cache.get(key)
        ) {
            Some(EChartsElement::with_commands(canvas.clone(), commands.clone()))
        } else {
            None
        }
    }
    
    /// 检查是否已缓存
    fn is_cached(&self, key: &str) -> bool {
        self.commands_cache.contains_key(key) && self.canvas_cache.contains_key(key)
    }
    
    /// 清除缓存
    fn clear_cache(&mut self, key: &str) {
        self.commands_cache.remove(key);
        self.canvas_cache.remove(key);
        println!("🗑️ 清除缓存: {}", key);
    }
}

struct PracticalDemo {
    chart_cache: ChartCache,
    current_chart: String,
    available_charts: Vec<String>,
}

impl PracticalDemo {
    fn new() -> Self {
        let mut demo = Self {
            chart_cache: ChartCache::new(),
            current_chart: "sales_line".to_string(),
            available_charts: vec![
                "sales_line".to_string(),
                "revenue_bar".to_string(),
                "growth_line".to_string(),
            ],
        };
        
        // 预计算所有图表
        demo.precompute_all_charts();
        
        demo
    }
    
    fn precompute_all_charts(&mut self) {
        println!("🔄 开始预计算所有图表...");
        
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(0.0, 0.0, 800.0, 400.0),
            (0.0, 13.0),
            (0.0, 500.0),
        );
        
        // 1. 销售折线图
        let sales_data = vec![
            (1.0, 120.0), (2.0, 132.0), (3.0, 101.0), (4.0, 134.0),
            (5.0, 90.0), (6.0, 230.0), (7.0, 210.0), (8.0, 182.0),
            (9.0, 191.0), (10.0, 234.0), (11.0, 290.0), (12.0, 330.0),
        ];
        let sales_series = LineSeries::new("月销售额")
            .data(sales_data)
            .color(Color::rgb(0.2, 0.6, 1.0))
            .line_width(3.0)
            .smooth(true);
        
        let _ = self.chart_cache.precompute_chart(
            "sales_line",
            Box::new(sales_series),
            coord_system.clone()
        );
        
        // 2. 收入柱状图
        let revenue_data = vec![
            (1.0, 200.0), (2.0, 180.0), (3.0, 220.0), (4.0, 250.0),
            (5.0, 190.0), (6.0, 300.0), (7.0, 280.0), (8.0, 320.0),
            (9.0, 350.0), (10.0, 400.0), (11.0, 380.0), (12.0, 450.0),
        ];
        let revenue_series = BarSeries::new("月收入")
            .data(revenue_data)
            .color(Color::rgb(0.9, 0.4, 0.2))
            .bar_width(0.6);
        
        let _ = self.chart_cache.precompute_chart(
            "revenue_bar",
            Box::new(revenue_series),
            coord_system.clone()
        );
        
        // 3. 增长率折线图
        let growth_data = vec![
            (1.0, 5.0), (2.0, 8.0), (3.0, -2.0), (4.0, 12.0),
            (5.0, -5.0), (6.0, 25.0), (7.0, 15.0), (8.0, 18.0),
            (9.0, 22.0), (10.0, 30.0), (11.0, 28.0), (12.0, 35.0),
        ];
        let growth_series = LineSeries::new("增长率 %")
            .data(growth_data)
            .color(Color::rgb(0.1, 0.8, 0.3))
            .line_width(2.0)
            .smooth(false);
        
        let _ = self.chart_cache.precompute_chart(
            "growth_line",
            Box::new(growth_series),
            coord_system
        );
        
        println!("✅ 所有图表预计算完成");
    }
    
    fn switch_chart(&mut self, chart_key: &str) {
        if self.available_charts.contains(&chart_key.to_string()) {
            self.current_chart = chart_key.to_string();
            println!("🔄 切换到图表: {}", chart_key);
        }
    }
    
    fn get_chart_title(&self, key: &str) -> &str {
        match key {
            "sales_line" => "月销售额趋势",
            "revenue_bar" => "月收入统计",
            "growth_line" => "增长率变化",
            _ => "未知图表",
        }
    }
}

impl Render for PracticalDemo {
    fn render(&mut self, cx: &mut ViewContext<Self>) -> impl IntoElement {
        div()
            .flex()
            .flex_col()
            .size_full()
            .bg(rgb(0xf8f9fa))
            .p_4()
            .child(
                // 标题和控制面板
                div()
                    .flex()
                    .justify_between()
                    .items_center()
                    .mb_6()
                    .child(
                        div()
                            .text_xl()
                            .font_bold()
                            .text_color(rgb(0x1f2937))
                            .child("实用预计算演示")
                    )
                    .child(
                        // 图表切换按钮
                        div()
                            .flex()
                            .gap_2()
                            .children(
                                self.available_charts.iter().map(|chart_key| {
                                    let is_active = &self.current_chart == chart_key;
                                    let bg_color = if is_active { rgb(0x3b82f6) } else { rgb(0x6b7280) };
                                    
                                    div()
                                        .px_3()
                                        .py_2()
                                        .bg(bg_color)
                                        .text_color(rgb(0xffffff))
                                        .rounded_md()
                                        .cursor_pointer()
                                        .child(self.get_chart_title(chart_key))
                                        // 注意：这里需要实现点击事件处理
                                        // .on_click(cx.listener(move |this, _event, _cx| {
                                        //     this.switch_chart(chart_key);
                                        // }))
                                })
                            )
                    )
            )
            .child(
                // 主图表区域
                div()
                    .flex_1()
                    .flex()
                    .flex_col()
                    .child(
                        div()
                            .text_lg()
                            .font_semibold()
                            .text_color(rgb(0x374151))
                            .mb_4()
                            .child(format!("当前图表: {}", self.get_chart_title(&self.current_chart)))
                    )
                    .child(
                        div()
                            .w_full()
                            .h(px(500.0))
                            .bg(rgb(0xffffff))
                            .border_1()
                            .border_color(rgb(0xe5e7eb))
                            .rounded_lg()
                            .p_4()
                            .child(
                                if let Some(chart_element) = self.chart_cache.get_precomputed_element(&self.current_chart) {
                                    // 使用预计算的图表
                                    div().child("预计算图表（需要实现显示）")
                                } else {
                                    // 显示加载状态
                                    div()
                                        .flex()
                                        .items_center()
                                        .justify_center()
                                        .size_full()
                                        .text_color(rgb(0x6b7280))
                                        .child("图表加载中...")
                                }
                            )
                    )
            )
            .child(
                // 底部信息面板
                div()
                    .mt_4()
                    .p_4()
                    .bg(rgb(0xf3f4f6))
                    .rounded_lg()
                    .child(
                        div()
                            .flex()
                            .justify_between()
                            .child(
                                div()
                                    .text_sm()
                                    .text_color(rgb(0x6b7280))
                                    .child("💡 所有图表都使用预计算的绘制命令，切换时无需重新计算")
                            )
                            .child(
                                div()
                                    .text_sm()
                                    .text_color(rgb(0x6b7280))
                                    .child(format!("缓存状态: {}/{} 图表已缓存", 
                                        self.available_charts.iter().filter(|k| self.chart_cache.is_cached(k)).count(),
                                        self.available_charts.len()
                                    ))
                            )
                    )
            )
    }
}
