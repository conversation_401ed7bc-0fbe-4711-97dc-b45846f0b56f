//! 交互式折线图完整演示
//!
//! 展示所有新增的交互功能：
//! - 鼠标悬停高亮
//! - 点击选择数据点
//! - 数据变化动画
//! - 数据优化算法
//! - 实时数据更新
//! - 缩放和平移

use echarts_rs::{LineSeries, Color, CartesianCoordinateSystem, Bounds as EchartsBounds};
use echarts_charts::line::{LabelFormatType, OptimizationAlgorithm, AnimationType, InteractionEvent};
use gpui_renderer::GpuiRenderer;
use gpui::*;
use gpui_component::StyledExt;
use std::time::{Duration, Instant};

fn main() {
    println!("🚀 启动交互式折线图完整演示...");

    let app = Application::new();
    app.run(move |cx| {
        let window_size = size(px(1400.0), px(900.0));

        let window_options = WindowOptions {
            window_bounds: Some(WindowBounds::Windowed(Bounds::centered(
                None,
                window_size,
                cx,
            ))),
            titlebar: Some(TitlebarOptions {
                title: Some("🎮 交互式折线图完整演示 - ECharts-rs".into()),
                appears_transparent: false,
                traffic_light_position: None,
            }),
            window_background: WindowBackgroundAppearance::Opaque,
            focus: true,
            show: true,
            kind: WindowKind::Normal,
            is_movable: true,
            display_id: None,
            app_id: Some("interactive-line-chart".to_string()),
            window_decorations: None,
            window_min_size: Some(size(px(1000.0), px(700.0))),
        };

        cx.open_window(window_options, |cx| {
            println!("✅ 窗口已创建，正在初始化交互式演示...");
            cx.new_view(|_cx| InteractiveLineChartDemo::new())
        }).expect("无法创建窗口");
    });
}

/// 演示模式
#[derive(Debug, Clone, PartialEq)]
enum DemoMode {
    BasicInteraction,    // 基础交互
    DataAnimation,       // 数据动画
    DataOptimization,    // 数据优化
    RealTimeUpdate,      // 实时更新
    MultiSeries,         // 多系列
}

impl DemoMode {
    fn name(&self) -> &'static str {
        match self {
            DemoMode::BasicInteraction => "基础交互",
            DemoMode::DataAnimation => "数据动画",
            DemoMode::DataOptimization => "数据优化",
            DemoMode::RealTimeUpdate => "实时更新",
            DemoMode::MultiSeries => "多系列图表",
        }
    }

    fn description(&self) -> &'static str {
        match self {
            DemoMode::BasicInteraction => "鼠标悬停高亮、点击选择数据点",
            DemoMode::DataAnimation => "数据变化时的平滑动画效果",
            DemoMode::DataOptimization => "大数据集的LTTB和Douglas-Peucker优化",
            DemoMode::RealTimeUpdate => "模拟实时数据流更新",
            DemoMode::MultiSeries => "多条折线的交互和对比",
        }
    }
}

/// 交互式折线图演示应用
struct InteractiveLineChartDemo {
    current_mode: DemoMode,
    series_list: Vec<LineSeries>,
    coord_system: CartesianCoordinateSystem,
    renderer: GpuiRenderer,

    // 交互状态
    hovered_point: Option<(usize, usize)>,
    selected_points: Vec<(usize, usize)>,
    mouse_position: Option<Point<Pixels>>,

    // 动画状态
    animation_start: Option<Instant>,
    is_animating: bool,

    // 实时更新
    auto_update: bool,
    last_update: Instant,
    update_interval: Duration,

    // 数据生成
    data_generator: DataGenerator,
}

/// 数据生成器
struct DataGenerator {
    base_values: Vec<f64>,
    time_step: f64,
    counter: usize,
}

impl DataGenerator {
    fn new() -> Self {
        Self {
            base_values: vec![50.0, 60.0, 45.0, 70.0, 55.0, 80.0, 65.0, 90.0, 75.0, 85.0],
            time_step: 0.0,
            counter: 0,
        }
    }

    fn generate_data(&mut self, count: usize) -> Vec<(f64, f64)> {
        let mut data = Vec::new();
        for i in 0..count {
            let x = i as f64;
            let base_y = if i < self.base_values.len() {
                self.base_values[i]
            } else {
                50.0 + (i as f64 * 0.1).sin() * 20.0
            };

            // 添加伪随机噪声（基于计数器）
            let noise = ((self.counter + i) as f64 * 0.1).sin() * 5.0;
            let y = base_y + noise + (self.time_step * 0.1).sin() * 10.0;

            data.push((x, y.max(0.0).min(100.0)));
        }
        self.time_step += 1.0;
        self.counter += count;
        data
    }

    fn generate_large_dataset(&mut self, count: usize) -> Vec<(f64, f64)> {
        let mut data = Vec::new();
        for i in 0..count {
            let x = i as f64 * 0.01;
            let y = 50.0 +
                   (x * 0.5).sin() * 30.0 +
                   (x * 2.0).sin() * 10.0 +
                   (x * 5.0).sin() * 5.0 +
                   ((self.counter + i) as f64 * 0.01).sin() * 2.0;
            data.push((x, y.max(0.0).min(100.0)));
        }
        self.counter += count;
        data
    }
}

impl InteractiveLineChartDemo {
    fn new() -> Self {
        println!("🎯 初始化交互式折线图演示...");

        let mut data_generator = DataGenerator::new();
        let initial_data = data_generator.generate_data(10);

        let series = LineSeries::new("交互演示数据")
            .data(initial_data)
            .color(Color::rgb(0.2, 0.6, 1.0))
            .line_width(3.0)
            .show_symbols(true)
            .symbol_size(8.0)
            .y_axis_decimal_places(1)
            .smooth(true)
            .smoothness(0.4);

        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(0.0, 0.0, 10.0, 100.0),
            (0.0, 10.0),
            (0.0, 100.0)
        );

        println!("📊 创建了交互式折线图系列");

        Self {
            current_mode: DemoMode::BasicInteraction,
            series_list: vec![series],
            coord_system,
            renderer: GpuiRenderer::new().with_debug(true),
            hovered_point: None,
            selected_points: Vec::new(),
            mouse_position: None,
            animation_start: None,
            is_animating: false,
            auto_update: false,
            last_update: Instant::now(),
            update_interval: Duration::from_millis(500),
            data_generator,
        }
    }

    /// 切换演示模式
    fn switch_mode(&mut self, new_mode: DemoMode) {
        println!("🔄 切换到模式: {}", new_mode.name());
        self.current_mode = new_mode.clone();

        match new_mode {
            DemoMode::BasicInteraction => {
                self.setup_basic_interaction();
            }
            DemoMode::DataAnimation => {
                self.setup_data_animation();
            }
            DemoMode::DataOptimization => {
                self.setup_data_optimization();
            }
            DemoMode::RealTimeUpdate => {
                self.setup_real_time_update();
            }
            DemoMode::MultiSeries => {
                self.setup_multi_series();
            }
        }
    }

    fn setup_basic_interaction(&mut self) {
        let data = self.data_generator.generate_data(10);
        let mut series = LineSeries::new("基础交互演示")
            .data(data)
            .color(Color::rgb(0.2, 0.6, 1.0))
            .line_width(3.0)
            .show_symbols(true)
            .symbol_size(8.0);

        // 启用交互功能
        series.interaction.hover_enabled = true;
        series.interaction.click_enabled = true;
        series.interaction.tooltip_enabled = true;

        self.series_list = vec![series];
        self.auto_update = false;
    }

    fn setup_data_animation(&mut self) {
        let data = self.data_generator.generate_data(8);
        let mut series = LineSeries::new("动画演示")
            .data(data)
            .color(Color::rgb(1.0, 0.4, 0.2))
            .line_width(2.5)
            .show_symbols(true)
            .smooth(true);

        // 启用动画
        series.animation.enabled = true;
        series.animation.animation_type = AnimationType::DrawLine;
        series.animation.duration = Duration::from_millis(1500);

        self.series_list = vec![series];
        self.start_animation();
    }

    fn setup_data_optimization(&mut self) {
        let large_data = self.data_generator.generate_large_dataset(5000);
        let mut series = LineSeries::new("大数据优化演示")
            .data(large_data)
            .color(Color::rgb(0.2, 0.8, 0.4))
            .line_width(1.5)
            .show_symbols(false);

        // 启用数据优化
        series.optimization_enabled = true;
        series.optimization_target_points = 200;
        series.optimization_algorithm = OptimizationAlgorithm::LTTB;

        self.series_list = vec![series];
        println!("📊 生成了5000个数据点，优化到200个点");
    }

    fn setup_real_time_update(&mut self) {
        let data = self.data_generator.generate_data(15);
        let mut series = LineSeries::new("实时数据流")
            .data(data)
            .color(Color::rgb(0.8, 0.2, 0.6))
            .line_width(2.0)
            .show_symbols(true)
            .symbol_size(6.0);

        // 启用动画和实时更新
        series.animation.enabled = true;
        series.animation.duration = Duration::from_millis(300);

        self.series_list = vec![series];
        self.auto_update = true;
        println!("🔄 启动实时数据更新");
    }

    fn setup_multi_series(&mut self) {
        let data1 = self.data_generator.generate_data(12);

impl Render for InteractiveLineChartDemo {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("🎨 渲染交互式折线图演示界面...");

        // 更新实时数据
        self.update_real_time_data();

        div()
            .size_full()
            .bg(rgb(0xf8fafc))
            .flex()
            .flex_col()
            .child(
                // 标题栏
                div()
                    .w_full()
                    .h(px(80.0))
                    .bg(rgb(0x1f2937))
                    .flex()
                    .items_center()
                    .justify_between()
                    .px_6()
                    .child(
                        div()
                            .text_2xl()
                            .font_bold()
                            .text_color(rgb(0xffffff))
                            .child("🎮 交互式折线图完整演示")
                    )
                    .child(
                        div()
                            .text_sm()
                            .text_color(rgb(0x9ca3af))
                            .child(format!("当前模式: {}", self.current_mode.name()))
                    )
            )
            .child(
                // 主图表区域
                div()
                    .flex_1()
                    .p_6()
                    .child(
                        div()
                            .w_full()
                            .h_full()
                            .bg(rgb(0xffffff))
                            .border_1()
                            .border_color(rgb(0xd1d5db))
                            .rounded_lg()
                            .p_4()
                            .flex()
                            .items_center()
                            .justify_center()
                            .child(
                                div()
                                    .text_lg()
                                    .text_color(rgb(0x6b7280))
                                    .child("🎨 交互式 GPUI Canvas 绘制区域")
                            )
                    )
            )
            .child(
                // 状态栏
                div()
                    .w_full()
                    .h(px(40.0))
                    .bg(rgb(0x374151))
                    .flex()
                    .items_center()
                    .justify_center()
                    .child(
                        div()
                            .text_xs()
                            .text_color(rgb(0x9ca3af))
                            .child(format!("✅ 交互式折线图演示已就绪 - 模式: {}", self.current_mode.name()))
                    )
            )
    }
}
        let data2 = self.data_generator.generate_data(12);
        let data3 = self.data_generator.generate_data(12);

        let series1 = LineSeries::new("系列 A")
            .data(data1)
            .color(Color::rgb(0.2, 0.6, 1.0))
            .line_width(2.5)
            .show_symbols(true);

        let series2 = LineSeries::new("系列 B")
            .data(data2)
            .color(Color::rgb(1.0, 0.4, 0.2))
            .line_width(2.5)
            .show_symbols(true);

        let series3 = LineSeries::new("系列 C")
            .data(data3)
            .color(Color::rgb(0.2, 0.8, 0.4))
            .line_width(2.5)
            .show_symbols(true);

        self.series_list = vec![series1, series2, series3];
        println!("📊 创建了3个系列的多系列图表");
    }

    fn start_animation(&mut self) {
        self.animation_start = Some(Instant::now());
        self.is_animating = true;
    }

    fn update_real_time_data(&mut self) {
        if !self.auto_update {
            return;
        }

        let now = Instant::now();
        if now.duration_since(self.last_update) >= self.update_interval {
            // 更新第一个系列的数据
            if let Some(series) = self.series_list.get_mut(0) {
                let new_data = self.data_generator.generate_data(15);
                series.animate_data_change(echarts_core::DataSet::from_xy_pairs(new_data));
            }
            self.last_update = now;
        }
    }
}
