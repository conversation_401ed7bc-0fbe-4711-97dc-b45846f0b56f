//! RenderContext API 演示
//!
//! 展示统一的 RenderContext API 的使用方法

use echarts_core::{
    render_context::{RenderContext, SimpleTheme, PerformanceHint},
    Bounds, Color, Point,
    draw_commands::{LineStyle, LineCap, LineJoin},
};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎨 RenderContext API 演示");

    // 1. 创建基础渲染上下文
    let mut ctx = RenderContext::new();
    println!("✅ 创建基础渲染上下文: {}x{}", ctx.bounds().size.width, ctx.bounds().size.height);

    // 2. 创建自定义配置的渲染上下文
    let bounds = Bounds::new(0.0, 0.0, 1200.0, 800.0);
    let theme = SimpleTheme {
        name: "demo".to_string(),
        ..Default::default()
    };
    let mut custom_ctx = RenderContext::with_config(bounds, theme, PerformanceHint::Quality);
    println!("✅ 创建自定义渲染上下文: 主题={}, 性能提示={:?}", 
             custom_ctx.theme().name, custom_ctx.performance_hint());

    // 3. 演示绘制命令
    println!("\n📊 绘制命令演示:");
    
    // 绘制线条
    let line_style = LineStyle {
        color: Color::rgb(1.0, 0.0, 0.0),
        width: 3.0,
        opacity: 1.0,
        dash_pattern: None,
        cap: LineCap::Round,
        join: LineJoin::Round,
    };
    ctx.draw_line(Point::new(10.0, 10.0), Point::new(100.0, 100.0), line_style);
    println!("  - 绘制红色线条");

    // 绘制圆形（使用当前样式）
    ctx.draw_circle_styled(Point::new(150.0, 50.0), 25.0);
    println!("  - 绘制圆形");

    // 绘制矩形
    let rect_bounds = Bounds::new(200.0, 20.0, 80.0, 60.0);
    ctx.draw_rect_styled(rect_bounds);
    println!("  - 绘制矩形");

    // 绘制文本
    ctx.draw_text_styled("Hello RenderContext!".to_string(), Point::new(50.0, 200.0));
    println!("  - 绘制文本");

    println!("  总共生成了 {} 个绘制命令", ctx.commands().len());

    // 4. 演示批量操作
    println!("\n🔄 批量操作演示:");
    let mut batch_ctx = RenderContext::new();
    
    batch_ctx.with_batch(|ctx| {
        for i in 0..5 {
            let x = i as f64 * 30.0;
            ctx.draw_circle_styled(Point::new(x, 100.0), 10.0);
        }
    });
    
    println!("  批量绘制了 {} 个圆形", batch_ctx.commands().len());

    // 5. 演示变换操作
    println!("\n🔄 变换操作演示:");
    let mut transform_ctx = RenderContext::new();
    
    // 原始点
    let original_point = Point::new(50.0, 50.0);
    println!("  原始点: ({}, {})", original_point.x, original_point.y);
    
    // 应用平移
    transform_ctx.translate(100.0, 50.0);
    let translated = transform_ctx.transform_point(original_point);
    println!("  平移后: ({}, {})", translated.x, translated.y);
    
    // 应用缩放
    transform_ctx.scale(2.0, 1.5);
    let scaled = transform_ctx.transform_point(original_point);
    println!("  缩放后: ({}, {})", scaled.x, scaled.y);

    // 6. 演示样式栈
    println!("\n🎨 样式栈演示:");
    let mut style_ctx = RenderContext::new();
    
    let original_color = style_ctx.current_style().fill_color;
    println!("  原始填充色: RGB({}, {}, {})", 
             original_color.r, original_color.g, original_color.b);
    
    style_ctx.with_style(|ctx| {
        let mut style = ctx.current_style().clone();
        style.fill_color = Color::rgb(0.0, 1.0, 0.0);
        ctx.set_style(style);
        
        let green_color = ctx.current_style().fill_color;
        println!("  临时填充色: RGB({}, {}, {})", 
                 green_color.r, green_color.g, green_color.b);
    });
    
    let restored_color = style_ctx.current_style().fill_color;
    println!("  恢复填充色: RGB({}, {}, {})", 
             restored_color.r, restored_color.g, restored_color.b);

    // 7. 演示扩展属性
    println!("\n🔧 扩展属性演示:");
    let mut ext_ctx = RenderContext::new();
    
    ext_ctx.set_extension("renderer".to_string(), "svg".to_string());
    ext_ctx.set_extension("quality".to_string(), "high".to_string());
    ext_ctx.set_extension("animation".to_string(), "enabled".to_string());
    
    println!("  设置了 {} 个扩展属性", ext_ctx.extensions().len());
    for (key, value) in ext_ctx.extensions() {
        println!("    {}: {}", key, value);
    }

    // 8. 演示主题系统
    println!("\n🌈 主题系统演示:");
    let theme = SimpleTheme::default();
    println!("  默认主题: {}", theme.name);
    println!("  调色板包含 {} 种颜色", theme.color_palette.len());
    println!("  背景色: RGB({}, {}, {})", 
             theme.background_color.r, theme.background_color.g, theme.background_color.b);
    
    for (i, color) in theme.color_palette.iter().take(3).enumerate() {
        println!("    颜色{}: RGB({:.2}, {:.2}, {:.2})", 
                 i + 1, color.r, color.g, color.b);
    }

    println!("\n🎉 RenderContext API 演示完成！");
    println!("✨ 统一的 RenderContext 提供了强大而灵活的绘制能力");

    Ok(())
}
