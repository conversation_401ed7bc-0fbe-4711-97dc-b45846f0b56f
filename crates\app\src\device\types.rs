use std::fmt;
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{debug, error, info, warn};
use tsdaq_protocol::*;

/// 设备状态枚举
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum DeviceStatus {
    /// 设备未连接
    Disconnected,
    /// 设备已连接但未采集
    Connected,
    /// 设备正在采集数据
    Running,
    /// 设备处于错误状态
    Error,
}

impl fmt::Display for DeviceStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Disconnected => write!(f, "未连接"),
            Self::Connected => write!(f, "已连接"),
            Self::Running => write!(f, "采集中"),
            Self::Error => write!(f, "错误"),
        }
    }
}

/// 设备句柄，表示一个已连接的设备
#[derive(Clone)]
pub struct DeviceHandle {
    /// 设备端口名称
    pub port: String,
    /// 设备客户端，使用Arc<Mutex<>>包装以支持线程间安全共享
    pub client: Arc<Mutex<TSDAQClient>>,
    /// 设备运行状态
    pub running: Arc<Mutex<bool>>,
}

impl fmt::Debug for DeviceHandle {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("DeviceHandle")
            .field("port", &self.port)
            .field("running", &"<Mutex<bool>>")
            .field("client", &"<Mutex<TSDAQClient>>")
            .finish()
    }
}

impl DeviceHandle {
    /// 创建新的设备句柄
    pub fn new(port: String, client: TSDAQClient) -> Self {
        Self {
            port,
            client: Arc::new(Mutex::new(client)),
            running: Arc::new(Mutex::new(false)),
        }
    }

    /// 断开设备连接
    pub async fn disconnect(&self) -> Result<(), ProtocolError> {
        debug!("[{}] 获取客户端锁", self.port);
        let mut client = self.client.lock().await;
        debug!("[{}] 发送断开连接命令", self.port);
        client.disconnect().await
    }
    /// 连接设备
    pub async fn connect(&self) -> Result<(), ProtocolError> {
        debug!("[{}] 获取客户端锁", self.port);
        let mut client = self.client.lock().await;
        debug!("[{}] 发送连接命令", self.port);
        client.connect().await
    }

    /// 停止数据采集
    pub async fn stop_collection(&self) -> Result<(), ProtocolError> {
        // 先设置运行状态为false，确保数据流处理循环可以退出
        *self.running.lock().await = false;
        let mut client = self.client.lock().await;
        client.stop_collection().await
    }

    /// 开始数据采集
    pub async fn start_collection(&self) -> Result<(), ProtocolError> {
        debug!("[{}] 获取客户端锁", self.port);
        let mut client = self.client.lock().await;
        debug!("[{}] 发送开始采集命令", self.port);
        let result = client.start_collection().await;

        // 如果命令成功，将运行状态设置为true
        if result.is_ok() {
            debug!("[{}] 设置运行状态为 true", self.port);
            *self.running.lock().await = true;
        }

        result
    }
    /// 检查设备是否正在采集数据
    pub async fn is_running(&self) -> bool {
        *self.running.lock().await
    }

    /// 设置设备运行状态
    pub async fn set_running(&self, state: bool) {
        debug!("[{}] 设置运行状态为 {}", self.port, state);
        *self.running.lock().await = state;
    }

    /// 获取设备当前状态
    pub async fn get_status(&self) -> DeviceStatus {
        if self.is_running().await {
            DeviceStatus::Running
        } else {
            DeviceStatus::Connected
        }
    }
}
