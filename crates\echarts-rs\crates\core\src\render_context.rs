//! 统一的渲染上下文 API
//!
//! 提供统一的接口来收集和管理绘制命令，支持多种渲染后端

use crate::{
    Bounds, Color, DrawCommand, Point, LineStyle, TextStyle,
    draw_commands::{CircleStyle, RectStyle, PathCommand, PathStyle},
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 渲染上下文主题结构（简化版，避免循环依赖）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RenderTheme {
    /// 主题名称
    pub name: String,

    /// 调色板
    pub color_palette: Vec<Color>,

    /// 背景颜色
    pub background_color: Color,

    /// 默认文本颜色
    pub text_color: Color,

    /// 默认线条颜色
    pub line_color: Color,
}

impl Default for RenderTheme {
    fn default() -> Self {
        Self {
            name: "default".to_string(),
            color_palette: vec![
                Color::rgb(0.33, 0.44, 0.78), // #5470c6
                Color::rgb(0.57, 0.78, 0.49), // #91cc75
                Color::rgb(0.98, 0.75, 0.37), // #fac858
                Color::rgb(0.93, 0.40, 0.26), // #ee6666
                Color::rgb(0.45, 0.69, 0.84), // #73c0de
                Color::rgb(0.42, 0.76, 0.64), // #3ba272
                Color::rgb(0.98, 0.55, 0.38), // #fc8452
                Color::rgb(0.58, 0.55, 0.78), // #9a60b4
            ],
            background_color: Color::rgb(1.0, 1.0, 1.0), // 白色
            text_color: Color::rgb(0.2, 0.2, 0.2),       // 深灰色
            line_color: Color::rgb(0.5, 0.5, 0.5),       // 中灰色
        }
    }
}

/// 统一的渲染上下文
/// 
/// 这是新架构的核心组件，负责：
/// 1. 收集 DrawCommand
/// 2. 管理渲染状态
/// 3. 提供便捷的绘制方法
/// 4. 支持批量操作和优化
#[derive(Debug, Clone)]
pub struct RenderContext {
    /// 收集的绘制命令
    commands: Vec<DrawCommand>,
    
    /// 当前渲染边界
    bounds: Bounds,
    
    /// 主题配置
    theme: RenderTheme,
    
    /// 当前样式状态
    current_style: StyleState,
    
    /// 样式栈（用于 save/restore）
    style_stack: Vec<StyleState>,
    
    /// 变换矩阵栈
    transform_stack: Vec<Transform>,
    
    /// 当前变换
    current_transform: Transform,
    
    /// 批量模式
    batching: bool,
    
    /// 批量缓冲区
    batch_buffer: Vec<DrawCommand>,
    
    /// 性能提示
    performance_hint: PerformanceHint,
    
    /// 扩展属性（用于特定渲染器的自定义数据）
    extensions: HashMap<String, String>,
}

/// 样式状态
#[derive(Debug, Clone)]
pub struct StyleState {
    /// 默认线条样式
    pub line_style: LineStyle,
    
    /// 默认填充颜色
    pub fill_color: Color,
    
    /// 默认文本样式
    pub text_style: TextStyle,
    
    /// 透明度
    pub opacity: f64,
}

/// 2D 变换矩阵
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct Transform {
    /// 变换矩阵 [a, b, c, d, e, f]
    /// 对应 CSS transform matrix(a, b, c, d, e, f)
    pub matrix: [f64; 6],
}

/// 性能提示
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PerformanceHint {
    /// 优化质量
    Quality,
    /// 平衡模式
    Balanced,
    /// 优化速度
    Speed,
    /// 批量模式
    Batch,
}

impl Default for PerformanceHint {
    fn default() -> Self {
        Self::Balanced
    }
}

impl Default for StyleState {
    fn default() -> Self {
        Self {
            line_style: LineStyle {
                color: Color::rgb(0.0, 0.0, 0.0),
                width: 1.0,
                opacity: 1.0,
                dash_pattern: None,
                cap: crate::LineCap::Butt,
                join: crate::LineJoin::Miter,
            },
            fill_color: Color::rgb(0.0, 0.0, 0.0),
            text_style: TextStyle {
                font_family: "Arial".to_string(),
                font_size: 12.0,
                font_weight: crate::FontWeight::Normal,
                font_style: crate::FontStyle::Normal,
                color: Color::rgb(0.0, 0.0, 0.0),
                opacity: 1.0,
                align: crate::TextAlign::Left,
                baseline: crate::TextBaseline::Alphabetic,
                rotation: 0.0,
                letter_spacing: 0.0,
                line_height: 1.0,
            },
            opacity: 1.0,
        }
    }
}

impl Default for Transform {
    fn default() -> Self {
        Self {
            matrix: [1.0, 0.0, 0.0, 1.0, 0.0, 0.0], // 单位矩阵
        }
    }
}

impl Transform {
    /// 创建单位变换
    pub fn identity() -> Self {
        Self::default()
    }
    
    /// 创建平移变换
    pub fn translate(x: f64, y: f64) -> Self {
        Self {
            matrix: [1.0, 0.0, 0.0, 1.0, x, y],
        }
    }
    
    /// 创建缩放变换
    pub fn scale(sx: f64, sy: f64) -> Self {
        Self {
            matrix: [sx, 0.0, 0.0, sy, 0.0, 0.0],
        }
    }
    
    /// 创建旋转变换
    pub fn rotate(angle: f64) -> Self {
        let cos_a = angle.cos();
        let sin_a = angle.sin();
        Self {
            matrix: [cos_a, sin_a, -sin_a, cos_a, 0.0, 0.0],
        }
    }
    
    /// 变换点
    pub fn transform_point(&self, point: Point) -> Point {
        let [a, b, c, d, e, f] = self.matrix;
        Point::new(
            a * point.x + c * point.y + e,
            b * point.x + d * point.y + f,
        )
    }
}

impl RenderContext {
    /// 创建新的渲染上下文
    pub fn new() -> Self {
        Self {
            commands: Vec::new(),
            bounds: Bounds::new(0.0, 0.0, 800.0, 600.0),
            theme: RenderTheme::default(),
            current_style: StyleState::default(),
            style_stack: Vec::new(),
            transform_stack: Vec::new(),
            current_transform: Transform::default(),
            batching: false,
            batch_buffer: Vec::new(),
            performance_hint: PerformanceHint::default(),
            extensions: HashMap::new(),
        }
    }
    
    /// 使用主题创建渲染上下文
    pub fn with_theme(theme: RenderTheme) -> Self {
        let mut ctx = Self::new();
        ctx.theme = theme;
        ctx
    }
    
    /// 使用边界创建渲染上下文
    pub fn new_with_bounds(bounds: Bounds) -> Self {
        let mut ctx = Self::new();
        ctx.bounds = bounds;
        ctx
    }
    
    /// 完整配置创建渲染上下文
    pub fn with_config(bounds: Bounds, theme: RenderTheme, hint: PerformanceHint) -> Self {
        Self {
            commands: Vec::new(),
            bounds,
            theme,
            current_style: StyleState::default(),
            style_stack: Vec::new(),
            transform_stack: Vec::new(),
            current_transform: Transform::default(),
            batching: false,
            batch_buffer: Vec::new(),
            performance_hint: hint,
            extensions: HashMap::new(),
        }
    }

    // === 命令管理方法 ===

    /// 添加绘制命令
    pub fn add_command(&mut self, command: DrawCommand) {
        if self.batching {
            self.batch_buffer.push(command);
        } else {
            self.commands.push(command);
        }
    }

    /// 批量添加绘制命令
    pub fn add_commands(&mut self, commands: Vec<DrawCommand>) {
        if self.batching {
            self.batch_buffer.extend(commands);
        } else {
            self.commands.extend(commands);
        }
    }

    /// 获取所有绘制命令
    pub fn commands(&self) -> &[DrawCommand] {
        &self.commands
    }

    /// 取出所有绘制命令（清空内部缓冲区）
    pub fn take_commands(&mut self) -> Vec<DrawCommand> {
        let mut commands = std::mem::take(&mut self.commands);
        if !self.batch_buffer.is_empty() {
            commands.extend(std::mem::take(&mut self.batch_buffer));
        }
        commands
    }

    /// 清空所有命令
    pub fn clear(&mut self) {
        self.commands.clear();
        self.batch_buffer.clear();
    }

    // === 状态管理方法 ===

    /// 获取当前边界
    pub fn bounds(&self) -> Bounds {
        self.bounds
    }

    /// 设置边界
    pub fn set_bounds(&mut self, bounds: Bounds) {
        self.bounds = bounds;
    }

    /// 获取主题
    pub fn theme(&self) -> &RenderTheme {
        &self.theme
    }

    /// 设置主题
    pub fn set_theme(&mut self, theme: RenderTheme) {
        self.theme = theme;
    }

    /// 获取性能提示
    pub fn performance_hint(&self) -> PerformanceHint {
        self.performance_hint
    }

    /// 设置性能提示
    pub fn set_performance_hint(&mut self, hint: PerformanceHint) {
        self.performance_hint = hint;
    }

    // === 批量操作 ===

    /// 开始批量模式
    pub fn begin_batch(&mut self) {
        self.batching = true;
        self.batch_buffer.clear();
    }

    /// 结束批量模式并提交
    pub fn end_batch(&mut self) {
        if self.batching {
            self.commands.extend(std::mem::take(&mut self.batch_buffer));
            self.batching = false;
        }
    }

    /// 取消批量模式（丢弃缓冲的命令）
    pub fn cancel_batch(&mut self) {
        self.batch_buffer.clear();
        self.batching = false;
    }

    /// 是否在批量模式
    pub fn is_batching(&self) -> bool {
        self.batching
    }

    // === 样式栈管理 ===

    /// 保存当前样式状态
    pub fn save_style(&mut self) {
        self.style_stack.push(self.current_style.clone());
    }

    /// 恢复上一个样式状态
    pub fn restore_style(&mut self) {
        if let Some(style) = self.style_stack.pop() {
            self.current_style = style;
        }
    }

    /// 获取当前样式
    pub fn current_style(&self) -> &StyleState {
        &self.current_style
    }

    /// 设置当前样式
    pub fn set_style(&mut self, style: StyleState) {
        self.current_style = style;
    }

    // === 变换管理 ===

    /// 保存当前变换
    pub fn save_transform(&mut self) {
        self.transform_stack.push(self.current_transform);
    }

    /// 恢复上一个变换
    pub fn restore_transform(&mut self) {
        if let Some(transform) = self.transform_stack.pop() {
            self.current_transform = transform;
        }
    }

    /// 应用变换
    pub fn transform(&mut self, transform: Transform) {
        // 矩阵乘法：current = transform * current
        let [a1, b1, c1, d1, e1, f1] = transform.matrix;
        let [a2, b2, c2, d2, e2, f2] = self.current_transform.matrix;

        self.current_transform.matrix = [
            a1 * a2 + b1 * c2,
            a1 * b2 + b1 * d2,
            c1 * a2 + d1 * c2,
            c1 * b2 + d1 * d2,
            e1 * a2 + f1 * c2 + e2,
            e1 * b2 + f1 * d2 + f2,
        ];
    }

    /// 平移
    pub fn translate(&mut self, x: f64, y: f64) {
        self.transform(Transform::translate(x, y));
    }

    /// 缩放
    pub fn scale(&mut self, sx: f64, sy: f64) {
        self.transform(Transform::scale(sx, sy));
    }

    /// 旋转
    pub fn rotate(&mut self, angle: f64) {
        self.transform(Transform::rotate(angle));
    }

    /// 变换点
    pub fn transform_point(&self, point: Point) -> Point {
        self.current_transform.transform_point(point)
    }

    // === 便捷绘制方法 ===

    /// 绘制线条
    pub fn draw_line(&mut self, from: Point, to: Point, style: LineStyle) {
        let from = self.transform_point(from);
        let to = self.transform_point(to);

        self.add_command(DrawCommand::Line { from, to, style });
    }

    /// 使用当前样式绘制线条
    pub fn draw_line_styled(&mut self, from: Point, to: Point) {
        self.draw_line(from, to, self.current_style.line_style.clone());
    }

    /// 绘制圆形
    pub fn draw_circle(&mut self, center: Point, radius: f64, style: CircleStyle) {
        let center = self.transform_point(center);

        self.add_command(DrawCommand::Circle { center, radius, style });
    }

    /// 使用当前样式绘制圆形
    pub fn draw_circle_styled(&mut self, center: Point, radius: f64) {
        let style = CircleStyle {
            fill: Some(self.current_style.fill_color),
            stroke: Some(self.current_style.line_style.clone()),
            opacity: self.current_style.opacity,
        };
        self.draw_circle(center, radius, style);
    }

    /// 绘制矩形
    pub fn draw_rect(&mut self, bounds: Bounds, style: RectStyle) {
        // 变换矩形的四个角点
        let origin = self.transform_point(bounds.origin);
        let size = bounds.size; // 注意：这里可能需要更复杂的变换处理

        let transformed_bounds = Bounds::new(origin.x, origin.y, size.width, size.height);

        self.add_command(DrawCommand::Rect {
            bounds: transformed_bounds,
            style
        });
    }

    /// 使用当前样式绘制矩形
    pub fn draw_rect_styled(&mut self, bounds: Bounds) {
        let style = RectStyle {
            fill: Some(self.current_style.fill_color),
            stroke: Some(self.current_style.line_style.clone()),
            opacity: self.current_style.opacity,
            corner_radius: 0.0,
        };
        self.draw_rect(bounds, style);
    }

    /// 绘制文本
    pub fn draw_text(&mut self, text: String, position: Point, style: TextStyle) {
        let position = self.transform_point(position);

        self.add_command(DrawCommand::Text { text, position, style });
    }

    /// 使用当前样式绘制文本
    pub fn draw_text_styled(&mut self, text: String, position: Point) {
        self.draw_text(text, position, self.current_style.text_style.clone());
    }

    /// 绘制路径
    pub fn draw_path(&mut self, commands: Vec<PathCommand>, style: PathStyle) {
        // 变换路径中的所有点
        let transformed_commands = commands.into_iter().map(|cmd| {
            match cmd {
                PathCommand::MoveTo(p) => PathCommand::MoveTo(self.transform_point(p)),
                PathCommand::LineTo(p) => PathCommand::LineTo(self.transform_point(p)),
                PathCommand::CurveTo { control1, control2, to } => PathCommand::CurveTo {
                    control1: self.transform_point(control1),
                    control2: self.transform_point(control2),
                    to: self.transform_point(to),
                },
                PathCommand::QuadTo { control, to } => PathCommand::QuadTo {
                    control: self.transform_point(control),
                    to: self.transform_point(to),
                },
                PathCommand::Close => PathCommand::Close,
            }
        }).collect();

        self.add_command(DrawCommand::Path {
            commands: transformed_commands,
            style
        });
    }

    // === 扩展属性管理 ===

    /// 设置扩展属性
    pub fn set_extension(&mut self, key: String, value: String) {
        self.extensions.insert(key, value);
    }

    /// 获取扩展属性
    pub fn get_extension(&self, key: &str) -> Option<&String> {
        self.extensions.get(key)
    }

    /// 移除扩展属性
    pub fn remove_extension(&mut self, key: &str) -> Option<String> {
        self.extensions.remove(key)
    }

    /// 获取所有扩展属性
    pub fn extensions(&self) -> &HashMap<String, String> {
        &self.extensions
    }

    // === 便捷操作 ===

    /// 在指定区域内执行绘制操作
    pub fn with_bounds<F>(&mut self, bounds: Bounds, f: F)
    where
        F: FnOnce(&mut Self)
    {
        let old_bounds = self.bounds;
        self.bounds = bounds;
        f(self);
        self.bounds = old_bounds;
    }

    /// 在保存/恢复样式的情况下执行操作
    pub fn with_style<F>(&mut self, f: F)
    where
        F: FnOnce(&mut Self)
    {
        self.save_style();
        f(self);
        self.restore_style();
    }

    /// 在保存/恢复变换的情况下执行操作
    pub fn with_transform<F>(&mut self, f: F)
    where
        F: FnOnce(&mut Self)
    {
        self.save_transform();
        f(self);
        self.restore_transform();
    }

    /// 在批量模式下执行操作
    pub fn with_batch<F>(&mut self, f: F)
    where
        F: FnOnce(&mut Self)
    {
        self.begin_batch();
        f(self);
        self.end_batch();
    }
}

impl Default for RenderContext {
    fn default() -> Self {
        Self::new()
    }
}

// 包含测试模块
#[cfg(test)]
#[path = "render_context_test.rs"]
mod render_context_test;
