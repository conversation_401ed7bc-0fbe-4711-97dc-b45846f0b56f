//! 饼图演示
//!
//! 展示饼图、环形图和玫瑰图的使用方法

use echarts_rs::prelude::*;
use echarts_rs::{PieSeries, PieRadius, RoseType, PieLabelPosition, ChartBuilder};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🥧 饼图演示");

    // 1. 基础饼图
    println!("\n📊 基础饼图:");
    
    let basic_pie = PieSeries::new("基础饼图")
        .data(vec![
            ("浏览器", 335.0),
            ("邮件营销", 310.0),
            ("联盟广告", 234.0),
            ("视频广告", 135.0),
            ("搜索引擎", 1548.0),
        ])
        .center(0.5, 0.5)
        .radius(0.6)
        .start_angle(90.0)
        .label(PieLabelPosition::Outside, true);
    
    println!("  ✅ 基础饼图创建成功");
    println!("  - 名称: {}", basic_pie.name);
    println!("  - 类型: {:?}", basic_pie.series_type());
    println!("  - 数据项数: {}", basic_pie.data.len());
    println!("  - 中心位置: [{}, {}]", basic_pie.center[0], basic_pie.center[1]);
    
    // 2. 环形图
    println!("\n🍩 环形图:");
    
    let donut_chart = PieSeries::new("环形图")
        .data(vec![
            ("直接访问", 335.0),
            ("邮件营销", 310.0),
            ("联盟广告", 234.0),
            ("视频广告", 135.0),
            ("搜索引擎", 1548.0),
        ])
        .as_donut(0.4, 0.7)
        .label(PieLabelPosition::Inside, false);
    
    println!("  ✅ 环形图创建成功");
    match donut_chart.radius {
        PieRadius::Range(inner, outer) => {
            println!("  - 内半径: {}", inner);
            println!("  - 外半径: {}", outer);
        },
        _ => {}
    }
    
    // 3. 玫瑰图
    println!("\n🌹 玫瑰图:");
    
    let rose_chart = PieSeries::new("玫瑰图")
        .data(vec![
            ("rose1", 10.0),
            ("rose2", 5.0),
            ("rose3", 15.0),
            ("rose4", 25.0),
            ("rose5", 20.0),
            ("rose6", 35.0),
            ("rose7", 30.0),
            ("rose8", 40.0),
        ])
        .as_rose(RoseType::Radius)
        .radius(0.8)
        .label(PieLabelPosition::Outside, true);
    
    println!("  ✅ 玫瑰图创建成功");
    println!("  - 玫瑰图类型: {:?}", rose_chart.rose_type);
    
    // 4. 测试渲染
    println!("\n🎨 渲染测试:");
    
    // 创建坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 500.0, 500.0),
        (0.0, 1.0),
        (0.0, 1.0),
    );
    
    // 渲染基础饼图
    match basic_pie.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 基础饼图渲染成功: {} 个绘制命令", commands.len());
            
            // 统计不同类型的命令
            let mut path_count = 0;
            let mut text_count = 0;
            let mut line_count = 0;
            
            for cmd in &commands {
                match cmd {
                    DrawCommand::Path { .. } => path_count += 1,
                    DrawCommand::Text { .. } => text_count += 1,
                    DrawCommand::Line { .. } => line_count += 1,
                    _ => {}
                }
            }
            
            println!("    - 路径命令: {}", path_count);
            println!("    - 文本命令: {}", text_count);
            println!("    - 线条命令: {}", line_count);
        }
        Err(e) => {
            println!("  ❌ 渲染失败: {}", e);
        }
    }
    
    // 渲染环形图
    match donut_chart.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 环形图渲染成功: {} 个绘制命令", commands.len());
        }
        Err(e) => {
            println!("  ❌ 渲染失败: {}", e);
        }
    }
    
    // 渲染玫瑰图
    match rose_chart.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 玫瑰图渲染成功: {} 个绘制命令", commands.len());
        }
        Err(e) => {
            println!("  ❌ 渲染失败: {}", e);
        }
    }
    
    // 5. 测试类型擦除
    println!("\n🔄 测试类型擦除:");
    
    let series_list: Vec<Box<dyn Series>> = vec![
        Box::new(basic_pie),
        Box::new(donut_chart),
        Box::new(rose_chart),
    ];
    
    println!("  ✅ 类型擦除成功 - 可以在同一容器中存储不同配置的饼图");
    for (i, series) in series_list.iter().enumerate() {
        println!("    {}. {} ({})", i + 1, series.name(), series.series_type().as_str());
    }
    
    // 6. 创建完整图表
    println!("\n📈 创建完整图表:");
    
    let chart = Chart::new()
        .title("访问来源")
        .size(600.0, 400.0)
        .background_color(Color::rgb(0.98, 0.98, 0.98))
        .add_series(Box::new(PieSeries::new("访问来源")
            .data(vec![
                ("直接访问", 335.0),
                ("邮件营销", 310.0),
                ("联盟广告", 234.0),
                ("视频广告", 135.0),
                ("搜索引擎", 1548.0),
            ])
            .radius(0.7)
            .center(0.5, 0.5)
            .label(PieLabelPosition::Outside, true)));
    
    println!("  ✅ 完整图表创建成功");
    println!("  - 标题: {:?}", chart.title);
    println!("  - 大小: {}x{}", chart.width, chart.height);
    println!("  - 系列数: {}", chart.series.len());
    
    // 7. 使用 ChartBuilder
    println!("\n🏗️ 使用 ChartBuilder:");
    
    let pie_chart = ChartBuilder::new()
        .title("销售数据")
        .size(800.0, 600.0)
        .add_series(PieSeries::new("销售数据")
            .data(vec![
                ("产品A", 5000.0),
                ("产品B", 3500.0),
                ("产品C", 2000.0),
                ("产品D", 1000.0),
                ("产品E", 500.0),
            ])
            .as_donut(0.4, 0.7)
            .label(PieLabelPosition::Inside, false))
        .build();
    
    println!("  ✅ 使用 ChartBuilder 创建饼图成功");
    println!("  - 标题: {:?}", pie_chart.title);
    println!("  - 大小: {}x{}", pie_chart.width, pie_chart.height);
    println!("  - 系列数: {}", pie_chart.series.len());
    
    println!("\n🎉 饼图演示完成！");
    println!("✨ PieSeries 提供了丰富的饼图功能，包括环形图和玫瑰图");
    
    Ok(())
}
