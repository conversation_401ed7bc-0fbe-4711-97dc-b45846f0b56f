//! 简化的图表核心模块 - 重构版本
//!
//! 这个模块定义了清晰简化的图表架构：
//! 1. 统一的 Series trait（支持类型擦除）
//! 2. 基于 DrawCommand 的渲染系统
//! 3. 简化的 Chart 结构

use crate::{
    Bounds, ChartError, Color, CoordinateSystem, DataValue, DrawCommand, Point, Result,
};
use serde::{Deserialize, Serialize};
use std::fmt::Debug;

/// Chart series type enumeration - type-safe series identification
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SeriesType {
    /// Line chart series
    Line,
    /// Bar chart series
    Bar,
    /// Pie chart series
    Pie,
    /// Scatter plot series
    Scatter,
    /// Area chart series
    Area,
    /// Candlestick chart series
    Candlestick,
    /// Heatmap series
    Heatmap,
    /// Radar chart series
    Radar,
    /// Funnel chart series
    Funnel,
    /// Gauge chart series
    Gauge,
    /// Tree chart series
    Tree,
    /// Graph/Network chart series
    Graph,
    /// 3D Surface chart series
    Surface3D,
    /// Custom series type
    Custom(String),
}

impl SeriesType {
    /// Get the string representation of the series type
    pub fn as_str(&self) -> &str {
        match self {
            SeriesType::Line => "line",
            SeriesType::Bar => "bar",
            SeriesType::Pie => "pie",
            SeriesType::Scatter => "scatter",
            SeriesType::Area => "area",
            SeriesType::Candlestick => "candlestick",
            SeriesType::Heatmap => "heatmap",
            SeriesType::Radar => "radar",
            SeriesType::Funnel => "funnel",
            SeriesType::Gauge => "gauge",
            SeriesType::Tree => "tree",
            SeriesType::Graph => "graph",
            SeriesType::Surface3D => "surface3d",
            SeriesType::Custom(name) => name,
        }
    }
}

impl Default for SeriesType {
    fn default() -> Self {
        SeriesType::Line
    }
}

/// 统一的 Series trait - 支持类型擦除
/// 
/// 这是所有图表系列必须实现的核心 trait。
/// 设计为 dyn 兼容，避免泛型参数导致的对象安全问题。
pub trait Series: Debug + Send + Sync {
    /// 获取系列名称
    fn name(&self) -> &str;

    /// 获取系列类型
    fn series_type(&self) -> SeriesType;

    /// 渲染系列为绘制命令列表（类型擦除）
    /// 
    /// 这个方法将系列的渲染逻辑转换为一系列 DrawCommand，
    /// 避免了泛型参数，使 trait 保持 dyn 兼容。
    fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>>;

    /// 获取系列的边界框
    /// 
    /// 返回系列数据的边界框，用于自动缩放和布局计算。
    fn bounds(&self) -> Option<Bounds>;

    /// 检查系列是否可见
    fn is_visible(&self) -> bool {
        true
    }

    /// 获取系列的 Z 索引（绘制顺序）
    fn z_index(&self) -> i32 {
        0
    }

    /// 克隆系列（支持类型擦除）
    fn clone_series(&self) -> Box<dyn Series>;
}

/// 简化的图表结构
///
/// 这个结构只包含核心功能，避免了复杂的配置系统
#[derive(Debug)]
pub struct Chart {
    /// 图表标题
    pub title: Option<String>,
    
    /// 图表大小
    pub width: f64,
    pub height: f64,
    
    /// 背景颜色
    pub background_color: Option<Color>,
    
    /// 系列列表
    pub series: Vec<Box<dyn Series>>,
    
    /// 图表边距
    pub padding: [f64; 4], // [top, right, bottom, left]
}

impl Clone for Chart {
    fn clone(&self) -> Self {
        Self {
            title: self.title.clone(),
            width: self.width,
            height: self.height,
            background_color: self.background_color,
            series: self.series.iter().map(|s| s.clone_series()).collect(),
            padding: self.padding,
        }
    }
}

impl Chart {
    /// 创建新的图表
    pub fn new() -> Self {
        Self {
            title: None,
            width: 800.0,
            height: 600.0,
            background_color: None,
            series: Vec::new(),
            padding: [20.0, 20.0, 20.0, 20.0],
        }
    }

    /// 设置标题
    pub fn title<S: Into<String>>(mut self, title: S) -> Self {
        self.title = Some(title.into());
        self
    }

    /// 设置大小
    pub fn size(mut self, width: f64, height: f64) -> Self {
        self.width = width;
        self.height = height;
        self
    }

    /// 设置背景颜色
    pub fn background_color(mut self, color: Color) -> Self {
        self.background_color = Some(color);
        self
    }

    /// 添加系列
    pub fn add_series(mut self, series: Box<dyn Series>) -> Self {
        self.series.push(series);
        self
    }

    /// 设置边距
    pub fn padding(mut self, top: f64, right: f64, bottom: f64, left: f64) -> Self {
        self.padding = [top, right, bottom, left];
        self
    }

    /// 渲染图表为 DrawCommand 列表
    /// 
    /// 这是新架构的核心方法：
    /// 1. 计算布局
    /// 2. 创建坐标系统
    /// 3. 生成所有 DrawCommand
    pub fn render_to_commands(&self) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        // 计算绘图区域
        let plot_area = self.calculate_plot_area();

        // 绘制背景
        if let Some(bg_color) = self.background_color {
            commands.push(DrawCommand::Rect {
                bounds: Bounds::new(0.0, 0.0, self.width, self.height),
                style: crate::draw_commands::RectStyle {
                    fill: Some(bg_color),
                    stroke: None,
                    opacity: 1.0,
                    corner_radius: 0.0,
                },
            });
        }

        // 绘制标题
        if let Some(ref title) = self.title {
            commands.push(DrawCommand::Text {
                text: title.clone(),
                position: Point::new(self.width / 2.0, self.padding[0] / 2.0),
                style: crate::TextStyle {
                    font_family: "Arial".to_string(),
                    font_size: 16.0,
                    font_weight: crate::FontWeight::Bold,
                    font_style: crate::FontStyle::Normal,
                    color: Color::rgb(0.0, 0.0, 0.0),
                    opacity: 1.0,
                    align: crate::TextAlign::Center,
                    baseline: crate::TextBaseline::Middle,
                    rotation: 0.0,
                    letter_spacing: 0.0,
                    line_height: 1.0,
                },
            });
        }

        // 创建坐标系统
        let coord_system = self.create_coordinate_system(plot_area)?;

        // 渲染所有系列
        for series in &self.series {
            if series.is_visible() {
                let series_commands = series.render_to_commands(coord_system.as_ref())?;
                commands.extend(series_commands);
            }
        }

        Ok(commands)
    }

    /// 计算绘图区域
    fn calculate_plot_area(&self) -> Bounds {
        let title_height = if self.title.is_some() { 30.0 } else { 0.0 };
        
        Bounds::new(
            self.padding[3], // left
            self.padding[0] + title_height, // top
            self.width - self.padding[1] - self.padding[3], // width
            self.height - self.padding[0] - self.padding[2] - title_height, // height
        )
    }

    /// 创建坐标系统
    fn create_coordinate_system(&self, plot_area: Bounds) -> Result<Box<dyn CoordinateSystem>> {
        // 计算数据范围
        let mut min_x = f64::INFINITY;
        let mut max_x = f64::NEG_INFINITY;
        let mut min_y = f64::INFINITY;
        let mut max_y = f64::NEG_INFINITY;

        for series in &self.series {
            if let Some(bounds) = series.bounds() {
                min_x = min_x.min(bounds.origin.x);
                max_x = max_x.max(bounds.origin.x + bounds.size.width);
                min_y = min_y.min(bounds.origin.y);
                max_y = max_y.max(bounds.origin.y + bounds.size.height);
            }
        }

        // 如果没有数据，使用默认范围
        if !min_x.is_finite() {
            min_x = 0.0;
            max_x = 10.0;
            min_y = 0.0;
            max_y = 10.0;
        }

        // 创建笛卡尔坐标系
        Ok(Box::new(crate::CartesianCoordinateSystem::new(
            plot_area,
            (min_x, max_x),
            (min_y, max_y),
        )))
    }
}

impl Default for Chart {
    fn default() -> Self {
        Self::new()
    }
}

/// 简化的渲染上下文
/// 
/// 用于收集 DrawCommand，而不是直接调用渲染器
#[derive(Debug, Clone)]
pub struct RenderContext {
    pub commands: Vec<DrawCommand>,
}

impl RenderContext {
    pub fn new() -> Self {
        Self {
            commands: Vec::new(),
        }
    }

    pub fn add_command(&mut self, command: DrawCommand) {
        self.commands.push(command);
    }

    pub fn add_commands(&mut self, commands: Vec<DrawCommand>) {
        self.commands.extend(commands);
    }

    pub fn take_commands(&mut self) -> Vec<DrawCommand> {
        std::mem::take(&mut self.commands)
    }
}

impl Default for RenderContext {
    fn default() -> Self {
        Self::new()
    }
}

/// 简化的笛卡尔坐标系实现
#[derive(Debug, Clone)]
pub struct CartesianCoordinateSystem {
    /// 绘图区域
    pub bounds: Bounds,
    /// X轴数据范围
    pub x_range: (f64, f64),
    /// Y轴数据范围
    pub y_range: (f64, f64),
}

impl CartesianCoordinateSystem {
    pub fn new(bounds: Bounds, x_range: (f64, f64), y_range: (f64, f64)) -> Self {
        Self {
            bounds,
            x_range,
            y_range,
        }
    }

    /// 将数据坐标转换为屏幕坐标
    pub fn data_to_screen(&self, x: f64, y: f64) -> Point {
        let screen_x = self.bounds.origin.x +
            (x - self.x_range.0) / (self.x_range.1 - self.x_range.0) * self.bounds.size.width;
        let screen_y = self.bounds.origin.y + self.bounds.size.height -
            (y - self.y_range.0) / (self.y_range.1 - self.y_range.0) * self.bounds.size.height;
        Point::new(screen_x, screen_y)
    }
}

impl CoordinateSystem for CartesianCoordinateSystem {
    fn data_to_point(&self, data: &[DataValue]) -> std::result::Result<Point, ChartError> {
        if data.len() < 2 {
            return Err(ChartError::InvalidData("需要至少2个数据值".to_string()));
        }

        let x = match &data[0] {
            DataValue::Number(n) => *n,
            _ => return Err(ChartError::InvalidData("X值必须是数字".to_string())),
        };

        let y = match &data[1] {
            DataValue::Number(n) => *n,
            _ => return Err(ChartError::InvalidData("Y值必须是数字".to_string())),
        };

        Ok(self.data_to_screen(x, y))
    }

    fn point_to_data(&self, point: Point) -> std::result::Result<Vec<DataValue>, ChartError> {
        let x = self.x_range.0 +
            (point.x - self.bounds.origin.x) / self.bounds.size.width * (self.x_range.1 - self.x_range.0);
        let y = self.y_range.0 +
            (self.bounds.origin.y + self.bounds.size.height - point.y) / self.bounds.size.height * (self.y_range.1 - self.y_range.0);

        Ok(vec![DataValue::Number(x), DataValue::Number(y)])
    }

    fn contains_point(&self, point: Point) -> bool {
        point.x >= self.bounds.origin.x &&
        point.x <= self.bounds.origin.x + self.bounds.size.width &&
        point.y >= self.bounds.origin.y &&
        point.y <= self.bounds.origin.y + self.bounds.size.height
    }

    fn bounds(&self) -> Bounds {
        self.bounds
    }

    fn dimensions(&self) -> usize {
        2 // X和Y两个维度
    }

    fn update_bounds(&mut self, bounds: Bounds) {
        self.bounds = bounds;
    }
}
