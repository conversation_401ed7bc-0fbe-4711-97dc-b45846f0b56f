# ECharts-rs 增强功能文档

本文档描述了 ECharts-rs 组件新增的交互功能，包括缩放、平移、增强的快捷键等。

## 🚀 新增功能概览

### 1. 缩放和平移功能 (ZoomPanController)

#### 功能特性
- **鼠标滚轮缩放**: 在图表上滚动鼠标滚轮进行缩放
- **拖拽平移**: 在平移模式下拖拽鼠标移动图表
- **框选缩放**: 在缩放模式下拖拽选择区域进行精确缩放
- **缩放限制**: 支持最小和最大缩放级别限制
- **重置功能**: 一键重置到默认视图

#### 配置选项
```rust
use echarts_core::{ZoomPanConfig, ZoomPanController};

let mut config = ZoomPanConfig::default();
config.zoom_enabled = true;           // 启用缩放
config.pan_enabled = true;            // 启用平移
config.min_zoom = 0.1;               // 最小缩放级别
config.max_zoom = 10.0;              // 最大缩放级别
config.zoom_sensitivity = 0.1;       // 缩放灵敏度
config.maintain_aspect_ratio = true; // 保持宽高比

let controller = ZoomPanController::with_config(config);
```

### 2. 交互模式系统 (InteractionHandler)

#### 支持的交互模式
- **Default**: 默认模式，基本交互
- **Zoom**: 缩放模式，拖拽进行框选缩放
- **Pan**: 平移模式，拖拽移动图表
- **Select**: 选择模式，拖拽选择数据点
- **Brush**: 刷选模式，创建选择区域

#### 模式切换
```rust
use echarts_core::{InteractionHandler, InteractionMode};

let mut handler = InteractionHandler::new();
handler.set_mode(InteractionMode::Zoom);  // 切换到缩放模式
```

### 3. 增强的快捷键系统

#### 新增快捷键

| 分类 | 快捷键 | 功能 | 描述 |
|------|--------|------|------|
| **交互模式** | Z | 缩放模式 | 切换到缩放模式 |
| | P | 平移模式 | 切换到平移模式 |
| | S | 选择模式 | 切换到选择模式 |
| | Esc | 默认模式 | 返回默认模式 |
| **缩放控制** | + / = | 放大 | 放大图表 |
| | - | 缩小 | 缩小图表 |
| | R | 重置缩放 | 重置到默认视图 |
| | 滚轮 | 缩放 | 鼠标滚轮缩放 |
| **平移控制** | Shift + ↑↓←→ | 平移 | 使用方向键平移 |
| | 拖拽 | 平移 | 在平移模式下拖拽 |
| **光标控制** | Ctrl + ↑↓←→ | 移动光标 | 精确移动十字线光标 |
| | Home | 跳转开始 | 跳转到第一个数据点 |
| | End | 跳转结束 | 跳转到最后一个数据点 |
| **数据操作** | 1-9 | 快速切换 | 快速切换到对应数据集 |
| | ← → | 切换数据集 | 在数据集间切换 |
| | Ctrl + A | 全选 | 选择所有数据点 |
| | Delete | 清除选择 | 清除当前选择 |
| **编辑功能** | Ctrl + C | 复制数据 | 复制选中的数据 |
| | Ctrl + V | 粘贴数据 | 粘贴数据 |
| | Ctrl + Z | 撤销 | 撤销上一步操作 |
| | Ctrl + Y | 重做 | 重做操作 |

### 4. 增强的十字线功能

#### 新特性
- **数据点吸附**: 自动吸附到最近的数据点
- **多系列支持**: 同时显示多个数据系列的信息
- **坐标格式化**: 自定义坐标值的显示格式
- **样式自定义**: 可配置线条样式、颜色、透明度

#### 配置示例
```rust
use echarts_core::{CrosshairConfig, CrosshairLineStyle, Color};

let mut config = CrosshairConfig::default();
config.snap_to_data = true;           // 启用数据点吸附
config.snap_threshold = 20.0;         // 吸附距离阈值
config.show_labels = true;            // 显示坐标标签

// 自定义线条样式
config.horizontal_style.color = Color::rgb(1.0, 0.0, 0.0); // 红色
config.horizontal_style.width = 2.0;                        // 线宽
config.horizontal_style.dash = vec![5.0, 5.0];             // 虚线样式
```

### 5. 状态显示和用户反馈

#### 状态栏信息
- **当前交互模式**: 显示当前的交互模式
- **缩放级别**: 实时显示当前缩放倍数
- **操作提示**: 显示可用的操作提示

#### 帮助系统
- **快捷键帮助**: 按 H 键显示完整的快捷键列表
- **分类显示**: 按功能分类显示快捷键
- **实时更新**: 根据当前模式显示相关操作

## 🎯 使用示例

### 基本集成
```rust
use echarts_core::{
    ZoomPanController, InteractionHandler, 
    CrosshairConfig, KeyboardShortcuts
};

struct ChartApp {
    zoom_pan_controller: ZoomPanController,
    interaction_handler: InteractionHandler,
    crosshair: Crosshair,
    shortcuts: KeyboardShortcuts,
}

impl ChartApp {
    fn new() -> Self {
        Self {
            zoom_pan_controller: ZoomPanController::new(),
            interaction_handler: InteractionHandler::new(),
            crosshair: Crosshair::with_config(CrosshairConfig::default()),
            shortcuts: KeyboardShortcuts::new_with_defaults(),
        }
    }
    
    fn handle_mouse_wheel(&mut self, position: Point, delta: f64) -> bool {
        self.zoom_pan_controller.handle_wheel_zoom(position, delta)
    }
    
    fn handle_key_event(&mut self, key: &str) -> bool {
        if let Some(action) = self.shortcuts.get_action_for_key(key) {
            self.execute_action(action);
            true
        } else {
            false
        }
    }
}
```

### 事件处理
```rust
// 鼠标滚轮缩放
.on_scroll_wheel(cx.listener(|view, event: &ScrollWheelEvent, _window, cx| {
    let delta_y = match event.delta {
        gpui::ScrollDelta::Lines(lines) => lines.y,
        gpui::ScrollDelta::Pixels(pixels) => pixels.y.0 / 20.0,
    };
    if view.handle_mouse_wheel(event.position, delta_y as f64) {
        cx.notify();
    }
}))

// 鼠标拖拽
.on_mouse_down(MouseButton::Left, cx.listener(|view, event, _window, cx| {
    if view.handle_mouse_down(event.position) {
        cx.notify();
    }
}))
```

## 🔧 技术实现

### 核心组件架构
1. **ViewportTransform**: 处理坐标变换和缩放计算
2. **ZoomPanController**: 管理缩放和平移状态
3. **InteractionHandler**: 处理不同交互模式的逻辑
4. **KeyboardShortcuts**: 扩展的快捷键映射系统

### 坐标系统
- **屏幕坐标**: GPUI 窗口坐标系
- **数据坐标**: 图表数据坐标系
- **变换矩阵**: 处理坐标系之间的转换

### 性能优化
- **增量更新**: 只在必要时重绘图表
- **事件节流**: 避免过度频繁的更新
- **内存管理**: 合理管理交互状态

## 🎨 用户体验改进

### 视觉反馈
- **模式指示器**: 清晰显示当前交互模式
- **缩放级别显示**: 实时显示缩放倍数
- **选择框预览**: 在缩放模式下显示选择框

### 操作流畅性
- **平滑动画**: 缩放和平移的平滑过渡
- **智能吸附**: 十字线智能吸附到数据点
- **边界限制**: 防止过度缩放或平移

### 可访问性
- **键盘导航**: 完整的键盘操作支持
- **快捷键提示**: 上下文相关的操作提示
- **状态反馈**: 清晰的操作状态反馈

## 📊 示例运行

运行增强功能示例：
```bash
cargo run --example simple_gpui_line_chart --features gpui-renderer
```

### 测试功能
1. **缩放测试**: 使用鼠标滚轮或 +/- 键缩放
2. **平移测试**: 按 P 键切换到平移模式，拖拽移动图表
3. **模式切换**: 使用 Z/P/S 键切换不同交互模式
4. **快捷键测试**: 按 H 键查看完整快捷键列表
5. **数据切换**: 使用数字键 1-9 快速切换数据集

这些增强功能大大提升了 ECharts-rs 的交互性和用户体验，使其更适合专业的数据可视化应用！
