[package]
name = "rust-echarts-data"
version = "0.1.0"
edition = "2021"
description = "Data processing and analysis utilities for ECharts-rs"
license = "MIT"
repository = "https://github.com/your-repo/echarts-rs"

[dependencies]
rust-echarts-core = { path = "../core" }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Data processing dependencies
csv = "1.2"
chrono = { version = "0.4", features = ["serde"] }
regex = "1.9"

# Statistical analysis
statrs = "0.16"

# Optional SQL support
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite"], optional = true }

# Optional HTTP client for data fetching
reqwest = { version = "0.11", features = ["json"], optional = true }
tokio = { version = "1.0", features = ["full"], optional = true }

[features]
default = []
sql = ["sqlx"]
http = ["reqwest", "tokio"]
full = ["sql", "http"]

[dev-dependencies]
tokio = { version = "1.0", features = ["full"] }
