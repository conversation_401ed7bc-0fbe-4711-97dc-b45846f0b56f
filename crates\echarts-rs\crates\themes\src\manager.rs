//! Theme manager implementation

use crate::Theme;
use echarts_core::*;
use std::collections::HashMap;

/// Theme manager for handling multiple themes
pub struct ThemeManager {
    themes: HashMap<String, Theme>,
    current_theme: String,
}

impl Default for ThemeManager {
    fn default() -> Self {
        let mut manager = ThemeManager {
            themes: HashMap::new(),
            current_theme: "light".to_string(),
        };

        // Register built-in themes
        manager.register_theme(Theme::light());
        manager.register_theme(Theme::dark());

        manager
    }
}

impl ThemeManager {
    /// Create a new theme manager
    pub fn new() -> Self {
        Self::default()
    }

    /// Register a theme
    pub fn register_theme(&mut self, theme: Theme) {
        self.themes.insert(theme.name.clone(), theme);
    }

    /// Get current theme
    pub fn current_theme(&self) -> &Theme {
        self.themes.get(&self.current_theme).unwrap()
    }

    /// Set current theme
    pub fn set_current_theme(&mut self, name: &str) -> Result<()> {
        if self.themes.contains_key(name) {
            self.current_theme = name.to_string();
            Ok(())
        } else {
            Err(ChartError::config(format!("Theme '{}' not found", name)))
        }
    }

    /// Get theme by name
    pub fn get_theme(&self, name: &str) -> Option<&Theme> {
        self.themes.get(name)
    }

    /// Get mutable theme by name
    pub fn get_theme_mut(&mut self, name: &str) -> Option<&mut Theme> {
        self.themes.get_mut(name)
    }

    /// List available themes
    pub fn list_themes(&self) -> Vec<&str> {
        self.themes.keys().map(|s| s.as_str()).collect()
    }

    /// Remove a theme
    pub fn remove_theme(&mut self, name: &str) -> Result<()> {
        if name == "light" || name == "dark" {
            return Err(ChartError::config("Cannot remove built-in themes"));
        }

        if self.current_theme == name {
            self.current_theme = "light".to_string();
        }

        self.themes.remove(name);
        Ok(())
    }

    /// Clone a theme with a new name
    pub fn clone_theme(&mut self, source: &str, target: &str) -> Result<()> {
        if let Some(theme) = self.themes.get(source).cloned() {
            let mut new_theme = theme;
            new_theme.name = target.to_string();
            self.register_theme(new_theme);
            Ok(())
        } else {
            Err(ChartError::config(format!(
                "Source theme '{}' not found",
                source
            )))
        }
    }

    /// Create a custom theme from base theme
    pub fn create_custom_theme(&mut self, name: &str, base: &str) -> Result<&mut Theme> {
        self.clone_theme(base, name)?;
        Ok(self.get_theme_mut(name).unwrap())
    }

    /// Apply theme to a chart component
    pub fn apply_theme_to<T: Themeable>(&self, component: &mut T) {
        component.apply_theme(self.current_theme());
    }

    /// Switch to next theme in the list
    pub fn next_theme(&mut self) -> Result<()> {
        let themes: Vec<String> = self.themes.keys().cloned().collect();
        if let Some(current_index) = themes.iter().position(|t| t == &self.current_theme) {
            let next_index = (current_index + 1) % themes.len();
            self.set_current_theme(&themes[next_index])
        } else {
            self.set_current_theme("light")
        }
    }

    /// Switch to previous theme in the list
    pub fn previous_theme(&mut self) -> Result<()> {
        let themes: Vec<String> = self.themes.keys().cloned().collect();
        if let Some(current_index) = themes.iter().position(|t| t == &self.current_theme) {
            let prev_index = if current_index == 0 {
                themes.len() - 1
            } else {
                current_index - 1
            };
            self.set_current_theme(&themes[prev_index])
        } else {
            self.set_current_theme("light")
        }
    }

    /// Get theme count
    pub fn theme_count(&self) -> usize {
        self.themes.len()
    }

    /// Check if theme exists
    pub fn has_theme(&self, name: &str) -> bool {
        self.themes.contains_key(name)
    }

    /// Export theme as JSON
    pub fn export_theme(&self, name: &str) -> Result<String> {
        if let Some(theme) = self.get_theme(name) {
            serde_json::to_string_pretty(theme)
                .map_err(|e| ChartError::config(format!("Failed to export theme: {}", e)))
        } else {
            Err(ChartError::config(format!("Theme '{}' not found", name)))
        }
    }

    /// Import theme from JSON
    pub fn import_theme(&mut self, json: &str) -> Result<()> {
        let theme: Theme = serde_json::from_str(json)
            .map_err(|e| ChartError::config(format!("Failed to import theme: {}", e)))?;
        self.register_theme(theme);
        Ok(())
    }
}

/// Trait for components that can be themed
pub trait Themeable {
    /// Apply a theme to this component
    fn apply_theme(&mut self, theme: &Theme);
}
