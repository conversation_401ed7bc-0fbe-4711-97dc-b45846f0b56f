# ECharts-rs

A Rust implementation of Apache ECharts, providing powerful data visualization capabilities for Rust applications.

## 🎯 Architecture Overview

ECharts-rs follows a modular architecture with clear separation of concerns:

- **echarts-core**: Core types, traits, and rendering abstractions
- **echarts-charts**: Complete chart implementations (LineSeries, BarSeries, etc.)
- **echarts-rs**: High-level API and chart builders
- **echarts-renderer**: Multiple rendering backends (SVG, PNG, GPUI)

## ✨ Features

- **Type-safe chart configuration**: Strongly typed APIs prevent runtime errors
- **Complete chart implementations**: Real LineSeries, BarSeries, PieSeries, ScatterSeries
- **Unified rendering system**: Consistent rendering across all chart types
- **Multiple rendering backends**: SVG, PNG, and GPUI support
- **High performance**: Optimized for large datasets
- **Extensible architecture**: Easy to add new chart types and renderers

## 🚀 Quick Start

### Using RuntimeChart (Direct API)

```rust
use echarts_rs::{RuntimeChart};
use echarts_charts::LineSeries;
use echarts_core::{Color, Bounds};

// Create a line series with real implementation
let line_series = LineSeries::new("Sales Data".to_string())
    .data(vec![(0.0, 120.0), (1.0, 200.0), (2.0, 150.0)])
    .color(Color::BLUE)
    .line_width(2.0)
    .smooth(true);

// Build chart using RuntimeChart
let chart = RuntimeChart::new()
    .title("Monthly Sales")
    .add_line_series(line_series);

// Render with any compatible renderer
let bounds = Bounds::new(0.0, 0.0, 800.0, 600.0);
chart.render(&mut renderer, bounds)?;
```

### Using ChartBuilder (Convenience API)

```rust
use echarts_rs::ChartBuilder;
use echarts_charts::{LineSeries, BarSeries};

// Create multiple series
let line_series = LineSeries::new("Trend".to_string())
    .data(vec![(0.0, 100.0), (1.0, 150.0), (2.0, 120.0)]);

let bar_series = BarSeries::new("Values".to_string())
    .data(vec![(0.0, 80.0), (1.0, 120.0), (2.0, 100.0)]);

// Build composite chart
let chart = ChartBuilder::new()
    .title("Composite Chart")
    .add_line_series(line_series)
    .add_bar_series(bar_series)
    .build();
```

## 📊 Supported Chart Types

- **Line Charts**: Smooth curves, stepped lines, area charts
- **Bar Charts**: Vertical/horizontal bars, stacked bars
- **Pie Charts**: Standard pies, donuts, rose charts
- **Scatter Charts**: Bubble charts, symbol variations

## 🏗️ Implementation Status

### ✅ Completed (按照 EXECUTION_PLAN.md)

- **Core Architecture**: Complete trait system and abstractions
- **Chart Implementations**: Full LineSeries, BarSeries, PieSeries, ScatterSeries
- **Unified Rendering**: DrawCommand system with renderer abstraction
- **Builder Pattern**: RuntimeChart and ChartBuilder APIs
- **Type Safety**: Strongly typed configuration and data handling

### 🚧 In Progress

- **Advanced Features**: Animations, interactions, themes
- **Additional Charts**: Candlestick, heatmap, tree charts
- **Performance**: GPU acceleration, large dataset optimization

## 🔧 Development

### Running Examples

```bash
# Run the new architecture demo
cargo run --example new_architecture_demo

# Run other examples
cargo run --example basic_charts
cargo run --example composite_charts
```

### Testing

```bash
# Run all tests
cargo test --workspace

# Run specific crate tests
cargo test -p echarts-core
cargo test -p echarts-charts
cargo test -p echarts-rs
```

## 📚 Documentation

- [Architecture Guide](docs/ARCHITECTURE.md)
- [API Reference](docs/API.md)
- [Execution Plan](EXECUTION_PLAN.md)
- [Examples](examples/)

## 🤝 Contributing

1. Follow the architecture defined in `EXECUTION_PLAN.md`
2. Ensure all tests pass
3. Add examples for new features
4. Update documentation

## 📄 License

Licensed under either of

- Apache License, Version 2.0
- MIT License

at your option.
