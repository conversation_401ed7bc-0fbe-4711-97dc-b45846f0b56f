//! 仪表盘SVG演示
//!
//! 生成各种仪表盘的SVG文件，展示GaugeSeries的完整功能

use std::fs;
use echarts_rs::{GaugeSeries, GaugeDataItem, PointerStyle, AxisTick, AxisLabel, Color};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("⏱️ 仪表盘SVG演示系统");
    println!("{}", "=".repeat(50));

    // 确保输出目录存在
    let output_dir = "temp/svg/gauge_charts";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 基础仪表盘
    println!("\n⏱️ 1. 生成基础仪表盘...");
    generate_basic_gauge(output_dir)?;

    // 2. 系统监控仪表盘
    println!("\n💻 2. 生成系统监控仪表盘...");
    generate_system_monitor_gauge(output_dir)?;

    // 3. 温度计仪表盘
    println!("\n🌡️ 3. 生成温度计仪表盘...");
    generate_temperature_gauge(output_dir)?;

    // 4. 速度计仪表盘
    println!("\n🚗 4. 生成速度计仪表盘...");
    generate_speedometer_gauge(output_dir)?;

    // 5. 自定义样式仪表盘
    println!("\n🎨 5. 生成自定义样式仪表盘...");
    generate_custom_style_gauge(output_dir)?;

    // 6. 生成展示页面
    println!("\n📄 6. 生成展示页面...");
    generate_gauge_showcase(output_dir)?;

    println!("\n🎉 仪表盘SVG演示生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/gauge_demo.html 查看演示", output_dir);

    Ok(())
}

/// 生成基础仪表盘
fn generate_basic_gauge(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        GaugeDataItem::new("进度", 75.0).color(Color::rgb(0.2, 0.6, 1.0)),
    ];

    let gauge_series = GaugeSeries::new("基础仪表盘")
        .data(data)
        .center(0.5, 0.5)
        .radius(0.75)
        .value_range(0.0, 100.0)
        .angle_range(225.0, -45.0)
        .show_title(true)
        .show_detail(true);

    let svg = create_gauge_svg(&gauge_series, "基础仪表盘演示", 600.0, 600.0)?;
    fs::write(format!("{}/01_basic_gauge.svg", output_dir), svg)?;
    
    println!("  ✅ 基础仪表盘生成完成");
    Ok(())
}

/// 生成系统监控仪表盘
fn generate_system_monitor_gauge(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        GaugeDataItem::new("CPU使用率", 68.5).color(Color::rgb(1.0, 0.4, 0.4)),
    ];

    let pointer_style = PointerStyle {
        length: 0.8,
        width: 8.0,
        color: Color::rgb(0.8, 0.2, 0.2),
    };

    let axis_tick = AxisTick {
        show: true,
        length: 20.0,
        width: 3.0,
        color: Color::rgb(0.6, 0.6, 0.6),
        split_number: 10,
    };

    let axis_label = AxisLabel {
        show: true,
        distance: 30.0,
        font_size: 14.0,
        color: Color::rgb(0.3, 0.3, 0.3),
        formatter: Some("{value}%".to_string()),
    };

    let gauge_series = GaugeSeries::new("系统监控")
        .data(data)
        .center(0.5, 0.5)
        .radius(0.8)
        .value_range(0.0, 100.0)
        .angle_range(200.0, -20.0)
        .pointer_style(pointer_style)
        .axis_tick(axis_tick)
        .axis_label(axis_label)
        .show_title(true)
        .show_detail(true);

    let svg = create_gauge_svg(&gauge_series, "系统监控仪表盘", 600.0, 600.0)?;
    fs::write(format!("{}/02_system_monitor_gauge.svg", output_dir), svg)?;
    
    println!("  ✅ 系统监控仪表盘生成完成");
    Ok(())
}

/// 生成温度计仪表盘
fn generate_temperature_gauge(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        GaugeDataItem::new("温度", 23.5).color(Color::rgb(0.2, 0.8, 0.4)),
    ];

    let pointer_style = PointerStyle {
        length: 0.85,
        width: 6.0,
        color: Color::rgb(0.1, 0.6, 0.3),
    };

    let axis_tick = AxisTick {
        show: true,
        length: 15.0,
        width: 2.0,
        color: Color::rgb(0.5, 0.5, 0.5),
        split_number: 8,
    };

    let axis_label = AxisLabel {
        show: true,
        distance: 25.0,
        font_size: 12.0,
        color: Color::rgb(0.2, 0.2, 0.2),
        formatter: Some("{value}°C".to_string()),
    };

    let gauge_series = GaugeSeries::new("温度监测")
        .data(data)
        .center(0.5, 0.5)
        .radius(0.7)
        .value_range(-10.0, 50.0)
        .angle_range(180.0, 0.0)
        .pointer_style(pointer_style)
        .axis_tick(axis_tick)
        .axis_label(axis_label)
        .show_title(true)
        .show_detail(true);

    let svg = create_gauge_svg(&gauge_series, "温度计仪表盘", 600.0, 600.0)?;
    fs::write(format!("{}/03_temperature_gauge.svg", output_dir), svg)?;
    
    println!("  ✅ 温度计仪表盘生成完成");
    Ok(())
}

/// 生成速度计仪表盘
fn generate_speedometer_gauge(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        GaugeDataItem::new("速度", 85.0).color(Color::rgb(1.0, 0.6, 0.2)),
    ];

    let pointer_style = PointerStyle {
        length: 0.9,
        width: 10.0,
        color: Color::rgb(0.8, 0.4, 0.1),
    };

    let axis_tick = AxisTick {
        show: true,
        length: 25.0,
        width: 4.0,
        color: Color::rgb(0.4, 0.4, 0.4),
        split_number: 12,
    };

    let axis_label = AxisLabel {
        show: true,
        distance: 35.0,
        font_size: 16.0,
        color: Color::rgb(0.1, 0.1, 0.1),
        formatter: Some("{value}".to_string()),
    };

    let gauge_series = GaugeSeries::new("车速表")
        .data(data)
        .center(0.5, 0.5)
        .radius(0.85)
        .value_range(0.0, 120.0)
        .angle_range(240.0, -60.0)
        .pointer_style(pointer_style)
        .axis_tick(axis_tick)
        .axis_label(axis_label)
        .show_title(true)
        .show_detail(true);

    let svg = create_gauge_svg(&gauge_series, "速度计仪表盘", 600.0, 600.0)?;
    fs::write(format!("{}/04_speedometer_gauge.svg", output_dir), svg)?;
    
    println!("  ✅ 速度计仪表盘生成完成");
    Ok(())
}

/// 生成自定义样式仪表盘
fn generate_custom_style_gauge(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        GaugeDataItem::new("性能指数", 92.3).color(Color::rgb(0.8, 0.2, 1.0)),
    ];

    let pointer_style = PointerStyle {
        length: 0.75,
        width: 12.0,
        color: Color::rgb(0.6, 0.1, 0.8),
    };

    let axis_tick = AxisTick {
        show: true,
        length: 18.0,
        width: 3.0,
        color: Color::rgb(0.7, 0.3, 0.9),
        split_number: 6,
    };

    let axis_label = AxisLabel {
        show: true,
        distance: 28.0,
        font_size: 13.0,
        color: Color::rgb(0.5, 0.1, 0.7),
        formatter: None,
    };

    let gauge_series = GaugeSeries::new("性能评估")
        .data(data)
        .center(0.5, 0.5)
        .radius(0.65)
        .value_range(0.0, 100.0)
        .angle_range(210.0, -30.0)
        .pointer_style(pointer_style)
        .axis_tick(axis_tick)
        .axis_label(axis_label)
        .show_title(true)
        .show_detail(true);

    let svg = create_gauge_svg(&gauge_series, "自定义样式仪表盘", 600.0, 600.0)?;
    fs::write(format!("{}/05_custom_style_gauge.svg", output_dir), svg)?;
    
    println!("  ✅ 自定义样式仪表盘生成完成");
    Ok(())
}

/// 创建仪表盘SVG
fn create_gauge_svg(series: &GaugeSeries, title: &str, width: f64, height: f64) -> std::result::Result<String, Box<dyn std::error::Error>> {
    use echarts_core::{CartesianCoordinateSystem, Bounds, Series, Point, Size};
    
    // 创建坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        Bounds {
            origin: Point { x: 50.0, y: 50.0 },
            size: Size { width: width - 100.0, height: height - 100.0 },
        },
        (0.0, 1.0),
        (0.0, 1.0),
    );
    
    // 获取渲染命令
    let commands = series.render_to_commands(&coord_system)?;
    
    // 生成SVG
    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 渲染命令
    for command in commands {
        render_gauge_svg_command(&mut svg, &command);
    }
    
    svg.push_str("</svg>");
    Ok(svg)
}

/// 渲染仪表盘SVG命令
fn render_gauge_svg_command(svg: &mut String, command: &echarts_core::DrawCommand) {
    use echarts_core::{DrawCommand, PathCommand};
    
    match command {
        DrawCommand::Path { commands, style } => {
            let mut path_data = String::new();
            for cmd in commands {
                match cmd {
                    PathCommand::MoveTo(point) => {
                        path_data.push_str(&format!("M {} {} ", point.x, point.y));
                    }
                    PathCommand::LineTo(point) => {
                        path_data.push_str(&format!("L {} {} ", point.x, point.y));
                    }
                    PathCommand::CurveTo { control1, control2, to } => {
                        path_data.push_str(&format!("C {} {} {} {} {} {} ", 
                            control1.x, control1.y, control2.x, control2.y, to.x, to.y));
                    }
                    PathCommand::QuadTo { control, to } => {
                        path_data.push_str(&format!("Q {} {} {} {} ", control.x, control.y, to.x, to.y));
                    }
                    PathCommand::Close => {
                        path_data.push_str("Z ");
                    }
                }
            }
            
            let fill = style.fill.map(|c| format!("#{:02x}{:02x}{:02x}", 
                (c.r * 255.0) as u8, (c.g * 255.0) as u8, (c.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke = style.stroke.as_ref().map(|s| format!("#{:02x}{:02x}{:02x}", 
                (s.color.r * 255.0) as u8, (s.color.g * 255.0) as u8, (s.color.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke_width = style.stroke.as_ref().map(|s| s.width).unwrap_or(0.0);
            
            svg.push_str(&format!("  <path d=\"{}\" fill=\"{}\" stroke=\"{}\" stroke-width=\"{}\" opacity=\"{}\"/>\n", 
                path_data.trim(), fill, stroke, stroke_width, style.opacity));
        }
        DrawCommand::Text { text, position, style } => {
            let color = format!("#{:02x}{:02x}{:02x}", 
                (style.color.r * 255.0) as u8, (style.color.g * 255.0) as u8, (style.color.b * 255.0) as u8);
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"{}\" fill=\"{}\" text-anchor=\"middle\" opacity=\"{}\">{}</text>\n", 
                position.x, position.y, style.font_size, color, style.opacity, text));
        }
        DrawCommand::Circle { center, radius, style } => {
            let fill = style.fill.map(|c| format!("#{:02x}{:02x}{:02x}", 
                (c.r * 255.0) as u8, (c.g * 255.0) as u8, (c.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke = style.stroke.as_ref().map(|s| format!("#{:02x}{:02x}{:02x}", 
                (s.color.r * 255.0) as u8, (s.color.g * 255.0) as u8, (s.color.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke_width = style.stroke.as_ref().map(|s| s.width).unwrap_or(0.0);
            
            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"{}\" fill=\"{}\" stroke=\"{}\" stroke-width=\"{}\" opacity=\"{}\"/>\n", 
                center.x, center.y, radius, fill, stroke, stroke_width, style.opacity));
        }
        _ => {} // 忽略其他命令类型
    }
}

/// 生成展示页面
fn generate_gauge_showcase(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts-rs 仪表盘演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .section {
            background: rgba(255,255,255,0.95);
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .chart-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        .chart-svg {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h2 { color: #333; margin-bottom: 20px; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        .description {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⏱️ ECharts-rs 仪表盘演示</h1>
            <p class="description">展现 GaugeSeries 的强大功能和精美仪表盘设计</p>
            <div class="feature-list">
                <div class="feature">
                    <h3>⏱️ 基础仪表盘</h3>
                    <p>经典的仪表盘设计</p>
                </div>
                <div class="feature">
                    <h3>💻 系统监控</h3>
                    <p>实时系统状态监控</p>
                </div>
                <div class="feature">
                    <h3>🌡️ 温度监测</h3>
                    <p>精确的温度显示</p>
                </div>
                <div class="feature">
                    <h3>🎨 自定义样式</h3>
                    <p>灵活的样式配置</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>⏱️ 基础仪表盘功能</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">基础仪表盘</div>
                    <object class="chart-svg" data="01_basic_gauge.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">系统监控仪表盘</div>
                    <object class="chart-svg" data="02_system_monitor_gauge.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🌡️ 专业应用场景</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">温度计仪表盘</div>
                    <object class="chart-svg" data="03_temperature_gauge.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">速度计仪表盘</div>
                    <object class="chart-svg" data="04_speedometer_gauge.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎨 自定义样式</h2>
            <div class="chart-item">
                <div class="chart-title">自定义样式仪表盘</div>
                <object class="chart-svg" data="05_custom_style_gauge.svg" type="image/svg+xml">SVG不支持</object>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h2>🎉 GaugeSeries 功能总结</h2>
            <p>ECharts-rs GaugeSeries 完整功能展示：</p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>✅ <strong>经典仪表盘设计</strong> - 传统的圆形仪表盘布局</li>
                <li>✅ <strong>精确的指针系统</strong> - 可配置的指针长度、宽度和颜色</li>
                <li>✅ <strong>灵活的刻度配置</strong> - 自定义刻度数量、长度和样式</li>
                <li>✅ <strong>智能标签系统</strong> - 数值标签和格式化显示</li>
                <li>✅ <strong>角度范围配置</strong> - 支持任意角度范围的仪表盘</li>
                <li>✅ <strong>数值范围映射</strong> - 灵活的最小值和最大值设置</li>
                <li>✅ <strong>标题和详情显示</strong> - 可配置的标题和数值详情</li>
                <li>✅ <strong>高质量渲染</strong> - 优化的SVG输出和视觉效果</li>
            </ul>
        </div>
    </div>
</body>
</html>"#;

    fs::write(format!("{}/gauge_demo.html", output_dir), html_content)?;
    Ok(())
}
