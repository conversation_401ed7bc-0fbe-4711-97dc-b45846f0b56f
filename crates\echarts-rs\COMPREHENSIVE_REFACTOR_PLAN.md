# ECharts-rs 全面重构计划

## 🔍 **重复代码问题总结**

### 🚨 **严重问题（立即修复）**

#### 1. 样式系统重复冲突
**问题**: `style.rs` 和 `draw_commands.rs` 中重复定义相同的样式结构体
```rust
// crates/core/src/style.rs
pub struct TextStyle { ... }
pub struct LineStyle { ... }
pub enum FontStyle { ... }

// crates/core/src/draw_commands.rs  
pub struct TextStyle { ... }  // 重复！
pub struct LineStyle { ... }  // 重复！
pub enum FontStyle { ... }    // 重复！
```
**影响**: 类型冲突，编译错误，API混乱

#### 2. 颜色系统不兼容
**问题**: 两种不同的颜色表示方式
```rust
// crates/core/src/color.rs (f64, 0.0-1.0)
pub struct Color { r: f64, g: f64, b: f64, a: f64 }

// crates/processor/src/lib.rs (u8, 0-255)  
pub struct Color { r: u8, g: u8, b: u8, a: u8 }
```
**影响**: 类型不兼容，需要复杂的转换

### ⚠️ **中等问题（影响维护性）**

#### 3. 主题系统分散
```rust
// crates/core/src/render_context.rs
pub struct SimpleTheme { ... }

// crates/themes/src/lib.rs
pub struct Theme { ... }
```

#### 4. 图表基础结构重复
所有图表类型都有相似的：
- 基础字段: `name`, `data`, `visible`, `z_index`
- 构造方法: `new()`, `data()`, `color()`
- 渲染逻辑: 边界计算，命令生成

## 🎯 **重构方案**

### Phase 1: 样式系统统一 🚨 (最高优先级)

#### 方案：保留 `style.rs`，移除 `draw_commands.rs` 中的重复定义

```rust
// crates/core/src/style.rs (统一样式定义)
pub struct TextStyle {
    pub font_family: String,
    pub font_size: f64,
    pub font_weight: FontWeight,
    pub font_style: FontStyle,
    pub color: Color,
    pub align: TextAlign,
    pub baseline: TextBaseline,
}

pub struct LineStyle {
    pub color: Color,
    pub width: f64,
    pub opacity: f64,
    pub dash_pattern: Option<Vec<f64>>,
    pub cap: LineCap,
    pub join: LineJoin,
}

// crates/core/src/draw_commands.rs (只保留绘制命令)
pub enum DrawCommand {
    Text { position: Point, text: String, style: TextStyle },
    Line { from: Point, to: Point, style: LineStyle },
    // ... 其他命令
}
```

#### 实施步骤：
1. ✅ 确认 `style.rs` 中的定义是完整的
2. 🔄 移除 `draw_commands.rs` 中的重复定义
3. 🔄 更新所有引用到正确的模块
4. 🔄 修复编译错误

### Phase 2: 颜色系统统一 ⚠️

#### 方案：统一使用 f64 (0.0-1.0) 表示，提供转换工具

```rust
// crates/core/src/color.rs (统一颜色系统)
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Color {
    pub r: f64, // 0.0 - 1.0
    pub g: f64, // 0.0 - 1.0  
    pub b: f64, // 0.0 - 1.0
    pub a: f64, // 0.0 - 1.0
}

impl Color {
    // 从 u8 创建 (0-255)
    pub fn from_u8(r: u8, g: u8, b: u8, a: u8) -> Self { ... }
    
    // 转换为 u8 (0-255)
    pub fn to_u8(&self) -> (u8, u8, u8, u8) { ... }
    
    // 从十六进制创建
    pub fn from_hex(hex: &str) -> Result<Self> { ... }
}
```

#### 实施步骤：
1. 🔄 扩展 `core/color.rs` 的转换功能
2. 🔄 移除 `processor/lib.rs` 中的 Color 定义
3. 🔄 更新所有使用 u8 颜色的代码
4. 🔄 提供迁移工具

### Phase 3: 主题系统统一 ⚠️

#### 方案：统一到 `themes` 包，移除 `SimpleTheme`

```rust
// crates/themes/src/lib.rs (统一主题系统)
pub struct Theme {
    pub name: String,
    pub color_palette: Vec<Color>,
    pub background_color: Color,
    pub text_style: TextStyle,
    pub grid_style: GridStyle,
    pub axis_style: AxisStyle,
}

// 提供简化的主题创建
impl Theme {
    pub fn simple(name: &str, colors: Vec<Color>) -> Self { ... }
}
```

### Phase 4: 图表基础类重构 📊

#### 方案：创建 `ChartBase` trait 和通用实现

```rust
// crates/charts/src/base.rs (新文件)
pub trait ChartBase {
    fn name(&self) -> &str;
    fn data(&self) -> &DataSet;
    fn visible(&self) -> bool;
    fn z_index(&self) -> i32;
    fn bounds(&self) -> Option<Bounds>;
}

pub struct ChartConfig {
    pub name: String,
    pub visible: bool,
    pub z_index: i32,
    pub animation: AnimationConfig,
    pub interaction: InteractionConfig,
}

// 为所有图表提供通用实现
pub trait ChartSeries: ChartBase + Series {
    fn config(&self) -> &ChartConfig;
    fn config_mut(&mut self) -> &mut ChartConfig;
    
    // 通用的链式方法
    fn visible(mut self, visible: bool) -> Self where Self: Sized {
        self.config_mut().visible = visible;
        self
    }
    
    fn z_index(mut self, z_index: i32) -> Self where Self: Sized {
        self.config_mut().z_index = z_index;
        self
    }
}
```

## 📋 **实施时间表**

### Week 1: 样式系统统一 🚨
- [ ] Day 1-2: 分析现有样式定义
- [ ] Day 3-4: 移除重复定义，统一到 `style.rs`
- [ ] Day 5: 修复编译错误，更新引用

### Week 2: 颜色系统统一 ⚠️
- [ ] Day 1-2: 扩展颜色转换功能
- [ ] Day 3-4: 移除重复定义，更新使用
- [ ] Day 5: 测试和验证

### Week 3: 主题系统统一 ⚠️
- [ ] Day 1-2: 统一主题定义
- [ ] Day 3-4: 迁移现有主题使用
- [ ] Day 5: 文档和示例更新

### Week 4: 图表基础类重构 📊
- [ ] Day 1-3: 设计和实现 ChartBase
- [ ] Day 4-5: 重构现有图表类型

## ✅ **预期成果**

### 代码质量提升
- **减少重复代码**: 60%+ 的重复代码消除
- **提高类型安全**: 统一的类型系统
- **简化维护**: 集中的定义和管理

### 开发体验改进
- **更清晰的API**: 统一的接口和命名
- **更好的文档**: 集中的类型文档
- **更快的编译**: 减少重复编译

### 用户体验优化
- **向后兼容**: 提供迁移工具和指南
- **更好的性能**: 优化的类型转换
- **更丰富的功能**: 统一的样式和主题系统

## 🚀 **开始实施**

让我们从最严重的样式系统重复问题开始！
