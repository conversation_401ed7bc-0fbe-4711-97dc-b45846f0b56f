//! ECharts-rs 性能基准测试
//! 
//! 这个模块包含了全面的性能基准测试，用于评估和优化ECharts-rs的性能表现

use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use echarts_core::{Chart, DataPoint, DataValue, Color, Bounds};
use echarts_themes::Theme;
use echarts_renderer::{<PERSON><PERSON>i<PERSON><PERSON><PERSON>, Renderer};


/// 数据生成基准测试
fn bench_data_generation(c: &mut Criterion) {
    let mut group = c.benchmark_group("data_generation");
    
    for size in [100, 1_000, 10_000, 100_000].iter() {
        group.bench_with_input(
            BenchmarkId::new("linear_data", size),
            size,
            |b, &size| {
                b.iter(|| {
                    let data: Vec<DataPoint> = (0..size).map(|i| {
                        DataPoint::new(vec![
                            DataValue::Number(i as f64),
                            DataValue::Number(i as f64 * 2.0)
                        ])
                    }).collect();
                    black_box(data)
                });
            },
        );
        
        group.bench_with_input(
            BenchmarkId::new("complex_data", size),
            size,
            |b, &size| {
                b.iter(|| {
                    let data: Vec<DataPoint> = (0..size).map(|i| {
                        let x = i as f64;
                        let y = (x * 0.01).sin() * 100.0 + (x * 0.005).cos() * 50.0;
                        DataPoint::new(vec![
                            DataValue::Number(x),
                            DataValue::Number(y)
                        ])
                    }).collect();
                    black_box(data)
                });
            },
        );
    }
    
    group.finish();
}

/// 图表创建基准测试
fn bench_chart_creation(c: &mut Criterion) {
    let mut group = c.benchmark_group("chart_creation");
    
    let small_data = generate_test_data(100);
    let medium_data = generate_test_data(1_000);
    let large_data = generate_test_data(10_000);
    
    group.bench_function("empty_chart", |b| {
        b.iter(|| {
            let chart = Chart::new();
            black_box(chart)
        });
    });
    
    group.bench_function("chart_with_small_data", |b| {
        b.iter(|| {
            let mut chart = Chart::new();
            chart.title = Some("Test Chart".to_string());
            chart.background_color = Some(Color::WHITE);
            black_box(chart)
        });
    });
    
    group.bench_function("chart_with_theme", |b| {
        b.iter(|| {
            let mut chart = Chart::new();
            let theme = Theme::light();
            chart.background_color = Some(theme.background_color);
            black_box(chart)
        });
    });
    
    group.finish();
}

/// 主题系统基准测试
fn bench_theme_system(c: &mut Criterion) {
    let mut group = c.benchmark_group("theme_system");
    
    group.bench_function("light_theme_creation", |b| {
        b.iter(|| {
            let theme = Theme::light();
            black_box(theme)
        });
    });
    
    group.bench_function("dark_theme_creation", |b| {
        b.iter(|| {
            let theme = Theme::dark();
            black_box(theme)
        });
    });
    
    group.bench_function("custom_theme_creation", |b| {
        b.iter(|| {
            let mut theme = Theme::light();
            theme.name = "Custom".to_string();
            theme.background_color = Color::rgb(0.95, 0.95, 0.95);
            theme.color_palette = vec![
                Color::rgb(1.0, 0.0, 0.0),
                Color::rgb(0.0, 1.0, 0.0),
                Color::rgb(0.0, 0.0, 1.0),
            ];
            black_box(theme)
        });
    });
    
    group.finish();
}

/// 数据处理基准测试
fn bench_data_processing(c: &mut Criterion) {
    let mut group = c.benchmark_group("data_processing");
    
    let large_dataset = generate_test_data(50_000);
    
    group.bench_function("data_point_access", |b| {
        b.iter(|| {
            let mut sum = 0.0;
            for point in &large_dataset {
                sum += point.x() + point.y();
            }
            black_box(sum)
        });
    });
    
    group.bench_function("data_filtering", |b| {
        b.iter(|| {
            let filtered: Vec<&DataPoint> = large_dataset.iter()
                .filter(|p| p.y() > 0.0)
                .collect();
            black_box(filtered)
        });
    });
    
    group.bench_function("data_transformation", |b| {
        b.iter(|| {
            let transformed: Vec<DataPoint> = large_dataset.iter()
                .map(|p| {
                    DataPoint::new(vec![
                        DataValue::Number(p.x()),
                        DataValue::Number(p.y() * 2.0)
                    ])
                })
                .collect();
            black_box(transformed)
        });
    });
    
    group.bench_function("data_aggregation", |b| {
        b.iter(|| {
            let stats = calculate_statistics(&large_dataset);
            black_box(stats)
        });
    });
    
    group.finish();
}

/// 内存使用基准测试
fn bench_memory_usage(c: &mut Criterion) {
    let mut group = c.benchmark_group("memory_usage");
    
    for size in [1_000, 10_000, 100_000].iter() {
        group.bench_with_input(
            BenchmarkId::new("memory_allocation", size),
            size,
            |b, &size| {
                b.iter(|| {
                    let data = generate_test_data(size);
                    let memory_usage = estimate_memory_usage(&data);
                    black_box(memory_usage)
                });
            },
        );
    }
    
    group.finish();
}

/// 渲染性能基准测试
fn bench_rendering_performance(c: &mut Criterion) {
    let mut group = c.benchmark_group("rendering_performance");
    
    let test_data = generate_test_data(1000);
    let bounds = Bounds::new(0.0, 0.0, 800.0, 600.0);
    
    group.bench_function("renderer_creation", |b| {
        b.iter(|| {
            let renderer = GpuiRenderer::new();
            black_box(renderer)
        });
    });
    
    group.bench_function("simple_chart_render", |b| {
        b.iter(|| {
            let mut renderer = GpuiRenderer::new();
            let mut chart = Chart::new();
            chart.title = Some("Benchmark Chart".to_string());

            // 模拟渲染过程
            let result = renderer.render_chart(&chart, bounds);
            black_box(result)
        });
    });
    
    group.finish();
}

/// 并发性能基准测试
fn bench_concurrent_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("concurrent_operations");
    
    group.bench_function("parallel_data_generation", |b| {
        b.iter(|| {
            use rayon::prelude::*;
            let data: Vec<DataPoint> = (0..10_000).into_par_iter().map(|i| {
                let x = i as f64;
                let y = (x * 0.01).sin() * 100.0;
                DataPoint::new(vec![
                    DataValue::Number(x),
                    DataValue::Number(y)
                ])
            }).collect();
            black_box(data)
        });
    });
    
    group.bench_function("parallel_data_processing", |b| {
        let data = generate_test_data(10_000);
        b.iter(|| {
            use rayon::prelude::*;
            let processed: Vec<f64> = data.par_iter()
                .map(|p| p.x() * p.y())
                .collect();
            black_box(processed)
        });
    });
    
    group.finish();
}

// 辅助函数

/// 生成测试数据
fn generate_test_data(size: usize) -> Vec<DataPoint> {
    (0..size).map(|i| {
        let x = i as f64;
        let y = (x * 0.01).sin() * 100.0 + pseudo_random(i) * 20.0;
        DataPoint::new(vec![
            DataValue::Number(x),
            DataValue::Number(y)
        ])
    }).collect()
}

/// 计算统计信息
fn calculate_statistics(data: &[DataPoint]) -> Statistics {
    let values: Vec<f64> = data.iter().map(|p| p.y()).collect();
    let sum: f64 = values.iter().sum();
    let mean = sum / values.len() as f64;
    let variance = values.iter()
        .map(|v| (v - mean).powi(2))
        .sum::<f64>() / values.len() as f64;
    
    Statistics {
        count: values.len(),
        sum,
        mean,
        variance,
        std_dev: variance.sqrt(),
        min: values.iter().fold(f64::INFINITY, |a, &b| a.min(b)),
        max: values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b)),
    }
}

/// 估算内存使用
fn estimate_memory_usage(data: &[DataPoint]) -> MemoryUsage {
    let data_size = data.len() * std::mem::size_of::<DataPoint>();
    let overhead = data_size / 10; // 估算10%的开销
    
    MemoryUsage {
        data_bytes: data_size,
        overhead_bytes: overhead,
        total_bytes: data_size + overhead,
    }
}

/// 伪随机数生成
fn pseudo_random(seed: usize) -> f64 {
    let a = 1664525u64;
    let c = 1013904223u64;
    let m = 2u64.pow(32);
    let x = ((a * seed as u64 + c) % m) as f64 / m as f64;
    x * 2.0 - 1.0
}

// 数据结构

#[derive(Debug)]
struct Statistics {
    count: usize,
    sum: f64,
    mean: f64,
    variance: f64,
    std_dev: f64,
    min: f64,
    max: f64,
}

#[derive(Debug)]
struct MemoryUsage {
    data_bytes: usize,
    overhead_bytes: usize,
    total_bytes: usize,
}

// 基准测试组
criterion_group!(
    benches,
    bench_data_generation,
    bench_chart_creation,
    bench_theme_system,
    bench_data_processing,
    bench_memory_usage,
    bench_rendering_performance,
    bench_concurrent_operations
);

criterion_main!(benches);
