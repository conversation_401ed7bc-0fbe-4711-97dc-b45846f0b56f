//! Color types and utilities

use serde::{Deserialize, Serialize};

/// RGBA color representation
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Serialize, Deserialize)]
pub struct Color {
    pub r: f32,
    pub g: f32,
    pub b: f32,
    pub a: f32,
}

impl Eq for Color {}

impl std::hash::Hash for Color {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        // Convert f32 to u32 bits for hashing
        self.r.to_bits().hash(state);
        self.g.to_bits().hash(state);
        self.b.to_bits().hash(state);
        self.a.to_bits().hash(state);
    }
}

impl Color {
    /// Create a new color from RGBA values (0.0 to 1.0)
    pub fn rgba(r: f32, g: f32, b: f32, a: f32) -> Self {
        Self { r, g, b, a }
    }

    /// Create a new color from RGB values (0.0 to 1.0)
    pub fn rgb(r: f32, g: f32, b: f32) -> Self {
        Self::rgba(r, g, b, 1.0)
    }

    /// Create a new color from RGBA values (0 to 255)
    pub fn rgba_u8(r: u8, g: u8, b: u8, a: u8) -> Self {
        Self::rgba(
            r as f32 / 255.0,
            g as f32 / 255.0,
            b as f32 / 255.0,
            a as f32 / 255.0,
        )
    }

    /// Create a new color from RGB values (0 to 255)
    pub fn rgb_u8(r: u8, g: u8, b: u8) -> Self {
        Self::rgba_u8(r, g, b, 255)
    }

    /// Create a new color from RGBA values (0 to 255)
    pub fn from_rgba(r: u8, g: u8, b: u8, a: u8) -> Self {
        Self::rgba_u8(r, g, b, a)
    }

    /// Create color from hex string (e.g., "#FF0000" or "#FF0000FF")
    pub fn from_hex(hex: &str) -> Result<Self, String> {
        let hex = hex.trim_start_matches('#');

        match hex.len() {
            6 => {
                let r = u8::from_str_radix(&hex[0..2], 16).map_err(|_| "Invalid hex color")?;
                let g = u8::from_str_radix(&hex[2..4], 16).map_err(|_| "Invalid hex color")?;
                let b = u8::from_str_radix(&hex[4..6], 16).map_err(|_| "Invalid hex color")?;
                Ok(Self::rgb_u8(r, g, b))
            }
            8 => {
                let r = u8::from_str_radix(&hex[0..2], 16).map_err(|_| "Invalid hex color")?;
                let g = u8::from_str_radix(&hex[2..4], 16).map_err(|_| "Invalid hex color")?;
                let b = u8::from_str_radix(&hex[4..6], 16).map_err(|_| "Invalid hex color")?;
                let a = u8::from_str_radix(&hex[6..8], 16).map_err(|_| "Invalid hex color")?;
                Ok(Self::rgba_u8(r, g, b, a))
            }
            _ => Err("Hex color must be 6 or 8 characters".to_string()),
        }
    }

    /// Convert to hex string
    pub fn to_hex(&self) -> String {
        format!(
            "#{:02X}{:02X}{:02X}{:02X}",
            (self.r * 255.0) as u8,
            (self.g * 255.0) as u8,
            (self.b * 255.0) as u8,
            (self.a * 255.0) as u8,
        )
    }

    /// Convert to HSL
    pub fn to_hsl(&self) -> (f32, f32, f32) {
        let max = self.r.max(self.g).max(self.b);
        let min = self.r.min(self.g).min(self.b);
        let delta = max - min;

        // Lightness
        let l = (max + min) / 2.0;

        if delta == 0.0 {
            return (0.0, 0.0, l); // Achromatic
        }

        // Saturation
        let s = if l < 0.5 {
            delta / (max + min)
        } else {
            delta / (2.0 - max - min)
        };

        // Hue
        let h = if max == self.r {
            ((self.g - self.b) / delta + if self.g < self.b { 6.0 } else { 0.0 }) / 6.0
        } else if max == self.g {
            ((self.b - self.r) / delta + 2.0) / 6.0
        } else {
            ((self.r - self.g) / delta + 4.0) / 6.0
        };

        (h * 360.0, s, l)
    }

    /// Create color from HSL values
    pub fn from_hsl(h: f32, s: f32, l: f32) -> Self {
        let h = h / 360.0;

        if s == 0.0 {
            return Self::rgb(l, l, l); // Achromatic
        }

        let hue_to_rgb = |p: f32, q: f32, t: f32| {
            let t = if t < 0.0 {
                t + 1.0
            } else if t > 1.0 {
                t - 1.0
            } else {
                t
            };

            if t < 1.0 / 6.0 {
                p + (q - p) * 6.0 * t
            } else if t < 1.0 / 2.0 {
                q
            } else if t < 2.0 / 3.0 {
                p + (q - p) * (2.0 / 3.0 - t) * 6.0
            } else {
                p
            }
        };

        let q = if l < 0.5 {
            l * (1.0 + s)
        } else {
            l + s - l * s
        };
        let p = 2.0 * l - q;

        let r = hue_to_rgb(p, q, h + 1.0 / 3.0);
        let g = hue_to_rgb(p, q, h);
        let b = hue_to_rgb(p, q, h - 1.0 / 3.0);

        Self::rgb(r, g, b)
    }

    /// Lighten the color by a factor (0.0 to 1.0)
    pub fn lighten(&self, factor: f32) -> Self {
        let (h, s, l) = self.to_hsl();
        Self::from_hsl(h, s, (l + factor * (1.0 - l)).min(1.0))
    }

    /// Darken the color by a factor (0.0 to 1.0)
    pub fn darken(&self, factor: f32) -> Self {
        let (h, s, l) = self.to_hsl();
        Self::from_hsl(h, s, (l * (1.0 - factor)).max(0.0))
    }

    /// Adjust alpha channel
    pub fn with_alpha(&self, alpha: f32) -> Self {
        Self::rgba(self.r, self.g, self.b, alpha.clamp(0.0, 1.0))
    }

    /// Mix two colors
    pub fn mix(&self, other: Color, ratio: f32) -> Self {
        let ratio = ratio.clamp(0.0, 1.0);
        let inv_ratio = 1.0 - ratio;

        Self::rgba(
            self.r * inv_ratio + other.r * ratio,
            self.g * inv_ratio + other.g * ratio,
            self.b * inv_ratio + other.b * ratio,
            self.a * inv_ratio + other.a * ratio,
        )
    }
}

// Predefined colors
impl Color {
    pub const TRANSPARENT: Color = Color {
        r: 0.0,
        g: 0.0,
        b: 0.0,
        a: 0.0,
    };
    pub const BLACK: Color = Color {
        r: 0.0,
        g: 0.0,
        b: 0.0,
        a: 1.0,
    };
    pub const WHITE: Color = Color {
        r: 1.0,
        g: 1.0,
        b: 1.0,
        a: 1.0,
    };
    pub const RED: Color = Color {
        r: 1.0,
        g: 0.0,
        b: 0.0,
        a: 1.0,
    };
    pub const GREEN: Color = Color {
        r: 0.0,
        g: 1.0,
        b: 0.0,
        a: 1.0,
    };
    pub const BLUE: Color = Color {
        r: 0.0,
        g: 0.0,
        b: 1.0,
        a: 1.0,
    };
    pub const YELLOW: Color = Color {
        r: 1.0,
        g: 1.0,
        b: 0.0,
        a: 1.0,
    };
    pub const CYAN: Color = Color {
        r: 0.0,
        g: 1.0,
        b: 1.0,
        a: 1.0,
    };
    pub const MAGENTA: Color = Color {
        r: 1.0,
        g: 0.0,
        b: 1.0,
        a: 1.0,
    };
    pub const GRAY: Color = Color {
        r: 0.5,
        g: 0.5,
        b: 0.5,
        a: 1.0,
    };
    pub const LIGHT_GRAY: Color = Color {
        r: 0.75,
        g: 0.75,
        b: 0.75,
        a: 1.0,
    };
    pub const DARK_GRAY: Color = Color {
        r: 0.25,
        g: 0.25,
        b: 0.25,
        a: 1.0,
    };
}

impl Default for Color {
    fn default() -> Self {
        Self::BLACK
    }
}

impl From<(f32, f32, f32)> for Color {
    fn from((r, g, b): (f32, f32, f32)) -> Self {
        Self::rgb(r, g, b)
    }
}

impl From<(f32, f32, f32, f32)> for Color {
    fn from((r, g, b, a): (f32, f32, f32, f32)) -> Self {
        Self::rgba(r, g, b, a)
    }
}

impl From<(u8, u8, u8)> for Color {
    fn from((r, g, b): (u8, u8, u8)) -> Self {
        Self::rgb_u8(r, g, b)
    }
}

impl From<(u8, u8, u8, u8)> for Color {
    fn from((r, g, b, a): (u8, u8, u8, u8)) -> Self {
        Self::rgba_u8(r, g, b, a)
    }
}

/// Color palette for charts
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ColorPalette {
    pub colors: Vec<Color>,
}

impl ColorPalette {
    pub fn new(colors: Vec<Color>) -> Self {
        Self { colors }
    }

    pub fn get_color(&self, index: usize) -> Color {
        if self.colors.is_empty() {
            Color::BLACK
        } else {
            self.colors[index % self.colors.len()]
        }
    }

    pub fn len(&self) -> usize {
        self.colors.len()
    }

    pub fn is_empty(&self) -> bool {
        self.colors.is_empty()
    }
}

impl Default for ColorPalette {
    fn default() -> Self {
        Self::new(vec![
            Color::from_hex("#5470c6").unwrap(),
            Color::from_hex("#91cc75").unwrap(),
            Color::from_hex("#fac858").unwrap(),
            Color::from_hex("#ee6666").unwrap(),
            Color::from_hex("#73c0de").unwrap(),
            Color::from_hex("#3ba272").unwrap(),
            Color::from_hex("#fc8452").unwrap(),
            Color::from_hex("#9a60b4").unwrap(),
            Color::from_hex("#ea7ccc").unwrap(),
        ])
    }
}

/// Predefined color palettes
impl ColorPalette {
    pub fn default_palette() -> Self {
        Self::default()
    }

    pub fn pastel_palette() -> Self {
        Self::new(vec![
            Color::from_hex("#FFB3BA").unwrap(),
            Color::from_hex("#BAFFC9").unwrap(),
            Color::from_hex("#BAE1FF").unwrap(),
            Color::from_hex("#FFFFBA").unwrap(),
            Color::from_hex("#FFDFBA").unwrap(),
            Color::from_hex("#E0BBE4").unwrap(),
        ])
    }

    pub fn vibrant_palette() -> Self {
        Self::new(vec![
            Color::from_hex("#FF6B6B").unwrap(),
            Color::from_hex("#4ECDC4").unwrap(),
            Color::from_hex("#45B7D1").unwrap(),
            Color::from_hex("#96CEB4").unwrap(),
            Color::from_hex("#FFEAA7").unwrap(),
            Color::from_hex("#DDA0DD").unwrap(),
        ])
    }

    pub fn monochrome_palette() -> Self {
        Self::new(vec![
            Color::from_hex("#2C3E50").unwrap(),
            Color::from_hex("#34495E").unwrap(),
            Color::from_hex("#7F8C8D").unwrap(),
            Color::from_hex("#95A5A6").unwrap(),
            Color::from_hex("#BDC3C7").unwrap(),
            Color::from_hex("#ECF0F1").unwrap(),
        ])
    }
}
