
严格按照zh文件执行，
尽量参考gpui，底层驱动，渲染 gpui_component 的 plot，chart 功能实现方法，函数的使用，应为我的曲线也是建立在这个基础上，
chart 曲线显示，现在只完成最基础的曲线显示，
现在想就创建复杂多功能，曲线显示 可以参考echarts  曲线显示相似，
基础功能：
1支持x，y放大，缩小，
2 xy光标，支持多光标
3 特质值计算，最大值，最小值，频率，波高，峰峰值，平均值等
4，yx轴自适应，满量程，自定义，曲线显示范围
5,支持暂停刷新，查看 鼠标拖动，
6支持鼠标控制功能，比如放大缩小，光标移动，

数据缩放：支持通过滑动条或鼠标滚轮进行数据区域缩放

标记线/区域：可添加辅助线或标记区域突出显示特定范围

堆叠模式：支持多系列数据的堆叠展示

大数据量优化：通过采样等技术支持万级以上数据点流畅展示

响应式设计：自动适应不同屏幕尺寸

可自定义线条颜色、宽度、类型(实线/虚线/点线)

支持平滑曲线(smooth)或直角折线

可配置线条阴影效果


















