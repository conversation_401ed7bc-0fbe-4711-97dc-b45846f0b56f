//! Theme system for Rust ECharts
//!
//! This crate provides built-in themes and theme management functionality.

pub mod builtin;
pub mod manager;

// 测试模块
#[cfg(test)]
pub mod tests;

use echarts_core::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Theme definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Theme {
    /// Theme name
    pub name: String,

    /// Color palette
    pub color_palette: Vec<Color>,

    /// Background color
    pub background_color: Color,

    /// Text style
    pub text_style: TextStyle,

    /// Line style
    pub line_style: LineStyle,

    /// Custom properties
    pub custom: HashMap<String, serde_json::Value>,
}

impl Default for Theme {
    fn default() -> Self {
        Self::light()
    }
}

impl Theme {
    /// Create a simple theme (compatible with SimpleTheme)
    pub fn simple(name: &str, colors: Vec<Color>) -> Self {
        Theme {
            name: name.to_string(),
            color_palette: colors,
            background_color: Color::WHITE,
            text_style: TextStyle::default(),
            line_style: LineStyle::default(),
            custom: HashMap::new(),
        }
    }

    /// Create a light theme
    pub fn light() -> Self {
        Theme {
            name: "light".to_string(),
            color_palette: vec![
                Color::from_hex("#5470c6").unwrap(),
                Color::from_hex("#91cc75").unwrap(),
                Color::from_hex("#fac858").unwrap(),
                Color::from_hex("#ee6666").unwrap(),
                Color::from_hex("#73c0de").unwrap(),
                Color::from_hex("#3ba272").unwrap(),
                Color::from_hex("#fc8452").unwrap(),
                Color::from_hex("#9a60b4").unwrap(),
                Color::from_hex("#ea7ccc").unwrap(),
            ],
            background_color: Color::WHITE,
            text_style: TextStyle::default(),
            line_style: LineStyle::default(),
            custom: HashMap::new(),
        }
    }

    /// Create a dark theme (based on ECharts official dark theme)
    pub fn dark() -> Self {
        Theme {
            name: "dark".to_string(),
            color_palette: vec![
                Color::from_hex("#4992ff").unwrap(),
                Color::from_hex("#7cffb2").unwrap(),
                Color::from_hex("#fddd60").unwrap(),
                Color::from_hex("#ff6e76").unwrap(),
                Color::from_hex("#58d9f9").unwrap(),
                Color::from_hex("#05c091").unwrap(),
                Color::from_hex("#ff8a45").unwrap(),
                Color::from_hex("#8d48e3").unwrap(),
                Color::from_hex("#dd79ff").unwrap(),
            ],
            background_color: Color::from_hex("#100c2a").unwrap(),
            text_style: TextStyle {
                color: Color::from_hex("#B9B8CE").unwrap(),
                ..Default::default()
            },
            line_style: LineStyle {
                color: Color::from_hex("#B9B8CE").unwrap(),
                ..Default::default()
            },
            custom: HashMap::new(),
        }
    }

    /// Get color from palette by index
    pub fn get_color(&self, index: usize) -> Color {
        self.color_palette[index % self.color_palette.len()]
    }

    /// Get next color in sequence
    pub fn next_color(&self, current_index: usize) -> Color {
        self.get_color(current_index + 1)
    }

    /// Get primary color (first color in palette)
    pub fn primary_color(&self) -> Color {
        self.get_color(0)
    }

    /// Get text color from text style
    pub fn text_color(&self) -> Color {
        self.text_style.color
    }
}

/// Theme manager for handling multiple themes
pub struct ThemeManager {
    themes: HashMap<String, Theme>,
    current_theme: String,
}

impl Default for ThemeManager {
    fn default() -> Self {
        let mut manager = ThemeManager {
            themes: HashMap::new(),
            current_theme: "light".to_string(),
        };

        // Register built-in themes
        manager.register_theme(Theme::light());
        manager.register_theme(Theme::dark());

        manager
    }
}

impl ThemeManager {
    /// Create a new theme manager
    pub fn new() -> Self {
        Self::default()
    }

    /// Register a theme
    pub fn register_theme(&mut self, theme: Theme) {
        self.themes.insert(theme.name.clone(), theme);
    }

    /// Get current theme
    pub fn current_theme(&self) -> &Theme {
        self.themes.get(&self.current_theme).unwrap()
    }

    /// Set current theme
    pub fn set_current_theme(&mut self, name: &str) -> Result<()> {
        if self.themes.contains_key(name) {
            self.current_theme = name.to_string();
            Ok(())
        } else {
            Err(ChartError::config(format!("Theme '{}' not found", name)))
        }
    }

    /// Get theme by name
    pub fn get_theme(&self, name: &str) -> Option<&Theme> {
        self.themes.get(name)
    }

    /// List available themes
    pub fn list_themes(&self) -> Vec<&str> {
        self.themes.keys().map(|s| s.as_str()).collect()
    }
}
