//! RenderContext 测试
//!
//! 测试统一的 RenderContext API 功能

#[cfg(test)]
mod tests {
    use crate::{
        render_context::{RenderContext, SimpleTheme, Transform, PerformanceHint},
        Bounds, Color, Point,
        draw_commands::{LineStyle, LineCap, LineJoin},
    };

    #[test]
    fn test_render_context_creation() {
        let ctx = RenderContext::new();
        assert_eq!(ctx.commands().len(), 0);
        assert_eq!(ctx.bounds().size.width, 800.0);
        assert_eq!(ctx.bounds().size.height, 600.0);
        assert_eq!(ctx.theme().name, "default");
    }

    #[test]
    fn test_render_context_with_config() {
        let bounds = Bounds::new(0.0, 0.0, 1200.0, 800.0);
        let theme = SimpleTheme {
            name: "custom".to_string(),
            ..Default::default()
        };
        let ctx = RenderContext::with_config(bounds, theme, PerformanceHint::Quality);
        
        assert_eq!(ctx.bounds().size.width, 1200.0);
        assert_eq!(ctx.theme().name, "custom");
        assert_eq!(ctx.performance_hint(), PerformanceHint::Quality);
    }

    #[test]
    fn test_draw_commands() {
        let mut ctx = RenderContext::new();
        
        // 测试绘制线条
        let line_style = LineStyle {
            color: Color::rgb(1.0, 0.0, 0.0),
            width: 2.0,
            opacity: 1.0,
            dash_pattern: None,
            cap: LineCap::Round,
            join: LineJoin::Round,
        };
        
        ctx.draw_line(Point::new(0.0, 0.0), Point::new(100.0, 100.0), line_style);
        assert_eq!(ctx.commands().len(), 1);
        
        // 测试绘制圆形
        ctx.draw_circle_styled(Point::new(50.0, 50.0), 25.0);
        assert_eq!(ctx.commands().len(), 2);
        
        // 测试绘制矩形
        let bounds = Bounds::new(10.0, 10.0, 80.0, 60.0);
        ctx.draw_rect_styled(bounds);
        assert_eq!(ctx.commands().len(), 3);
        
        // 测试绘制文本
        ctx.draw_text_styled("Hello World".to_string(), Point::new(50.0, 200.0));
        assert_eq!(ctx.commands().len(), 4);
    }

    #[test]
    fn test_batch_operations() {
        let mut ctx = RenderContext::new();
        
        // 开始批量模式
        ctx.begin_batch();
        assert!(ctx.is_batching());
        
        // 添加一些命令
        ctx.draw_circle_styled(Point::new(0.0, 0.0), 10.0);
        ctx.draw_circle_styled(Point::new(10.0, 10.0), 10.0);
        
        // 此时主命令列表应该为空
        assert_eq!(ctx.commands().len(), 0);
        
        // 结束批量模式
        ctx.end_batch();
        assert!(!ctx.is_batching());
        
        // 现在命令应该被提交
        assert_eq!(ctx.commands().len(), 2);
    }

    #[test]
    fn test_style_stack() {
        let mut ctx = RenderContext::new();
        
        // 保存当前样式
        ctx.save_style();
        
        // 修改样式
        let mut new_style = ctx.current_style().clone();
        new_style.fill_color = Color::rgb(1.0, 0.0, 0.0);
        ctx.set_style(new_style);
        
        assert_eq!(ctx.current_style().fill_color, Color::rgb(1.0, 0.0, 0.0));
        
        // 恢复样式
        ctx.restore_style();
        assert_ne!(ctx.current_style().fill_color, Color::rgb(1.0, 0.0, 0.0));
    }

    #[test]
    fn test_transform_operations() {
        let mut ctx = RenderContext::new();
        
        // 测试平移
        ctx.translate(10.0, 20.0);
        let transformed = ctx.transform_point(Point::new(0.0, 0.0));
        assert_eq!(transformed.x, 10.0);
        assert_eq!(transformed.y, 20.0);
        
        // 测试缩放
        ctx.scale(2.0, 2.0);
        let transformed = ctx.transform_point(Point::new(5.0, 5.0));
        assert_eq!(transformed.x, 20.0); // (5 * 2) + 10
        assert_eq!(transformed.y, 30.0); // (5 * 2) + 20
    }

    #[test]
    fn test_transform_stack() {
        let mut ctx = RenderContext::new();
        
        // 保存变换
        ctx.save_transform();
        
        // 应用变换
        ctx.translate(100.0, 100.0);
        let transformed = ctx.transform_point(Point::new(0.0, 0.0));
        assert_eq!(transformed.x, 100.0);
        assert_eq!(transformed.y, 100.0);
        
        // 恢复变换
        ctx.restore_transform();
        let transformed = ctx.transform_point(Point::new(0.0, 0.0));
        assert_eq!(transformed.x, 0.0);
        assert_eq!(transformed.y, 0.0);
    }

    #[test]
    fn test_convenience_methods() {
        let mut ctx = RenderContext::new();
        
        // 测试 with_batch
        ctx.with_batch(|ctx| {
            ctx.draw_circle_styled(Point::new(0.0, 0.0), 5.0);
            ctx.draw_circle_styled(Point::new(10.0, 10.0), 5.0);
        });
        
        assert_eq!(ctx.commands().len(), 2);
        
        // 测试 with_style
        let original_color = ctx.current_style().fill_color;
        ctx.with_style(|ctx| {
            let mut style = ctx.current_style().clone();
            style.fill_color = Color::rgb(0.0, 1.0, 0.0);
            ctx.set_style(style);
            
            assert_eq!(ctx.current_style().fill_color, Color::rgb(0.0, 1.0, 0.0));
        });
        
        // 样式应该被恢复
        assert_eq!(ctx.current_style().fill_color, original_color);
    }

    #[test]
    fn test_extensions() {
        let mut ctx = RenderContext::new();
        
        // 设置扩展属性
        ctx.set_extension("renderer".to_string(), "svg".to_string());
        ctx.set_extension("quality".to_string(), "high".to_string());
        
        // 获取扩展属性
        assert_eq!(ctx.get_extension("renderer"), Some(&"svg".to_string()));
        assert_eq!(ctx.get_extension("quality"), Some(&"high".to_string()));
        assert_eq!(ctx.get_extension("nonexistent"), None);
        
        // 移除扩展属性
        let removed = ctx.remove_extension("quality");
        assert_eq!(removed, Some("high".to_string()));
        assert_eq!(ctx.get_extension("quality"), None);
    }

    #[test]
    fn test_simple_theme() {
        let theme = SimpleTheme::default();
        
        assert_eq!(theme.name, "default");
        assert_eq!(theme.color_palette.len(), 8);
        assert_eq!(theme.background_color, Color::rgb(1.0, 1.0, 1.0));
        
        // 测试调色板颜色
        assert_eq!(theme.color_palette[0], Color::rgb(0.33, 0.44, 0.78)); // #5470c6
    }

    #[test]
    fn test_transform_matrix() {
        let identity = Transform::identity();
        assert_eq!(identity.matrix, [1.0, 0.0, 0.0, 1.0, 0.0, 0.0]);
        
        let translate = Transform::translate(10.0, 20.0);
        assert_eq!(translate.matrix, [1.0, 0.0, 0.0, 1.0, 10.0, 20.0]);
        
        let scale = Transform::scale(2.0, 3.0);
        assert_eq!(scale.matrix, [2.0, 0.0, 0.0, 3.0, 0.0, 0.0]);
        
        // 测试点变换
        let point = Point::new(5.0, 10.0);
        let transformed = translate.transform_point(point);
        assert_eq!(transformed.x, 15.0);
        assert_eq!(transformed.y, 30.0);
    }
}
