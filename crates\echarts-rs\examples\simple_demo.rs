//! 简化的新架构演示
//!
//! 这个示例展示了新的 RuntimeChart 和 ChartBuilder 的基本功能
//! 使用临时实现，等待 echarts-charts 编译完成后将使用真实的 Series 类型

use echarts_rs::{RuntimeChart, ChartBuilder};
use echarts_core::{Bounds, Point, Color};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎯 新架构演示 - EXECUTION_PLAN.md 方案2");
    println!("=====================================");

    // 1. 使用 ChartBuilder 创建图表
    let chart = ChartBuilder::new()
        .title("新架构演示图表")
        .build();

    // 2. 创建模拟的 Series 数据（临时实现）
    let line_data = vec![120.0, 132.0, 101.0, 134.0, 90.0, 230.0, 210.0];
    let bar_data = vec![20.0, 25.0, 18.0, 30.0, 22.0, 35.0, 28.0];
    let pie_data = vec![
        ("产品A", 30.0),
        ("产品B", 25.0),
        ("产品C", 20.0),
        ("产品D", 15.0),
        ("其他", 10.0),
    ];
    let scatter_data = vec![
        (10.0, 20.0),
        (15.0, 25.0),
        (20.0, 18.0),
        (25.0, 30.0),
        (30.0, 22.0),
    ];

    // 3. 将 Series 添加到 RuntimeChart（使用临时实现）
    let runtime_chart = chart
        .add_line_series(line_data)
        .add_bar_series(bar_data)
        .add_pie_series(pie_data)
        .add_scatter_series(scatter_data);

    // 4. 创建模拟渲染器并渲染
    struct MockRenderer;
    impl echarts_core::ChartRenderer for MockRenderer {
        fn draw_text(&mut self, text: &str, _position: Point, _style: &echarts_core::TextStyle) -> echarts_core::Result<()> {
            println!("🖊️  绘制文本: {}", text);
            Ok(())
        }
        
        fn draw_line(&mut self, _start: Point, _end: Point, _style: &echarts_core::LineStyle) -> echarts_core::Result<()> {
            println!("📏 绘制线条: 颜色={:?}, 宽度={}", _style.color, _style.width);
            Ok(())
        }
        
        fn draw_rect(&mut self, _bounds: Bounds, _style: &echarts_core::RectStyle) -> echarts_core::Result<()> {
            println!("⬜ 绘制矩形");
            Ok(())
        }
        
        fn draw_circle(&mut self, _center: Point, _radius: f64, _style: &echarts_core::CircleStyle) -> echarts_core::Result<()> {
            println!("⭕ 绘制圆形");
            Ok(())
        }
        
        fn draw_path(&mut self, _path: &echarts_core::Path, _style: &echarts_core::PathStyle) -> echarts_core::Result<()> {
            println!("🛤️  绘制路径");
            Ok(())
        }
    }

    let mut renderer = MockRenderer;
    let bounds = Bounds::new(Point::new(0.0, 0.0), 800.0, 600.0);

    println!("🎨 开始渲染新架构图表...");
    runtime_chart.render(&mut renderer, bounds)?;

    // 5. 输出渲染结果
    println!("✅ 渲染完成！");
    println!("📊 图表包含:");
    println!("   - 1个折线图系列（临时实现）");
    println!("   - 1个柱状图系列（临时实现）");
    println!("   - 1个饼图系列（临时实现）");
    println!("   - 1个散点图系列（临时实现）");

    println!("\n🎯 新架构优势:");
    println!("   ✓ 类型安全的 RuntimeChart");
    println!("   ✓ 构建器模式的 ChartBuilder");
    println!("   ✓ 模块化的架构设计");
    println!("   ✓ 易于扩展和维护");
    println!("   ✓ 等待 echarts-charts 完成后将使用真实 Series");

    Ok(())
}
