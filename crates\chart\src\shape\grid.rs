use gpui::{px, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PathBuilder, Pixels, Point, Window};

use crate::shape::{dash_line, origin_point};

pub struct GridAttr {
    pub grid: Pixels,
    pub dash_array: Option<[Pixels; 2]>,
}

pub struct Grid {
    x: Vec<GridAttr>,
    y: Vec<GridAttr>,
    stroke: Hsla,
}

impl Grid {
    #[allow(clippy::new_without_default)]
    pub fn new() -> Self {
        Self {
            x: vec![],
            y: vec![],
            stroke: Default::default(),
        }
    }

    /// Set the x of the Grid.
    pub fn x(mut self, x: Vec<impl Into<GridAttr>>) -> Self {
        self.x = x.into_iter().map(|v| v.into()).collect();
        self
    }

    /// Set the y of the Grid.
    pub fn y(mut self, y: Vec<impl Into<GridAttr>>) -> Self {
        self.y = y.into_iter().map(|v| v.into()).collect();
        self
    }

    /// Set the stroke color of the Grid.
    pub fn stroke(mut self, stroke: impl Into<Hsla>) -> Self {
        self.stroke = stroke.into();
        self
    }

    fn points(
        &self,
        bounds: &Bounds<Pixels>,
    ) -> Vec<(Point<Pixels>, Point<Pixels>, Option<[Pixels; 2]>)> {
        let size = bounds.size;
        let origin = bounds.origin;

        let mut points = Vec::new();

        for x_attr in &self.x {
            let start = origin_point(x_attr.grid, px(0.), origin);
            let end = origin_point(x_attr.grid, size.height, origin);
            points.push((start, end, x_attr.dash_array));
        }

        for y_attr in &self.y {
            let start = origin_point(px(0.), y_attr.grid, origin);
            let end = origin_point(size.width, y_attr.grid, origin);
            points.push((start, end, y_attr.dash_array));
        }

        points
    }

    /// Paint the Grid.
    pub fn paint(&self, bounds: &Bounds<Pixels>, window: &mut Window) {
        let points = self.points(bounds);
        for (start, end, dash_array) in points {
            if let Some(d) = dash_array {
                if let Some(line) = dash_line(start, end, d, 2.0) {
                    window.paint_path(line, self.stroke);
                }
            } else {
                let mut builder = PathBuilder::stroke(px(2.));
                builder.move_to(start);
                builder.line_to(end);
                if let Ok(line) = builder.build() {
                    window.paint_path(line, self.stroke);
                }
            }
        }
    }
}
