//! 饼图SVG演示
//!
//! 生成各种饼图的SVG文件，展示PieSeries的完整功能

use std::fs;
use echarts_rs::{PieSeries, RoseType};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🥧 饼图SVG演示系统");
    println!("{}", "=".repeat(50));

    // 确保输出目录存在
    let output_dir = "temp/svg/pie_charts";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 基础饼图
    println!("\n🥧 1. 生成基础饼图...");
    generate_basic_pie_chart(output_dir)?;

    // 2. 环形图
    println!("\n🍩 2. 生成环形图...");
    generate_donut_chart(output_dir)?;

    // 3. 玫瑰图
    println!("\n🌹 3. 生成玫瑰图...");
    generate_rose_chart(output_dir)?;

    // 4. 多样式饼图
    println!("\n🎨 4. 生成多样式饼图...");
    generate_styled_pie_chart(output_dir)?;

    // 5. 数据对比饼图
    println!("\n📊 5. 生成数据对比饼图...");
    generate_comparison_pie_charts(output_dir)?;

    // 6. 生成展示页面
    println!("\n📄 6. 生成展示页面...");
    generate_pie_showcase(output_dir)?;

    println!("\n🎉 饼图SVG演示生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/pie_demo.html 查看演示", output_dir);

    Ok(())
}

/// 生成基础饼图
fn generate_basic_pie_chart(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let pie_series = PieSeries::new("访问来源")
        .data(vec![
            ("移动端", 45.2),
            ("桌面端", 32.8),
            ("平板端", 15.6),
            ("其他", 6.4),
        ])
        .center(0.5, 0.5)
        .radius(0.7)
        .start_angle(90.0)
        .show_label(true);

    let svg = create_pie_svg(&pie_series, "基础饼图演示", 600.0, 400.0)?;
    fs::write(format!("{}/01_basic_pie.svg", output_dir), svg)?;

    println!("  ✅ 基础饼图生成完成");
    Ok(())
}

/// 生成环形图
fn generate_donut_chart(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let pie_series = PieSeries::new("部门分布")
        .data(vec![
            ("销售部", 28.5),
            ("技术部", 35.2),
            ("市场部", 18.7),
            ("运营部", 12.3),
            ("财务部", 5.3),
        ])
        .center(0.5, 0.5)
        .as_donut(0.4, 0.8)
        .show_label(true);

    let svg = create_pie_svg(&pie_series, "环形图演示", 600.0, 400.0)?;
    fs::write(format!("{}/02_donut_chart.svg", output_dir), svg)?;

    println!("  ✅ 环形图生成完成");
    Ok(())
}

/// 生成玫瑰图
fn generate_rose_chart(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let pie_series = PieSeries::new("产品销量")
        .data(vec![
            ("产品A", 120.0),
            ("产品B", 200.0),
            ("产品C", 150.0),
            ("产品D", 80.0),
            ("产品E", 70.0),
            ("产品F", 110.0),
            ("产品G", 130.0),
            ("产品H", 90.0),
        ])
        .center(0.5, 0.5)
        .radius(0.8)
        .as_rose(RoseType::Radius)
        .show_label(true);

    let svg = create_pie_svg(&pie_series, "玫瑰图演示", 600.0, 400.0)?;
    fs::write(format!("{}/03_rose_chart.svg", output_dir), svg)?;

    println!("  ✅ 玫瑰图生成完成");
    Ok(())
}

/// 生成多样式饼图
fn generate_styled_pie_chart(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let pie_series = PieSeries::new("评价分布")
        .data(vec![
            ("优秀", 25.0),
            ("良好", 35.0),
            ("一般", 28.0),
            ("较差", 12.0),
        ])
        .center(0.5, 0.5)
        .radius(0.75)
        .start_angle(45.0)
        .show_label(true);

    let svg = create_pie_svg(&pie_series, "多样式饼图演示", 600.0, 400.0)?;
    fs::write(format!("{}/04_styled_pie.svg", output_dir), svg)?;

    println!("  ✅ 多样式饼图生成完成");
    Ok(())
}

/// 生成数据对比饼图
fn generate_comparison_pie_charts(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建两个饼图进行对比
    let q1_series = PieSeries::new("第一季度")
        .data(vec![
            ("收入", 450.0),
            ("成本", 280.0),
            ("利润", 170.0),
        ])
        .center(0.25, 0.5)
        .radius(0.3)
        .show_label(true);

    let q2_series = PieSeries::new("第二季度")
        .data(vec![
            ("收入", 520.0),
            ("成本", 310.0),
            ("利润", 210.0),
        ])
        .center(0.75, 0.5)
        .radius(0.3)
        .show_label(true);

    let svg = create_comparison_pie_svg(&q1_series, &q2_series, "季度数据对比", 800.0, 400.0)?;
    fs::write(format!("{}/05_comparison_pies.svg", output_dir), svg)?;

    println!("  ✅ 数据对比饼图生成完成");
    Ok(())
}

/// 生成展示页面
fn generate_pie_showcase(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts-rs 饼图演示</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { 
            text-align: center; 
            margin-bottom: 40px; 
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .section { 
            background: rgba(255,255,255,0.95); 
            margin: 30px 0; 
            padding: 30px; 
            border-radius: 15px; 
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .chart-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); 
            gap: 20px; 
            margin: 20px 0;
        }
        .chart-item { 
            background: white; 
            padding: 20px; 
            border-radius: 12px; 
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title { 
            font-size: 16px; 
            font-weight: bold; 
            margin-bottom: 15px; 
            color: #333;
            text-align: center;
        }
        .chart-svg { 
            width: 100%; 
            height: auto; 
            border-radius: 8px;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h2 { color: #333; margin-bottom: 20px; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        .description {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🥧 ECharts-rs 饼图演示</h1>
            <p class="description">展现 PieSeries 的强大功能和灵活配置</p>
            <div class="feature-list">
                <div class="feature">
                    <h3>🥧 基础饼图</h3>
                    <p>标准扇形数据展示</p>
                </div>
                <div class="feature">
                    <h3>🍩 环形图</h3>
                    <p>内外半径可配置</p>
                </div>
                <div class="feature">
                    <h3>🌹 玫瑰图</h3>
                    <p>半径或面积模式</p>
                </div>
                <div class="feature">
                    <h3>🎨 样式配置</h3>
                    <p>丰富的视觉效果</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>🥧 基础饼图功能</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">基础饼图</div>
                    <object class="chart-svg" data="01_basic_pie.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">环形图</div>
                    <object class="chart-svg" data="02_donut_chart.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>🌹 高级饼图功能</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">玫瑰图</div>
                    <object class="chart-svg" data="03_rose_chart.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">多样式饼图</div>
                    <object class="chart-svg" data="04_styled_pie.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📊 数据对比</h2>
            <div class="chart-item">
                <div class="chart-title">季度数据对比</div>
                <object class="chart-svg" data="05_comparison_pies.svg" type="image/svg+xml">SVG不支持</object>
            </div>
        </div>
        
        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h2>🎉 PieSeries 功能总结</h2>
            <p>ECharts-rs PieSeries 完整功能展示：</p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>✅ <strong>基础饼图</strong> - 标准的扇形数据展示</li>
                <li>✅ <strong>环形图</strong> - 内外半径可配置的环形展示</li>
                <li>✅ <strong>玫瑰图</strong> - 半径或面积模式的玫瑰图</li>
                <li>✅ <strong>标签系统</strong> - 内部、外部、中心位置标签</li>
                <li>✅ <strong>引导线</strong> - 自动生成的标签引导线</li>
                <li>✅ <strong>样式配置</strong> - 颜色、透明度、边框等</li>
                <li>✅ <strong>多图表支持</strong> - 同一画布多个饼图对比</li>
                <li>✅ <strong>数据验证</strong> - 完整的数据处理和验证</li>
            </ul>
        </div>
    </div>
</body>
</html>"#;

    fs::write(format!("{}/pie_demo.html", output_dir), html_content)?;
    Ok(())
}

/// 创建饼图SVG
fn create_pie_svg(series: &PieSeries, title: &str, width: f64, height: f64) -> std::result::Result<String, Box<dyn std::error::Error>> {
    use echarts_core::{CartesianCoordinateSystem, Bounds, Series};

    // 创建坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, width - 100.0, height - 100.0),
        (0.0, 1.0),
        (0.0, 1.0),
    );

    // 获取渲染命令
    let commands = series.render_to_commands(&coord_system)?;

    // 生成SVG
    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    // 渲染命令
    for command in commands {
        render_svg_command(&mut svg, &command);
    }

    svg.push_str("</svg>");
    Ok(svg)
}

/// 创建对比饼图SVG
fn create_comparison_pie_svg(series1: &PieSeries, series2: &PieSeries, title: &str, width: f64, height: f64) -> std::result::Result<String, Box<dyn std::error::Error>> {
    use echarts_core::{CartesianCoordinateSystem, Bounds, Series};

    // 创建坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, width - 100.0, height - 100.0),
        (0.0, 1.0),
        (0.0, 1.0),
    );

    // 获取两个饼图的渲染命令
    let commands1 = series1.render_to_commands(&coord_system)?;
    let commands2 = series2.render_to_commands(&coord_system)?;

    // 生成SVG
    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    // 渲染第一个饼图
    for command in commands1 {
        render_svg_command(&mut svg, &command);
    }

    // 渲染第二个饼图
    for command in commands2 {
        render_svg_command(&mut svg, &command);
    }

    // 添加图例
    svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"14\" fill=\"#666\">左：{}</text>\n", width * 0.25, height - 30.0, series1.name()));
    svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"14\" fill=\"#666\">右：{}</text>\n", width * 0.75, height - 30.0, series2.name()));

    svg.push_str("</svg>");
    Ok(svg)
}

/// 渲染单个SVG命令
fn render_svg_command(svg: &mut String, command: &echarts_core::DrawCommand) {
    use echarts_core::{DrawCommand, PathCommand};

    match command {
        DrawCommand::Path { commands, style } => {
            let mut path_data = String::new();
            for cmd in commands {
                match cmd {
                    PathCommand::MoveTo(point) => {
                        path_data.push_str(&format!("M {} {} ", point.x, point.y));
                    }
                    PathCommand::LineTo(point) => {
                        path_data.push_str(&format!("L {} {} ", point.x, point.y));
                    }
                    PathCommand::CurveTo { control1, control2, to } => {
                        path_data.push_str(&format!("C {} {} {} {} {} {} ",
                            control1.x, control1.y, control2.x, control2.y, to.x, to.y));
                    }
                    PathCommand::QuadTo { control, to } => {
                        path_data.push_str(&format!("Q {} {} {} {} ", control.x, control.y, to.x, to.y));
                    }
                    PathCommand::Close => {
                        path_data.push_str("Z ");
                    }
                }
            }

            let fill = style.fill.map(|c| format!("#{:02x}{:02x}{:02x}",
                (c.r * 255.0) as u8, (c.g * 255.0) as u8, (c.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke = style.stroke.as_ref().map(|s| format!("#{:02x}{:02x}{:02x}",
                (s.color.r * 255.0) as u8, (s.color.g * 255.0) as u8, (s.color.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke_width = style.stroke.as_ref().map(|s| s.width).unwrap_or(0.0);

            svg.push_str(&format!("  <path d=\"{}\" fill=\"{}\" stroke=\"{}\" stroke-width=\"{}\" opacity=\"{}\"/>\n",
                path_data.trim(), fill, stroke, stroke_width, style.opacity));
        }
        DrawCommand::Text { text, position, style } => {
            let color = format!("#{:02x}{:02x}{:02x}",
                (style.color.r * 255.0) as u8, (style.color.g * 255.0) as u8, (style.color.b * 255.0) as u8);
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"{}\" fill=\"{}\" text-anchor=\"middle\" opacity=\"{}\">{}</text>\n",
                position.x, position.y, style.font_size, color, style.opacity, text));
        }
        DrawCommand::Line { from, to, style } => {
            let color = format!("#{:02x}{:02x}{:02x}",
                (style.color.r * 255.0) as u8, (style.color.g * 255.0) as u8, (style.color.b * 255.0) as u8);
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"{}\" stroke-width=\"{}\"/>\n",
                from.x, from.y, to.x, to.y, color, style.width));
        }
        DrawCommand::Circle { center, radius, style } => {
            let fill = style.fill.map(|c| format!("#{:02x}{:02x}{:02x}",
                (c.r * 255.0) as u8, (c.g * 255.0) as u8, (c.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke = style.stroke.as_ref().map(|s| format!("#{:02x}{:02x}{:02x}",
                (s.color.r * 255.0) as u8, (s.color.g * 255.0) as u8, (s.color.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke_width = style.stroke.as_ref().map(|s| s.width).unwrap_or(0.0);

            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"{}\" fill=\"{}\" stroke=\"{}\" stroke-width=\"{}\" opacity=\"{}\"/>\n",
                center.x, center.y, radius, fill, stroke, stroke_width, style.opacity));
        }
        _ => {} // 忽略其他命令类型
    }
}
