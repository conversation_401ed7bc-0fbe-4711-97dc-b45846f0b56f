//! 漏斗图实现
//!
//! 提供完整的漏斗图功能，包括销售漏斗、转化率分析、流程分析等应用场景

use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};
use echarts_core::{
    Color, DrawCommand, Point, Series, CoordinateSystem, SeriesType, Bounds, DataSet,
    draw_commands::{PathCommand, PathStyle},
    TextStyle, LineStyle, FontWeight, FontStyle, TextAlign, TextBaseline, LineCap, LineJoin,
    Result
};
use std::f64::consts::PI;

/// 漏斗图数据项
#[derive(Debug, Clone)]
pub struct FunnelDataItem {
    /// 数据名称
    pub name: String,
    /// 数值
    pub value: f64,
    /// 颜色
    pub color: Option<Color>,
    /// 标签
    pub label: Option<String>,
    /// 是否可见
    pub visible: bool,
}

impl FunnelDataItem {
    /// 创建新的漏斗图数据项
    pub fn new(name: impl Into<String>, value: f64) -> Self {
        Self {
            name: name.into(),
            value,
            color: None,
            label: None,
            visible: true,
        }
    }

    /// 设置颜色
    pub fn color(mut self, color: Color) -> Self {
        self.color = Some(color);
        self
    }

    /// 设置标签
    pub fn label(mut self, label: impl Into<String>) -> Self {
        self.label = Some(label.into());
        self
    }

    /// 设置可见性
    pub fn visible(mut self, visible: bool) -> Self {
        self.visible = visible;
        self
    }
}

/// 漏斗图标签配置
#[derive(Debug, Clone)]
pub struct FunnelLabel {
    /// 是否显示标签
    pub show: bool,
    /// 字体大小
    pub font_size: f64,
    /// 标签颜色
    pub color: Color,
    /// 标签位置
    pub position: FunnelLabelPosition,
    /// 显示格式
    pub formatter: Option<String>,
}

impl Default for FunnelLabel {
    fn default() -> Self {
        Self {
            show: true,
            font_size: 12.0,
            color: Color::rgb(0.2, 0.2, 0.2),
            position: FunnelLabelPosition::Inside,
            formatter: None,
        }
    }
}

/// 标签位置
#[derive(Debug, Clone, Copy)]
pub enum FunnelLabelPosition {
    /// 内侧
    Inside,
    /// 外侧
    Outside,
    /// 左侧
    Left,
    /// 右侧
    Right,
}

/// 漏斗图排序方式
#[derive(Debug, Clone, Copy)]
pub enum FunnelSort {
    /// 降序（默认）
    Descending,
    /// 升序
    Ascending,
    /// 不排序
    None,
}

/// 漏斗图对齐方式
#[derive(Debug, Clone, Copy)]
pub enum FunnelAlign {
    /// 左对齐
    Left,
    /// 居中对齐
    Center,
    /// 右对齐
    Right,
}

/// 漏斗图梯形节点（用于布局计算）
#[derive(Debug, Clone)]
struct FunnelNode {
    /// 数据项引用
    item: FunnelDataItem,
    /// 顶部宽度
    top_width: f64,
    /// 底部宽度
    bottom_width: f64,
    /// Y坐标位置
    y: f64,
    /// 高度
    height: f64,
    /// 左边界X坐标
    left_x: f64,
    /// 右边界X坐标
    right_x: f64,
}

/// 漏斗图系列
#[derive(Debug, Clone)]
pub struct FunnelSeries {
    /// 基础配置
    config: ChartConfig,
    /// 数据项
    data: Vec<FunnelDataItem>,
    /// 漏斗图位置和大小 (left, top, width, height)
    position: (f64, f64, f64, f64),
    /// 最小尺寸（最小层的宽度占比）
    min_size: f64,
    /// 最大尺寸（最大层的宽度占比）
    max_size: f64,
    /// 排序方式
    sort: FunnelSort,
    /// 对齐方式
    align: FunnelAlign,
    /// 间隙大小
    gap: f64,
    /// 标签配置
    label: FunnelLabel,
    /// 边框宽度
    border_width: f64,
    /// 边框颜色
    border_color: Color,
    /// 默认颜色列表
    colors: Vec<Color>,
}

impl FunnelSeries {
    /// 创建新的漏斗图系列
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            config: ChartConfig {
                name: name.into(),
                ..ChartConfig::default()
            },
            data: Vec::new(),
            position: (0.1, 0.1, 0.8, 0.8), // 默认占用80%的空间
            min_size: 0.0,
            max_size: 1.0,
            sort: FunnelSort::Descending,
            align: FunnelAlign::Center,
            gap: 2.0,
            label: FunnelLabel::default(),
            border_width: 1.0,
            border_color: Color::rgb(1.0, 1.0, 1.0),
            colors: vec![
                Color::rgb(0.3, 0.6, 1.0),
                Color::rgb(0.6, 0.8, 0.4),
                Color::rgb(1.0, 0.6, 0.3),
                Color::rgb(0.8, 0.4, 0.8),
                Color::rgb(0.4, 0.8, 0.8),
                Color::rgb(1.0, 0.8, 0.2),
                Color::rgb(0.8, 0.2, 0.4),
            ],
        }
    }

    /// 设置数据
    pub fn data(mut self, data: Vec<FunnelDataItem>) -> Self {
        self.data = data;
        self
    }

    /// 添加数据项
    pub fn add_data(mut self, item: FunnelDataItem) -> Self {
        self.data.push(item);
        self
    }

    /// 设置位置和大小
    pub fn position(mut self, left: f64, top: f64, width: f64, height: f64) -> Self {
        self.position = (left, top, width, height);
        self
    }

    /// 设置最小尺寸
    pub fn min_size(mut self, min_size: f64) -> Self {
        self.min_size = min_size.clamp(0.0, 1.0);
        self
    }

    /// 设置最大尺寸
    pub fn max_size(mut self, max_size: f64) -> Self {
        self.max_size = max_size.clamp(0.0, 1.0);
        self
    }

    /// 设置排序方式
    pub fn sort(mut self, sort: FunnelSort) -> Self {
        self.sort = sort;
        self
    }

    /// 设置对齐方式
    pub fn align(mut self, align: FunnelAlign) -> Self {
        self.align = align;
        self
    }

    /// 设置间隙
    pub fn gap(mut self, gap: f64) -> Self {
        self.gap = gap;
        self
    }

    /// 设置标签配置
    pub fn label(mut self, label: FunnelLabel) -> Self {
        self.label = label;
        self
    }

    /// 设置边框
    pub fn border(mut self, width: f64, color: Color) -> Self {
        self.border_width = width;
        self.border_color = color;
        self
    }

    /// 设置颜色列表
    pub fn colors(mut self, colors: Vec<Color>) -> Self {
        self.colors = colors;
        self
    }

    /// 计算漏斗图布局
    fn calculate_layout(&self, bounds: Bounds) -> Vec<FunnelNode> {
        if self.data.is_empty() {
            return Vec::new();
        }

        // 排序数据
        let mut sorted_data = self.data.clone();
        match self.sort {
            FunnelSort::Descending => {
                sorted_data.sort_by(|a, b| b.value.partial_cmp(&a.value).unwrap_or(std::cmp::Ordering::Equal));
            }
            FunnelSort::Ascending => {
                sorted_data.sort_by(|a, b| a.value.partial_cmp(&b.value).unwrap_or(std::cmp::Ordering::Equal));
            }
            FunnelSort::None => {
                // 保持原始顺序
            }
        }

        // 计算漏斗图区域
        let funnel_left = bounds.origin.x + bounds.size.width * self.position.0;
        let funnel_top = bounds.origin.y + bounds.size.height * self.position.1;
        let funnel_width = bounds.size.width * self.position.2;
        let funnel_height = bounds.size.height * self.position.3;

        // 计算每个节点的高度
        let total_gap = self.gap * (sorted_data.len() - 1) as f64;
        let available_height = funnel_height - total_gap;
        let node_height = available_height / sorted_data.len() as f64;

        // 找到最大值用于宽度计算
        let max_value = sorted_data.iter().map(|item| item.value).fold(0.0, f64::max);
        let min_value = sorted_data.iter().map(|item| item.value).fold(f64::INFINITY, f64::min);

        let mut nodes = Vec::new();
        
        for (i, item) in sorted_data.iter().enumerate() {
            // 计算宽度比例
            let width_ratio = if max_value > min_value {
                self.min_size + (self.max_size - self.min_size) * (item.value - min_value) / (max_value - min_value)
            } else {
                self.max_size
            };

            let node_width = funnel_width * width_ratio;
            let y = funnel_top + i as f64 * (node_height + self.gap);

            // 计算X位置（根据对齐方式）
            let left_x = match self.align {
                FunnelAlign::Left => funnel_left,
                FunnelAlign::Center => funnel_left + (funnel_width - node_width) / 2.0,
                FunnelAlign::Right => funnel_left + funnel_width - node_width,
            };

            let right_x = left_x + node_width;

            // 计算顶部和底部宽度（梯形效果）
            let next_width_ratio = if i + 1 < sorted_data.len() {
                let next_value = sorted_data[i + 1].value;
                if max_value > min_value {
                    self.min_size + (self.max_size - self.min_size) * (next_value - min_value) / (max_value - min_value)
                } else {
                    self.max_size
                }
            } else {
                width_ratio
            };

            let next_node_width = funnel_width * next_width_ratio;

            nodes.push(FunnelNode {
                item: item.clone(),
                top_width: node_width,
                bottom_width: next_node_width,
                y,
                height: node_height,
                left_x,
                right_x,
            });
        }

        nodes
    }

    /// 生成梯形和标签
    fn generate_trapezoids_and_labels(&self, nodes: &[FunnelNode]) -> Vec<DrawCommand> {
        let mut commands = Vec::new();

        for (i, node) in nodes.iter().enumerate() {
            if !node.item.visible {
                continue;
            }

            // 获取颜色
            let color = node.item.color.unwrap_or_else(|| {
                self.colors[i % self.colors.len()]
            });

            // 生成梯形路径
            let trapezoid_commands = self.generate_trapezoid_path(node);

            commands.push(DrawCommand::Path {
                commands: trapezoid_commands,
                style: PathStyle {
                    fill: Some(color),
                    stroke: Some(LineStyle {
                        color: self.border_color,
                        width: self.border_width,
                        dash_pattern: None,
                        cap: LineCap::Round,
                        join: LineJoin::Round,
                        opacity: 1.0,
                    }),
                    opacity: 0.8,
                    fill_rule: echarts_core::draw_commands::FillRule::NonZero,
                },
            });

            // 生成标签
            if self.label.show {
                let label_text = node.item.label.as_ref().unwrap_or(&node.item.name);
                let label_position = self.calculate_label_position(node);

                commands.push(DrawCommand::Text {
                    text: self.format_label(label_text, node.item.value),
                    position: label_position,
                    style: TextStyle {
                        font_family: "Arial".to_string(),
                        font_size: self.label.font_size,
                        font_weight: FontWeight::Normal,
                        font_style: FontStyle::Normal,
                        color: self.label.color,
                        opacity: 1.0,
                        align: TextAlign::Center,
                        baseline: TextBaseline::Middle,
                        rotation: 0.0,
                        letter_spacing: 0.0,
                        line_height: 1.2,
                    },
                });
            }
        }

        commands
    }

    /// 生成梯形路径
    fn generate_trapezoid_path(&self, node: &FunnelNode) -> Vec<PathCommand> {
        let mut commands = Vec::new();

        // 计算梯形的四个顶点
        let top_left = Point {
            x: node.left_x,
            y: node.y,
        };

        let top_right = Point {
            x: node.right_x,
            y: node.y,
        };

        // 计算底部宽度的中心对齐偏移
        let width_diff = node.top_width - node.bottom_width;
        let bottom_offset = width_diff / 2.0;

        let bottom_left = Point {
            x: node.left_x + bottom_offset,
            y: node.y + node.height,
        };

        let bottom_right = Point {
            x: node.right_x - bottom_offset,
            y: node.y + node.height,
        };

        // 构建梯形路径
        commands.push(PathCommand::MoveTo(top_left));
        commands.push(PathCommand::LineTo(top_right));
        commands.push(PathCommand::LineTo(bottom_right));
        commands.push(PathCommand::LineTo(bottom_left));
        commands.push(PathCommand::Close);

        commands
    }

    /// 计算标签位置
    fn calculate_label_position(&self, node: &FunnelNode) -> Point {
        let center_x = (node.left_x + node.right_x) / 2.0;
        let center_y = node.y + node.height / 2.0;

        match self.label.position {
            FunnelLabelPosition::Inside => Point {
                x: center_x,
                y: center_y,
            },
            FunnelLabelPosition::Outside => Point {
                x: center_x,
                y: center_y - 20.0, // 向上偏移
            },
            FunnelLabelPosition::Left => Point {
                x: node.left_x - 10.0,
                y: center_y,
            },
            FunnelLabelPosition::Right => Point {
                x: node.right_x + 10.0,
                y: center_y,
            },
        }
    }

    /// 格式化标签文本
    fn format_label(&self, name: &str, value: f64) -> String {
        if let Some(formatter) = &self.label.formatter {
            // 简单的格式化支持
            formatter
                .replace("{name}", name)
                .replace("{value}", &value.to_string())
                .replace("{percent}", &format!("{:.1}%", value)) // 假设为百分比
        } else {
            format!("{}: {}", name, value)
        }
    }
}

impl Series for FunnelSeries {
    fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        if self.data.is_empty() {
            return Ok(commands);
        }

        // 获取绘图区域
        let bounds = coord_system.bounds();

        // 计算布局
        let nodes = self.calculate_layout(bounds);

        // 生成梯形和标签
        commands.extend(self.generate_trapezoids_and_labels(&nodes));

        Ok(commands)
    }

    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Funnel
    }

    fn bounds(&self) -> Option<Bounds> {
        // 漏斗图使用整个可用空间
        None
    }

    fn is_visible(&self) -> bool {
        true
    }

    fn z_index(&self) -> i32 {
        0
    }

    fn clone_series(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use echarts_core::{CartesianCoordinateSystem, Point, Size};

    #[test]
    fn test_funnel_data_item() {
        let item = FunnelDataItem::new("访问", 1000.0)
            .color(Color::rgb(1.0, 0.0, 0.0))
            .label("访问用户")
            .visible(true);

        assert_eq!(item.name, "访问");
        assert_eq!(item.value, 1000.0);
        assert_eq!(item.color, Some(Color::rgb(1.0, 0.0, 0.0)));
        assert_eq!(item.label, Some("访问用户".to_string()));
        assert!(item.visible);
    }

    #[test]
    fn test_funnel_series_creation() {
        let series = FunnelSeries::new("销售漏斗")
            .position(0.1, 0.1, 0.8, 0.8)
            .min_size(0.1)
            .max_size(0.9)
            .sort(FunnelSort::Descending)
            .align(FunnelAlign::Center)
            .gap(5.0);

        assert_eq!(series.name(), "销售漏斗");
        assert_eq!(series.position, (0.1, 0.1, 0.8, 0.8));
        assert_eq!(series.min_size, 0.1);
        assert_eq!(series.max_size, 0.9);
        assert!(matches!(series.sort, FunnelSort::Descending));
        assert!(matches!(series.align, FunnelAlign::Center));
        assert_eq!(series.gap, 5.0);
    }

    #[test]
    fn test_funnel_label_config() {
        let label = FunnelLabel {
            show: true,
            font_size: 14.0,
            color: Color::rgb(0.1, 0.1, 0.1),
            position: FunnelLabelPosition::Outside,
            formatter: Some("{name}: {value}".to_string()),
        };

        assert!(label.show);
        assert_eq!(label.font_size, 14.0);
        assert_eq!(label.color, Color::rgb(0.1, 0.1, 0.1));
        assert!(matches!(label.position, FunnelLabelPosition::Outside));
        assert_eq!(label.formatter, Some("{name}: {value}".to_string()));
    }

    #[test]
    fn test_funnel_series_with_data() {
        let data = vec![
            FunnelDataItem::new("访问", 1000.0),
            FunnelDataItem::new("点击", 800.0),
            FunnelDataItem::new("咨询", 600.0),
            FunnelDataItem::new("订单", 300.0),
            FunnelDataItem::new("支付", 200.0),
        ];

        let series = FunnelSeries::new("转化漏斗")
            .data(data)
            .sort(FunnelSort::Descending);

        assert_eq!(series.data.len(), 5);
        assert_eq!(series.data[0].name, "访问");
        assert_eq!(series.data[0].value, 1000.0);
        assert!(matches!(series.sort, FunnelSort::Descending));
    }

    #[test]
    fn test_funnel_series_rendering() {
        let data = vec![
            FunnelDataItem::new("阶段1", 100.0),
            FunnelDataItem::new("阶段2", 80.0),
            FunnelDataItem::new("阶段3", 60.0),
            FunnelDataItem::new("阶段4", 40.0),
        ];

        let series = FunnelSeries::new("流程漏斗")
            .data(data)
            .position(0.1, 0.1, 0.8, 0.8);

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 400.0, height: 400.0 },
            },
            (0.0, 100.0),
            (0.0, 100.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();

        // 应该生成梯形和标签命令
        assert!(!commands.is_empty());

        // 检查命令类型
        let mut path_count = 0;
        let mut text_count = 0;

        for cmd in &commands {
            match cmd {
                DrawCommand::Path { .. } => path_count += 1,
                DrawCommand::Text { .. } => text_count += 1,
                _ => {}
            }
        }

        assert!(path_count > 0); // 梯形路径
        assert!(text_count > 0); // 标签
    }

    #[test]
    fn test_funnel_series_empty_data() {
        let series = FunnelSeries::new("空漏斗图");

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 400.0, height: 400.0 },
            },
            (0.0, 100.0),
            (0.0, 100.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();
        assert!(commands.is_empty());
    }

    #[test]
    fn test_label_formatting() {
        let series = FunnelSeries::new("测试");

        // 测试默认格式化
        let formatted = series.format_label("测试", 100.0);
        assert_eq!(formatted, "测试: 100");

        // 测试自定义格式化
        let mut series_with_formatter = series.clone();
        series_with_formatter.label.formatter = Some("{name} ({value})".to_string());
        let formatted = series_with_formatter.format_label("测试", 100.0);
        assert_eq!(formatted, "测试 (100)");
    }

    #[test]
    fn test_funnel_sort_modes() {
        let data = vec![
            FunnelDataItem::new("C", 30.0),
            FunnelDataItem::new("A", 10.0),
            FunnelDataItem::new("B", 20.0),
        ];

        // 测试降序排序
        let series_desc = FunnelSeries::new("降序")
            .data(data.clone())
            .sort(FunnelSort::Descending);

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 400.0, height: 400.0 },
            },
            (0.0, 100.0),
            (0.0, 100.0),
        );

        let nodes_desc = series_desc.calculate_layout(coord_system.bounds());
        assert_eq!(nodes_desc[0].item.value, 30.0); // C应该在第一位
        assert_eq!(nodes_desc[1].item.value, 20.0); // B应该在第二位
        assert_eq!(nodes_desc[2].item.value, 10.0); // A应该在第三位

        // 测试升序排序
        let series_asc = FunnelSeries::new("升序")
            .data(data.clone())
            .sort(FunnelSort::Ascending);

        let nodes_asc = series_asc.calculate_layout(coord_system.bounds());
        assert_eq!(nodes_asc[0].item.value, 10.0); // A应该在第一位
        assert_eq!(nodes_asc[1].item.value, 20.0); // B应该在第二位
        assert_eq!(nodes_asc[2].item.value, 30.0); // C应该在第三位
    }

    #[test]
    fn test_funnel_alignment() {
        let data = vec![
            FunnelDataItem::new("大", 100.0),
            FunnelDataItem::new("小", 50.0),
        ];

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 400.0, height: 400.0 },
            },
            (0.0, 100.0),
            (0.0, 100.0),
        );

        // 测试居中对齐
        let series_center = FunnelSeries::new("居中")
            .data(data.clone())
            .align(FunnelAlign::Center);

        let nodes_center = series_center.calculate_layout(coord_system.bounds());
        let center_x = (nodes_center[0].left_x + nodes_center[0].right_x) / 2.0;
        let expected_center = 40.0 + 320.0 / 2.0; // 漏斗区域的中心
        assert!((center_x - expected_center).abs() < 1.0);

        // 测试左对齐
        let series_left = FunnelSeries::new("左对齐")
            .data(data.clone())
            .align(FunnelAlign::Left);

        let nodes_left = series_left.calculate_layout(coord_system.bounds());
        assert_eq!(nodes_left[0].left_x, 40.0); // 应该从左边界开始
    }
}
// ============================================================================
// 图表基础架构集成 - ChartBase 实现
// ============================================================================

/// 为 FunnelSeries 实现 ChartBase trait
impl ChartBase for FunnelSeries {
    type DataType = Vec<FunnelDataItem>;

    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn raw_data(&self) -> &Self::DataType {
        &self.data
    }

    fn to_dataset(&self) -> DataSet {
        // TODO: 实现 Vec<FunnelDataItem> 到 DataSet 的转换
        DataSet::new()
    }
    
    fn visible(&self) -> bool {
        self.config.visible
    }
    
    fn z_index(&self) -> i32 {
        self.config.z_index
    }
    
    fn bounds(&self) -> Option<Bounds> {
        // TODO: 为 FunnelSeries 实现边界计算
        None
    }
    
    fn config(&self) -> &ChartConfig {
        &self.config
    }
    
    fn config_mut(&mut self) -> &mut ChartConfig {
        &mut self.config
    }
}

/// 为 FunnelSeries 实现 ChartSeries trait
impl ChartSeries for FunnelSeries {
    // 所有方法都由默认实现提供，基于 ChartBase
}
