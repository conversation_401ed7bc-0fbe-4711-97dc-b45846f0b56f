//! Charts 模块 - 重构版本
//!
//! 基于新的 Core 架构的简化图表实现

// 重新导出 Core 中的核心类型
pub use echarts_core::{
    Bounds, Color, CoordinateSystem, DataSet, DataValue, DrawCommand, Point, Result, Series, SeriesType,
    CartesianCoordinateSystem,
};

// 导出图表类型
pub mod base;
pub mod line;
pub mod bar;
pub mod enhanced_bar;
pub mod scatter;
pub mod pie;
pub mod radar;
pub mod gauge;
pub mod treemap;
pub mod sunburst;
pub mod funnel;
pub mod candlestick;
pub mod heatmap;
pub mod surface3d;

// 重新导出基础架构
pub use base::{
    ChartBase, ChartSeries, ChartConfig, AnimationConfig, InteractionConfig,
    EasingFunction, BoundsCalculator,
};

pub use line::{LineSeries, StepType, LineOptimizationAlgorithm};
pub use bar::BarSeries;
pub use scatter::{ScatterSeries, SymbolType};
pub use pie::{PieSeries, PieRadius, RoseType, PieLabelPosition, SelectedMode};
pub use radar::{RadarSeries, RadarDataItem, RadarIndicator};
pub use gauge::{GaugeSeries, GaugeDataItem, PointerStyle, AxisTick, AxisLabel};
pub use treemap::{TreemapSeries, TreemapDataItem, TreemapAlgorithm, TreemapLabel, TreemapLabelPosition};
pub use sunburst::{SunburstSeries, SunburstDataItem, SunburstLabel, SunburstLabelPosition};
pub use funnel::{FunnelSeries, FunnelDataItem, FunnelLabel, FunnelLabelPosition, FunnelSort, FunnelAlign};
pub use candlestick::{CandlestickSeries, CandlestickDataItem, CandlestickStyle, CandlestickLabel, CandlestickLabelPosition, CandlestickLabelContent};
pub use heatmap::{HeatmapSeries, ColorMap, ColorMapType, HeatmapLabel};
pub use surface3d::{Surface3DSeries, Point3D, SimpleLighting};

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_line_series_integration() {
        let series = LineSeries::new("Test Line")
            .data(vec![(0.0, 1.0), (1.0, 2.0), (2.0, 3.0)])
            .color(Color::rgb(1.0, 0.0, 0.0));

        assert_eq!(series.name(), "Test Line");
        assert_eq!(series.series_type(), SeriesType::Line);
        assert!(series.is_visible());
    }

    #[test]
    fn test_bar_series_integration() {
        let series = BarSeries::new("Test Bar")
            .data(vec![(0.0, 10.0), (1.0, 20.0), (2.0, 15.0)])
            .color(Color::rgb(0.0, 1.0, 0.0));

        assert_eq!(series.name(), "Test Bar");
        assert_eq!(series.series_type(), SeriesType::Bar);
        assert!(series.is_visible());
    }

    #[test]
    fn test_scatter_series_integration() {
        let series = ScatterSeries::new("Test Scatter")
            .data(vec![(1.0, 2.0), (3.0, 4.0), (5.0, 6.0)])
            .color(Color::rgb(0.0, 0.0, 1.0));

        assert_eq!(series.name(), "Test Scatter");
        assert_eq!(series.series_type(), SeriesType::Scatter);
        assert!(series.is_visible());
    }

    #[test]
    fn test_series_render_to_commands() {
        let line_series = LineSeries::new("Test")
            .data(vec![(0.0, 1.0), (1.0, 2.0)]);

        let coord_system = CartesianCoordinateSystem::new(
            Bounds::new(0.0, 0.0, 100.0, 100.0),
            (0.0, 1.0),
            (1.0, 2.0),
        );

        let commands = line_series.render_to_commands(&coord_system).unwrap();
        assert!(!commands.is_empty(), "应该生成绘制命令");
    }

    #[test]
    fn test_series_bounds() {
        let series = LineSeries::new("Test")
            .data(vec![(1.0, 2.0), (3.0, 4.0), (5.0, 6.0)]);

        let bounds = series.bounds().unwrap();
        assert_eq!(bounds.origin.x, 1.0);
        assert_eq!(bounds.origin.y, 2.0);
        assert_eq!(bounds.size.width, 4.0);
        assert_eq!(bounds.size.height, 4.0);
    }

    #[test]
    fn test_series_trait_object() {
        // 测试类型擦除 - 这是重构的核心目标
        let series1: Box<dyn Series> = Box::new(LineSeries::new("Line").data(vec![(0.0, 1.0)]));
        let series2: Box<dyn Series> = Box::new(BarSeries::new("Bar").data(vec![(0.0, 2.0)]));
        let series3: Box<dyn Series> = Box::new(ScatterSeries::new("Scatter").data(vec![(0.0, 3.0)]));

        let series_list = vec![series1, series2, series3];

        for series in &series_list {
            assert!(!series.name().is_empty());
            assert!(series.is_visible());
            assert!(series.bounds().is_some());
        }

        println!("✅ 类型擦除测试通过 - Series trait 对象正常工作");
    }
}
