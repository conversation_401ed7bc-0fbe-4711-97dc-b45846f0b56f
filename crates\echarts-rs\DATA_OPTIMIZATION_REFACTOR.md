# 数据优化架构重构方案

## 🔍 **问题分析**

### 重复代码问题
1. **OptimizationAlgorithm 枚举重复定义**
   - `core/optimization.rs`: 5种算法 (LTTB, Uniform, MinMax, Adaptive, PixelBased)
   - `charts/line.rs`: 2种算法 (LTTB, Douglas)

2. **算法实现重复**
   - Core模块: 完整的优化器实现
   - Line模块: 内嵌的优化方法

3. **配置管理分散**
   - Core: `OptimizationConfig`, `DataOptimizer`
   - Line: 内嵌优化配置字段

## 🏗️ **统一架构方案**

### 方案：集中式数据优化架构

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
│                   图表使用优化功能                           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                图表层 (Chart Layer)                         │
│              (line.rs, bar.rs, scatter.rs等)               │
│  • 使用统一的优化接口                                       │
│  • 不再内嵌优化实现                                         │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│              优化适配层 (Optimization Adapter)              │
│                   optimization_adapter.rs                   │
│  • 图表特定的优化策略                                       │
│  • 算法选择和参数调优                                       │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│               核心优化引擎 (Core Optimization)               │
│                   core/optimization.rs                      │
│  • 统一的算法实现                                           │
│  • 性能监控和统计                                           │
│  • 扩展的算法支持                                           │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 **重构步骤**

### Step 1: 扩展核心优化算法
```rust
// core/optimization.rs
pub enum OptimizationAlgorithm {
    // 现有算法
    LTTB,
    Uniform,
    MinMax,
    Adaptive,
    PixelBased,
    
    // 新增算法 (从 line.rs 迁移)
    DouglasPeucker,
    
    // 未来扩展
    Visvalingam,
    RamerDouglasPeucker,
    Custom(Box<dyn OptimizationStrategy>),
}
```

### Step 2: 创建优化适配层
```rust
// optimization_adapter.rs
pub trait ChartOptimization {
    fn get_optimization_config(&self) -> OptimizationConfig;
    fn apply_optimization(&mut self, optimizer: &mut DataOptimizer) -> Result<()>;
    fn should_optimize(&self) -> bool;
}

pub struct OptimizationAdapter {
    core_optimizer: DataOptimizer,
    chart_strategies: HashMap<String, Box<dyn ChartOptimization>>,
}
```

### Step 3: 重构图表实现
```rust
// charts/line.rs
impl ChartOptimization for LineSeries {
    fn get_optimization_config(&self) -> OptimizationConfig {
        OptimizationConfig {
            target_points: self.optimization_target_points,
            algorithm: match self.optimization_algorithm {
                LineOptimizationAlgorithm::LTTB => OptimizationAlgorithm::LTTB,
                LineOptimizationAlgorithm::Douglas => OptimizationAlgorithm::DouglasPeucker,
            },
            ..Default::default()
        }
    }
}
```

## ✅ **重构优势**

### 1. **消除代码重复**
- 统一的算法定义和实现
- 避免维护多个版本的相同算法
- 减少代码量和复杂度

### 2. **提高可扩展性**
- 新算法只需在核心模块添加
- 所有图表类型自动受益
- 支持自定义优化策略

### 3. **增强性能监控**
- 统一的性能统计
- 跨图表类型的优化分析
- 更好的调试和优化能力

### 4. **简化图表实现**
- 图表专注于渲染逻辑
- 优化逻辑集中管理
- 更清晰的职责分离

## 🚀 **实施计划**

### Phase 1: 扩展核心优化引擎 ✅
- 添加 DouglasPeucker 算法到 core/optimization.rs
- 扩展 OptimizationAlgorithm 枚举
- 实现算法参数配置

### Phase 2: 创建优化适配层 🔄
- 实现 optimization_adapter.rs
- 定义 ChartOptimization trait
- 创建适配器管理器

### Phase 3: 重构图表实现 📊
- 更新 line.rs 使用统一接口
- 移除重复的优化代码
- 实现 ChartOptimization trait

### Phase 4: 扩展到其他图表 🎮
- 更新 bar.rs, scatter.rs 等
- 统一优化配置
- 性能测试和优化

## 📝 **迁移指南**

### 现有代码迁移
1. **保持向后兼容**: 现有API继续工作
2. **渐进式迁移**: 逐步采用新架构
3. **性能验证**: 确保优化效果不降低

### 新功能开发
1. **使用统一接口**: 实现 ChartOptimization trait
2. **利用核心算法**: 不要重复实现优化算法
3. **考虑扩展性**: 设计可复用的优化策略

## 🎊 **预期成果**

### 代码质量提升
- **减少重复代码**: 50%+ 的优化相关代码重复消除
- **提高可维护性**: 统一的算法实现和配置
- **增强测试覆盖**: 集中的测试和验证

### 性能优化
- **更好的算法选择**: 基于数据特征自动选择最优算法
- **统一的性能监控**: 跨图表类型的性能分析
- **优化参数调优**: 基于统计数据的自动调优

### 开发体验
- **简化图表开发**: 专注于渲染逻辑，优化自动处理
- **一致的API**: 所有图表类型使用相同的优化接口
- **更好的文档**: 集中的优化功能文档

---

**这个统一架构将使 ECharts-rs 的数据优化更加专业、高效和可维护！** 🎉
