//! Coordinate system implementations

use crate::{<PERSON><PERSON>, <PERSON><PERSON><PERSON>r, <PERSON>Value, Point};
use serde::{Deserialize, Serialize};

/// Trait for coordinate systems
pub trait CoordinateSystem {
    /// Convert data values to screen coordinates
    fn data_to_point(&self, data: &[DataValue]) -> Result<Point, ChartError>;

    /// Convert screen coordinates to data values
    fn point_to_data(&self, point: Point) -> Result<Vec<DataValue>, ChartError>;

    /// Check if a point is within the coordinate system bounds
    fn contains_point(&self, point: Point) -> bool;

    /// Get the bounds of this coordinate system
    fn bounds(&self) -> Bounds;

    /// Get the number of dimensions this coordinate system supports
    fn dimensions(&self) -> usize;

    /// Update the coordinate system with new bounds
    fn update_bounds(&mut self, bounds: Bounds);
}

/// Cartesian (rectangular) coordinate system
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CartesianCoord {
    /// Bounds of the coordinate system
    pub bounds: Bounds,

    /// X-axis configuration
    pub x_axis: AxisConfig,

    /// Y-axis configuration  
    pub y_axis: AxisConfig,

    /// Whether to invert the Y axis (useful for some chart types)
    pub invert_y: bool,
}

impl CartesianCoord {
    pub fn new(x_axis: AxisConfig, y_axis: AxisConfig) -> Self {
        Self {
            bounds: Bounds::new(0.0, 0.0, 800.0, 600.0), // 默认大小
            x_axis,
            y_axis,
            invert_y: false,
        }
    }

    pub fn with_bounds(mut self, bounds: Bounds) -> Self {
        self.bounds = bounds;
        self
    }

    pub fn with_inverted_y(mut self, invert: bool) -> Self {
        self.invert_y = invert;
        self
    }

    /// 简化的数据到屏幕坐标转换方法
    pub fn data_to_screen(&self, x: f64, y: f64) -> Point {
        let screen_x = self
            .x_axis
            .value_to_pixel(x, self.bounds.origin.x, self.bounds.size.width);
        let screen_y = if self.invert_y {
            self.y_axis
                .value_to_pixel(y, self.bounds.origin.y, self.bounds.size.height)
        } else {
            self.bounds.origin.y + self.bounds.size.height
                - self.y_axis.value_to_pixel(y, 0.0, self.bounds.size.height)
        };

        Point::new(screen_x, screen_y)
    }
}

impl CoordinateSystem for CartesianCoord {
    fn data_to_point(&self, data: &[DataValue]) -> Result<Point, ChartError> {
        if data.len() < 2 {
            return Err(ChartError::InvalidData(
                "Cartesian coordinates require at least 2 dimensions".into(),
            ));
        }

        let x_val = data[0]
            .as_number()
            .ok_or_else(|| ChartError::InvalidData("X value must be numeric".into()))?;
        let y_val = data[1]
            .as_number()
            .ok_or_else(|| ChartError::InvalidData("Y value must be numeric".into()))?;

        let x = self
            .x_axis
            .value_to_pixel(x_val, self.bounds.origin.x, self.bounds.size.width);
        let y = if self.invert_y {
            self.y_axis
                .value_to_pixel(y_val, self.bounds.origin.y, self.bounds.size.height)
        } else {
            self.bounds.origin.y + self.bounds.size.height
                - self
                    .y_axis
                    .value_to_pixel(y_val, 0.0, self.bounds.size.height)
        };

        Ok(Point::new(x, y))
    }

    fn point_to_data(&self, point: Point) -> Result<Vec<DataValue>, ChartError> {
        let x_val = self
            .x_axis
            .pixel_to_value(point.x - self.bounds.origin.x, self.bounds.size.width);

        let y_pixel = if self.invert_y {
            point.y - self.bounds.origin.y
        } else {
            self.bounds.origin.y + self.bounds.size.height - point.y
        };

        let y_val = self.y_axis.pixel_to_value(y_pixel, self.bounds.size.height);

        Ok(vec![DataValue::Number(x_val), DataValue::Number(y_val)])
    }

    fn contains_point(&self, point: Point) -> bool {
        self.bounds.contains_point(point)
    }

    fn bounds(&self) -> Bounds {
        self.bounds
    }

    fn dimensions(&self) -> usize {
        2
    }

    fn update_bounds(&mut self, bounds: Bounds) {
        self.bounds = bounds;
    }
}

/// Polar coordinate system
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PolarCoord {
    /// Center point of the polar coordinate system
    pub center: Point,

    /// Radius of the polar coordinate system
    pub radius: f64,

    /// Angle axis configuration (theta)
    pub angle_axis: AxisConfig,

    /// Radius axis configuration (r)
    pub radius_axis: AxisConfig,

    /// Starting angle in radians (0 = right, π/2 = top)
    pub start_angle: f64,

    /// Whether angles increase clockwise
    pub clockwise: bool,
}

impl PolarCoord {
    pub fn new(
        center: Point,
        radius: f64,
        angle_axis: AxisConfig,
        radius_axis: AxisConfig,
    ) -> Self {
        Self {
            center,
            radius,
            angle_axis,
            radius_axis,
            start_angle: 0.0,
            clockwise: true,
        }
    }

    pub fn with_start_angle(mut self, angle: f64) -> Self {
        self.start_angle = angle;
        self
    }

    pub fn with_clockwise(mut self, clockwise: bool) -> Self {
        self.clockwise = clockwise;
        self
    }
}

impl CoordinateSystem for PolarCoord {
    fn data_to_point(&self, data: &[DataValue]) -> Result<Point, ChartError> {
        if data.len() < 2 {
            return Err(ChartError::InvalidData(
                "Polar coordinates require at least 2 dimensions".into(),
            ));
        }

        let angle_val = data[0]
            .as_number()
            .ok_or_else(|| ChartError::InvalidData("Angle value must be numeric".into()))?;
        let radius_val = data[1]
            .as_number()
            .ok_or_else(|| ChartError::InvalidData("Radius value must be numeric".into()))?;

        // Convert angle to radians
        let angle_normalized =
            self.angle_axis
                .value_to_pixel(angle_val, 0.0, 2.0 * std::f64::consts::PI);
        let angle = self.start_angle
            + if self.clockwise {
                angle_normalized
            } else {
                -angle_normalized
            };

        // Convert radius
        let r = self
            .radius_axis
            .value_to_pixel(radius_val, 0.0, self.radius);

        // Convert to Cartesian coordinates
        let x = self.center.x + r * angle.cos();
        let y = self.center.y + r * angle.sin();

        Ok(Point::new(x, y))
    }

    fn point_to_data(&self, point: Point) -> Result<Vec<DataValue>, ChartError> {
        let dx = point.x - self.center.x;
        let dy = point.y - self.center.y;

        let r = (dx * dx + dy * dy).sqrt();
        let mut angle = dy.atan2(dx) - self.start_angle;

        if !self.clockwise {
            angle = -angle;
        }

        // Normalize angle to [0, 2π]
        while angle < 0.0 {
            angle += 2.0 * std::f64::consts::PI;
        }
        while angle >= 2.0 * std::f64::consts::PI {
            angle -= 2.0 * std::f64::consts::PI;
        }

        let angle_val = self
            .angle_axis
            .pixel_to_value(angle, 2.0 * std::f64::consts::PI);
        let radius_val = self.radius_axis.pixel_to_value(r, self.radius);

        Ok(vec![
            DataValue::Number(angle_val),
            DataValue::Number(radius_val),
        ])
    }

    fn contains_point(&self, point: Point) -> bool {
        let dx = point.x - self.center.x;
        let dy = point.y - self.center.y;
        let distance = (dx * dx + dy * dy).sqrt();
        distance <= self.radius
    }

    fn bounds(&self) -> Bounds {
        Bounds::new(
            self.center.x - self.radius,
            self.center.y - self.radius,
            2.0 * self.radius,
            2.0 * self.radius,
        )
    }

    fn dimensions(&self) -> usize {
        2
    }

    fn update_bounds(&mut self, bounds: Bounds) {
        self.center = bounds.center();
        self.radius = (bounds.size.width.min(bounds.size.height)) / 2.0;
    }
}

/// Axis configuration for coordinate systems
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AxisConfig {
    /// Minimum value
    pub min: f64,

    /// Maximum value
    pub max: f64,

    /// Axis type
    pub axis_type: AxisType,

    /// Whether the axis is inverted
    pub inverse: bool,
}

impl AxisConfig {
    pub fn new(min: f64, max: f64, axis_type: AxisType) -> Self {
        Self {
            min,
            max,
            axis_type,
            inverse: false,
        }
    }

    pub fn linear(min: f64, max: f64) -> Self {
        Self::new(min, max, AxisType::Linear)
    }

    pub fn log(min: f64, max: f64, base: f64) -> Self {
        Self::new(min, max, AxisType::Log { base })
    }

    pub fn category(categories: Vec<String>) -> Self {
        Self {
            min: 0.0,
            max: categories.len() as f64 - 1.0,
            axis_type: AxisType::Category { categories },
            inverse: false,
        }
    }

    pub fn with_inverse(mut self, inverse: bool) -> Self {
        self.inverse = inverse;
        self
    }

    /// Convert a data value to pixel position
    pub fn value_to_pixel(&self, value: f64, start: f64, length: f64) -> f64 {
        let normalized = match &self.axis_type {
            AxisType::Linear => {
                if self.max == self.min {
                    0.5
                } else {
                    (value - self.min) / (self.max - self.min)
                }
            }
            AxisType::Log { base } => {
                if self.max == self.min || *base <= 1.0 {
                    0.5
                } else {
                    (value.log(*base) - self.min.log(*base))
                        / (self.max.log(*base) - self.min.log(*base))
                }
            }
            AxisType::Category { .. } => {
                if self.max == self.min {
                    0.5
                } else {
                    (value - self.min) / (self.max - self.min)
                }
            }
        };

        let normalized = if self.inverse {
            1.0 - normalized
        } else {
            normalized
        };
        start + normalized * length
    }

    /// Convert pixel position to data value
    pub fn pixel_to_value(&self, pixel: f64, length: f64) -> f64 {
        let normalized = if length == 0.0 { 0.5 } else { pixel / length };
        let normalized = if self.inverse {
            1.0 - normalized
        } else {
            normalized
        };

        match &self.axis_type {
            AxisType::Linear => self.min + normalized * (self.max - self.min),
            AxisType::Log { base } => {
                let log_min = self.min.log(*base);
                let log_max = self.max.log(*base);
                base.powf(log_min + normalized * (log_max - log_min))
            }
            AxisType::Category { .. } => self.min + normalized * (self.max - self.min),
        }
    }
}

impl Default for AxisConfig {
    fn default() -> Self {
        Self::linear(0.0, 1.0)
    }
}

/// Types of axes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AxisType {
    /// Linear scale
    Linear,

    /// Logarithmic scale
    Log { base: f64 },

    /// Categorical scale
    Category { categories: Vec<String> },
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cartesian_coord() {
        let bounds = Bounds::new(0.0, 0.0, 100.0, 100.0);
        let x_axis = AxisConfig::linear(0.0, 10.0);
        let y_axis = AxisConfig::linear(0.0, 10.0);

        let coord = CartesianCoord::new(x_axis, y_axis).with_bounds(bounds);

        // Test data to point conversion
        let data = vec![DataValue::Number(5.0), DataValue::Number(5.0)];
        let point = coord.data_to_point(&data).unwrap();

        assert_eq!(point.x, 50.0);
        assert_eq!(point.y, 50.0);

        // Test point to data conversion
        let back_data = coord.point_to_data(point).unwrap();
        assert!((back_data[0].as_number().unwrap() - 5.0).abs() < 0.001);
        assert!((back_data[1].as_number().unwrap() - 5.0).abs() < 0.001);
    }

    #[test]
    fn test_polar_coord() {
        let center = Point::new(50.0, 50.0);
        let radius = 50.0;
        let angle_axis = AxisConfig::linear(0.0, 360.0);
        let radius_axis = AxisConfig::linear(0.0, 10.0);

        let coord = PolarCoord::new(center, radius, angle_axis, radius_axis);

        // Test data to point conversion (0 degrees, radius 5)
        let data = vec![DataValue::Number(0.0), DataValue::Number(5.0)];
        let point = coord.data_to_point(&data).unwrap();

        // Should be at (75, 50) for 0 degrees, half radius
        assert!((point.x - 75.0).abs() < 0.001);
        assert!((point.y - 50.0).abs() < 0.001);
    }
}
