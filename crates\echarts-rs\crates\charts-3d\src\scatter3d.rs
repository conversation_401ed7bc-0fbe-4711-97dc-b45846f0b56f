//! 3D scatter plot implementation

use crate::*;
use echarts_core::*;
use echarts_charts::*;
use serde::{Deserialize, Serialize};

/// 3D scatter plot series
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Scatter3DSeries {
    /// Series name
    pub name: String,
    /// Data points
    pub data: Vec<Scatter3DPoint>,
    /// Symbol configuration
    pub symbol: Symbol3D,
    /// Material properties
    pub material: Material3D,
    /// Animation configuration
    pub animation: Option<Animation>,
    /// Whether the series is visible
    pub visible: bool,
    /// Z-index for rendering order
    pub z_index: i32,
}

/// 3D scatter plot data point
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Scatter3DPoint {
    /// 3D position
    pub position: Point3D,
    /// Point value (for color mapping, size, etc.)
    pub value: Option<f64>,
    /// Point color override
    pub color: Option<Color>,
    /// Point size override
    pub size: Option<f64>,
    /// Point label
    pub label: Option<String>,
    /// Custom data
    pub custom_data: std::collections::HashMap<String, serde_json::Value>,
}

/// 3D symbol configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Symbol3D {
    /// Symbol type
    pub symbol_type: Symbol3DType,
    /// Symbol size
    pub size: SymbolSize3D,
    /// Symbol color
    pub color: Color,
    /// Symbol opacity
    pub opacity: f64,
    /// Whether to show symbol outline
    pub show_outline: bool,
    /// Outline color
    pub outline_color: Color,
    /// Outline width
    pub outline_width: f64,
}

/// 3D symbol types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum Symbol3DType {
    Sphere,
    Cube,
    Cylinder,
    Cone,
    Pyramid,
    Octahedron,
    Tetrahedron,
    Torus,
    Custom,
}

/// 3D symbol size configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SymbolSize3D {
    /// Fixed size for all symbols
    Fixed(f64),
    /// Size based on data value
    ValueBased {
        min_size: f64,
        max_size: f64,
        value_range: (f64, f64),
    },
    /// Custom size function
    Custom(String), // Expression or function name
}

/// 3D scatter plot configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Scatter3DConfig {
    /// Coordinate system
    pub coordinate: Coordinate3D,
    /// Color mapping
    pub color_map: ColorMap3D,
    /// Size mapping
    pub size_map: SizeMap3D,
    /// Interaction settings
    pub interaction: Interaction3D,
    /// Performance settings
    pub performance: Performance3D,
}

/// 3D color mapping
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ColorMap3D {
    /// Color mapping type
    pub map_type: ColorMapType,
    /// Color palette
    pub palette: Vec<Color>,
    /// Value range for color mapping
    pub value_range: Option<(f64, f64)>,
    /// Whether to show color legend
    pub show_legend: bool,
}

/// Color mapping types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ColorMapType {
    /// No color mapping
    None,
    /// Map by value
    Value,
    /// Map by category
    Category,
    /// Map by position (X, Y, or Z coordinate)
    Position(PositionAxis),
}

/// Position axis for color mapping
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PositionAxis {
    X,
    Y,
    Z,
}

/// 3D size mapping
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SizeMap3D {
    /// Size mapping type
    pub map_type: SizeMapType,
    /// Size range
    pub size_range: (f64, f64),
    /// Value range for size mapping
    pub value_range: Option<(f64, f64)>,
}

/// Size mapping types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SizeMapType {
    /// No size mapping
    None,
    /// Map by value
    Value,
    /// Map by position
    Position(PositionAxis),
}

/// 3D interaction settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Interaction3D {
    /// Enable rotation
    pub enable_rotation: bool,
    /// Enable zoom
    pub enable_zoom: bool,
    /// Enable pan
    pub enable_pan: bool,
    /// Auto rotation
    pub auto_rotation: Option<AutoRotation3D>,
    /// Selection mode
    pub selection_mode: SelectionMode3D,
    /// Hover effects
    pub hover_effects: HoverEffects3D,
}

/// Auto rotation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoRotation3D {
    /// Rotation speed (degrees per second)
    pub speed: f64,
    /// Rotation axis
    pub axis: Vector3D,
    /// Whether to pause on hover
    pub pause_on_hover: bool,
}

/// Selection modes
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SelectionMode3D {
    None,
    Single,
    Multiple,
    Brush,
}

/// Hover effects
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HoverEffects3D {
    /// Enable hover highlighting
    pub enable_highlight: bool,
    /// Highlight color
    pub highlight_color: Color,
    /// Highlight scale factor
    pub highlight_scale: f64,
    /// Show tooltip on hover
    pub show_tooltip: bool,
    /// Tooltip configuration
    pub tooltip: Option<Tooltip3D>,
}

/// 3D tooltip
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Tooltip3D {
    /// Tooltip content template
    pub template: String,
    /// Tooltip style
    pub style: TooltipStyle3D,
    /// Follow cursor
    pub follow_cursor: bool,
}

/// 3D tooltip style
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TooltipStyle3D {
    /// Background color
    pub background_color: Color,
    /// Text color
    pub text_color: Color,
    /// Font size
    pub font_size: f64,
    /// Padding
    pub padding: f64,
    /// Border radius
    pub border_radius: f64,
    /// Shadow
    pub shadow: bool,
}

/// 3D performance settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Performance3D {
    /// Level of detail (LOD) settings
    pub lod: LevelOfDetail3D,
    /// Instancing settings
    pub instancing: Instancing3D,
    /// Culling settings
    pub culling: Culling3D,
}

/// Level of detail settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LevelOfDetail3D {
    /// Enable LOD
    pub enabled: bool,
    /// Distance thresholds for different LOD levels
    pub distance_thresholds: Vec<f64>,
    /// Symbol complexity for each LOD level
    pub symbol_complexity: Vec<u32>,
}

/// Instancing settings for performance
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Instancing3D {
    /// Enable instancing
    pub enabled: bool,
    /// Maximum instances per batch
    pub max_instances: u32,
    /// Instance buffer size
    pub buffer_size: u32,
}

/// Culling settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Culling3D {
    /// Enable frustum culling
    pub frustum_culling: bool,
    /// Enable occlusion culling
    pub occlusion_culling: bool,
    /// Enable distance culling
    pub distance_culling: bool,
    /// Maximum render distance
    pub max_distance: f64,
}

impl Default for Scatter3DSeries {
    fn default() -> Self {
        Self {
            name: "Scatter3D".to_string(),
            data: Vec::new(),
            symbol: Symbol3D::default(),
            material: Material3D::default(),
            animation: Some(Animation::normal()),
            visible: true,
            z_index: 0,
        }
    }
}

impl Default for Symbol3D {
    fn default() -> Self {
        Self {
            symbol_type: Symbol3DType::Sphere,
            size: SymbolSize3D::Fixed(5.0),
            color: Color::BLUE,
            opacity: 1.0,
            show_outline: false,
            outline_color: Color::BLACK,
            outline_width: 1.0,
        }
    }
}

impl Default for Scatter3DConfig {
    fn default() -> Self {
        Self {
            coordinate: Coordinate3D::default(),
            color_map: ColorMap3D::default(),
            size_map: SizeMap3D::default(),
            interaction: Interaction3D::default(),
            performance: Performance3D::default(),
        }
    }
}

impl Default for ColorMap3D {
    fn default() -> Self {
        Self {
            map_type: ColorMapType::None,
            palette: vec![Color::BLUE, Color::GREEN, Color::YELLOW, Color::RED],
            value_range: None,
            show_legend: true,
        }
    }
}

impl Default for SizeMap3D {
    fn default() -> Self {
        Self {
            map_type: SizeMapType::None,
            size_range: (2.0, 20.0),
            value_range: None,
        }
    }
}

impl Default for Interaction3D {
    fn default() -> Self {
        Self {
            enable_rotation: true,
            enable_zoom: true,
            enable_pan: true,
            auto_rotation: None,
            selection_mode: SelectionMode3D::Single,
            hover_effects: HoverEffects3D::default(),
        }
    }
}

impl Default for HoverEffects3D {
    fn default() -> Self {
        Self {
            enable_highlight: true,
            highlight_color: Color::YELLOW,
            highlight_scale: 1.2,
            show_tooltip: true,
            tooltip: Some(Tooltip3D::default()),
        }
    }
}

impl Default for Tooltip3D {
    fn default() -> Self {
        Self {
            template: "{name}: ({x}, {y}, {z})".to_string(),
            style: TooltipStyle3D::default(),
            follow_cursor: true,
        }
    }
}

impl Default for TooltipStyle3D {
    fn default() -> Self {
        Self {
            background_color: Color::from_rgba(0, 0, 0, 200),
            text_color: Color::WHITE,
            font_size: 12.0,
            padding: 8.0,
            border_radius: 4.0,
            shadow: true,
        }
    }
}

impl Default for Performance3D {
    fn default() -> Self {
        Self {
            lod: LevelOfDetail3D::default(),
            instancing: Instancing3D::default(),
            culling: Culling3D::default(),
        }
    }
}

impl Default for LevelOfDetail3D {
    fn default() -> Self {
        Self {
            enabled: true,
            distance_thresholds: vec![50.0, 100.0, 200.0],
            symbol_complexity: vec![32, 16, 8], // Number of segments/faces
        }
    }
}

impl Default for Instancing3D {
    fn default() -> Self {
        Self {
            enabled: true,
            max_instances: 10000,
            buffer_size: 1024 * 1024, // 1MB
        }
    }
}

impl Default for Culling3D {
    fn default() -> Self {
        Self {
            frustum_culling: true,
            occlusion_culling: false,
            distance_culling: true,
            max_distance: 1000.0,
        }
    }
}

impl Scatter3DSeries {
    /// Create a new 3D scatter series
    pub fn new<S: Into<String>>(name: S) -> Self {
        Self {
            name: name.into(),
            ..Default::default()
        }
    }

    /// Set the data points
    pub fn data<I>(mut self, data: I) -> Self
    where
        I: IntoIterator<Item = Scatter3DPoint>,
    {
        self.data = data.into_iter().collect();
        self
    }

    /// Add a single data point
    pub fn add_point(mut self, point: Scatter3DPoint) -> Self {
        self.data.push(point);
        self
    }

    /// Set the symbol configuration
    pub fn symbol(mut self, symbol: Symbol3D) -> Self {
        self.symbol = symbol;
        self
    }

    /// Set the material properties
    pub fn material(mut self, material: Material3D) -> Self {
        self.material = material;
        self
    }

    /// Set visibility
    pub fn visible(mut self, visible: bool) -> Self {
        self.visible = visible;
        self
    }

    /// Set z-index
    pub fn z_index(mut self, z_index: i32) -> Self {
        self.z_index = z_index;
        self
    }

    /// Get the bounding box of all data points
    pub fn bounding_box(&self) -> BoundingBox3D {
        if self.data.is_empty() {
            return BoundingBox3D::default();
        }

        let mut min_x = f64::INFINITY;
        let mut max_x = f64::NEG_INFINITY;
        let mut min_y = f64::INFINITY;
        let mut max_y = f64::NEG_INFINITY;
        let mut min_z = f64::INFINITY;
        let mut max_z = f64::NEG_INFINITY;

        for point in &self.data {
            min_x = min_x.min(point.position.x);
            max_x = max_x.max(point.position.x);
            min_y = min_y.min(point.position.y);
            max_y = max_y.max(point.position.y);
            min_z = min_z.min(point.position.z);
            max_z = max_z.max(point.position.z);
        }

        BoundingBox3D {
            min: Point3D::new(min_x, min_y, min_z),
            max: Point3D::new(max_x, max_y, max_z),
        }
    }

    /// Get data points within a bounding box
    pub fn points_in_box(&self, bbox: &BoundingBox3D) -> Vec<&Scatter3DPoint> {
        self.data
            .iter()
            .filter(|point| {
                point.position.x >= bbox.min.x
                    && point.position.x <= bbox.max.x
                    && point.position.y >= bbox.min.y
                    && point.position.y <= bbox.max.y
                    && point.position.z >= bbox.min.z
                    && point.position.z <= bbox.max.z
            })
            .collect()
    }
}

impl Scatter3DPoint {
    /// Create a new 3D scatter point
    pub fn new(position: Point3D) -> Self {
        Self {
            position,
            value: None,
            color: None,
            size: None,
            label: None,
            custom_data: std::collections::HashMap::new(),
        }
    }

    /// Create a point with value
    pub fn with_value(position: Point3D, value: f64) -> Self {
        Self {
            position,
            value: Some(value),
            color: None,
            size: None,
            label: None,
            custom_data: std::collections::HashMap::new(),
        }
    }

    /// Set the point color
    pub fn color(mut self, color: Color) -> Self {
        self.color = Some(color);
        self
    }

    /// Set the point size
    pub fn size(mut self, size: f64) -> Self {
        self.size = Some(size);
        self
    }

    /// Set the point label
    pub fn label<S: Into<String>>(mut self, label: S) -> Self {
        self.label = Some(label.into());
        self
    }

    /// Add custom data
    pub fn custom_data<K: Into<String>>(mut self, key: K, value: serde_json::Value) -> Self {
        self.custom_data.insert(key.into(), value);
        self
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_scatter3d_series_creation() {
        let series = Scatter3DSeries::new("Test Series");
        assert_eq!(series.name, "Test Series");
        assert!(series.data.is_empty());
        assert!(series.visible);
    }

    #[test]
    fn test_scatter3d_point_creation() {
        let point = Scatter3DPoint::new(Point3D::new(1.0, 2.0, 3.0))
            .color(Color::RED)
            .size(10.0)
            .label("Test Point");

        assert_eq!(point.position.x, 1.0);
        assert_eq!(point.position.y, 2.0);
        assert_eq!(point.position.z, 3.0);
        assert_eq!(point.color, Some(Color::RED));
        assert_eq!(point.size, Some(10.0));
        assert_eq!(point.label, Some("Test Point".to_string()));
    }

    #[test]
    fn test_bounding_box_calculation() {
        let mut series = Scatter3DSeries::new("Test");
        series = series
            .add_point(Scatter3DPoint::new(Point3D::new(0.0, 0.0, 0.0)))
            .add_point(Scatter3DPoint::new(Point3D::new(10.0, 5.0, 15.0)))
            .add_point(Scatter3DPoint::new(Point3D::new(-5.0, 10.0, -3.0)));

        let bbox = series.bounding_box();
        assert_eq!(bbox.min.x, -5.0);
        assert_eq!(bbox.max.x, 10.0);
        assert_eq!(bbox.min.y, 0.0);
        assert_eq!(bbox.max.y, 10.0);
        assert_eq!(bbox.min.z, -3.0);
        assert_eq!(bbox.max.z, 15.0);
    }
}
