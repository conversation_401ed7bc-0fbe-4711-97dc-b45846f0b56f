# 高级图表构建器 API 文档

本文档介绍了 echarts-rs 的高级图表构建器功能，包括扩展的 API、智能配置和主题集成。

## 目录

1. [扩展的 ChartBuilder API](#扩展的-chartbuilder-api)
2. [AdvancedChartBuilder](#advancedchartbuilder)
3. [坐标轴配置预设](#坐标轴配置预设)
4. [响应式网格系统](#响应式网格系统)
5. [主题集成](#主题集成)
6. [完整示例](#完整示例)

## 扩展的 ChartBuilder API

### 坐标轴配置

```rust
use echarts_rs::prelude::*;
use echarts_components::{Axis, AxisPosition, AxisType};

// 基础坐标轴配置
let chart = ChartBuilder::line_chart()
    .default_x_axis(Some(0.0), Some(100.0))  // 数值X轴，范围0-100
    .default_y_axis(Some(0.0), Some(50.0))   // 数值Y轴，范围0-50
    .build_enhanced();

// 自定义坐标轴
let x_axis = Axis::value()
    .position(AxisPosition::Bottom)
    .name("时间");

let chart = ChartBuilder::line_chart()
    .x_axis(x_axis)
    .y_axis(y_axis)
    .build_enhanced();

// 分类轴
let categories = vec!["一月", "二月", "三月", "四月", "五月"];
let chart = ChartBuilder::bar_chart()
    .category_x_axis(categories)
    .default_y_axis(None, None)
    .build_enhanced();
```

### 网格配置

```rust
// 默认网格
let chart = ChartBuilder::line_chart()
    .default_grid()
    .build_enhanced();

// 自定义边距的网格
let chart = ChartBuilder::line_chart()
    .grid_with_margins(100.0, 80.0, 50.0, 100.0)  // left, top, right, bottom
    .build_enhanced();

// 自定义网格
let grid = Grid::default()
    .with_show_grid_lines(true)
    .with_background_color(Some(Color::rgba(0.95, 0.95, 0.95, 0.3)));

let chart = ChartBuilder::line_chart()
    .grid(grid)
    .build_enhanced();
```

### 图例配置

```rust
// 默认图例
let chart = ChartBuilder::line_chart()
    .default_legend()
    .build_enhanced();

// 自定义图例
let legend = Legend::default()
    .with_position(Position::Top)
    .with_orientation(Orientation::Horizontal);

let chart = ChartBuilder::line_chart()
    .legend(legend)
    .build_enhanced();
```

### 便捷预设

```rust
// 带坐标轴和网格的预设
let chart = ChartBuilder::line_chart_with_axes()
    .add_line_series(series)
    .build_enhanced();

let chart = ChartBuilder::bar_chart_with_axes()
    .add_bar_series(series)
    .build_enhanced();

let chart = ChartBuilder::scatter_chart_with_axes()
    .add_scatter_series(series)
    .build_enhanced();

// 完整功能图表
let chart = ChartBuilder::full_featured_chart()
    .add_line_series(series)
    .build_enhanced();
```

## AdvancedChartBuilder

AdvancedChartBuilder 提供智能配置和自动化功能。

### 基础使用

```rust
use echarts_rs::chart_builder::{AdvancedChartBuilder, AutoConfigOptions};

// 智能折线图
let chart = AdvancedChartBuilder::new()
    .smart_line_chart()
    .theme("light")
    .responsive(true)
    .build();

// 智能柱状图
let chart = AdvancedChartBuilder::new()
    .smart_bar_chart()
    .theme("dark")
    .build();

// 智能散点图
let chart = AdvancedChartBuilder::new()
    .smart_scatter_chart()
    .auto_analyze_data(&data)  // 自动分析数据范围
    .build();
```

### 自动配置选项

```rust
let auto_config = AutoConfigOptions {
    auto_axis_range: true,      // 自动计算坐标轴范围
    auto_grid_density: true,    // 自动选择网格密度
    auto_margins: true,         // 自动调整边距
    auto_colors: true,          // 自动选择颜色
    auto_performance: true,     // 自动优化性能
};

let chart = AdvancedChartBuilder::new()
    .auto_config(auto_config)
    .smart_line_chart()
    .build();
```

### 便捷函数

```rust
use echarts_rs::chart_builder::{smart_line_chart, smart_bar_chart, responsive_chart, themed_chart};

// 直接创建智能图表
let chart = smart_line_chart()
    .builder_mut()
    .add_line_series(series)
    .build();

// 响应式图表
let chart = responsive_chart()
    .smart_line_chart()
    .build();

// 主题化图表
let chart = themed_chart("dark")
    .smart_bar_chart()
    .build();
```

## 坐标轴配置预设

### 基础预设

```rust
use echarts_components::{AxisPresets, presets};

// 标准坐标轴
let x_axis = AxisPresets::standard_x_axis();
let y_axis = AxisPresets::standard_y_axis();

// 时间轴
let time_axis = AxisPresets::time_x_axis();

// 对数轴
let log_axis = AxisPresets::log_y_axis();

// 百分比轴
let percentage_axis = AxisPresets::percentage_y_axis();
```

### 专业预设

```rust
// 温度轴
let temp_axis = AxisPresets::temperature_y_axis();

// 压力轴
let pressure_axis = AxisPresets::pressure_y_axis();

// 金融价格轴
let price_axis = AxisPresets::price_y_axis();

// 角度轴（0-360度）
let angle_axis = AxisPresets::angle_axis();

// 评分轴（1-5星）
let rating_axis = AxisPresets::rating_axis();
```

### 分类轴预设

```rust
// 月份轴
let month_axis = AxisPresets::month_category_axis();

// 星期轴
let weekday_axis = AxisPresets::weekday_category_axis();

// 年龄分组轴
let age_group_axis = AxisPresets::age_group_axis();
```

### 轴对预设

```rust
// 标准XY轴对
let (x_axis, y_axis) = presets::standard_xy_axes();

// 时间-数值轴对
let (x_axis, y_axis) = presets::time_value_axes();

// 月份-数值轴对
let (x_axis, y_axis) = presets::month_value_axes();

// 金融图表轴对
let (x_axis, y_axis) = presets::financial_axes();

// 科学数据轴对
let (x_axis, y_axis) = presets::scientific_axes();

// 温度监控轴对
let (x_axis, y_axis) = presets::temperature_monitoring_axes();
```

### 自定义范围轴

```rust
// 自定义范围的数值轴
let custom_axis = AxisPresets::custom_range_axis(
    0.0, 1000.0,                    // 范围
    AxisPosition::Left,             // 位置
    Some("自定义单位".to_string())   // 名称
);

// 货币轴
let currency_axis = AxisPresets::currency_axis("¥");
```

## 响应式网格系统

### 基础响应式网格

```rust
use echarts_components::{ResponsiveGrid, ResponsiveGridConfig, ResponsiveGridPresets};

// 创建响应式网格
let grid = ResponsiveGrid::new()
    .with_data_range(0.0, 100.0, 0.0, 50.0)    // 设置数据范围
    .with_data_point_count(200);                // 设置数据点数量

// 自定义配置
let config = ResponsiveGridConfig {
    min_grid_spacing: 20.0,         // 最小网格间距
    max_grid_spacing: 100.0,        // 最大网格间距
    auto_density: true,             // 自动调整密度
    auto_style: true,               // 自动调整样式
    data_density_threshold: 0.1,    // 数据密度阈值
};

let grid = ResponsiveGrid::new().with_config(config);
```

### 预设响应式网格

```rust
// 高密度数据网格
let grid = ResponsiveGridPresets::high_density_data();

// 稀疏数据网格
let grid = ResponsiveGridPresets::sparse_data();

// 时间序列网格
let grid = ResponsiveGridPresets::time_series();

// 金融数据网格
let grid = ResponsiveGridPresets::financial_data();

// 科学数据网格
let grid = ResponsiveGridPresets::scientific_data();
```

## 主题集成

### 主题化组件

```rust
use echarts_components::{ThemedComponents, ComponentTheme, ThemedComponentBuilder};
use echarts_themes::Theme;

// 使用预定义的主题化组件
let (x_axis, y_axis) = ThemedComponents::light_standard_axes();
let grid = ThemedComponents::light_responsive_grid();

let (x_axis, y_axis) = ThemedComponents::dark_standard_axes();
let grid = ThemedComponents::dark_responsive_grid();
```

### 自定义主题

```rust
// 从 ECharts 主题创建组件主题
let echarts_theme = Theme::light();
let component_theme = ComponentTheme::from_echarts_theme(&echarts_theme);

// 创建主题化构建器
let builder = ThemedComponentBuilder::from_echarts_theme(&echarts_theme);

// 创建主题化组件
let axis = builder.create_axis(AxisPresets::standard_x_axis());
let grid = builder.create_grid(Grid::default());
let responsive_grid = builder.create_responsive_grid(ResponsiveGrid::new());
```

### 主题切换

```rust
// 亮色主题
let light_theme = ComponentTheme {
    axis_theme: AxisTheme::light(),
    grid_theme: GridTheme::light(),
    text_theme: TextTheme::from_echarts_theme(&Theme::light()),
};

// 暗色主题
let dark_theme = ComponentTheme {
    axis_theme: AxisTheme::dark(),
    grid_theme: GridTheme::dark(),
    text_theme: TextTheme::from_echarts_theme(&Theme::dark()),
};

// 应用主题
let mut axis = AxisPresets::standard_x_axis();
light_theme.apply_to_axis(&mut axis);
```

## 完整示例

### 专业金融图表

```rust
use echarts_rs::prelude::*;
use echarts_components::{presets, ResponsiveGridPresets, ThemedComponents};

fn create_financial_chart() -> EnhancedChart {
    // 使用金融预设
    let (x_axis, y_axis) = presets::financial_axes();
    let grid = ResponsiveGridPresets::financial_data()
        .with_data_range(0.0, 30.0, 90.0, 120.0)
        .with_data_point_count(30);
    
    ChartBuilder::line_chart()
        .title("股价走势图")
        .size(1000.0, 600.0)
        .x_axis(x_axis)
        .y_axis(y_axis)
        .add_line_series(
            LineSeries::new("股价")
                .data(generate_stock_data())
                .color(Color::rgb(0.2, 0.8, 0.2))
                .line_width(2.0)
        )
        .build_enhanced()
}
```

### 智能科学数据图表

```rust
fn create_scientific_chart() -> EnhancedChart {
    let data = generate_scientific_data();
    
    AdvancedChartBuilder::new()
        .smart_scatter_chart()
        .auto_analyze_data(&data)
        .theme("scientific")
        .responsive(true)
        .builder_mut()
        .title("科学实验数据")
        .add_scatter_series(
            ScatterSeries::new("实验数据")
                .data(data)
                .color(Color::rgb(0.8, 0.2, 0.8))
        )
        .build()
}
```

### 主题化仪表板

```rust
fn create_themed_dashboard() -> EnhancedChart {
    let (x_axis, y_axis) = ThemedComponents::dark_standard_axes();
    let grid = ThemedComponents::dark_responsive_grid();
    
    ChartBuilder::mixed_chart()
        .title("主题化仪表板")
        .size(1200.0, 800.0)
        .x_axis(x_axis)
        .y_axis(y_axis)
        .add_line_series(line_series)
        .add_bar_series(bar_series)
        .default_legend()
        .build_enhanced()
}
```

## 最佳实践

1. **选择合适的预设**：根据数据类型选择对应的坐标轴预设
2. **使用响应式网格**：对于大数据量或动态数据，使用响应式网格
3. **主题一致性**：在应用中保持主题的一致性
4. **性能优化**：对于复杂图表，启用自动性能优化
5. **渐进增强**：从基础 ChartBuilder 开始，根据需要升级到 AdvancedChartBuilder

## 迁移指南

### 从基础 ChartBuilder 迁移

```rust
// 旧方式
let chart = ChartBuilder::line_chart()
    .add_line_series(series)
    .build();

// 新方式 - 添加坐标轴和网格
let chart = ChartBuilder::line_chart_with_axes()
    .add_line_series(series)
    .build_enhanced();
```

### 升级到智能构建器

```rust
// 基础方式
let chart = ChartBuilder::line_chart_with_axes()
    .add_line_series(series)
    .build_enhanced();

// 智能方式
let chart = smart_line_chart()
    .auto_analyze_data(&data)
    .theme("light")
    .builder_mut()
    .add_line_series(series)
    .build();
```
