//! Rendering backends for Rust ECharts
//!
//! This crate provides various rendering backends for chart output.

use echarts_themes::Theme;
use echarts_core::{*, style::{FontWeight, FontStyle, TextAlign, TextBaseline}};
use serde::{Deserialize, Serialize};

pub mod canvas;
pub mod html_canvas;

// Re-export core types
pub use echarts_core::{DrawCommand, Chart, Bounds, Color, Point, Result};

pub use canvas::*;
pub use html_canvas::*;

pub trait Renderer {
    /// 渲染DrawCommand数据 - 新架构的核心方法
    fn render_commands(&mut self, commands: Vec<DrawCommand>, bounds: Bounds) -> Result<()>;

    /// 兼容性方法：从Chart渲染（将被废弃）
    fn render_chart(&mut self, chart: &Chart, bounds: Bounds) -> Result<()> {
        // 默认实现：提示使用新方法
        println!("⚠️ render_chart已废弃，请使用render_commands方法");
        Ok(())
    }

    /// Clear the rendering surface
    fn clear(&mut self);

    /// Get the current bounds
    fn bounds(&self) -> Bounds;

    /// Set the bounds
    fn set_bounds(&mut self, bounds: Bounds);
}

/// 性能提示，用于优化渲染
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PerformanceHint {
    /// 优化质量，可能较慢
    Quality,
    /// 平衡质量和速度
    Balanced,
    /// 优化速度，可能降低质量
    Speed,
    /// 批量模式，延迟渲染直到 flush
    Batch,
}

impl Default for PerformanceHint {
    fn default() -> Self {
        Self::Balanced
    }
}

/// 渲染统计信息
#[derive(Debug, Clone, Default)]
pub struct RenderStats {
    /// 渲染的命令数量
    pub commands_rendered: usize,
    /// 渲染时间（毫秒）
    pub render_time_ms: f64,
    /// 内存使用量（字节）
    pub memory_usage_bytes: usize,
    /// 缓存命中率
    pub cache_hit_rate: f64,
}
