// // src/logging/exporter.rs
// use std::{fs, path::PathBuf};
// use chrono::Utc;
// use serde_json::Value;

// pub struct LogExporter;

// impl LogExporter {
//     #[instrument(name = "LogExporter::export", skip_all)]
//     pub async fn export_logs(
//         log_dir: PathBuf,
//         output_path: PathBuf
//     ) -> Result<(), ExportError> {
//         let mut combined = Vec::new();
        
//         // 读取所有日志文件
//         for entry in fs::read_dir(log_dir)? {
//             let path = entry?.path();
//             if path.extension().map_or(false, |ext| ext == "log") {
//                 let content = fs::read_to_string(&path)?;
//                 for line in content.lines() {
//                     if let Ok(json) = serde_json::from_str::<Value>(line) {
//                         combined.push(json);
//                     }
//                 }
//             }
//         }
        
//         // 按时间排序
//         combined.sort_by(|a, b| {
//             let a_time = a["timestamp"].as_str().unwrap_or("");
//             let b_time = b["timestamp"].as_str().unwrap_or("");
//             a_time.cmp(b_time)
//         });
        
//         // 写入合并文件
//         let export_data = serde_json::to_string_pretty(&combined)?;
//         fs::write(output_path, export_data)?;
        
//         tracing::info!(
//             output_path = %output_path.display(),
//             log_entries = combined.len(),
//             "日志导出完成"
//         );
        
//         Ok(())
//     }
// }

// // UI 集成
// fn export_logs(cx: &mut ViewContext<Self>) {
//     let exporter = LogExporter;
//     let log_dir = cx.resource_dir().join("logs");
//     let output = cx.save_file_dialog();
    
//     cx.spawn(|cx| async move {
//         exporter.export_logs(log_dir, output)
//             .instrument(info_span!("log_export"))
//             .await
//             .map_err(|e| {
//                 tracing::error!(error = ?e, "日志导出失败");
//                 cx.update(|cx| {
//                     cx.show_error("导出失败");
//                 })
//             })
//     });
// }