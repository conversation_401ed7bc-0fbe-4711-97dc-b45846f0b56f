//! DataZoom component implementation

use crate::{Component, Renderable, Themeable};
use echarts_core::*;
use echarts_core::RenderContext;
use echarts_themes::Theme;
use serde::{Deserialize, Serialize};

/// DataZoom component for data range selection and zooming
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataZoom {
    /// Whether the datazoom is visible
    pub visible: bool,

    /// DataZoom type
    pub zoom_type: DataZoomType,

    /// Position and orientation
    pub orientation: Orientation,

    /// Position configuration
    pub position: DataZoomPosition,

    /// Start percentage (0-100)
    pub start: f64,

    /// End percentage (0-100)
    pub end: f64,

    /// Minimum span percentage
    pub min_span: f64,

    /// Maximum span percentage
    pub max_span: f64,

    /// Background style
    pub background_style: BackgroundStyle,

    /// Selected area style
    pub selected_style: SelectedStyle,

    /// Handle style
    pub handle_style: HandleStyle,

    /// Text style for labels
    pub text_style: TextStyle,

    /// Whether to show detail (percentage values)
    pub show_detail: bool,

    /// Whether to show data shadow
    pub show_data_shadow: bool,

    /// Data shadow style
    pub data_shadow_style: DataShadowStyle,

    /// Filter mode
    pub filter_mode: FilterMode,

    /// Target axis indices
    pub x_axis_index: Vec<usize>,
    pub y_axis_index: Vec<usize>,
}

/// DataZoom types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum DataZoomType {
    /// Slider type with handles
    Slider,
    /// Inside type (mouse/touch interaction)
    Inside,
    /// Select type (brush selection)
    Select,
}

/// DataZoom position configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataZoomPosition {
    /// Left position (pixels or percentage)
    pub left: PositionValue,
    /// Top position (pixels or percentage)
    pub top: PositionValue,
    /// Right position (pixels or percentage)
    pub right: PositionValue,
    /// Bottom position (pixels or percentage)
    pub bottom: PositionValue,
    /// Width (pixels or percentage)
    pub width: Option<PositionValue>,
    /// Height (pixels or percentage)
    pub height: Option<PositionValue>,
}

/// Position value (pixels or percentage)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PositionValue {
    Pixels(f64),
    Percentage(f64),
    Auto,
}

/// Orientation for datazoom
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum Orientation {
    Horizontal,
    Vertical,
}

/// Background style for datazoom
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackgroundStyle {
    /// Background color
    pub color: Color,
    /// Border color
    pub border_color: Color,
    /// Border width
    pub border_width: f64,
    /// Border radius
    pub border_radius: f64,
}

/// Selected area style
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SelectedStyle {
    /// Fill color
    pub color: Color,
    /// Border color
    pub border_color: Color,
    /// Border width
    pub border_width: f64,
    /// Opacity
    pub opacity: f64,
}

/// Handle style for slider
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HandleStyle {
    /// Handle color
    pub color: Color,
    /// Handle size
    pub size: f64,
    /// Handle border color
    pub border_color: Color,
    /// Handle border width
    pub border_width: f64,
    /// Handle shadow
    pub shadow_blur: f64,
    pub shadow_color: Color,
    pub shadow_offset: (f64, f64),
}

/// Data shadow style
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataShadowStyle {
    /// Line color
    pub line_color: Color,
    /// Line width
    pub line_width: f64,
    /// Area color
    pub area_color: Color,
    /// Area opacity
    pub area_opacity: f64,
}

/// Filter mode for datazoom
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum FilterMode {
    /// Filter out data outside the range
    Filter,
    /// Keep all data but highlight the range
    WeakFilter,
    /// Empty data outside the range
    Empty,
    /// No filtering
    None,
}

impl Default for DataZoom {
    fn default() -> Self {
        Self {
            visible: true,
            zoom_type: DataZoomType::Slider,
            orientation: Orientation::Horizontal,
            position: DataZoomPosition::default(),
            start: 0.0,
            end: 100.0,
            min_span: 0.0,
            max_span: 100.0,
            background_style: BackgroundStyle::default(),
            selected_style: SelectedStyle::default(),
            handle_style: HandleStyle::default(),
            text_style: TextStyle::default(),
            show_detail: true,
            show_data_shadow: true,
            data_shadow_style: DataShadowStyle::default(),
            filter_mode: FilterMode::Filter,
            x_axis_index: vec![0],
            y_axis_index: Vec::new(),
        }
    }
}

impl Default for DataZoomPosition {
    fn default() -> Self {
        Self {
            left: PositionValue::Percentage(10.0),
            top: PositionValue::Auto,
            right: PositionValue::Percentage(10.0),
            bottom: PositionValue::Pixels(20.0),
            width: None,
            height: Some(PositionValue::Pixels(30.0)),
        }
    }
}

impl Default for BackgroundStyle {
    fn default() -> Self {
        Self {
            color: Color::from_rgba(247, 247, 247, 255),
            border_color: Color::from_rgba(204, 204, 204, 255),
            border_width: 1.0,
            border_radius: 3.0,
        }
    }
}

impl Default for SelectedStyle {
    fn default() -> Self {
        Self {
            color: Color::from_rgba(167, 183, 204, 255),
            border_color: Color::from_rgba(167, 183, 204, 255),
            border_width: 1.0,
            opacity: 0.3,
        }
    }
}

impl Default for HandleStyle {
    fn default() -> Self {
        Self {
            color: Color::WHITE,
            size: 10.0,
            border_color: Color::from_rgba(167, 183, 204, 255),
            border_width: 1.0,
            shadow_blur: 3.0,
            shadow_color: Color::from_rgba(0, 0, 0, 100),
            shadow_offset: (0.0, 2.0),
        }
    }
}

impl Default for DataShadowStyle {
    fn default() -> Self {
        Self {
            line_color: Color::from_rgba(136, 136, 136, 255),
            line_width: 1.0,
            area_color: Color::from_rgba(136, 136, 136, 100),
            area_opacity: 0.3,
        }
    }
}

impl Component for DataZoom {
    fn component_type(&self) -> &'static str {
        "datazoom"
    }

    fn is_visible(&self) -> bool {
        self.visible
    }

    fn set_visible(&mut self, visible: bool) {
        self.visible = visible;
    }
}

impl DataZoom {
    /// Create a new datazoom component
    pub fn new() -> Self {
        Self::default()
    }

    /// Set datazoom type
    pub fn zoom_type(mut self, zoom_type: DataZoomType) -> Self {
        self.zoom_type = zoom_type;
        self
    }

    /// Set orientation
    pub fn orientation(mut self, orientation: Orientation) -> Self {
        self.orientation = orientation;
        self
    }

    /// Set start and end percentages
    pub fn range(mut self, start: f64, end: f64) -> Self {
        self.start = start.clamp(0.0, 100.0);
        self.end = end.clamp(0.0, 100.0);
        self
    }

    /// Set minimum and maximum span
    pub fn span_range(mut self, min_span: f64, max_span: f64) -> Self {
        self.min_span = min_span.clamp(0.0, 100.0);
        self.max_span = max_span.clamp(0.0, 100.0);
        self
    }

    /// Set target x-axis indices
    pub fn x_axis_index(mut self, indices: Vec<usize>) -> Self {
        self.x_axis_index = indices;
        self
    }

    /// Set target y-axis indices
    pub fn y_axis_index(mut self, indices: Vec<usize>) -> Self {
        self.y_axis_index = indices;
        self
    }

    /// Set filter mode
    pub fn filter_mode(mut self, mode: FilterMode) -> Self {
        self.filter_mode = mode;
        self
    }

    /// Calculate datazoom bounds
    fn calculate_bounds(&self, container_bounds: Bounds) -> Bounds {
        // For now, return a simple horizontal datazoom at the bottom
        let height = 30.0;
        let margin = 20.0;

        Bounds::new(
            container_bounds.origin.x + margin,
            container_bounds.origin.y + container_bounds.height() - height - margin,
            container_bounds.width() - 2.0 * margin,
            height,
        )
    }

    /// Get current data range based on start/end percentages
    pub fn get_data_range(&self, total_data_count: usize) -> (usize, usize) {
        let start_index = ((self.start / 100.0) * total_data_count as f64) as usize;
        let end_index = ((self.end / 100.0) * total_data_count as f64) as usize;

        (
            start_index.min(total_data_count),
            end_index.min(total_data_count),
        )
    }

    /// Update range based on user interaction
    pub fn update_range(&mut self, new_start: f64, new_end: f64) {
        let span = (new_end - new_start).abs();

        // Ensure span is within limits
        if span < self.min_span {
            return;
        }
        if span > self.max_span {
            return;
        }

        self.start = new_start.clamp(0.0, 100.0);
        self.end = new_end.clamp(0.0, 100.0);

        // Ensure start <= end
        if self.start > self.end {
            std::mem::swap(&mut self.start, &mut self.end);
        }
    }
}

impl Renderable for DataZoom {
    fn render<R: ChartRenderer>(&self, ctx: &mut RenderContext<R>, bounds: Bounds) -> Result<()> {
        if !self.visible {
            return Ok(());
        }

        let datazoom_bounds = self.calculate_bounds(bounds);

        match self.zoom_type {
            DataZoomType::Slider => {
                self.render_slider(ctx, datazoom_bounds)?;
            }
            DataZoomType::Inside => {
                // Inside datazoom doesn't render visible UI
                return Ok(());
            }
            DataZoomType::Select => {
                self.render_select(ctx, datazoom_bounds)?;
            }
        }

        Ok(())
    }
}

impl Themeable for DataZoom {
    fn apply_theme(&mut self, theme: &Theme) {
        // Apply theme colors to datazoom styles
        self.text_style.color = theme.text_style.color;
    }
}

impl DataZoom {
    /// Render slider type datazoom
    fn render_slider<R: ChartRenderer>(&self, ctx: &mut RenderContext<R>, bounds: Bounds) -> Result<()> {
        // Draw background
        ctx.set_fill_color(self.background_style.color);
        ctx.fill_rect(bounds);
        ctx.set_stroke(self.background_style.border_color, self.background_style.border_width);
        ctx.stroke_rect(bounds);

        // Draw data shadow if enabled
        if self.show_data_shadow {
            self.draw_data_shadow(ctx, bounds)?;
        }

        // Calculate selected area
        let selected_start = bounds.origin.x + (bounds.width() * self.start / 100.0);
        let selected_end = bounds.origin.x + (bounds.width() * self.end / 100.0);
        let selected_bounds = Bounds::new(
            selected_start,
            bounds.origin.y,
            selected_end - selected_start,
            bounds.height(),
        );

        // Draw selected area
        ctx.set_fill_color(self.selected_style.color.with_alpha(self.selected_style.opacity as f32));
        ctx.fill_rect(selected_bounds);
        ctx.set_stroke(self.selected_style.border_color, self.selected_style.border_width);
        ctx.stroke_rect(selected_bounds);

        // Draw handles
        self.draw_handles(ctx, bounds, selected_start, selected_end)?;

        // Draw detail text if enabled
        if self.show_detail {
            self.draw_detail_text(ctx, bounds)?;
        }

        Ok(())
    }

    /// Render select type datazoom
    fn render_select<R: ChartRenderer>(&self, ctx: &mut RenderContext<R>, bounds: Bounds) -> Result<()> {
        // Select type shows a brush selection area
        // For now, just draw a simple selection indicator
        let indicator_height = 5.0;
        let indicator_bounds = Bounds::new(
            bounds.origin.x,
            bounds.origin.y + bounds.height() - indicator_height,
            bounds.width(),
            indicator_height,
        );

        ctx.set_fill_color(self.background_style.color);
        ctx.fill_rect(indicator_bounds);

        // Draw selected range
        let selected_start = bounds.origin.x + (bounds.width() * self.start / 100.0);
        let selected_end = bounds.origin.x + (bounds.width() * self.end / 100.0);
        let selected_bounds = Bounds::new(
            selected_start,
            bounds.origin.y + bounds.height() - indicator_height,
            selected_end - selected_start,
            indicator_height,
        );

        ctx.set_fill_color(self.selected_style.color);
        ctx.fill_rect(selected_bounds);

        Ok(())
    }

    /// Draw data shadow
    fn draw_data_shadow<R: ChartRenderer>(&self, ctx: &mut RenderContext<R>, bounds: Bounds) -> Result<()> {
        // For now, draw a simple sine wave as data shadow
        let points = 50;
        let mut path = Path::new();

        for i in 0..points {
            let x = bounds.origin.x + (bounds.width() * i as f64) / (points - 1) as f64;
            let y = bounds.origin.y
                + bounds.height() / 2.0
                + (bounds.height() / 4.0) * (i as f64 * 0.2).sin();

            if i == 0 {
                path = path.move_to(Point::new(x, y));
            } else {
                path = path.line_to(Point::new(x, y));
            }
        }

        ctx.draw_path(
            path,
            None,
            Some((
                self.data_shadow_style.line_color,
                self.data_shadow_style.line_width,
            )),
        );

        Ok(())
    }

    /// Draw slider handles
    fn draw_handles(
        &self,
        ctx: &mut RenderContext,
        bounds: Bounds,
        start_x: f64,
        end_x: f64,
    ) -> Result<()> {
        let handle_size = self.handle_style.size;
        let handle_y = bounds.origin.y + bounds.height() / 2.0;

        // Draw start handle
        let start_handle = Bounds::new(
            start_x - handle_size / 2.0,
            handle_y - handle_size / 2.0,
            handle_size,
            handle_size,
        );
        ctx.set_fill_color(self.handle_style.color);
        ctx.fill_rect(start_handle);
        ctx.set_stroke(self.handle_style.border_color, self.handle_style.border_width);
        ctx.stroke_rect(start_handle);

        // Draw end handle
        let end_handle = Bounds::new(
            end_x - handle_size / 2.0,
            handle_y - handle_size / 2.0,
            handle_size,
            handle_size,
        );
        ctx.set_fill_color(self.handle_style.color);
        ctx.fill_rect(end_handle);
        ctx.set_stroke(self.handle_style.border_color, self.handle_style.border_width);
        ctx.stroke_rect(end_handle);

        Ok(())
    }

    /// Draw detail text
    fn draw_detail_text<R: ChartRenderer>(&self, ctx: &mut RenderContext<R>, bounds: Bounds) -> Result<()> {
        let text = format!("{}% - {}%", self.start as i32, self.end as i32);
        let text_pos = Point::new(
            bounds.origin.x + bounds.width() / 2.0,
            bounds.origin.y - 5.0,
        );

        ctx.draw_text(text, text_pos, self.text_style.clone());
        Ok(())
    }
}

/// Convenient constructors
impl DataZoom {
    /// Create a horizontal slider datazoom
    pub fn horizontal_slider() -> Self {
        Self::new()
            .zoom_type(DataZoomType::Slider)
            .orientation(Orientation::Horizontal)
    }

    /// Create a vertical slider datazoom
    pub fn vertical_slider() -> Self {
        Self::new()
            .zoom_type(DataZoomType::Slider)
            .orientation(Orientation::Vertical)
    }

    /// Create an inside datazoom (no visible UI)
    pub fn inside() -> Self {
        Self::new().zoom_type(DataZoomType::Inside)
    }

    /// Create a select datazoom
    pub fn select() -> Self {
        Self::new().zoom_type(DataZoomType::Select)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_datazoom_creation() {
        let datazoom = DataZoom::new();
        assert_eq!(datazoom.zoom_type, DataZoomType::Slider);
        assert_eq!(datazoom.start, 0.0);
        assert_eq!(datazoom.end, 100.0);
        assert!(datazoom.visible);
    }

    #[test]
    fn test_datazoom_range() {
        let mut datazoom = DataZoom::new().range(20.0, 80.0);
        assert_eq!(datazoom.start, 20.0);
        assert_eq!(datazoom.end, 80.0);

        datazoom.update_range(30.0, 70.0);
        assert_eq!(datazoom.start, 30.0);
        assert_eq!(datazoom.end, 70.0);
    }

    #[test]
    fn test_data_range_calculation() {
        let datazoom = DataZoom::new().range(25.0, 75.0);
        let (start, end) = datazoom.get_data_range(100);
        assert_eq!(start, 25);
        assert_eq!(end, 75);
    }

    #[test]
    fn test_datazoom_types() {
        let slider = DataZoom::horizontal_slider();
        assert_eq!(slider.zoom_type, DataZoomType::Slider);
        assert_eq!(slider.orientation, Orientation::Horizontal);

        let inside = DataZoom::inside();
        assert_eq!(inside.zoom_type, DataZoomType::Inside);

        let select = DataZoom::select();
        assert_eq!(select.zoom_type, DataZoomType::Select);
    }
}
