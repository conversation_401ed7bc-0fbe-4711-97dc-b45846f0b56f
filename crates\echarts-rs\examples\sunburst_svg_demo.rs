//! 旭日图SVG演示
//!
//! 生成各种旭日图的SVG文件，展示SunburstSeries的完整功能

use std::fs;
use echarts_rs::{SunburstSeries, SunburstDataItem, SunburstLabel, SunburstLabelPosition, Color};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("☀️ 旭日图SVG演示系统");
    println!("{}", "=".repeat(50));

    // 确保输出目录存在
    let output_dir = "temp/svg/sunburst_charts";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 基础旭日图
    println!("\n☀️ 1. 生成基础旭日图...");
    generate_basic_sunburst(output_dir)?;

    // 2. 公司组织架构旭日图
    println!("\n🏢 2. 生成公司组织架构旭日图...");
    generate_organization_sunburst(output_dir)?;

    // 3. 产品分类旭日图
    println!("\n📦 3. 生成产品分类旭日图...");
    generate_product_category_sunburst(output_dir)?;

    // 4. 技能树旭日图
    println!("\n🌳 4. 生成技能树旭日图...");
    generate_skill_tree_sunburst(output_dir)?;

    // 5. 多层级数据旭日图
    println!("\n📊 5. 生成多层级数据旭日图...");
    generate_multilevel_sunburst(output_dir)?;

    // 6. 生成展示页面
    println!("\n📄 6. 生成展示页面...");
    generate_sunburst_showcase(output_dir)?;

    println!("\n🎉 旭日图SVG演示生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/sunburst_demo.html 查看演示", output_dir);

    Ok(())
}

/// 生成基础旭日图
fn generate_basic_sunburst(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        SunburstDataItem::new("分类A", 40.0).color(Color::rgb(0.3, 0.6, 1.0)),
        SunburstDataItem::new("分类B", 30.0).color(Color::rgb(0.6, 0.8, 0.4)),
        SunburstDataItem::new("分类C", 20.0).color(Color::rgb(1.0, 0.6, 0.3)),
        SunburstDataItem::new("分类D", 10.0).color(Color::rgb(0.8, 0.4, 0.8)),
    ];

    let sunburst_series = SunburstSeries::new("基础旭日图")
        .data(data)
        .center(0.5, 0.5)
        .radius(0.0, 0.8)
        .start_angle(90.0)
        .border(1.0, Color::rgb(1.0, 1.0, 1.0));

    let svg = create_sunburst_svg(&sunburst_series, "基础旭日图演示", 600.0, 600.0)?;
    fs::write(format!("{}/01_basic_sunburst.svg", output_dir), svg)?;
    
    println!("  ✅ 基础旭日图生成完成");
    Ok(())
}

/// 生成公司组织架构旭日图
fn generate_organization_sunburst(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        SunburstDataItem::new("CEO", 0.0)
            .add_child(
                SunburstDataItem::new("技术部", 0.0)
                    .add_child(SunburstDataItem::new("前端", 15.0).color(Color::rgb(0.2, 0.6, 1.0)))
                    .add_child(SunburstDataItem::new("后端", 20.0).color(Color::rgb(0.4, 0.8, 0.6)))
                    .add_child(SunburstDataItem::new("移动端", 12.0).color(Color::rgb(0.6, 0.4, 1.0)))
                    .add_child(SunburstDataItem::new("测试", 8.0).color(Color::rgb(1.0, 0.4, 0.6)))
            )
            .add_child(
                SunburstDataItem::new("市场部", 0.0)
                    .add_child(SunburstDataItem::new("推广", 10.0).color(Color::rgb(1.0, 0.6, 0.3)))
                    .add_child(SunburstDataItem::new("销售", 15.0).color(Color::rgb(1.0, 0.8, 0.2)))
                    .add_child(SunburstDataItem::new("客服", 8.0).color(Color::rgb(0.4, 1.0, 0.8)))
            )
            .add_child(
                SunburstDataItem::new("运营部", 0.0)
                    .add_child(SunburstDataItem::new("产品", 12.0).color(Color::rgb(0.8, 0.4, 0.8)))
                    .add_child(SunburstDataItem::new("运营", 10.0).color(Color::rgb(0.4, 0.8, 0.8)))
            ),
    ];

    let label = SunburstLabel {
        show: true,
        font_size: 11.0,
        color: Color::rgb(0.1, 0.1, 0.1),
        position: SunburstLabelPosition::Middle,
        min_angle: 0.05,
        rotate: false,
    };

    let sunburst_series = SunburstSeries::new("公司组织架构")
        .data(data)
        .center(0.5, 0.5)
        .radius(0.1, 0.85)
        .start_angle(0.0)
        .label(label)
        .max_depth(3)
        .level_gap(3.0)
        .border(1.5, Color::rgb(0.9, 0.9, 0.9));

    let svg = create_sunburst_svg(&sunburst_series, "公司组织架构旭日图", 600.0, 600.0)?;
    fs::write(format!("{}/02_organization_sunburst.svg", output_dir), svg)?;
    
    println!("  ✅ 公司组织架构旭日图生成完成");
    Ok(())
}

/// 生成产品分类旭日图
fn generate_product_category_sunburst(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        SunburstDataItem::new("电子产品", 0.0)
            .add_child(SunburstDataItem::new("手机", 45.0).color(Color::rgb(0.8, 0.2, 0.2)))
            .add_child(SunburstDataItem::new("电脑", 35.0).color(Color::rgb(0.2, 0.8, 0.2)))
            .add_child(SunburstDataItem::new("平板", 20.0).color(Color::rgb(0.2, 0.2, 0.8))),
        SunburstDataItem::new("服装", 0.0)
            .add_child(SunburstDataItem::new("男装", 30.0).color(Color::rgb(0.8, 0.8, 0.2)))
            .add_child(SunburstDataItem::new("女装", 40.0).color(Color::rgb(0.8, 0.2, 0.8)))
            .add_child(SunburstDataItem::new("童装", 15.0).color(Color::rgb(0.2, 0.8, 0.8))),
        SunburstDataItem::new("家居", 0.0)
            .add_child(SunburstDataItem::new("家具", 25.0).color(Color::rgb(1.0, 0.6, 0.4)))
            .add_child(SunburstDataItem::new("装饰", 18.0).color(Color::rgb(0.6, 1.0, 0.4)))
            .add_child(SunburstDataItem::new("厨具", 12.0).color(Color::rgb(0.4, 0.6, 1.0))),
    ];

    let label = SunburstLabel {
        show: true,
        font_size: 10.0,
        color: Color::rgb(0.2, 0.2, 0.2),
        position: SunburstLabelPosition::Inside,
        min_angle: 0.08,
        rotate: true,
    };

    let sunburst_series = SunburstSeries::new("产品分类")
        .data(data)
        .center(0.5, 0.5)
        .radius(0.15, 0.75)
        .start_angle(45.0)
        .label(label)
        .max_depth(2)
        .level_gap(2.0)
        .border(1.0, Color::rgb(0.8, 0.8, 0.8));

    let svg = create_sunburst_svg(&sunburst_series, "产品分类旭日图", 600.0, 600.0)?;
    fs::write(format!("{}/03_product_category_sunburst.svg", output_dir), svg)?;
    
    println!("  ✅ 产品分类旭日图生成完成");
    Ok(())
}

/// 生成技能树旭日图
fn generate_skill_tree_sunburst(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        SunburstDataItem::new("编程技能", 0.0)
            .add_child(
                SunburstDataItem::new("前端", 0.0)
                    .add_child(SunburstDataItem::new("HTML", 8.0).color(Color::rgb(1.0, 0.4, 0.2)))
                    .add_child(SunburstDataItem::new("CSS", 7.0).color(Color::rgb(0.2, 0.6, 1.0)))
                    .add_child(SunburstDataItem::new("JavaScript", 9.0).color(Color::rgb(1.0, 0.8, 0.2)))
                    .add_child(SunburstDataItem::new("React", 6.0).color(Color::rgb(0.4, 0.8, 1.0)))
            )
            .add_child(
                SunburstDataItem::new("后端", 0.0)
                    .add_child(SunburstDataItem::new("Python", 8.5).color(Color::rgb(0.3, 0.7, 0.3)))
                    .add_child(SunburstDataItem::new("Java", 7.5).color(Color::rgb(0.8, 0.4, 0.2)))
                    .add_child(SunburstDataItem::new("Rust", 6.0).color(Color::rgb(0.8, 0.2, 0.4)))
                    .add_child(SunburstDataItem::new("Go", 5.5).color(Color::rgb(0.4, 0.8, 0.8)))
            )
            .add_child(
                SunburstDataItem::new("数据库", 0.0)
                    .add_child(SunburstDataItem::new("MySQL", 6.0).color(Color::rgb(0.2, 0.4, 0.8)))
                    .add_child(SunburstDataItem::new("PostgreSQL", 5.0).color(Color::rgb(0.6, 0.2, 0.8)))
                    .add_child(SunburstDataItem::new("MongoDB", 4.0).color(Color::rgb(0.8, 0.6, 0.2)))
            ),
    ];

    let label = SunburstLabel {
        show: true,
        font_size: 9.0,
        color: Color::rgb(0.1, 0.1, 0.1),
        position: SunburstLabelPosition::Outside,
        min_angle: 0.03,
        rotate: false,
    };

    let sunburst_series = SunburstSeries::new("技能树")
        .data(data)
        .center(0.5, 0.5)
        .radius(0.05, 0.7)
        .start_angle(90.0)
        .label(label)
        .max_depth(3)
        .level_gap(1.5)
        .border(0.5, Color::rgb(0.9, 0.9, 0.9));

    let svg = create_sunburst_svg(&sunburst_series, "技能树旭日图", 600.0, 600.0)?;
    fs::write(format!("{}/04_skill_tree_sunburst.svg", output_dir), svg)?;
    
    println!("  ✅ 技能树旭日图生成完成");
    Ok(())
}

/// 生成多层级数据旭日图
fn generate_multilevel_sunburst(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        SunburstDataItem::new("根节点", 0.0)
            .add_child(
                SunburstDataItem::new("分支A", 0.0)
                    .add_child(
                        SunburstDataItem::new("子分支A1", 0.0)
                            .add_child(SunburstDataItem::new("叶子A1a", 5.0).color(Color::rgb(1.0, 0.2, 0.2)))
                            .add_child(SunburstDataItem::new("叶子A1b", 3.0).color(Color::rgb(1.0, 0.4, 0.2)))
                    )
                    .add_child(
                        SunburstDataItem::new("子分支A2", 0.0)
                            .add_child(SunburstDataItem::new("叶子A2a", 4.0).color(Color::rgb(1.0, 0.6, 0.2)))
                            .add_child(SunburstDataItem::new("叶子A2b", 6.0).color(Color::rgb(1.0, 0.8, 0.2)))
                    )
            )
            .add_child(
                SunburstDataItem::new("分支B", 0.0)
                    .add_child(
                        SunburstDataItem::new("子分支B1", 0.0)
                            .add_child(SunburstDataItem::new("叶子B1a", 7.0).color(Color::rgb(0.2, 1.0, 0.2)))
                            .add_child(SunburstDataItem::new("叶子B1b", 5.0).color(Color::rgb(0.4, 1.0, 0.2)))
                    )
                    .add_child(
                        SunburstDataItem::new("子分支B2", 0.0)
                            .add_child(SunburstDataItem::new("叶子B2a", 3.0).color(Color::rgb(0.6, 1.0, 0.2)))
                            .add_child(SunburstDataItem::new("叶子B2b", 4.0).color(Color::rgb(0.8, 1.0, 0.2)))
                    )
            )
            .add_child(
                SunburstDataItem::new("分支C", 0.0)
                    .add_child(SunburstDataItem::new("叶子C1", 8.0).color(Color::rgb(0.2, 0.2, 1.0)))
                    .add_child(SunburstDataItem::new("叶子C2", 6.0).color(Color::rgb(0.4, 0.2, 1.0)))
                    .add_child(SunburstDataItem::new("叶子C3", 5.0).color(Color::rgb(0.6, 0.2, 1.0)))
            ),
    ];

    let label = SunburstLabel {
        show: true,
        font_size: 8.0,
        color: Color::rgb(0.0, 0.0, 0.0),
        position: SunburstLabelPosition::Middle,
        min_angle: 0.02,
        rotate: true,
    };

    let sunburst_series = SunburstSeries::new("多层级数据")
        .data(data)
        .center(0.5, 0.5)
        .radius(0.0, 0.9)
        .start_angle(0.0)
        .label(label)
        .max_depth(4)
        .level_gap(1.0)
        .border(0.8, Color::rgb(0.95, 0.95, 0.95));

    let svg = create_sunburst_svg(&sunburst_series, "多层级数据旭日图", 600.0, 600.0)?;
    fs::write(format!("{}/05_multilevel_sunburst.svg", output_dir), svg)?;
    
    println!("  ✅ 多层级数据旭日图生成完成");
    Ok(())
}

/// 创建旭日图SVG
fn create_sunburst_svg(series: &SunburstSeries, title: &str, width: f64, height: f64) -> std::result::Result<String, Box<dyn std::error::Error>> {
    use echarts_core::{CartesianCoordinateSystem, Bounds, Series, Point, Size};
    
    // 创建坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        Bounds {
            origin: Point { x: 50.0, y: 50.0 },
            size: Size { width: width - 100.0, height: height - 100.0 },
        },
        (0.0, 1.0),
        (0.0, 1.0),
    );
    
    // 获取渲染命令
    let commands = series.render_to_commands(&coord_system)?;
    
    // 生成SVG
    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 渲染命令
    for command in commands {
        render_sunburst_svg_command(&mut svg, &command);
    }
    
    svg.push_str("</svg>");
    Ok(svg)
}

/// 渲染旭日图SVG命令
fn render_sunburst_svg_command(svg: &mut String, command: &echarts_core::DrawCommand) {
    use echarts_core::DrawCommand;
    
    match command {
        DrawCommand::Path { commands, style } => {
            let mut path_data = String::new();
            for cmd in commands {
                match cmd {
                    echarts_core::draw_commands::PathCommand::MoveTo(point) => {
                        path_data.push_str(&format!("M {} {} ", point.x, point.y));
                    }
                    echarts_core::draw_commands::PathCommand::LineTo(point) => {
                        path_data.push_str(&format!("L {} {} ", point.x, point.y));
                    }
                    echarts_core::draw_commands::PathCommand::Close => {
                        path_data.push_str("Z ");
                    }
                    _ => {} // 忽略其他路径命令
                }
            }
            
            let fill = style.fill.map(|c| format!("#{:02x}{:02x}{:02x}", 
                (c.r * 255.0) as u8, (c.g * 255.0) as u8, (c.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke = style.stroke.as_ref().map(|s| format!("#{:02x}{:02x}{:02x}", 
                (s.color.r * 255.0) as u8, (s.color.g * 255.0) as u8, (s.color.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke_width = style.stroke.as_ref().map(|s| s.width).unwrap_or(0.0);
            
            svg.push_str(&format!("  <path d=\"{}\" fill=\"{}\" stroke=\"{}\" stroke-width=\"{}\" opacity=\"{}\"/>\n", 
                path_data.trim(), fill, stroke, stroke_width, style.opacity));
        }
        DrawCommand::Text { text, position, style } => {
            let color = format!("#{:02x}{:02x}{:02x}", 
                (style.color.r * 255.0) as u8, (style.color.g * 255.0) as u8, (style.color.b * 255.0) as u8);
            let rotation = if style.rotation != 0.0 {
                format!(" transform=\"rotate({} {} {})\"", style.rotation, position.x, position.y)
            } else {
                String::new()
            };
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"{}\" fill=\"{}\" text-anchor=\"middle\" opacity=\"{}\"{}>{}</text>\n", 
                position.x, position.y, style.font_size, color, style.opacity, rotation, text));
        }
        _ => {} // 忽略其他命令类型
    }
}

/// 生成展示页面
fn generate_sunburst_showcase(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts-rs 旭日图演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .section {
            background: rgba(255,255,255,0.95);
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .chart-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        .chart-svg {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h2 { color: #333; margin-bottom: 20px; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        .description {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .label-info {
            background: rgba(255,255,255,0.05);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>☀️ ECharts-rs 旭日图演示</h1>
            <p class="description">展现 SunburstSeries 的强大功能和层次数据的扇形可视化能力</p>
            <div class="feature-list">
                <div class="feature">
                    <h3>☀️ 基础旭日图</h3>
                    <p>经典的扇形映射设计</p>
                </div>
                <div class="feature">
                    <h3>🏢 组织架构</h3>
                    <p>公司结构层次展示</p>
                </div>
                <div class="feature">
                    <h3>📦 产品分类</h3>
                    <p>分类数据可视化</p>
                </div>
                <div class="feature">
                    <h3>🌳 技能树</h3>
                    <p>多层级技能展示</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>☀️ 基础旭日图功能</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">基础旭日图</div>
                    <object class="chart-svg" data="01_basic_sunburst.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">公司组织架构旭日图</div>
                    <object class="chart-svg" data="02_organization_sunburst.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📦 实际应用场景</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">产品分类旭日图</div>
                    <object class="chart-svg" data="03_product_category_sunburst.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">技能树旭日图</div>
                    <object class="chart-svg" data="04_skill_tree_sunburst.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 多层级数据</h2>
            <div class="chart-item">
                <div class="chart-title">多层级数据旭日图</div>
                <object class="chart-svg" data="05_multilevel_sunburst.svg" type="image/svg+xml">SVG不支持</object>
            </div>

            <div class="label-info">
                <h3>🏷️ 标签位置说明</h3>
                <ul>
                    <li><strong>Inside（内侧）</strong>：标签显示在扇形内侧，适合较大的扇形</li>
                    <li><strong>Middle（中间）</strong>：标签显示在扇形中间位置，平衡的选择</li>
                    <li><strong>Outside（外侧）</strong>：标签显示在扇形外侧，避免重叠</li>
                </ul>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h2>🎉 SunburstSeries 功能总结</h2>
            <p>ECharts-rs SunburstSeries 完整功能展示：</p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>✅ <strong>层次数据可视化</strong> - 支持多层级数据结构的扇形展示</li>
                <li>✅ <strong>扇形映射算法</strong> - 数值大小直观的角度表示</li>
                <li>✅ <strong>灵活的标签系统</strong> - 可配置的标签位置和旋转</li>
                <li>✅ <strong>自定义样式配置</strong> - 颜色、边框、半径等样式设置</li>
                <li>✅ <strong>层级深度控制</strong> - 可配置的最大显示层级</li>
                <li>✅ <strong>角度范围配置</strong> - 自定义起始角度和扇形范围</li>
                <li>✅ <strong>响应式布局</strong> - 自适应容器大小的扇形布局</li>
                <li>✅ <strong>高质量渲染</strong> - 优化的SVG输出和视觉效果</li>
            </ul>
        </div>
    </div>
</body>
</html>"#;

    fs::write(format!("{}/sunburst_demo.html", output_dir), html_content)?;
    Ok(())
}
