[package]
name = "image-renderer"
version = "0.1.0"
edition = "2021"
description = "Image renderer for ECharts-rs (PNG, JPEG, etc.)"
license = "MIT"
repository = "https://github.com/your-repo/echarts-rs"

[dependencies]
rust-echarts-core = { path = "../../core" }
rust-echarts-renderer = { path = ".." }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Image processing dependencies
image = { version = "0.24", features = ["png", "jpeg"] }
imageproc = "0.23"
rusttype = "0.9"
ab_glyph = "0.2"

[dev-dependencies]
tokio = { version = "1.0", features = ["full"] }
