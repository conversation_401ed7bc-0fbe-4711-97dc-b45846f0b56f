/*!
 * ECharts基础图表案例
 * 
 * 本示例展示了如何使用echarts-rs创建各种基础图表类型：
 * - 折线图 (Line Chart)
 * - 柱状图 (Bar Chart) 
 * - 饼图 (Pie Chart)
 * - 散点图 (Scatter Chart)
 */

use echarts_rs::prelude::*;
use serde_json::json;

fn main() -> Result<()> {
    println!("🎯 ECharts 基础图表案例");
    
    // 创建不同类型的图表
    create_line_chart()?;
    create_bar_chart()?;
    create_pie_chart()?;
    create_scatter_chart()?;
    
    println!("✅ 所有图表创建完成！");
    Ok(())
}

/// 创建折线图
fn create_line_chart() -> Result<()> {
    println!("\n📈 创建折线图...");
    
    let mut chart = Chart::new();
    chart = chart.title("月度销售趋势".to_string());
    
    // 配置图表选项
    let option = json!({
        "title": {
            "text": "月度销售趋势",
            "left": "center"
        },
        "tooltip": {
            "trigger": "axis"
        },
        "legend": {
            "data": ["销售额", "利润"],
            "top": "10%"
        },
        "xAxis": {
            "type": "category",
            "data": ["1月", "2月", "3月", "4月", "5月", "6月"]
        },
        "yAxis": {
            "type": "value"
        },
        "series": [
            {
                "name": "销售额",
                "type": "line",
                "data": [120, 200, 150, 80, 70, 110],
                "smooth": true,
                "itemStyle": {
                    "color": "#5470c6"
                }
            },
            {
                "name": "利润", 
                "type": "line",
                "data": [60, 100, 80, 40, 35, 55],
                "smooth": true,
                "itemStyle": {
                    "color": "#91cc75"
                }
            }
        ]
    });
    
    chart = chart.option(option);
    
    // 使用SVG渲染器输出
    let svg_output = chart.render_svg(800, 600)?;
    std::fs::write("line_chart.svg", svg_output)?;
    println!("  ✅ 折线图已保存到 line_chart.svg");
    
    Ok(())
}

/// 创建柱状图
fn create_bar_chart() -> Result<()> {
    println!("\n📊 创建柱状图...");
    
    let mut chart = Chart::new();
    
    let option = json!({
        "title": {
            "text": "产品销量对比",
            "left": "center"
        },
        "tooltip": {
            "trigger": "axis",
            "axisPointer": {
                "type": "shadow"
            }
        },
        "xAxis": {
            "type": "category",
            "data": ["产品A", "产品B", "产品C", "产品D", "产品E"]
        },
        "yAxis": {
            "type": "value"
        },
        "series": [{
            "name": "销量",
            "type": "bar",
            "data": [320, 280, 450, 200, 380],
            "itemStyle": {
                "color": "#ee6666"
            },
            "emphasis": {
                "itemStyle": {
                    "color": "#ff8080"
                }
            }
        }]
    });
    
    chart = chart.option(option);
    
    let svg_output = chart.render_svg(800, 600)?;
    std::fs::write("bar_chart.svg", svg_output)?;
    println!("  ✅ 柱状图已保存到 bar_chart.svg");
    
    Ok(())
}

/// 创建饼图
fn create_pie_chart() -> Result<()> {
    println!("\n🥧 创建饼图...");
    
    let mut chart = Chart::new();
    
    let option = json!({
        "title": {
            "text": "市场份额分布",
            "left": "center"
        },
        "tooltip": {
            "trigger": "item",
            "formatter": "{a} <br/>{b}: {c} ({d}%)"
        },
        "legend": {
            "orient": "vertical",
            "left": "left"
        },
        "series": [{
            "name": "市场份额",
            "type": "pie",
            "radius": "50%",
            "data": [
                {"value": 335, "name": "产品A"},
                {"value": 310, "name": "产品B"},
                {"value": 234, "name": "产品C"},
                {"value": 135, "name": "产品D"},
                {"value": 148, "name": "产品E"}
            ],
            "emphasis": {
                "itemStyle": {
                    "shadowBlur": 10,
                    "shadowOffsetX": 0,
                    "shadowColor": "rgba(0, 0, 0, 0.5)"
                }
            }
        }]
    });
    
    chart = chart.option(option);
    
    let svg_output = chart.render_svg(800, 600)?;
    std::fs::write("pie_chart.svg", svg_output)?;
    println!("  ✅ 饼图已保存到 pie_chart.svg");
    
    Ok(())
}

/// 创建散点图
fn create_scatter_chart() -> Result<()> {
    println!("\n🔵 创建散点图...");
    
    let mut chart = Chart::new();
    
    // 生成散点数据
    let scatter_data: Vec<[f64; 2]> = (0..50)
        .map(|i| {
            let x = i as f64 * 0.5;
            let y = (x * 0.3).sin() * 50.0 + (rand::random::<f64>() - 0.5) * 20.0;
            [x, y]
        })
        .collect();
    
    let option = json!({
        "title": {
            "text": "数据分布散点图",
            "left": "center"
        },
        "tooltip": {
            "trigger": "item",
            "formatter": "X: {c[0]}<br/>Y: {c[1]}"
        },
        "xAxis": {
            "type": "value",
            "name": "X轴"
        },
        "yAxis": {
            "type": "value", 
            "name": "Y轴"
        },
        "series": [{
            "name": "数据点",
            "type": "scatter",
            "data": scatter_data,
            "symbolSize": 8,
            "itemStyle": {
                "color": "#73c0de"
            }
        }]
    });
    
    chart = chart.option(option);
    
    let svg_output = chart.render_svg(800, 600)?;
    std::fs::write("scatter_chart.svg", svg_output)?;
    println!("  ✅ 散点图已保存到 scatter_chart.svg");
    
    Ok(())
}
