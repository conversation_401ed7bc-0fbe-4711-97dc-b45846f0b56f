use gpui::{IntoElement, ParentElement, Styled as _};
use gpui_component::{button::Button, label::Label, v_flex};
use std::sync::Arc;
use tsdaq_protocol::*;

pub fn data_collection_controls(client: Arc<TSDAQClient>) -> impl IntoElement {
    let _client_clone = Arc::clone(&client);
    // NOTE: Replace `.on_press` with the correct event handler for your Button type.
    // For example, if using gpui_component::button::Button, you might use `.on_click`.
    // If no such method exists, you may need to wrap the Button in an event handler or use a callback.

    let _client_clone = Arc::clone(&client);
    // let start_button = Button::new("Start Collection").on_click(move |cx, event, element| {
    //     let client_clone = Arc::clone(&client_clone);
    //     tokio::spawn(async move {
    //         if let Err(e) = client_clone.start_collection().await {
    //             eprintln!("[ERROR] Failed to start collection: {}", e);
    //         }
    //     });
    // });

    // let client_clone = Arc::clone(&client);
    // let stop_button = Button::new("Stop Collection").on_click(move |cx, event, element| {
    //     let client_clone = Arc::clone(&client_clone);
    //     tokio::spawn(async move {
    //         if let Err(e) = client_clone.stop_collection().await {
    //             eprintln!("[ERROR] Failed to stop collection: {}", e);
    //         }
    //     });
    // });

    let _client_clone = Arc::clone(&client);
    // let save_button = Button::new("Save Data").on_click(move |cx, event, element| {
    //     let client_clone = Arc::clone(&client_clone);
    //     tokio::spawn(async move {
    //         if let Err(e) = client_clone.save_data("data.csv").await {
    //             eprintln!("[ERROR] Failed to save data: {}", e);
    //         }
    //     });
    // });
    v_flex()
        .gap_10()
        .child(Label::new("start_button"))
        .child(Label::new("stop_button"))
        .child(Label::new("save_button"))
        // .child(start_button)
        // .child(stop_button)
        // .child(save_button)
        .into_element()
}
pub fn data_display(client: Arc<TSDAQClient>) -> impl IntoElement {
    let _client_clone = Arc::clone(&client);
    // let data_text = async move {
    //     let data = client_clone.get_latest_data().await;
    //     match data {
    //         Ok(data) => format!("Latest data: {:?}", data),
    //         Err(e) => format!("Error fetching data: {}", e),
    //     }
    // };
    let _data_text = "DDDDSDGSGFS";
    v_flex()
        .gap_10()
        .child(Label::new("Data Display"))
        // .child(text(data_text))
        .into_element()
}

pub fn sensor_management_panel(_client: Arc<TSDAQClient>) -> impl IntoElement {
    // let sensors = futures::executor::block_on(client.get_sensors());

    // let sensor_items = sensors
    //     .into_iter()
    //     .map(|(id, sensor)| {
    //         let info = sensor.info();

    //         column(vec![
    //             Text::new(format!("ID: {}", id)).size(14).bold(),
    //             Text::new(format!("Name: {}", info.name)),
    //             Text::new(format!("Model: {}", info.model)),
    //             Text::new(format!("Manufacturer: {}", info.manufacturer)),
    //             Text::new(format!("Channels: {}", info.supported_channels)),
    //             Button::new("Configure"),
    //         ])

    //     })
    //     .collect::<Vec<_>>();
    v_flex()
        .gap_10()
        .child(Label::new("Connected Sensors"))
        .child(
            v_flex()
                .gap_10()
                .child(Label::new("No sensors connected"))
                .child(Button::new("Connect")),
        )
        .into_element()
}
