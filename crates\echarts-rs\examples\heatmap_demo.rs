//! 热力图演示
//!
//! 展示热力图、矩阵数据和颜色映射的使用方法

use echarts_rs::prelude::*;
use echarts_rs::{HeatmapSeries, ColorMap, ColorMapType, ChartBuilder};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🔥 热力图演示");

    // 1. 基础热力图（从矩阵数据）
    println!("\n📊 基础热力图:");
    
    let matrix = vec![
        vec![1.0, 2.0, 3.0, 4.0, 5.0],
        vec![2.0, 4.0, 6.0, 8.0, 10.0],
        vec![3.0, 6.0, 9.0, 12.0, 15.0],
        vec![4.0, 8.0, 12.0, 16.0, 20.0],
        vec![5.0, 10.0, 15.0, 20.0, 25.0],
    ];
    
    let basic_heatmap = HeatmapSeries::new("基础热力图")
        .from_matrix(matrix)
        .gap(1.0)
        .border(1.0, Color::rgb(1.0, 1.0, 1.0))
        .show_label(true);
    
    println!("  ✅ 基础热力图创建成功");
    println!("  - 名称: {}", basic_heatmap.name);
    println!("  - 类型: {:?}", basic_heatmap.series_type());
    println!("  - 网格大小: {:?}", basic_heatmap.grid_size);
    println!("  - 数据项数: {}", basic_heatmap.data.len());
    println!("  - 数据范围: {:?}", basic_heatmap.data_range);
    
    // 2. 自定义颜色映射的热力图
    println!("\n🌈 自定义颜色映射:");
    
    let custom_colors = vec![
        Color::rgb(0.0, 0.0, 0.5),   // 深蓝色
        Color::rgb(0.0, 0.5, 1.0),   // 蓝色
        Color::rgb(0.0, 1.0, 1.0),   // 青色
        Color::rgb(0.5, 1.0, 0.0),   // 绿色
        Color::rgb(1.0, 1.0, 0.0),   // 黄色
        Color::rgb(1.0, 0.5, 0.0),   // 橙色
        Color::rgb(1.0, 0.0, 0.0),   // 红色
    ];
    
    let color_map = ColorMap::new(custom_colors, (0.0, 100.0))
        .map_type(ColorMapType::Linear);
    
    let custom_heatmap = HeatmapSeries::new("自定义颜色映射")
        .data(vec![
            (0, 0, 10.0), (1, 0, 20.0), (2, 0, 30.0),
            (0, 1, 40.0), (1, 1, 50.0), (2, 1, 60.0),
            (0, 2, 70.0), (1, 2, 80.0), (2, 2, 90.0),
        ])
        .color_map(color_map)
        .gap(2.0)
        .border_radius(5.0)
        .show_label(true)
        .label_formatter("{value}°C");
    
    println!("  ✅ 自定义颜色映射热力图创建成功");
    println!("  - 网格大小: {:?}", custom_heatmap.grid_size);
    println!("  - 颜色数量: {}", custom_heatmap.color_map.colors.len());
    println!("  - 映射类型: {:?}", custom_heatmap.color_map.map_type);
    
    // 3. 分段颜色映射
    println!("\n📈 分段颜色映射:");
    
    let piecewise_colors = vec![
        Color::rgb(0.0, 1.0, 0.0),   // 绿色（低）
        Color::rgb(1.0, 1.0, 0.0),   // 黄色（中）
        Color::rgb(1.0, 0.0, 0.0),   // 红色（高）
    ];
    
    let piecewise_map = ColorMap::new(piecewise_colors, (0.0, 50.0))
        .map_type(ColorMapType::Piecewise)
        .pieces(3);
    
    let piecewise_heatmap = HeatmapSeries::new("分段颜色映射")
        .data(vec![
            (0, 0, 5.0),  (1, 0, 15.0), (2, 0, 25.0), (3, 0, 35.0),
            (0, 1, 10.0), (1, 1, 20.0), (2, 1, 30.0), (3, 1, 40.0),
            (0, 2, 12.0), (1, 2, 22.0), (2, 2, 32.0), (3, 2, 42.0),
        ])
        .color_map(piecewise_map)
        .gap(1.5)
        .show_label(true);
    
    println!("  ✅ 分段颜色映射热力图创建成功");
    println!("  - 分段数量: {:?}", piecewise_heatmap.color_map.pieces);
    
    // 4. 反转颜色映射
    println!("\n🔄 反转颜色映射:");
    
    let inverted_map = ColorMap::default()
        .invert(true);
    
    let inverted_heatmap = HeatmapSeries::new("反转颜色映射")
        .data(vec![
            (0, 0, 10.0), (1, 0, 30.0), (2, 0, 50.0),
            (0, 1, 20.0), (1, 1, 40.0), (2, 1, 60.0),
        ])
        .color_map(inverted_map)
        .show_label(true);
    
    println!("  ✅ 反转颜色映射热力图创建成功");
    println!("  - 颜色反转: {}", inverted_heatmap.color_map.invert);
    
    // 5. 测试渲染
    println!("\n🎨 渲染测试:");
    
    // 创建坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 400.0, 400.0),
        (0.0, 1.0),
        (0.0, 1.0),
    );
    
    // 渲染基础热力图
    match basic_heatmap.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 基础热力图渲染成功: {} 个绘制命令", commands.len());
            
            // 统计不同类型的命令
            let mut rect_count = 0;
            let mut text_count = 0;
            
            for cmd in &commands {
                match cmd {
                    DrawCommand::Rect { .. } => rect_count += 1,
                    DrawCommand::Text { .. } => text_count += 1,
                    _ => {}
                }
            }
            
            println!("    - 矩形命令: {}", rect_count);
            println!("    - 文本命令: {}", text_count);
        }
        Err(e) => {
            println!("  ❌ 渲染失败: {}", e);
        }
    }
    
    // 渲染自定义颜色映射热力图
    match custom_heatmap.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 自定义颜色映射热力图渲染成功: {} 个绘制命令", commands.len());
        }
        Err(e) => {
            println!("  ❌ 渲染失败: {}", e);
        }
    }
    
    // 6. 测试颜色映射
    println!("\n🎨 颜色映射测试:");
    
    let test_map = ColorMap::default();
    
    println!("  测试颜色映射 (范围: {:?}):", test_map.range);
    for value in [0.0, 25.0, 50.0, 75.0, 100.0] {
        let color = test_map.get_color(value);
        println!("    值 {}: RGB({:.2}, {:.2}, {:.2})", 
                 value, color.r, color.g, color.b);
    }
    
    // 7. 测试类型擦除
    println!("\n🔄 测试类型擦除:");
    
    let series_list: Vec<Box<dyn Series>> = vec![
        Box::new(basic_heatmap),
        Box::new(custom_heatmap),
        Box::new(piecewise_heatmap),
        Box::new(inverted_heatmap),
    ];
    
    println!("  ✅ 类型擦除成功 - 可以在同一容器中存储不同配置的热力图");
    for (i, series) in series_list.iter().enumerate() {
        println!("    {}. {} ({})", i + 1, series.name(), series.series_type().as_str());
    }
    
    // 8. 创建完整图表
    println!("\n📈 创建完整图表:");
    
    let temperature_matrix = vec![
        vec![15.2, 16.8, 18.5, 20.1, 22.3],
        vec![14.1, 15.9, 17.8, 19.6, 21.4],
        vec![13.5, 15.2, 17.1, 18.9, 20.8],
        vec![12.8, 14.6, 16.5, 18.3, 20.1],
        vec![12.1, 13.9, 15.8, 17.6, 19.5],
    ];
    
    let chart = Chart::new()
        .title("温度分布热力图")
        .size(600.0, 500.0)
        .background_color(Color::rgb(0.98, 0.98, 0.98))
        .add_series(Box::new(HeatmapSeries::new("温度分布")
            .from_matrix(temperature_matrix)
            .gap(1.0)
            .border_radius(3.0)
            .show_label(true)
            .label_formatter("{value}°C")));
    
    println!("  ✅ 完整图表创建成功");
    println!("  - 标题: {:?}", chart.title);
    println!("  - 大小: {}x{}", chart.width, chart.height);
    println!("  - 系列数: {}", chart.series.len());
    
    // 9. 使用 ChartBuilder
    println!("\n🏗️ 使用 ChartBuilder:");
    
    let sales_data = vec![
        (0, 0, 120.0), (1, 0, 132.0), (2, 0, 101.0), (3, 0, 134.0), (4, 0, 90.0),
        (0, 1, 220.0), (1, 1, 182.0), (2, 1, 191.0), (3, 1, 234.0), (4, 1, 290.0),
        (0, 2, 150.0), (1, 2, 232.0), (2, 2, 201.0), (3, 2, 154.0), (4, 2, 190.0),
        (0, 3, 320.0), (1, 3, 332.0), (2, 3, 301.0), (3, 3, 334.0), (4, 3, 390.0),
    ];
    
    let heatmap_chart = ChartBuilder::new()
        .title("销售热力图")
        .size(800.0, 600.0)
        .add_series(HeatmapSeries::new("销售数据")
            .data(sales_data)
            .gap(2.0)
            .border_radius(4.0)
            .show_label(true)
            .label_formatter("${value}k"))
        .build();
    
    println!("  ✅ 使用 ChartBuilder 创建热力图成功");
    println!("  - 标题: {:?}", heatmap_chart.title);
    println!("  - 大小: {}x{}", heatmap_chart.width, heatmap_chart.height);
    println!("  - 系列数: {}", heatmap_chart.series.len());
    
    println!("\n🎉 热力图演示完成！");
    println!("✨ HeatmapSeries 提供了丰富的热力图功能，包括矩阵数据和颜色映射");
    
    Ok(())
}
