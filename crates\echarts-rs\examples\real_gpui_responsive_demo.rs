//! 真实 GPUI 响应式边界更新演示
//!
//! 本演示展示了如何在真实的 GPUI 应用中实现响应式图表：
//! 1. 使用 GPUI 的 BackgroundExecutor 进行异步处理
//! 2. 监听窗口大小变化事件
//! 3. 自动更新坐标系统和绘制命令
//! 4. 实现真正的响应式图表界面

use echarts_rs::{Bounds as EchartsBounds, CartesianCoordinateSystem, Color, LineSeries, Series};
use echarts_core::{DrawCommand, LineStyle, TextStyle, Point, Chart};
use gpui::*;
use gpui_renderer::{EChartsCanvas, EChartsElement, GpuiCommandUpdater, ResponsiveChartState};
use std::sync::Arc;

fn generate_test_data(n: usize) -> Vec<Pixels> {
    (0..n)
        .map(|i| {
            let x = i as f32 * 0.1;
            px(x.sin())
        })
        .collect()
}

/// 创建优化的正弦波系列
fn create_optimized_sine_series() -> Box<dyn Series> {
    println!("📈 创建500点正弦波系列...");

    // 生成500个正弦波数据点
    let data: Vec<(f64, f64)> = (0..500)
        .map(|i| {
            let x = i as f64;
            let y = (x * 0.02).sin() * 50.0 + 50.0; // 正弦波，范围0-100
            (x, y)
        })
        .collect();

    println!("📊 生成了 {} 个正弦波数据点", data.len());

    Box::new(
        LineSeries::new("500点正弦波")
            .data(data)
            .color(Color::rgb(0.1, 0.5, 0.9))
            .line_width(2.0)
            .smooth(false), // 大数据量时关闭平滑以提高性能
    )
}

fn main() {
    println!("🚀 启动优化的500点正弦波响应式演示...");

    Application::new().run(move |cx| {
        println!("📱 GPUI 应用程序上下文已创建");

        // 优化的窗口配置
        let window_size = size(px(1200.0), px(800.0)); // 更大的默认窗口
        
        let _window = cx.open_window(
            WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(Bounds::centered(
                    None,
                    window_size,
                    cx,
                ))),
                titlebar: Some(TitlebarOptions {
                    title: Some("� 500点正弦波响应式图表 - ECharts-rs".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                window_background: WindowBackgroundAppearance::Opaque,
                app_id: None,
                window_min_size: Some(size(px(800.0), px(600.0))), // 最小尺寸
                window_decorations: None,
            },
            |_window, cx| {
                println!("✅ 窗口已创建，正在初始化500点正弦波演示...");
                cx.new(|cx| RealGpuiResponsiveDemo::new(cx))
            },
        );
    });
}

/// 真实 GPUI 响应式演示组件
struct RealGpuiResponsiveDemo {
    /// 当前系列
    current_series: Box<dyn Series>,
    /// 响应式图表状态（使用真实的 GPUI BackgroundExecutor）
    chart_state: ResponsiveChartState,
    /// GPUI 命令更新器
    command_updater: GpuiCommandUpdater,
    /// 更新计数器（本地）
    local_update_count: usize,
    /// 当前窗口尺寸
    current_window_size: Option<Size<Pixels>>,
}

impl Clone for RealGpuiResponsiveDemo {
    fn clone(&self) -> Self {
        Self {
            current_series: self.current_series.clone_series(),
            chart_state: self.chart_state.clone(),
            command_updater: self.command_updater.clone(),
            local_update_count: self.local_update_count,
            current_window_size: self.current_window_size,
        }
    }
}

impl RealGpuiResponsiveDemo {
    fn new(cx: &mut Context<Self>) -> Self {
        println!("🎯 初始化优化的500点正弦波演示组件");

        // 创建优化的正弦波系列
        let current_series = create_optimized_sine_series();
        println!("📊 500点正弦波系列已创建");

        // 获取 GPUI 的 BackgroundExecutor
        let bg_executor = Arc::new(cx.background_executor().clone());
        println!("🧵 获取到真实的 GPUI BackgroundExecutor");

        // 创建适配500点数据的坐标系统
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(80.0, 60.0, 920.0, 680.0), // 留出更多空间给轴标签
            (0.0, 500.0), // x 轴范围：0-500
            (0.0, 100.0), // y 轴范围：0-100（正弦波范围）
        );

        // 创建响应式图表状态
        let chart_state = ResponsiveChartState::new(coord_system, bg_executor);
        let command_updater = chart_state.create_command_updater();

        println!("📐 优化的500点数据响应式状态已创建");

        Self {
            current_series,
            chart_state,
            command_updater,
            local_update_count: 0,
            current_window_size: None,
        }
    }

    /// 生成复合正弦波数据
    fn generate_complex_sine_wave(count: usize) -> Vec<(f64, f64)> {
        println!("🌊 生成复合正弦波数据，点数: {}", count);

        (0..count)
            .map(|i| {
                let x = i as f64;
                // 复合正弦波：主波 + 高频波 + 低频波 + 噪声
                let main_wave = 1.0 * (x * 0.01).sin(); // 主波
                                                            // let high_freq = 1500.0 * (x * 0.01).sin();           // 高频波
                                                            // let low_freq = 2000.0 * (x * 0.0003).sin();          // 低频波
                                                            // let noise = 300.0 * (x * 0.05).sin() * (x * 0.03).cos(); // 噪声

                let y = main_wave;
                (x, y)
            })
            .collect()
        // (0..count)
        //     .map(|i| {
        //         let x = i as f64;
        //         // 复合正弦波：主波 + 高频波 + 低频波 + 噪声
        //         let main_wave = 6000.0 * (x * 0.001).sin();           // 主波
        //         let high_freq = 1500.0 * (x * 0.01).sin();           // 高频波
        //         let low_freq = 2000.0 * (x * 0.0003).sin();          // 低频波
        //         let noise = 300.0 * (x * 0.05).sin() * (x * 0.03).cos(); // 噪声

        //         let y = 6000.0 + main_wave + high_freq + low_freq + noise;
        //         (x, y)
        //     })
        //     .collect()
    }

    /// 创建动态线图系列
    fn create_dynamic_line_series() -> Box<dyn Series> {
        println!("📈 创建大数据量动态折线图系列...");

        // 创建大数据量的复合正弦波数据
        let data = Self::generate_complex_sine_wave(100);

        println!("📊 生成了 {} 个复合正弦波数据点", data.len());
        println!(
            "🔍 数据范围: X(0.0 - {:.1}), Y({:.1} - {:.1})",
            data.last().map(|(x, _)| *x).unwrap_or(0.0),
            data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min),
            data.iter()
                .map(|(_, y)| *y)
                .fold(f64::NEG_INFINITY, f64::max)
        );

        Box::new(
            LineSeries::new("大数据量复合正弦波")
                .data(data)
                .color(Color::rgb(0.1, 0.5, 0.9))
                .line_width(2.0) // 大数据量时使用较细的线条
                .smooth(false), // 大数据量时关闭平滑以提高性能
        )
    }

    /// 优化的 GPUI 响应式更新处理（500点数据）
    fn handle_bounds_change_gpui(&mut self, bounds: Bounds<Pixels>, cx: &mut Context<Self>) {
        println!("\n🔄 处理500点数据的边界变化...");
        println!(
            "📏 新边界: {:.1}x{:.1} at ({:.1}, {:.1})",
            bounds.size.width.0, bounds.size.height.0, bounds.origin.x.0, bounds.origin.y.0
        );

        // 检查边界是否真的变化了
        let size_changed = self
            .current_window_size
            .map(|current_size| current_size != bounds.size)
            .unwrap_or(true);

        if !size_changed {
            println!("⏭️  窗口尺寸未变化，跳过更新");
            return;
        }

        // 更新本地状态
        self.current_window_size = Some(bounds.size);
        self.local_update_count += 1;

        println!("📊 第 {} 次500点数据边界更新", self.local_update_count);

        // 计算图表区域（留出更多轴标签空间）
        let margin_left = 100.0;  // 增加左边距，为Y轴标签留出更多空间
        let margin_right = 50.0;  // 增加右边距
        let margin_top = 80.0;    // 增加上边距
        let margin_bottom = 100.0; // 增加下边距，为X轴标签留出更多空间

        let chart_width = bounds.size.width.0 as f64 - margin_left - margin_right;
        let chart_height = bounds.size.height.0 as f64 - margin_top - margin_bottom;

        // 转换边界格式
        let echarts_bounds = EchartsBounds::new(
            margin_left,
            margin_top,
            chart_width,
            chart_height,
        );

        // 固定的数据范围（适配500点正弦波）
        let x_range = (0.0, 499.0); // 500个数据点，索引0-499
        let y_range = (0.0, 100.0);  // 正弦波范围0-100

        // 创建新的坐标系统
        let new_coord_system = CartesianCoordinateSystem::new(echarts_bounds, x_range, y_range);

        // 更新共享坐标系统
        if let Err(e) = self.chart_state.update_coord_system(new_coord_system.clone()) {
            println!("❌ 更新坐标系统失败: {:?}", e);
            return;
        }

        // 在 GPUI 后台执行器中重新生成绘制命令
        let series = self.current_series.clone_series();
        let command_updater = self.command_updater.clone();
        let update_count = self.local_update_count;

        // 使用真实的 GPUI BackgroundExecutor
        cx.background_executor()
            .spawn(async move {
                println!("🧵 GPUI 后台执行器开始生成500点绘制命令 (更新 {})", update_count);

                match series.render_to_commands(&new_coord_system) {
                    Ok(mut new_commands) => {
                        // 添加坐标轴绘制命令（在数据线之后绘制，确保在最上层）
                        let axis_commands = Self::generate_axis_commands(&new_coord_system, x_range, y_range);
                        new_commands.extend(axis_commands);

                        // 异步更新命令
                        command_updater.update_async(new_commands.clone());

                        println!("✅ GPUI 后台执行器更新成功 (更新 {}):", update_count);
                        println!(
                            "   📏 图表区域: {:.1}x{:.1} at ({:.1}, {:.1})",
                            echarts_bounds.size.width,
                            echarts_bounds.size.height,
                            echarts_bounds.origin.x,
                            echarts_bounds.origin.y
                        );
                        println!(
                            "   🎯 数据范围: X({:.1}, {:.1}), Y({:.1}, {:.1})",
                            x_range.0, x_range.1, y_range.0, y_range.1
                        );
                        println!("   🎨 绘制命令: {} 个（含坐标轴）", new_commands.len());
                    }
                    Err(e) => {
                        println!(
                            "❌ GPUI 后台执行器更新失败 (更新 {}): {:?}",
                            update_count, e
                        );
                    }
                }
            })
            .detach();
    }

    /// 生成坐标轴绘制命令
    fn generate_axis_commands(
        coord_system: &CartesianCoordinateSystem,
        x_range: (f64, f64),
        y_range: (f64, f64),
    ) -> Vec<DrawCommand> {
        let mut commands = Vec::new();
        let bounds = coord_system.bounds;

        // X轴线（加粗并使用更深的颜色）
        commands.push(DrawCommand::Line {
            from: Point::new(bounds.origin.x, bounds.origin.y + bounds.size.height),
            to: Point::new(bounds.origin.x + bounds.size.width, bounds.origin.y + bounds.size.height),
            style: LineStyle {
                color: Color::rgb(0.1, 0.1, 0.1), // 更深的颜色
                width: 2.0, // 加粗线条
                ..Default::default()
            },
        });

        // Y轴线（加粗并使用更深的颜色）
        commands.push(DrawCommand::Line {
            from: Point::new(bounds.origin.x, bounds.origin.y),
            to: Point::new(bounds.origin.x, bounds.origin.y + bounds.size.height),
            style: LineStyle {
                color: Color::rgb(0.1, 0.1, 0.1), // 更深的颜色
                width: 2.0, // 加粗线条
                ..Default::default()
            },
        });

        // X轴刻度和标签（每50个点一个刻度）
        for i in (0..=500).step_by(50) {
            let x = bounds.origin.x + (i as f64 / x_range.1) * bounds.size.width;
            let y = bounds.origin.y + bounds.size.height;

            // 刻度线（加粗并使用更深的颜色）
            commands.push(DrawCommand::Line {
                from: Point::new(x, y),
                to: Point::new(x, y + 8.0), // 加长刻度线
                style: LineStyle {
                    color: Color::rgb(0.2, 0.2, 0.2), // 更深的颜色
                    width: 1.5, // 稍微加粗
                    ..Default::default()
                },
            });

            // X轴标签（更大更清晰）
            commands.push(DrawCommand::Text {
                text: i.to_string(),
                position: Point::new(x, y + 25.0), // 稍微远离轴线
                style: TextStyle {
                    color: Color::rgb(0.1, 0.1, 0.1), // 更深的颜色
                    font_size: 14.0, // 更大的字体
                    ..Default::default()
                },
            });
        }

        // Y轴刻度和标签（每20个单位一个刻度）
        for i in (0..=100).step_by(20) {
            let x = bounds.origin.x;
            let y = bounds.origin.y + bounds.size.height - (i as f64 / y_range.1) * bounds.size.height;

            // 刻度线（加粗并使用更深的颜色）
            commands.push(DrawCommand::Line {
                from: Point::new(x - 8.0, y), // 加长刻度线
                to: Point::new(x, y),
                style: LineStyle {
                    color: Color::rgb(0.2, 0.2, 0.2), // 更深的颜色
                    width: 1.5, // 稍微加粗
                    ..Default::default()
                },
            });

            // Y轴标签（更大更清晰）
            commands.push(DrawCommand::Text {
                text: i.to_string(),
                position: Point::new(x - 35.0, y + 5.0), // 稍微远离轴线
                style: TextStyle {
                    color: Color::rgb(0.1, 0.1, 0.1), // 更深的颜色
                    font_size: 14.0, // 更大的字体
                    ..Default::default()
                },
            });
        }

        // 添加网格线，让坐标轴更加明显
        // X轴网格线（垂直线）
        for i in (0..=500).step_by(100) {
            let x = bounds.origin.x + (i as f64 / x_range.1) * bounds.size.width;
            commands.push(DrawCommand::Line {
                from: Point::new(x, bounds.origin.y),
                to: Point::new(x, bounds.origin.y + bounds.size.height),
                style: LineStyle {
                    color: Color::rgb(0.9, 0.9, 0.9), // 浅灰色网格线
                    width: 0.5,
                    ..Default::default()
                },
            });
        }

        // Y轴网格线（水平线）
        for i in (0..=100).step_by(20) {
            let y = bounds.origin.y + bounds.size.height - (i as f64 / y_range.1) * bounds.size.height;
            commands.push(DrawCommand::Line {
                from: Point::new(bounds.origin.x, y),
                to: Point::new(bounds.origin.x + bounds.size.width, y),
                style: LineStyle {
                    color: Color::rgb(0.9, 0.9, 0.9), // 浅灰色网格线
                    width: 0.5,
                    ..Default::default()
                },
            });
        }

        commands
    }

    /// 创建优化的图表（使用手动绘制的坐标轴和网格）
    fn create_optimized_chart(&self, bounds: Bounds<Pixels>) -> Chart {
        println!("🎨 创建优化的图表（包含手动绘制的坐标轴和网格）...");

        // 使用简化的 Chart 结构，专注于数据展示
        Chart::new()
            .title("500点正弦波响应式演示")
            .size(bounds.size.width.into(), bounds.size.height.into())
            .background_color(Color::rgb(0.98, 0.99, 1.0)) // 浅蓝色背景
            .padding(100.0, 50.0, 100.0, 100.0) // 为坐标轴留出足够空间
            .add_series(Box::new(self.current_series.clone_series()))
    }
}

impl Render for RealGpuiResponsiveDemo {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("🎨 渲染优化的500点正弦波演示界面");

        div()
            .flex()
            .flex_col()
            .size_full()
            .bg(rgb(0xf8f9fa))
            .child(
                // 优化的标题栏
                div()
                    .flex()
                    .items_center()
                    .justify_center()
                    .h(px(60.0))
                    .bg(rgb(0x1976d2))
                    .text_color(rgb(0xffffff))
                    .child(
                        div()
                            .text_xl()
                            .font_weight(FontWeight::BOLD)
                            .child("📈 500点正弦波响应式图表演示")
                    )
            )
            .child(
                // 优化的信息面板
                div()
                    .flex()
                    .justify_between()
                    .items_center()
                    .p_4()
                    .bg(rgb(0xe3f2fd))
                    .border_b_1()
                    .border_color(rgb(0xbbdefb))
                    .child(
                        div()
                            .flex()
                            .gap_4()
                            .child(
                                div()
                                    .text_sm()
                                    .font_weight(FontWeight::MEDIUM)
                                    .child(format!("📊 数据点: 500个"))
                            )
                            .child(
                                div()
                                    .text_sm()
                                    .child(format!("🔄 更新次数: {}", self.local_update_count))
                            )
                            .child(
                                div()
                                    .text_sm()
                                    .child(format!("🧵 共享更新: {}", self.chart_state.get_update_count()))
                            )
                    )
                    .child(
                        div()
                            .flex()
                            .gap_4()
                            .child(
                                div()
                                    .text_sm()
                                    .child(
                                        if let Some(size) = self.current_window_size {
                                            format!("📐 尺寸: {:.0}×{:.0}",
                                                size.width.0,
                                                size.height.0
                                            )
                                        } else {
                                            "📐 等待尺寸...".to_string()
                                        }
                                    )
                            )
                            .child(
                                div()
                                    .text_sm()
                                    .child(
                                        if let Some(commands) = self.chart_state.get_commands() {
                                            format!("🎨 命令: {}个", commands.len())
                                        } else {
                                            "🎨 生成中...".to_string()
                                        }
                                    )
                            )
                    )
            )
            .child(
                // 优化的图表区域
                div()
                    .flex_1()
                    .p_4()
                    .child(
                        div()
                            .size_full()
                            .bg(rgb(0xffffff))
                            .border_1()
                            .border_color(rgb(0xe0e0e0))
                            .rounded_lg()
                            .shadow_md()
                            .child({
                                // 使用优化的 EChartsElement
                                EChartsElement::new(
                                    EChartsCanvas::new(
                                        self.current_series.clone_series(),
                                        self.chart_state.get_coord_system().unwrap_or_else(|| {
                                            // 提供默认坐标系统
                                            CartesianCoordinateSystem::new(
                                                EchartsBounds::new(80.0, 60.0, 920.0, 680.0),
                                                (0.0, 500.0),
                                                (0.0, 100.0),
                                            )
                                        }),
                                    ).with_debug(false) // 生产环境关闭调试
                                )
                                .with_bounds_callback({
                                    let _demo = self.clone();
                                    move |bounds| {
                                        // 这里需要通过其他方式触发更新
                                        // 因为闭包中无法直接调用 handle_bounds_change_gpui
                                        println!("📏 图表边界变化: {:?}", bounds);
                                        // 在实际应用中，可以通过消息传递或状态管理来触发更新
                                    }
                                })
                            })
                    )
            )
            .child(
                // 性能信息栏
                div()
                    .h(px(40.0))
                    .bg(rgb(0xf5f5f5))
                    .border_t_1()
                    .border_color(rgb(0xe0e0e0))
                    .flex()
                    .items_center()
                    .justify_center()
                    .child(
                        div()
                            .text_xs()
                            .text_color(rgb(0x666666))
                            .child("🚀 优化版本 | 500点正弦波 | 响应式坐标轴 | GPUI后台渲染")
                    )
            )
    }
}




