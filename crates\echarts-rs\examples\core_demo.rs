//! 核心功能演示 - 只使用 echarts-core
//! 
//! 这个示例展示了 echarts-core 的基本功能，不依赖有编译问题的组件

use echarts_core::{Bounds, Point, Color, ChartRenderer, Result, TextStyle, Path, style::FontWeight};

/// 简单的控制台渲染器，用于演示
struct ConsoleRenderer;

impl ChartRenderer for ConsoleRenderer {
    fn draw_rect(&mut self, bounds: Bounds, fill: Option<Color>, stroke: Option<Color>, stroke_width: f64) -> Result<()> {
        println!("🔲 绘制矩形: bounds={:?}, fill={:?}, stroke={:?}, width={}", 
                 bounds, fill, stroke, stroke_width);
        Ok(())
    }

    fn draw_circle(&mut self, center: Point, radius: f64, fill: Option<Color>, stroke: Option<Color>, stroke_width: f64) -> Result<()> {
        println!("⭕ 绘制圆形: center={:?}, radius={}, fill={:?}, stroke={:?}, width={}", 
                 center, radius, fill, stroke, stroke_width);
        Ok(())
    }

    fn draw_text(&mut self, text: &str, position: Point, style: &TextStyle) -> Result<()> {
        println!("📝 绘制文本: '{}' at {:?}, style={:?}", text, position, style);
        Ok(())
    }

    fn draw_path(&mut self, path: &Path, fill: Option<Color>, stroke: Option<(Color, f64)>) -> Result<()> {
        println!("🛤️ 绘制路径: fill={:?}, stroke={:?}", fill, stroke);
        Ok(())
    }

    fn capabilities(&self) -> echarts_core::RendererCapabilities {
        echarts_core::RendererCapabilities {
            supports_animation: false,
            supports_interaction: false,
            supports_text_measurement: false,
            max_texture_size: None,
        }
    }
}

fn main() -> Result<()> {
    println!("🚀 ECharts-rs 核心功能演示");
    println!("📋 只使用 echarts-core，验证基础架构");
    println!();

    let mut renderer = ConsoleRenderer;
    let bounds = Bounds::new(0.0, 0.0, 800.0, 600.0);

    // 演示1: 基本绘制功能
    println!("📊 演示1: 基本绘制功能");
    
    // 绘制背景
    renderer.draw_rect(
        bounds,
        Some(Color::rgba(250.0, 250.0, 250.0, 1.0)),
        Some(Color::rgba(200.0, 200.0, 200.0, 1.0)),
        1.0
    )?;

    // 绘制标题
    let title_style = TextStyle {
        font_family: "Arial".to_string(),
        font_size: 16.0,
        color: Color::BLACK,
        font_weight: FontWeight::Bold,
        font_style: echarts_core::style::FontStyle::Normal,
        text_align: echarts_core::style::TextAlign::Center,
        text_baseline: echarts_core::style::TextBaseline::Top,
        line_height: 1.2,
        letter_spacing: 0.0,
    };
    let title_pos = Point::new(bounds.width() / 2.0, 30.0);
    renderer.draw_text("ECharts-rs 核心演示", title_pos, &title_style)?;

    // 绘制一些数据点
    let data_points = vec![
        (100.0, 120.0), (200.0, 200.0), (300.0, 150.0),
        (400.0, 80.0), (500.0, 70.0), (600.0, 110.0)
    ];

    for (i, &(x, y)) in data_points.iter().enumerate() {
        let screen_x = bounds.left() + x;
        let screen_y = bounds.bottom() - y;
        
        renderer.draw_circle(
            Point::new(screen_x, screen_y),
            5.0,
            Some(Color::BLUE),
            Some(Color::rgba(0.0, 0.0, 150.0, 1.0)),
            2.0
        )?;
        
        println!("  📍 数据点 {}: ({}, {}) -> 屏幕坐标 ({:.1}, {:.1})", 
                 i + 1, x, y, screen_x, screen_y);
    }

    println!();
    println!("✅ 核心功能演示完成！");
    println!("💡 这展示了 echarts-core 的基础功能：");
    println!("   - ChartRenderer trait 正常工作");
    println!("   - 基本图形绘制功能");
    println!("   - 颜色和样式系统");
    println!("   - 坐标系统");
    
    Ok(())
}
