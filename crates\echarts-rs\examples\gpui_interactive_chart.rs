/*!
 * ECharts GPUI 交互式图表案例
 * 
 * 本示例展示了如何创建一个交互式的ECharts图表，
 * 用户可以通过按钮切换不同的图表类型和数据。
 */

use echarts_rs::{prelude::*, ChartExt};
use gpui::{
    div, px, rgb, size, AppContext, Context, IntoElement, ParentElement, Render,
    Styled, Window, WindowBounds, WindowKind, WindowOptions, TitlebarOptions,
    WindowBackgroundAppearance, Application, ClickEvent, InteractiveElement, FontWeight,
};
use gpui::Bounds as GpuiBounds;
use serde_json::json;

fn main() {
    println!("🎮 启动 ECharts 交互式图表");
    
    let app = Application::new();
    
    app.run(move |cx| {
        let window_size = size(px(900.0), px(700.0));
        
        let _window = cx.open_window(
            WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(GpuiBounds::centered(None, window_size, cx))),
                titlebar: Some(TitlebarOptions {
                    title: Some("交互式 ECharts 图表".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                window_background: WindowBackgroundAppearance::Opaque,
                app_id: None,
                window_min_size: Some(size(px(600.0), px(500.0))),
                window_decorations: None,
            },
            |_window, cx| {
                println!("🪟 交互式图表窗口已创建");
                cx.new(|cx| InteractiveChart::new(cx))
            },
        );
    });
}

/// 图表类型枚举
#[derive(Debug, Clone, Copy, PartialEq)]
enum ChartType {
    Line,
    Bar,
    Pie,
    Scatter,
}

/// 交互式图表结构
struct InteractiveChart {
    /// 当前图表类型
    current_type: ChartType,
    /// 当前图表实例
    current_chart: Chart,
    /// 示例数据
    sample_data: Vec<(String, f64)>,
}

impl InteractiveChart {
    fn new(_cx: &mut Context<Self>) -> Self {
        println!("📊 初始化交互式图表");
        
        let sample_data = vec![
            ("产品A".to_string(), 320.0),
            ("产品B".to_string(), 280.0),
            ("产品C".to_string(), 450.0),
            ("产品D".to_string(), 200.0),
            ("产品E".to_string(), 380.0),
        ];
        
        let mut instance = Self {
            current_type: ChartType::Line,
            current_chart: Chart::new(),
            sample_data,
        };
        
        instance.update_chart();
        instance
    }
    
    /// 更新图表
    fn update_chart(&mut self) {
        self.current_chart = match self.current_type {
            ChartType::Line => self.create_line_chart(),
            ChartType::Bar => self.create_bar_chart(),
            ChartType::Pie => self.create_pie_chart(),
            ChartType::Scatter => self.create_scatter_chart(),
        };
        
        println!("🔄 图表已更新为: {:?}", self.current_type);
    }
    
    /// 创建折线图
    fn create_line_chart(&self) -> Chart {
        let mut chart = Chart::new();
        
        let data: Vec<f64> = self.sample_data.iter().map(|(_, v)| *v).collect();
        let categories: Vec<&str> = self.sample_data.iter().map(|(k, _)| k.as_str()).collect();
        
        let option = json!({
            "title": {
                "text": "折线图示例",
                "left": "center",
                "textStyle": {
                    "fontSize": 18,
                    "color": "#333"
                }
            },
            "tooltip": {
                "trigger": "axis"
            },
            "xAxis": {
                "type": "category",
                "data": categories
            },
            "yAxis": {
                "type": "value"
            },
            "series": [{
                "name": "数值",
                "type": "line",
                "data": data,
                "smooth": true,
                "itemStyle": {
                    "color": "#5470c6"
                },
                "areaStyle": {
                    "opacity": 0.3
                }
            }]
        });
        
        chart.custom.insert("option".to_string(), option);
        chart
    }
    
    /// 创建柱状图
    fn create_bar_chart(&self) -> Chart {
        let mut chart = Chart::new();
        
        let data: Vec<f64> = self.sample_data.iter().map(|(_, v)| *v).collect();
        let categories: Vec<&str> = self.sample_data.iter().map(|(k, _)| k.as_str()).collect();
        
        let option = json!({
            "title": {
                "text": "柱状图示例",
                "left": "center",
                "textStyle": {
                    "fontSize": 18,
                    "color": "#333"
                }
            },
            "tooltip": {
                "trigger": "axis"
            },
            "xAxis": {
                "type": "category",
                "data": categories
            },
            "yAxis": {
                "type": "value"
            },
            "series": [{
                "name": "数值",
                "type": "bar",
                "data": data,
                "itemStyle": {
                    "color": "#91cc75"
                }
            }]
        });
        
        chart.custom.insert("option".to_string(), option);
        chart
    }
    
    /// 创建饼图
    fn create_pie_chart(&self) -> Chart {
        let mut chart = Chart::new();
        
        let data: Vec<serde_json::Value> = self.sample_data
            .iter()
            .map(|(name, value)| json!({"name": name, "value": value}))
            .collect();
        
        let option = json!({
            "title": {
                "text": "饼图示例",
                "left": "center",
                "textStyle": {
                    "fontSize": 18,
                    "color": "#333"
                }
            },
            "tooltip": {
                "trigger": "item",
                "formatter": "{b}: {c} ({d}%)"
            },
            "series": [{
                "type": "pie",
                "radius": "60%",
                "data": data,
                "emphasis": {
                    "itemStyle": {
                        "shadowBlur": 10,
                        "shadowOffsetX": 0,
                        "shadowColor": "rgba(0, 0, 0, 0.5)"
                    }
                }
            }]
        });
        
        chart.custom.insert("option".to_string(), option);
        chart
    }
    
    /// 创建散点图
    fn create_scatter_chart(&self) -> Chart {
        let mut chart = Chart::new();
        
        // 生成散点数据
        let data: Vec<[f64; 2]> = self.sample_data
            .iter()
            .enumerate()
            .map(|(i, (_, value))| [i as f64, *value])
            .collect();
        
        let option = json!({
            "title": {
                "text": "散点图示例",
                "left": "center",
                "textStyle": {
                    "fontSize": 18,
                    "color": "#333"
                }
            },
            "tooltip": {
                "trigger": "item"
            },
            "xAxis": {
                "type": "value"
            },
            "yAxis": {
                "type": "value"
            },
            "series": [{
                "type": "scatter",
                "data": data,
                "symbolSize": 12,
                "itemStyle": {
                    "color": "#ee6666"
                }
            }]
        });
        
        chart.option(option)
    }
    
    /// 切换图表类型
    fn switch_chart_type(&mut self, chart_type: ChartType, _cx: &mut Context<Self>) {
        if self.current_type != chart_type {
            self.current_type = chart_type;
            self.update_chart();
        }
    }
}

impl Render for InteractiveChart {
    fn render(&mut self, _window: &mut Window, cx: &mut Context<Self>) -> impl IntoElement {
        div()
            .flex()
            .flex_col()
            .w_full()
            .h_full()
            .bg(rgb(0xf8f9fa))
            .child(
                // 标题和控制栏
                div()
                    .flex()
                    .justify_between()
                    .items_center()
                    .h_16()
                    .bg(rgb(0x343a40))
                    .text_color(rgb(0xffffff))
                    .px_6()
                    .child(
                        div()
                            .text_xl()
                            .font_bold()
                            .child("🎮 交互式图表演示")
                    )
                    .child(
                        // 图表类型切换按钮
                        div()
                            .flex()
                            .gap_2()
                            .child(self.create_button("折线图", ChartType::Line, cx))
                            .child(self.create_button("柱状图", ChartType::Bar, cx))
                            .child(self.create_button("饼图", ChartType::Pie, cx))
                            .child(self.create_button("散点图", ChartType::Scatter, cx))
                    )
            )
            .child(
                // 图表显示区域
                div()
                    .flex_1()
                    .bg(rgb(0xffffff))
                    .m_4()
                    .rounded_lg()
                    .shadow_lg()
                    .p_6()
                    .child(self.current_chart.draw())
            )
    }
}

impl InteractiveChart {
    /// 创建按钮
    fn create_button(&mut self, text: &str, chart_type: ChartType, cx: &mut Context<Self>) -> impl IntoElement {
        let is_active = self.current_type == chart_type;
        let bg_color = if is_active { rgb(0x007bff) } else { rgb(0x6c757d) };
        
        div()
            .px_4()
            .py_2()
            .bg(bg_color)
            .text_color(rgb(0xffffff))
            .rounded_md()
            .cursor_pointer()
            .hover(|style| style.bg(rgb(0x0056b3)))
            .on_click(cx.listener(move |this, _event: &ClickEvent, cx| {
                this.switch_chart_type(chart_type, cx);
            }))
            .child(text)
    }
}
