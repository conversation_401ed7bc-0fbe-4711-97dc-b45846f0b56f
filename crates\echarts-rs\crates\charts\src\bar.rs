//! 柱状图实现 - 重构版本
//!
//! 基于新的 Core 架构的简化但完整的柱状图实现

use echarts_core::{
    Bounds, Color, CoordinateSystem, DataSet, DataValue, DrawCommand, Point, Result, Series, SeriesType,
    draw_commands::{RectStyle},
    LineStyle, LineCap, LineJoin,
};
use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};
use serde::{Deserialize, Serialize};

/// 简化的柱状图系列
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BarSeries {
    /// 基础配置
    pub config: ChartConfig,

    /// 图表数据
    pub data: DataSet,
    
    /// 柱子颜色
    pub color: Color,
    
    /// 柱子宽度（相对于类别间距的比例，0.0-1.0）
    pub bar_width: f64,
    
    /// 是否显示边框
    pub show_border: bool,
    
    /// 边框颜色
    pub border_color: Color,
    
    /// 边框宽度
    pub border_width: f64,
    
    // visible, z_index 现在在 config 中
}

impl BarSeries {
    /// 创建新的柱状图系列
    pub fn new<S: Into<String>>(name: S) -> Self {
        Self {
            config: ChartConfig {
                name: name.into(),
                ..ChartConfig::default()
            },
            data: DataSet::new(),
            color: Color::rgb(0.3, 0.7, 0.9), // 默认蓝色
            bar_width: 0.8,
            show_border: false,
            border_color: Color::rgb(0.0, 0.0, 0.0),
            border_width: 1.0,
        }
    }

    /// 设置数据（从 (x, y) 点对）
    pub fn data<I>(mut self, data: I) -> Self
    where
        I: IntoIterator<Item = (f64, f64)>,
    {
        self.data = DataSet::from_xy_pairs(data);
        self
    }

    /// 设置颜色
    pub fn color(mut self, color: Color) -> Self {
        self.color = color;
        self
    }

    /// 设置柱子宽度
    pub fn bar_width(mut self, width: f64) -> Self {
        self.bar_width = width.clamp(0.1, 1.0);
        self
    }

    /// 设置边框
    pub fn border(mut self, show: bool, color: Color, width: f64) -> Self {
        self.show_border = show;
        self.border_color = color;
        self.border_width = width;
        self
    }

    /// 设置可见性
    pub fn visible(mut self, visible: bool) -> Self {
        self.config.visible = visible;
        self
    }

    /// 设置 Z-index
    pub fn z_index(mut self, z_index: i32) -> Self {
        self.config.z_index = z_index;
        self
    }
}

impl Series for BarSeries {
    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Bar
    }

    fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        if !self.config.visible || self.data.is_empty() {
            return Ok(commands);
        }

        // 计算柱子的基本参数
        let data_count = self.data.len();
        let coord_bounds = coord_system.bounds();
        let bar_spacing = coord_bounds.size.width / data_count as f64;
        let actual_bar_width = bar_spacing * self.bar_width;

        // 绘制每个柱子
        for i in 0..data_count {
            if let Some(point) = self.data.get_point(i) {
                if let (Some(x), Some(y)) = (point.get_number(0), point.get_number(1)) {
                    // 计算柱子位置
                    let bar_x = coord_bounds.origin.x + (i as f64 + 0.5) * bar_spacing - actual_bar_width / 2.0;
                    
                    // 将数据坐标转换为屏幕坐标
                    let data_values = vec![DataValue::Number(x), DataValue::Number(0.0)];
                    let base_point = coord_system.data_to_point(&data_values)?;
                    
                    let data_values = vec![DataValue::Number(x), DataValue::Number(y)];
                    let top_point = coord_system.data_to_point(&data_values)?;
                    
                    // 计算柱子高度（处理负值）
                    let bar_height = (base_point.y - top_point.y).abs();
                    let bar_y = if y >= 0.0 { top_point.y } else { base_point.y };

                    // 创建柱子边界
                    let bar_bounds = Bounds::new(bar_x, bar_y, actual_bar_width, bar_height);

                    // 创建柱子样式
                    let style = RectStyle {
                        fill: Some(self.color),
                        stroke: if self.show_border {
                            Some(LineStyle {
                                color: self.border_color,
                                width: self.border_width,
                                opacity: 1.0,
                                dash_pattern: None,
                                cap: LineCap::Butt,
                                join: LineJoin::Miter,
                            })
                        } else {
                            None
                        },
                        opacity: 1.0,
                        corner_radius: 0.0,
                    };

                    commands.push(DrawCommand::Rect {
                        bounds: bar_bounds,
                        style,
                    });
                }
            }
        }

        Ok(commands)
    }

    fn bounds(&self) -> Option<Bounds> {
        if self.data.is_empty() {
            return None;
        }

        let mut min_x = f64::INFINITY;
        let mut max_x = f64::NEG_INFINITY;
        let mut min_y = 0.0f64; // 柱状图通常从0开始
        let mut max_y = 0.0f64;

        for i in 0..self.data.len() {
            if let Some(point) = self.data.get_point(i) {
                if let (Some(x), Some(y)) = (point.get_number(0), point.get_number(1)) {
                    min_x = min_x.min(x);
                    max_x = max_x.max(x);
                    max_y = max_y.max(y);
                    min_y = min_y.min(y); // 支持负值
                }
            }
        }

        if min_x.is_finite() && max_x.is_finite() {
            Some(Bounds::new(min_x, min_y, max_x - min_x, max_y - min_y))
        } else {
            None
        }
    }

    fn is_visible(&self) -> bool {
        self.config.visible
    }

    fn z_index(&self) -> i32 {
        self.config.z_index
    }

    fn clone_series(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_bar_series_creation() {
        let series = BarSeries::new("Test Bar")
            .data(vec![(0.0, 10.0), (1.0, 20.0), (2.0, 15.0)])
            .color(Color::rgb(0.8, 0.2, 0.2))
            .bar_width(0.6);

        assert_eq!(series.name(), "Test Bar");
        assert_eq!(series.series_type(), SeriesType::Bar);
        assert_eq!(series.bar_width, 0.6);
        assert_eq!(series.data.len(), 3);
    }

    #[test]
    fn test_bar_series_bounds() {
        let series = BarSeries::new("Test")
            .data(vec![(1.0, 5.0), (2.0, 10.0), (3.0, 7.0)]);

        let bounds = series.bounds().unwrap();
        assert_eq!(bounds.origin.x, 1.0);
        assert_eq!(bounds.origin.y, 0.0); // 柱状图从0开始
        assert_eq!(bounds.size.width, 2.0);
        assert_eq!(bounds.size.height, 10.0);
    }
}

// ============================================================================
// 图表基础架构集成 - ChartBase 实现
// ============================================================================

/// 为 BarSeries 实现 ChartBase trait
impl ChartBase for BarSeries {
    type DataType = DataSet;

    fn name(&self) -> &str {
        &self.config.name
    }

    fn raw_data(&self) -> &Self::DataType {
        &self.data
    }

    fn to_dataset(&self) -> DataSet {
        self.data.clone()
    }

    fn visible(&self) -> bool {
        self.config.visible
    }

    fn z_index(&self) -> i32 {
        self.config.z_index
    }

    fn bounds(&self) -> Option<Bounds> {
        BoundsCalculator::from_dataset(&self.data)
    }

    fn config(&self) -> &ChartConfig {
        &self.config
    }

    fn config_mut(&mut self) -> &mut ChartConfig {
        &mut self.config
    }
}

/// 为 BarSeries 实现 ChartSeries trait
impl ChartSeries for BarSeries {
    // 所有方法都由默认实现提供，基于 ChartBase
}
