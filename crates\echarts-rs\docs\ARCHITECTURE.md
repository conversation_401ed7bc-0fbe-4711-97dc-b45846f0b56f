# Rust ECharts Architecture

## Overview

Rust ECharts is a high-performance chart library written in Rust, designed to provide type-safe, memory-efficient data visualization with GPU-accelerated rendering.

## Design Principles

### 1. Type Safety
- **Compile-time guarantees**: All chart configurations and data are validated at compile time
- **Strong typing**: Data types are enforced throughout the pipeline
- **Error handling**: Comprehensive error types with detailed messages

### 2. Performance
- **Zero-copy operations**: Minimize data copying where possible
- **GPU acceleration**: Leverage GPUI for hardware-accelerated rendering
- **Incremental updates**: Only re-render changed components
- **Memory efficiency**: Careful memory management and object pooling

### 3. Modularity
- **Crate separation**: Clear separation of concerns across crates
- **Trait-based design**: Extensible through well-defined traits
- **Plugin architecture**: Easy to add new chart types and features

### 4. Usability
- **Fluent API**: Chainable method calls for easy configuration
- **Sensible defaults**: Works out of the box with minimal configuration
- **Rich documentation**: Comprehensive examples and API documentation

## Crate Structure

```
rust-echarts/
├── crates/
│   ├── core/           # Core data structures and traits
│   ├── charts/         # Chart type implementations
│   ├── components/     # UI components (legend, tooltip, etc.)
│   ├── themes/         # Theme system
│   └── renderer/       # Rendering backends
├── examples/           # Usage examples
└── benches/           # Performance benchmarks
```

### Core Crate (`rust-echarts-core`)

The foundation of the library, containing:

- **Data structures**: `DataSet`, `DataPoint`, `DataValue`
- **Geometry**: `Point`, `Size`, `Bounds`, `Path`, `Transform`
- **Colors**: `Color`, `ColorPalette` with HSL/RGB support
- **Coordinate systems**: `CartesianCoord`, `PolarCoord`
- **Styling**: `TextStyle`, `LineStyle`, `FillStyle`
- **Core traits**: `Renderable`, `Interactive`, `Themeable`

### Charts Crate (`rust-echarts-charts`)

Chart type implementations:

- **Series trait**: Common interface for all chart types
- **Bar charts**: Vertical/horizontal bars, stacked bars
- **Line charts**: Smooth lines, step lines, area charts
- **Pie charts**: Pie and donut charts
- **Scatter plots**: Bubble charts with size mapping

### Components Crate (`rust-echarts-components`)

UI components and helpers:

- **Legend**: Configurable legend with multiple positions
- **Tooltip**: Interactive tooltips with custom formatting
- **Axes**: X/Y axis with labels, ticks, and grid lines
- **Title**: Chart titles with styling options
- **Grid**: Layout grid for chart positioning

### Themes Crate (`rust-echarts-themes`)

Theme system:

- **Built-in themes**: Default, pastel, vibrant, monochrome
- **Custom themes**: Full customization support
- **Color palettes**: Predefined and custom color schemes
- **Typography**: Font management and text styling

### Renderer Crate (`rust-echarts-renderer`)

Rendering backends:

- **GPUI renderer**: GPU-accelerated rendering (primary)
- **Canvas renderer**: HTML5 Canvas fallback
- **SVG renderer**: Vector graphics export
- **Image export**: PNG, JPEG export functionality

## Data Flow

```
Raw Data → DataSet → Series → CoordinateSystem → Renderer → Output
    ↓         ↓        ↓           ↓              ↓         ↓
  Validate  Process  Transform   Map to         Draw    Display/
            Filter   Apply       Screen         Ops     Export
            Sort     Theme       Coords
```

### 1. Data Input
- Raw data is converted to `DataSet` with type validation
- Support for various input formats: tuples, vectors, iterators
- Automatic type inference and conversion

### 2. Series Processing
- Data is bound to specific chart series
- Series-specific transformations and calculations
- Theme application and styling

### 3. Coordinate Mapping
- Data values are mapped to screen coordinates
- Support for multiple coordinate systems
- Automatic scaling and range calculation

### 4. Rendering
- Drawing operations are generated
- GPU-accelerated rendering through GPUI
- Efficient batching and caching

## Key Traits

### Renderable
```rust
pub trait Renderable {
    fn render(&self, ctx: &mut RenderContext, bounds: Bounds) -> Result<(), ChartError>;
}
```

All visual elements implement this trait for consistent rendering.

### Interactive
```rust
pub trait Interactive {
    fn on_mouse_move(&mut self, x: f64, y: f64) -> bool;
    fn on_mouse_click(&mut self, x: f64, y: f64) -> bool;
    fn on_mouse_wheel(&mut self, delta: f64) -> bool;
}
```

Enables mouse interactions and event handling.

### Themeable
```rust
pub trait Themeable {
    fn apply_theme(&mut self, theme: &Theme);
}
```

Allows components to respond to theme changes.

### Series
```rust
pub trait Series: Send + Sync {
    fn name(&self) -> &str;
    fn series_type(&self) -> SeriesType;
    fn data(&self) -> &DataSet;
    fn validate(&self) -> Result<(), ChartError>;
    fn render(&self, ctx: &mut RenderContext, coord: &dyn CoordinateSystem) -> Result<(), ChartError>;
    // ...
}
```

Common interface for all chart types.

## Coordinate Systems

### Cartesian (Rectangular)
- Linear and logarithmic scales
- Category and value axes
- Support for multiple axes
- Automatic range calculation

### Polar
- Angle and radius axes
- Configurable start angle and direction
- Useful for radar charts and polar plots

### Geographic (Future)
- Map projections
- Geographic coordinate support
- Integration with mapping libraries

## Rendering Pipeline

### 1. Layout Calculation
```rust
let layout = chart.calculate_layout(bounds);
// Determines positions for title, legend, axes, plot area
```

### 2. Coordinate System Setup
```rust
let coord = CartesianCoord::new(plot_bounds, x_axis, y_axis);
// Maps data space to screen space
```

### 3. Series Rendering
```rust
for series in chart.series {
    series.render(&mut ctx, &coord)?;
}
// Each series draws itself using the coordinate system
```

### 4. Component Rendering
```rust
legend.render(&mut ctx, legend_bounds)?;
tooltip.render(&mut ctx, mouse_position)?;
// UI components are rendered on top
```

## Performance Optimizations

### Memory Management
- **Object pooling**: Reuse expensive objects
- **Arena allocation**: Reduce allocation overhead
- **Copy-on-write**: Share immutable data

### Rendering Optimizations
- **Dirty rectangles**: Only redraw changed areas
- **Level-of-detail**: Reduce complexity for distant objects
- **Culling**: Skip rendering of off-screen elements

### Data Processing
- **Parallel processing**: Use multiple cores for large datasets
- **Streaming**: Process data in chunks
- **Caching**: Cache expensive calculations

## Error Handling

Comprehensive error types with context:

```rust
pub enum ChartError {
    Configuration(String),
    InvalidData(String),
    Rendering(String),
    CoordinateSystem(String),
    Theme(String),
    Animation(String),
    // ...
}
```

All operations return `Result<T, ChartError>` for proper error handling.

## Testing Strategy

### Unit Tests
- Individual component testing
- Data validation testing
- Coordinate transformation testing

### Integration Tests
- End-to-end chart creation
- Rendering pipeline testing
- Theme application testing

### Performance Tests
- Benchmark large datasets
- Memory usage profiling
- Rendering performance measurement

### Visual Tests
- Screenshot comparison
- Regression testing
- Cross-platform consistency

## Future Enhancements

### Short Term
- Complete all basic chart types
- Animation system
- Advanced interactions (zoom, pan, brush)

### Medium Term
- 3D chart support
- WebGL renderer
- Real-time data streaming

### Long Term
- Geographic mapping
- Statistical analysis tools
- Machine learning integration
- Collaborative features

## Contributing

The modular architecture makes it easy to contribute:

1. **New chart types**: Implement the `Series` trait
2. **New themes**: Add to the themes crate
3. **New renderers**: Implement rendering backends
4. **Performance improvements**: Optimize hot paths
5. **Documentation**: Improve examples and guides

Each crate has its own tests and can be developed independently while maintaining the overall system integrity.
