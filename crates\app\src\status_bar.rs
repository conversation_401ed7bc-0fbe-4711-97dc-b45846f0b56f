/*
 * @Author: Art<PERSON>
 * @Date: 2025-06-29 13:56:20
 * @LastEditors: Artis
 * @LastEditTime: 2025-07-05 02:41:36
 * @FilePath: \FscDAQ_GPUI\crates\app\src\status_bar.rs
 * @Description:
 *
 * Copyright (c) 2025 by Artis, All Rights Reserved.
 */
/*
 * @Author: Artis
 * @Date: 2025-06-07 17:15:29
 * @LastEditors: Artis
 * @LastEditTime: 2025-06-29 03:16:46
 * @FilePath: \FscDAQ_GPUI\crates\story\src\title_bar.rs
 * @Description:
 *
 * Copyright (c) 2025 by Artis, All Rights Reserved.
 */
use std::rc::Rc;

use gpui::{
    div, AnyElement, App, AppContext, Context, IntoElement, ParentElement as _, Render,
    Styled as _, Window,
};

pub struct AppStatusBar {
    child: Rc<dyn Fn(&mut Window, &mut App) -> AnyElement>,
}

impl AppStatusBar {
    pub fn new(window: &mut Window, cx: &mut Context<Self>) -> Self {
        Self {
            child: Rc::new(|_, _| div().into_any_element()),
        }
    }

    pub fn child<F, E>(mut self, f: F) -> Self
    where
        E: IntoElement,
        F: Fn(&mut Window, &mut App) -> E + 'static,
    {
        self.child = Rc::new(move |window, cx| f(window, cx).into_any_element());
        self
    }
}

impl Render for AppStatusBar {
    fn render(&mut self, window: &mut Window, cx: &mut Context<Self>) -> impl IntoElement {
        div().child(div().flex().items_center().justify_end().px_2().gap_2())
    }
}
