{"rustc": 1842507548689473721, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 15315924755136109342, "profile": 15657897354478470176, "path": 17930799292104289896, "deps": [[5157631553186200874, "num_traits", false, 8668028564112938398], [9689903380558560274, "serde", false, 14697884230040560124], [11505586985402185701, "windows_link", false, 16704414573833123138]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\chrono-ca29d5c391745cf5\\dep-lib-chrono", "checksum": false}}], "rustflags": ["-C", "link-arg=/STACK:16000000"], "config": 2069994364910194474, "compile_kind": 0}