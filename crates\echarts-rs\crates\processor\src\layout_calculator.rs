/*!
 * 布局计算器
 * 
 * 负责计算图表的各个区域布局，包括标题区、图例区、绘图区等
 */

use crate::{ProcessedChart, Bounds, Point, Result, ProcessorError};
use serde_json::Value;
use std::collections::HashMap;

/// 图表布局信息
#[derive(Debug, Clone)]
pub struct ChartLayout {
    /// 整体边界
    pub total_bounds: Bounds,
    /// 标题区域
    pub title_area: Option<Bounds>,
    /// 图例区域
    pub legend_area: Option<Bounds>,
    /// 绘图区域
    pub plot_area: Bounds,
    /// 坐标轴区域
    pub axis_areas: HashMap<String, Bounds>,
    /// 边距信息
    pub margins: Margins,
}

/// 边距信息
#[derive(Debug, Clone)]
pub struct Margins {
    pub top: f64,
    pub right: f64,
    pub bottom: f64,
    pub left: f64,
}

/// 布局计算器
pub struct LayoutCalculator {
    /// 默认边距
    default_margins: Margins,
}

impl LayoutCalculator {
    /// 创建新的布局计算器
    pub fn new() -> Self {
        Self {
            default_margins: Margins {
                top: 60.0,
                right: 40.0,
                bottom: 60.0,
                left: 80.0,
            },
        }
    }

    /// 计算图表布局
    pub fn calculate_layout(
        &self,
        processed: &ProcessedChart,
        echarts_option: &Value,
    ) -> Result<ChartLayout> {
        println!("📐 计算图表布局");

        let total_bounds = processed.bounds;
        let margins = self.calculate_margins(echarts_option);

        // 计算标题区域
        let title_area = self.calculate_title_area(&total_bounds, echarts_option, &margins);

        // 计算图例区域
        let legend_area = self.calculate_legend_area(&total_bounds, echarts_option, &margins);

        // 计算绘图区域
        let plot_area = self.calculate_plot_area(&total_bounds, &margins, &title_area, &legend_area);

        // 计算坐标轴区域
        let axis_areas = self.calculate_axis_areas(&plot_area, echarts_option);

        Ok(ChartLayout {
            total_bounds,
            title_area,
            legend_area,
            plot_area,
            axis_areas,
            margins,
        })
    }

    /// 计算边距
    fn calculate_margins(&self, echarts_option: &Value) -> Margins {
        // 检查是否有自定义grid配置
        if let Some(grid) = echarts_option.get("grid") {
            let left = grid
                .get("left")
                .and_then(|v| self.parse_size_value(v))
                .unwrap_or(self.default_margins.left);

            let right = grid
                .get("right")
                .and_then(|v| self.parse_size_value(v))
                .unwrap_or(self.default_margins.right);

            let top = grid
                .get("top")
                .and_then(|v| self.parse_size_value(v))
                .unwrap_or(self.default_margins.top);

            let bottom = grid
                .get("bottom")
                .and_then(|v| self.parse_size_value(v))
                .unwrap_or(self.default_margins.bottom);

            return Margins { top, right, bottom, left };
        }

        self.default_margins.clone()
    }

    /// 解析尺寸值（支持像素值和百分比）
    fn parse_size_value(&self, value: &Value) -> Option<f64> {
        match value {
            Value::Number(n) => n.as_f64(),
            Value::String(s) => {
                if s.ends_with('%') {
                    // 百分比值，这里简化处理
                    s.trim_end_matches('%').parse::<f64>().ok().map(|v| v * 8.0) // 简化转换
                } else if s.ends_with("px") {
                    // 像素值
                    s.trim_end_matches("px").parse::<f64>().ok()
                } else {
                    s.parse::<f64>().ok()
                }
            }
            _ => None,
        }
    }

    /// 计算标题区域
    fn calculate_title_area(
        &self,
        total_bounds: &Bounds,
        echarts_option: &Value,
        margins: &Margins,
    ) -> Option<Bounds> {
        if let Some(title) = echarts_option.get("title") {
            if title.get("text").is_some() {
                let height = 40.0; // 标题高度
                return Some(Bounds::new(
                    total_bounds.x + margins.left,
                    total_bounds.y + 10.0,
                    total_bounds.width - margins.left - margins.right,
                    height,
                ));
            }
        }
        None
    }

    /// 计算图例区域
    fn calculate_legend_area(
        &self,
        total_bounds: &Bounds,
        echarts_option: &Value,
        margins: &Margins,
    ) -> Option<Bounds> {
        if let Some(legend) = echarts_option.get("legend") {
            if legend.get("data").is_some() {
                let height = 30.0; // 图例高度
                return Some(Bounds::new(
                    total_bounds.x + margins.left,
                    total_bounds.y + total_bounds.height - margins.bottom - height,
                    total_bounds.width - margins.left - margins.right,
                    height,
                ));
            }
        }
        None
    }

    /// 计算绘图区域
    fn calculate_plot_area(
        &self,
        total_bounds: &Bounds,
        margins: &Margins,
        title_area: &Option<Bounds>,
        legend_area: &Option<Bounds>,
    ) -> Bounds {
        let mut top_offset = margins.top;
        let mut bottom_offset = margins.bottom;

        // 考虑标题占用的空间
        if let Some(title) = title_area {
            top_offset = title.y + title.height + 10.0 - total_bounds.y;
        }

        // 考虑图例占用的空间
        if let Some(legend) = legend_area {
            bottom_offset = total_bounds.y + total_bounds.height - legend.y + 10.0;
        }

        Bounds::new(
            total_bounds.x + margins.left,
            total_bounds.y + top_offset,
            total_bounds.width - margins.left - margins.right,
            total_bounds.height - top_offset - bottom_offset,
        )
    }

    /// 计算坐标轴区域
    fn calculate_axis_areas(&self, plot_area: &Bounds, echarts_option: &Value) -> HashMap<String, Bounds> {
        let mut axis_areas = HashMap::new();

        // X轴区域
        if echarts_option.get("xAxis").is_some() {
            let x_axis_bounds = Bounds::new(
                plot_area.x,
                plot_area.y + plot_area.height,
                plot_area.width,
                30.0, // X轴高度
            );
            axis_areas.insert("xAxis".to_string(), x_axis_bounds);
        }

        // Y轴区域
        if echarts_option.get("yAxis").is_some() {
            let y_axis_bounds = Bounds::new(
                plot_area.x - 50.0, // Y轴宽度
                plot_area.y,
                50.0,
                plot_area.height,
            );
            axis_areas.insert("yAxis".to_string(), y_axis_bounds);
        }

        axis_areas
    }

    /// 获取绘图区域的有效边界（考虑坐标轴）
    pub fn get_effective_plot_area(&self, layout: &ChartLayout) -> Bounds {
        let mut effective_area = layout.plot_area;

        // 如果有坐标轴，需要进一步缩小绘图区域
        if layout.axis_areas.contains_key("yAxis") {
            // Y轴会占用左侧空间，但这已经在margins中考虑了
        }

        if layout.axis_areas.contains_key("xAxis") {
            // X轴会占用底部空间，但这已经在margins中考虑了
        }

        effective_area
    }

    /// 计算数据点在绘图区域中的位置
    pub fn map_data_to_plot_position(
        &self,
        data_value: f64,
        data_index: usize,
        data_count: usize,
        max_value: f64,
        plot_area: &Bounds,
        is_horizontal: bool,
    ) -> Point {
        if is_horizontal {
            // 水平方向（X轴）
            let x = plot_area.x + (data_index as f64 / (data_count as f64 - 1.0).max(1.0)) * plot_area.width;
            let y = plot_area.y + plot_area.height - (data_value / max_value) * plot_area.height * 0.9;
            Point::new(x, y)
        } else {
            // 垂直方向（Y轴）
            let x = plot_area.x + (data_value / max_value) * plot_area.width * 0.9;
            let y = plot_area.y + (data_index as f64 / (data_count as f64 - 1.0).max(1.0)) * plot_area.height;
            Point::new(x, y)
        }
    }
}

impl Default for LayoutCalculator {
    fn default() -> Self {
        Self::new()
    }
}

impl Default for Margins {
    fn default() -> Self {
        Self {
            top: 60.0,
            right: 40.0,
            bottom: 60.0,
            left: 80.0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_layout_calculation() {
        let calculator = LayoutCalculator::new();
        let bounds = Bounds::new(0.0, 0.0, 800.0, 600.0);
        
        let processed = ProcessedChart {
            title: Some("测试".to_string()),
            background_color: None,
            draw_operations: Vec::new(),
            bounds,
            metadata: std::collections::HashMap::new(),
        };

        let option = json!({
            "title": { "text": "测试图表" },
            "xAxis": { "type": "category" },
            "yAxis": { "type": "value" }
        });

        let layout = calculator.calculate_layout(&processed, &option).unwrap();
        
        assert!(layout.title_area.is_some());
        assert!(layout.axis_areas.contains_key("xAxis"));
        assert!(layout.axis_areas.contains_key("yAxis"));
        assert!(layout.plot_area.width > 0.0);
        assert!(layout.plot_area.height > 0.0);
    }
}
