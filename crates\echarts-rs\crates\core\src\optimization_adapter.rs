//! 数据优化适配层
//!
//! 提供图表特定的优化策略和统一的优化接口

use crate::{DataSet, Result, optimization::{DataOptimizer, OptimizationConfig, OptimizationAlgorithm}};
use std::collections::HashMap;

/// 图表优化特征
pub trait ChartOptimization {
    /// 获取图表ID
    fn chart_id(&self) -> &str;
    
    /// 获取优化配置
    fn get_optimization_config(&self) -> OptimizationConfig;
    
    /// 应用优化
    fn apply_optimization(&mut self, optimizer: &mut DataOptimizer) -> Result<()>;
    
    /// 是否应该进行优化
    fn should_optimize(&self) -> bool;
    
    /// 获取数据集
    fn get_dataset(&self) -> &DataSet;
    
    /// 设置优化后的数据集
    fn set_optimized_dataset(&mut self, dataset: DataSet);
}

/// 优化策略
#[derive(Debug, Clone)]
pub enum OptimizationStrategy {
    /// 自动选择最佳算法
    Auto,
    /// 基于数据大小选择
    DataSizeBased,
    /// 基于图表类型选择
    ChartTypeBased,
    /// 自定义策略
    Custom(OptimizationConfig),
}

/// 优化适配器
pub struct OptimizationAdapter {
    /// 核心优化器
    core_optimizer: DataOptimizer,
    
    /// 图表优化策略
    chart_strategies: HashMap<String, OptimizationStrategy>,
    
    /// 全局优化配置
    global_config: OptimizationConfig,
    
    /// 性能统计
    performance_stats: AdapterStats,
}

/// 适配器统计信息
#[derive(Debug, Clone, Default)]
pub struct AdapterStats {
    /// 优化的图表数量
    pub optimized_charts: u64,
    
    /// 总节省的数据点数
    pub total_points_saved: u64,
    
    /// 平均压缩比
    pub avg_compression_ratio: f64,
    
    /// 最后优化时间
    pub last_optimization_time: Option<std::time::Instant>,
}

impl OptimizationAdapter {
    /// 创建新的优化适配器
    pub fn new(global_config: OptimizationConfig) -> Self {
        Self {
            core_optimizer: DataOptimizer::new(global_config.clone()),
            chart_strategies: HashMap::new(),
            global_config,
            performance_stats: AdapterStats::default(),
        }
    }
    
    /// 使用默认配置创建适配器
    pub fn default() -> Self {
        Self::new(OptimizationConfig::default())
    }
    
    /// 注册图表优化策略
    pub fn register_chart_strategy(&mut self, chart_id: String, strategy: OptimizationStrategy) {
        self.chart_strategies.insert(chart_id, strategy);
    }
    
    /// 优化图表数据
    pub fn optimize_chart(&mut self, chart: &mut dyn ChartOptimization) -> Result<()> {
        if !chart.should_optimize() {
            return Ok(());
        }
        
        let start_time = std::time::Instant::now();
        let original_size = chart.get_dataset().len();
        
        // 获取优化配置
        let config = self.get_chart_optimization_config(chart);
        
        // 更新核心优化器配置
        self.core_optimizer = DataOptimizer::new(config);
        
        // 执行优化
        let optimized_dataset = self.core_optimizer.optimize_dataset(chart.get_dataset())?;
        
        // 设置优化后的数据
        chart.set_optimized_dataset(optimized_dataset.clone());
        
        // 更新统计信息
        self.update_stats(original_size, optimized_dataset.len(), start_time.elapsed());
        
        Ok(())
    }
    
    /// 批量优化多个图表
    pub fn optimize_charts(&mut self, charts: &mut [&mut dyn ChartOptimization]) -> Result<()> {
        for chart in charts {
            self.optimize_chart(*chart)?;
        }
        Ok(())
    }
    
    /// 获取图表的优化配置
    fn get_chart_optimization_config(&self, chart: &dyn ChartOptimization) -> OptimizationConfig {
        let chart_id = chart.chart_id();
        
        // 检查是否有特定的图表策略
        if let Some(strategy) = self.chart_strategies.get(chart_id) {
            match strategy {
                OptimizationStrategy::Auto => self.auto_select_config(chart),
                OptimizationStrategy::DataSizeBased => self.data_size_based_config(chart),
                OptimizationStrategy::ChartTypeBased => self.chart_type_based_config(chart),
                OptimizationStrategy::Custom(config) => config.clone(),
            }
        } else {
            // 使用图表自己的配置或全局配置
            let chart_config = chart.get_optimization_config();
            if chart_config.target_points > 0 {
                chart_config
            } else {
                self.global_config.clone()
            }
        }
    }
    
    /// 自动选择最佳配置
    fn auto_select_config(&self, chart: &dyn ChartOptimization) -> OptimizationConfig {
        let dataset = chart.get_dataset();
        let data_size = dataset.len();
        
        let algorithm = if data_size > 100000 {
            // 大数据集使用 LTTB
            OptimizationAlgorithm::LTTB
        } else if data_size > 10000 {
            // 中等数据集使用自适应采样
            OptimizationAlgorithm::Adaptive
        } else if data_size > 1000 {
            // 小数据集使用 Douglas-Peucker
            OptimizationAlgorithm::DouglasPeucker { epsilon: 1.0 }
        } else {
            // 很小的数据集不优化
            return OptimizationConfig {
                target_points: data_size,
                min_threshold: data_size + 1, // 确保不会优化
                ..self.global_config.clone()
            };
        };
        
        OptimizationConfig {
            algorithm,
            target_points: (data_size / 10).max(100).min(2000),
            ..self.global_config.clone()
        }
    }
    
    /// 基于数据大小的配置
    fn data_size_based_config(&self, chart: &dyn ChartOptimization) -> OptimizationConfig {
        let data_size = chart.get_dataset().len();
        
        let (algorithm, target_points) = match data_size {
            0..=1000 => (OptimizationAlgorithm::Uniform, data_size),
            1001..=10000 => (OptimizationAlgorithm::DouglasPeucker { epsilon: 0.5 }, data_size / 5),
            10001..=100000 => (OptimizationAlgorithm::LTTB, data_size / 10),
            _ => (OptimizationAlgorithm::LTTB, 2000),
        };
        
        OptimizationConfig {
            algorithm,
            target_points,
            ..self.global_config.clone()
        }
    }
    
    /// 基于图表类型的配置
    fn chart_type_based_config(&self, chart: &dyn ChartOptimization) -> OptimizationConfig {
        let chart_id = chart.chart_id();
        
        // 根据图表类型选择最适合的算法
        let algorithm = if chart_id.contains("line") {
            // 折线图适合 Douglas-Peucker
            OptimizationAlgorithm::DouglasPeucker { epsilon: 1.0 }
        } else if chart_id.contains("scatter") {
            // 散点图适合像素级优化
            OptimizationAlgorithm::PixelBased
        } else if chart_id.contains("bar") {
            // 柱状图适合极值采样
            OptimizationAlgorithm::MinMax
        } else {
            // 默认使用 LTTB
            OptimizationAlgorithm::LTTB
        };
        
        OptimizationConfig {
            algorithm,
            ..chart.get_optimization_config()
        }
    }
    
    /// 更新统计信息
    fn update_stats(&mut self, original_size: usize, optimized_size: usize, duration: std::time::Duration) {
        self.performance_stats.optimized_charts += 1;
        
        if original_size > optimized_size {
            self.performance_stats.total_points_saved += (original_size - optimized_size) as u64;
        }
        
        // 计算平均压缩比
        if original_size > 0 {
            let compression_ratio = optimized_size as f64 / original_size as f64;
            let total_charts = self.performance_stats.optimized_charts as f64;
            self.performance_stats.avg_compression_ratio = 
                (self.performance_stats.avg_compression_ratio * (total_charts - 1.0) + compression_ratio) / total_charts;
        }
        
        self.performance_stats.last_optimization_time = Some(std::time::Instant::now());
        
        println!("📊 优化完成: {} -> {} 点 ({:.1}% 压缩), 耗时: {:.2}ms", 
            original_size, optimized_size, 
            (1.0 - optimized_size as f64 / original_size as f64) * 100.0,
            duration.as_secs_f64() * 1000.0
        );
    }
    
    /// 获取性能统计
    pub fn stats(&self) -> &AdapterStats {
        &self.performance_stats
    }
    
    /// 获取核心优化器统计
    pub fn core_stats(&self) -> &crate::optimization::OptimizationStats {
        self.core_optimizer.stats()
    }
    
    /// 重置统计信息
    pub fn reset_stats(&mut self) {
        self.performance_stats = AdapterStats::default();
        self.core_optimizer.reset_stats();
    }
    
    /// 设置全局配置
    pub fn set_global_config(&mut self, config: OptimizationConfig) {
        self.global_config = config;
    }
    
    /// 获取全局配置
    pub fn global_config(&self) -> &OptimizationConfig {
        &self.global_config
    }
}

impl Default for OptimizationAdapter {
    fn default() -> Self {
        Self::new(OptimizationConfig::default())
    }
}
