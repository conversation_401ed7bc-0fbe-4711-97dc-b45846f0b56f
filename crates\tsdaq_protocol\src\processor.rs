use crate::ProtocolError;
use bytes::Bytes;
use serde::{Deserialize, Serialize};
use tracing::{debug, error, info, warn};

/// 通道数据结构
#[derive(Debug, <PERSON>lone)]
pub struct ChannelData {
    pub ch: u8,
    pub data: Bytes,
}

/// 通道长度配置
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct ChannelLength {
    pub ch: u8,
    pub length: u16,
}

/// 设备配置信息
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct DeviceConfig {
    pub product_name: String,
    pub serial_number: u64,
    pub software_version: String,
    pub supported_interfaces: u8,
    pub sampling_rate: f32,
    pub ip_address: String,
}

/// 数据处理器
#[derive(Debug, <PERSON>lone)]
pub struct DataProcessor;

impl DataProcessor {
    /// 创建新的数据处理器实例
    pub fn new() -> Self {
        debug!("创建数据处理器实例");
        Self {}
    }

    /// 处理单包数据
    pub fn single_packet_data(&self, data: &Bytes) -> Vec<ChannelData> {
        debug!("处理单包数据: {} 字节", data.len());
        vec![ChannelData {
            ch: 0,
            data: data.clone(),
        }]
    }

    /// 处理组包数据
    pub fn grouped_data(&self, data: &Bytes) -> Vec<ChannelData> {
        debug!("处理组包数据: {} 字节", data.len());
        let mut channels = Vec::new();
        let mut pos = 0;
        let data_len = data.len();

        while pos + 3 <= data_len {
            // 解析通道ID
            let ch = data[pos];
            if ch == 0xFF {
                warn!("检测到无效通道ID (0xFF)，数据格式可能不正确");
                return Vec::new();
            }
            pos += 1;

            // 解析数据长度
            let len = u16::from_be_bytes([data[pos], data[pos + 1]]) as usize;
            pos += 2;

            if pos + len > data_len {
                warn!(
                    "数据长度超出范围: 需要 {} 字节，剩余 {} 字节",
                    len,
                    data_len - pos
                );
                return Vec::new();
            }

            // 提取数据
            let channel_data = data.slice(pos..pos + len);
            pos += len;

            debug!("解析通道 {}: {} 字节", ch, len);
            channels.push(ChannelData {
                ch,
                data: channel_data,
            });
        }

        info!("组包数据处理完成: 解析出 {} 个通道", channels.len());
        channels
    }

    /// 处理广播通道（ch == 0xFF）
    fn process_broadcast_channels(
        &self,
        data: &Bytes,
        channel: &ChannelLength,
        pos: &mut usize,
        data_len: usize,
        channels: &mut Vec<ChannelData>,
    ) -> bool {
        let required_size = channel.length as usize;
        if *pos + required_size > data_len {
            warn!("广播通道处理失败: 数据不足");
            return false;
        }

        // 计算最多能处理多少个通道数据
        let max_channels = (data_len - *pos) / required_size;
        debug!(
            "广播通道处理: 每个通道 {} 字节，最多处理 {} 个通道",
            required_size, max_channels
        );

        for ch in 0..=254 {
            // 如果剩余空间不足一个完整数据块，则结束处理
            if max_channels < ch as usize {
                break;
            }

            channels.push(ChannelData {
                ch,
                data: data.slice(*pos..*pos + required_size),
            });
            *pos += required_size;
        }

        info!("广播通道处理完成: 处理了 {} 个通道", max_channels.min(255));
        true
    }

    /// 处理单个通道
    fn process_single_channel(
        &self,
        data: &Bytes,
        channel: &ChannelLength,
        pos: &mut usize,
        data_len: usize,
        channels: &mut Vec<ChannelData>,
    ) -> bool {
        let required_size = channel.length as usize;
        if *pos + required_size > data_len {
            warn!("单个通道处理失败: 通道 {} 数据不足", channel.ch);
            return false;
        }

        let slice = data.slice(*pos..*pos + required_size);
        *pos += required_size;

        debug!("处理通道 {}: {} 字节", channel.ch, required_size);
        channels.push(ChannelData {
            ch: channel.ch,
            data: slice,
        });

        true
    }

    /// 根据通道表对数据进行分组处理
    ///
    /// # 参数
    /// * `data` - 原始数据切片
    /// * `channel_table` - 通道配置表
    ///
    /// # 返回值
    /// 分组后的通道数据 Vec<ChannelData>
    pub fn channel_grouped_data(
        &self,
        data: &Bytes,
        channel_table: &[ChannelLength],
    ) -> Vec<ChannelData> {
        debug!(
            "通道分组处理: 数据 {} 字节，通道表 {} 项",
            data.len(),
            channel_table.len()
        );

        let mut pos = 0;
        let data_len = data.len();
        let mut channels = Vec::with_capacity(254);

        for (index, channel) in channel_table.iter().enumerate() {
            if channel.ch == 0xFF {
                // 处理广播通道（全部通道）
                debug!("处理广播通道 (索引 {})", index);
                if !self.process_broadcast_channels(
                    data,
                    channel,
                    &mut pos,
                    data_len,
                    &mut channels,
                ) {
                    break;
                }
                break;
            } else {
                // 处理普通通道
                debug!("处理普通通道 {} (索引 {})", channel.ch, index);
                if !self.process_single_channel(data, channel, &mut pos, data_len, &mut channels) {
                    break;
                }
            }
        }

        info!("通道分组处理完成: 解析出 {} 个通道", channels.len());
        channels
    }

    /// 解析通道表
    ///
    /// 通道表格格式: CH1+CH2+CHn
    /// CH：单通道
    /// CH：`通道[0] + 数据长度[0:1]
    /// 通道[0]：1~254, 255:全部  0:无效保留
    /// 数据长度[0:1]: 1~65535, 0:无数据长度
    pub fn parse_channel_table(&self, data: &Bytes) -> Result<Vec<ChannelLength>, ProtocolError> {
        debug!("解析通道表: {} 字节", data.len());

        if data.len() % 3 != 0 {
            error!("通道表数据长度错误: {} 字节不是3的倍数", data.len());
            return Err(ProtocolError::Config(
                "通道表数据长度必须是3的倍数".to_string(),
            ));
        }

        let mut channel_table: Vec<ChannelLength> = Vec::new();

        for (index, chunk) in data.chunks_exact(3).enumerate() {
            let channel_id = chunk[0];
            let length = u16::from_be_bytes([chunk[1], chunk[2]]);

            if channel_id != 0 {
                debug!("通道表项 {}: 通道ID={}, 长度={}", index, channel_id, length);
                channel_table.push(ChannelLength {
                    ch: channel_id,
                    length,
                });
            } else {
                debug!("跳过无效通道表项 {}: 通道ID=0", index);
            }
        }

        info!("通道表解析完成: {} 个有效通道", channel_table.len());
        Ok(channel_table)
    }

    /// 解析设备配置信息
    pub fn parse_configuration(&self, data: &Bytes) -> Result<DeviceConfig, ProtocolError> {
        debug!("解析设备配置: {} 字节", data.len());

        if data.len() < 100 {
            error!(
                "配置数据长度不足: 需要至少100字节，实际 {} 字节",
                data.len()
            );
            return Err(ProtocolError::Config("配置数据长度不足".to_string()));
        }

        // 产品名称 (0-31)
        let product_name_end = 32.min(data.len());
        let product_name = String::from_utf8_lossy(&data[0..product_name_end])
            .trim_end_matches('\0')
            .to_string();

        // 序列号 (32-39)
        let serial_number = if data.len() >= 40 {
            let mut bytes = [0u8; 8];
            bytes.copy_from_slice(&data[32..40]);
            u64::from_be_bytes(bytes)
        } else {
            warn!("序列号数据不足，使用默认值0");
            0
        };

        // 软件版本号 (40-49)
        let software_version_end = 50.min(data.len());
        let software_version = String::from_utf8_lossy(&data[40..software_version_end])
            .trim_end_matches('\0')
            .to_string();

        // 通信接口支持 (50)
        let supported_interfaces = if data.len() > 50 { data[50] } else { 0 };

        // 采样率 (54-57)
        let sampling_rate = if data.len() >= 58 {
            let mut bytes = [0u8; 4];
            bytes.copy_from_slice(&data[54..58]);
            f32::from_be_bytes(bytes)
        } else {
            warn!("采样率数据不足，使用默认值0.0");
            0.0
        };

        // IP地址 (78-89)
        let ip_address = if data.len() >= 90 {
            let ip_bytes = &data[78..90];
            format!(
                "{}.{}.{}.{}:{}",
                ip_bytes[0],
                ip_bytes[1],
                ip_bytes[2],
                ip_bytes[3],
                u16::from_be_bytes([ip_bytes[8], ip_bytes[9]])
            )
        } else {
            warn!("IP地址数据不足，使用空字符串");
            "".to_string()
        };

        let config = DeviceConfig {
            product_name,
            serial_number,
            software_version,
            supported_interfaces,
            sampling_rate,
            ip_address,
        };

        info!(
            "设备配置解析完成: 产品={}, 序列号={}, 版本={}, 采样率={}Hz",
            config.product_name,
            config.serial_number,
            config.software_version,
            config.sampling_rate
        );

        Ok(config)
    }
}
