//! WebAssembly bindings for ECharts-rs

use echarts_charts::*;
use echarts_components::*;
use echarts_core::*;
use echarts_svg_renderer::*;
use echarts_themes::*;
use serde::{Deserialize, Serialize};
use wasm_bindgen::prelude::*;
use web_sys::{console, CanvasRenderingContext2d, HtmlCanvasElement};

// When the `wee_alloc` feature is enabled, use `wee_alloc` as the global allocator
#[cfg(feature = "wee_alloc")]
#[global_allocator]
static ALLOC: wee_alloc::WeeAlloc = wee_alloc::WeeAlloc::INIT;

/// Initialize the WebAssembly module
#[wasm_bindgen(start)]
pub fn init() {
    console_error_panic_hook::set_once();
    console::log_1(&"ECharts-rs WASM module initialized".into());
}

/// WebAssembly wrapper for Chart
#[wasm_bindgen]
pub struct WasmChart {
    inner: Chart,
}

/// WebAssembly wrapper for chart configuration
#[derive(Serialize, Deserialize)]
pub struct ChartConfig {
    pub title: Option<String>,
    pub subtitle: Option<String>,
    pub theme: Option<String>,
    pub width: Option<f64>,
    pub height: Option<f64>,
    pub animation: Option<bool>,
}

/// WebAssembly wrapper for series data
#[derive(Serialize, Deserialize)]
pub struct SeriesData {
    pub name: String,
    pub series_type: String,
    pub data: Vec<DataPoint>,
}

/// WebAssembly wrapper for data points
#[derive(Serialize, Deserialize)]
pub struct DataPoint {
    pub x: Option<f64>,
    pub y: Option<f64>,
    pub value: Option<f64>,
    pub name: Option<String>,
    pub category: Option<String>,
}

#[wasm_bindgen]
impl WasmChart {
    /// Create a new chart
    #[wasm_bindgen(constructor)]
    pub fn new() -> WasmChart {
        WasmChart {
            inner: Chart::new(),
        }
    }

    /// Create a chart from configuration
    #[wasm_bindgen]
    pub fn from_config(config: &JsValue) -> Result<WasmChart, JsValue> {
        let config: ChartConfig = serde_wasm_bindgen::from_value(config.clone())?;

        let mut chart = Chart::new();

        if let Some(title) = config.title {
            let mut title_component = Title::new(title);
            if let Some(subtitle) = config.subtitle {
                title_component = title_component.subtitle(subtitle);
            }
            chart = chart.title(title_component);
        }

        if let Some(theme) = config.theme {
            chart = chart.theme(&theme);
        }

        if let Some(animation_enabled) = config.animation {
            if animation_enabled {
                chart = chart.animation(Animation::normal());
            } else {
                chart = chart.animation(Animation::none());
            }
        }

        Ok(WasmChart { inner: chart })
    }

    /// Set chart title
    #[wasm_bindgen]
    pub fn set_title(&mut self, title: &str, subtitle: Option<String>) {
        let mut title_component = Title::new(title);
        if let Some(sub) = subtitle {
            title_component = title_component.subtitle(sub);
        }
        self.inner = self.inner.clone().title(title_component);
    }

    /// Set chart theme
    #[wasm_bindgen]
    pub fn set_theme(&mut self, theme: &str) {
        self.inner = self.inner.clone().theme(theme);
    }

    /// Add a line series
    #[wasm_bindgen]
    pub fn add_line_series(&mut self, name: &str, data: &JsValue) -> Result<(), JsValue> {
        let data_points: Vec<(String, f64)> = serde_wasm_bindgen::from_value(data.clone())?;

        let series = LineSeries::new(name)
            .data(data_points)
            .smooth(true)
            .show_symbol(true);

        self.inner = self.inner.clone().add_series(Box::new(series));
        Ok(())
    }

    /// Add a bar series
    #[wasm_bindgen]
    pub fn add_bar_series(&mut self, name: &str, data: &JsValue) -> Result<(), JsValue> {
        let data_points: Vec<(String, f64)> = serde_wasm_bindgen::from_value(data.clone())?;

        let series = BarSeries::new(name).data(data_points);

        self.inner = self.inner.clone().add_series(Box::new(series));
        Ok(())
    }

    /// Add a pie series
    #[wasm_bindgen]
    pub fn add_pie_series(&mut self, name: &str, data: &JsValue) -> Result<(), JsValue> {
        let data_points: Vec<(String, f64)> = serde_wasm_bindgen::from_value(data.clone())?;

        let pie_data: Vec<PieDataItem> = data_points
            .into_iter()
            .map(|(name, value)| PieDataItem::new(name, value))
            .collect();

        let series = PieSeries::new(name)
            .data(pie_data)
            .radius(PieRadius::Percentage(70.0));

        self.inner = self.inner.clone().add_series(Box::new(series));
        Ok(())
    }

    /// Add a scatter series
    #[wasm_bindgen]
    pub fn add_scatter_series(&mut self, name: &str, data: &JsValue) -> Result<(), JsValue> {
        let data_points: Vec<(f64, f64)> = serde_wasm_bindgen::from_value(data.clone())?;

        let series = ScatterSeries::new(name)
            .data(data_points)
            .symbol(ScatterSymbolType::Circle);

        self.inner = self.inner.clone().add_series(Box::new(series));
        Ok(())
    }

    /// Enable legend
    #[wasm_bindgen]
    pub fn enable_legend(&mut self, position: Option<String>) {
        let pos = match position.as_deref() {
            Some("top") => Position::Top,
            Some("bottom") => Position::Bottom,
            Some("left") => Position::Left,
            Some("right") => Position::Right,
            _ => Position::TopRight,
        };

        let legend = Legend::new().position(pos);
        self.inner = self.inner.clone().legend(legend);
    }

    /// Enable tooltip
    #[wasm_bindgen]
    pub fn enable_tooltip(&mut self) {
        let tooltip = Tooltip::new()
            .trigger(TooltipTrigger::Axis)
            .show_content(true);
        self.inner = self.inner.clone().tooltip(tooltip);
    }

    /// Enable grid
    #[wasm_bindgen]
    pub fn enable_grid(&mut self, show_lines: bool) {
        let grid = Grid::new().show_grid_lines(show_lines);
        self.inner = self.inner.clone().grid(grid);
    }

    /// Export to SVG
    #[wasm_bindgen]
    pub fn to_svg(&self, width: f64, height: f64) -> Result<String, JsValue> {
        SvgExporter::export_to_string(&self.inner, width, height)
            .map_err(|e| JsValue::from_str(&format!("SVG export failed: {}", e)))
    }

    /// Export to JSON configuration
    #[wasm_bindgen]
    pub fn to_json(&self) -> Result<String, JsValue> {
        self.inner
            .to_json()
            .map_err(|e| JsValue::from_str(&format!("JSON export failed: {}", e)))
    }

    /// Validate the chart
    #[wasm_bindgen]
    pub fn validate(&self) -> Result<(), JsValue> {
        self.inner
            .validate()
            .map_err(|e| JsValue::from_str(&format!("Validation failed: {}", e)))
    }

    /// Get series count
    #[wasm_bindgen]
    pub fn series_count(&self) -> usize {
        self.inner.series_count()
    }

    /// Render to canvas
    #[wasm_bindgen]
    pub fn render_to_canvas(&self, canvas: &HtmlCanvasElement) -> Result<(), JsValue> {
        let context = canvas
            .get_context("2d")?
            .unwrap()
            .dyn_into::<CanvasRenderingContext2d>()?;

        // Get canvas dimensions
        let width = canvas.width() as f64;
        let height = canvas.height() as f64;

        // Clear canvas
        context.clear_rect(0.0, 0.0, width, height);

        // Set background
        context.set_fill_style(&JsValue::from_str("white"));
        context.fill_rect(0.0, 0.0, width, height);

        // Simplified rendering - draw a placeholder
        context.set_fill_style(&JsValue::from_str("black"));
        context.set_font("16px Arial");
        context.fill_text("ECharts-rs Chart", 10.0, 30.0)?;

        // TODO: Implement actual chart rendering to canvas
        // This would involve converting the chart to canvas drawing commands

        Ok(())
    }
}

/// Utility functions for WebAssembly
#[wasm_bindgen]
pub struct WasmUtils;

#[wasm_bindgen]
impl WasmUtils {
    /// Get available themes
    #[wasm_bindgen]
    pub fn get_available_themes() -> JsValue {
        let themes = vec!["light", "dark"];
        serde_wasm_bindgen::to_value(&themes).unwrap()
    }

    /// Get available chart types
    #[wasm_bindgen]
    pub fn get_available_chart_types() -> JsValue {
        let chart_types = vec!["line", "bar", "pie", "scatter", "area", "radar"];
        serde_wasm_bindgen::to_value(&chart_types).unwrap()
    }

    /// Create sample data for testing
    #[wasm_bindgen]
    pub fn create_sample_data(chart_type: &str, count: usize) -> JsValue {
        match chart_type {
            "line" | "bar" => {
                let data: Vec<(String, f64)> = (0..count)
                    .map(|i| (format!("Item {}", i), (i as f64 * 10.0) % 100.0))
                    .collect();
                serde_wasm_bindgen::to_value(&data).unwrap()
            }
            "scatter" => {
                let data: Vec<(f64, f64)> = (0..count)
                    .map(|i| {
                        let x = i as f64;
                        let y = (x * 0.5).sin() * 50.0 + 50.0;
                        (x, y)
                    })
                    .collect();
                serde_wasm_bindgen::to_value(&data).unwrap()
            }
            _ => {
                let data: Vec<(String, f64)> = (0..count.min(10))
                    .map(|i| (format!("Category {}", i), (i + 1) as f64 * 10.0))
                    .collect();
                serde_wasm_bindgen::to_value(&data).unwrap()
            }
        }
    }

    /// Log message to console
    #[wasm_bindgen]
    pub fn log(message: &str) {
        console::log_1(&message.into());
    }

    /// Get version information
    #[wasm_bindgen]
    pub fn get_version() -> String {
        env!("CARGO_PKG_VERSION").to_string()
    }
}

/// Theme manager for WebAssembly
#[wasm_bindgen]
pub struct WasmThemeManager {
    inner: ThemeManager,
}

#[wasm_bindgen]
impl WasmThemeManager {
    /// Create a new theme manager
    #[wasm_bindgen(constructor)]
    pub fn new() -> WasmThemeManager {
        WasmThemeManager {
            inner: ThemeManager::default(),
        }
    }

    /// Get current theme name
    #[wasm_bindgen]
    pub fn current_theme(&self) -> String {
        self.inner.current_theme_name().to_string()
    }

    /// Set current theme
    #[wasm_bindgen]
    pub fn set_theme(&mut self, name: &str) -> Result<(), JsValue> {
        self.inner
            .set_current_theme(name)
            .map_err(|e| JsValue::from_str(&format!("Failed to set theme: {}", e)))
    }

    /// Get available themes
    #[wasm_bindgen]
    pub fn available_themes(&self) -> JsValue {
        let themes: Vec<String> = self.inner.list_themes();
        serde_wasm_bindgen::to_value(&themes).unwrap()
    }

    /// Switch to next theme
    #[wasm_bindgen]
    pub fn next_theme(&mut self) -> Result<(), JsValue> {
        self.inner
            .next_theme()
            .map_err(|e| JsValue::from_str(&format!("Failed to switch theme: {}", e)))
    }

    /// Switch to previous theme
    #[wasm_bindgen]
    pub fn previous_theme(&mut self) -> Result<(), JsValue> {
        self.inner
            .previous_theme()
            .map_err(|e| JsValue::from_str(&format!("Failed to switch theme: {}", e)))
    }
}

/// Animation utilities for WebAssembly
#[wasm_bindgen]
pub struct WasmAnimation;

#[wasm_bindgen]
impl WasmAnimation {
    /// Create a fast animation
    #[wasm_bindgen]
    pub fn fast() -> JsValue {
        let animation = Animation::fast();
        serde_wasm_bindgen::to_value(&animation).unwrap()
    }

    /// Create a normal animation
    #[wasm_bindgen]
    pub fn normal() -> JsValue {
        let animation = Animation::normal();
        serde_wasm_bindgen::to_value(&animation).unwrap()
    }

    /// Create a slow animation
    #[wasm_bindgen]
    pub fn slow() -> JsValue {
        let animation = Animation::slow();
        serde_wasm_bindgen::to_value(&animation).unwrap()
    }

    /// Create a bouncy animation
    #[wasm_bindgen]
    pub fn bouncy() -> JsValue {
        let animation = Animation::bouncy();
        serde_wasm_bindgen::to_value(&animation).unwrap()
    }

    /// Create an elastic animation
    #[wasm_bindgen]
    pub fn elastic() -> JsValue {
        let animation = Animation::elastic();
        serde_wasm_bindgen::to_value(&animation).unwrap()
    }

    /// Disable animation
    #[wasm_bindgen]
    pub fn none() -> JsValue {
        let animation = Animation::none();
        serde_wasm_bindgen::to_value(&animation).unwrap()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use wasm_bindgen_test::*;

    wasm_bindgen_test_configure!(run_in_browser);

    #[wasm_bindgen_test]
    fn test_chart_creation() {
        let chart = WasmChart::new();
        assert_eq!(chart.series_count(), 0);
    }

    #[wasm_bindgen_test]
    fn test_utils() {
        let version = WasmUtils::get_version();
        assert!(!version.is_empty());

        let themes = WasmUtils::get_available_themes();
        assert!(!themes.is_undefined());
    }

    #[wasm_bindgen_test]
    fn test_theme_manager() {
        let mut manager = WasmThemeManager::new();
        assert!(!manager.current_theme().is_empty());

        let themes = manager.available_themes();
        assert!(!themes.is_undefined());
    }
}
