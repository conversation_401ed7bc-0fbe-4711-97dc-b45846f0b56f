//! Crosshair component for chart interactions
//!
//! Provides crosshair (十字线) functionality for tracking mouse position
//! and displaying coordinate information.

use crate::{Color, DataValue, Point};
use serde::{Deserialize, Serialize};

/// Crosshair line style
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CrosshairLineStyle {
    /// Line color
    pub color: Color,

    /// Line width
    pub width: f64,

    /// Line dash pattern (empty for solid line)
    pub dash: Vec<f64>,

    /// Line opacity (0.0 to 1.0)
    pub opacity: f64,
}

impl Default for CrosshairLineStyle {
    fn default() -> Self {
        Self {
            color: Color::rgb(0.5, 0.5, 0.5),
            width: 1.0,
            dash: vec![5.0, 5.0], // 虚线样式
            opacity: 0.8,
        }
    }
}

/// Crosshair label style
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CrosshairLabelStyle {
    /// Background color
    pub background_color: Color,

    /// Text color
    pub text_color: Color,

    /// Font size
    pub font_size: f64,

    /// Padding
    pub padding: f64,

    /// Border radius
    pub border_radius: f64,

    /// Show shadow
    pub show_shadow: bool,
}

impl Default for CrosshairLabelStyle {
    fn default() -> Self {
        Self {
            background_color: Color::rgba(0.0, 0.0, 0.0, 0.8),
            text_color: Color::rgb(1.0, 1.0, 1.0),
            font_size: 12.0,
            padding: 4.0,
            border_radius: 3.0,
            show_shadow: true,
        }
    }
}

/// Crosshair configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CrosshairConfig {
    /// Whether crosshair is enabled
    pub enabled: bool,

    /// Whether to show horizontal line
    pub show_horizontal: bool,

    /// Whether to show vertical line
    pub show_vertical: bool,

    /// Whether to show coordinate labels
    pub show_labels: bool,

    /// Whether to snap to data points
    pub snap_to_data: bool,

    /// Snap distance threshold
    pub snap_threshold: f64,

    /// Horizontal line style
    pub horizontal_style: CrosshairLineStyle,

    /// Vertical line style
    pub vertical_style: CrosshairLineStyle,

    /// Label style
    pub label_style: CrosshairLabelStyle,
}

impl Default for CrosshairConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            show_horizontal: true,
            show_vertical: true,
            show_labels: true,
            snap_to_data: true,
            snap_threshold: 20.0,
            horizontal_style: CrosshairLineStyle::default(),
            vertical_style: CrosshairLineStyle::default(),
            label_style: CrosshairLabelStyle::default(),
        }
    }
}

/// Crosshair state
#[derive(Debug, Clone)]
pub struct CrosshairState {
    /// Current mouse position
    pub position: Option<Point>,

    /// Current data coordinates
    pub data_position: Option<Point>,

    /// Nearest data point information
    pub nearest_data: Option<NearestDataPoint>,

    /// Whether crosshair is visible
    pub visible: bool,
}

/// Information about the nearest data point
#[derive(Debug, Clone)]
pub struct NearestDataPoint {
    /// Series index
    pub series_index: usize,

    /// Data point index
    pub data_index: usize,

    /// Screen position
    pub screen_position: Point,

    /// Data values
    pub values: Vec<DataValue>,

    /// Distance from cursor
    pub distance: f64,

    /// Series name
    pub series_name: String,
}

impl Default for CrosshairState {
    fn default() -> Self {
        Self {
            position: None,
            data_position: None,
            nearest_data: None,
            visible: false,
        }
    }
}

/// Crosshair component
#[derive(Debug, Clone)]
pub struct Crosshair {
    /// Configuration
    pub config: CrosshairConfig,

    /// Current state
    pub state: CrosshairState,
}

impl Default for Crosshair {
    fn default() -> Self {
        Self::new()
    }
}

impl Crosshair {
    /// Create a new crosshair
    pub fn new() -> Self {
        Self {
            config: CrosshairConfig::default(),
            state: CrosshairState::default(),
        }
    }

    /// Create crosshair with custom config
    pub fn with_config(config: CrosshairConfig) -> Self {
        Self {
            config,
            state: CrosshairState::default(),
        }
    }

    /// Enable or disable crosshair
    pub fn set_enabled(&mut self, enabled: bool) {
        self.config.enabled = enabled;
        if !enabled {
            self.state.visible = false;
        }
    }

    /// Toggle crosshair visibility
    pub fn toggle(&mut self) {
        self.config.enabled = !self.config.enabled;
        if !self.config.enabled {
            self.state.visible = false;
        }
    }

    /// Update crosshair position
    pub fn update_position(&mut self, screen_pos: Point, data_pos: Option<Point>) {
        if !self.config.enabled {
            return;
        }

        self.state.position = Some(screen_pos);
        self.state.data_position = data_pos;
        self.state.visible = true;
    }

    /// Hide crosshair
    pub fn hide(&mut self) {
        self.state.visible = false;
        self.state.position = None;
        self.state.data_position = None;
        self.state.nearest_data = None;
    }

    /// Update nearest data point
    pub fn update_nearest_data(&mut self, nearest: Option<NearestDataPoint>) {
        self.state.nearest_data = nearest;
    }

    /// Get formatted coordinate text
    pub fn get_coordinate_text(&self) -> Option<(String, String)> {
        if let Some(data_pos) = &self.state.data_position {
            Some((
                format!("X: {:.2}", data_pos.x),
                format!("Y: {:.2}", data_pos.y),
            ))
        } else {
            None
        }
    }

    /// Get nearest data point text
    pub fn get_data_point_text(&self) -> Option<String> {
        if let Some(nearest) = &self.state.nearest_data {
            if nearest.values.len() >= 2 {
                Some(format!(
                    "{}: ({:.2}, {:.2})",
                    nearest.series_name,
                    nearest.values[0].as_number().unwrap_or(0.0),
                    nearest.values[1].as_number().unwrap_or(0.0)
                ))
            } else {
                Some(format!(
                    "{}: {:.2}",
                    nearest.series_name,
                    nearest
                        .values
                        .get(0)
                        .and_then(|v| v.as_number())
                        .unwrap_or(0.0)
                ))
            }
        } else {
            None
        }
    }

    /// Check if crosshair should be visible
    pub fn is_visible(&self) -> bool {
        self.config.enabled && self.state.visible
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_crosshair_creation() {
        let crosshair = Crosshair::new();
        assert!(crosshair.config.enabled);
        assert!(!crosshair.state.visible);
    }

    #[test]
    fn test_crosshair_toggle() {
        let mut crosshair = Crosshair::new();
        assert!(crosshair.config.enabled);

        crosshair.toggle();
        assert!(!crosshair.config.enabled);

        crosshair.toggle();
        assert!(crosshair.config.enabled);
    }

    #[test]
    fn test_crosshair_position_update() {
        let mut crosshair = Crosshair::new();
        let pos = Point::new(100.0, 200.0);
        let data_pos = Point::new(10.0, 20.0);

        crosshair.update_position(pos, Some(data_pos));

        assert!(crosshair.state.visible);
        assert_eq!(crosshair.state.position, Some(pos));
        assert_eq!(crosshair.state.data_position, Some(data_pos));
    }
}
