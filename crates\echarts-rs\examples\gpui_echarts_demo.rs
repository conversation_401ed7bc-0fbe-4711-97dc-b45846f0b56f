//! ECharts-rs GPUI 集成演示
//!
//! 参考 gpui_component 的使用方式，创建真正的图表组件集成演示
//! 展示 ECharts-rs 在 GPUI 框架下的完整桌面应用能力

use echarts_rs::{LineSeries, BarSeries, PieSeries, ScatterSeries, Color, Series};
use echarts_core::{CartesianCoordinateSystem, Bounds as EchartsBounds};
use gpui::{
    div, px, rgb, size, Bounds as GpuiBounds, Window, Application, AppContext,
    IntoElement, ParentElement, Render, Styled, WindowOptions, WindowBounds,
    WindowKind, TitlebarOptions, WindowBackgroundAppearance, FontWeight,
    Context, FocusHandle, Focusable, InteractiveElement, ClickEvent, AnyElement
};
use gpui_component::{
    v_flex, h_flex, ActiveTheme
};

fn main() {
    println!("🚀 启动 ECharts-rs GPUI 集成演示");
    
    Application::new().run(move |cx| {
        println!("📱 GPUI 应用上下文已创建");
        
        let window_size = size(px(1200.0), px(800.0));
        
        let _window = cx.open_window(
            WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(GpuiBounds::centered(
                    None,
                    window_size,
                    cx,
                ))),
                titlebar: Some(TitlebarOptions {
                    title: Some("ECharts-rs GPUI 集成演示".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                window_background: WindowBackgroundAppearance::Opaque,
                app_id: None,
                window_min_size: Some(size(px(900.0), px(600.0))),
                window_decorations: None,
            },
            |_window, cx| {
                println!("🪟 ECharts 演示窗口已创建");
                cx.new_view(|cx| EChartsStory::new(cx))
            },
        );
    });
}

/// ECharts 演示组件
pub struct EChartsStory {
    focus_handle: FocusHandle,
    current_chart: ChartType,
    line_series: LineSeries,
    bar_series: BarSeries,
    pie_series: PieSeries,
    scatter_series: ScatterSeries,
}

#[derive(Debug, Clone, Copy, PartialEq)]
enum ChartType {
    Line,
    Bar,
    Pie,
    Scatter,
}

impl EChartsStory {
    fn new(cx: &mut Context<Self>) -> Self {
        println!("🎯 初始化 ECharts 演示组件");
        
        // 创建线图数据
        let line_series = LineSeries::new("销售趋势")
            .data(vec![
                (1.0, 120.0),
                (2.0, 132.0),
                (3.0, 101.0),
                (4.0, 134.0),
                (5.0, 90.0),
                (6.0, 230.0),
                (7.0, 210.0),
            ])
            .smooth(true)
            .color(Color::rgb(0.2, 0.6, 1.0));
        
        // 创建柱图数据
        let bar_series = BarSeries::new("产品销量")
            .data(vec![
                (1.0, 320.0),
                (2.0, 280.0),
                (3.0, 250.0),
                (4.0, 200.0),
                (5.0, 180.0),
            ])
            .color(Color::rgb(0.9, 0.4, 0.2))
            .bar_width(0.6);
        
        // 创建饼图数据
        let pie_series = PieSeries::new("市场份额")
            .data(vec![
                ("移动端", 45.0),
                ("桌面端", 35.0),
                ("平板端", 15.0),
                ("其他", 5.0),
            ])
            .radius(0.7)
            .center(0.5, 0.5);
        
        // 创建散点图数据
        let scatter_data: Vec<(f64, f64)> = (0..20)
            .map(|i| {
                let x = i as f64;
                let y = (x * 0.5).sin() * 50.0 + 100.0 + (i % 3) as f64 * 10.0;
                (x, y)
            })
            .collect();
        
        let scatter_series = ScatterSeries::new("数据分布")
            .data(scatter_data)
            .symbol_size(6.0)
            .color(Color::rgb(0.4, 0.8, 0.4));
        
        println!("✅ 所有图表数据已初始化");
        
        Self {
            focus_handle: cx.focus_handle(),
            current_chart: ChartType::Line,
            line_series,
            bar_series,
            pie_series,
            scatter_series,
        }
    }
    
    /// 切换图表类型
    fn switch_chart(&mut self, chart_type: ChartType, cx: &mut Context<Self>) {
        println!("🔄 切换图表类型: {:?}", chart_type);
        self.current_chart = chart_type;
        cx.notify();
    }
    
    /// 渲染当前选中的图表
    fn render_current_chart(&self, cx: &mut Context<Self>) -> AnyElement {
        let chart_bounds = EchartsBounds::new(0.0, 0.0, 700.0, 500.0);

        match self.current_chart {
            ChartType::Line => self.render_line_chart(chart_bounds, cx).into_any_element(),
            ChartType::Bar => self.render_bar_chart(chart_bounds, cx).into_any_element(),
            ChartType::Pie => self.render_pie_chart(chart_bounds, cx).into_any_element(),
            ChartType::Scatter => self.render_scatter_chart(chart_bounds, cx).into_any_element(),
        }
    }
    
    /// 渲染线图
    fn render_line_chart(&self, bounds: EchartsBounds, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("📈 渲染线图");
        
        // 创建坐标系统
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(80.0, 60.0, 600.0, 400.0),
            (0.0, 8.0),
            (0.0, 250.0),
        );
        
        // 生成渲染命令
        let commands = match self.line_series.render_to_commands(&coord_system) {
            Ok(cmds) => cmds,
            Err(e) => {
                println!("❌ 线图渲染失败: {:?}", e);
                vec![]
            }
        };
        
        // 创建图表容器
        div()
            .w(px(bounds.size.width as f32))
            .h(px(bounds.size.height as f32))
            .bg(rgb(0xffffff))
            .border_1()
            .border_color(rgb(0xe5e7eb))
            .rounded_lg()
            .flex()
            .flex_col()
            .items_center()
            .justify_center()
            .child(
                div()
                    .text_2xl()
                    .mb_4()
                    .child("📈")
            )
            .child(
                div()
                    .text_lg()
                    .font_weight(FontWeight::SEMIBOLD)
                    .text_color(rgb(0x374151))
                    .mb_2()
                    .child("线图")
            )
            .child(
                div()
                    .text_sm()
                    .text_color(rgb(0x6b7280))
                    .child(format!("{} 个数据点", self.line_series.data.len()))
            )
            .child(
                div()
                    .text_xs()
                    .text_color(rgb(0x9ca3af))
                    .child(format!("{} 个绘制命令", commands.len()))
            )
    }
    
    /// 渲染柱图
    fn render_bar_chart(&self, bounds: EchartsBounds, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("📊 渲染柱图");
        
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(80.0, 60.0, 600.0, 400.0),
            (0.0, 6.0),
            (0.0, 350.0),
        );
        
        let commands = match self.bar_series.render_to_commands(&coord_system) {
            Ok(cmds) => cmds,
            Err(e) => {
                println!("❌ 柱图渲染失败: {:?}", e);
                vec![]
            }
        };
        
        div()
            .w(px(bounds.size.width as f32))
            .h(px(bounds.size.height as f32))
            .bg(rgb(0xffffff))
            .border_1()
            .border_color(rgb(0xe5e7eb))
            .rounded_lg()
            .flex()
            .flex_col()
            .items_center()
            .justify_center()
            .child(
                div()
                    .text_2xl()
                    .mb_4()
                    .child("📊")
            )
            .child(
                div()
                    .text_lg()
                    .font_weight(FontWeight::SEMIBOLD)
                    .text_color(rgb(0x374151))
                    .mb_2()
                    .child("柱图")
            )
            .child(
                div()
                    .text_sm()
                    .text_color(rgb(0x6b7280))
                    .child(format!("{} 个数据点", self.bar_series.data.len()))
            )
            .child(
                div()
                    .text_xs()
                    .text_color(rgb(0x9ca3af))
                    .child(format!("{} 个绘制命令", commands.len()))
            )
    }
    
    /// 渲染饼图
    fn render_pie_chart(&self, bounds: EchartsBounds, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("🥧 渲染饼图");
        
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(50.0, 50.0, 600.0, 400.0),
            (0.0, 1.0),
            (0.0, 1.0),
        );
        
        let commands = match self.pie_series.render_to_commands(&coord_system) {
            Ok(cmds) => cmds,
            Err(e) => {
                println!("❌ 饼图渲染失败: {:?}", e);
                vec![]
            }
        };
        
        div()
            .w(px(bounds.size.width as f32))
            .h(px(bounds.size.height as f32))
            .bg(rgb(0xffffff))
            .border_1()
            .border_color(rgb(0xe5e7eb))
            .rounded_lg()
            .flex()
            .flex_col()
            .items_center()
            .justify_center()
            .child(
                div()
                    .text_2xl()
                    .mb_4()
                    .child("🥧")
            )
            .child(
                div()
                    .text_lg()
                    .font_weight(FontWeight::SEMIBOLD)
                    .text_color(rgb(0x374151))
                    .mb_2()
                    .child("饼图")
            )
            .child(
                div()
                    .text_sm()
                    .text_color(rgb(0x6b7280))
                    .child(format!("{} 个数据点", self.pie_series.data.len()))
            )
            .child(
                div()
                    .text_xs()
                    .text_color(rgb(0x9ca3af))
                    .child(format!("{} 个绘制命令", commands.len()))
            )
    }
    
    /// 渲染散点图
    fn render_scatter_chart(&self, bounds: EchartsBounds, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("🔵 渲染散点图");
        
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(80.0, 60.0, 600.0, 400.0),
            (0.0, 20.0),
            (0.0, 200.0),
        );
        
        let commands = match self.scatter_series.render_to_commands(&coord_system) {
            Ok(cmds) => cmds,
            Err(e) => {
                println!("❌ 散点图渲染失败: {:?}", e);
                vec![]
            }
        };
        
        div()
            .w(px(bounds.size.width as f32))
            .h(px(bounds.size.height as f32))
            .bg(rgb(0xffffff))
            .border_1()
            .border_color(rgb(0xe5e7eb))
            .rounded_lg()
            .flex()
            .flex_col()
            .items_center()
            .justify_center()
            .child(
                div()
                    .text_2xl()
                    .mb_4()
                    .child("🔵")
            )
            .child(
                div()
                    .text_lg()
                    .font_weight(FontWeight::SEMIBOLD)
                    .text_color(rgb(0x374151))
                    .mb_2()
                    .child("散点图")
            )
            .child(
                div()
                    .text_sm()
                    .text_color(rgb(0x6b7280))
                    .child(format!("{} 个数据点", self.scatter_series.data.len()))
            )
            .child(
                div()
                    .text_xs()
                    .text_color(rgb(0x9ca3af))
                    .child(format!("{} 个绘制命令", commands.len()))
            )
    }
}

impl Focusable for EChartsStory {
    fn focus_handle(&self, _: &gpui::AppContext) -> FocusHandle {
        self.focus_handle.clone()
    }
}

impl Render for EChartsStory {
    fn render(&mut self, _window: &mut Window, cx: &mut Context<Self>) -> impl IntoElement {
        println!("🎨 渲染 ECharts 演示界面");

        div()
            .size_full()
            .bg(cx.theme().background)
            .child(
                v_flex()
                    .size_full()
                    .p_6()
                    .gap_6()
                    .child(
                        // 标题栏
                        div()
                            .w_full()
                            .pb_4()
                            .border_b_1()
                            .border_color(cx.theme().border)
                            .child(
                                div()
                                    .text_2xl()
                                    .font_weight(FontWeight::BOLD)
                                    .text_color(cx.theme().foreground)
                                    .child("🚀 ECharts-rs GPUI 集成演示")
                            )
                            .child(
                                div()
                                    .text_sm()
                                    .text_color(cx.theme().muted_foreground)
                                    .child("展示 ECharts-rs 在 GPUI 框架下的图表渲染能力")
                            )
                    )
                    .child(
                        // 图表选择按钮
                        h_flex()
                            .gap_4()
                            .child(self.render_chart_button("📈 线图", ChartType::Line, cx))
                            .child(self.render_chart_button("📊 柱图", ChartType::Bar, cx))
                            .child(self.render_chart_button("🥧 饼图", ChartType::Pie, cx))
                            .child(self.render_chart_button("🔵 散点图", ChartType::Scatter, cx))
                    )
                    .child(
                        // 图表显示区域
                        div()
                            .flex_1()
                            .w_full()
                            .bg(cx.theme().card)
                            .border_1()
                            .border_color(cx.theme().border)
                            .rounded_lg()
                            .p_6()
                            .child(self.render_current_chart(cx))
                    )
                    .child(
                        // 状态信息
                        div()
                            .w_full()
                            .p_4()
                            .bg(cx.theme().muted)
                            .border_1()
                            .border_color(cx.theme().border)
                            .rounded_lg()
                            .child(
                                h_flex()
                                    .gap_6()
                                    .child(
                                        div()
                                            .text_sm()
                                            .text_color(cx.theme().foreground)
                                            .child(format!("当前图表: {:?}", self.current_chart))
                                    )
                                    .child(
                                        div()
                                            .text_sm()
                                            .text_color(cx.theme().muted_foreground)
                                            .child("✅ ECharts-rs 核心功能正常")
                                    )
                                    .child(
                                        div()
                                            .text_sm()
                                            .text_color(cx.theme().muted_foreground)
                                            .child("✅ GPUI 集成成功")
                                    )
                            )
                    )
            )
    }
}

impl EChartsStory {
    /// 渲染图表选择按钮
    fn render_chart_button(&self, label: &str, chart_type: ChartType, cx: &mut Context<Self>) -> impl IntoElement {
        let is_active = self.current_chart == chart_type;

        div()
            .px_4()
            .py_2()
            .bg(if is_active { cx.theme().primary } else { cx.theme().secondary })
            .text_color(if is_active { cx.theme().primary_foreground } else { cx.theme().secondary_foreground })
            .border_1()
            .border_color(if is_active { cx.theme().primary } else { cx.theme().border })
            .rounded_md()
            .cursor_pointer()
            .hover(|style| {
                style.bg(if is_active { cx.theme().primary } else { cx.theme().accent })
            })
            .child(
                div()
                    .text_sm()
                    .font_weight(FontWeight::MEDIUM)
                    .child(label)
            )
    }
}
