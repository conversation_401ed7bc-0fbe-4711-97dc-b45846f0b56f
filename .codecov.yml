# ECharts-rs 代码覆盖率配置

coverage:
  precision: 2
  round: down
  range: "70...100"
  
  status:
    project:
      default:
        target: 80%
        threshold: 1%
        if_ci_failed: error
    patch:
      default:
        target: 75%
        threshold: 2%
        if_ci_failed: error

  ignore:
    - "examples/**/*"
    - "benches/**/*"
    - "tests/**/*"
    - "**/tests.rs"
    - "**/test_*.rs"
    - "**/*_test.rs"
    - "target/**/*"

comment:
  layout: "reach,diff,flags,tree"
  behavior: default
  require_changes: false
  require_base: no
  require_head: yes

github_checks:
  annotations: true

parsers:
  gcov:
    branch_detection:
      conditional: yes
      loop: yes
      method: no
      macro: no

component_management:
  default_rules:
    statuses:
      - type: project
        target: 80%
      - type: patch
        target: 75%
  individual_components:
    - component_id: core
      name: "Core Module"
      paths:
        - "crates/echarts-rs/crates/core/**/*"
      statuses:
        - type: project
          target: 85%
    - component_id: charts
      name: "Charts Module"  
      paths:
        - "crates/echarts-rs/crates/charts/**/*"
      statuses:
        - type: project
          target: 80%
    - component_id: renderer
      name: "Renderer Module"
      paths:
        - "crates/echarts-rs/crates/renderer/**/*"
      statuses:
        - type: project
          target: 75%
    - component_id: themes
      name: "Themes Module"
      paths:
        - "crates/echarts-rs/crates/themes/**/*"
      statuses:
        - type: project
          target: 70%
    - component_id: components
      name: "Components Module"
      paths:
        - "crates/echarts-rs/crates/components/**/*"
      statuses:
        - type: project
          target: 70%
