#!/usr/bin/env python3
"""
批量修复字段访问问题
"""

import os
import re

def fix_field_access_in_file(file_path):
    """修复单个文件中的字段访问问题"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复 &self.config().name -> &self.config.name
        content = re.sub(r'&self\.config\(\)\.name', '&self.config.name', content)
        
        # 修复 self.config().visible -> self.config.visible
        content = re.sub(r'self\.config\(\)\.visible', 'self.config.visible', content)
        
        # 修复 self.config().z_index -> self.config.z_index
        content = re.sub(r'self\.config\(\)\.z_index', 'self.config.z_index', content)
        
        # 修复 &self.config -> &self.config (这个应该已经正确)
        # 修复 &mut self.config -> &mut self.config (这个应该已经正确)
        
        # 修复数据项中的 config 访问（这些应该是 visible 字段）
        # 例如：self.config.visible = visible; -> self.visible = visible;
        # 但只对数据项类型进行修复，不对系列类型修复
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        return False
        
    except Exception as e:
        print(f"  ❌ 修复失败: {e}")
        return False

def fix_data_item_field_access():
    """修复数据项中的字段访问问题"""
    
    # 需要修复的文件和对应的数据项类型
    files_to_fix = [
        ("crates/charts/src/treemap.rs", "TreemapDataItem"),
        ("crates/charts/src/sunburst.rs", "SunburstDataItem"), 
        ("crates/charts/src/funnel.rs", "FunnelDataItem"),
        ("crates/charts/src/candlestick.rs", "CandlestickDataItem"),
    ]
    
    for file_path, data_item_type in files_to_fix:
        if not os.path.exists(file_path):
            continue
            
        print(f"修复 {file_path} 中的 {data_item_type} 字段访问...")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找数据项的 visible 方法，将 self.config.visible 改为 self.visible
            # 这是因为数据项类型没有 config 字段，只有 visible 字段
            pattern = rf'(impl {data_item_type}.*?pub fn visible.*?self\.config\.visible = visible;)'
            
            def replace_config_visible(match):
                return match.group(1).replace('self.config.visible = visible;', 'self.visible = visible;')
            
            content = re.sub(pattern, replace_config_visible, content, flags=re.DOTALL)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ 已修复 {data_item_type}")
                
        except Exception as e:
            print(f"  ❌ 修复失败 {data_item_type}: {e}")

def main():
    """主函数"""
    print("🚀 开始修复字段访问问题...")
    
    # 需要修复的系列文件
    series_files = [
        "crates/charts/src/sunburst.rs",
        "crates/charts/src/funnel.rs",
        "crates/charts/src/treemap.rs",
        "crates/charts/src/candlestick.rs",
        "crates/charts/src/gauge.rs",
    ]
    
    fixed_count = 0
    for file_path in series_files:
        if os.path.exists(file_path):
            print(f"修复系列字段访问: {file_path}")
            if fix_field_access_in_file(file_path):
                fixed_count += 1
                print(f"  ✅ 已修复")
            else:
                print(f"  ⏭️  无需修复")
        else:
            print(f"  ❌ 文件不存在: {file_path}")
    
    # 修复数据项字段访问
    fix_data_item_field_access()
    
    print(f"✅ 修复完成！共修复 {fixed_count} 个系列文件")

if __name__ == "__main__":
    main()
