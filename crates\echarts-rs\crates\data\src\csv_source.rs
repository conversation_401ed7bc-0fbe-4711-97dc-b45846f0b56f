//! CSV data source implementation

use crate::{DataSource, DataSourceMetadata, ColumnInfo, DataType, MissingValueStrategy};
use echarts_core::*;
use csv::{<PERSON>, ReaderBuilder, StringRecord};
use serde::{Deserialize, Serialize};
use std::fs::File;
use std::io::BufReader;
use std::path::Path;

/// CSV data source configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CsvDataSource {
    /// Path to the CSV file
    pub file_path: String,
    /// Whether the CSV has headers
    pub has_headers: bool,
    /// Field delimiter
    pub delimiter: u8,
    /// Quote character
    pub quote: u8,
    /// Escape character
    pub escape: Option<u8>,
    /// Whether to skip empty lines
    pub skip_empty_lines: bool,
    /// Maximum number of rows to read (None for all)
    pub max_rows: Option<usize>,
    /// Column type overrides
    pub column_types: std::collections::HashMap<String, DataType>,
    /// Missing value handling
    pub missing_value_strategy: MissingValueStrategy,
}

impl Default for CsvDataSource {
    fn default() -> Self {
        Self {
            file_path: String::new(),
            has_headers: true,
            delimiter: b',',
            quote: b'"',
            escape: None,
            skip_empty_lines: true,
            max_rows: None,
            column_types: std::collections::HashMap::new(),
            missing_value_strategy: MissingValueStrategy::Skip,
        }
    }
}

impl CsvDataSource {
    /// Create a new CSV data source
    pub fn new<P: AsRef<Path>>(file_path: P) -> Self {
        Self {
            file_path: file_path.as_ref().to_string_lossy().to_string(),
            ..Default::default()
        }
    }

    /// Set whether the CSV has headers
    pub fn with_headers(mut self, has_headers: bool) -> Self {
        self.has_headers = has_headers;
        self
    }

    /// Set the field delimiter
    pub fn with_delimiter(mut self, delimiter: u8) -> Self {
        self.delimiter = delimiter;
        self
    }

    /// Set the quote character
    pub fn with_quote(mut self, quote: u8) -> Self {
        self.quote = quote;
        self
    }

    /// Set maximum number of rows to read
    pub fn with_max_rows(mut self, max_rows: usize) -> Self {
        self.max_rows = Some(max_rows);
        self
    }

    /// Set column type override
    pub fn with_column_type<S: Into<String>>(mut self, column: S, data_type: DataType) -> Self {
        self.column_types.insert(column.into(), data_type);
        self
    }

    /// Set missing value strategy
    pub fn with_missing_value_strategy(mut self, strategy: MissingValueStrategy) -> Self {
        self.missing_value_strategy = strategy;
        self
    }

    /// Infer data types from sample data
    fn infer_data_types(&self, records: &[StringRecord], headers: &[String]) -> Vec<DataType> {
        let mut column_types = Vec::new();
        
        for (col_index, header) in headers.iter().enumerate() {
            // Check if type is explicitly set
            if let Some(&explicit_type) = self.column_types.get(header) {
                column_types.push(explicit_type);
                continue;
            }

            // Infer type from sample data
            let mut is_integer = true;
            let mut is_float = true;
            let mut is_boolean = true;
            let mut is_datetime = true;

            for record in records.iter().take(100) { // Sample first 100 records
                if let Some(value) = record.get(col_index) {
                    let trimmed = value.trim();
                    
                    if trimmed.is_empty() {
                        continue; // Skip empty values for type inference
                    }

                    // Check integer
                    if is_integer && trimmed.parse::<i64>().is_err() {
                        is_integer = false;
                    }

                    // Check float
                    if is_float && trimmed.parse::<f64>().is_err() {
                        is_float = false;
                    }

                    // Check boolean
                    if is_boolean {
                        let lower = trimmed.to_lowercase();
                        if !matches!(lower.as_str(), "true" | "false" | "1" | "0" | "yes" | "no") {
                            is_boolean = false;
                        }
                    }

                    // Check datetime (simplified)
                    if is_datetime {
                        if chrono::DateTime::parse_from_rfc3339(trimmed).is_err() &&
                           chrono::NaiveDateTime::parse_from_str(trimmed, "%Y-%m-%d %H:%M:%S").is_err() &&
                           chrono::NaiveDate::parse_from_str(trimmed, "%Y-%m-%d").is_err() {
                            is_datetime = false;
                        }
                    }
                }
            }

            // Determine the most specific type
            let inferred_type = if is_boolean {
                DataType::Boolean
            } else if is_integer {
                DataType::Integer
            } else if is_float {
                DataType::Float
            } else if is_datetime {
                DataType::DateTime
            } else {
                DataType::String
            };

            column_types.push(inferred_type);
        }

        column_types
    }

    /// Parse a value according to its data type
    fn parse_value(&self, value: &str, data_type: DataType) -> Result<DataValue> {
        let trimmed = value.trim();
        
        if trimmed.is_empty() {
            return Ok(DataValue::Null);
        }

        match data_type {
            DataType::String => Ok(DataValue::String(trimmed.to_string())),
            DataType::Integer => {
                trimmed.parse::<i64>()
                    .map(|i| DataValue::Number(i as f64))
                    .map_err(|_| ChartError::DataParsing(format!("Invalid integer: {}", trimmed)))
            }
            DataType::Float => {
                trimmed.parse::<f64>()
                    .map(DataValue::Number)
                    .map_err(|_| ChartError::DataParsing(format!("Invalid float: {}", trimmed)))
            }
            DataType::Boolean => {
                let lower = trimmed.to_lowercase();
                match lower.as_str() {
                    "true" | "1" | "yes" => Ok(DataValue::Boolean(true)),
                    "false" | "0" | "no" => Ok(DataValue::Boolean(false)),
                    _ => Err(ChartError::DataParsing(format!("Invalid boolean: {}", trimmed)))
                }
            }
            DataType::DateTime => {
                // Try different datetime formats
                if let Ok(dt) = chrono::DateTime::parse_from_rfc3339(trimmed) {
                    Ok(DataValue::DateTime(dt.with_timezone(&chrono::Utc)))
                } else if let Ok(ndt) = chrono::NaiveDateTime::parse_from_str(trimmed, "%Y-%m-%d %H:%M:%S") {
                    Ok(DataValue::DateTime(chrono::DateTime::from_utc(ndt, chrono::Utc)))
                } else if let Ok(nd) = chrono::NaiveDate::parse_from_str(trimmed, "%Y-%m-%d") {
                    let ndt = nd.and_hms_opt(0, 0, 0).unwrap();
                    Ok(DataValue::DateTime(chrono::DateTime::from_utc(ndt, chrono::Utc)))
                } else {
                    Err(ChartError::DataParsing(format!("Invalid datetime: {}", trimmed)))
                }
            }
            DataType::Date => {
                chrono::NaiveDate::parse_from_str(trimmed, "%Y-%m-%d")
                    .map(|d| DataValue::Date(d))
                    .map_err(|_| ChartError::DataParsing(format!("Invalid date: {}", trimmed)))
            }
            DataType::Time => {
                chrono::NaiveTime::parse_from_str(trimmed, "%H:%M:%S")
                    .map(|t| DataValue::Time(t))
                    .map_err(|_| ChartError::DataParsing(format!("Invalid time: {}", trimmed)))
            }
            DataType::Json => {
                serde_json::from_str(trimmed)
                    .map(DataValue::Json)
                    .map_err(|_| ChartError::DataParsing(format!("Invalid JSON: {}", trimmed)))
            }
            DataType::Binary => {
                // For CSV, binary data would typically be base64 encoded
                // For now, just store as string representation
                Ok(DataValue::String(trimmed.to_string()))
            }
        }
    }

    /// Handle missing values according to the strategy
    fn handle_missing_value(&self, data_type: DataType) -> DataValue {
        match self.missing_value_strategy {
            MissingValueStrategy::FillDefault => {
                match data_type {
                    DataType::String => DataValue::String(String::new()),
                    DataType::Integer | DataType::Float => DataValue::Number(0.0),
                    DataType::Boolean => DataValue::Boolean(false),
                    DataType::DateTime => DataValue::DateTime(chrono::Utc::now()),
                    DataType::Date => DataValue::Date(chrono::Utc::now().date_naive()),
                    DataType::Time => DataValue::Time(chrono::NaiveTime::from_hms_opt(0, 0, 0).unwrap()),
                    DataType::Json => DataValue::Json(serde_json::Value::Null),
                    DataType::Binary => DataValue::Binary(Vec::new()),
                }
            }
            _ => DataValue::Null,
        }
    }
}

impl DataSource for CsvDataSource {
    fn load(&self) -> Result<DataSet> {
        let file = File::open(&self.file_path)
            .map_err(|e| ChartError::Io(e))?;
        
        let mut reader = ReaderBuilder::new()
            .has_headers(self.has_headers)
            .delimiter(self.delimiter)
            .quote(self.quote)
            .from_reader(BufReader::new(file));

        // Read headers
        let headers = if self.has_headers {
            reader.headers()
                .map_err(|e| ChartError::DataParsing(format!("Failed to read CSV headers: {}", e)))?
                .iter()
                .map(|h| h.to_string())
                .collect()
        } else {
            // Generate default column names
            let first_record = reader.records().next();
            if let Some(Ok(record)) = first_record {
                (0..record.len()).map(|i| format!("column_{}", i)).collect()
            } else {
                return Err(ChartError::DataParsing("Empty CSV file".into()));
            }
        };

        // Read all records for type inference
        let mut all_records = Vec::new();
        for result in reader.records() {
            let record = result
                .map_err(|e| ChartError::DataParsing(format!("Failed to read CSV record: {}", e)))?;
            all_records.push(record);
            
            if let Some(max_rows) = self.max_rows {
                if all_records.len() >= max_rows {
                    break;
                }
            }
        }

        // Infer data types
        let column_types = self.infer_data_types(&all_records, &headers);

        // Parse data points
        let mut data_points = Vec::new();
        
        for record in all_records {
            let mut values = Vec::new();
            let mut skip_row = false;

            for (col_index, data_type) in column_types.iter().enumerate() {
                let raw_value = record.get(col_index).unwrap_or("");
                
                match self.parse_value(raw_value, *data_type) {
                    Ok(DataValue::Null) => {
                        match self.missing_value_strategy {
                            MissingValueStrategy::Skip => {
                                skip_row = true;
                                break;
                            }
                            _ => {
                                values.push(self.handle_missing_value(*data_type));
                            }
                        }
                    }
                    Ok(value) => values.push(value),
                    Err(_) => {
                        if matches!(self.missing_value_strategy, MissingValueStrategy::Skip) {
                            skip_row = true;
                            break;
                        } else {
                            values.push(self.handle_missing_value(*data_type));
                        }
                    }
                }
            }

            if !skip_row {
                data_points.push(DataPoint::new(values));
            }
        }

        Ok(DataSet {
            dimensions: headers,
            points: data_points,
            metadata: std::collections::HashMap::new(),
        })
    }

    fn metadata(&self) -> DataSourceMetadata {
        // Try to get file metadata
        let last_modified = std::fs::metadata(&self.file_path)
            .and_then(|m| m.modified())
            .ok()
            .and_then(|t| chrono::DateTime::from(t).into());

        // Estimate row count by reading the file
        let estimated_rows = if Path::new(&self.file_path).exists() {
            std::fs::read_to_string(&self.file_path)
                .map(|content| content.lines().count())
                .ok()
        } else {
            None
        };

        DataSourceMetadata {
            source_type: "CSV".to_string(),
            location: self.file_path.clone(),
            estimated_rows,
            columns: Vec::new(), // Would be populated by analyzing the file
            last_modified,
        }
    }

    fn is_available(&self) -> bool {
        Path::new(&self.file_path).exists()
    }
}

/// Utility functions for CSV processing
pub struct CsvUtils;

impl CsvUtils {
    /// Detect CSV delimiter by analyzing the file
    pub fn detect_delimiter<P: AsRef<Path>>(file_path: P) -> Result<u8> {
        let content = std::fs::read_to_string(file_path)
            .map_err(|e| ChartError::Io(e))?;
        
        let sample = content.lines().take(10).collect::<Vec<_>>().join("\n");
        
        let delimiters = [b',', b';', b'\t', b'|'];
        let mut delimiter_counts = std::collections::HashMap::new();
        
        for &delimiter in &delimiters {
            let count = sample.matches(delimiter as char).count();
            delimiter_counts.insert(delimiter, count);
        }
        
        delimiter_counts
            .into_iter()
            .max_by_key(|(_, count)| *count)
            .map(|(delimiter, _)| delimiter)
            .ok_or_else(|| ChartError::DataParsing("Could not detect CSV delimiter".into()))
    }

    /// Validate CSV file structure
    pub fn validate_csv<P: AsRef<Path>>(file_path: P) -> Result<CsvValidationResult> {
        let file = File::open(file_path)
            .map_err(|e| ChartError::Io(e))?;
        
        let mut reader = csv::Reader::from_reader(BufReader::new(file));
        let mut row_count = 0;
        let mut column_count = None;
        let mut errors = Vec::new();
        
        for (line_num, result) in reader.records().enumerate() {
            match result {
                Ok(record) => {
                    row_count += 1;
                    
                    if let Some(expected_cols) = column_count {
                        if record.len() != expected_cols {
                            errors.push(format!("Line {}: Expected {} columns, found {}", 
                                               line_num + 1, expected_cols, record.len()));
                        }
                    } else {
                        column_count = Some(record.len());
                    }
                }
                Err(e) => {
                    errors.push(format!("Line {}: {}", line_num + 1, e));
                }
            }
        }
        
        Ok(CsvValidationResult {
            is_valid: errors.is_empty(),
            row_count,
            column_count: column_count.unwrap_or(0),
            errors,
        })
    }
}

/// Result of CSV validation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CsvValidationResult {
    pub is_valid: bool,
    pub row_count: usize,
    pub column_count: usize,
    pub errors: Vec<String>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::NamedTempFile;

    #[test]
    fn test_csv_data_source() {
        // Create a temporary CSV file
        let mut temp_file = NamedTempFile::new().unwrap();
        writeln!(temp_file, "name,age,score").unwrap();
        writeln!(temp_file, "Alice,25,95.5").unwrap();
        writeln!(temp_file, "Bob,30,87.2").unwrap();
        writeln!(temp_file, "Charlie,35,92.1").unwrap();
        
        let csv_source = CsvDataSource::new(temp_file.path())
            .with_headers(true);
        
        assert!(csv_source.is_available());
        
        let dataset = csv_source.load().unwrap();
        assert_eq!(dataset.dimensions.len(), 3);
        assert_eq!(dataset.points.len(), 3);
        assert_eq!(dataset.dimensions[0], "name");
        assert_eq!(dataset.dimensions[1], "age");
        assert_eq!(dataset.dimensions[2], "score");
    }

    #[test]
    fn test_csv_type_inference() {
        let mut temp_file = NamedTempFile::new().unwrap();
        writeln!(temp_file, "text,number,flag,date").unwrap();
        writeln!(temp_file, "hello,42,true,2023-01-01").unwrap();
        writeln!(temp_file, "world,3.14,false,2023-12-31").unwrap();
        
        let csv_source = CsvDataSource::new(temp_file.path());
        let dataset = csv_source.load().unwrap();
        
        assert_eq!(dataset.dimensions.len(), 4);
        assert_eq!(dataset.points.len(), 2);
    }

    #[test]
    fn test_delimiter_detection() {
        let mut temp_file = NamedTempFile::new().unwrap();
        writeln!(temp_file, "a;b;c").unwrap();
        writeln!(temp_file, "1;2;3").unwrap();
        
        let delimiter = CsvUtils::detect_delimiter(temp_file.path()).unwrap();
        assert_eq!(delimiter, b';');
    }
}
