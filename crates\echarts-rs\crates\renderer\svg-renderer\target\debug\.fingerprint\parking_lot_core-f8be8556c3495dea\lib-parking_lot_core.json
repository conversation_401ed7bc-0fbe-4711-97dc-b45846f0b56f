{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 12558056885032795287, "profile": 15657897354478470176, "path": 16547318230551699138, "deps": [[2828590642173593838, "cfg_if", false, 10897771254427737185], [3666196340704888985, "smallvec", false, 14519369749394223245], [4269498962362888130, "build_script_build", false, 14189337319115818980], [14322346790800707264, "windows_targets", false, 8187617998473925482]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\parking_lot_core-f8be8556c3495dea\\dep-lib-parking_lot_core", "checksum": false}}], "rustflags": ["-C", "link-arg=/STACK:16000000"], "config": 2069994364910194474, "compile_kind": 0}