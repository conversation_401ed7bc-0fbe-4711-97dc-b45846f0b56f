# ECharts 与 GPUI Component 集成方案

## 集成概述

本方案旨在将 Apache ECharts 的强大图表功能与 GPUI Component 的现代桌面 UI 框架深度集成，创建高性能、原生体验的数据可视化桌面应用。

## 集成架构

### 1. 整体架构设计

```
┌─────────────────────────────────────────────────────────┐
│                 GPUI Desktop Application                │
├─────────────────────────────────────────────────────────┤
│  GPUI Component UI Framework                           │
│  ┌─────────────────┐  ┌─────────────────────────────┐   │
│  │   Native UI     │  │    Chart Integration       │   │
│  │   Components    │  │                             │   │
│  │                 │  │  ┌─────────────────────────┐ │   │
│  │ • Button        │  │  │   ECharts Wrapper       │ │   │
│  │ • Input         │  │  │                         │ │   │
│  │ • Table         │  │  │ • WebView Container     │ │   │
│  │ • Layout        │  │  │ • Canvas Renderer       │ │   │
│  │ • Theme         │  │  │ • Event Bridge          │ │   │
│  └─────────────────┘  │  │ • Data Binding          │ │   │
│                       │  └─────────────────────────┘ │   │
│                       └─────────────────────────────────┘   │
├─────────────────────────────────────────────────────────┤
│                    GPUI Rendering Engine                │
│                    (GPU Accelerated)                    │
└─────────────────────────────────────────────────────────┘
```

### 2. 集成策略

#### 策略 A: WebView 嵌入方案
- 使用 GPUI Component 的 WebView 组件
- ECharts 运行在 Web 环境中
- 通过 JavaScript Bridge 进行通信

#### 策略 B: Canvas 渲染方案
- 将 ECharts 渲染到 Canvas
- 将 Canvas 作为纹理嵌入 GPUI
- 直接在 Rust 中处理交互

#### 策略 C: 原生重写方案
- 使用 Rust 重写 ECharts 核心功能
- 完全集成到 GPUI Component 中
- 最佳性能和集成度

## 方案一：WebView 嵌入集成

### 1. 技术实现

```rust
use gpui_component::{WebView, h_flex, v_flex};
use serde_json::Value;

pub struct EChartsWebView {
    webview: WebView,
    option: Value,
    theme: String,
}

impl EChartsWebView {
    pub fn new(cx: &mut WindowContext) -> Self {
        let webview = WebView::new(cx)
            .with_url("echarts://chart.html")
            .with_dev_tools(cfg!(debug_assertions));
            
        Self {
            webview,
            option: Value::Null,
            theme: "default".to_string(),
        }
    }
    
    pub fn set_option(&mut self, option: Value) {
        self.option = option;
        self.update_chart();
    }
    
    fn update_chart(&self) {
        let script = format!(
            "window.chart.setOption({});",
            serde_json::to_string(&self.option).unwrap()
        );
        self.webview.execute_script(&script);
    }
}

impl RenderOnce for EChartsWebView {
    fn render(self, cx: &mut WindowContext) -> impl IntoElement {
        v_flex()
            .size_full()
            .child(self.webview)
    }
}
```

### 2. JavaScript Bridge

```javascript
// echarts-bridge.js
class EChartsBridge {
    constructor() {
        this.chart = null;
        this.setupChart();
        this.setupBridge();
    }
    
    setupChart() {
        const container = document.getElementById('chart-container');
        this.chart = echarts.init(container);
        
        // 监听图表事件
        this.chart.on('click', (params) => {
            window.gpui.postMessage({
                type: 'chart-click',
                data: params
            });
        });
    }
    
    setupBridge() {
        window.addEventListener('message', (event) => {
            const { type, data } = event.data;
            
            switch (type) {
                case 'set-option':
                    this.chart.setOption(data);
                    break;
                case 'resize':
                    this.chart.resize();
                    break;
                case 'set-theme':
                    this.applyTheme(data);
                    break;
            }
        });
    }
    
    applyTheme(theme) {
        this.chart.dispose();
        this.chart = echarts.init(
            document.getElementById('chart-container'),
            theme
        );
    }
}

new EChartsBridge();
```

### 3. 主题同步

```rust
impl EChartsWebView {
    pub fn sync_theme(&mut self, gpui_theme: &Theme) {
        let echarts_theme = self.convert_theme(gpui_theme);
        let script = format!(
            "window.bridge.applyTheme({});",
            serde_json::to_string(&echarts_theme).unwrap()
        );
        self.webview.execute_script(&script);
    }
    
    fn convert_theme(&self, theme: &Theme) -> Value {
        json!({
            "backgroundColor": theme.background.to_hex(),
            "textStyle": {
                "color": theme.foreground.to_hex(),
                "fontFamily": theme.font_family,
                "fontSize": theme.font_size.0
            },
            "color": [
                theme.chart_1.to_hex(),
                theme.chart_2.to_hex(),
                theme.chart_3.to_hex(),
                theme.chart_4.to_hex(),
                theme.chart_5.to_hex(),
            ],
            "grid": {
                "borderColor": theme.border.to_hex()
            },
            "categoryAxis": {
                "axisLine": {
                    "lineStyle": {
                        "color": theme.border.to_hex()
                    }
                },
                "axisLabel": {
                    "color": theme.muted_foreground.to_hex()
                }
            }
        })
    }
}
```

## 方案二：Canvas 渲染集成

### 1. 离屏渲染

```rust
use gpui::{Canvas, Texture, Size};

pub struct EChartsCanvas {
    canvas: Canvas,
    texture: Option<Texture>,
    chart_instance: EChartsInstance,
    size: Size<Pixels>,
}

impl EChartsCanvas {
    pub fn new(size: Size<Pixels>) -> Self {
        let canvas = Canvas::new(size);
        let chart_instance = EChartsInstance::new(&canvas);
        
        Self {
            canvas,
            texture: None,
            chart_instance,
            size,
        }
    }
    
    pub fn set_option(&mut self, option: Value) {
        self.chart_instance.set_option(option);
        self.render_to_texture();
    }
    
    fn render_to_texture(&mut self) {
        // 渲染 ECharts 到 Canvas
        self.chart_instance.render();
        
        // 将 Canvas 转换为 GPU 纹理
        let image_data = self.canvas.get_image_data();
        self.texture = Some(Texture::from_image_data(image_data));
    }
}

impl RenderOnce for EChartsCanvas {
    fn render(self, cx: &mut WindowContext) -> impl IntoElement {
        div()
            .size(self.size)
            .child(
                canvas(move |bounds, cx| {
                    if let Some(texture) = &self.texture {
                        cx.paint_texture(bounds, texture);
                    }
                })
            )
    }
}
```

### 2. 事件处理

```rust
impl EChartsCanvas {
    pub fn handle_mouse_event(&mut self, event: &MouseEvent) -> bool {
        let local_point = self.global_to_local(event.position);
        
        // 将鼠标事件转发给 ECharts
        match event.kind {
            MouseEventKind::Down => {
                self.chart_instance.on_mouse_down(local_point);
            }
            MouseEventKind::Move => {
                self.chart_instance.on_mouse_move(local_point);
            }
            MouseEventKind::Up => {
                self.chart_instance.on_mouse_up(local_point);
            }
        }
        
        // 检查是否需要重新渲染
        if self.chart_instance.needs_update() {
            self.render_to_texture();
            true
        } else {
            false
        }
    }
}
```

## 方案三：原生 Rust 重写

### 1. 核心图表组件

```rust
// 基于 GPUI Component 的原生图表实现
pub struct NativeLineChart<T> {
    data: Vec<T>,
    x_accessor: Box<dyn Fn(&T) -> f64>,
    y_accessor: Box<dyn Fn(&T) -> f64>,
    style: LineChartStyle,
    animation: Option<Animation>,
}

impl<T> NativeLineChart<T> {
    pub fn new(data: Vec<T>) -> Self {
        Self {
            data,
            x_accessor: Box::new(|_| 0.0),
            y_accessor: Box::new(|_| 0.0),
            style: LineChartStyle::default(),
            animation: None,
        }
    }
    
    pub fn x<F>(mut self, accessor: F) -> Self 
    where F: Fn(&T) -> f64 + 'static {
        self.x_accessor = Box::new(accessor);
        self
    }
    
    pub fn y<F>(mut self, accessor: F) -> Self 
    where F: Fn(&T) -> f64 + 'static {
        self.y_accessor = Box::new(accessor);
        self
    }
}

impl<T> RenderOnce for NativeLineChart<T> {
    fn render(self, cx: &mut WindowContext) -> impl IntoElement {
        canvas(move |bounds, cx| {
            let renderer = LineChartRenderer::new(bounds, &self.style);
            renderer.render(&self.data, &self.x_accessor, &self.y_accessor, cx);
        })
    }
}
```

### 2. 渲染引擎

```rust
pub struct LineChartRenderer {
    bounds: Bounds<Pixels>,
    style: LineChartStyle,
}

impl LineChartRenderer {
    pub fn render<T>(
        &self,
        data: &[T],
        x_accessor: &dyn Fn(&T) -> f64,
        y_accessor: &dyn Fn(&T) -> f64,
        cx: &mut PaintContext,
    ) {
        // 计算数据范围
        let x_range = self.calculate_x_range(data, x_accessor);
        let y_range = self.calculate_y_range(data, y_accessor);
        
        // 创建比例尺
        let x_scale = LinearScale::new(x_range, (0.0, self.bounds.size.width.0));
        let y_scale = LinearScale::new(y_range, (self.bounds.size.height.0, 0.0));
        
        // 渲染坐标轴
        self.render_axes(&x_scale, &y_scale, cx);
        
        // 渲染数据线
        self.render_line(data, x_accessor, y_accessor, &x_scale, &y_scale, cx);
        
        // 渲染数据点
        if self.style.show_points {
            self.render_points(data, x_accessor, y_accessor, &x_scale, &y_scale, cx);
        }
    }
    
    fn render_line<T>(
        &self,
        data: &[T],
        x_accessor: &dyn Fn(&T) -> f64,
        y_accessor: &dyn Fn(&T) -> f64,
        x_scale: &LinearScale,
        y_scale: &LinearScale,
        cx: &mut PaintContext,
    ) {
        let mut path = Path::new();
        
        for (i, item) in data.iter().enumerate() {
            let x = x_scale.scale(x_accessor(item));
            let y = y_scale.scale(y_accessor(item));
            
            if i == 0 {
                path.move_to(point(px(x), px(y)));
            } else {
                path.line_to(point(px(x), px(y)));
            }
        }
        
        cx.paint_path(path, self.style.stroke_color);
    }
}
```

## 数据绑定和状态管理

### 1. 响应式数据绑定

```rust
use gpui::{Model, ModelContext};

#[derive(Clone)]
pub struct ChartData<T> {
    items: Vec<T>,
    version: usize,
}

impl<T> ChartData<T> {
    pub fn new(items: Vec<T>) -> Model<Self> {
        Model::new(Self { items, version: 0 })
    }
    
    pub fn update(&mut self, items: Vec<T>, cx: &mut ModelContext<Self>) {
        self.items = items;
        self.version += 1;
        cx.notify();
    }
}

pub struct ReactiveChart<T> {
    data: Model<ChartData<T>>,
    chart_type: ChartType,
}

impl<T> ReactiveChart<T> {
    pub fn new(data: Model<ChartData<T>>, chart_type: ChartType) -> Self {
        Self { data, chart_type }
    }
}

impl<T> RenderOnce for ReactiveChart<T> 
where T: Clone + 'static {
    fn render(self, cx: &mut WindowContext) -> impl IntoElement {
        let data = self.data.read(cx);
        
        match self.chart_type {
            ChartType::Line => {
                NativeLineChart::new(data.items.clone())
                    .render(cx)
            }
            ChartType::Bar => {
                NativeBarChart::new(data.items.clone())
                    .render(cx)
            }
            // ...
        }
    }
}
```

### 2. 图表交互

```rust
pub struct InteractiveChart<T> {
    chart: ReactiveChart<T>,
    on_click: Option<Rc<dyn Fn(&T, usize)>>,
    on_hover: Option<Rc<dyn Fn(Option<&T>)>>,
    tooltip: Option<TooltipConfig>,
}

impl<T> InteractiveChart<T> {
    pub fn on_click<F>(mut self, handler: F) -> Self 
    where F: Fn(&T, usize) + 'static {
        self.on_click = Some(Rc::new(handler));
        self
    }
    
    pub fn with_tooltip(mut self, config: TooltipConfig) -> Self {
        self.tooltip = Some(config);
        self
    }
}
```

## 性能优化策略

### 1. 渲染优化

```rust
pub struct ChartCache {
    texture_cache: HashMap<u64, Texture>,
    geometry_cache: HashMap<u64, Vec<Vertex>>,
    last_bounds: Option<Bounds<Pixels>>,
}

impl ChartCache {
    pub fn get_or_render<T>(
        &mut self,
        data: &[T],
        bounds: Bounds<Pixels>,
        render_fn: impl FnOnce() -> Texture,
    ) -> &Texture {
        let hash = self.calculate_hash(data, bounds);
        
        self.texture_cache.entry(hash).or_insert_with(render_fn)
    }
}
```

### 2. 数据处理优化

```rust
pub struct DataProcessor<T> {
    sampler: Option<Box<dyn DataSampler<T>>>,
    aggregator: Option<Box<dyn DataAggregator<T>>>,
}

pub trait DataSampler<T> {
    fn sample(&self, data: &[T], target_count: usize) -> Vec<T>;
}

pub struct LTTBSampler;

impl<T> DataSampler<T> for LTTBSampler 
where T: Clone {
    fn sample(&self, data: &[T], target_count: usize) -> Vec<T> {
        // Largest-Triangle-Three-Buckets 采样算法
        // 保持数据的视觉特征
        todo!()
    }
}
```

## 集成建议

### 1. 推荐方案

**短期方案**: WebView 嵌入集成
- 快速实现，利用现有 ECharts 生态
- 适合原型开发和快速迭代
- 主题同步相对简单

**长期方案**: 原生 Rust 重写
- 最佳性能和集成度
- 完全的类型安全
- 与 GPUI Component 深度集成

### 2. 实施步骤

1. **阶段一**: WebView 集成 (1-2个月)
   - 实现基础的 WebView 容器
   - 建立 JavaScript Bridge
   - 实现主题同步

2. **阶段二**: 功能完善 (2-3个月)
   - 完善事件处理
   - 优化性能
   - 增加更多图表类型支持

3. **阶段三**: 原生迁移 (3-6个月)
   - 逐步用原生组件替换 WebView
   - 保持 API 兼容性
   - 性能优化和测试

### 3. 技术风险评估

**WebView 方案风险**:
- 性能开销
- 内存使用较高
- 跨平台兼容性

**原生方案风险**:
- 开发周期长
- 功能完整性
- 维护成本高

这个集成方案为 ECharts 与 GPUI Component 的结合提供了多种可行路径，可以根据项目需求和时间安排选择合适的实施策略。
