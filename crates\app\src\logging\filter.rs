// // src/logging/filter.rs
// use tracing_subscriber::layer::{Layer, SubscriberExt};
// use tracing_core::Subscriber;

// struct ThresholdFilter {
//     default_level: Level,
//     ui_threshold: Level,
// }

// impl<S: Subscriber> Layer<S> for ThresholdFilter {
//     fn enabled(
//         &self,
//         meta: &tracing::Metadata<'_>,
//         ctx: &tracing_subscriber::layer::Context<'_, S>
//     ) -> bool {
//         // UI相关日志使用更高阈值
//         if meta.target().starts_with("ui::") {
//             meta.level() <= &self.ui_threshold
//         } else {
//             meta.level() <= &self.default_level
//         }
//     }
// }

// // 初始化时添加
// tracing_subscriber::registry()
//     .with(ThresholdFilter {
//         default_level: Level::DEBUG,
//         ui_threshold: Level::INFO,
//     })
//     .with(fmt::layer())
//     .init();