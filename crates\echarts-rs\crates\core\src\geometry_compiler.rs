//! 几何体编译器
//!
//! 负责将图表数据和配置编译为高性能的绘制指令。
//! 这里进行所有复杂的计算，渲染器只需要执行简单的绘制操作。

use crate::{
    coord::CartesianCoord, data::DataPoint, Bounds, Color, DrawBatch, LineStyle,
    PathCommand, PathStyle, Point, TextStyle,
};
use crate::draw_commands::DrawCommand;
use std::collections::HashMap;

/// 几何体编译器特征
///
/// 不同类型的图表实现这个特征来生成绘制指令
pub trait GeometryCompiler {
    /// 编译图表数据为绘制指令
    fn compile(&self, context: &CompileContext) -> CompileResult;

    /// 获取编译器名称
    fn name(&self) -> &str;

    /// 获取支持的数据类型
    fn supported_data_types(&self) -> Vec<&str>;
}

/// 编译上下文
///
/// 包含编译所需的所有信息
#[derive(Debug, Clone)]
pub struct CompileContext {
    /// 数据点
    pub data: Vec<DataPoint>,

    /// 坐标系统
    pub coordinate: CartesianCoord,

    /// 绘制区域
    pub bounds: Bounds,

    /// 样式配置
    pub style: StyleConfig,

    /// LOD级别
    pub lod_level: LodLevel,

    /// 视口信息
    pub viewport: ViewportInfo,

    /// 缓存键（用于缓存优化）
    pub cache_key: String,
}

/// 编译结果
#[derive(Debug, Clone)]
pub struct CompileResult {
    /// 绘制批次
    pub batches: Vec<DrawBatch>,

    /// 边界框
    pub bounds: Bounds,

    /// 是否可缓存
    pub cacheable: bool,

    /// 统计信息
    pub stats: CompileStats,
}

/// 编译统计信息
#[derive(Debug, Clone)]
pub struct CompileStats {
    pub total_commands: usize,
    pub total_points: usize,
    pub compile_time_ms: f64,
    pub memory_usage_bytes: usize,
}

/// 样式配置
#[derive(Debug, Clone)]
pub struct StyleConfig {
    pub line_styles: HashMap<String, LineStyle>,
    pub text_styles: HashMap<String, TextStyle>,
    pub colors: Vec<Color>,
    pub default_line_width: f64,
    pub default_font_size: f64,
    pub default_font_family: String,
}

/// LOD级别
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum LodLevel {
    /// 最高质量，显示所有细节
    High,
    /// 中等质量，适度简化
    Medium,
    /// 低质量，大幅简化
    Low,
    /// 最低质量，仅显示轮廓
    Minimal,
}

/// 视口信息
#[derive(Debug, Clone)]
pub struct ViewportInfo {
    pub visible_bounds: Bounds,
    pub zoom_level: f64,
    pub pixel_ratio: f64,
}

/// 线图编译器
pub struct LineCompiler {
    pub smooth: bool,
    pub show_points: bool,
    pub line_width: f64,
    pub point_size: f64,
}

impl LineCompiler {
    pub fn new() -> Self {
        Self {
            smooth: false,
            show_points: false,
            line_width: 2.0,
            point_size: 4.0,
        }
    }

    pub fn smooth(mut self, smooth: bool) -> Self {
        self.smooth = smooth;
        self
    }

    pub fn show_points(mut self, show_points: bool) -> Self {
        self.show_points = show_points;
        self
    }

    pub fn line_width(mut self, width: f64) -> Self {
        self.line_width = width;
        self
    }
}

impl GeometryCompiler for LineCompiler {
    fn compile(&self, context: &CompileContext) -> CompileResult {
        let start_time = std::time::Instant::now();
        let mut batches = Vec::new();
        let mut total_commands = 0;

        // 根据LOD级别决定采样策略
        let sampled_data = self.sample_data(&context.data, context.lod_level, &context.viewport);

        // 生成线条路径
        if sampled_data.len() > 1 {
            let mut batch = DrawBatch::new();

            if self.smooth {
                // 生成平滑曲线
                let path_commands = self.generate_smooth_path(&sampled_data, &context.coordinate);
                let style = PathStyle {
                    fill: None,
                    stroke: Some(crate::LineStyle {
                        color: context
                            .style
                            .colors
                            .get(0)
                            .cloned()
                            .unwrap_or(Color::rgb(0.0, 0.0, 255.0)),
                        width: self.line_width,
                        ..Default::default()
                    }),
                    opacity: 1.0,
                    fill_rule: crate::FillRule::NonZero,
                };

                batch.add_command(DrawCommand::Path {
                    commands: path_commands,
                    style,
                });
                total_commands += 1;
            } else {
                // 生成直线段
                for window in sampled_data.windows(2) {
                    let from = context
                        .coordinate
                        .data_to_screen(window[0].x(), window[0].y());
                    let to = context
                        .coordinate
                        .data_to_screen(window[1].x(), window[1].y());

                    batch.add_command(DrawCommand::Line {
                        from,
                        to,
                        style: crate::LineStyle {
                            color: context
                                .style
                                .colors
                                .get(0)
                                .cloned()
                                .unwrap_or(Color::rgb(0.0, 0.0, 255.0)),
                            width: self.line_width,
                            ..Default::default()
                        },
                    });
                    total_commands += 1;
                }
            }

            batches.push(batch);
        }

        // 生成数据点
        if self.show_points {
            let mut point_batch = DrawBatch::new();
            point_batch.set_z_index(1); // 点在线条之上

            for point in &sampled_data {
                let screen_pos = context.coordinate.data_to_screen(point.x(), point.y());
                point_batch.add_command(DrawCommand::Circle {
                    center: screen_pos,
                    radius: self.point_size,
                    style: crate::CircleStyle {
                        fill: Some(
                            context
                                .style
                                .colors
                                .get(0)
                                .cloned()
                                .unwrap_or(Color::rgb(0.0, 0.0, 255.0)),
                        ),
                        stroke: None,
                        opacity: 1.0,
                    },
                });
                total_commands += 1;
            }

            batches.push(point_batch);
        }

        let compile_time = start_time.elapsed().as_secs_f64() * 1000.0;

        CompileResult {
            batches,
            bounds: context.bounds,
            cacheable: true,
            stats: CompileStats {
                total_commands,
                total_points: sampled_data.len(),
                compile_time_ms: compile_time,
                memory_usage_bytes: total_commands * 64, // 估算
            },
        }
    }

    fn name(&self) -> &str {
        "LineCompiler"
    }

    fn supported_data_types(&self) -> Vec<&str> {
        vec!["line", "spline"]
    }
}

impl LineCompiler {
    /// 根据LOD级别和视口采样数据
    fn sample_data(
        &self,
        data: &[DataPoint],
        lod_level: LodLevel,
        _viewport: &ViewportInfo,
    ) -> Vec<DataPoint> {
        let sample_rate = match lod_level {
            LodLevel::High => 1.0,
            LodLevel::Medium => 0.5,
            LodLevel::Low => 0.25,
            LodLevel::Minimal => 0.1,
        };

        let target_points = (data.len() as f64 * sample_rate) as usize;
        if target_points >= data.len() {
            return data.to_vec();
        }

        // 使用Douglas-Peucker算法进行智能采样
        self.douglas_peucker_sample(data, target_points)
    }

    /// Douglas-Peucker采样算法
    fn douglas_peucker_sample(&self, data: &[DataPoint], target_points: usize) -> Vec<DataPoint> {
        if data.len() <= target_points {
            return data.to_vec();
        }

        // 简化版实现，实际可以使用更复杂的算法
        let step = data.len() / target_points;
        data.iter().step_by(step).cloned().collect()
    }

    /// 生成平滑路径
    fn generate_smooth_path(&self, data: &[DataPoint], coord: &CartesianCoord) -> Vec<PathCommand> {
        let mut commands = Vec::new();

        if data.is_empty() {
            return commands;
        }

        // 移动到第一个点
        let first_point = coord.data_to_screen(data[0].x(), data[0].y());
        commands.push(PathCommand::MoveTo(first_point));

        // 生成贝塞尔曲线
        for i in 1..data.len() {
            let current = coord.data_to_screen(data[i].x(), data[i].y());

            if i == 1 {
                // 第二个点直接连线
                commands.push(PathCommand::LineTo(current));
            } else {
                // 使用二次贝塞尔曲线平滑连接
                let prev = coord.data_to_screen(data[i - 1].x(), data[i - 1].y());
                let control = Point::new((prev.x + current.x) / 2.0, (prev.y + current.y) / 2.0);
                commands.push(PathCommand::QuadTo {
                    control,
                    to: current,
                });
            }
        }

        commands
    }
}

impl Default for StyleConfig {
    fn default() -> Self {
        Self {
            line_styles: HashMap::new(),
            text_styles: HashMap::new(),
            colors: vec![
                Color::rgb(84.0, 112.0, 198.0),
                Color::rgb(145.0, 204.0, 117.0),
                Color::rgb(250.0, 200.0, 88.0),
                Color::rgb(238.0, 102.0, 102.0),
                Color::rgb(115.0, 192.0, 222.0),
            ],
            default_line_width: 2.0,
            default_font_size: 12.0,
            default_font_family: "Arial".to_string(),
        }
    }
}

impl Default for ViewportInfo {
    fn default() -> Self {
        Self {
            visible_bounds: Bounds::zero(),
            zoom_level: 1.0,
            pixel_ratio: 1.0,
        }
    }
}
