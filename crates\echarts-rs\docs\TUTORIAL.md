# ECharts-rs 教程

欢迎来到ECharts-rs教程！本教程将带你从零开始学习如何使用ECharts-rs创建美观、高性能的数据可视化图表。

## 目录

1. [环境准备](#环境准备)
2. [第一个图表](#第一个图表)
3. [数据处理](#数据处理)
4. [图表类型](#图表类型)
5. [主题和样式](#主题和样式)
6. [交互功能](#交互功能)
7. [性能优化](#性能优化)
8. [实际项目应用](#实际项目应用)

## 环境准备

### 1. 安装Rust

确保你已经安装了Rust 1.70或更高版本：

```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
rustc --version
```

### 2. 创建新项目

```bash
cargo new my_charts_app
cd my_charts_app
```

### 3. 添加依赖

在 `Cargo.toml` 中添加ECharts-rs依赖：

```toml
[dependencies]
echarts-rs = "0.1.0"
rust-echarts-core = "0.1.0"
rust-echarts-themes = "0.1.0"
rust-echarts-renderer = "0.1.0"
rust-echarts-charts = "0.1.0"
rust-echarts-components = "0.1.0"
```

## 第一个图表

让我们创建第一个简单的图表：

```rust
// src/main.rs
use echarts_core::{Chart, DataPoint, DataValue, Color};
use echarts_themes::Theme;

fn main() {
    println!("🎨 我的第一个ECharts-rs图表");
    
    // 1. 创建图表
    let mut chart = Chart::new();
    chart.title = Some("月度销售数据".to_string());
    
    // 2. 准备数据
    let sales_data = vec![
        DataPoint::new(vec![DataValue::String("1月".to_string()), DataValue::Number(2300.0)]),
        DataPoint::new(vec![DataValue::String("2月".to_string()), DataValue::Number(1800.0)]),
        DataPoint::new(vec![DataValue::String("3月".to_string()), DataValue::Number(2800.0)]),
        DataPoint::new(vec![DataValue::String("4月".to_string()), DataValue::Number(3200.0)]),
        DataPoint::new(vec![DataValue::String("5月".to_string()), DataValue::Number(2900.0)]),
        DataPoint::new(vec![DataValue::String("6月".to_string()), DataValue::Number(3500.0)]),
    ];
    
    // 3. 应用主题
    let theme = Theme::light();
    chart.background_color = Some(theme.background_color);
    
    // 4. 显示结果
    println!("✅ 图表创建成功！");
    println!("   标题: {}", chart.title.as_ref().unwrap());
    println!("   数据点数量: {}", sales_data.len());
    println!("   主题: {}", theme.name);
}
```

运行程序：
```bash
cargo run
```

## 数据处理

### 从CSV文件读取数据

```rust
use std::fs::File;
use std::io::{BufRead, BufReader};

fn load_data_from_csv(filename: &str) -> Vec<DataPoint> {
    let file = File::open(filename).expect("无法打开文件");
    let reader = BufReader::new(file);
    let mut data = Vec::new();
    
    for line in reader.lines().skip(1) { // 跳过标题行
        let line = line.expect("读取行失败");
        let parts: Vec<&str> = line.split(',').collect();
        
        if parts.len() >= 2 {
            let category = parts[0].to_string();
            let value: f64 = parts[1].parse().unwrap_or(0.0);
            
            data.push(DataPoint::new(vec![
                DataValue::String(category),
                DataValue::Number(value)
            ]));
        }
    }
    
    data
}
```

### 数据转换和清理

```rust
fn clean_and_transform_data(raw_data: Vec<DataPoint>) -> Vec<DataPoint> {
    raw_data.into_iter()
        .filter(|point| {
            // 过滤无效数据
            point.y() > 0.0 && point.y().is_finite()
        })
        .map(|mut point| {
            // 数据转换
            if point.y() > 10000.0 {
                // 将大数值转换为千为单位
                let new_value = point.y() / 1000.0;
                DataPoint::new(vec![
                    point.values[0].clone(),
                    DataValue::Number(new_value)
                ])
            } else {
                point
            }
        })
        .collect()
}
```

## 图表类型

### 折线图

```rust
fn create_line_chart() -> Chart {
    let mut chart = Chart::new();
    chart.title = Some("温度变化趋势".to_string());
    
    // 生成时间序列数据
    let time_data: Vec<DataPoint> = (0..24).map(|hour| {
        let temp = 20.0 + (hour as f64 * std::f64::consts::PI / 12.0).sin() * 8.0;
        DataPoint::new(vec![
            DataValue::Number(hour as f64),
            DataValue::Number(temp)
        ])
    }).collect();
    
    println!("📈 折线图数据点: {}", time_data.len());
    chart
}
```

### 柱状图

```rust
fn create_bar_chart() -> Chart {
    let mut chart = Chart::new();
    chart.title = Some("产品销量对比".to_string());
    
    let products = vec![
        ("iPhone", 1200),
        ("Samsung", 980),
        ("Huawei", 750),
        ("Xiaomi", 650),
        ("OPPO", 580),
    ];
    
    let bar_data: Vec<DataPoint> = products.into_iter().map(|(name, sales)| {
        DataPoint::new(vec![
            DataValue::String(name.to_string()),
            DataValue::Number(sales as f64)
        ])
    }).collect();
    
    println!("📊 柱状图数据点: {}", bar_data.len());
    chart
}
```

### 饼图

```rust
fn create_pie_chart() -> Chart {
    let mut chart = Chart::new();
    chart.title = Some("市场份额分布".to_string());
    
    let market_share = vec![
        ("移动端", 45.2),
        ("桌面端", 32.8),
        ("平板端", 15.6),
        ("其他", 6.4),
    ];
    
    let pie_data: Vec<DataPoint> = market_share.into_iter().map(|(platform, share)| {
        DataPoint::new(vec![
            DataValue::String(platform.to_string()),
            DataValue::Number(share)
        ])
    }).collect();
    
    // 验证数据总和
    let total: f64 = pie_data.iter().map(|p| p.y()).sum();
    println!("🥧 饼图总份额: {:.1}%", total);
    
    chart
}
```

### 散点图

```rust
fn create_scatter_chart() -> Chart {
    let mut chart = Chart::new();
    chart.title = Some("身高体重关系".to_string());
    
    // 生成模拟数据
    let scatter_data: Vec<DataPoint> = (0..100).map(|i| {
        let height = 160.0 + (i as f64 * 0.3) + pseudo_random(i) * 25.0;
        let weight = (height - 100.0) * 0.9 + pseudo_random(i + 100) * 15.0;
        
        DataPoint::new(vec![
            DataValue::Number(height),
            DataValue::Number(weight)
        ])
    }).collect();
    
    println!("🔵 散点图数据点: {}", scatter_data.len());
    chart
}

fn pseudo_random(seed: usize) -> f64 {
    let a = 1664525u64;
    let c = 1013904223u64;
    let m = 2u64.pow(32);
    let x = ((a * seed as u64 + c) % m) as f64 / m as f64;
    x * 2.0 - 1.0
}
```

## 主题和样式

### 使用内置主题

```rust
use echarts_themes::Theme;

fn apply_themes_demo() {
    let themes = vec![
        ("浅色主题", Theme::light()),
        ("深色主题", Theme::dark()),
        ("复古主题", Theme::vintage()),
        ("马卡龙主题", Theme::macarons()),
    ];
    
    for (name, theme) in themes {
        let mut chart = Chart::new();
        chart.title = Some(format!("{} 示例", name));
        chart.background_color = Some(theme.background_color);
        
        println!("🎨 应用主题: {} (颜色数量: {})", 
            theme.name, theme.color_palette.len());
    }
}
```

### 创建自定义主题

```rust
fn create_custom_theme() -> Theme {
    let mut theme = Theme::light();
    theme.name = "企业主题".to_string();
    theme.background_color = Color::rgb(0.98, 0.98, 0.98);
    
    // 自定义颜色调色板
    theme.color_palette = vec![
        Color::rgb(0.2, 0.4, 0.8),   // 企业蓝
        Color::rgb(0.8, 0.2, 0.2),   // 警告红
        Color::rgb(0.2, 0.7, 0.3),   // 成功绿
        Color::rgb(0.9, 0.6, 0.1),   // 提醒橙
        Color::rgb(0.6, 0.2, 0.8),   // 紫色
    ];
    
    // 自定义文本样式
    theme.text_style.font_size = 14.0;
    theme.text_style.color = Color::rgb(0.2, 0.2, 0.2);
    
    println!("✨ 创建自定义主题: {}", theme.name);
    theme
}
```

## 交互功能

### 启用缩放和平移

```rust
fn create_interactive_chart() -> Chart {
    let mut chart = Chart::new();
    chart.title = Some("可交互图表".to_string());
    
    // 启用交互功能（概念性代码）
    // chart.enable_zoom = true;
    // chart.enable_pan = true;
    // chart.enable_brush = true;
    
    println!("🎯 交互功能已启用");
    chart
}
```

## 性能优化

### 大数据集处理

```rust
fn handle_large_dataset() {
    use std::time::Instant;
    
    // 生成大数据集
    let start = Instant::now();
    let large_data: Vec<DataPoint> = (0..100000).map(|i| {
        let x = i as f64;
        let y = (x * 0.001).sin() * 1000.0 + pseudo_random(i) * 100.0;
        DataPoint::new(vec![DataValue::Number(x), DataValue::Number(y)])
    }).collect();
    let generation_time = start.elapsed();
    
    // 数据优化
    let start = Instant::now();
    let optimized_data = if large_data.len() > 10000 {
        // 采样优化
        let step = large_data.len() / 10000;
        large_data.into_iter().step_by(step).collect()
    } else {
        large_data
    };
    let optimization_time = start.elapsed();
    
    println!("🚀 性能优化结果:");
    println!("   原始数据: 100,000 点");
    println!("   优化后: {} 点", optimized_data.len());
    println!("   生成时间: {:?}", generation_time);
    println!("   优化时间: {:?}", optimization_time);
}
```

## 实际项目应用

### 销售仪表板

```rust
fn create_sales_dashboard() {
    println!("📊 创建销售仪表板");
    
    // 1. 收入趋势图
    let revenue_chart = create_line_chart();
    
    // 2. 产品销量对比
    let sales_chart = create_bar_chart();
    
    // 3. 市场份额分布
    let market_chart = create_pie_chart();
    
    // 4. 应用统一主题
    let theme = create_custom_theme();
    
    println!("✅ 仪表板创建完成");
    println!("   包含 3 个图表");
    println!("   使用主题: {}", theme.name);
}
```

### 完整示例程序

```rust
fn main() {
    println!("🎨 ECharts-rs 完整教程示例");
    println!("============================");
    
    // 基础图表
    let _line_chart = create_line_chart();
    let _bar_chart = create_bar_chart();
    let _pie_chart = create_pie_chart();
    let _scatter_chart = create_scatter_chart();
    
    // 主题应用
    apply_themes_demo();
    let _custom_theme = create_custom_theme();
    
    // 交互功能
    let _interactive_chart = create_interactive_chart();
    
    // 性能优化
    handle_large_dataset();
    
    // 实际应用
    create_sales_dashboard();
    
    println!("\n🎉 教程完成！");
    println!("现在你已经掌握了ECharts-rs的基本用法。");
    println!("查看examples/目录获取更多高级示例。");
}
```

## 下一步

恭喜你完成了ECharts-rs教程！现在你可以：

1. 查看 [API文档](API.md) 了解详细的API接口
2. 运行 `examples/` 目录中的示例程序
3. 阅读源代码了解内部实现
4. 参与开源贡献

## 常见问题

**Q: 如何处理大数据集？**
A: 使用数据采样和GPU加速功能，参考性能优化章节。

**Q: 如何自定义图表样式？**
A: 创建自定义主题或直接修改图表属性。

**Q: 支持哪些图表类型？**
A: 目前支持折线图、柱状图、饼图、散点图等，更多类型正在开发中。

**Q: 如何导出图表？**
A: 使用SVG渲染器可以导出为SVG格式，支持进一步转换为PNG等格式。

## 资源链接

- [GitHub仓库](https://github.com/your-repo/echarts-rs)
- [API文档](API.md)
- [示例代码](../examples/)
- [问题反馈](https://github.com/your-repo/echarts-rs/issues)
