//! 响应式边界更新演示
//!
//! 这个示例展示了如何使用新的响应式架构：
//! 1. 边界变化时自动通知外部
//! 2. 外部更新坐标系统和数据
//! 3. 动态更新绘制命令
//! 4. 实现真正的响应式图表

use echarts_rs::{LineSeries, Color, CartesianCoordinateSystem, Bounds as EchartsBounds, Series};
use echarts_core::DrawCommand;
use std::sync::{Arc, RwLock};
use tokio::time::{sleep, Duration};

/// 简化的响应式图表状态（不依赖 GPUI BackgroundExecutor）
#[derive(Clone)]
pub struct SimpleResponsiveChartState {
    /// 共享的绘制命令
    pub commands: Arc<RwLock<Option<Vec<DrawCommand>>>>,
    /// 共享的坐标系统
    pub coord_system: Arc<RwLock<CartesianCoordinateSystem>>,
    /// 更新计数器
    pub update_count: Arc<RwLock<usize>>,
}

impl SimpleResponsiveChartState {
    /// 创建新的简化响应式图表状态
    pub fn new(coord_system: CartesianCoordinateSystem) -> Self {
        Self {
            commands: Arc::new(RwLock::new(None)),
            coord_system: Arc::new(RwLock::new(coord_system)),
            update_count: Arc::new(RwLock::new(0)),
        }
    }

    /// 获取当前坐标系统
    pub fn get_coord_system(&self) -> Option<CartesianCoordinateSystem> {
        Some(self.coord_system.read().ok()?.clone())
    }

    /// 获取当前命令
    pub fn get_commands(&self) -> Option<Vec<DrawCommand>> {
        self.commands.read().ok()?.clone()
    }

    /// 获取更新计数
    pub fn get_update_count(&self) -> usize {
        self.update_count.read().map(|guard| *guard).unwrap_or(0)
    }

    /// 更新坐标系统
    pub fn update_coord_system(&self, coord_system: CartesianCoordinateSystem) -> Result<(), String> {
        match self.coord_system.write() {
            Ok(mut guard) => {
                *guard = coord_system;
                Ok(())
            }
            Err(_) => Err("Failed to acquire write lock for coord_system".to_string()),
        }
    }
}

/// 简化的命令更新器（使用 tokio 而不是 GPUI BackgroundExecutor）
#[derive(Clone)]
pub struct SimpleCommandUpdater {
    shared_commands: Arc<RwLock<Option<Vec<DrawCommand>>>>,
}

impl SimpleCommandUpdater {
    /// 创建新的简化命令更新器
    pub fn new(shared_commands: Arc<RwLock<Option<Vec<DrawCommand>>>>) -> Self {
        Self { shared_commands }
    }

    /// 同步更新命令
    pub fn update_sync(&self, commands: Vec<DrawCommand>) -> Result<(), String> {
        match self.shared_commands.write() {
            Ok(mut guard) => {
                *guard = Some(commands);
                Ok(())
            }
            Err(_) => Err("Failed to acquire write lock for commands".to_string()),
        }
    }
}

#[tokio::main]
async fn main() {
    println!("🚀 启动 GPUI 后台执行器响应式边界更新演示...");
    println!("💡 这个演示展示了如何使用 GPUI BackgroundExecutor 安全的共享状态来处理边界变化");

    // 简化的演示 - 直接创建和测试 GPUI 后台执行器组件
    test_multithreaded_responsive_state().await;
}

/// 测试 GPUI 后台执行器响应式状态
async fn test_multithreaded_responsive_state() {
    println!("\n🧵 测试 GPUI 后台执行器响应式状态...");

    // 创建 GPUI 后台执行器（模拟）
    // 注意：在实际应用中，这应该从 GPUI 应用上下文中获取
    // 这里我们使用一个简化的方法来创建后台执行器
    println!("⚠️  注意：这是一个简化的演示，实际应用中应该从 GPUI AppContext 获取 BackgroundExecutor");

    // 由于我们无法直接创建 BackgroundExecutor，我们将使用 tokio 的运行时来模拟
    println!("🔄 使用 tokio 运行时模拟 GPUI 后台执行器行为...");

    // 创建初始坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        EchartsBounds::new(0.0, 0.0, 800.0, 600.0),
        (0.0, 20.0),
        (0.0, 120.0),
    );

    // 创建简化的响应式图表状态（不使用 GPUI BackgroundExecutor）
    let chart_state = SimpleResponsiveChartState::new(coord_system);

    // 创建简化的命令更新器
    let command_updater = SimpleCommandUpdater::new(chart_state.commands.clone());

    // 创建测试数据
    let data: Vec<(f64, f64)> = (0..10)
        .map(|i| {
            let x = i as f64;
            let y = 50.0 + 30.0 * (x * 0.5).sin();
            (x, y)
        })
        .collect();

    let series = LineSeries::new("测试线图")
        .data(data)
        .color(Color::rgb(0.2, 0.6, 0.9))
        .line_width(2.0)
        .smooth(true);

    println!("📊 创建了测试系列，包含 {} 个数据点", series.data.len());

    // 测试 GPUI 后台执行器更新
    test_concurrent_updates(chart_state, command_updater, Box::new(series)).await;
}

/// 测试并发更新（使用 tokio 任务）
async fn test_concurrent_updates(
    chart_state: SimpleResponsiveChartState,
    command_updater: SimpleCommandUpdater,
    series: Box<dyn Series>,
) {
    println!("\n🔄 开始并发更新测试...");

    // 模拟多个边界变化
    let test_bounds = vec![
        (800.0, 600.0),
        (1024.0, 768.0),
        (1200.0, 900.0),
        (640.0, 480.0),
        (1920.0, 1080.0),
    ];

    // 使用 tokio 任务并发处理
    let mut tasks = vec![];

    for (i, (width, height)) in test_bounds.into_iter().enumerate() {
        let chart_state_clone = chart_state.clone();
        let command_updater_clone = command_updater.clone();
        let series_clone = series.clone_series();

        let task = tokio::spawn(async move {
            println!("🧵 线程 {} 开始处理边界 {}x{}", i, width, height);

            // 创建新的坐标系统
            let echarts_bounds = EchartsBounds::new(0.0, 0.0, width, height);
            let x_range = if width > 800.0 { (0.0, 20.0) } else { (0.0, 15.0) };
            let y_range = if height > 600.0 { (0.0, 120.0) } else { (0.0, 100.0) };

            let new_coord_system = CartesianCoordinateSystem::new(
                echarts_bounds,
                x_range,
                y_range,
            );

            // 更新坐标系统
            if let Err(e) = chart_state_clone.update_coord_system(new_coord_system.clone()) {
                println!("❌ 线程 {} 更新坐标系统失败: {:?}", i, e);
                return;
            }

            // 生成绘制命令
            match series_clone.render_to_commands(&new_coord_system) {
                Ok(commands) => {
                    // 同步更新命令
                    if let Err(e) = command_updater_clone.update_sync(commands.clone()) {
                        println!("❌ 线程 {} 更新命令失败: {:?}", i, e);
                    } else {
                        println!("✅ 线程 {} 成功更新 {} 个绘制命令", i, commands.len());
                    }
                }
                Err(e) => {
                    println!("❌ 线程 {} 生成绘制命令失败: {:?}", i, e);
                }
            }

            // 模拟一些处理时间
            sleep(Duration::from_millis(100)).await;

            println!("🏁 tokio 任务 {} 完成处理", i);
        });

        tasks.push(task);
    }

    // 等待所有任务完成
    for (i, task) in tasks.into_iter().enumerate() {
        if let Err(e) = task.await {
            println!("❌ tokio 任务 {} 执行失败: {:?}", i, e);
        }
    }

    // 输出最终状态
    println!("\n📊 最终状态:");
    println!("   更新计数: {}", chart_state.get_update_count());

    if let Some(commands) = chart_state.get_commands() {
        println!("   绘制命令: {} 个", commands.len());
    } else {
        println!("   绘制命令: 无");
    }

    if let Some(coord_system) = chart_state.get_coord_system() {
        println!("   坐标系统边界: {:?}", coord_system.bounds);
    } else {
        println!("   坐标系统: 无");
    }

    println!("\n🎉 tokio 异步响应式边界更新演示完成！");
    println!("💡 这展示了如何使用 Arc<RwLock<T>> 和 tokio 任务实现线程安全的状态共享");
    println!("🔧 在实际 GPUI 应用中，可以将 tokio::spawn 替换为 BackgroundExecutor::spawn");
}

// 删除了 UI 组件，这个演示专注于多线程功能测试
