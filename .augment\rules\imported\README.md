---
type: "always_apply"
description: "Example description"
---
# AI 规则执行系统

这是一个确保 AI 助手严格遵循项目规范的系统。它会检查每次 AI 响应并确保符合规定的格式和规范。

## 文件结构

- `ai.mdc`: 核心规则文件，定义了 Rust 开发规范和 AI 响应规范
- `enforce_ai_rules.rs`: Rust 实现的规则检查与执行器
- `init_ai_rules.js`: JavaScript 初始化脚本，用于加载规则和处理响应
- `README.md`: 本文档

## 功能特性

1. **规则定义**：在 `ai.mdc` 中集中定义所有规范
2. **自动检查**：检查每次 AI 响应是否符合规范
3. **自动修正**：对不符合规范的响应进行结构调整
4. **强制执行**：确保所有代码符合 Rust 开发规范

## 如何使用

### 配置系统

1. 确保已安装 Rust 和 Node.js 环境
2. 编译规则执行器（首次使用时自动进行）

```bash
cd .cursor/rules
rustc --crate-type=cdylib -o enforce_ai_rules.so enforce_ai_rules.rs
```

### 初始化规则

在项目启动时，将以下代码添加到入口文件：

```javascript
const aiRules = require('./.cursor/rules/init_ai_rules');
const rules = aiRules.initRules();

// 添加到 AI 系统的上下文
const aiContext = aiRules.prepareAiContext();
```

### 使用规则检查响应

```javascript
const aiRules = require('./.cursor/rules/init_ai_rules');

// 检查响应是否符合规范
const checkResult = aiRules.checkResponse(aiResponse);
if (!checkResult.passed) {
  console.log('AI响应不符合规范，问题：', checkResult.issues);
}

// 格式化响应以符合规范
const formattedResponse = aiRules.formatResponse(aiResponse, 'standard');
```

## 规则执行流程

1. **加载规则**：从 `ai.mdc` 读取并解析规则
2. **检查响应**：分析 AI 响应是否符合规范
3. **格式化响应**：对不符合规范的响应进行结构调整
4. **返回合规响应**：返回最终符合规范的响应

## 响应规范

每次 AI 响应必须包含以下结构：

```

【分析】
问题/任务的理解和关键点
【检索】
检索相关文件，
【方案】
解决思路（复杂任务提供多个方案）

【执行】
代码实现或具体步骤

【检查】
可能的问题和改进点
```

## 使用示例

### 简单任务

对于简单任务，可使用简化结构：

```javascript
const response = aiRules.formatResponse(aiOutput, 'simple');
```

### 标准任务

对于标准任务，使用完整的四部分结构：

```javascript
const response = aiRules.formatResponse(aiOutput, 'standard');
```

### 复杂任务

对于复杂任务，使用带有多方案的结构：

```javascript
const response = aiRules.formatResponse(aiOutput, 'complex');
```

## 自定义规则

可以通过修改 `ai.mdc` 文件来自定义规则，添加或调整 Rust 开发规范和 AI 响应规范。

## 故障排除

- **规则执行器编译失败**：确保已正确安装 Rust 编译器
- **响应格式错误**：检查响应结构是否包含所有必需部分
- **规则文件不存在**：确保 `ai.mdc` 文件位于正确位置 