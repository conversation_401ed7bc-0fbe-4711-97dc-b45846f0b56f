use std::fmt;
use thiserror::Error;
use tsdaq_protocol::ProtocolError;

/// 设备操作结果类型
pub type DeviceResult<T> = Result<T, DeviceError>;

/// 设备相关错误类型
#[derive(Debug, Error)]
pub enum DeviceError {
    /// 没有可用的串口设备
    #[error("没有可用的串口")]
    NoSerialPortsAvailable,

    /// 设备连接失败
    #[error("连接失败: {0}")]
    ConnectionFailed(String),

    /// 设备断开连接失败
    #[error("断开连接失败: {0}")]
    DisconnectionFailed(String),

    /// 数据采集操作失败
    #[error("数据采集失败: {0}")]
    DataCollectionFailed(String),

    /// 操作超时
    #[error("操作超时: {0}")]
    TimeoutError(String),

    /// 协议错误
    #[error("协议错误: {0}")]
    ProtocolError(#[from] ProtocolError),

    /// 其他未分类错误
    #[error("未知错误: {0}")]
    Other(String),
}

impl From<anyhow::Error> for DeviceError {
    fn from(err: anyhow::Error) -> Self {
        Self::ConnectionFailed(err.to_string())
    }
}
