//! 预计算绘制命令演示
//!
//! 这个示例展示了如何使用预计算的 DrawCommand 来优化图表渲染性能

use echarts_rs::{LineSeries, Color, CartesianCoordinateSystem};
use echarts_core::{Bounds as EchartsBounds, DrawCommand};
use gpui_renderer::{EChartsCanvas, EChartsElement};
use gpui::*;

fn main() {
    println!("🚀 启动预计算命令演示...");
    
    Application::new().run(move |cx| {
        println!("📱 应用程序上下文已创建");
        
        let window_size = size(px(1000.0), px(700.0));
        let window_bounds = Bounds::centered(None, window_size, cx);
        
        let options = WindowOptions {
            window_bounds: Some(WindowBounds::Windowed(window_bounds)),
            titlebar: Some(TitlebarOptions {
                title: Some("预计算命令演示".into()),
                appears_transparent: false,
                traffic_light_position: None,
            }),
            focus: true,
            show: true,
            kind: WindowKind::Normal,
            is_movable: true,
            display_id: None,
            app_id: None,
            window_background: WindowBackgroundAppearance::Opaque,
            window_decorations: None,
            window_min_size: Some(size(px(800.0), px(600.0))),
        };
        
        cx.open_window(options, |_window, cx| {
            println!("✅ 窗口已创建");
            cx.new_view(|_cx| PrecomputedDemo::new())
        }).expect("无法创建窗口");
    });
}

struct PrecomputedDemo {
    /// 实时渲染的图表
    realtime_chart: EChartsCanvas,
    /// 预计算命令的图表
    precomputed_chart: Option<EChartsElement>,
    /// 预计算的绘制命令
    cached_commands: Option<Vec<DrawCommand>>,
}

impl PrecomputedDemo {
    fn new() -> Self {
        // 创建测试数据
        let data = vec![
            (1.0, 120.0), (2.0, 132.0), (3.0, 101.0), (4.0, 134.0),
            (5.0, 90.0), (6.0, 230.0), (7.0, 210.0), (8.0, 182.0),
            (9.0, 191.0), (10.0, 234.0), (11.0, 290.0), (12.0, 330.0),
        ];
        
        // 创建折线图系列
        let line_series = LineSeries::new("月销售额")
            .data(data.clone())
            .color(Color::rgb(0.2, 0.6, 1.0))
            .line_width(3.0)
            .smooth(true);
        
        // 创建坐标系统
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(0.0, 0.0, 800.0, 600.0),
            (0.0, 13.0),  // x 轴范围
            (0.0, 400.0), // y 轴范围
        );
        
        // 创建实时渲染的画布
        let realtime_chart = EChartsCanvas::new(
            Box::new(line_series.clone()),
            coord_system.clone(),
        ).with_debug(true);
        
        Self {
            realtime_chart,
            precomputed_chart: None,
            cached_commands: None,
        }
    }
    
    /// 预计算绘制命令
    fn precompute_commands(&mut self) {
        println!("🔄 开始预计算绘制命令...");
        
        // 创建相同的数据和系列
        let data = vec![
            (1.0, 120.0), (2.0, 132.0), (3.0, 101.0), (4.0, 134.0),
            (5.0, 90.0), (6.0, 230.0), (7.0, 210.0), (8.0, 182.0),
            (9.0, 191.0), (10.0, 234.0), (11.0, 290.0), (12.0, 330.0),
        ];
        
        let line_series = LineSeries::new("预计算图表")
            .data(data)
            .color(Color::rgb(1.0, 0.4, 0.2))  // 不同颜色以区分
            .line_width(3.0)
            .smooth(true);
        
        let coord_system = CartesianCoordinateSystem::new(
            EchartsBounds::new(0.0, 0.0, 800.0, 600.0),
            (0.0, 13.0),
            (0.0, 400.0),
        );
        
        // 预计算绘制命令
        match line_series.render_to_commands(&coord_system) {
            Ok(commands) => {
                println!("✅ 预计算完成，生成了 {} 个绘制命令", commands.len());
                
                // 创建画布
                let canvas = EChartsCanvas::new(
                    Box::new(line_series),
                    coord_system,
                ).with_debug(true);
                
                // 使用预计算命令创建元素
                let precomputed_element = EChartsElement::with_commands(canvas, commands.clone());
                
                self.cached_commands = Some(commands);
                self.precomputed_chart = Some(precomputed_element);
            }
            Err(e) => {
                println!("❌ 预计算失败: {:?}", e);
            }
        }
    }
}

impl Render for PrecomputedDemo {
    fn render(&mut self, cx: &mut ViewContext<Self>) -> impl IntoElement {
        // 如果还没有预计算，则进行预计算
        if self.precomputed_chart.is_none() {
            self.precompute_commands();
        }
        
        div()
            .flex()
            .flex_col()
            .size_full()
            .bg(rgb(0xf8f9fa))
            .p_4()
            .child(
                // 标题
                div()
                    .text_xl()
                    .font_bold()
                    .text_color(rgb(0x1f2937))
                    .mb_6()
                    .child("预计算绘制命令演示")
            )
            .child(
                // 图表容器
                div()
                    .flex()
                    .gap_4()
                    .size_full()
                    .child(
                        // 左侧：实时渲染图表
                        div()
                            .flex_1()
                            .flex()
                            .flex_col()
                            .child(
                                div()
                                    .text_lg()
                                    .font_semibold()
                                    .text_color(rgb(0x374151))
                                    .mb_2()
                                    .child("实时渲染（每次 paint 都生成命令）")
                            )
                            .child(
                                div()
                                    .w_full()
                                    .h(px(400.0))
                                    .bg(rgb(0xffffff))
                                    .border_1()
                                    .border_color(rgb(0xe5e7eb))
                                    .rounded_lg()
                                    .child(
                                        self.realtime_chart.clone().into_element()
                                    )
                            )
                    )
                    .child(
                        // 右侧：预计算图表
                        div()
                            .flex_1()
                            .flex()
                            .flex_col()
                            .child(
                                div()
                                    .text_lg()
                                    .font_semibold()
                                    .text_color(rgb(0x374151))
                                    .mb_2()
                                    .child("预计算渲染（使用缓存的命令）")
                            )
                            .child(
                                div()
                                    .w_full()
                                    .h(px(400.0))
                                    .bg(rgb(0xffffff))
                                    .border_1()
                                    .border_color(rgb(0xe5e7eb))
                                    .rounded_lg()
                                    .child(
                                        if let Some(ref precomputed) = self.precomputed_chart {
                                            // 注意：这里需要克隆，因为 GPUI 需要拥有所有权
                                            // 在实际应用中，您可能需要使用 Rc<RefCell<>> 或其他共享机制
                                            div().child("预计算图表（需要实现 Clone）")
                                        } else {
                                            div()
                                                .flex()
                                                .items_center()
                                                .justify_center()
                                                .text_color(rgb(0x6b7280))
                                                .child("正在预计算...")
                                        }
                                    )
                            )
                    )
            )
            .child(
                // 底部说明
                div()
                    .mt_4()
                    .p_4()
                    .bg(rgb(0xf3f4f6))
                    .rounded_lg()
                    .child(
                        div()
                            .text_sm()
                            .text_color(rgb(0x6b7280))
                            .child("💡 性能对比：左侧图表每次重绘都会重新生成绘制命令，右侧图表使用预计算的命令，渲染更快。")
                    )
            )
    }
}
