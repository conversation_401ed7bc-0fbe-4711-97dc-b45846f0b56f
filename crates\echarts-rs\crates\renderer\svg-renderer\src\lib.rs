//! SVG 渲染器 - 重构版本
//!
//! 基于新的 DrawCommand 系统的 SVG 渲染器实现

use echarts_core::{
    Bounds, Color, DrawCommand, Point, Result,
    draw_commands::{
        LineStyle, CircleStyle, RectStyle, TextStyle, PathCommand, PathStyle,
        LineCap, LineJoin, TextAlign, TextBaseline, FontWeight, FontStyle,
    },
};
use serde::{Deserialize, Serialize};
use std::fmt::Write;

/// SVG 渲染器
#[derive(Debug)]
pub struct SvgRenderer {
    /// SVG 内容缓冲区
    content: String,
    /// 渲染设置
    settings: SvgRenderSettings,
}

/// SVG 渲染设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SvgRenderSettings {
    /// SVG 宽度
    pub width: f64,
    /// SVG 高度
    pub height: f64,
    /// 背景颜色
    pub background_color: Option<Color>,
    /// 是否包含 XML 声明
    pub include_xml_declaration: bool,
    /// 浮点数精度
    pub precision: usize,
    /// 是否美化输出
    pub pretty_print: bool,
}

impl Default for SvgRenderSettings {
    fn default() -> Self {
        Self {
            width: 800.0,
            height: 600.0,
            background_color: Some(Color::rgb(1.0, 1.0, 1.0)), // 白色背景
            include_xml_declaration: true,
            precision: 2,
            pretty_print: true,
        }
    }
}

impl SvgRenderer {
    /// 创建新的 SVG 渲染器
    pub fn new(settings: SvgRenderSettings) -> Self {
        Self {
            content: String::new(),
            settings,
        }
    }

    /// 使用默认设置创建 SVG 渲染器
    pub fn with_size(width: f64, height: f64) -> Self {
        let settings = SvgRenderSettings {
            width,
            height,
            ..Default::default()
        };
        Self::new(settings)
    }

    /// 渲染 DrawCommand 列表为 SVG 字符串
    pub fn render_commands(&mut self, commands: Vec<DrawCommand>) -> Result<String> {
        self.content.clear();
        self.write_svg_header();

        // 渲染所有绘制命令
        for command in commands {
            self.render_draw_command(&command)?;
        }

        self.write_svg_footer();
        Ok(self.content.clone())
    }

    /// 写入 SVG 头部
    fn write_svg_header(&mut self) {
        if self.settings.include_xml_declaration {
            writeln!(self.content, r#"<?xml version="1.0" encoding="UTF-8"?>"#).unwrap();
        }

        writeln!(
            self.content,
            r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">"#,
            self.format_number(self.settings.width),
            self.format_number(self.settings.height)
        ).unwrap();

        // 添加背景
        if let Some(bg_color) = &self.settings.background_color {
            writeln!(
                self.content,
                r#"  <rect width="100%" height="100%" fill="{}"/>"#,
                self.color_to_string(bg_color)
            ).unwrap();
        }
    }

    /// 写入 SVG 尾部
    fn write_svg_footer(&mut self) {
        writeln!(self.content, "</svg>").unwrap();
    }

    /// 渲染单个 DrawCommand
    fn render_draw_command(&mut self, command: &DrawCommand) -> Result<()> {
        let indent = if self.settings.pretty_print { "  " } else { "" };

        match command {
            DrawCommand::Line { from, to, style } => {
                writeln!(
                    self.content,
                    r#"{}<line x1="{}" y1="{}" x2="{}" y2="{}" stroke="{}" stroke-width="{}"{}{}{}"/>"#,
                    indent,
                    self.format_number(from.x),
                    self.format_number(from.y),
                    self.format_number(to.x),
                    self.format_number(to.y),
                    self.color_to_string(&style.color),
                    self.format_number(style.width),
                    self.line_cap_to_svg(&style.cap),
                    self.line_join_to_svg(&style.join),
                    self.dash_pattern_to_svg(&style.dash_pattern)
                ).unwrap();
            }

            DrawCommand::Circle { center, radius, style } => {
                write!(
                    self.content,
                    r#"{}<circle cx="{}" cy="{}" r="{}""#,
                    indent,
                    self.format_number(center.x),
                    self.format_number(center.y),
                    self.format_number(*radius)
                ).unwrap();

                if let Some(fill_color) = &style.fill {
                    write!(self.content, r#" fill="{}""#, self.color_to_string(fill_color)).unwrap();
                } else {
                    write!(self.content, r#" fill="none""#).unwrap();
                }

                if let Some(stroke) = &style.stroke {
                    write!(
                        self.content,
                        r#" stroke="{}" stroke-width="{}""#,
                        self.color_to_string(&stroke.color),
                        self.format_number(stroke.width)
                    ).unwrap();
                }

                writeln!(self.content, "/>").unwrap();
            }

            DrawCommand::Rect { bounds, style } => {
                write!(
                    self.content,
                    r#"{}<rect x="{}" y="{}" width="{}" height="{}""#,
                    indent,
                    self.format_number(bounds.origin.x),
                    self.format_number(bounds.origin.y),
                    self.format_number(bounds.size.width),
                    self.format_number(bounds.size.height)
                ).unwrap();

                if let Some(fill_color) = &style.fill {
                    write!(self.content, r#" fill="{}""#, self.color_to_string(fill_color)).unwrap();
                } else {
                    write!(self.content, r#" fill="none""#).unwrap();
                }

                if let Some(stroke) = &style.stroke {
                    write!(
                        self.content,
                        r#" stroke="{}" stroke-width="{}""#,
                        self.color_to_string(&stroke.color),
                        self.format_number(stroke.width)
                    ).unwrap();
                }

                if style.corner_radius > 0.0 {
                    write!(
                        self.content,
                        r#" rx="{}" ry="{}""#,
                        self.format_number(style.corner_radius),
                        self.format_number(style.corner_radius)
                    ).unwrap();
                }

                writeln!(self.content, "/>").unwrap();
            }

            DrawCommand::Text { text, position, style } => {
                write!(
                    self.content,
                    r#"{}<text x="{}" y="{}" fill="{}" font-size="{}""#,
                    indent,
                    self.format_number(position.x),
                    self.format_number(position.y),
                    self.color_to_string(&style.color),
                    self.format_number(style.font_size)
                ).unwrap();

                if !style.font_family.is_empty() {
                    write!(self.content, r#" font-family="{}""#, style.font_family).unwrap();
                }

                write!(self.content, r#" text-anchor="{}""#, self.text_align_to_svg(&style.align)).unwrap();
                write!(self.content, r#" dominant-baseline="{}""#, self.text_baseline_to_svg(&style.baseline)).unwrap();

                if let Some(weight) = self.font_weight_to_svg(&style.font_weight) {
                    write!(self.content, r#" font-weight="{}""#, weight).unwrap();
                }

                match style.font_style {
                    FontStyle::Normal => {},
                    FontStyle::Italic => {
                        write!(self.content, r#" font-style="italic""#).unwrap();
                    }
                    FontStyle::Oblique => {
                        write!(self.content, r#" font-style="oblique""#).unwrap();
                    }
                }

                write!(self.content, ">").unwrap();
                write!(self.content, "{}", self.escape_text(text)).unwrap();
                writeln!(self.content, "</text>").unwrap();
            }

            DrawCommand::Path { commands, style } => {
                write!(
                    self.content,
                    r#"{}<path d="{}""#,
                    indent,
                    self.path_commands_to_svg(commands)
                ).unwrap();

                if let Some(fill_color) = &style.fill {
                    write!(self.content, r#" fill="{}""#, self.color_to_string(fill_color)).unwrap();
                } else {
                    write!(self.content, r#" fill="none""#).unwrap();
                }

                if let Some(stroke) = &style.stroke {
                    write!(
                        self.content,
                        r#" stroke="{}" stroke-width="{}""#,
                        self.color_to_string(&stroke.color),
                        self.format_number(stroke.width)
                    ).unwrap();
                }

                writeln!(self.content, "/>").unwrap();
            }

            _ => {
                // 其他命令类型的占位符实现
                writeln!(self.content, "{}<!-- Unsupported command type -->", indent).unwrap();
            }
        }

        Ok(())
    }

    /// 将颜色转换为 CSS 颜色字符串
    fn color_to_string(&self, color: &Color) -> String {
        if color.a >= 1.0 {
            format!(
                "rgb({}, {}, {})",
                (color.r * 255.0) as u8,
                (color.g * 255.0) as u8,
                (color.b * 255.0) as u8
            )
        } else {
            format!(
                "rgba({}, {}, {}, {})",
                (color.r * 255.0) as u8,
                (color.g * 255.0) as u8,
                (color.b * 255.0) as u8,
                self.format_number(color.a as f64)
            )
        }
    }

    /// 格式化数字
    fn format_number(&self, value: f64) -> String {
        format!("{:.prec$}", value, prec = self.settings.precision)
    }

    /// 转义文本
    fn escape_text(&self, text: &str) -> String {
        text.replace('&', "&amp;")
            .replace('<', "&lt;")
            .replace('>', "&gt;")
            .replace('"', "&quot;")
            .replace('\'', "&#39;")
    }

    /// 将 LineCap 转换为 SVG 属性
    fn line_cap_to_svg(&self, cap: &LineCap) -> String {
        match cap {
            LineCap::Butt => String::new(),
            LineCap::Round => r#" stroke-linecap="round""#.to_string(),
            LineCap::Square => r#" stroke-linecap="square""#.to_string(),
        }
    }

    /// 将 LineJoin 转换为 SVG 属性
    fn line_join_to_svg(&self, join: &LineJoin) -> String {
        match join {
            LineJoin::Miter => String::new(),
            LineJoin::Round => r#" stroke-linejoin="round""#.to_string(),
            LineJoin::Bevel => r#" stroke-linejoin="bevel""#.to_string(),
        }
    }

    /// 将虚线模式转换为 SVG 属性
    fn dash_pattern_to_svg(&self, pattern: &Option<Vec<f64>>) -> String {
        if let Some(dashes) = pattern {
            if !dashes.is_empty() {
                let dash_str = dashes
                    .iter()
                    .map(|d| self.format_number(*d))
                    .collect::<Vec<_>>()
                    .join(",");
                format!(r#" stroke-dasharray="{}""#, dash_str)
            } else {
                String::new()
            }
        } else {
            String::new()
        }
    }

    /// 将 TextAlign 转换为 SVG text-anchor
    fn text_align_to_svg(&self, align: &TextAlign) -> &'static str {
        match align {
            TextAlign::Left | TextAlign::Start => "start",
            TextAlign::Center => "middle",
            TextAlign::Right | TextAlign::End => "end",
        }
    }

    /// 将 TextBaseline 转换为 SVG dominant-baseline
    fn text_baseline_to_svg(&self, baseline: &TextBaseline) -> &'static str {
        match baseline {
            TextBaseline::Top => "hanging",
            TextBaseline::Middle => "middle",
            TextBaseline::Bottom => "baseline",
            TextBaseline::Alphabetic => "alphabetic",
            TextBaseline::Hanging => "hanging",
            TextBaseline::Ideographic => "ideographic",
        }
    }

    /// 将 FontWeight 转换为 SVG font-weight
    fn font_weight_to_svg(&self, weight: &FontWeight) -> Option<&'static str> {
        match weight {
            FontWeight::Thin => Some("100"),
            FontWeight::Light => Some("300"),
            FontWeight::Normal => None, // 默认值，不需要属性
            FontWeight::Medium => Some("500"),
            FontWeight::Bold => Some("700"),
            FontWeight::ExtraBold => Some("800"),
            FontWeight::Black => Some("900"),
        }
    }

    /// 将路径命令转换为 SVG 路径字符串
    fn path_commands_to_svg(&self, commands: &[PathCommand]) -> String {
        let mut result = String::new();

        for command in commands {
            match command {
                PathCommand::MoveTo(point) => {
                    write!(
                        result,
                        "M {} {} ",
                        self.format_number(point.x),
                        self.format_number(point.y)
                    ).unwrap();
                }
                PathCommand::LineTo(point) => {
                    write!(
                        result,
                        "L {} {} ",
                        self.format_number(point.x),
                        self.format_number(point.y)
                    ).unwrap();
                }
                PathCommand::CurveTo { control1, control2, to } => {
                    write!(
                        result,
                        "C {} {} {} {} {} {} ",
                        self.format_number(control1.x),
                        self.format_number(control1.y),
                        self.format_number(control2.x),
                        self.format_number(control2.y),
                        self.format_number(to.x),
                        self.format_number(to.y)
                    ).unwrap();
                }
                PathCommand::QuadTo { control, to } => {
                    write!(
                        result,
                        "Q {} {} {} {} ",
                        self.format_number(control.x),
                        self.format_number(control.y),
                        self.format_number(to.x),
                        self.format_number(to.y)
                    ).unwrap();
                }
                PathCommand::Close => {
                    write!(result, "Z ").unwrap();
                }
            }
        }

        result.trim().to_string()
    }

    /// 获取当前 SVG 内容
    pub fn get_content(&self) -> &str {
        &self.content
    }

    /// 保存 SVG 到文件
    pub fn save_to_file(&self, path: &str) -> Result<()> {
        std::fs::write(path, &self.content)
            .map_err(|e| echarts_core::ChartError::Io(e))?;
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_svg_renderer_creation() {
        let renderer = SvgRenderer::with_size(800.0, 600.0);
        assert_eq!(renderer.settings.width, 800.0);
        assert_eq!(renderer.settings.height, 600.0);
    }

    #[test]
    fn test_render_line_command() {
        let mut renderer = SvgRenderer::with_size(400.0, 300.0);

        let commands = vec![DrawCommand::Line {
            from: Point::new(10.0, 10.0),
            to: Point::new(100.0, 100.0),
            style: LineStyle {
                color: Color::rgb(1.0, 0.0, 0.0),
                width: 2.0,
                opacity: 1.0,
                dash_pattern: None,
                cap: LineCap::Round,
                join: LineJoin::Round,
            },
        }];

        let svg = renderer.render_commands(commands).unwrap();
        assert!(svg.contains("<svg"));
        assert!(svg.contains("</svg>"));
        assert!(svg.contains("<line"));
        assert!(svg.contains("stroke=\"rgb(255, 0, 0)\""));
        assert!(svg.contains("stroke-width=\"2.00\""));
    }

    #[test]
    fn test_render_circle_command() {
        let mut renderer = SvgRenderer::with_size(400.0, 300.0);

        let commands = vec![DrawCommand::Circle {
            center: Point::new(50.0, 50.0),
            radius: 25.0,
            style: CircleStyle {
                fill: Some(Color::rgb(0.0, 1.0, 0.0)),
                stroke: Some(LineStyle {
                    color: Color::rgb(0.0, 0.0, 1.0),
                    width: 1.0,
                    opacity: 1.0,
                    dash_pattern: None,
                    cap: LineCap::Butt,
                    join: LineJoin::Miter,
                }),
                opacity: 1.0,
            },
        }];

        let svg = renderer.render_commands(commands).unwrap();
        assert!(svg.contains("<circle"));
        assert!(svg.contains("fill=\"rgb(0, 255, 0)\""));
        assert!(svg.contains("stroke=\"rgb(0, 0, 255)\""));
    }

    #[test]
    fn test_color_conversion() {
        let renderer = SvgRenderer::with_size(100.0, 100.0);

        assert_eq!(
            renderer.color_to_string(&Color::rgb(1.0, 0.0, 0.0)),
            "rgb(255, 0, 0)"
        );

        assert_eq!(
            renderer.color_to_string(&Color::rgba(1.0, 0.0, 0.0, 0.5)),
            "rgba(255, 0, 0, 0.50)"
        );
    }
}
