use crate::ProtocolError;
use async_trait::async_trait;
use serialport::SerialPort;
use std::io::{Read, Write};
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream as AsyncTcpStream;
use tokio::net::UdpSocket as AsyncUdpSocket;
use tracing::{debug, error, warn};

#[async_trait]
pub trait Transport: Send + Sync {
    async fn send(&mut self, data: &[u8]) -> Result<(), ProtocolError>;
    async fn recv(&mut self, buf: &mut [u8], timeout: Duration) -> Result<usize, ProtocolError>;
    async fn set_timeout(&mut self, timeout: Duration) -> Result<(), ProtocolError>;
    async fn reconnect(&mut self) -> Result<(), ProtocolError>;
    async fn check_connection(&mut self) -> bool;
}

/// 串口传输实现
pub struct SerialTransport {
    port: Arc<Mutex<Box<dyn SerialPort + Send>>>,
    config: SerialPortConfig,
    reconnect_attempts: usize,
    max_reconnect_attempts: usize,
}

/// 串口配置结构体
#[derive(Clone)]
pub struct SerialPortConfig {
    pub port_name: String,
    pub baud_rate: u32,
    pub timeout: Duration,
    pub data_bits: serialport::DataBits,
    pub flow_control: serialport::FlowControl,
    pub parity: serialport::Parity,
    pub stop_bits: serialport::StopBits,
}

impl Default for SerialPortConfig {
    fn default() -> Self {
        Self {
            port_name: String::new(),
            baud_rate: 115200,
            timeout: Duration::from_millis(100),
            data_bits: serialport::DataBits::Eight,
            flow_control: serialport::FlowControl::None,
            parity: serialport::Parity::None,
            stop_bits: serialport::StopBits::One,
        }
    }
}

impl SerialTransport {
    pub fn new(port_name: &str, baud_rate: u32) -> Result<Self, ProtocolError> {
        let config = SerialPortConfig {
            port_name: port_name.to_string(),
            baud_rate,
            ..Default::default()
        };

        Self::with_config(&config)
    }

    pub fn with_config(config: &SerialPortConfig) -> Result<Self, ProtocolError> {
        let port = serialport::new(&config.port_name, config.baud_rate)
            .timeout(config.timeout)
            .data_bits(config.data_bits)
            .flow_control(config.flow_control)
            .parity(config.parity)
            .stop_bits(config.stop_bits)
            .open_native()
            .map_err(ProtocolError::Serial)?;

        debug!(
            "串口 {} 打开成功 (波特率: {})",
            config.port_name, config.baud_rate
        );

        Ok(Self {
            port: Arc::new(Mutex::new(Box::new(port))),
            config: config.clone(),
            reconnect_attempts: 0,
            max_reconnect_attempts: 3,
        })
    }

    pub fn available_ports() -> Vec<serialport::SerialPortInfo> {
        match serialport::available_ports() {
            Ok(ports) => ports,
            Err(e) => {
                error!("获取可用串口失败: {}", e);
                Vec::new()
            }
        }
    }

    /// 重置重连尝试计数器
    fn reset_reconnect_counter(&mut self) {
        self.reconnect_attempts = 0;
    }

    /// 内部重连方法
    fn try_reconnect(&mut self) -> Result<(), ProtocolError> {
        if self.reconnect_attempts >= self.max_reconnect_attempts {
            return Err(ProtocolError::DeviceError(format!(
                "串口 {} 重连失败，已超过最大尝试次数 {}",
                self.config.port_name, self.max_reconnect_attempts
            )));
        }

        self.reconnect_attempts += 1;
        warn!(
            "尝试重连串口 {} (第 {}/{} 次)",
            self.config.port_name, self.reconnect_attempts, self.max_reconnect_attempts
        );

        // 关闭现有连接尝试创建新连接
        let new_port = serialport::new(&self.config.port_name, self.config.baud_rate)
            .timeout(self.config.timeout)
            .data_bits(self.config.data_bits)
            .flow_control(self.config.flow_control)
            .parity(self.config.parity)
            .stop_bits(self.config.stop_bits)
            .open_native()
            .map_err(ProtocolError::Serial)?;

        // 更新端口
        {
            let mut port_guard = match self.port.lock() {
                Ok(guard) => guard,
                Err(_) => {
                    return Err(ProtocolError::DeviceError(
                        "获取串口锁失败，无法重连".to_string(),
                    ));
                }
            };

            *port_guard = Box::new(new_port);
        } // 在这里释放 port_guard

        debug!("串口 {} 重连成功", self.config.port_name);
        self.reset_reconnect_counter();
        Ok(())
    }

    /// 设置最大重连尝试次数
    pub fn set_max_reconnect_attempts(&mut self, attempts: usize) {
        self.max_reconnect_attempts = attempts;
    }
}

#[async_trait]
impl Transport for SerialTransport {
    async fn send(&mut self, data: &[u8]) -> Result<(), ProtocolError> {
        let mut result = {
            let mut port = match self.port.lock() {
                Ok(guard) => guard,
                Err(_) => {
                    return Err(ProtocolError::DeviceError(
                        "获取串口锁失败，无法发送数据".to_string(),
                    ));
                }
            };
            port.write_all(data).map_err(Into::into)
        };

        // 如果发送失败，尝试重连
        if result.is_err() {
            warn!("串口 {} 发送数据失败，尝试重连", self.config.port_name);
            match self.try_reconnect() {
                Ok(_) => {
                    // 重连成功后再次尝试发送
                    let mut port = self.port.lock().unwrap();
                    result = port.write_all(data).map_err(Into::into);
                }
                Err(e) => {
                    return Err(e);
                }
            }
        }

        result
    }

    async fn recv(&mut self, buf: &mut [u8], timeout: Duration) -> Result<usize, ProtocolError> {
        // 获取端口并设置超时
        let mut result = {
            let mut port = match self.port.lock() {
                Ok(guard) => guard,
                Err(_) => {
                    return Err(ProtocolError::DeviceError(
                        "获取串口锁失败，无法接收数据".to_string(),
                    ));
                }
            };
            port.set_timeout(timeout).map_err(ProtocolError::Serial)?;
            port.read(buf).map_err(Into::into)
        };

        // 如果接收失败，尝试重连
        if let Err(ref e) = result {
            // 只对特定错误进行重连
            if let ProtocolError::Io(io_err) = e {
                if io_err.kind() != std::io::ErrorKind::TimedOut {
                    warn!(
                        "串口 {} 接收数据失败，尝试重连: {}",
                        self.config.port_name, io_err
                    );
                    match self.try_reconnect() {
                        Ok(_) => {
                            // 重连成功后再次尝试接收
                            let mut port = self.port.lock().unwrap();
                            port.set_timeout(timeout).map_err(ProtocolError::Serial)?;
                            result = port.read(buf).map_err(Into::into);
                        }
                        Err(e) => {
                            return Err(e);
                        }
                    }
                }
            }
        }

        // 成功接收数据时重置重连计数器
        if result.is_ok() && self.reconnect_attempts > 0 {
            self.reset_reconnect_counter();
        }

        result
    }

    async fn set_timeout(&mut self, timeout: Duration) -> Result<(), ProtocolError> {
        let mut port = match self.port.lock() {
            Ok(guard) => guard,
            Err(_) => {
                return Err(ProtocolError::DeviceError(
                    "获取串口锁失败，无法设置超时".to_string(),
                ));
            }
        };
        port.set_timeout(timeout).map_err(ProtocolError::Serial)?;
        // 更新配置
        self.config.timeout = timeout;
        Ok(())
    }

    async fn reconnect(&mut self) -> Result<(), ProtocolError> {
        self.try_reconnect()
    }

    async fn check_connection(&mut self) -> bool {
        // 一个简单的方法来检查连接是否有效
        let mut port = match self.port.lock() {
            Ok(guard) => guard,
            Err(_) => return false,
        };

        // 尝试获取端口状态参数，如果失败则连接可能断开
        match port.bytes_to_read() {
            Ok(_) => true,
            Err(_) => false,
        }
    }
}

// 异步UDP实现
pub struct UdpTransport {
    socket: AsyncUdpSocket,
    remote_addr: String,
    bind_addr: String,
}

impl UdpTransport {
    pub async fn new(bind_addr: &str, remote_addr: &str) -> Result<Self, ProtocolError> {
        let socket = AsyncUdpSocket::bind(bind_addr).await?;
        socket.connect(remote_addr).await?;
        debug!("UDP 连接已建立: {} -> {}", bind_addr, remote_addr);

        Ok(Self {
            socket,
            remote_addr: remote_addr.to_string(),
            bind_addr: bind_addr.to_string(),
        })
    }
}

#[async_trait]
impl Transport for UdpTransport {
    async fn send(&mut self, data: &[u8]) -> Result<(), ProtocolError> {
        match self.socket.send(data).await {
            Ok(_) => Ok(()),
            Err(e) => {
                error!("UDP 发送失败: {}", e);
                Err(e.into())
            }
        }
    }

    async fn recv(&mut self, buf: &mut [u8], timeout: Duration) -> Result<usize, ProtocolError> {
        match tokio::time::timeout(timeout, self.socket.recv(buf)).await {
            Ok(result) => result.map_err(Into::into),
            Err(_) => Err(ProtocolError::Timeout),
        }
    }

    async fn set_timeout(&mut self, _timeout: Duration) -> Result<(), ProtocolError> {
        // UDP 不支持直接设置超时，通过 tokio::time::timeout 实现
        Ok(())
    }

    async fn reconnect(&mut self) -> Result<(), ProtocolError> {
        // 重新创建连接
        let socket = AsyncUdpSocket::bind(&self.bind_addr).await?;
        socket.connect(&self.remote_addr).await?;
        self.socket = socket;
        debug!("UDP 连接已重建: {} -> {}", self.bind_addr, self.remote_addr);
        Ok(())
    }

    async fn check_connection(&mut self) -> bool {
        // UDP 是无连接的，这个检查不是很有意义，但为了接口一致性保留
        true
    }
}

// 异步TCP实现
pub struct TcpTransport {
    stream: AsyncTcpStream,
    addr: String,
    reconnect_attempts: usize,
    max_reconnect_attempts: usize,
}

impl TcpTransport {
    pub async fn new(addr: &str) -> Result<Self, ProtocolError> {
        let stream = AsyncTcpStream::connect(addr).await?;
        debug!("TCP 连接已建立: {}", addr);

        Ok(Self {
            stream,
            addr: addr.to_string(),
            reconnect_attempts: 0,
            max_reconnect_attempts: 3,
        })
    }

    /// 设置最大重连尝试次数
    pub fn set_max_reconnect_attempts(&mut self, attempts: usize) {
        self.max_reconnect_attempts = attempts;
    }

    /// 重置重连尝试计数器
    fn reset_reconnect_counter(&mut self) {
        self.reconnect_attempts = 0;
    }

    /// 内部重连方法
    async fn try_reconnect(&mut self) -> Result<(), ProtocolError> {
        if self.reconnect_attempts >= self.max_reconnect_attempts {
            return Err(ProtocolError::DeviceError(format!(
                "TCP 连接 {} 重连失败，已超过最大尝试次数 {}",
                self.addr, self.max_reconnect_attempts
            )));
        }

        self.reconnect_attempts += 1;
        warn!(
            "尝试重连 TCP {} (第 {}/{} 次)",
            self.addr, self.reconnect_attempts, self.max_reconnect_attempts
        );

        match AsyncTcpStream::connect(&self.addr).await {
            Ok(stream) => {
                self.stream = stream;
                debug!("TCP 连接 {} 重连成功", self.addr);
                self.reset_reconnect_counter();
                Ok(())
            }
            Err(e) => {
                error!("TCP 重连失败 {}: {}", self.addr, e);
                Err(e.into())
            }
        }
    }
}

#[async_trait]
impl Transport for TcpTransport {
    async fn send(&mut self, data: &[u8]) -> Result<(), ProtocolError> {
        match self.stream.write_all(data).await {
            Ok(_) => Ok(()),
            Err(e) => {
                // 如果发送失败，尝试重连
                warn!("TCP 发送失败: {}，尝试重连", e);
                match self.try_reconnect().await {
                    Ok(_) => {
                        // 重连成功后再次尝试发送
                        self.stream.write_all(data).await.map_err(Into::into)
                    }
                    Err(e) => Err(e),
                }
            }
        }
    }

    async fn recv(&mut self, buf: &mut [u8], timeout: Duration) -> Result<usize, ProtocolError> {
        match tokio::time::timeout(timeout, self.stream.read(buf)).await {
            Ok(Ok(n)) => Ok(n),
            Ok(Err(e)) => {
                // 如果接收失败但不是超时，尝试重连
                warn!("TCP 接收失败: {}，尝试重连", e);
                match self.try_reconnect().await {
                    Ok(_) => {
                        // 重连成功后再次尝试接收
                        match tokio::time::timeout(timeout, self.stream.read(buf)).await {
                            Ok(result) => result.map_err(Into::into),
                            Err(_) => Err(ProtocolError::Timeout),
                        }
                    }
                    Err(e) => Err(e),
                }
            }
            Err(_) => Err(ProtocolError::Timeout),
        }
    }

    async fn set_timeout(&mut self, _timeout: Duration) -> Result<(), ProtocolError> {
        // TCP 不支持直接设置超时，通过 tokio::time::timeout 实现
        Ok(())
    }

    async fn reconnect(&mut self) -> Result<(), ProtocolError> {
        self.try_reconnect().await
    }

    async fn check_connection(&mut self) -> bool {
        // 检查 TCP 连接是否仍然有效
        !self.stream.peek(&mut [0u8]).await.is_err()
    }
}
