// src/settings.rs
use crate::logging::LogConfig;
use gpui::*;

#[derive(Debug, Clone)]
pub struct LogSettings {
    pub terminal_level: tracing::Level,
    pub file_level: tracing::Level,
    pub high_freq_logging: bool,
}

impl LogSettings {
    pub fn to_config(&self) -> LogConfig {
        LogConfig {
            log_dir: "logs".into(),
            max_files: 5,
            terminal_level: self.terminal_level,
            file_level: self.file_level,
            high_freq_targets: if self.high_freq_logging {
                vec!["high_freq".to_string()]
            } else {
                vec![]
            },
        }
    }
}

// pub fn logging_settings_view(cx: &mut Context<LogSettings>) -> impl IntoElement {
//     let log_settings = cx.global::<LogSettings>();

//     // 1. 使用DescriptionList作为布局容器 :cite[1]:cite[2]
//     gpui_component::description_list()
//         .item(
//             "终端日志级别",
//             segmented_control()
//                 .variant(SegmentedVariant::Pill) // 使用Pill样式导航标签 :cite[1]
//                 .selected(log_settings.terminal_level)
//                 .on_change(|level| log_settings.terminal_level = level)
//                 .options(vec![
//                     (Level::ERROR, "Error"),
//                     (Level::WARN, "Warn"),
//                     (Level::INFO, "Info"),
//                     (Level::DEBUG, "Debug"),
//                     (Level::TRACE, "Trace"),
//                 ]),
//         )
//         .item(
//             "文件日志级别",
//             segmented_control()
//                 .variant(SegmentedVariant::Pill)
//                 .selected(log_settings.file_level)
//                 .on_change(|level| log_settings.file_level = level)
//                 .options(vec![
//                     (Level::ERROR, "Error"),
//                     (Level::WARN, "Warn"),
//                     (Level::INFO, "Info"),
//                     (Level::DEBUG, "Debug"),
//                     (Level::TRACE, "Trace"),
//                 ]),
//         )
//         // .item(
//         //     "高级选项",
//         //     v_stack()
//         //         .spacing(4) // 使用紧凑间距 (8px→4px) :cite[2]
//         //         .child(
//         //             toggle_group() // 开关组组件 :cite[1]
//         //                 .item(
//         //                     toggle()
//         //                         .label("启用高频日志")
//         //                         .checked(log_settings.high_freq_logging)
//         //                         .on_toggle(|cx, checked| {
//         //                             cx.global_mut::<LogSettings>().high_freq_logging = checked;
//         //                         }),
//         //                 ),
//         //         )
//         //         .child(
//         //             button()
//         //                 .label("应用设置")
//         //                 .style(ButtonStyle::Primary) // 主操作按钮样式 :cite[1]
//         //                 .on_click(cx.listener(|_, cx| {
//         //                     let config = cx.global::<LogSettings>().to_config();
//         //                     cx.spawn(|cx| async move {
//         //                         cx.update(|cx| {
//         //                             cx.global_mut::<LoggingGuards>() = init_logging(config);

//         //                             // 使用Alert组件显示操作反馈 :cite[1]
//         //                             cx.show_alert(
//         //                                 Alert::new()
//         //                                     .title("日志配置已更新")
//         //                                     .description("新设置将在下次日志写入时生效")
//         //                                     .variant(AlertVariant::Success) // 成功状态提示
//         //                             );
//         //                         })
//         //                     })
//         //                 }),
//         //         ),
//         // )
// }
