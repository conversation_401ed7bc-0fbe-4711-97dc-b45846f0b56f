#!/usr/bin/env python3
"""
批量重构其他图表类型使用新的基础架构
"""

import os
import re
import glob

def refactor_chart_series():
    """批量重构图表系列使用新的基础架构"""
    
    # 需要重构的图表文件（除了已经完成的 line.rs 和 bar.rs）
    chart_files = [
        "crates/charts/src/scatter.rs",
        "crates/charts/src/pie.rs", 
        "crates/charts/src/enhanced_bar.rs",
        "crates/charts/src/radar.rs",
        "crates/charts/src/gauge.rs",
        "crates/charts/src/treemap.rs",
        "crates/charts/src/sunburst.rs",
        "crates/charts/src/funnel.rs",
        "crates/charts/src/candlestick.rs",
        "crates/charts/src/heatmap.rs",
        "crates/charts/src/surface3d.rs",
    ]
    
    for file_path in chart_files:
        if not os.path.exists(file_path):
            continue
            
        print(f"重构文件: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 1. 添加基础架构导入
            if "use crate::base::" not in content:
                import_pattern = r'(use echarts_core::\{[^}]+\};)\n(use serde::\{[^}]+\};)'
                replacement = r'\1\nuse crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};\n\2'
                content = re.sub(import_pattern, replacement, content)
            
            # 2. 替换结构体中的 name 字段为 config
            content = re.sub(
                r'(\s+)/// 系列名称\n\s+pub name: String,\n\s+\n\s+/// 图表数据\n\s+pub data: DataSet,',
                r'\1/// 基础配置\n\1pub config: ChartConfig,\n\1\n\1/// 图表数据\n\1pub data: DataSet,',
                content
            )
            
            # 3. 移除重复的 visible 和 z_index 字段
            content = re.sub(
                r'\s+/// 是否可见\n\s+pub visible: bool,\n\s+\n\s+/// Z-?index.*\n\s+pub z_index: i32,',
                '\n    // visible, z_index 现在在 config 中',
                content
            )
            
            # 4. 更新构造函数
            content = re.sub(
                r'(\s+Self \{\n\s+)name: name\.into\(\),\n(\s+data: DataSet::new\(\),)',
                r'\1config: ChartConfig {\n\1    name: name.into(),\n\1    ..ChartConfig::default()\n\1},\n\2',
                content
            )
            
            # 5. 移除构造函数中的 visible 和 z_index 设置
            content = re.sub(r',\n\s+visible: true,\n\s+z_index: 0,', ',', content)
            
            # 6. 更新字段访问
            content = re.sub(r'self\.visible', 'self.config.visible', content)
            content = re.sub(r'self\.z_index', 'self.config.z_index', content)
            content = re.sub(r'&self\.name', '&self.config.name', content)
            
            # 7. 更新方法实现
            content = re.sub(
                r'(\s+pub fn visible\(mut self, visible: bool\) -> Self \{\n\s+)self\.visible = visible;',
                r'\1self.config.visible = visible;',
                content
            )
            content = re.sub(
                r'(\s+pub fn z_index\(mut self, z_index: i32\) -> Self \{\n\s+)self\.z_index = z_index;',
                r'\1self.config.z_index = z_index;',
                content
            )
            
            # 8. 添加 ChartBase 和 ChartSeries 实现（在文件末尾）
            if "impl ChartBase for" not in content:
                # 提取系列名称
                series_match = re.search(r'pub struct (\w+Series)', content)
                if series_match:
                    series_name = series_match.group(1)
                    
                    chart_base_impl = f"""
// ============================================================================
// 图表基础架构集成 - ChartBase 实现
// ============================================================================

/// 为 {series_name} 实现 ChartBase trait
impl ChartBase for {series_name} {{
    fn name(&self) -> &str {{
        &self.config.name
    }}
    
    fn data(&self) -> &DataSet {{
        &self.data
    }}
    
    fn visible(&self) -> bool {{
        self.config.visible
    }}
    
    fn z_index(&self) -> i32 {{
        self.config.z_index
    }}
    
    fn bounds(&self) -> Option<Bounds> {{
        BoundsCalculator::from_dataset(&self.data)
    }}
    
    fn config(&self) -> &ChartConfig {{
        &self.config
    }}
    
    fn config_mut(&mut self) -> &mut ChartConfig {{
        &mut self.config
    }}
}}

/// 为 {series_name} 实现 ChartSeries trait
impl ChartSeries for {series_name} {{
    // 所有方法都由默认实现提供，基于 ChartBase
}}"""
                    
                    # 在最后一个 } 之前插入
                    content = content.rstrip() + chart_base_impl + "\n"
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ 已重构 {file_path}")
                
        except Exception as e:
            print(f"  ❌ 重构失败 {file_path}: {e}")

if __name__ == "__main__":
    print("🚀 开始批量重构图表类型...")
    refactor_chart_series()
    print("✅ 批量重构完成！")
