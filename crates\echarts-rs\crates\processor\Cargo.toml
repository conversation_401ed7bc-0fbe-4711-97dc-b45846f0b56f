[package]
name = "echarts-processor"
version = "0.1.0"
edition = "2021"
description = "ECharts data preprocessing engine - separates chart computation from rendering"
license = "MIT OR Apache-2.0"
repository = "https://github.com/your-repo/echarts-rs"
keywords = ["echarts", "charts", "data-processing", "visualization"]
categories = ["visualization", "graphics"]

[dependencies]
echarts-core = { path = "../core" }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"], optional = true }

[dev-dependencies]

[features]
default = []
async = ["tokio"]
debug = []


