//! HTML5 Canvas渲染器实现
//!
//! 提供基于HTML5 Canvas的渲染功能，生成可在Web浏览器中运行的JavaScript代码

use crate::{Renderer, RenderStats, PerformanceHint};
use echarts_core::{
    DrawCommand, Point, Bounds, Size, Color, Result,
    draw_commands::{PathCommand, RectStyle, CircleStyle, PathStyle},
    LineStyle, TextStyle, TextAlign, TextBaseline, LineCap, LineJoin, FontWeight, FontStyle,
};
use std::collections::HashMap;

/// HTML5 Canvas渲染器
pub struct HtmlCanvasRenderer {
    /// Canvas元素ID
    canvas_id: String,
    /// 画布尺寸
    bounds: Bounds,
    /// 渲染命令缓存
    command_buffer: Vec<String>,
    /// 样式缓存
    style_cache: HashMap<String, String>,
    /// 性能提示
    performance_hint: PerformanceHint,
    /// 渲染统计
    stats: RenderStats,
}

impl HtmlCanvasRenderer {
    /// 创建新的HTML5 Canvas渲染器
    pub fn new(canvas_id: impl Into<String>, bounds: Bounds) -> Self {
        Self {
            canvas_id: canvas_id.into(),
            bounds,
            command_buffer: Vec::new(),
            style_cache: HashMap::new(),
            performance_hint: PerformanceHint::default(),
            stats: RenderStats::default(),
        }
    }

    /// 设置性能提示
    pub fn set_performance_hint(&mut self, hint: PerformanceHint) {
        self.performance_hint = hint;
    }

    /// 获取渲染统计信息
    pub fn get_stats(&self) -> &RenderStats {
        &self.stats
    }

    /// 添加JavaScript命令
    fn add_command(&mut self, command: &str) {
        self.command_buffer.push(command.to_string());
        self.stats.commands_rendered += 1;
    }

    /// 渲染直线
    fn render_line(&mut self, from: Point, to: Point, style: &LineStyle) {
        self.add_command("ctx.beginPath();");
        self.add_command(&format!("ctx.moveTo({}, {});", from.x, from.y));
        self.add_command(&format!("ctx.lineTo({}, {});", to.x, to.y));
        
        self.apply_line_style(style);
        self.add_command("ctx.stroke();");
    }

    /// 渲染矩形
    fn render_rect(&mut self, bounds: Bounds, style: &RectStyle) {
        let x = bounds.origin.x;
        let y = bounds.origin.y;
        let width = bounds.size.width;
        let height = bounds.size.height;

        // 填充
        if let Some(fill_color) = style.fill {
            self.set_fill_style(&fill_color);
            self.add_command(&format!("ctx.globalAlpha = {};", style.opacity));
            
            if style.corner_radius > 0.0 {
                self.add_rounded_rect_path(x, y, width, height, style.corner_radius);
                self.add_command("ctx.fill();");
            } else {
                self.add_command(&format!("ctx.fillRect({}, {}, {}, {});", x, y, width, height));
            }
        }

        // 边框
        if let Some(stroke_style) = &style.stroke {
            self.apply_line_style(stroke_style);
            
            if style.corner_radius > 0.0 {
                self.add_rounded_rect_path(x, y, width, height, style.corner_radius);
                self.add_command("ctx.stroke();");
            } else {
                self.add_command(&format!("ctx.strokeRect({}, {}, {}, {});", x, y, width, height));
            }
        }
    }

    /// 渲染圆形
    fn render_circle(&mut self, center: Point, radius: f64, style: &CircleStyle) {
        self.add_command("ctx.beginPath();");
        self.add_command(&format!("ctx.arc({}, {}, {}, 0, 2 * Math.PI);", center.x, center.y, radius));

        // 填充
        if let Some(fill_color) = style.fill {
            self.set_fill_style(&fill_color);
            self.add_command(&format!("ctx.globalAlpha = {};", style.opacity));
            self.add_command("ctx.fill();");
        }

        // 边框
        if let Some(stroke_style) = &style.stroke {
            self.apply_line_style(stroke_style);
            self.add_command("ctx.stroke();");
        }
    }

    /// 渲染路径
    fn render_path(&mut self, commands: &[PathCommand], style: &PathStyle) {
        self.add_command("ctx.beginPath();");

        for cmd in commands {
            match cmd {
                PathCommand::MoveTo(point) => {
                    self.add_command(&format!("ctx.moveTo({}, {});", point.x, point.y));
                }
                PathCommand::LineTo(point) => {
                    self.add_command(&format!("ctx.lineTo({}, {});", point.x, point.y));
                }
                PathCommand::CurveTo { control1, control2, to } => {
                    self.add_command(&format!("ctx.bezierCurveTo({}, {}, {}, {}, {}, {});", 
                        control1.x, control1.y, control2.x, control2.y, to.x, to.y));
                }
                PathCommand::QuadTo { control, to } => {
                    self.add_command(&format!("ctx.quadraticCurveTo({}, {}, {}, {});", 
                        control.x, control.y, to.x, to.y));
                }
                PathCommand::Close => {
                    self.add_command("ctx.closePath();");
                }
            }
        }

        // 填充
        if let Some(fill_color) = style.fill {
            self.set_fill_style(&fill_color);
            self.add_command(&format!("ctx.globalAlpha = {};", style.opacity));
            self.add_command("ctx.fill();");
        }

        // 边框
        if let Some(stroke_style) = &style.stroke {
            self.apply_line_style(stroke_style);
            self.add_command("ctx.stroke();");
        }
    }

    /// 渲染文本
    fn render_text(&mut self, text: &str, position: Point, style: &TextStyle) {
        self.apply_text_style(style);
        
        let align = match style.align {
            TextAlign::Left => "left",
            TextAlign::Center => "center",
            TextAlign::Right => "right",
            TextAlign::Start => "start",
            TextAlign::End => "end",
        };
        
        let baseline = match style.baseline {
            TextBaseline::Top => "top",
            TextBaseline::Middle => "middle",
            TextBaseline::Bottom => "bottom",
            TextBaseline::Alphabetic => "alphabetic",
            TextBaseline::Hanging => "hanging",
            TextBaseline::Ideographic => "ideographic",
        };

        self.add_command(&format!("ctx.textAlign = '{}';", align));
        self.add_command(&format!("ctx.textBaseline = '{}';", baseline));

        if style.rotation != 0.0 {
            self.add_command("ctx.save();");
            self.add_command(&format!("ctx.translate({}, {});", position.x, position.y));
            self.add_command(&format!("ctx.rotate({});", style.rotation));
            self.add_command(&format!("ctx.fillText('{}', 0, 0);", text.replace('\'', "\\'")));
            self.add_command("ctx.restore();");
        } else {
            self.add_command(&format!("ctx.fillText('{}', {}, {});", text.replace('\'', "\\'"), position.x, position.y));
        }
    }

    /// 应用线条样式
    fn apply_line_style(&mut self, style: &LineStyle) {
        self.set_stroke_style(&style.color);
        self.add_command(&format!("ctx.lineWidth = {};", style.width));
        self.add_command(&format!("ctx.globalAlpha = {};", style.opacity));

        // 线条端点样式
        let cap = match style.cap {
            LineCap::Butt => "butt",
            LineCap::Round => "round",
            LineCap::Square => "square",
        };
        self.add_command(&format!("ctx.lineCap = '{}';", cap));

        // 线条连接样式
        let join = match style.join {
            LineJoin::Miter => "miter",
            LineJoin::Round => "round",
            LineJoin::Bevel => "bevel",
        };
        self.add_command(&format!("ctx.lineJoin = '{}';", join));

        // 虚线样式
        if let Some(dash_pattern) = &style.dash_pattern {
            let dash_array = dash_pattern.iter()
                .map(|&x| x.to_string())
                .collect::<Vec<_>>()
                .join(", ");
            self.add_command(&format!("ctx.setLineDash([{}]);", dash_array));
        } else {
            self.add_command("ctx.setLineDash([]);");
        }
    }

    /// 应用文本样式
    fn apply_text_style(&mut self, style: &TextStyle) {
        let font_weight = match style.font_weight {
            FontWeight::Thin => "100",
            FontWeight::ExtraLight => "200",
            FontWeight::Light => "300",
            FontWeight::Normal => "normal",
            FontWeight::Medium => "500",
            FontWeight::SemiBold => "600",
            FontWeight::Bold => "bold",
            FontWeight::ExtraBold => "800",
            FontWeight::Black => "900",
        };

        let font_style = match style.font_style {
            FontStyle::Normal => "normal",
            FontStyle::Italic => "italic",
            FontStyle::Oblique => "oblique",
        };

        self.add_command(&format!("ctx.font = '{} {} {}px {}';", 
            font_style, font_weight, style.font_size, style.font_family));
        
        self.set_fill_style(&style.color);
        self.add_command(&format!("ctx.globalAlpha = {};", style.opacity));
    }

    /// 设置填充样式
    fn set_fill_style(&mut self, color: &Color) {
        let color_str = format!("rgba({}, {}, {}, {})", 
            (color.r * 255.0) as u8, 
            (color.g * 255.0) as u8, 
            (color.b * 255.0) as u8, 
            color.a);
        self.add_command(&format!("ctx.fillStyle = '{}';", color_str));
    }

    /// 设置描边样式
    fn set_stroke_style(&mut self, color: &Color) {
        let color_str = format!("rgba({}, {}, {}, {})", 
            (color.r * 255.0) as u8, 
            (color.g * 255.0) as u8, 
            (color.b * 255.0) as u8, 
            color.a);
        self.add_command(&format!("ctx.strokeStyle = '{}';", color_str));
    }

    /// 添加圆角矩形路径
    fn add_rounded_rect_path(&mut self, x: f64, y: f64, width: f64, height: f64, radius: f64) {
        self.add_command("ctx.beginPath();");
        self.add_command(&format!("ctx.moveTo({}, {});", x + radius, y));
        self.add_command(&format!("ctx.lineTo({}, {});", x + width - radius, y));
        self.add_command(&format!("ctx.quadraticCurveTo({}, {}, {}, {});", x + width, y, x + width, y + radius));
        self.add_command(&format!("ctx.lineTo({}, {});", x + width, y + height - radius));
        self.add_command(&format!("ctx.quadraticCurveTo({}, {}, {}, {});", x + width, y + height, x + width - radius, y + height));
        self.add_command(&format!("ctx.lineTo({}, {});", x + radius, y + height));
        self.add_command(&format!("ctx.quadraticCurveTo({}, {}, {}, {});", x, y + height, x, y + height - radius));
        self.add_command(&format!("ctx.lineTo({}, {});", x, y + radius));
        self.add_command(&format!("ctx.quadraticCurveTo({}, {}, {}, {});", x, y, x + radius, y));
        self.add_command("ctx.closePath();");
    }

    /// 获取生成的JavaScript代码
    pub fn get_javascript(&self) -> String {
        let setup = format!(
            "const canvas = document.getElementById('{}');\nconst ctx = canvas.getContext('2d');\ncanvas.width = {};\ncanvas.height = {};\n",
            self.canvas_id, self.bounds.size.width, self.bounds.size.height
        );
        
        format!("{}{}", setup, self.command_buffer.join("\n"))
    }

    /// 获取完整的HTML页面
    pub fn get_html_page(&self, title: &str) -> String {
        format!(r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{}</title>
    <style>
        body {{
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(15px);
        }}
        canvas {{
            border: 1px solid #ddd;
            border-radius: 8px;
            display: block;
            margin: 20px auto;
            background: white;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }}
        h1 {{
            text-align: center;
            color: #333;
            margin-bottom: 10px;
            font-size: 2.2em;
        }}
        .subtitle {{
            text-align: center;
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
        }}
        .stats {{
            background: rgba(255,255,255,0.8);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: center;
            font-size: 0.9em;
            color: #555;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{}</h1>
        <p class="subtitle">基于HTML5 Canvas的ECharts-rs渲染演示</p>
        <canvas id="{}"></canvas>
        <div class="stats">
            渲染命令数: {} | 画布尺寸: {}×{} | 渲染器: HTML5 Canvas
        </div>
    </div>
    <script>
        {}
    </script>
</body>
</html>"#, 
            title, title, self.canvas_id, 
            self.stats.commands_rendered, 
            self.bounds.size.width, self.bounds.size.height,
            self.get_javascript())
    }
}

impl Renderer for HtmlCanvasRenderer {
    fn render_commands(&mut self, commands: Vec<DrawCommand>, _bounds: Bounds) -> Result<()> {
        let start_time = std::time::Instant::now();
        
        self.add_command("ctx.save();");
        
        for command in &commands {
            match command {
                DrawCommand::Line { from, to, style } => {
                    self.render_line(*from, *to, style);
                }
                DrawCommand::Rect { bounds, style } => {
                    self.render_rect(*bounds, style);
                }
                DrawCommand::Circle { center, radius, style } => {
                    self.render_circle(*center, *radius, style);
                }
                DrawCommand::Path { commands, style } => {
                    self.render_path(commands, style);
                }
                DrawCommand::Text { text, position, style } => {
                    self.render_text(text, *position, style);
                }
                _ => {
                    // 其他命令类型暂未实现
                }
            }
        }
        
        self.add_command("ctx.restore();");
        
        self.stats.render_time_ms = start_time.elapsed().as_secs_f64() * 1000.0;
        Ok(())
    }

    fn clear(&mut self) {
        self.command_buffer.clear();
        self.style_cache.clear();
        self.stats = RenderStats::default();
        self.add_command("ctx.clearRect(0, 0, canvas.width, canvas.height);");
    }

    fn bounds(&self) -> Bounds {
        self.bounds
    }

    fn set_bounds(&mut self, bounds: Bounds) {
        self.bounds = bounds;
    }
}
