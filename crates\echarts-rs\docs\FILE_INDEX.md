# ECharts-rs 文件索引

## 📁 快速文件导航

### 🏗️ 项目根目录
```
FscDAQ_echarts/
├── Cargo.toml                  # 工作空间配置
├── crates/                     # 所有子项目
└── doc/                        # 项目文档
```

### 📦 主要 Crates

#### echarts-rs (主库)
- **路径**: `crates/echarts-rs/`
- **入口**: `src/lib.rs`
- **配置**: `Cargo.toml`
- **文档**: `docs/`
- **示例**: `examples/`
- **测试**: `tests/`

#### echarts-core (核心模块)
- **路径**: `crates/echarts-rs/crates/core/`
- **关键文件**:
  - `src/lib.rs` - 模块入口和导出
  - `src/chart.rs` - Chart 和 Series 定义
  - `src/data.rs` - 数据结构 (DataValue, DataPoint, DataSet)
  - `src/geometry.rs` - 几何类型 (Point, Bounds)
  - `src/draw_commands.rs` - 绘制命令系统
  - `src/color.rs` - 颜色系统
  - `src/error.rs` - 错误类型

#### echarts-charts (图表实现)
- **路径**: `crates/echarts-rs/crates/charts/`
- **图表类型**:
  - `src/line.rs` - 折线图
  - `src/bar.rs` - 柱状图
  - `src/pie.rs` - 饼图
  - `src/scatter.rs` - 散点图
  - `src/radar.rs` - 雷达图
  - `src/gauge.rs` - 仪表盘
  - `src/heatmap.rs` - 热力图
  - `src/candlestick.rs` - K线图
  - `src/funnel.rs` - 漏斗图
  - `src/treemap.rs` - 树图
  - `src/sunburst.rs` - 旭日图
- **基础文件**:
  - `src/base.rs` - ChartBase trait 和配置

#### echarts-renderer (渲染器)
- **路径**: `crates/echarts-rs/crates/renderer/`
- **渲染器实现**:
  - `src/lib.rs` - 渲染器抽象
  - `gpui_renderer/` - GPUI 渲染器
  - `svg-renderer/` - SVG 渲染器
  - `image-renderer/` - 图像渲染器

#### echarts-interaction (交互系统)
- **路径**: `crates/echarts-rs/crates/interaction/`
- **交互功能**:
  - `src/lib.rs` - 交互管理器
  - `src/tooltip.rs` - 提示框
  - `src/zoom_pan.rs` - 缩放平移
  - `src/selection.rs` - 选择功能
  - `src/legend_interaction.rs` - 图例交互

#### echarts-themes (主题系统)
- **路径**: `crates/echarts-rs/crates/themes/`
- **主题文件**:
  - `src/lib.rs` - 主题系统入口
  - `src/builtin.rs` - 内置主题
  - `src/manager.rs` - 主题管理器

## 🔍 按功能分类的文件

### 核心架构文件
| 文件 | 功能 | 重要程度 |
|------|------|----------|
| `core/src/lib.rs` | 核心模块入口 | ⭐⭐⭐⭐⭐ |
| `core/src/chart.rs` | 图表和系列定义 | ⭐⭐⭐⭐⭐ |
| `core/src/data.rs` | 数据结构定义 | ⭐⭐⭐⭐⭐ |
| `core/src/draw_commands.rs` | 绘制命令系统 | ⭐⭐⭐⭐⭐ |
| `charts/src/base.rs` | 图表基础 trait | ⭐⭐⭐⭐ |

### 图表实现文件
| 文件 | 图表类型 | 完成度 |
|------|----------|--------|
| `charts/src/line.rs` | 折线图 | ✅ 完成 |
| `charts/src/bar.rs` | 柱状图 | ✅ 完成 |
| `charts/src/pie.rs` | 饼图 | ✅ 完成 |
| `charts/src/scatter.rs` | 散点图 | ✅ 完成 |
| `charts/src/radar.rs` | 雷达图 | 🚧 开发中 |
| `charts/src/gauge.rs` | 仪表盘 | 🚧 开发中 |
| `charts/src/heatmap.rs` | 热力图 | 🚧 开发中 |

### 渲染系统文件
| 文件 | 渲染器类型 | 状态 |
|------|------------|------|
| `renderer/gpui_renderer/src/lib.rs` | GPUI 渲染器 | ✅ 主要 |
| `renderer/svg-renderer/` | SVG 渲染器 | 🚧 开发中 |
| `renderer/image-renderer/` | 图像渲染器 | 🚧 开发中 |

### 配置文件
| 文件 | 用途 | 说明 |
|------|------|------|
| `Cargo.toml` (根) | 工作空间配置 | 定义所有成员项目 |
| `echarts-rs/Cargo.toml` | 主库配置 | 依赖和功能配置 |
| `core/Cargo.toml` | 核心模块配置 | 基础依赖 |
| `charts/Cargo.toml` | 图表模块配置 | 图表相关依赖 |

### 测试文件
| 文件 | 测试类型 | 覆盖范围 |
|------|----------|----------|
| `tests/basic_functionality.rs` | 基础功能测试 | 核心 API |
| `tests/integration_tests.rs` | 集成测试 | 完整流程 |
| `charts/src/tests.rs` | 图表测试 | 图表功能 |
| `core/src/tests.rs` | 核心测试 | 基础类型 |

### 示例文件
| 文件 | 示例类型 | 说明 |
|------|----------|------|
| `examples/gpui_line_chart.rs` | GPUI 折线图 | 基础使用 |
| `examples/chart_builder_demo.rs` | 图表构建器 | API 演示 |
| `examples/performance_test.rs` | 性能测试 | 性能基准 |

### 文档文件
| 文件 | 文档类型 | 内容 |
|------|----------|------|
| `docs/QUICKSTART.md` | 快速开始 | 入门指南 |
| `docs/API.md` | API 参考 | 完整 API |
| `docs/ARCHITECTURE.md` | 架构文档 | 设计说明 |
| `docs/COMPLETE_PROJECT_DOCUMENTATION.md` | 完整文档 | 本文档 |

## 🎯 开发者快速定位

### 我想要...

#### 了解项目架构
1. 阅读 `docs/ARCHITECTURE.md`
2. 查看 `core/src/lib.rs`
3. 理解 `core/src/chart.rs`

#### 添加新图表类型
1. 参考 `charts/src/line.rs`
2. 实现 `Series` 和 `ChartBase` trait
3. 更新 `charts/src/lib.rs`

#### 修改渲染逻辑
1. 查看 `core/src/draw_commands.rs`
2. 修改 `renderer/gpui_renderer/src/lib.rs`
3. 测试渲染效果

#### 添加交互功能
1. 查看 `interaction/src/lib.rs`
2. 参考现有交互实现
3. 扩展交互管理器

#### 自定义主题
1. 查看 `themes/src/builtin.rs`
2. 使用 `themes/src/manager.rs`
3. 创建自定义主题

#### 运行测试
1. 基础测试: `cargo test basic_functionality`
2. 集成测试: `cargo test integration_tests`
3. 性能测试: `cargo run --example performance_test`

#### 查看示例
1. GPUI 示例: `cargo run --example gpui_line_chart`
2. 构建器示例: `cargo run --example chart_builder_demo`
3. 完整示例: 查看 `examples/` 目录

## 📚 相关文档链接

- [完整项目文档](./COMPLETE_PROJECT_DOCUMENTATION.md)
- [快速开始指南](./QUICKSTART.md)
- [API 参考文档](./API.md)
- [架构设计文档](./ARCHITECTURE.md)
- [项目状态报告](./PROJECT_STATUS.md)

## 🔧 开发工具

### 推荐的 IDE 配置
- **VS Code**: 安装 rust-analyzer 插件
- **IntelliJ IDEA**: 安装 Rust 插件
- **Vim/Neovim**: 配置 rust-analyzer LSP

### 有用的 Cargo 命令
```bash
# 检查代码
cargo check

# 格式化代码
cargo fmt

# 代码检查
cargo clippy

# 生成文档
cargo doc --open

# 运行基准测试
cargo bench
```

### 调试技巧
1. 使用 `tracing` 进行日志记录
2. 启用 `debug` 功能进行详细输出
3. 使用 `cargo test -- --nocapture` 查看测试输出
4. 利用 `criterion` 进行性能分析

这个文件索引帮助开发者快速定位到需要的文件和功能，提高开发效率。
