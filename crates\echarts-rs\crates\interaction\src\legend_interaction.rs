//! 图例交互系统
//!
//! 提供图例的点击、悬停等交互功能

use crate::{InteractionResult, HoveredElement};
use echarts_core::{Point, Bounds, Result};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// 图例交互管理器
#[derive(Debug)]
pub struct LegendInteractionManager {
    /// 配置选项
    config: LegendInteractionConfig,
    /// 当前状态
    state: LegendInteractionState,
}

/// 图例交互配置
#[derive(Debug, Clone)]
pub struct LegendInteractionConfig {
    /// 是否启用图例交互
    pub enabled: bool,
    /// 点击行为
    pub click_behavior: LegendClickBehavior,
    /// 悬停行为
    pub hover_behavior: LegendHoverBehavior,
    /// 是否支持多选
    pub multi_select: bool,
    /// 选择模式
    pub select_mode: LegendSelectMode,
    /// 动画配置
    pub animation: LegendAnimation,
}

/// 图例交互状态
#[derive(Debug, C<PERSON>)]
pub struct LegendInteractionState {
    /// 系列可见性状态
    pub series_visibility: HashMap<usize, bool>,
    /// 当前悬停的图例项
    pub hovered_legend: Option<usize>,
    /// 选中的图例项
    pub selected_legends: Vec<usize>,
    /// 图例项边界信息
    pub legend_bounds: HashMap<usize, Bounds>,
}

/// 图例点击行为
#[derive(Debug, Clone, PartialEq)]
pub enum LegendClickBehavior {
    /// 切换系列可见性
    ToggleVisibility,
    /// 选择系列
    SelectSeries,
    /// 高亮系列
    HighlightSeries,
    /// 自定义行为
    Custom,
    /// 无操作
    None,
}

/// 图例悬停行为
#[derive(Debug, Clone, PartialEq)]
pub enum LegendHoverBehavior {
    /// 高亮对应系列
    HighlightSeries,
    /// 显示工具提示
    ShowTooltip,
    /// 改变鼠标样式
    ChangeCursor,
    /// 无操作
    None,
}

/// 图例选择模式
#[derive(Debug, Clone, PartialEq)]
pub enum LegendSelectMode {
    /// 单选
    Single,
    /// 多选
    Multiple,
    /// 无选择
    None,
}

/// 图例动画配置
#[derive(Debug, Clone)]
pub struct LegendAnimation {
    /// 是否启用动画
    pub enabled: bool,
    /// 动画持续时间（毫秒）
    pub duration: u64,
    /// 动画缓动函数
    pub easing: LegendEasing,
}

/// 图例动画缓动函数
#[derive(Debug, Clone, PartialEq)]
pub enum LegendEasing {
    /// 线性
    Linear,
    /// 缓入
    EaseIn,
    /// 缓出
    EaseOut,
    /// 缓入缓出
    EaseInOut,
}

/// 图例项信息
#[derive(Debug, Clone)]
pub struct LegendItem {
    /// 系列索引
    pub series_index: usize,
    /// 图例文本
    pub text: String,
    /// 图例颜色
    pub color: echarts_core::Color,
    /// 图例符号
    pub symbol: LegendSymbol,
    /// 是否可见
    pub visible: bool,
    /// 是否启用
    pub enabled: bool,
    /// 边界
    pub bounds: Bounds,
}

/// 图例符号
#[derive(Debug, Clone, PartialEq)]
pub enum LegendSymbol {
    /// 圆形
    Circle,
    /// 矩形
    Rect,
    /// 线条
    Line,
    /// 三角形
    Triangle,
    /// 菱形
    Diamond,
    /// 自定义
    Custom(String),
}

impl Default for LegendInteractionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            click_behavior: LegendClickBehavior::ToggleVisibility,
            hover_behavior: LegendHoverBehavior::HighlightSeries,
            multi_select: false,
            select_mode: LegendSelectMode::Single,
            animation: LegendAnimation::default(),
        }
    }
}

impl Default for LegendAnimation {
    fn default() -> Self {
        Self {
            enabled: true,
            duration: 300,
            easing: LegendEasing::EaseOut,
        }
    }
}

impl Default for LegendInteractionState {
    fn default() -> Self {
        Self {
            series_visibility: HashMap::new(),
            hovered_legend: None,
            selected_legends: Vec::new(),
            legend_bounds: HashMap::new(),
        }
    }
}

impl LegendInteractionManager {
    /// 创建新的图例交互管理器
    pub fn new(config: LegendInteractionConfig) -> Self {
        Self {
            config,
            state: LegendInteractionState::default(),
        }
    }
    
    /// 初始化系列可见性
    pub fn initialize_series_visibility(&mut self, series_count: usize) {
        self.state.series_visibility.clear();
        for i in 0..series_count {
            self.state.series_visibility.insert(i, true);
        }
    }
    
    /// 设置图例项边界
    pub fn set_legend_bounds(&mut self, legend_bounds: HashMap<usize, Bounds>) {
        self.state.legend_bounds = legend_bounds;
    }
    
    /// 处理图例点击
    pub fn handle_legend_click(&mut self, position: Point) -> Result<InteractionResult> {
        if !self.config.enabled {
            return Ok(InteractionResult::None);
        }
        
        // 查找点击的图例项
        let clicked_legend = self.find_legend_at_position(position);
        
        if let Some(legend_index) = clicked_legend {
            match self.config.click_behavior {
                LegendClickBehavior::ToggleVisibility => {
                    self.toggle_series_visibility(legend_index)
                }
                LegendClickBehavior::SelectSeries => {
                    self.select_legend(legend_index)
                }
                LegendClickBehavior::HighlightSeries => {
                    Ok(InteractionResult::Custom {
                        name: "highlight_series".to_string(),
                        data: serde_json::json!({
                            "series_index": legend_index,
                            "highlight": true
                        }),
                    })
                }
                LegendClickBehavior::Custom => {
                    Ok(InteractionResult::Custom {
                        name: "legend_click".to_string(),
                        data: serde_json::json!({
                            "legend_index": legend_index,
                            "position": {
                                "x": position.x,
                                "y": position.y
                            }
                        }),
                    })
                }
                LegendClickBehavior::None => Ok(InteractionResult::None),
            }
        } else {
            Ok(InteractionResult::None)
        }
    }
    
    /// 处理图例悬停
    pub fn handle_legend_hover(&mut self, position: Point) -> Result<InteractionResult> {
        if !self.config.enabled {
            return Ok(InteractionResult::None);
        }
        
        let hovered_legend = self.find_legend_at_position(position);
        
        // 检查悬停状态是否发生变化
        if self.state.hovered_legend != hovered_legend {
            self.state.hovered_legend = hovered_legend;
            
            match self.config.hover_behavior {
                LegendHoverBehavior::HighlightSeries => {
                    if let Some(legend_index) = hovered_legend {
                        Ok(InteractionResult::Custom {
                            name: "highlight_series".to_string(),
                            data: serde_json::json!({
                                "series_index": legend_index,
                                "highlight": true
                            }),
                        })
                    } else {
                        Ok(InteractionResult::Custom {
                            name: "clear_highlight".to_string(),
                            data: serde_json::json!({}),
                        })
                    }
                }
                LegendHoverBehavior::ShowTooltip => {
                    if let Some(legend_index) = hovered_legend {
                        Ok(InteractionResult::ShowTooltip {
                            position,
                            content: crate::TooltipContent::simple(
                                "图例",
                                format!("系列 {}", legend_index)
                            ),
                        })
                    } else {
                        Ok(InteractionResult::HideTooltip)
                    }
                }
                LegendHoverBehavior::ChangeCursor => {
                    Ok(InteractionResult::Custom {
                        name: "change_cursor".to_string(),
                        data: serde_json::json!({
                            "cursor": if hovered_legend.is_some() { "pointer" } else { "default" }
                        }),
                    })
                }
                LegendHoverBehavior::None => Ok(InteractionResult::None),
            }
        } else {
            Ok(InteractionResult::None)
        }
    }
    
    /// 切换系列可见性
    fn toggle_series_visibility(&mut self, series_index: usize) -> Result<InteractionResult> {
        let current_visibility = self.state.series_visibility.get(&series_index).copied().unwrap_or(true);
        let new_visibility = !current_visibility;
        
        self.state.series_visibility.insert(series_index, new_visibility);
        
        Ok(InteractionResult::Custom {
            name: "toggle_series_visibility".to_string(),
            data: serde_json::json!({
                "series_index": series_index,
                "visible": new_visibility
            }),
        })
    }
    
    /// 选择图例
    fn select_legend(&mut self, legend_index: usize) -> Result<InteractionResult> {
        match self.config.select_mode {
            LegendSelectMode::Single => {
                self.state.selected_legends.clear();
                self.state.selected_legends.push(legend_index);
            }
            LegendSelectMode::Multiple => {
                if let Some(pos) = self.state.selected_legends.iter().position(|&x| x == legend_index) {
                    self.state.selected_legends.remove(pos);
                } else {
                    self.state.selected_legends.push(legend_index);
                }
            }
            LegendSelectMode::None => {
                return Ok(InteractionResult::None);
            }
        }
        
        Ok(InteractionResult::Custom {
            name: "select_legend".to_string(),
            data: serde_json::json!({
                "selected_legends": self.state.selected_legends
            }),
        })
    }
    
    /// 查找指定位置的图例项
    fn find_legend_at_position(&self, position: Point) -> Option<usize> {
        for (&legend_index, bounds) in &self.state.legend_bounds {
            if bounds.contains_point(position) {
                return Some(legend_index);
            }
        }
        None
    }
    
    /// 获取系列可见性
    pub fn is_series_visible(&self, series_index: usize) -> bool {
        self.state.series_visibility.get(&series_index).copied().unwrap_or(true)
    }
    
    /// 设置系列可见性
    pub fn set_series_visibility(&mut self, series_index: usize, visible: bool) {
        self.state.series_visibility.insert(series_index, visible);
    }
    
    /// 获取所有系列可见性
    pub fn get_series_visibility(&self) -> &HashMap<usize, bool> {
        &self.state.series_visibility
    }
    
    /// 显示所有系列
    pub fn show_all_series(&mut self) -> Result<InteractionResult> {
        for (_, visibility) in self.state.series_visibility.iter_mut() {
            *visibility = true;
        }
        
        Ok(InteractionResult::Custom {
            name: "show_all_series".to_string(),
            data: serde_json::json!({}),
        })
    }
    
    /// 隐藏所有系列
    pub fn hide_all_series(&mut self) -> Result<InteractionResult> {
        for (_, visibility) in self.state.series_visibility.iter_mut() {
            *visibility = false;
        }
        
        Ok(InteractionResult::Custom {
            name: "hide_all_series".to_string(),
            data: serde_json::json!({}),
        })
    }
    
    /// 反转所有系列可见性
    pub fn invert_all_series(&mut self) -> Result<InteractionResult> {
        for (_, visibility) in self.state.series_visibility.iter_mut() {
            *visibility = !*visibility;
        }
        
        Ok(InteractionResult::Custom {
            name: "invert_all_series".to_string(),
            data: serde_json::json!({}),
        })
    }
    
    /// 获取当前悬停的图例
    pub fn get_hovered_legend(&self) -> Option<usize> {
        self.state.hovered_legend
    }
    
    /// 获取选中的图例
    pub fn get_selected_legends(&self) -> &[usize] {
        &self.state.selected_legends
    }
    
    /// 清除选择
    pub fn clear_selection(&mut self) {
        self.state.selected_legends.clear();
    }
    
    /// 获取当前状态
    pub fn get_state(&self) -> &LegendInteractionState {
        &self.state
    }
}

impl LegendItem {
    /// 创建新的图例项
    pub fn new(
        series_index: usize,
        text: impl Into<String>,
        color: echarts_core::Color,
        bounds: Bounds,
    ) -> Self {
        Self {
            series_index,
            text: text.into(),
            color,
            symbol: LegendSymbol::Circle,
            visible: true,
            enabled: true,
            bounds,
        }
    }
    
    /// 设置符号
    pub fn symbol(mut self, symbol: LegendSymbol) -> Self {
        self.symbol = symbol;
        self
    }
    
    /// 设置可见性
    pub fn visible(mut self, visible: bool) -> Self {
        self.visible = visible;
        self
    }
    
    /// 设置启用状态
    pub fn enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }
}
