# ECharts 项目整体架构分析

## 项目概述

ECharts 是一个基于 TypeScript 开发的强大的交互式图表和数据可视化库，支持 Canvas 和 SVG 渲染。

### 基本信息
- **版本**: 6.0.0-beta.1
- **许可证**: Apache-2.0
- **主要依赖**: 
  - zrender: 6.0.0-rc.1 (底层渲染引擎)
  - tslib: 2.3.0 (TypeScript 运行时库)
- **构建工具**: Rollup + TypeScript + ESBuild
- **测试框架**: Jest

## 目录结构分析

### 核心源码目录 (`src/`)

```
src/
├── animation/          # 动画相关模块
├── chart/             # 图表类型实现
├── component/         # 组件实现
├── coord/             # 坐标系实现
├── core/              # 核心功能模块
├── data/              # 数据处理模块
├── export/            # 导出模块定义
├── i18n/              # 国际化支持
├── label/             # 标签布局和样式
├── layout/            # 布局算法
├── legacy/            # 兼容性模块
├── loading/           # 加载动画
├── model/             # 数据模型
├── preprocessor/      # 数据预处理
├── processor/         # 数据处理器
├── renderer/          # 渲染器安装
├── scale/             # 刻度计算
├── theme/             # 主题定义
├── util/              # 工具函数
├── view/              # 视图基类
├── visual/            # 视觉映射
├── echarts.ts         # 默认入口
├── echarts.all.ts     # 完整功能入口
├── echarts.common.ts  # 常用功能入口
├── echarts.simple.ts  # 简化版入口
└── echarts.blank.ts   # 空白入口
```

### 构建和配置目录

```
build/                 # 构建脚本和配置
├── build.js          # 主构建脚本
├── config.js         # 构建配置
├── dev-fast.js       # 开发模式快速构建
└── template/         # 构建模板

dist/                 # 构建输出目录
├── echarts.js        # UMD 格式
├── echarts.min.js    # 压缩版
├── echarts.esm.js    # ES 模块格式
└── extension/        # 扩展插件

theme/                # 主题文件
i18n/                 # 国际化文件
test/                 # 测试文件
```

## 模块化架构

### 1. 分层架构

```
应用层 (Application Layer)
├── 图表组件 (Charts)
├── 交互组件 (Components)
└── 主题和国际化 (Themes & i18n)

核心层 (Core Layer)
├── ECharts 核心 (Core)
├── 数据模型 (Model)
├── 坐标系 (Coordinate Systems)
└── 视觉映射 (Visual Mapping)

渲染层 (Rendering Layer)
├── Canvas 渲染器
├── SVG 渲染器
└── ZRender 引擎
```

### 2. 插件化设计

ECharts 采用插件化架构，通过 `use()` 函数动态注册功能模块：

```typescript
// 核心入口只包含基础功能
export * from './export/core';
import { use } from './extension';

// 按需注册渲染器和组件
use([CanvasRenderer, DatasetComponent]);
```

### 3. 入口文件策略

- **echarts.ts**: 默认入口，包含 Canvas 渲染器和数据集组件
- **echarts.all.ts**: 完整功能，包含所有图表类型和组件
- **echarts.common.ts**: 常用功能集合
- **echarts.simple.ts**: 简化版本
- **echarts.blank.ts**: 空白版本，需要手动注册所需功能

## 核心设计模式

### 1. 组件化架构
- 每个图表类型都是独立的组件
- 组件间通过事件和数据流通信
- 支持组件的动态加载和卸载

### 2. 数据驱动
- 基于配置对象 (Option) 驱动图表渲染
- 数据变更自动触发视图更新
- 支持增量更新和动画过渡

### 3. 坐标系抽象
- 统一的坐标系接口
- 支持多种坐标系：直角坐标系、极坐标系、地理坐标系等
- 坐标系与图表类型解耦

### 4. 视觉映射系统
- 将数据值映射到视觉属性（颜色、大小、形状等）
- 支持连续和离散映射
- 可配置的映射规则

## 构建系统

### 构建配置
- 使用 Rollup 作为主要构建工具
- TypeScript 编译和类型检查
- ESBuild 用于快速开发构建
- 支持多种输出格式：UMD、ES Module、CommonJS

### 输出产物
- 完整版本 (echarts.js)
- 压缩版本 (echarts.min.js)
- ES 模块版本 (echarts.esm.js)
- 按需加载版本 (lib/*)

## 扩展机制

### 1. 图表扩展
- 继承 Chart 基类
- 实现渲染逻辑和数据处理
- 注册到 ECharts 系统

### 2. 组件扩展
- 继承 Component 基类
- 实现组件特定功能
- 支持配置和交互

### 3. 坐标系扩展
- 实现坐标系接口
- 提供坐标转换功能
- 与图表类型集成

## 性能优化策略

### 1. 渲染优化
- Canvas 和 SVG 双渲染引擎
- 脏矩形检测和局部重绘
- 图形元素复用和缓存

### 2. 数据处理优化
- 大数据集的分块处理
- 数据采样和聚合
- 增量数据更新

### 3. 内存管理
- 对象池复用
- 及时清理无用引用
- 事件监听器管理

## 重构建议预览

基于架构分析，主要重构方向：

1. **模块解耦**: 进一步细化模块边界，减少循环依赖
2. **类型安全**: 加强 TypeScript 类型定义，提高代码质量
3. **性能优化**: 优化大数据场景下的渲染性能
4. **API 简化**: 简化复杂配置，提供更友好的 API
5. **测试覆盖**: 提高单元测试和集成测试覆盖率

详细的重构建议将在后续分析中提供。
