[package]
name = "echarts-rs"
version = "0.1.0"
edition = "2021"

[[example]]
name = "gpui_line_chart"
path = "examples/gpui_line_chart.rs"

[[example]]
name = "chart_rendering_demo"
path = "../../examples/chart_rendering_demo.rs"

[[example]]
name = "basic_charts"
path = "../../examples/basic_charts.rs"

[[example]]
name = "data_visualization_showcase"
path = "../../examples/data_visualization_showcase.rs"

[[example]]
name = "new_chart_types_demo"
path = "../../examples/new_chart_types_demo.rs"

[[example]]
name = "performance_test"
path = "../../examples/performance_test.rs"

[[example]]
name = "charts_integration_test"
path = "examples/charts_integration_test.rs"

[[example]]
name = "chart_builder_demo"
path = "examples/chart_builder_demo.rs"

[[example]]
name = "pie_chart_demo"
path = "examples/pie_chart_demo.rs"

[[example]]
name = "heatmap_demo"
path = "examples/heatmap_demo.rs"

[[example]]
name = "surface3d_demo"
path = "examples/surface3d_demo.rs"

[[example]]
name = "svg_renderer_demo"
path = "examples/svg_renderer_demo.rs"

[[example]]
name = "real_svg_export"
path = "examples/real_svg_export.rs"

[[example]]
name = "advanced_svg_renderer"
path = "examples/advanced_svg_renderer.rs"

[[example]]
name = "svg_renderer_integration_test"
path = "examples/svg_renderer_integration_test.rs"

[[example]]
name = "comprehensive_validation"
path = "examples/comprehensive_validation.rs"

[[example]]
name = "performance_benchmark"
path = "examples/performance_benchmark.rs"

[[example]]
name = "real_svg_charts"
path = "examples/real_svg_charts.rs"

[[example]]
name = "quick_complete_charts"
path = "examples/quick_complete_charts.rs"

[[example]]
name = "comprehensive_line_charts"
path = "examples/comprehensive_line_charts.rs"

[[example]]
name = "simple_line_charts"
path = "examples/simple_line_charts.rs"

[[example]]
name = "working_line_charts"
path = "examples/working_line_charts.rs"

[[example]]
name = "clean_line_charts"
path = "examples/clean_line_charts.rs"

[[example]]
name = "professional_charts_demo"
path = "examples/professional_charts_demo.rs"

[[example]]
name = "pie_svg_demo"
path = "examples/pie_svg_demo.rs"

[[example]]
name = "heatmap_svg_demo"
path = "examples/heatmap_svg_demo.rs"

[[example]]
name = "radar_svg_demo"
path = "examples/radar_svg_demo.rs"

[[example]]
name = "gauge_svg_demo"
path = "examples/gauge_svg_demo.rs"

[[example]]
name = "treemap_svg_demo"
path = "examples/treemap_svg_demo.rs"

[[example]]
name = "sunburst_svg_demo"
path = "examples/sunburst_svg_demo.rs"

[[example]]
name = "funnel_svg_demo"
path = "examples/funnel_svg_demo.rs"

[[example]]
name = "candlestick_svg_demo"
path = "examples/candlestick_svg_demo.rs"

[[example]]
name = "canvas_renderer_demo"
path = "examples/canvas_renderer_demo.rs"

[[example]]
name = "interaction_demo"
path = "examples/interaction_demo.rs"

[[example]]
name = "comprehensive_pie_demo"
path = "examples/comprehensive_pie_demo.rs"

[[example]]
name = "web_demo_generator"
path = "examples/web_demo_generator.rs"

[[example]]
name = "gpui_desktop_demo"
path = "examples/gpui_desktop_demo.rs"

[[example]]
name = "simple_gpui_demo"
path = "examples/simple_gpui_demo.rs"

[[example]]
name = "echarts_validation_demo"
path = "examples/echarts_validation_demo.rs"

[[example]]
name = "gpui_echarts_demo"
path = "examples/gpui_echarts_demo.rs"

[[example]]
name = "echarts_gpui_integration"
path = "examples/echarts_gpui_integration.rs"

[[example]]
name = "final_integration_test"
path = "examples/final_integration_test.rs"

[dependencies]
echarts-core = { path = "crates/core" }
echarts-charts = { path = "crates/charts" }
echarts-renderer = { path = "crates/renderer" }
echarts-interaction = { path = "crates/interaction" }
# 暂时禁用 components 以专注于 charts 集成测试
# echarts-components = { path = "crates/components" }
echarts-themes = { path = "crates/themes" }
# 暂时禁用 renderer 和 processor 以简化测试
# echarts-renderer = { path = "crates/renderer" }
# echarts-processor = { path = "crates/processor" }
# 暂时禁用可选依赖以解决编译问题
# rust-echarts-svg-renderer = { path = "crates/renderer/svg-renderer", optional = true }
# rust-echarts-image-renderer = { path = "crates/renderer/image-renderer", optional = true }
# rust-echarts-wasm = { path = "crates/wasm", optional = true }
# rust-echarts-data = { path = "crates/data", optional = true }
# rust-echarts-charts-3d = { path = "crates/charts-3d", optional = true }
# rust-echarts-streaming = { path = "crates/streaming", optional = true }
# rust-echarts-plugins = { path = "crates/plugins", optional = true }

# 使用 workspace 依赖
serde.workspace = true
serde_json.workspace = true
thiserror.workspace = true
anyhow.workspace = true
uuid.workspace = true
chrono.workspace = true
indexmap.workspace = true
smallvec.workspace = true
nalgebra.workspace = true

# CLI工具依赖
clap = { version = "4.0", features = ["derive"] }
euclid.workspace = true

# GPUI 相关依赖
gpui.workspace = true
gpui-component.workspace = true
gpui_renderer = { path = "crates/renderer/gpui_renderer" }

# 示例程序需要的额外依赖
eframe = { version = "0.28", optional = true }
egui_plot = { version = "0.28", optional = true }
resvg = { version = "0.37", optional = true }
tiny-skia = { version = "0.11", optional = true }
tokio.workspace = true
futures.workspace = true

[dev-dependencies]
criterion = { version = "0.5", features = ["html_reports"] }
rayon = "1.8"
proptest = "1.0"

# Benchmarks configuration
[[bench]]
name = "performance_benchmarks"
harness = false
# [[bench]]
# name = "chart_rendering"
# harness = false

# [[bench]]
# name = "data_processing"
# harness = false

[features]
default = ["std"]
std = []
# 暂时禁用 renderer 相关的 features
# gpu-acceleration = ["echarts-renderer/gpu"]
# web-export = ["echarts-renderer/web"]
# svg-export = ["echarts-renderer/svg"]
# pdf-export = ["echarts-renderer/pdf"]
examples = ["eframe", "egui_plot", "resvg", "tiny-skia"]

# Core rendering features (暂时禁用)
# gpui-renderer = ["echarts-renderer/gpui"]
# svg-renderer = ["rust-echarts-svg-renderer"]
# image-renderer = ["rust-echarts-image-renderer"]

# Extended functionality
# wasm = ["rust-echarts-wasm"]
# data-processing = ["rust-echarts-data"]
# charts-3d = ["rust-echarts-charts-3d"]
# streaming = ["rust-echarts-streaming"]
# plugins = ["rust-echarts-plugins"]

# Feature combinations (暂时禁用)
# basic = ["gpui-renderer"]
# extended = ["gpui-renderer", "svg-renderer", "image-renderer", "data-processing"]
# full = ["gpui-renderer", "svg-renderer", "image-renderer", "wasm", "data-processing", "charts-3d", "streaming", "plugins"]

# Profile configurations are handled by the workspace root


