//! 数据优化模块
//!
//! 提供高性能的数据点优化算法，用于处理大数据集的可视化
//! 包括 LTTB（Largest Triangle Three Buckets）采样算法和路径优化

use crate::{ChartError, DataPoint, DataSet, DataValue, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::Instant;

/// 数据优化配置
#[derive(Debug, Clone)]
pub struct OptimizationConfig {
    /// 目标数据点数量
    pub target_points: usize,
    /// 最小数据点数量阈值，低于此值不进行优化
    pub min_threshold: usize,
    /// 优化算法类型
    pub algorithm: OptimizationAlgorithm,
    /// 是否启用像素级优化
    pub enable_pixel_optimization: bool,
    /// 显示区域宽度（用于像素级优化）
    pub display_width: Option<f64>,
    /// 显示区域高度（用于像素级优化）
    pub display_height: Option<f64>,
    /// 是否保留极值点
    pub preserve_extremes: bool,
    /// 是否启用性能监控
    pub enable_performance_monitoring: bool,
}

impl Default for OptimizationConfig {
    fn default() -> Self {
        Self {
            target_points: 2000,
            min_threshold: 5000,
            algorithm: OptimizationAlgorithm::LTTB,
            enable_pixel_optimization: true,
            display_width: None,
            display_height: None,
            preserve_extremes: true,
            enable_performance_monitoring: false,
        }
    }
}

/// 优化算法类型 (统一版本 - 集成所有算法)
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum OptimizationAlgorithm {
    /// LTTB（Largest Triangle Three Buckets）算法
    LTTB,
    /// 均匀采样
    Uniform,
    /// 极值采样
    MinMax,
    /// 自适应采样
    Adaptive,
    /// 像素级优化
    PixelBased,

    // 新增算法 (从 line.rs 迁移)
    /// Douglas-Peucker 路径简化算法
    DouglasPeucker { epsilon: f64 },

    // 未来扩展算法
    /// Visvalingam-Whyatt 算法
    Visvalingam { min_area: f64 },
    /// Ramer-Douglas-Peucker 算法
    RamerDouglasPeucker { epsilon: f64 },
}

/// 数据优化器
pub struct DataOptimizer {
    config: OptimizationConfig,
    stats: OptimizationStats,
}

/// 优化统计信息
#[derive(Debug, Clone, Default)]
pub struct OptimizationStats {
    /// 优化次数
    pub optimization_count: u64,
    /// 原始数据点总数
    pub original_points_total: u64,
    /// 优化后数据点总数
    pub optimized_points_total: u64,
    /// 总优化时间（毫秒）
    pub total_optimization_time_ms: f64,
    /// 平均优化时间（毫秒）
    pub avg_optimization_time_ms: f64,
    /// 数据压缩比
    pub compression_ratio: f64,
    /// 最后优化时间
    pub last_optimization_time: Option<Instant>,
}

impl DataOptimizer {
    /// 创建新的数据优化器
    pub fn new(config: OptimizationConfig) -> Self {
        Self {
            config,
            stats: OptimizationStats::default(),
        }
    }

    /// 使用默认配置创建优化器
    pub fn default() -> Self {
        Self::new(OptimizationConfig::default())
    }

    /// 优化数据集
    pub fn optimize_dataset(&mut self, dataset: &DataSet) -> Result<DataSet> {
        let start_time = Instant::now();

        if dataset.len() < self.config.min_threshold {
            return Ok(dataset.clone());
        }

        let optimized_points = match &self.config.algorithm {
            OptimizationAlgorithm::LTTB => self.optimize_with_lttb(dataset)?,
            OptimizationAlgorithm::Uniform => self.optimize_with_uniform(dataset)?,
            OptimizationAlgorithm::MinMax => self.optimize_with_minmax(dataset)?,
            OptimizationAlgorithm::Adaptive => self.optimize_with_adaptive(dataset)?,
            OptimizationAlgorithm::PixelBased => self.optimize_with_pixel_based(dataset)?,
            OptimizationAlgorithm::DouglasPeucker { epsilon } => self.optimize_with_douglas_peucker(dataset, *epsilon)?,
            OptimizationAlgorithm::Visvalingam { min_area } => self.optimize_with_visvalingam(dataset, *min_area)?,
            OptimizationAlgorithm::RamerDouglasPeucker { epsilon } => self.optimize_with_ramer_douglas_peucker(dataset, *epsilon)?,
        };

        // 更新统计信息
        if self.config.enable_performance_monitoring {
            self.update_stats(dataset.len(), optimized_points.len(), start_time.elapsed());
        }

        // 创建优化后的数据集
        let mut optimized_dataset = DataSet::with_dimensions(dataset.dimensions().clone());
        optimized_dataset = optimized_dataset.add_points(optimized_points);

        Ok(optimized_dataset)
    }

    /// 优化数值数据（专门用于处理 f64 类型）
    pub fn optimize_data_f64(&mut self, data: &[f64], target_count: usize) -> Vec<f64> {
        if data.len() <= target_count || target_count < 3 {
            return data.to_vec();
        }

        match &self.config.algorithm {
            OptimizationAlgorithm::LTTB => self.lttb_downsample_f64(data, target_count),
            OptimizationAlgorithm::Uniform => self.uniform_downsample_f64(data, target_count),
            OptimizationAlgorithm::MinMax => self.minmax_downsample_f64(data, target_count),
            OptimizationAlgorithm::DouglasPeucker { epsilon } => {
                // 对于1D数据，将其转换为2D进行Douglas-Peucker处理
                let points_2d: Vec<(f64, f64)> = data.iter().enumerate()
                    .map(|(i, &y)| (i as f64, y)).collect();
                let optimized_2d = self.douglas_peucker_2d(&points_2d, *epsilon);
                optimized_2d.into_iter().map(|(_, y)| y).collect()
            }
            _ => self.lttb_downsample_f64(data, target_count), // 默认使用 LTTB
        }
    }

    /// 优化像素数据（专门用于处理像素坐标）
    pub fn optimize_data_pixels(
        &mut self,
        data: &[(f64, f64)],
        target_count: usize,
    ) -> Vec<(f64, f64)> {
        if data.len() <= target_count || target_count < 3 {
            return data.to_vec();
        }

        // 如果启用像素级优化，考虑显示区域
        if self.config.enable_pixel_optimization {
            if let (Some(width), Some(height)) =
                (self.config.display_width, self.config.display_height)
            {
                return self.pixel_based_optimization(data, target_count, width, height);
            }
        }

        // 提取 Y 值进行 LTTB 优化
        let y_values: Vec<f64> = data.iter().map(|(_, y)| *y).collect();
        let selected_indices = self.lttb_downsample_indices(&y_values, target_count);

        selected_indices.into_iter().map(|idx| data[idx]).collect()
    }

    /// LTTB 算法优化数据集
    fn optimize_with_lttb(&self, dataset: &DataSet) -> Result<Vec<DataPoint>> {
        if dataset.dimensions().len() < 2 {
            return Err(ChartError::validation("LTTB 算法需要至少 2 个维度的数据"));
        }

        let points = dataset.points();
        if points.is_empty() {
            return Ok(vec![]);
        }

        // 提取 Y 值（假设第二个维度是 Y 值）
        let y_values: Vec<f64> = points
            .iter()
            .map(|point| {
                if point.values.len() >= 2 {
                    match &point.values[1] {
                        DataValue::Number(n) => *n,
                        _ => 0.0,
                    }
                } else {
                    0.0
                }
            })
            .collect();

        let selected_indices = self.lttb_downsample_indices(&y_values, self.config.target_points);
        Ok(selected_indices
            .into_iter()
            .map(|idx| points[idx].clone())
            .collect())
    }

    /// 均匀采样优化
    fn optimize_with_uniform(&self, dataset: &DataSet) -> Result<Vec<DataPoint>> {
        let points = dataset.points();
        let step = points.len() as f64 / self.config.target_points as f64;

        let mut result = Vec::with_capacity(self.config.target_points);
        for i in 0..self.config.target_points {
            let idx = (i as f64 * step) as usize;
            if idx < points.len() {
                result.push(points[idx].clone());
            }
        }

        Ok(result)
    }

    /// 极值采样优化
    fn optimize_with_minmax(&self, dataset: &DataSet) -> Result<Vec<DataPoint>> {
        let points = dataset.points();
        if points.len() <= self.config.target_points {
            return Ok(points.to_vec());
        }

        let bucket_size = points.len() / (self.config.target_points / 2);
        let mut result = Vec::new();

        for chunk in points.chunks(bucket_size) {
            if chunk.is_empty() {
                continue;
            }

            // 找到最大值和最小值点
            let mut min_point = &chunk[0];
            let mut max_point = &chunk[0];
            let mut min_val = f64::INFINITY;
            let mut max_val = f64::NEG_INFINITY;

            for point in chunk {
                if point.values.len() >= 2 {
                    if let DataValue::Number(val) = &point.values[1] {
                        if *val < min_val {
                            min_val = *val;
                            min_point = point;
                        }
                        if *val > max_val {
                            max_val = *val;
                            max_point = point;
                        }
                    }
                }
            }

            if min_val != max_val {
                result.push(min_point.clone());
                result.push(max_point.clone());
            } else {
                result.push(min_point.clone());
            }
        }

        Ok(result)
    }

    /// 自适应采样优化
    fn optimize_with_adaptive(&self, dataset: &DataSet) -> Result<Vec<DataPoint>> {
        // 根据数据变化率自适应选择采样密度
        let points = dataset.points();
        if points.len() <= self.config.target_points {
            return Ok(points.to_vec());
        }

        // 计算数据变化率
        let mut change_rates = Vec::new();
        for i in 1..points.len() {
            if points[i].values.len() >= 2 && points[i - 1].values.len() >= 2 {
                if let (DataValue::Number(curr), DataValue::Number(prev)) =
                    (&points[i].values[1], &points[i - 1].values[1])
                {
                    change_rates.push((curr - prev).abs());
                } else {
                    change_rates.push(0.0);
                }
            } else {
                change_rates.push(0.0);
            }
        }

        // 根据变化率选择点
        let mut result = Vec::new();
        result.push(points[0].clone()); // 始终包含第一个点

        let threshold = change_rates.iter().sum::<f64>() / change_rates.len() as f64;

        for (i, &rate) in change_rates.iter().enumerate() {
            if rate > threshold && result.len() < self.config.target_points - 1 {
                result.push(points[i + 1].clone());
            }
        }

        // 如果点数不够，用均匀采样补充
        if result.len() < self.config.target_points {
            let remaining = self.config.target_points - result.len();
            let step = points.len() as f64 / remaining as f64;

            for i in 1..remaining {
                let idx = (i as f64 * step) as usize;
                if idx < points.len() && !result.iter().any(|p| std::ptr::eq(p, &points[idx])) {
                    result.push(points[idx].clone());
                }
            }
        }

        result.push(points[points.len() - 1].clone()); // 始终包含最后一个点
        Ok(result)
    }

    /// 像素级优化
    fn optimize_with_pixel_based(&self, dataset: &DataSet) -> Result<Vec<DataPoint>> {
        let points = dataset.points();
        if points.len() <= self.config.target_points {
            return Ok(points.to_vec());
        }

        // 基于像素密度进行优化
        let width = self.config.display_width.unwrap_or(800.0);
        let pixel_density = points.len() as f64 / width;

        if pixel_density <= 2.0 {
            return Ok(points.to_vec());
        }

        // 使用 LTTB 算法，但目标点数基于像素密度
        let _target_points = (width * 1.5) as usize;
        self.optimize_with_lttb(dataset)
    }

    /// LTTB 下采样算法实现
    fn lttb_downsample_indices(&self, data: &[f64], target_count: usize) -> Vec<usize> {
        if data.len() <= target_count || target_count < 3 {
            return (0..data.len()).collect();
        }

        let mut result = Vec::with_capacity(target_count);
        result.push(0); // 始终包含第一个点

        let bucket_size = (data.len() - 2) as f64 / (target_count - 2) as f64;
        let mut a = 0; // 当前选中的点索引

        for i in 0..(target_count - 2) {
            // 计算下一个桶的平均点
            let avg_range_start = ((i + 1) as f64 * bucket_size + 1.0).floor() as usize;
            let avg_range_end = ((i + 2) as f64 * bucket_size + 1.0).floor() as usize;
            let avg_range_end = avg_range_end.min(data.len());

            let (avg_x, avg_y) = if avg_range_end > avg_range_start {
                let mut sum_x = 0.0;
                let mut sum_y = 0.0;
                let mut count = 0;

                for j in avg_range_start..avg_range_end {
                    sum_x += j as f64;
                    sum_y += data[j];
                    count += 1;
                }

                if count > 0 {
                    (sum_x / count as f64, sum_y / count as f64)
                } else {
                    (avg_range_start as f64, data[avg_range_start])
                }
            } else {
                (avg_range_start as f64, data[avg_range_start])
            };

            // 当前桶的范围
            let range_offs = (i as f64 * bucket_size + 1.0).floor() as usize;
            let range_to = ((i + 1) as f64 * bucket_size + 1.0).floor() as usize;

            // 点A的坐标
            let point_a_x = a as f64;
            let point_a_y = data[a];

            let mut max_area = -1.0;
            let mut next_a = range_offs;

            // 在当前桶中找到形成最大三角形面积的点
            for j in range_offs..range_to.min(data.len()) {
                let area = ((point_a_x - avg_x) * (data[j] - point_a_y)
                    - (point_a_x - j as f64) * (avg_y - point_a_y))
                    .abs();

                if area > max_area {
                    max_area = area;
                    next_a = j;
                }
            }

            result.push(next_a);
            a = next_a;
        }

        result.push(data.len() - 1); // 始终包含最后一个点
        result
    }

    /// 像素级优化算法
    fn pixel_based_optimization(
        &self,
        data: &[(f64, f64)],
        target_count: usize,
        width: f64,
        height: f64,
    ) -> Vec<(f64, f64)> {
        if data.len() <= target_count {
            return data.to_vec();
        }

        // 创建像素网格
        let grid_width = width as usize;
        let grid_height = height as usize;
        let mut pixel_grid: HashMap<(usize, usize), Vec<usize>> = HashMap::new();

        // 将数据点映射到像素网格
        for (i, &(x, y)) in data.iter().enumerate() {
            let pixel_x = (x * grid_width as f64 / width) as usize;
            let pixel_y = (y * grid_height as f64 / height) as usize;

            pixel_grid
                .entry((pixel_x, pixel_y))
                .or_insert_with(Vec::new)
                .push(i);
        }

        // 从每个像素选择代表点
        let mut result = Vec::new();
        for indices in pixel_grid.values() {
            if indices.len() == 1 {
                result.push(data[indices[0]]);
            } else {
                // 选择中位数点作为代表
                let mid_idx = indices[indices.len() / 2];
                result.push(data[mid_idx]);
            }
        }

        // 如果结果点数超过目标，使用 LTTB 进一步优化
        if result.len() > target_count {
            let y_values: Vec<f64> = result.iter().map(|(_, y)| *y).collect();
            let selected_indices = self.lttb_downsample_indices(&y_values, target_count);
            result = selected_indices
                .into_iter()
                .map(|idx| result[idx])
                .collect();
        }

        result
    }

    /// 更新统计信息
    fn update_stats(
        &mut self,
        original_count: usize,
        optimized_count: usize,
        duration: std::time::Duration,
    ) {
        self.stats.optimization_count += 1;
        self.stats.original_points_total += original_count as u64;
        self.stats.optimized_points_total += optimized_count as u64;

        let duration_ms = duration.as_secs_f64() * 1000.0;
        self.stats.total_optimization_time_ms += duration_ms;
        self.stats.avg_optimization_time_ms =
            self.stats.total_optimization_time_ms / self.stats.optimization_count as f64;

        if self.stats.original_points_total > 0 {
            self.stats.compression_ratio =
                self.stats.optimized_points_total as f64 / self.stats.original_points_total as f64;
        }

        self.stats.last_optimization_time = Some(Instant::now());
    }

    /// 获取优化统计信息
    pub fn stats(&self) -> &OptimizationStats {
        &self.stats
    }

    /// 重置统计信息
    pub fn reset_stats(&mut self) {
        self.stats = OptimizationStats::default();
    }

    // 辅助方法实现
    fn lttb_downsample_f64(&self, data: &[f64], target_count: usize) -> Vec<f64> {
        let indices = self.lttb_downsample_indices(data, target_count);
        indices.into_iter().map(|idx| data[idx]).collect()
    }

    fn uniform_downsample_f64(&self, data: &[f64], target_count: usize) -> Vec<f64> {
        let step = data.len() as f64 / target_count as f64;
        (0..target_count)
            .map(|i| {
                let idx = (i as f64 * step) as usize;
                data[idx.min(data.len() - 1)]
            })
            .collect()
    }

    fn minmax_downsample_f64(&self, data: &[f64], target_count: usize) -> Vec<f64> {
        let bucket_size = data.len() / (target_count / 2);
        let mut result = Vec::new();

        for chunk in data.chunks(bucket_size) {
            if let (Some(&min), Some(&max)) = (
                chunk.iter().min_by(|a, b| a.partial_cmp(b).unwrap()),
                chunk.iter().max_by(|a, b| a.partial_cmp(b).unwrap()),
            ) {
                if min != max {
                    result.push(min);
                    result.push(max);
                } else {
                    result.push(min);
                }
            }
        }

        result
    }

    // ============================================================================
    // 新增算法实现 (从 line.rs 迁移并扩展)
    // ============================================================================

    /// Douglas-Peucker 路径简化算法
    fn optimize_with_douglas_peucker(&self, dataset: &DataSet, epsilon: f64) -> Result<Vec<DataPoint>> {
        let points = dataset.points();
        if points.len() <= 2 {
            return Ok(points.to_vec());
        }

        // 提取2D坐标点
        let mut coords = Vec::new();
        for point in points {
            if point.values.len() >= 2 {
                let x = match &point.values[0] {
                    DataValue::Number(n) => *n,
                    _ => 0.0,
                };
                let y = match &point.values[1] {
                    DataValue::Number(n) => *n,
                    _ => 0.0,
                };
                coords.push((x, y));
            }
        }

        // 应用 Douglas-Peucker 算法
        let simplified_coords = self.douglas_peucker_2d(&coords, epsilon);

        // 根据简化后的坐标找回原始数据点
        let mut result = Vec::new();
        for &(x, y) in &simplified_coords {
            // 找到最接近的原始点
            if let Some(point) = points.iter().find(|p| {
                if p.values.len() >= 2 {
                    if let (DataValue::Number(px), DataValue::Number(py)) = (&p.values[0], &p.values[1]) {
                        (px - x).abs() < 1e-10 && (py - y).abs() < 1e-10
                    } else {
                        false
                    }
                } else {
                    false
                }
            }) {
                result.push(point.clone());
            }
        }

        Ok(result)
    }

    /// Visvalingam-Whyatt 算法 (基于面积的简化)
    fn optimize_with_visvalingam(&self, dataset: &DataSet, min_area: f64) -> Result<Vec<DataPoint>> {
        let points = dataset.points();
        if points.len() <= 3 {
            return Ok(points.to_vec());
        }

        // 提取2D坐标点
        let mut coords = Vec::new();
        for point in points {
            if point.values.len() >= 2 {
                let x = match &point.values[0] {
                    DataValue::Number(n) => *n,
                    _ => 0.0,
                };
                let y = match &point.values[1] {
                    DataValue::Number(n) => *n,
                    _ => 0.0,
                };
                coords.push((x, y));
            }
        }

        // 应用 Visvalingam 算法
        let simplified_coords = self.visvalingam_simplify(&coords, min_area);

        // 根据简化后的坐标找回原始数据点
        let mut result = Vec::new();
        for &(x, y) in &simplified_coords {
            if let Some(point) = points.iter().find(|p| {
                if p.values.len() >= 2 {
                    if let (DataValue::Number(px), DataValue::Number(py)) = (&p.values[0], &p.values[1]) {
                        (px - x).abs() < 1e-10 && (py - y).abs() < 1e-10
                    } else {
                        false
                    }
                } else {
                    false
                }
            }) {
                result.push(point.clone());
            }
        }

        Ok(result)
    }

    /// Ramer-Douglas-Peucker 算法 (改进版)
    fn optimize_with_ramer_douglas_peucker(&self, dataset: &DataSet, epsilon: f64) -> Result<Vec<DataPoint>> {
        // 目前使用标准 Douglas-Peucker 实现，未来可以添加改进
        self.optimize_with_douglas_peucker(dataset, epsilon)
    }

    /// Douglas-Peucker 2D 算法核心实现
    fn douglas_peucker_2d(&self, points: &[(f64, f64)], epsilon: f64) -> Vec<(f64, f64)> {
        if points.len() <= 2 {
            return points.to_vec();
        }

        let mut result = Vec::new();
        self.douglas_peucker_recursive(points, 0, points.len() - 1, epsilon, &mut result);

        // 确保结果按索引排序
        result.sort_by(|a, b| {
            points.iter().position(|p| p == a).unwrap()
                .cmp(&points.iter().position(|p| p == b).unwrap())
        });

        result
    }

    /// Douglas-Peucker 递归实现
    fn douglas_peucker_recursive(
        &self,
        points: &[(f64, f64)],
        start: usize,
        end: usize,
        epsilon: f64,
        result: &mut Vec<(f64, f64)>,
    ) {
        if end <= start + 1 {
            result.push(points[start]);
            if end > start {
                result.push(points[end]);
            }
            return;
        }

        let mut max_distance = 0.0;
        let mut max_index = start;

        // 找到距离起点和终点连线最远的点
        for i in (start + 1)..end {
            let distance = self.point_to_line_distance(
                points[i],
                points[start],
                points[end],
            );
            if distance > max_distance {
                max_distance = distance;
                max_index = i;
            }
        }

        // 如果最大距离大于阈值，递归处理
        if max_distance > epsilon {
            self.douglas_peucker_recursive(points, start, max_index, epsilon, result);
            self.douglas_peucker_recursive(points, max_index, end, epsilon, result);
        } else {
            // 否则只保留起点和终点
            result.push(points[start]);
            result.push(points[end]);
        }
    }

    /// 计算点到直线的距离
    fn point_to_line_distance(&self, point: (f64, f64), line_start: (f64, f64), line_end: (f64, f64)) -> f64 {
        let (px, py) = point;
        let (x1, y1) = line_start;
        let (x2, y2) = line_end;

        let a = y2 - y1;
        let b = x1 - x2;
        let c = x2 * y1 - x1 * y2;

        let denominator = (a * a + b * b).sqrt();
        if denominator == 0.0 {
            // 起点和终点重合，返回点到起点的距离
            ((px - x1) * (px - x1) + (py - y1) * (py - y1)).sqrt()
        } else {
            (a * px + b * py + c).abs() / denominator
        }
    }

    /// Visvalingam 简化算法实现
    fn visvalingam_simplify(&self, points: &[(f64, f64)], min_area: f64) -> Vec<(f64, f64)> {
        if points.len() <= 3 {
            return points.to_vec();
        }

        let mut simplified = points.to_vec();

        loop {
            if simplified.len() <= 3 {
                break;
            }

            let mut min_area_found = f64::INFINITY;
            let mut min_index = 0;

            // 找到形成最小三角形面积的点
            for i in 1..(simplified.len() - 1) {
                let area = self.triangle_area(
                    simplified[i - 1],
                    simplified[i],
                    simplified[i + 1],
                );
                if area < min_area_found {
                    min_area_found = area;
                    min_index = i;
                }
            }

            // 如果最小面积大于阈值，停止简化
            if min_area_found > min_area {
                break;
            }

            // 移除形成最小面积的点
            simplified.remove(min_index);
        }

        simplified
    }

    /// 计算三角形面积
    fn triangle_area(&self, p1: (f64, f64), p2: (f64, f64), p3: (f64, f64)) -> f64 {
        let (x1, y1) = p1;
        let (x2, y2) = p2;
        let (x3, y3) = p3;

        0.5 * ((x1 * (y2 - y3) + x2 * (y3 - y1) + x3 * (y1 - y2)).abs())
    }
}
