//! 测试在没有 GPUI feature 的情况下 ECharts 核心功能是否正常工作
//!
//! 这个示例验证 echarts-core 在不依赖 GPUI 的情况下仍然可以正常使用

use echarts_core::{Bounds, Point, Size, Color};

fn main() {
    println!("🧪 测试 ECharts 核心功能（无 GPUI 依赖）");
    println!("=" .repeat(50));

    // 1. 测试基础几何类型
    println!("\n📐 1. 测试基础几何类型");
    test_geometry_types();

    // 2. 测试颜色系统
    println!("\n🎨 2. 测试颜色系统");
    test_color_system();

    // 3. 测试边界计算
    println!("\n📊 3. 测试边界计算");
    test_bounds_calculations();

    // 4. 验证没有 GPUI 相关代码
    println!("\n🔍 4. 验证 GPUI 集成状态");
    verify_gpui_integration_status();

    println!("\n✅ 所有测试通过！ECharts 核心功能在没有 GPUI 的情况下正常工作。");
}

fn test_geometry_types() {
    // 测试 Point
    let point = Point::new(10.0, 20.0);
    println!("  Point: {:?}", point);
    assert_eq!(point.x, 10.0);
    assert_eq!(point.y, 20.0);

    // 测试 Size
    let size = Size::new(100.0, 200.0);
    println!("  Size: {:?}", size);
    assert_eq!(size.width, 100.0);
    assert_eq!(size.height, 200.0);
    assert_eq!(size.area(), 20000.0);

    // 测试 Bounds
    let bounds = Bounds::new(5.0, 10.0, 300.0, 400.0);
    println!("  Bounds: {:?}", bounds);
    assert_eq!(bounds.origin.x, 5.0);
    assert_eq!(bounds.origin.y, 10.0);
    assert_eq!(bounds.size.width, 300.0);
    assert_eq!(bounds.size.height, 400.0);

    println!("  ✅ 几何类型测试通过");
}

fn test_color_system() {
    // 测试 RGB 颜色
    let red = Color::rgb(1.0, 0.0, 0.0);
    println!("  Red RGB: {:?}", red);
    assert_eq!(red.r, 1.0);
    assert_eq!(red.g, 0.0);
    assert_eq!(red.b, 0.0);
    assert_eq!(red.a, 1.0);

    // 测试 RGBA 颜色
    let transparent_blue = Color::rgba(0.0, 0.0, 1.0, 0.5);
    println!("  Transparent Blue: {:?}", transparent_blue);
    assert_eq!(transparent_blue.a, 0.5);

    // 测试预定义颜色
    let white = Color::WHITE;
    let black = Color::BLACK;
    println!("  White: {:?}", white);
    println!("  Black: {:?}", black);

    // 测试颜色转换
    let hex = red.to_hex();
    println!("  Red as hex: {}", hex);

    println!("  ✅ 颜色系统测试通过");
}

fn test_bounds_calculations() {
    let bounds1 = Bounds::new(0.0, 0.0, 100.0, 100.0);
    let bounds2 = Bounds::new(50.0, 50.0, 100.0, 100.0);

    // 测试中心点计算
    let center = bounds1.center();
    println!("  Bounds1 center: {:?}", center);
    assert_eq!(center.x, 50.0);
    assert_eq!(center.y, 50.0);

    // 测试包含检测
    let point_inside = Point::new(25.0, 25.0);
    let point_outside = Point::new(150.0, 150.0);
    assert!(bounds1.contains_point(point_inside));
    assert!(!bounds1.contains_point(point_outside));
    println!("  ✅ 点包含检测正确");

    // 测试相交检测
    assert!(bounds1.intersects(bounds2));
    println!("  ✅ 边界相交检测正确");

    // 测试联合计算
    let union = bounds1.union(bounds2);
    println!("  Union bounds: {:?}", union);
    assert_eq!(union.origin.x, 0.0);
    assert_eq!(union.origin.y, 0.0);
    assert_eq!(union.size.width, 150.0);
    assert_eq!(union.size.height, 150.0);

    // 测试交集计算
    let intersection = bounds1.intersect(bounds2);
    println!("  Intersection bounds: {:?}", intersection);
    assert_eq!(intersection.origin.x, 50.0);
    assert_eq!(intersection.origin.y, 50.0);
    assert_eq!(intersection.size.width, 50.0);
    assert_eq!(intersection.size.height, 50.0);

    println!("  ✅ 边界计算测试通过");
}

fn verify_gpui_integration_status() {
    // 在编译时检查 GPUI feature 是否启用
    #[cfg(feature = "gpui")]
    {
        println!("  ⚠️  GPUI feature 已启用 - 这不应该在此测试中发生");
        println!("  请使用 `cargo run --example no_gpui_test` 运行此测试");
    }

    #[cfg(not(feature = "gpui"))]
    {
        println!("  ✅ GPUI feature 未启用 - 正确！");
        println!("  📝 ECharts 核心功能完全独立于 GPUI");
        
        // 验证 GPUI 相关的转换函数不存在
        // 这些代码在没有 gpui feature 时不会编译
        println!("  📝 GPUI 转换函数在此环境中不可用（这是预期的）");
    }

    println!("  ✅ GPUI 集成状态验证完成");
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_core_functionality_without_gpui() {
        // 确保核心功能在没有 GPUI 的情况下正常工作
        let bounds = Bounds::new(0.0, 0.0, 100.0, 100.0);
        let center = bounds.center();
        assert_eq!(center.x, 50.0);
        assert_eq!(center.y, 50.0);

        let color = Color::rgb(1.0, 0.0, 0.0);
        assert_eq!(color.r, 1.0);
    }

    #[test]
    #[cfg(not(feature = "gpui"))]
    fn test_gpui_feature_disabled() {
        // 这个测试只在没有 gpui feature 时运行
        // 验证 GPUI 相关代码确实不存在
        println!("GPUI feature is correctly disabled");
    }
}
