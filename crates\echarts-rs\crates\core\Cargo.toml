[package]
name = "echarts-core"
version = "0.1.0"
edition = "2021"
description = "Core data structures and traits for Rust ECharts"
license = "Apache-2.0"

[dependencies]
serde = { workspace = true }
serde_json = { workspace = true }
thiserror = { workspace = true }
anyhow = { workspace = true }
nalgebra = { workspace = true }
euclid = { workspace = true }
uuid = { workspace = true }
chrono = { workspace = true, features = ["serde"] }
indexmap = { workspace = true }
smallvec = { workspace = true }


[[example]]
name = "render_context_demo"
path = "examples/render_context_demo.rs"

[features]
default = ["std"]
std = []
