//! 工作版轴标签格式化演示
//!
//! 使用当前依赖版本展示轴标签格式化功能

use echarts_rs::{LineSeries, Color, CartesianCoordinateSystem, Bounds as EchartsBounds};
use echarts_charts::line::LabelFormatType;
use gpui::*;
use gpui_component::StyledExt;

fn main() {
    println!("🚀 启动工作版轴标签格式化演示...");

    let app = Application::new();
    app.run(move |cx| {
        println!("📱 应用程序上下文已创建");

        let mut window_size = size(px(1200.0), px(800.0));
        if let Some(display) = cx.primary_display() {
            let display_size = display.bounds().size;
            window_size.width = window_size.width.min(display_size.width * 0.9);
            window_size.height = window_size.height.min(display_size.height * 0.9);
            println!("🖥️  显示器大小: {:?}, 窗口大小: {:?}", display_size, window_size);
        }
        let window_bounds = gpui::Bounds::centered(None, window_size, cx);

        cx.spawn(async move |cx| {
            let options = WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(window_bounds)),
                titlebar: Some(TitlebarOptions {
                    title: Some("📊 轴标签格式化演示 - ECharts-rs".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                window_min_size: Some(size(px(800.0), px(600.0))),
                window_background: WindowBackgroundAppearance::Opaque,
                window_decorations: None,
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                app_id: Some("axis-labels-demo".to_string()),
            };

            cx.update(|cx| {
                cx.open_window(options, |window, cx| {
                    println!("✅ 窗口已创建，正在初始化轴标签演示...");
                    cx.new(|_cx| AxisLabelsDemo::new())
                })
                .expect("无法创建窗口");
            })
            .ok();
        })
        .detach();
    });
}

/// 轴标签演示应用
struct AxisLabelsDemo {
    examples: Vec<LabelExample>,
    current_example: usize,
}

/// 标签示例
struct LabelExample {
    title: String,
    description: String,
    series: LineSeries,
}

impl AxisLabelsDemo {
    fn new() -> Self {
        println!("🎯 初始化轴标签演示...");

        let examples = vec![
            // 示例1：基本小数位数设置
            LabelExample {
                title: "基本小数位数设置".to_string(),
                description: "X轴显示2位小数，Y轴显示1位小数".to_string(),
                series: LineSeries::new("销售数据")
                    .data(vec![
                        (1.234, 20.5), (2.567, 35.8), (3.891, 25.2), 
                        (4.123, 60.7), (5.456, 45.3), (6.789, 80.9)
                    ])
                    .color(Color::rgb(0.2, 0.6, 1.0))
                    .x_axis_decimal_places(2)  // X轴2位小数
                    .y_axis_decimal_places(1)  // Y轴1位小数
                    .line_width(3.0)
                    .show_symbols(true),
            },

            // 示例2：科学计数法
            LabelExample {
                title: "科学计数法格式".to_string(),
                description: "大数值使用科学计数法显示".to_string(),
                series: LineSeries::new_scientific("大数据", 2)
                    .data(vec![
                        (1000.0, 1234567.0), (2000.0, 2345678.0), 
                        (3000.0, 1876543.0), (4000.0, 3456789.0),
                        (5000.0, 2987654.0), (6000.0, 4123456.0)
                    ])
                    .color(Color::rgb(1.0, 0.4, 0.2))
                    .line_width(2.5),
            },

            // 示例3：百分比格式
            LabelExample {
                title: "百分比格式".to_string(),
                description: "Y轴显示为百分比格式".to_string(),
                series: LineSeries::new_percentage("转化率", 1)
                    .data(vec![
                        (1.0, 0.234), (2.0, 0.345), (3.0, 0.287), 
                        (4.0, 0.456), (5.0, 0.398), (6.0, 0.512)
                    ])
                    .color(Color::rgb(0.2, 0.8, 0.4))
                    .x_axis_decimal_places(0)  // X轴整数
                    .line_width(2.0)
                    .area(true)
                    .area_opacity(0.3),
            },

            // 示例4：整数显示
            LabelExample {
                title: "整数显示".to_string(),
                description: "X轴和Y轴都显示为整数".to_string(),
                series: LineSeries::new("整数数据")
                    .data(vec![
                        (10.0, 150.0), (20.0, 280.0), (30.0, 220.0),
                        (40.0, 380.0), (50.0, 320.0), (60.0, 450.0)
                    ])
                    .color(Color::rgb(0.8, 0.2, 0.6))
                    .x_axis_decimal_places(0)  // X轴整数
                    .y_axis_decimal_places(0)  // Y轴整数
                    .line_width(2.5)
                    .smooth(true)
                    .smoothness(0.4),
            },

            // 示例5：高精度显示
            LabelExample {
                title: "高精度显示".to_string(),
                description: "Y轴显示3位小数的高精度数据".to_string(),
                series: LineSeries::new("高精度数据")
                    .data(vec![
                        (1.0, 25.678), (2.0, 45.123), (3.0, 35.456),
                        (4.0, 65.789), (5.0, 55.234), (6.0, 75.567)
                    ])
                    .color(Color::rgb(0.9, 0.5, 0.1))
                    .x_axis_decimal_places(0)  // X轴整数
                    .y_axis_decimal_places(3)  // Y轴3位小数
                    .line_width(2.0)
                    .symbol_size(8.0),
            },
        ];

        println!("📊 创建了 {} 个轴标签示例", examples.len());

        Self {
            examples,
            current_example: 0,
        }
    }

    /// 切换到下一个示例
    fn next_example(&mut self) {
        self.current_example = (self.current_example + 1) % self.examples.len();
        println!("🔄 切换到示例: {}", self.get_current_example().title);
    }

    /// 切换到上一个示例
    fn prev_example(&mut self) {
        self.current_example = if self.current_example == 0 {
            self.examples.len() - 1
        } else {
            self.current_example - 1
        };
        println!("🔄 切换到示例: {}", self.get_current_example().title);
    }

    /// 获取当前示例
    fn get_current_example(&self) -> &LabelExample {
        &self.examples[self.current_example]
    }
}

impl Render for AxisLabelsDemo {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("🎨 渲染轴标签演示界面...");

        let current_example = self.get_current_example();

        div()
            .size_full()
            .bg(rgb(0xf8fafc))
            .flex()
            .flex_col()
            .child(
                // 标题栏
                div()
                    .w_full()
                    .h(px(80.0))
                    .bg(rgb(0x1f2937))
                    .flex()
                    .items_center()
                    .justify_center()
                    .child(
                        div()
                            .text_2xl()
                            .font_bold()
                            .text_color(rgb(0xffffff))
                            .child("📊 轴标签格式化功能演示")
                    )
            )
            .child(
                // 说明文字
                div()
                    .w_full()
                    .p_4()
                    .bg(rgb(0xe5e7eb))
                    .flex()
                    .justify_center()
                    .child(
                        div()
                            .text_sm()
                            .text_color(rgb(0x374151))
                            .child("✨ 展示如何设置XY轴标签的小数位数、科学计数法、百分比格式等功能")
                    )
            )
            .child(
                // 当前示例信息
                div()
                    .w_full()
                    .p_6()
                    .bg(rgb(0xf3f4f6))
                    .child(
                        div()
                            .max_w(px(1000.0))
                            .mx_auto()
                            .flex()
                            .justify_between()
                            .items_center()
                            .child(
                                div()
                                    .flex()
                                    .flex_col()
                                    .child(
                                        div()
                                            .text_xl()
                                            .font_bold()
                                            .text_color(rgb(0x1f2937))
                                            .mb_2()
                                            .child(format!("{}. {}", self.current_example + 1, current_example.title))
                                    )
                                    .child(
                                        div()
                                            .text_base()
                                            .text_color(rgb(0x6b7280))
                                            .child(current_example.description.clone())
                                    )
                            )
                            .child(
                                div()
                                    .flex()
                                    .gap_2()
                                    .child(
                                        div()
                                            .px_4()
                                            .py_2()
                                            .bg(rgb(0x3b82f6))
                                            .text_color(rgb(0xffffff))
                                            .rounded_md()
                                            .text_sm()
                                            .cursor_pointer()
                                            .hover(|div| div.bg(rgb(0x2563eb)))
                                            .child("⏮️ 上一个")
                                    )
                                    .child(
                                        div()
                                            .px_4()
                                            .py_2()
                                            .bg(rgb(0x3b82f6))
                                            .text_color(rgb(0xffffff))
                                            .rounded_md()
                                            .text_sm()
                                            .cursor_pointer()
                                            .hover(|div| div.bg(rgb(0x2563eb)))
                                            .child("⏭️ 下一个")
                                    )
                            )
                    )
            )
            .child(
                // 主图表区域
                div()
                    .flex_1()
                    .p_6()
                    .child(
                        div()
                            .w_full()
                            .h_full()
                            .bg(rgb(0xffffff))
                            .border_1()
                            .border_color(rgb(0xd1d5db))
                            .rounded_lg()
                            .p_6()
                            .flex()
                            .items_center()
                            .justify_center()
                            .child(
                                div()
                                    .w(px(800.0))
                                    .h(px(400.0))
                                    .bg(rgb(0xf9fafb))
                                    .border_1()
                                    .border_color(rgb(0xe5e7eb))
                                    .rounded_md()
                                    .flex()
                                    .items_center()
                                    .justify_center()
                                    .child(
                                        div()
                                            .text_center()
                                            .child(
                                                div()
                                                    .text_2xl()
                                                    .mb_4()
                                                    .child("📈")
                                            )
                                            .child(
                                                div()
                                                    .text_xl()
                                                    .font_semibold()
                                                    .text_color(rgb(0x1f2937))
                                                    .mb_2()
                                                    .child(current_example.title.clone())
                                            )
                                            .child(
                                                div()
                                                    .text_sm()
                                                    .text_color(rgb(0x6b7280))
                                                    .child("🎨 GPUI Canvas 轴标签渲染区域")
                                            )
                                    )
                            )
                    )
            )
            .child(
                // 状态栏
                div()
                    .w_full()
                    .h(px(40.0))
                    .bg(rgb(0x374151))
                    .flex()
                    .items_center()
                    .justify_between()
                    .px_6()
                    .child(
                        div()
                            .text_xs()
                            .text_color(rgb(0x9ca3af))
                            .child(format!("✅ {} 已就绪 | X轴: {:?}, Y轴: {:?}", 
                                current_example.title,
                                current_example.series.axis_labels.x_axis_format,
                                current_example.series.axis_labels.y_axis_format
                            ))
                    )
                    .child(
                        div()
                            .text_xs()
                            .text_color(rgb(0x9ca3af))
                            .child(format!("示例 {} / {}", self.current_example + 1, self.examples.len()))
                    )
            )
    }
}
