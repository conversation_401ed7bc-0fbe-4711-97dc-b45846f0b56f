//! Event system for chart interactions

use crate::{DataValue, Point};
use serde::{Deserialize, Serialize};

/// Mouse button types
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub enum MouseButton {
    Left,
    Middle,
    Right,
}

/// Mouse event types
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub enum MouseEventType {
    Click,
    DoubleClick,
    MouseDown,
    MouseUp,
    Mouse<PERSON>ove,
    MouseEnter,
    MouseLeave,
    ContextMenu,
    Wheel,
}

/// Mouse event data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MouseEvent {
    /// Event type
    pub event_type: MouseEventType,

    /// Mouse position
    pub position: Point,

    /// Mouse button (if applicable)
    pub button: Option<MouseButton>,

    /// Whether the Alt key was pressed
    pub alt_key: bool,

    /// Whether the Ctrl key was pressed
    pub ctrl_key: bool,

    /// Whether the Shift key was pressed
    pub shift_key: bool,

    /// Whether the Meta key was pressed
    pub meta_key: bool,

    /// Wheel delta (if applicable)
    pub delta: Option<f64>,
}

impl MouseEvent {
    /// Create a new mouse event
    pub fn new(event_type: MouseEventType, position: Point) -> Self {
        Self {
            event_type,
            position,
            button: None,
            alt_key: false,
            ctrl_key: false,
            shift_key: false,
            meta_key: false,
            delta: None,
        }
    }

    /// Set the mouse button
    pub fn with_button(mut self, button: MouseButton) -> Self {
        self.button = Some(button);
        self
    }

    /// Set the wheel delta
    pub fn with_delta(mut self, delta: f64) -> Self {
        self.delta = Some(delta);
        self
    }

    /// Set modifier keys
    pub fn with_modifiers(mut self, alt: bool, ctrl: bool, shift: bool, meta: bool) -> Self {
        self.alt_key = alt;
        self.ctrl_key = ctrl;
        self.shift_key = shift;
        self.meta_key = meta;
        self
    }
}

/// Key event types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum KeyEventType {
    KeyDown,
    KeyUp,
    KeyPress,
}

/// Key event data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeyEvent {
    /// Event type
    pub event_type: KeyEventType,

    /// Key code
    pub key: String,

    /// Whether the Alt key was pressed
    pub alt_key: bool,

    /// Whether the Ctrl key was pressed
    pub ctrl_key: bool,

    /// Whether the Shift key was pressed
    pub shift_key: bool,

    /// Whether the Meta key was pressed
    pub meta_key: bool,
}

impl KeyEvent {
    /// Create a new key event
    pub fn new(event_type: KeyEventType, key: String) -> Self {
        Self {
            event_type,
            key,
            alt_key: false,
            ctrl_key: false,
            shift_key: false,
            meta_key: false,
        }
    }

    /// Set modifier keys
    pub fn with_modifiers(mut self, alt: bool, ctrl: bool, shift: bool, meta: bool) -> Self {
        self.alt_key = alt;
        self.ctrl_key = ctrl;
        self.shift_key = shift;
        self.meta_key = meta;
        self
    }
}

/// Chart interaction event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InteractionEvent {
    /// Mouse event
    Mouse(MouseEvent),

    /// Key event
    Key(KeyEvent),

    /// Data point selection
    Select {
        /// Series index
        series_index: usize,

        /// Data point index
        data_index: usize,

        /// Data point values
        values: Vec<DataValue>,
    },

    /// Data point hover
    Hover {
        /// Series index
        series_index: usize,

        /// Data point index
        data_index: usize,

        /// Data point values
        values: Vec<DataValue>,
    },

    /// Zoom event
    Zoom {
        /// Start point
        start: Point,

        /// End point
        end: Point,
    },

    /// Pan event
    Pan {
        /// Delta x
        delta_x: f64,

        /// Delta y
        delta_y: f64,
    },

    /// Brush selection
    Brush {
        /// Start point
        start: Point,

        /// End point
        end: Point,

        /// Selected data points
        selected: Vec<(usize, usize)>, // (series_index, data_index)
    },

    /// Custom event
    Custom {
        /// Event name
        name: String,

        /// Event data
        data: serde_json::Value,
    },
}

/// Event handler function type
pub type EventHandler = Box<dyn Fn(&InteractionEvent) -> bool + 'static>;

/// Event dispatcher for managing event handlers
#[derive(Default)]
pub struct EventDispatcher {
    /// Event handlers
    handlers: Vec<(String, EventHandler)>,
}

impl EventDispatcher {
    /// Create a new event dispatcher
    pub fn new() -> Self {
        Self {
            handlers: Vec::new(),
        }
    }

    /// Add an event handler
    pub fn add_handler<F>(&mut self, event_type: &str, handler: F)
    where
        F: Fn(&InteractionEvent) -> bool + 'static,
    {
        self.handlers
            .push((event_type.to_string(), Box::new(handler)));
    }

    /// Dispatch an event
    pub fn dispatch(&self, event: &InteractionEvent) -> bool {
        let event_type = match event {
            InteractionEvent::Mouse(mouse) => match mouse.event_type {
                MouseEventType::Click => "click",
                MouseEventType::DoubleClick => "dblclick",
                MouseEventType::MouseDown => "mousedown",
                MouseEventType::MouseUp => "mouseup",
                MouseEventType::MouseMove => "mousemove",
                MouseEventType::MouseEnter => "mouseenter",
                MouseEventType::MouseLeave => "mouseleave",
                MouseEventType::ContextMenu => "contextmenu",
                MouseEventType::Wheel => "wheel",
            },
            InteractionEvent::Key(key) => match key.event_type {
                KeyEventType::KeyDown => "keydown",
                KeyEventType::KeyUp => "keyup",
                KeyEventType::KeyPress => "keypress",
            },
            InteractionEvent::Select { .. } => "select",
            InteractionEvent::Hover { .. } => "hover",
            InteractionEvent::Zoom { .. } => "zoom",
            InteractionEvent::Pan { .. } => "pan",
            InteractionEvent::Brush { .. } => "brush",
            InteractionEvent::Custom { name, .. } => name,
        };

        let mut handled = false;

        for (handler_type, handler) in &self.handlers {
            if handler_type == event_type || handler_type == "*" {
                handled = handler(event) || handled;
            }
        }

        handled
    }

    /// Remove all handlers for a specific event type
    pub fn remove_handlers(&mut self, event_type: &str) {
        self.handlers.retain(|(t, _)| t != event_type);
    }

    /// Clear all handlers
    pub fn clear(&mut self) {
        self.handlers.clear();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_mouse_event() {
        let event = MouseEvent::new(MouseEventType::Click, Point::new(10.0, 20.0))
            .with_button(MouseButton::Left)
            .with_modifiers(false, true, false, false);

        assert_eq!(event.event_type, MouseEventType::Click);
        assert_eq!(event.position.x, 10.0);
        assert_eq!(event.position.y, 20.0);
        assert_eq!(event.button, Some(MouseButton::Left));
        assert!(!event.alt_key);
        assert!(event.ctrl_key);
        assert!(!event.shift_key);
        assert!(!event.meta_key);
    }

    #[test]
    fn test_event_dispatcher() {
        let mut dispatcher = EventDispatcher::new();

        dispatcher.add_handler("click", |_| {
            // 简化测试，不需要捕获外部变量
            true
        });

        let event = InteractionEvent::Mouse(
            MouseEvent::new(MouseEventType::Click, Point::new(10.0, 20.0))
                .with_button(MouseButton::Left),
        );

        let handled = dispatcher.dispatch(&event);
        assert!(handled);
    }
}
