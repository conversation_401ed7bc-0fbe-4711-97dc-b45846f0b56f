//! Viewport management for chart zooming and panning
//!
//! This module provides functionality for managing chart viewport transformations,
//! including zooming, panning, and coordinate system transformations.

use crate::{Bounds, Point};
use serde::{Deserialize, Serialize};

/// Viewport transformation matrix
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Serialize, Deserialize)]
pub struct ViewportTransform {
    /// Scale factor for X axis
    pub scale_x: f64,

    /// Scale factor for Y axis  
    pub scale_y: f64,

    /// Translation offset for X axis
    pub translate_x: f64,

    /// Translation offset for Y axis
    pub translate_y: f64,
}

impl Default for ViewportTransform {
    fn default() -> Self {
        Self {
            scale_x: 1.0,
            scale_y: 1.0,
            translate_x: 0.0,
            translate_y: 0.0,
        }
    }
}

impl ViewportTransform {
    /// Create a new identity transform
    pub fn identity() -> Self {
        Self::default()
    }

    /// Create a transform with specific scale and translation
    pub fn new(scale_x: f64, scale_y: f64, translate_x: f64, translate_y: f64) -> Self {
        Self {
            scale_x,
            scale_y,
            translate_x,
            translate_y,
        }
    }

    /// Transform a point from data coordinates to screen coordinates
    pub fn transform_point(&self, point: Point) -> Point {
        Point::new(
            point.x * self.scale_x + self.translate_x,
            point.y * self.scale_y + self.translate_y,
        )
    }

    /// Transform a point from screen coordinates to data coordinates
    pub fn inverse_transform_point(&self, point: Point) -> Point {
        Point::new(
            (point.x - self.translate_x) / self.scale_x,
            (point.y - self.translate_y) / self.scale_y,
        )
    }

    /// Apply scaling around a center point
    pub fn scale_around(&mut self, center: Point, scale_x: f64, scale_y: f64) {
        // Translate to origin
        self.translate_x -= center.x;
        self.translate_y -= center.y;

        // Apply scaling
        self.scale_x *= scale_x;
        self.scale_y *= scale_y;
        self.translate_x *= scale_x;
        self.translate_y *= scale_y;

        // Translate back
        self.translate_x += center.x;
        self.translate_y += center.y;
    }

    /// Apply translation
    pub fn translate(&mut self, dx: f64, dy: f64) {
        self.translate_x += dx;
        self.translate_y += dy;
    }

    /// Reset to identity transform
    pub fn reset(&mut self) {
        *self = Self::identity();
    }

    /// Check if this is an identity transform
    pub fn is_identity(&self) -> bool {
        (self.scale_x - 1.0).abs() < f64::EPSILON
            && (self.scale_y - 1.0).abs() < f64::EPSILON
            && self.translate_x.abs() < f64::EPSILON
            && self.translate_y.abs() < f64::EPSILON
    }
}

/// Zoom and pan controller configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ZoomPanConfig {
    /// Whether zooming is enabled
    pub zoom_enabled: bool,

    /// Whether panning is enabled
    pub pan_enabled: bool,

    /// Minimum zoom level
    pub min_zoom: f64,

    /// Maximum zoom level
    pub max_zoom: f64,

    /// Zoom sensitivity for mouse wheel
    pub zoom_sensitivity: f64,

    /// Pan sensitivity for mouse drag
    pub pan_sensitivity: f64,

    /// Whether to maintain aspect ratio when zooming
    pub maintain_aspect_ratio: bool,

    /// Whether to enable zoom to selection
    pub zoom_to_selection_enabled: bool,
}

impl Default for ZoomPanConfig {
    fn default() -> Self {
        Self {
            zoom_enabled: true,
            pan_enabled: true,
            min_zoom: 0.1,
            max_zoom: 10.0,
            zoom_sensitivity: 0.1,
            pan_sensitivity: 1.0,
            maintain_aspect_ratio: true,
            zoom_to_selection_enabled: true,
        }
    }
}

/// Zoom and pan controller state
#[derive(Debug, Clone)]
pub struct ZoomPanState {
    /// Current viewport transform
    pub transform: ViewportTransform,

    /// Whether currently panning
    pub is_panning: bool,

    /// Last pan position
    pub last_pan_position: Option<Point>,

    /// Selection rectangle for zoom to selection
    pub selection_rect: Option<Bounds>,

    /// Whether currently selecting
    pub is_selecting: bool,
}

impl Default for ZoomPanState {
    fn default() -> Self {
        Self {
            transform: ViewportTransform::identity(),
            is_panning: false,
            last_pan_position: None,
            selection_rect: None,
            is_selecting: false,
        }
    }
}

/// Zoom and pan controller
#[derive(Debug, Clone)]
pub struct ZoomPanController {
    /// Configuration
    pub config: ZoomPanConfig,

    /// Current state
    pub state: ZoomPanState,

    /// Chart bounds for constraint calculations
    pub chart_bounds: Option<Bounds>,
}

impl Default for ZoomPanController {
    fn default() -> Self {
        Self::new()
    }
}

impl ZoomPanController {
    /// Create a new zoom pan controller
    pub fn new() -> Self {
        Self {
            config: ZoomPanConfig::default(),
            state: ZoomPanState::default(),
            chart_bounds: None,
        }
    }

    /// Create with custom configuration
    pub fn with_config(config: ZoomPanConfig) -> Self {
        Self {
            config,
            state: ZoomPanState::default(),
            chart_bounds: None,
        }
    }

    /// Set chart bounds for constraint calculations
    pub fn set_chart_bounds(&mut self, bounds: Bounds) {
        self.chart_bounds = Some(bounds);
    }

    /// Handle mouse wheel zoom
    pub fn handle_wheel_zoom(&mut self, position: Point, delta: f64) -> bool {
        if !self.config.zoom_enabled {
            return false;
        }

        let zoom_factor = if delta > 0.0 {
            1.0 + self.config.zoom_sensitivity
        } else {
            1.0 - self.config.zoom_sensitivity
        };

        self.zoom_at_point(position, zoom_factor, zoom_factor)
    }

    /// Zoom at a specific point
    pub fn zoom_at_point(&mut self, center: Point, scale_x: f64, scale_y: f64) -> bool {
        let new_scale_x = (self.state.transform.scale_x * scale_x)
            .max(self.config.min_zoom)
            .min(self.config.max_zoom);
        let new_scale_y = if self.config.maintain_aspect_ratio {
            new_scale_x
        } else {
            (self.state.transform.scale_y * scale_y)
                .max(self.config.min_zoom)
                .min(self.config.max_zoom)
        };

        let actual_scale_x = new_scale_x / self.state.transform.scale_x;
        let actual_scale_y = new_scale_y / self.state.transform.scale_y;

        self.state
            .transform
            .scale_around(center, actual_scale_x, actual_scale_y);
        true
    }

    /// Start panning
    pub fn start_pan(&mut self, position: Point) -> bool {
        if !self.config.pan_enabled {
            return false;
        }

        self.state.is_panning = true;
        self.state.last_pan_position = Some(position);
        true
    }

    /// Update pan position
    pub fn update_pan(&mut self, position: Point) -> bool {
        if !self.state.is_panning || !self.config.pan_enabled {
            return false;
        }

        if let Some(last_pos) = self.state.last_pan_position {
            let dx = (position.x - last_pos.x) * self.config.pan_sensitivity;
            let dy = (position.y - last_pos.y) * self.config.pan_sensitivity;

            self.state.transform.translate(dx, dy);
            self.state.last_pan_position = Some(position);
            true
        } else {
            false
        }
    }

    /// End panning
    pub fn end_pan(&mut self) {
        self.state.is_panning = false;
        self.state.last_pan_position = None;
    }

    /// Reset zoom and pan to default
    pub fn reset(&mut self) {
        self.state.transform.reset();
        self.end_pan();
        self.end_selection();
    }

    /// Start selection for zoom to selection
    pub fn start_selection(&mut self, position: Point) -> bool {
        if !self.config.zoom_to_selection_enabled {
            return false;
        }

        self.state.is_selecting = true;
        self.state.selection_rect = Some(Bounds::from_two_points(position, position));
        true
    }

    /// Update selection rectangle
    pub fn update_selection(&mut self, position: Point) -> bool {
        if !self.state.is_selecting {
            return false;
        }

        if let Some(ref mut rect) = self.state.selection_rect {
            *rect = Bounds::from_two_points(rect.min(), position);
            true
        } else {
            false
        }
    }

    /// End selection and zoom to selected area
    pub fn end_selection(&mut self) -> bool {
        if !self.state.is_selecting {
            return false;
        }

        self.state.is_selecting = false;

        if let Some(rect) = self.state.selection_rect.take() {
            if let Some(chart_bounds) = self.chart_bounds {
                self.zoom_to_bounds(rect, chart_bounds)
            } else {
                false
            }
        } else {
            false
        }
    }

    /// Zoom to fit specific bounds
    pub fn zoom_to_bounds(&mut self, target_bounds: Bounds, chart_bounds: Bounds) -> bool {
        let width_scale = chart_bounds.width() / target_bounds.width();
        let height_scale = chart_bounds.height() / target_bounds.height();

        let scale = if self.config.maintain_aspect_ratio {
            width_scale.min(height_scale)
        } else {
            width_scale
        };

        let scale = scale.max(self.config.min_zoom).min(self.config.max_zoom);

        // Calculate translation to center the target bounds
        let center_x = (target_bounds.min().x + target_bounds.max().x) / 2.0;
        let center_y = (target_bounds.min().y + target_bounds.max().y) / 2.0;

        let chart_center_x = (chart_bounds.min().x + chart_bounds.max().x) / 2.0;
        let chart_center_y = (chart_bounds.min().y + chart_bounds.max().y) / 2.0;

        self.state.transform.scale_x = scale;
        self.state.transform.scale_y = scale;
        self.state.transform.translate_x = chart_center_x - center_x * scale;
        self.state.transform.translate_y = chart_center_y - center_y * scale;

        true
    }

    /// Get current zoom level
    pub fn get_zoom_level(&self) -> f64 {
        self.state.transform.scale_x
    }

    /// Check if currently panning
    pub fn is_panning(&self) -> bool {
        self.state.is_panning
    }

    /// Check if currently selecting
    pub fn is_selecting(&self) -> bool {
        self.state.is_selecting
    }

    /// Get current selection rectangle
    pub fn get_selection_rect(&self) -> Option<&Bounds> {
        self.state.selection_rect.as_ref()
    }
}
