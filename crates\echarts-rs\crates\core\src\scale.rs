//! Scale implementations for data mapping

use crate::{ChartError, DataValue};
use serde::{Deserialize, Serialize};

/// Trait for scales that map data values to normalized values (0.0 to 1.0)
pub trait Scale {
    /// Map a data value to a normalized value (0.0 to 1.0)
    fn normalize(&self, value: &DataValue) -> Result<f64, ChartError>;

    /// Map a normalized value (0.0 to 1.0) back to a data value
    fn denormalize(&self, normalized: f64) -> Result<DataValue, ChartError>;

    /// Get the domain (input range) of this scale
    fn domain(&self) -> (DataValue, DataValue);

    /// Set the domain (input range) of this scale
    fn set_domain(&mut self, min: DataValue, max: DataValue) -> Result<(), ChartError>;

    /// Get nice/rounded domain values
    fn nice_domain(&self) -> (DataValue, DataValue);

    /// Generate tick values for this scale
    fn ticks(&self, count: usize) -> Vec<DataValue>;
}

/// Linear scale for numeric data
#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct LinearScale {
    pub min: f64,
    pub max: f64,
}

impl LinearScale {
    pub fn new(min: f64, max: f64) -> Self {
        Self { min, max }
    }

    pub fn from_data(data: &[f64]) -> Self {
        if data.is_empty() {
            return Self::new(0.0, 1.0);
        }

        let min = data.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max = data.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));

        Self::new(min, max)
    }

    fn range(&self) -> f64 {
        self.max - self.min
    }
}

impl Scale for LinearScale {
    fn normalize(&self, value: &DataValue) -> Result<f64, ChartError> {
        let num = value.as_number().ok_or_else(|| {
            ChartError::InvalidData("Value must be numeric for linear scale".into())
        })?;

        if self.range() == 0.0 {
            Ok(0.5)
        } else {
            Ok((num - self.min) / self.range())
        }
    }

    fn denormalize(&self, normalized: f64) -> Result<DataValue, ChartError> {
        let value = self.min + normalized * self.range();
        Ok(DataValue::Number(value))
    }

    fn domain(&self) -> (DataValue, DataValue) {
        (DataValue::Number(self.min), DataValue::Number(self.max))
    }

    fn set_domain(&mut self, min: DataValue, max: DataValue) -> Result<(), ChartError> {
        self.min = min
            .as_number()
            .ok_or_else(|| ChartError::InvalidData("Min value must be numeric".into()))?;
        self.max = max
            .as_number()
            .ok_or_else(|| ChartError::InvalidData("Max value must be numeric".into()))?;
        Ok(())
    }

    fn nice_domain(&self) -> (DataValue, DataValue) {
        let range = self.range();
        if range == 0.0 {
            return self.domain();
        }

        let step = nice_number(range / 5.0, false);
        let nice_min = (self.min / step).floor() * step;
        let nice_max = (self.max / step).ceil() * step;

        (DataValue::Number(nice_min), DataValue::Number(nice_max))
    }

    fn ticks(&self, count: usize) -> Vec<DataValue> {
        if count == 0 {
            return Vec::new();
        }

        let range = self.range();
        if range == 0.0 {
            return vec![DataValue::Number(self.min)];
        }

        let step = nice_number(range / (count - 1) as f64, true);
        let start = (self.min / step).ceil() * step;

        let mut ticks = Vec::new();
        let mut value = start;

        while value <= self.max && ticks.len() < count {
            ticks.push(DataValue::Number(value));
            value += step;
        }

        ticks
    }
}

/// Logarithmic scale for numeric data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogScale {
    pub min: f64,
    pub max: f64,
    pub base: f64,
}

impl LogScale {
    pub fn new(min: f64, max: f64, base: f64) -> Result<Self, ChartError> {
        if min <= 0.0 || max <= 0.0 {
            return Err(ChartError::InvalidData(
                "Log scale requires positive values".into(),
            ));
        }
        if base <= 1.0 {
            return Err(ChartError::InvalidData(
                "Log base must be greater than 1".into(),
            ));
        }

        Ok(Self { min, max, base })
    }

    fn log_range(&self) -> f64 {
        self.max.log(self.base) - self.min.log(self.base)
    }
}

impl Scale for LogScale {
    fn normalize(&self, value: &DataValue) -> Result<f64, ChartError> {
        let num = value
            .as_number()
            .ok_or_else(|| ChartError::InvalidData("Value must be numeric for log scale".into()))?;

        if num <= 0.0 {
            return Err(ChartError::InvalidData(
                "Log scale requires positive values".into(),
            ));
        }

        let log_range = self.log_range();
        if log_range == 0.0 {
            Ok(0.5)
        } else {
            Ok((num.log(self.base) - self.min.log(self.base)) / log_range)
        }
    }

    fn denormalize(&self, normalized: f64) -> Result<DataValue, ChartError> {
        let log_min = self.min.log(self.base);
        let log_value = log_min + normalized * self.log_range();
        let value = self.base.powf(log_value);
        Ok(DataValue::Number(value))
    }

    fn domain(&self) -> (DataValue, DataValue) {
        (DataValue::Number(self.min), DataValue::Number(self.max))
    }

    fn set_domain(&mut self, min: DataValue, max: DataValue) -> Result<(), ChartError> {
        let min_num = min
            .as_number()
            .ok_or_else(|| ChartError::InvalidData("Min value must be numeric".into()))?;
        let max_num = max
            .as_number()
            .ok_or_else(|| ChartError::InvalidData("Max value must be numeric".into()))?;

        if min_num <= 0.0 || max_num <= 0.0 {
            return Err(ChartError::InvalidData(
                "Log scale requires positive values".into(),
            ));
        }

        self.min = min_num;
        self.max = max_num;
        Ok(())
    }

    fn nice_domain(&self) -> (DataValue, DataValue) {
        // For log scales, nice values are powers of the base
        let log_min = self.min.log(self.base);
        let log_max = self.max.log(self.base);

        let nice_log_min = log_min.floor();
        let nice_log_max = log_max.ceil();

        let nice_min = self.base.powf(nice_log_min);
        let nice_max = self.base.powf(nice_log_max);

        (DataValue::Number(nice_min), DataValue::Number(nice_max))
    }

    fn ticks(&self, count: usize) -> Vec<DataValue> {
        if count == 0 {
            return Vec::new();
        }

        let log_min = self.min.log(self.base);
        let log_max = self.max.log(self.base);
        let log_range = log_max - log_min;

        if log_range == 0.0 {
            return vec![DataValue::Number(self.min)];
        }

        let mut ticks = Vec::new();

        // Generate ticks at powers of the base
        let start_power = log_min.floor() as i32;
        let end_power = log_max.ceil() as i32;

        for power in start_power..=end_power {
            let value = self.base.powi(power);
            if value >= self.min && value <= self.max {
                ticks.push(DataValue::Number(value));
            }

            if ticks.len() >= count {
                break;
            }
        }

        ticks
    }
}

/// Ordinal scale for categorical data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrdinalScale {
    pub categories: Vec<String>,
}

impl OrdinalScale {
    pub fn new(categories: Vec<String>) -> Self {
        Self { categories }
    }

    pub fn from_data(data: &[String]) -> Self {
        let mut categories = Vec::new();
        for item in data {
            if !categories.contains(item) {
                categories.push(item.clone());
            }
        }
        Self::new(categories)
    }
}

impl Scale for OrdinalScale {
    fn normalize(&self, value: &DataValue) -> Result<f64, ChartError> {
        let string_val = value.as_string();

        if let Some(index) = self.categories.iter().position(|cat| cat == &string_val) {
            if self.categories.len() <= 1 {
                Ok(0.5)
            } else {
                Ok(index as f64 / (self.categories.len() - 1) as f64)
            }
        } else {
            Err(ChartError::InvalidData(format!(
                "Category '{}' not found in scale",
                string_val
            )))
        }
    }

    fn denormalize(&self, normalized: f64) -> Result<DataValue, ChartError> {
        if self.categories.is_empty() {
            return Err(ChartError::InvalidData(
                "No categories in ordinal scale".into(),
            ));
        }

        let index = if self.categories.len() == 1 {
            0
        } else {
            (normalized * (self.categories.len() - 1) as f64).round() as usize
        };

        let index = index.min(self.categories.len() - 1);
        Ok(DataValue::String(self.categories[index].clone()))
    }

    fn domain(&self) -> (DataValue, DataValue) {
        if self.categories.is_empty() {
            (
                DataValue::String("".to_string()),
                DataValue::String("".to_string()),
            )
        } else {
            (
                DataValue::String(self.categories[0].clone()),
                DataValue::String(self.categories[self.categories.len() - 1].clone()),
            )
        }
    }

    fn set_domain(&mut self, min: DataValue, max: DataValue) -> Result<(), ChartError> {
        // For ordinal scales, we interpret this as setting the category list
        let min_str = min.as_string();
        let max_str = max.as_string();

        self.categories = vec![min_str, max_str];
        Ok(())
    }

    fn nice_domain(&self) -> (DataValue, DataValue) {
        // Ordinal scales don't need "nice" domains
        self.domain()
    }

    fn ticks(&self, _count: usize) -> Vec<DataValue> {
        self.categories
            .iter()
            .map(|cat| DataValue::String(cat.clone()))
            .collect()
    }
}

/// Helper function to calculate nice numbers for axis ticks
fn nice_number(range: f64, round: bool) -> f64 {
    let exponent = range.log10().floor();
    let fraction = range / 10.0_f64.powf(exponent);

    let nice_fraction = if round {
        if fraction < 1.5 {
            1.0
        } else if fraction < 3.0 {
            2.0
        } else if fraction < 7.0 {
            5.0
        } else {
            10.0
        }
    } else {
        if fraction <= 1.0 {
            1.0
        } else if fraction <= 2.0 {
            2.0
        } else if fraction <= 5.0 {
            5.0
        } else {
            10.0
        }
    };

    nice_fraction * 10.0_f64.powf(exponent)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_linear_scale() {
        let scale = LinearScale::new(0.0, 100.0);

        assert_eq!(scale.normalize(&DataValue::Number(0.0)).unwrap(), 0.0);
        assert_eq!(scale.normalize(&DataValue::Number(50.0)).unwrap(), 0.5);
        assert_eq!(scale.normalize(&DataValue::Number(100.0)).unwrap(), 1.0);

        let ticks = scale.ticks(5);
        assert_eq!(ticks.len(), 5);
    }

    #[test]
    fn test_ordinal_scale() {
        let categories = vec!["A".to_string(), "B".to_string(), "C".to_string()];
        let scale = OrdinalScale::new(categories);

        assert_eq!(
            scale
                .normalize(&DataValue::String("A".to_string()))
                .unwrap(),
            0.0
        );
        assert_eq!(
            scale
                .normalize(&DataValue::String("B".to_string()))
                .unwrap(),
            0.5
        );
        assert_eq!(
            scale
                .normalize(&DataValue::String("C".to_string()))
                .unwrap(),
            1.0
        );
    }
}
