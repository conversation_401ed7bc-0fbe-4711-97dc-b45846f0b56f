//! Error types for Rust ECharts

use thiserror::Error;

/// Main error type for chart operations
#[derive(Error, Debug)]
pub enum ChartError {
    #[error("Configuration error: {0}")]
    Configuration(String),

    #[error("Invalid data: {0}")]
    InvalidData(String),

    #[error("Rendering error: {0}")]
    Rendering(String),

    #[error("Coordinate system error: {0}")]
    CoordinateSystem(String),

    #[error("Theme error: {0}")]
    Theme(String),

    #[error("Animation error: {0}")]
    Animation(String),

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("Parse error: {0}")]
    Parse(String),

    #[error("Not supported: {0}")]
    NotSupported(String),

    /// GPU/Hardware related errors
    #[error("GPU error: {0}")]
    GpuError(String),

    /// Canvas/Drawing errors
    #[error("Canvas error: {0}")]
    CanvasError(String),

    /// Layout calculation errors
    #[error("Layout error: {0}")]
    LayoutError(String),

    /// Font/Text rendering errors
    #[error("Font error: {0}")]
    FontError(String),
}

/// Result type alias for chart operations
pub type Result<T> = std::result::Result<T, ChartError>;

impl ChartError {
    /// Create a configuration error
    pub fn config<S: Into<String>>(msg: S) -> Self {
        ChartError::Configuration(msg.into())
    }

    /// Create an invalid data error
    pub fn invalid_data<S: Into<String>>(msg: S) -> Self {
        ChartError::InvalidData(msg.into())
    }

    /// Create a rendering error
    pub fn rendering<S: Into<String>>(msg: S) -> Self {
        ChartError::Rendering(msg.into())
    }

    /// Create a coordinate system error
    pub fn coord<S: Into<String>>(msg: S) -> Self {
        ChartError::CoordinateSystem(msg.into())
    }

    /// Create a not supported error
    pub fn not_supported<S: Into<String>>(msg: S) -> Self {
        ChartError::NotSupported(msg.into())
    }

    /// Create a GPU error
    pub fn gpu<S: Into<String>>(msg: S) -> Self {
        ChartError::GpuError(msg.into())
    }

    /// Create a canvas error
    pub fn canvas<S: Into<String>>(msg: S) -> Self {
        ChartError::CanvasError(msg.into())
    }

    /// Create a layout error
    pub fn layout<S: Into<String>>(msg: S) -> Self {
        ChartError::LayoutError(msg.into())
    }

    /// Create a font error
    pub fn font<S: Into<String>>(msg: S) -> Self {
        ChartError::FontError(msg.into())
    }

    /// Create a validation error (alias for invalid_data)
    pub fn validation<S: Into<String>>(msg: S) -> Self {
        ChartError::InvalidData(msg.into())
    }

    /// Create a data error (alias for invalid_data)
    pub fn data<S: Into<String>>(msg: S) -> Self {
        ChartError::InvalidData(msg.into())
    }
}
