#!/usr/bin/env python3
"""
批量修复 ECharts-rs 中的样式导入问题
"""

import os
import re
import glob

def fix_style_imports():
    """批量修复样式导入问题"""
    
    # 需要修复的文件模式
    chart_files = glob.glob("crates/charts/src/*.rs")
    
    # 需要替换的模式
    replacements = [
        (r'echarts_core::draw_commands::LineCap::', 'LineCap::'),
        (r'echarts_core::draw_commands::LineJoin::', 'LineJoin::'),
        (r'echarts_core::draw_commands::FontWeight::', 'FontWeight::'),
        (r'echarts_core::draw_commands::FontStyle::', 'FontStyle::'),
        (r'echarts_core::draw_commands::TextAlign::', 'TextAlign::'),
        (r'echarts_core::draw_commands::TextBaseline::', 'TextBaseline::'),
    ]
    
    for file_path in chart_files:
        if not os.path.exists(file_path):
            continue
            
        print(f"修复文件: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 应用所有替换
            modified = False
            for pattern, replacement in replacements:
                new_content = re.sub(pattern, replacement, content)
                if new_content != content:
                    content = new_content
                    modified = True
            
            # 如果有修改，写回文件
            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ 已修复 {file_path}")
            else:
                print(f"  ⏭️  无需修复 {file_path}")
                
        except Exception as e:
            print(f"  ❌ 修复失败 {file_path}: {e}")

if __name__ == "__main__":
    print("🚀 开始批量修复样式导入问题...")
    fix_style_imports()
    print("✅ 批量修复完成！")
