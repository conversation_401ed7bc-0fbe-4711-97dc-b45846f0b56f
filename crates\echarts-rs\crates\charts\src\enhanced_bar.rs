//! 增强版柱状图实现
//!
//! 基于折线图的成功经验，实现完整的柱状图功能：
//! - 堆叠柱状图
//! - 分组柱状图
//! - 瀑布图
//! - 交互功能
//! - 动画效果
//! - 数据标签

use echarts_core::{
    Bounds, Color, CoordinateSystem, DataSet, DataValue, DrawCommand, Point, Result, Series, SeriesType,
    draw_commands::{RectStyle},
    TextStyle, FontWeight, FontStyle, TextAlign, TextBaseline, LineStyle, LineCap, LineJoin,
};
use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};
use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant};

/// 柱状图类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum BarType {
    /// 基础柱状图
    Basic,
    /// 堆叠柱状图
    Stacked,
    /// 分组柱状图
    Grouped,
    /// 瀑布图
    Waterfall,
    /// 百分比堆叠
    PercentStacked,
}

/// 柱状图方向
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum BarOrientation {
    /// 垂直柱状图
    Vertical,
    /// 水平柱状图
    Horizontal,
}

/// 柱状图动画类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum BarAnimationType {
    None,
    GrowUp,      // 从底部向上生长
    GrowDown,    // 从顶部向下生长
    GrowLeft,    // 从右向左生长
    GrowRight,   // 从左向右生长
    FadeIn,      // 淡入
    SlideIn,     // 滑入
}

/// 柱状图交互事件
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum BarInteractionEvent {
    BarHover { series_index: usize, bar_index: usize, position: Point, value: f64 },
    BarClick { series_index: usize, bar_index: usize, position: Point, value: f64 },
    BarLeave,
    SeriesHover { series_index: usize },
    SeriesLeave,
}

/// 柱状图动画配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BarAnimationConfig {
    pub enabled: bool,
    pub animation_type: BarAnimationType,
    pub duration: Duration,
    pub delay: Duration,
    pub stagger_delay: Duration, // 柱子之间的延迟
    pub easing: String,
}

impl Default for BarAnimationConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            animation_type: BarAnimationType::GrowUp,
            duration: Duration::from_millis(800),
            delay: Duration::from_millis(0),
            stagger_delay: Duration::from_millis(50),
            easing: "ease-out".to_string(),
        }
    }
}

/// 柱状图交互配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BarInteractionConfig {
    pub hover_enabled: bool,
    pub click_enabled: bool,
    pub hover_highlight_color: Option<Color>,
    pub hover_border_width: f64,
    pub tooltip_enabled: bool,
    pub cursor_style: String,
}

impl Default for BarInteractionConfig {
    fn default() -> Self {
        Self {
            hover_enabled: true,
            click_enabled: true,
            hover_highlight_color: Some(Color::rgba(1.0, 1.0, 0.0, 0.3)),
            hover_border_width: 2.0,
            tooltip_enabled: true,
            cursor_style: "pointer".to_string(),
        }
    }
}

/// 数据标签配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BarLabelConfig {
    pub show: bool,
    pub position: BarLabelPosition,
    pub format: String,
    pub font_size: f64,
    pub color: Color,
    pub background_color: Option<Color>,
    pub border_radius: f64,
    pub padding: f64,
}

/// 数据标签位置
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum BarLabelPosition {
    Top,      // 柱子顶部
    Middle,   // 柱子中间
    Bottom,   // 柱子底部
    Inside,   // 柱子内部
    Outside,  // 柱子外部
}

impl Default for BarLabelConfig {
    fn default() -> Self {
        Self {
            show: false,
            position: BarLabelPosition::Top,
            format: "{value}".to_string(),
            font_size: 12.0,
            color: Color::rgb(0.3, 0.3, 0.3),
            background_color: None,
            border_radius: 2.0,
            padding: 4.0,
        }
    }
}

/// 增强版柱状图系列
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnhancedBarSeries {
    /// 基础配置
    pub config: ChartConfig,

    /// 数据集
    pub data: DataSet,
    
    /// 柱状图类型
    pub bar_type: BarType,
    
    /// 柱状图方向
    pub orientation: BarOrientation,
    
    /// 柱子颜色
    pub color: Color,
    
    /// 柱子宽度 (0.0-1.0, 相对于类别间距)
    pub bar_width: f64,
    
    /// 柱子间距 (0.0-1.0)
    pub bar_gap: f64,
    
    /// 类别间距 (0.0-1.0)
    pub category_gap: f64,
    
    /// 边框颜色
    pub border_color: Option<Color>,
    
    /// 边框宽度
    pub border_width: f64,
    
    /// 圆角半径
    pub border_radius: f64,
    
    /// 渐变填充
    pub gradient: Option<Vec<(f64, Color)>>,
    
    /// 数据标签配置
    pub label: BarLabelConfig,
    
    /// 动画配置
    pub animation: BarAnimationConfig,
    
    /// 交互配置
    pub interaction: BarInteractionConfig,
    
    /// 堆叠组名称 (用于堆叠柱状图)
    pub stack: Option<String>,
    
    // visible, z_index 现在在 config 中
    
    /// 最小柱子高度
    pub min_bar_height: f64,
    
    /// 最大柱子宽度 (像素)
    pub max_bar_width: Option<f64>,
    
    /// 最后更新时间
    #[serde(skip)]
    pub last_update: Option<Instant>,
}

impl EnhancedBarSeries {
    /// 创建新的增强版柱状图系列
    pub fn new<S: Into<String>>(name: S) -> Self {
        Self {
            config: ChartConfig {
                name: name.into(),
                ..ChartConfig::default()
            },
            data: DataSet::new(),
            bar_type: BarType::Basic,
            orientation: BarOrientation::Vertical,
            color: Color::rgb(0.3, 0.6, 0.9),
            bar_width: 0.8,
            bar_gap: 0.1,
            category_gap: 0.2,
            border_color: None,
            border_width: 0.0,
            border_radius: 0.0,
            gradient: None,
            label: BarLabelConfig::default(),
            animation: BarAnimationConfig::default(),
            interaction: BarInteractionConfig::default(),
            stack: None,
            min_bar_height: 1.0,
            max_bar_width: None,
            last_update: None,
        }
    }

    /// 设置数据
    pub fn data<T: Into<DataSet>>(mut self, data: T) -> Self {
        self.data = data.into();
        self
    }

    /// 设置柱状图类型
    pub fn bar_type(mut self, bar_type: BarType) -> Self {
        self.bar_type = bar_type;
        self
    }

    /// 设置方向
    pub fn orientation(mut self, orientation: BarOrientation) -> Self {
        self.orientation = orientation;
        self
    }

    /// 设置颜色
    pub fn color(mut self, color: Color) -> Self {
        self.color = color;
        self
    }

    /// 设置柱子宽度
    pub fn bar_width(mut self, width: f64) -> Self {
        self.bar_width = width.max(0.0).min(1.0);
        self
    }

    /// 设置柱子间距
    pub fn bar_gap(mut self, gap: f64) -> Self {
        self.bar_gap = gap.max(0.0).min(1.0);
        self
    }

    /// 设置边框
    pub fn border(mut self, color: Color, width: f64) -> Self {
        self.border_color = Some(color);
        self.border_width = width;
        self
    }

    /// 设置圆角
    pub fn border_radius(mut self, radius: f64) -> Self {
        self.border_radius = radius;
        self
    }

    /// 设置渐变
    pub fn gradient(mut self, gradient: Vec<(f64, Color)>) -> Self {
        self.gradient = Some(gradient);
        self
    }

    /// 显示数据标签
    pub fn show_labels(mut self, show: bool) -> Self {
        self.label.show = show;
        self
    }

    /// 设置标签位置
    pub fn label_position(mut self, position: BarLabelPosition) -> Self {
        self.label.position = position;
        self
    }

    /// 设置堆叠组
    pub fn stack<S: Into<String>>(mut self, stack: S) -> Self {
        self.stack = Some(stack.into());
        self
    }

    /// 启用动画
    pub fn animate(mut self, animation_type: BarAnimationType) -> Self {
        self.animation.enabled = true;
        self.animation.animation_type = animation_type;
        self
    }

    /// 创建堆叠柱状图
    pub fn new_stacked<S: Into<String>>(name: S, stack: S) -> Self {
        Self::new(name)
            .bar_type(BarType::Stacked)
            .stack(stack)
    }

    /// 创建分组柱状图
    pub fn new_grouped<S: Into<String>>(name: S) -> Self {
        Self::new(name)
            .bar_type(BarType::Grouped)
            .bar_width(0.6)
            .bar_gap(0.2)
    }

    /// 创建瀑布图
    pub fn new_waterfall<S: Into<String>>(name: S) -> Self {
        Self::new(name)
            .bar_type(BarType::Waterfall)
            .show_labels(true)
            .label_position(BarLabelPosition::Top)
    }

    /// 创建水平柱状图
    pub fn new_horizontal<S: Into<String>>(name: S) -> Self {
        Self::new(name)
            .orientation(BarOrientation::Horizontal)
            .animate(BarAnimationType::GrowRight)
    }

    /// 处理鼠标悬停事件
    pub fn handle_mouse_hover(&mut self, position: Point, coord_system: &dyn CoordinateSystem) -> Option<BarInteractionEvent> {
        if !self.interaction.hover_enabled {
            return None;
        }

        if let Some((bar_index, value)) = self.find_bar_at_position(position, coord_system) {
            Some(BarInteractionEvent::BarHover {
                series_index: 0,
                bar_index,
                position,
                value,
            })
        } else {
            Some(BarInteractionEvent::BarLeave)
        }
    }

    /// 处理鼠标点击事件
    pub fn handle_mouse_click(&mut self, position: Point, coord_system: &dyn CoordinateSystem) -> Option<BarInteractionEvent> {
        if !self.interaction.click_enabled {
            return None;
        }

        if let Some((bar_index, value)) = self.find_bar_at_position(position, coord_system) {
            Some(BarInteractionEvent::BarClick {
                series_index: 0,
                bar_index,
                position,
                value,
            })
        } else {
            None
        }
    }

    /// 查找指定位置的柱子
    fn find_bar_at_position(&self, position: Point, coord_system: &dyn CoordinateSystem) -> Option<(usize, f64)> {
        let bounds = coord_system.bounds();

        for i in 0..self.data.len() {
            if let Some(point) = self.data.get_point(i) {
                if let (Some(x), Some(y)) = (point.get_number(0), point.get_number(1)) {
                    let bar_rect = self.calculate_bar_rect(i, x, y, bounds);

                    if position.x >= bar_rect.origin.x &&
                       position.x <= bar_rect.origin.x + bar_rect.size.width &&
                       position.y >= bar_rect.origin.y &&
                       position.y <= bar_rect.origin.y + bar_rect.size.height {
                        return Some((i, y));
                    }
                }
            }
        }

        None
    }

    /// 计算柱子的矩形区域
    fn calculate_bar_rect(&self, index: usize, x: f64, y: f64, bounds: Bounds) -> Bounds {
        let category_count = self.data.len() as f64;
        let category_width = bounds.size.width / category_count;
        let bar_width = category_width * self.bar_width;

        let bar_x = bounds.origin.x + (index as f64 * category_width) +
                   (category_width - bar_width) * 0.5;

        let bar_height = (y / 100.0) * bounds.size.height; // 假设数据范围0-100
        let bar_y = bounds.origin.y + bounds.size.height - bar_height;

        Bounds::new(bar_x, bar_y, bar_width, bar_height)
    }

    /// 开始动画
    pub fn start_animation(&mut self) {
        if self.animation.enabled {
            self.last_update = Some(Instant::now());
        }
    }

    /// 更新动画状态
    pub fn update_animation(&mut self, current_time: Instant) -> bool {
        if !self.animation.enabled {
            return false;
        }

        if let Some(start_time) = self.last_update {
            let elapsed = current_time.duration_since(start_time);
            if elapsed < self.animation.duration {
                return true; // 动画进行中
            } else {
                self.last_update = None;
                return false; // 动画完成
            }
        }

        false
    }

    /// 获取动画进度
    pub fn get_animation_progress(&self) -> f64 {
        if let Some(start_time) = self.last_update {
            let elapsed = Instant::now().duration_since(start_time);
            let progress = elapsed.as_secs_f64() / self.animation.duration.as_secs_f64();
            self.apply_easing(progress.min(1.0))
        } else {
            1.0
        }
    }

    /// 应用缓动函数
    fn apply_easing(&self, t: f64) -> f64 {
        match self.animation.easing.as_str() {
            "ease-in" => t * t,
            "ease-out" => 1.0 - (1.0 - t) * (1.0 - t),
            "ease-in-out" => {
                if t < 0.5 {
                    2.0 * t * t
                } else {
                    1.0 - 2.0 * (1.0 - t) * (1.0 - t)
                }
            }
            "bounce" => {
                if t < 1.0 / 2.75 {
                    7.5625 * t * t
                } else if t < 2.0 / 2.75 {
                    let t = t - 1.5 / 2.75;
                    7.5625 * t * t + 0.75
                } else if t < 2.5 / 2.75 {
                    let t = t - 2.25 / 2.75;
                    7.5625 * t * t + 0.9375
                } else {
                    let t = t - 2.625 / 2.75;
                    7.5625 * t * t + 0.984375
                }
            }
            _ => t, // linear
        }
    }
}

impl Series for EnhancedBarSeries {
    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Bar
    }

    fn bounds(&self) -> Option<Bounds> {
        None // 使用默认边界
    }

    fn z_index(&self) -> i32 {
        self.config.z_index
    }

    fn clone_series(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }

    fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        if !self.config.visible || self.data.is_empty() {
            return Ok(commands);
        }

        let bounds = coord_system.bounds();
        let animation_progress = self.get_animation_progress();

        // 渲染柱子
        for i in 0..self.data.len() {
            if let Some(point) = self.data.get_point(i) {
                if let (Some(x), Some(y)) = (point.get_number(0), point.get_number(1)) {
                    self.render_bar(&mut commands, i, x, y, bounds, animation_progress)?;
                }
            }
        }

        // 渲染数据标签
        if self.label.show {
            self.render_labels(&mut commands, bounds, animation_progress)?;
        }

        Ok(commands)
    }
}

impl EnhancedBarSeries {
    /// 渲染单个柱子
    fn render_bar(&self, commands: &mut Vec<DrawCommand>, index: usize, x: f64, y: f64, bounds: Bounds, progress: f64) -> Result<()> {
        let mut bar_rect = self.calculate_bar_rect(index, x, y, bounds);

        // 应用动画效果
        match self.animation.animation_type {
            BarAnimationType::GrowUp => {
                let original_height = bar_rect.size.height;
                bar_rect.size.height = original_height * progress;
                bar_rect.origin.y = bar_rect.origin.y + original_height - bar_rect.size.height;
            }
            BarAnimationType::GrowDown => {
                bar_rect.size.height = bar_rect.size.height * progress;
            }
            BarAnimationType::GrowRight => {
                bar_rect.size.width = bar_rect.size.width * progress;
            }
            BarAnimationType::GrowLeft => {
                let original_width = bar_rect.size.width;
                bar_rect.size.width = original_width * progress;
                bar_rect.origin.x = bar_rect.origin.x + original_width - bar_rect.size.width;
            }
            BarAnimationType::FadeIn => {
                // 透明度动画在颜色中处理
            }
            _ => {}
        }

        // 应用最小高度
        if bar_rect.size.height < self.min_bar_height {
            bar_rect.size.height = self.min_bar_height;
        }

        // 应用最大宽度限制
        if let Some(max_width) = self.max_bar_width {
            if bar_rect.size.width > max_width {
                let width_diff = bar_rect.size.width - max_width;
                bar_rect.size.width = max_width;
                bar_rect.origin.x += width_diff * 0.5;
            }
        }

        // 计算颜色 (包含透明度动画)
        let mut color = self.color;
        if self.animation.animation_type == BarAnimationType::FadeIn {
            color.a *= progress as f32;
        }

        // 创建矩形样式
        let rect_style = RectStyle {
            fill: Some(color),
            stroke: self.border_color.map(|border_color| {
                LineStyle {
                    color: border_color,
                    width: self.border_width,
                    dash_pattern: None,
                    cap: LineCap::Butt,
                    join: LineJoin::Miter,
                    opacity: 1.0,
                }
            }),
            opacity: color.a as f64,
            corner_radius: self.border_radius,
        };

        // 添加矩形绘制命令
        commands.push(DrawCommand::Rect {
            bounds: bar_rect,
            style: rect_style,
        });

        Ok(())
    }

    /// 渲染数据标签
    fn render_labels(&self, commands: &mut Vec<DrawCommand>, bounds: Bounds, progress: f64) -> Result<()> {
        for i in 0..self.data.len() {
            if let Some(point) = self.data.get_point(i) {
                if let (Some(x), Some(y)) = (point.get_number(0), point.get_number(1)) {
                    let bar_rect = self.calculate_bar_rect(i, x, y, bounds);

                    // 计算标签位置
                    let label_position = match self.label.position {
                        BarLabelPosition::Top => Point::new(
                            bar_rect.origin.x + bar_rect.size.width * 0.5,
                            bar_rect.origin.y - self.label.padding
                        ),
                        BarLabelPosition::Middle => Point::new(
                            bar_rect.origin.x + bar_rect.size.width * 0.5,
                            bar_rect.origin.y + bar_rect.size.height * 0.5
                        ),
                        BarLabelPosition::Bottom => Point::new(
                            bar_rect.origin.x + bar_rect.size.width * 0.5,
                            bar_rect.origin.y + bar_rect.size.height + self.label.padding
                        ),
                        _ => Point::new(
                            bar_rect.origin.x + bar_rect.size.width * 0.5,
                            bar_rect.origin.y - self.label.padding
                        ),
                    };

                    // 格式化标签文本
                    let label_text = self.label.format.replace("{value}", &format!("{:.1}", y));

                    // 创建文本样式
                    let mut text_color = self.label.color;
                    if self.animation.animation_type == BarAnimationType::FadeIn {
                        text_color.a *= progress as f32;
                    }

                    let text_style = TextStyle {
                        font_family: "Arial".to_string(),
                        font_size: self.label.font_size,
                        font_weight: FontWeight::Normal,
                        font_style: FontStyle::Normal,
                        color: text_color,
                        opacity: text_color.a as f64,
                        align: TextAlign::Center,
                        baseline: TextBaseline::Middle,
                        rotation: 0.0,
                        letter_spacing: 0.0,
                        line_height: 1.0,
                    };

                    // 添加文本绘制命令
                    commands.push(DrawCommand::Text {
                        text: label_text,
                        position: label_position,
                        style: text_style,
                    });
                }
            }
        }

        Ok(())
    }
}
// ============================================================================
// 图表基础架构集成 - ChartBase 实现
// ============================================================================

/// 为 EnhancedBarSeries 实现 ChartBase trait
impl ChartBase for EnhancedBarSeries {
    type DataType = DataSet;

    fn name(&self) -> &str {
        &self.config.name
    }

    fn raw_data(&self) -> &Self::DataType {
        &self.data
    }

    fn to_dataset(&self) -> DataSet {
        self.data.clone()
    }

    fn visible(&self) -> bool {
        self.config.visible
    }

    fn z_index(&self) -> i32 {
        self.config.z_index
    }

    fn bounds(&self) -> Option<Bounds> {
        BoundsCalculator::from_dataset(&self.data)
    }

    fn config(&self) -> &ChartConfig {
        &self.config
    }

    fn config_mut(&mut self) -> &mut ChartConfig {
        &mut self.config
    }
}

/// 为 EnhancedBarSeries 实现 ChartSeries trait
impl ChartSeries for EnhancedBarSeries {
    // 所有方法都由默认实现提供，基于 ChartBase
}
