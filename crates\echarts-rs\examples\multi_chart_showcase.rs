//! 多图表类型展示
//!
//! 展示所有增强的图表类型：
//! - 增强版折线图 (交互、动画、数据优化)
//! - 增强版柱状图 (堆叠、分组、瀑布图)
//! - 散点图增强
//! - 混合图表
//! - 实时数据仪表板

use echarts_rs::{LineSeries, Color, CartesianCoordinateSystem, Bounds as EchartsBounds};
use echarts_charts::enhanced_bar::{EnhancedBarSeries, BarType, BarOrientation, BarAnimationType};
use echarts_charts::line::LabelFormatType;
use gpui_renderer::GpuiRenderer;
use gpui::*;
use gpui_component::StyledExt;
use std::time::{Duration, Instant};

fn main() {
    println!("🚀 启动多图表类型展示...");

    App::new().run(move |cx: &mut AppContext| {
        let window_size = size(px(1600.0), px(1000.0));
        
        let window_options = WindowOptions {
            window_bounds: Some(WindowBounds::Windowed(Bounds::centered(
                None,
                window_size,
                cx,
            ))),
            titlebar: Some(TitlebarOptions {
                title: Some("📊 多图表类型展示 - ECharts-rs 完整功能".into()),
                appears_transparent: false,
                traffic_light_position: None,
            }),
            window_background: WindowBackgroundAppearance::Opaque,
            focus: true,
            show: true,
            kind: WindowKind::Normal,
            is_movable: true,
            display_id: None,
            app_id: Some("multi-chart-showcase".to_string()),
            window_decorations: None,
            window_min_size: Some(size(px(1200.0), px(800.0))),
        };
        
        cx.open_window(window_options, |cx| {
            println!("✅ 窗口已创建，正在初始化多图表展示...");
            cx.new_view(|_cx| MultiChartShowcase::new())
        }).expect("无法创建窗口");
    });
}

/// 图表展示项
#[derive(Debug, Clone)]
struct ChartShowcaseItem {
    title: String,
    description: String,
    chart_type: ChartType,
    data_points: usize,
    features: Vec<String>,
}

/// 图表类型
#[derive(Debug, Clone, PartialEq)]
enum ChartType {
    InteractiveLine,     // 交互式折线图
    AnimatedBar,         // 动画柱状图
    StackedBar,          // 堆叠柱状图
    WaterfallChart,      // 瀑布图
    HorizontalBar,       // 水平柱状图
    MultiSeries,         // 多系列混合
    RealTimeDashboard,   // 实时仪表板
    OptimizedBigData,    // 大数据优化
}

impl ChartType {
    fn name(&self) -> &'static str {
        match self {
            ChartType::InteractiveLine => "交互式折线图",
            ChartType::AnimatedBar => "动画柱状图",
            ChartType::StackedBar => "堆叠柱状图",
            ChartType::WaterfallChart => "瀑布图",
            ChartType::HorizontalBar => "水平柱状图",
            ChartType::MultiSeries => "多系列混合",
            ChartType::RealTimeDashboard => "实时仪表板",
            ChartType::OptimizedBigData => "大数据优化",
        }
    }

    fn icon(&self) -> &'static str {
        match self {
            ChartType::InteractiveLine => "📈",
            ChartType::AnimatedBar => "📊",
            ChartType::StackedBar => "📚",
            ChartType::WaterfallChart => "🌊",
            ChartType::HorizontalBar => "📋",
            ChartType::MultiSeries => "🎯",
            ChartType::RealTimeDashboard => "⚡",
            ChartType::OptimizedBigData => "🚀",
        }
    }
}

/// 多图表展示应用
struct MultiChartShowcase {
    showcase_items: Vec<ChartShowcaseItem>,
    current_item: usize,
    renderer: GpuiRenderer,
    data_generator: DataGenerator,
    last_update: Instant,
    auto_cycle: bool,
    cycle_interval: Duration,
}

/// 数据生成器
struct DataGenerator {
    time_step: f64,
    counter: usize,
}

impl DataGenerator {
    fn new() -> Self {
        Self {
            time_step: 0.0,
            counter: 0,
        }
    }

    fn generate_line_data(&mut self, count: usize) -> Vec<(f64, f64)> {
        let mut data = Vec::new();
        for i in 0..count {
            let x = i as f64;
            let y = 50.0 +
                   (x * 0.5).sin() * 20.0 +
                   (x * 2.0).sin() * 10.0 +
                   ((self.counter + i) as f64 * 0.1).sin() * 5.0;
            data.push((x, y.max(0.0).min(100.0)));
        }
        self.time_step += 1.0;
        self.counter += count;
        data
    }

    fn generate_bar_data(&mut self, count: usize) -> Vec<(f64, f64)> {
        let mut data = Vec::new();
        let _categories = ["Q1", "Q2", "Q3", "Q4", "Q5", "Q6", "Q7", "Q8"];
        for i in 0..count.min(8) {
            let value = 20.0 + ((self.counter + i) as f64 * 0.1).sin() * 30.0 + 30.0;
            data.push((i as f64, value.max(20.0).min(80.0)));
        }
        self.counter += count;
        data
    }

    fn generate_waterfall_data(&mut self) -> Vec<(f64, f64)> {
        vec![
            (0.0, 100.0),  // 起始值
            (1.0, 20.0),   // 增加
            (2.0, -15.0),  // 减少
            (3.0, 30.0),   // 增加
            (4.0, -10.0),  // 减少
            (5.0, 25.0),   // 增加
            (6.0, 150.0),  // 最终值
        ]
    }

    fn generate_large_dataset(&mut self, count: usize) -> Vec<(f64, f64)> {
        let mut data = Vec::new();
        for i in 0..count {
            let x = i as f64 * 0.01;
            let y = 50.0 +
                   (x * 0.1).sin() * 30.0 +
                   (x * 1.0).sin() * 15.0 +
                   (x * 5.0).sin() * 5.0 +
                   ((self.counter + i) as f64 * 0.01).sin() * 2.0;
            data.push((x, y.max(0.0).min(100.0)));
        }
        self.counter += count;
        data
    }
}

impl MultiChartShowcase {
    fn new() -> Self {
        println!("🎯 初始化多图表展示...");
        
        let showcase_items = vec![
            ChartShowcaseItem {
                title: "交互式折线图".to_string(),
                description: "支持鼠标悬停、点击选择、缩放平移的智能折线图".to_string(),
                chart_type: ChartType::InteractiveLine,
                data_points: 15,
                features: vec![
                    "鼠标悬停高亮".to_string(),
                    "数据点选择".to_string(),
                    "平滑曲线".to_string(),
                    "自定义轴标签".to_string(),
                ],
            },
            ChartShowcaseItem {
                title: "动画柱状图".to_string(),
                description: "从底部向上生长的动画效果，支持多种缓动函数".to_string(),
                chart_type: ChartType::AnimatedBar,
                data_points: 8,
                features: vec![
                    "生长动画".to_string(),
                    "缓动效果".to_string(),
                    "数据标签".to_string(),
                    "渐变填充".to_string(),
                ],
            },
            ChartShowcaseItem {
                title: "堆叠柱状图".to_string(),
                description: "多系列数据堆叠显示，支持百分比堆叠模式".to_string(),
                chart_type: ChartType::StackedBar,
                data_points: 6,
                features: vec![
                    "多系列堆叠".to_string(),
                    "百分比模式".to_string(),
                    "图例交互".to_string(),
                    "颜色主题".to_string(),
                ],
            },
            ChartShowcaseItem {
                title: "瀑布图".to_string(),
                description: "展示数据的累积变化过程，适用于财务分析".to_string(),
                chart_type: ChartType::WaterfallChart,
                data_points: 7,
                features: vec![
                    "累积效果".to_string(),
                    "正负值区分".to_string(),
                    "连接线".to_string(),
                    "总计显示".to_string(),
                ],
            },
            ChartShowcaseItem {
                title: "水平柱状图".to_string(),
                description: "横向显示的柱状图，适合长标签和排名数据".to_string(),
                chart_type: ChartType::HorizontalBar,
                data_points: 10,
                features: vec![
                    "横向布局".to_string(),
                    "长标签支持".to_string(),
                    "排序功能".to_string(),
                    "右向生长动画".to_string(),
                ],
            },
            ChartShowcaseItem {
                title: "多系列混合图表".to_string(),
                description: "折线图和柱状图的组合，双Y轴显示".to_string(),
                chart_type: ChartType::MultiSeries,
                data_points: 12,
                features: vec![
                    "混合图表类型".to_string(),
                    "双Y轴".to_string(),
                    "系列联动".to_string(),
                    "统一图例".to_string(),
                ],
            },
            ChartShowcaseItem {
                title: "实时数据仪表板".to_string(),
                description: "模拟实时数据流，自动更新的动态仪表板".to_string(),
                chart_type: ChartType::RealTimeDashboard,
                data_points: 20,
                features: vec![
                    "实时更新".to_string(),
                    "数据流动画".to_string(),
                    "性能监控".to_string(),
                    "自适应缩放".to_string(),
                ],
            },
            ChartShowcaseItem {
                title: "大数据优化展示".to_string(),
                description: "10万数据点的LTTB算法优化，保持视觉效果".to_string(),
                chart_type: ChartType::OptimizedBigData,
                data_points: 100000,
                features: vec![
                    "LTTB算法".to_string(),
                    "Douglas-Peucker".to_string(),
                    "性能优化".to_string(),
                    "视觉保真".to_string(),
                ],
            },
        ];
        
        println!("📊 创建了 {} 个图表展示项", showcase_items.len());
        
        Self {
            showcase_items,
            current_item: 0,
            renderer: GpuiRenderer::new().with_debug(true),
            data_generator: DataGenerator::new(),
            last_update: Instant::now(),
            auto_cycle: false,
            cycle_interval: Duration::from_secs(5),
        }
    }

    /// 切换到下一个展示项
    fn next_item(&mut self) {
        self.current_item = (self.current_item + 1) % self.showcase_items.len();
        println!("🔄 切换到: {}", self.get_current_item().title);
    }

    /// 切换到上一个展示项
    fn prev_item(&mut self) {
        self.current_item = if self.current_item == 0 {
            self.showcase_items.len() - 1
        } else {
            self.current_item - 1
        };
        println!("🔄 切换到: {}", self.get_current_item().title);
    }

    /// 获取当前展示项
    fn get_current_item(&self) -> &ChartShowcaseItem {
        &self.showcase_items[self.current_item]
    }

    /// 自动循环更新
    fn update_auto_cycle(&mut self) {
        if !self.auto_cycle {
            return;
        }

        let now = Instant::now();
        if now.duration_since(self.last_update) >= self.cycle_interval {
            self.next_item();
            self.last_update = now;
        }
    }
}

impl Render for MultiChartShowcase {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("🎨 渲染多图表展示界面...");

        // 更新自动循环
        self.update_auto_cycle();

        let current_item = self.get_current_item();

        div()
            .size_full()
            .bg(rgb(0xf8fafc))
            .flex()
            .flex_col()
            .child(
                // 标题栏
                div()
                    .w_full()
                    .h(px(80.0))
                    .bg(rgb(0x1f2937))
                    .flex()
                    .items_center()
                    .justify_between()
                    .px_6()
                    .child(
                        div()
                            .flex()
                            .items_center()
                            .gap_4()
                            .child(
                                div()
                                    .text_3xl()
                                    .child(current_item.chart_type.icon())
                            )
                            .child(
                                div()
                                    .flex()
                                    .flex_col()
                                    .child(
                                        div()
                                            .text_xl()
                                            .font_bold()
                                            .text_color(rgb(0xffffff))
                                            .child("📊 ECharts-rs 完整功能展示")
                                    )
                                    .child(
                                        div()
                                            .text_sm()
                                            .text_color(rgb(0x9ca3af))
                                            .child(format!("{} / {}", self.current_item + 1, self.showcase_items.len()))
                                    )
                            )
                    )
                    .child(
                        div()
                            .flex()
                            .items_center()
                            .gap_2()
                            .child(
                                div()
                                    .px_3()
                                    .py_1()
                                    .bg(rgb(0x3b82f6))
                                    .rounded_md()
                                    .text_xs()
                                    .text_color(rgb(0xffffff))
                                    .child(format!("{} 数据点", current_item.data_points))
                            )
                            .child(
                                div()
                                    .px_3()
                                    .py_1()
                                    .bg(rgb(0x10b981))
                                    .rounded_md()
                                    .text_xs()
                                    .text_color(rgb(0xffffff))
                                    .child(if self.auto_cycle { "自动播放" } else { "手动控制" })
                            )
                    )
            )
            .child(
                // 导航栏
                div()
                    .w_full()
                    .h(px(60.0))
                    .bg(rgb(0xe5e7eb))
                    .flex()
                    .items_center()
                    .justify_center()
                    .gap_2()
                    .children(
                        self.showcase_items.iter().enumerate().map(|(i, item)| {
                            self.render_nav_item(i, item)
                        })
                    )
            )
            .child(
                // 当前图表信息
                div()
                    .w_full()
                    .p_6()
                    .bg(rgb(0xf3f4f6))
                    .child(
                        div()
                            .max_w(px(1200.0))
                            .mx_auto()
                            .flex()
                            .gap_8()
                            .child(
                                // 图表信息
                                div()
                                    .flex_1()
                                    .child(
                                        div()
                                            .text_2xl()
                                            .font_bold()
                                            .text_color(rgb(0x1f2937))
                                            .mb_2()
                                            .child(format!("{} {}", current_item.chart_type.icon(), current_item.title))
                                    )
                                    .child(
                                        div()
                                            .text_base()
                                            .text_color(rgb(0x6b7280))
                                            .mb_4()
                                            .child(&current_item.description)
                                    )
                                    .child(
                                        div()
                                            .flex()
                                            .flex_wrap()
                                            .gap_2()
                                            .children(
                                                current_item.features.iter().map(|feature| {
                                                    div()
                                                        .px_3()
                                                        .py_1()
                                                        .bg(rgb(0xdbeafe))
                                                        .text_color(rgb(0x1e40af))
                                                        .rounded_full()
                                                        .text_sm()
                                                        .child(format!("✨ {}", feature))
                                                })
                                            )
                                    )
                            )
                            .child(
                                // 控制按钮
                                div()
                                    .flex()
                                    .flex_col()
                                    .gap_2()
                                    .child(
                                        div()
                                            .px_4()
                                            .py_2()
                                            .bg(rgb(0x3b82f6))
                                            .text_color(rgb(0xffffff))
                                            .rounded_md()
                                            .text_sm()
                                            .cursor_pointer()
                                            .hover(|div| div.bg(rgb(0x2563eb)))
                                            .child("⏮️ 上一个")
                                    )
                                    .child(
                                        div()
                                            .px_4()
                                            .py_2()
                                            .bg(rgb(0x3b82f6))
                                            .text_color(rgb(0xffffff))
                                            .rounded_md()
                                            .text_sm()
                                            .cursor_pointer()
                                            .hover(|div| div.bg(rgb(0x2563eb)))
                                            .child("⏭️ 下一个")
                                    )
                                    .child(
                                        div()
                                            .px_4()
                                            .py_2()
                                            .bg(if self.auto_cycle { rgb(0xef4444) } else { rgb(0x10b981) })
                                            .text_color(rgb(0xffffff))
                                            .rounded_md()
                                            .text_sm()
                                            .cursor_pointer()
                                            .hover(|div| div.opacity(0.9))
                                            .child(if self.auto_cycle { "⏸️ 暂停" } else { "▶️ 自动播放" })
                                    )
                            )
                    )
            )
            .child(
                // 主图表区域
                div()
                    .flex_1()
                    .p_6()
                    .child(
                        div()
                            .w_full()
                            .h_full()
                            .bg(rgb(0xffffff))
                            .border_1()
                            .border_color(rgb(0xd1d5db))
                            .rounded_lg()
                            .p_6()
                            .flex()
                            .items_center()
                            .justify_center()
                            .child(
                                div()
                                    .w(px(900.0))
                                    .h(px(500.0))
                                    .bg(rgb(0xf9fafb))
                                    .border_1()
                                    .border_color(rgb(0xe5e7eb))
                                    .rounded_md()
                                    .flex()
                                    .items_center()
                                    .justify_center()
                                    .child(
                                        div()
                                            .text_center()
                                            .child(
                                                div()
                                                    .text_4xl()
                                                    .mb_4()
                                                    .child(current_item.chart_type.icon())
                                            )
                                            .child(
                                                div()
                                                    .text_xl()
                                                    .font_semibold()
                                                    .text_color(rgb(0x1f2937))
                                                    .mb_2()
                                                    .child(&current_item.title)
                                            )
                                            .child(
                                                div()
                                                    .text_sm()
                                                    .text_color(rgb(0x6b7280))
                                                    .child("🎨 GPUI Canvas 渲染区域")
                                            )
                                    )
                            )
                    )
            )
            .child(
                // 状态栏
                div()
                    .w_full()
                    .h(px(40.0))
                    .bg(rgb(0x374151))
                    .flex()
                    .items_center()
                    .justify_between()
                    .px_6()
                    .child(
                        div()
                            .text_xs()
                            .text_color(rgb(0x9ca3af))
                            .child(format!("✅ {} 已就绪 | 功能: {}",
                                current_item.title,
                                current_item.features.join(", ")
                            ))
                    )
                    .child(
                        div()
                            .text_xs()
                            .text_color(rgb(0x9ca3af))
                            .child("🚀 ECharts-rs v1.0 - 完整功能演示")
                    )
            )
    }
}

impl MultiChartShowcase {
    fn render_nav_item(&self, index: usize, item: &ChartShowcaseItem) -> impl IntoElement {
        let is_active = self.current_item == index;

        div()
            .px_3()
            .py_2()
            .rounded_md()
            .text_xs()
            .cursor_pointer()
            .flex()
            .items_center()
            .gap_1()
            .when(is_active, |div| {
                div.bg(rgb(0x3b82f6))
                   .text_color(rgb(0xffffff))
            })
            .when(!is_active, |div| {
                div.bg(rgb(0xffffff))
                   .text_color(rgb(0x374151))
                   .border_1()
                   .border_color(rgb(0xd1d5db))
                   .hover(|div| div.bg(rgb(0xf3f4f6)))
            })
            .child(
                div()
                    .text_sm()
                    .child(item.chart_type.icon())
            )
            .child(
                div()
                    .child(item.chart_type.name())
            )
    }
}
