//! 专业演示图表生成函数

use std::collections::HashMap;

/// 创建移动平均线图表
pub fn create_moving_average_chart(title: &str, data: &(Vec<(f64, f64)>, Vec<(String, Vec<(f64, f64)>, String)>), width: f64, height: f64) -> String {
    let (price_data, indicators) = data;
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 200.0;
    let chart_height = height - 120.0;
    
    // 绘制背景
    svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, chart_height));
    
    if !price_data.is_empty() {
        // 计算价格范围
        let mut all_values: Vec<f64> = price_data.iter().map(|(_, y)| *y).collect();
        for (_, indicator_data, _) in indicators {
            all_values.extend(indicator_data.iter().map(|(_, y)| *y));
        }
        
        let min_y = all_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_y = all_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let y_range = max_y - min_y;
        let max_x = price_data.len() as f64;
        
        // 绘制网格线
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));
            
            let value = max_y - (i as f64 / 5.0) * y_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.2}</text>\n", chart_x - 10.0, y + 4.0, value));
        }
        
        // 绘制价格曲线
        let mut price_path = String::from("M");
        for (i, (_, y)) in price_data.iter().enumerate() {
            let px = chart_x + (i as f64 / max_x) * chart_width;
            let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;
            
            if i == 0 {
                price_path.push_str(&format!(" {} {}", px, py));
            } else {
                price_path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\"/>\n", price_path));
        
        // 绘制移动平均线
        for (idx, (name, indicator_data, color)) in indicators.iter().enumerate() {
            if !indicator_data.is_empty() {
                let mut ma_path = String::from("M");
                
                for (i, (_, y)) in indicator_data.iter().enumerate() {
                    let px = chart_x + (i as f64 / max_x) * chart_width;
                    let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;
                    
                    if i == 0 {
                        ma_path.push_str(&format!(" {} {}", px, py));
                    } else {
                        ma_path.push_str(&format!(" L {} {}", px, py));
                    }
                }
                
                svg.push_str("  <path d=\"");
                svg.push_str(&ma_path);
                svg.push_str("\" stroke=\"#");
                svg.push_str(color);
                svg.push_str("\" stroke-width=\"2\" fill=\"none\"/>\n");
                
                // 图例
                let legend_y = chart_y + 30.0 + idx as f64 * 25.0;
                svg.push_str("  <line x1=\"");
                svg.push_str(&(chart_x + chart_width + 20.0).to_string());
                svg.push_str("\" y1=\"");
                svg.push_str(&legend_y.to_string());
                svg.push_str("\" x2=\"");
                svg.push_str(&(chart_x + chart_width + 40.0).to_string());
                svg.push_str("\" y2=\"");
                svg.push_str(&legend_y.to_string());
                svg.push_str("\" stroke=\"#");
                svg.push_str(color);
                svg.push_str("\" stroke-width=\"2\"/>\n");
                svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">{}</text>\n", chart_x + chart_width + 45.0, legend_y + 4.0, name));
            }
        }
        
        // 价格图例
        svg.push_str("  <line x1=\"");
        svg.push_str(&(chart_x + chart_width + 20.0).to_string());
        svg.push_str("\" y1=\"");
        svg.push_str(&(chart_y + 5.0).to_string());
        svg.push_str("\" x2=\"");
        svg.push_str(&(chart_x + chart_width + 40.0).to_string());
        svg.push_str("\" y2=\"");
        svg.push_str(&(chart_y + 5.0).to_string());
        svg.push_str("\" stroke=\"#333\" stroke-width=\"2\"/>\n");
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">价格</text>\n", chart_x + chart_width + 45.0, chart_y + 9.0));
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建RSI指标图表
pub fn create_rsi_indicator_chart(title: &str, data: &(Vec<(f64, f64)>, Vec<(f64, f64)>), width: f64, height: f64) -> String {
    let (price_data, rsi_data) = data;
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 价格图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let price_height = height * 0.6;
    let rsi_height = height * 0.3;
    let gap = 20.0;
    
    // 绘制价格图表
    if !price_data.is_empty() {
        let min_price = price_data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_price = price_data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        let price_range = max_price - min_price;
        let max_x = price_data.len() as f64;
        
        // 价格背景
        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, price_height));
        
        // 价格网格线
        for i in 0..=4 {
            let y = chart_y + (i as f64 / 4.0) * price_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));
            
            let price = max_price - (i as f64 / 4.0) * price_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.2}</text>\n", chart_x - 10.0, y + 4.0, price));
        }
        
        // 绘制价格曲线
        let mut price_path = String::from("M");
        for (i, (_, y)) in price_data.iter().enumerate() {
            let px = chart_x + (i as f64 / max_x) * chart_width;
            let py = chart_y + price_height - ((y - min_price) / price_range) * price_height;
            
            if i == 0 {
                price_path.push_str(&format!(" {} {}", px, py));
            } else {
                price_path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#2196f3\" stroke-width=\"2\" fill=\"none\"/>\n", price_path));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"14\" font-weight=\"bold\" fill=\"#333\">价格</text>\n", chart_x, chart_y - 10.0));
    }
    
    // 绘制RSI图表
    if !rsi_data.is_empty() {
        let rsi_y = chart_y + price_height + gap;
        
        // RSI背景
        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, rsi_y, chart_width, rsi_height));
        
        // RSI参考线
        let rsi_70 = rsi_y + rsi_height * 0.3;
        let rsi_30 = rsi_y + rsi_height * 0.7;
        let rsi_50 = rsi_y + rsi_height * 0.5;
        
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#ff5722\" stroke-width=\"1\" stroke-dasharray=\"3,3\"/>\n", chart_x, rsi_70, chart_x + chart_width, rsi_70));
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#4caf50\" stroke-width=\"1\" stroke-dasharray=\"3,3\"/>\n", chart_x, rsi_30, chart_x + chart_width, rsi_30));
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#999\" stroke-width=\"1\" stroke-dasharray=\"2,2\"/>\n", chart_x, rsi_50, chart_x + chart_width, rsi_50));
        
        // RSI标签
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"10\" fill=\"#ff5722\">70</text>\n", chart_x - 5.0, rsi_70 + 3.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"10\" fill=\"#999\">50</text>\n", chart_x - 5.0, rsi_50 + 3.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"10\" fill=\"#4caf50\">30</text>\n", chart_x - 5.0, rsi_30 + 3.0));
        
        // 绘制RSI曲线
        let max_x = rsi_data.len() as f64;
        let mut rsi_path = String::from("M");
        
        for (i, (_, rsi)) in rsi_data.iter().enumerate() {
            let px = chart_x + (i as f64 / max_x) * chart_width;
            let py = rsi_y + rsi_height - (rsi / 100.0) * rsi_height;
            
            if i == 0 {
                rsi_path.push_str(&format!(" {} {}", px, py));
            } else {
                rsi_path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#9c27b0\" stroke-width=\"2\" fill=\"none\"/>\n", rsi_path));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"14\" font-weight=\"bold\" fill=\"#333\">RSI</text>\n", chart_x, rsi_y - 10.0));
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建KPI仪表板图表
pub fn create_kpi_dashboard_chart(title: &str, data: &[super::KpiMetric], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 定义渐变
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"positiveGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#4caf50;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#8bc34a;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("    <linearGradient id=\"negativeGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#f44336;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#ff5722;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f5f5f5\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"24\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 计算布局
    let cols = 2;
    let rows = (data.len() + cols - 1) / cols;
    let kpi_width = (width - 100.0) / cols as f64;
    let kpi_height = (height - 100.0) / rows as f64;
    
    for (idx, kpi) in data.iter().enumerate() {
        let col = idx % cols;
        let row = idx / cols;
        
        let x = 50.0 + col as f64 * kpi_width;
        let y = 60.0 + row as f64 * kpi_height;
        
        // KPI卡片背景
        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\" rx=\"8\"/>\n", 
            x, y, kpi_width - 20.0, kpi_height - 20.0));
        
        // KPI名称
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"16\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", 
            x + 20.0, y + 30.0, kpi.name));
        
        // 当前值
        let is_positive = kpi.current_value >= kpi.target_value;
        let value_color = if is_positive { "#4caf50" } else { "#f44336" };
        
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"32\" font-weight=\"bold\" fill=\"{}\">{:.0}{}</text>\n", 
            x + 20.0, y + 70.0, value_color, kpi.current_value, kpi.unit));
        
        // 目标值
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"14\" fill=\"#666\">目标: {:.0}{}</text>\n", 
            x + 20.0, y + 90.0, kpi.target_value, kpi.unit));
        
        // 趋势图
        if !kpi.trend.is_empty() {
            let trend_x = x + 20.0;
            let trend_y = y + 110.0;
            let trend_width = kpi_width - 60.0;
            let trend_height = 60.0;
            
            let min_trend = kpi.trend.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
            let max_trend = kpi.trend.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
            let trend_range = max_trend - min_trend;
            
            let mut trend_path = String::from("M");
            
            for (i, (_, value)) in kpi.trend.iter().enumerate() {
                let px = trend_x + (i as f64 / kpi.trend.len() as f64) * trend_width;
                let py = trend_y + trend_height - ((value - min_trend) / trend_range) * trend_height;
                
                if i == 0 {
                    trend_path.push_str(&format!(" {} {}", px, py));
                } else {
                    trend_path.push_str(&format!(" L {} {}", px, py));
                }
            }
            
            svg.push_str(&format!("  <path d=\"{}\" stroke=\"{}\" stroke-width=\"2\" fill=\"none\"/>\n", trend_path, value_color));
        }
    }
    
    svg.push_str("</svg>");
    svg
}
