//! 矩形树图SVG演示
//!
//! 生成各种矩形树图的SVG文件，展示TreemapSeries的完整功能

use std::fs;
use echarts_rs::{TreemapSeries, TreemapDataItem, TreemapAlgorithm, TreemapLabel, TreemapLabelPosition, Color};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🌳 矩形树图SVG演示系统");
    println!("{}", "=".repeat(50));

    // 确保输出目录存在
    let output_dir = "temp/svg/treemap_charts";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 基础矩形树图
    println!("\n🌳 1. 生成基础矩形树图...");
    generate_basic_treemap(output_dir)?;

    // 2. 层次数据矩形树图
    println!("\n📊 2. 生成层次数据矩形树图...");
    generate_hierarchical_treemap(output_dir)?;

    // 3. 文件系统矩形树图
    println!("\n📁 3. 生成文件系统矩形树图...");
    generate_filesystem_treemap(output_dir)?;

    // 4. 公司组织架构矩形树图
    println!("\n🏢 4. 生成公司组织架构矩形树图...");
    generate_organization_treemap(output_dir)?;

    // 5. 不同算法对比矩形树图
    println!("\n🔄 5. 生成不同算法对比矩形树图...");
    generate_algorithm_comparison_treemap(output_dir)?;

    // 6. 生成展示页面
    println!("\n📄 6. 生成展示页面...");
    generate_treemap_showcase(output_dir)?;

    println!("\n🎉 矩形树图SVG演示生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/treemap_demo.html 查看演示", output_dir);

    Ok(())
}

/// 生成基础矩形树图
fn generate_basic_treemap(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        TreemapDataItem::new("产品A", 40.0).color(Color::rgb(0.3, 0.6, 1.0)),
        TreemapDataItem::new("产品B", 30.0).color(Color::rgb(0.6, 0.8, 0.4)),
        TreemapDataItem::new("产品C", 20.0).color(Color::rgb(1.0, 0.6, 0.3)),
        TreemapDataItem::new("产品D", 10.0).color(Color::rgb(0.8, 0.4, 0.8)),
    ];

    let treemap_series = TreemapSeries::new("基础矩形树图")
        .data(data)
        .algorithm(TreemapAlgorithm::Squarify)
        .gap(2.0)
        .border(1.0, Color::rgb(0.8, 0.8, 0.8));

    let svg = create_treemap_svg(&treemap_series, "基础矩形树图演示", 600.0, 400.0)?;
    fs::write(format!("{}/01_basic_treemap.svg", output_dir), svg)?;
    
    println!("  ✅ 基础矩形树图生成完成");
    Ok(())
}

/// 生成层次数据矩形树图
fn generate_hierarchical_treemap(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        TreemapDataItem::new("技术部", 0.0)
            .add_child(TreemapDataItem::new("前端", 25.0).color(Color::rgb(0.2, 0.6, 1.0)))
            .add_child(TreemapDataItem::new("后端", 35.0).color(Color::rgb(0.4, 0.8, 0.6)))
            .add_child(TreemapDataItem::new("移动端", 20.0).color(Color::rgb(0.8, 0.6, 0.4)))
            .add_child(TreemapDataItem::new("测试", 15.0).color(Color::rgb(1.0, 0.4, 0.6))),
        TreemapDataItem::new("市场部", 0.0)
            .add_child(TreemapDataItem::new("推广", 20.0).color(Color::rgb(0.6, 0.4, 1.0)))
            .add_child(TreemapDataItem::new("销售", 30.0).color(Color::rgb(1.0, 0.8, 0.2)))
            .add_child(TreemapDataItem::new("客服", 15.0).color(Color::rgb(0.4, 1.0, 0.8))),
    ];

    let treemap_series = TreemapSeries::new("层次数据矩形树图")
        .data(data)
        .algorithm(TreemapAlgorithm::Binary)
        .max_depth(2)
        .gap(3.0)
        .border(2.0, Color::rgb(0.6, 0.6, 0.6));

    let svg = create_treemap_svg(&treemap_series, "层次数据矩形树图", 600.0, 400.0)?;
    fs::write(format!("{}/02_hierarchical_treemap.svg", output_dir), svg)?;
    
    println!("  ✅ 层次数据矩形树图生成完成");
    Ok(())
}

/// 生成文件系统矩形树图
fn generate_filesystem_treemap(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        TreemapDataItem::new("src", 0.0)
            .add_child(TreemapDataItem::new("main.rs", 5.2).color(Color::rgb(1.0, 0.6, 0.2)))
            .add_child(TreemapDataItem::new("lib.rs", 8.5).color(Color::rgb(1.0, 0.6, 0.2)))
            .add_child(TreemapDataItem::new("utils.rs", 3.8).color(Color::rgb(1.0, 0.6, 0.2)))
            .add_child(TreemapDataItem::new("config.rs", 2.1).color(Color::rgb(1.0, 0.6, 0.2))),
        TreemapDataItem::new("tests", 0.0)
            .add_child(TreemapDataItem::new("unit_tests.rs", 4.2).color(Color::rgb(0.4, 0.8, 0.4)))
            .add_child(TreemapDataItem::new("integration_tests.rs", 6.8).color(Color::rgb(0.4, 0.8, 0.4))),
        TreemapDataItem::new("docs", 0.0)
            .add_child(TreemapDataItem::new("README.md", 1.5).color(Color::rgb(0.4, 0.6, 1.0)))
            .add_child(TreemapDataItem::new("API.md", 2.8).color(Color::rgb(0.4, 0.6, 1.0))),
        TreemapDataItem::new("assets", 0.0)
            .add_child(TreemapDataItem::new("images", 12.5).color(Color::rgb(0.8, 0.4, 0.8)))
            .add_child(TreemapDataItem::new("fonts", 3.2).color(Color::rgb(0.8, 0.4, 0.8))),
    ];

    let label = TreemapLabel {
        show: true,
        font_size: 10.0,
        color: Color::rgb(0.2, 0.2, 0.2),
        position: TreemapLabelPosition::TopLeft,
        min_area: 50.0,
    };

    let treemap_series = TreemapSeries::new("文件系统矩形树图")
        .data(data)
        .algorithm(TreemapAlgorithm::Squarify)
        .label(label)
        .max_depth(2)
        .gap(1.0)
        .border(1.0, Color::rgb(0.9, 0.9, 0.9));

    let svg = create_treemap_svg(&treemap_series, "文件系统矩形树图", 600.0, 400.0)?;
    fs::write(format!("{}/03_filesystem_treemap.svg", output_dir), svg)?;
    
    println!("  ✅ 文件系统矩形树图生成完成");
    Ok(())
}

/// 生成公司组织架构矩形树图
fn generate_organization_treemap(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        TreemapDataItem::new("CEO", 0.0)
            .add_child(
                TreemapDataItem::new("技术VP", 0.0)
                    .add_child(TreemapDataItem::new("研发总监", 45.0).color(Color::rgb(0.2, 0.6, 1.0)))
                    .add_child(TreemapDataItem::new("架构师", 25.0).color(Color::rgb(0.4, 0.8, 0.6)))
                    .add_child(TreemapDataItem::new("运维总监", 20.0).color(Color::rgb(0.6, 0.4, 1.0)))
            )
            .add_child(
                TreemapDataItem::new("市场VP", 0.0)
                    .add_child(TreemapDataItem::new("销售总监", 35.0).color(Color::rgb(1.0, 0.6, 0.3)))
                    .add_child(TreemapDataItem::new("市场总监", 25.0).color(Color::rgb(1.0, 0.8, 0.2)))
                    .add_child(TreemapDataItem::new("品牌总监", 15.0).color(Color::rgb(0.8, 0.4, 0.8)))
            )
            .add_child(
                TreemapDataItem::new("运营VP", 0.0)
                    .add_child(TreemapDataItem::new("产品总监", 30.0).color(Color::rgb(0.4, 1.0, 0.8)))
                    .add_child(TreemapDataItem::new("运营总监", 20.0).color(Color::rgb(1.0, 0.4, 0.6)))
            ),
    ];

    let label = TreemapLabel {
        show: true,
        font_size: 11.0,
        color: Color::rgb(0.1, 0.1, 0.1),
        position: TreemapLabelPosition::Center,
        min_area: 100.0,
    };

    let treemap_series = TreemapSeries::new("公司组织架构")
        .data(data)
        .algorithm(TreemapAlgorithm::Binary)
        .label(label)
        .max_depth(3)
        .gap(2.0)
        .border(1.5, Color::rgb(0.7, 0.7, 0.7));

    let svg = create_treemap_svg(&treemap_series, "公司组织架构矩形树图", 600.0, 400.0)?;
    fs::write(format!("{}/04_organization_treemap.svg", output_dir), svg)?;
    
    println!("  ✅ 公司组织架构矩形树图生成完成");
    Ok(())
}

/// 生成不同算法对比矩形树图
fn generate_algorithm_comparison_treemap(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        TreemapDataItem::new("算法A", 25.0).color(Color::rgb(0.8, 0.2, 0.2)),
        TreemapDataItem::new("算法B", 35.0).color(Color::rgb(0.2, 0.8, 0.2)),
        TreemapDataItem::new("算法C", 20.0).color(Color::rgb(0.2, 0.2, 0.8)),
        TreemapDataItem::new("算法D", 15.0).color(Color::rgb(0.8, 0.8, 0.2)),
        TreemapDataItem::new("算法E", 5.0).color(Color::rgb(0.8, 0.2, 0.8)),
    ];

    // 使用条带算法
    let treemap_series = TreemapSeries::new("条带算法对比")
        .data(data)
        .algorithm(TreemapAlgorithm::Strip)
        .gap(1.5)
        .border(2.0, Color::rgb(0.5, 0.5, 0.5));

    let svg = create_treemap_svg(&treemap_series, "不同算法对比矩形树图", 600.0, 400.0)?;
    fs::write(format!("{}/05_algorithm_comparison_treemap.svg", output_dir), svg)?;
    
    println!("  ✅ 不同算法对比矩形树图生成完成");
    Ok(())
}

/// 创建矩形树图SVG
fn create_treemap_svg(series: &TreemapSeries, title: &str, width: f64, height: f64) -> std::result::Result<String, Box<dyn std::error::Error>> {
    use echarts_core::{CartesianCoordinateSystem, Bounds, Series, Point, Size};
    
    // 创建坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        Bounds {
            origin: Point { x: 50.0, y: 50.0 },
            size: Size { width: width - 100.0, height: height - 100.0 },
        },
        (0.0, 1.0),
        (0.0, 1.0),
    );
    
    // 获取渲染命令
    let commands = series.render_to_commands(&coord_system)?;
    
    // 生成SVG
    let mut svg = String::new();
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 渲染命令
    for command in commands {
        render_treemap_svg_command(&mut svg, &command);
    }
    
    svg.push_str("</svg>");
    Ok(svg)
}

/// 渲染矩形树图SVG命令
fn render_treemap_svg_command(svg: &mut String, command: &echarts_core::DrawCommand) {
    use echarts_core::DrawCommand;
    
    match command {
        DrawCommand::Rect { bounds, style } => {
            let fill = style.fill.map(|c| format!("#{:02x}{:02x}{:02x}", 
                (c.r * 255.0) as u8, (c.g * 255.0) as u8, (c.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke = style.stroke.as_ref().map(|s| format!("#{:02x}{:02x}{:02x}", 
                (s.color.r * 255.0) as u8, (s.color.g * 255.0) as u8, (s.color.b * 255.0) as u8)).unwrap_or("none".to_string());
            let stroke_width = style.stroke.as_ref().map(|s| s.width).unwrap_or(0.0);
            
            svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"{}\" stroke=\"{}\" stroke-width=\"{}\" opacity=\"{}\" rx=\"{}\"/>\n", 
                bounds.origin.x, bounds.origin.y, bounds.size.width, bounds.size.height, 
                fill, stroke, stroke_width, style.opacity, style.corner_radius));
        }
        DrawCommand::Text { text, position, style } => {
            let color = format!("#{:02x}{:02x}{:02x}", 
                (style.color.r * 255.0) as u8, (style.color.g * 255.0) as u8, (style.color.b * 255.0) as u8);
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"{}\" fill=\"{}\" text-anchor=\"middle\" opacity=\"{}\">{}</text>\n", 
                position.x, position.y, style.font_size, color, style.opacity, text));
        }
        _ => {} // 忽略其他命令类型
    }
}

/// 生成展示页面
fn generate_treemap_showcase(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts-rs 矩形树图演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .section {
            background: rgba(255,255,255,0.95);
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .chart-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        .chart-svg {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h2 { color: #333; margin-bottom: 20px; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        .description {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .algorithm-info {
            background: rgba(255,255,255,0.05);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌳 ECharts-rs 矩形树图演示</h1>
            <p class="description">展现 TreemapSeries 的强大功能和层次数据可视化能力</p>
            <div class="feature-list">
                <div class="feature">
                    <h3>🌳 基础矩形树图</h3>
                    <p>经典的面积映射设计</p>
                </div>
                <div class="feature">
                    <h3>📊 层次数据</h3>
                    <p>多层级数据结构展示</p>
                </div>
                <div class="feature">
                    <h3>📁 文件系统</h3>
                    <p>文件大小可视化</p>
                </div>
                <div class="feature">
                    <h3>🔄 多种算法</h3>
                    <p>不同布局算法对比</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🌳 基础矩形树图功能</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">基础矩形树图</div>
                    <object class="chart-svg" data="01_basic_treemap.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">层次数据矩形树图</div>
                    <object class="chart-svg" data="02_hierarchical_treemap.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📁 实际应用场景</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">文件系统矩形树图</div>
                    <object class="chart-svg" data="03_filesystem_treemap.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">公司组织架构矩形树图</div>
                    <object class="chart-svg" data="04_organization_treemap.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔄 算法对比</h2>
            <div class="chart-item">
                <div class="chart-title">不同算法对比矩形树图</div>
                <object class="chart-svg" data="05_algorithm_comparison_treemap.svg" type="image/svg+xml">SVG不支持</object>
            </div>

            <div class="algorithm-info">
                <h3>🧮 布局算法说明</h3>
                <ul>
                    <li><strong>Squarify（方形化）</strong>：尽可能生成接近正方形的矩形，视觉效果最佳</li>
                    <li><strong>Binary（二分法）</strong>：递归二分空间，适合层次结构清晰的数据</li>
                    <li><strong>Strip（条带）</strong>：按条带排列，适合时间序列或有序数据</li>
                </ul>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h2>🎉 TreemapSeries 功能总结</h2>
            <p>ECharts-rs TreemapSeries 完整功能展示：</p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>✅ <strong>层次数据可视化</strong> - 支持多层级数据结构展示</li>
                <li>✅ <strong>面积映射算法</strong> - 数值大小直观的面积表示</li>
                <li>✅ <strong>多种布局算法</strong> - Squarify、Binary、Strip算法</li>
                <li>✅ <strong>灵活的标签系统</strong> - 可配置的标签位置和显示条件</li>
                <li>✅ <strong>自定义样式配置</strong> - 颜色、边框、间隙等样式设置</li>
                <li>✅ <strong>层级深度控制</strong> - 可配置的最大显示层级</li>
                <li>✅ <strong>响应式布局</strong> - 自适应容器大小的布局算法</li>
                <li>✅ <strong>高质量渲染</strong> - 优化的SVG输出和视觉效果</li>
            </ul>
        </div>
    </div>
</body>
</html>"#;

    fs::write(format!("{}/treemap_demo.html", output_dir), html_content)?;
    Ok(())
}
