---
type: "agent_requested"
description: "Example description"
---
# 提交

创建格式良好的提交，使用常规提交消息和表情符号。

## 功能：
- 默认运行预提交检查（lint、构建、生成文档）
- 如果没有暂存文件，自动暂存文件
- 使用带有描述性表情符号的常规提交格式
- 建议将不同关注点的更改分拆成单独的提交

## 用法：
- `/commit` - 带预提交检查的标准提交
- `/commit --no-verify` - 跳过预提交检查

## 提交类型：
- ✨ feat: 新功能
- 🐛 fix: Bug修复
- 📝 docs: 文档更改
- ♻️ refactor: 代码重构（不改变功能）
- 🎨 style: 代码格式化，缺少分号等
- ⚡️ perf: 性能改进
- ✅ test: 添加或修正测试
- 🧑‍💻 chore: 工具、配置、维护
- 🚧 wip: 进行中的工作
- 🔥 remove: 删除代码或文件
- 🚑 hotfix: 紧急修复
- 🔒 security: 安全改进

## 流程：
1. 检查暂存的更改（`git status`）
2. 如果没有暂存的更改，审查并暂存适当的文件
3. 运行预提交检查（除非使用 --no-verify）
4. 分析更改以确定提交类型
5. 生成描述性提交消息
6. 如适用，包含范围：`type(scope): description`
7. 对于复杂更改，添加正文解释原因
8. 执行提交

## 最佳实践：
- 保持提交原子性和专注性
- 使用祈使语气（"添加功能"而非"已添加功能"）
- 解释为什么，而不仅仅是什么
- 在相关时引用问题/PR
- 将无关的更改分拆成单独的提交