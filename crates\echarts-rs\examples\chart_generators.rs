// 曲线图生成器函数
// 包含各种特殊效果的曲线图生成函数

/// 创建基础曲线图
pub fn create_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64, color: &str) -> String {
    let mut svg = String::new();
    
    // SVG 头部
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 定义渐变和滤镜
    svg.push_str("  <defs>\n");
    svg.push_str(&format!("    <linearGradient id=\"areaGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n"));
    svg.push_str(&format!("      <stop offset=\"0%\" style=\"stop-color:#{};stop-opacity:0.3\" />\n", color));
    svg.push_str(&format!("      <stop offset=\"100%\" style=\"stop-color:#{};stop-opacity:0.1\" />\n", color));
    svg.push_str("    </linearGradient>\n");
    svg.push_str("    <filter id=\"shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n");
    svg.push_str("      <feDropShadow dx=\"2\" dy=\"2\" stdDeviation=\"2\" flood-color=\"#000000\" flood-opacity=\"0.2\"/>\n");
    svg.push_str("    </filter>\n");
    svg.push_str("  </defs>\n");
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));
    
    if !data.is_empty() {
        // 计算数据范围
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        
        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };
        
        // 添加网格线
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", x, chart_y, x, chart_y + chart_height));
        }
        
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));
        }
        
        // 生成路径
        let mut path = String::from("M");
        let mut area_path = String::from("M");
        
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
                area_path.push_str(&format!(" {} {}", px, chart_y + chart_height));
                area_path.push_str(&format!(" L {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
                area_path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        // 完成面积路径
        if let Some((last_x, _)) = data.last() {
            let last_px = chart_x + (last_x - min_x) / x_range * chart_width;
            area_path.push_str(&format!(" L {} {} Z", last_px, chart_y + chart_height));
        }
        
        // 绘制面积
        svg.push_str(&format!("  <path d=\"{}\" fill=\"url(#areaGradient)\"/>\n", area_path));
        
        // 绘制曲线
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#{}\" stroke-width=\"3\" fill=\"none\" filter=\"url(#shadow)\"/>\n", path, color));
        
        // 绘制数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#{}\" stroke=\"white\" stroke-width=\"2\" filter=\"url(#shadow)\"/>\n", px, py, color));
        }
        
        // 添加坐标轴标签
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            let value = min_x + (i as f64 / 5.0) * x_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{:.1}</text>\n", x, chart_y + chart_height + 20.0, value));
        }
        
        for i in 0..=5 {
            let y = chart_y + chart_height - (i as f64 / 5.0) * chart_height;
            let value = min_y + (i as f64 / 5.0) * y_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n", chart_x - 10.0, y + 4.0, value));
        }
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建渐变色曲线图
pub fn create_gradient_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 定义多色渐变
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"multiGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#ff6b6b;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"25%\" style=\"stop-color:#4ecdc4;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"50%\" style=\"stop-color:#45b7d1;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"75%\" style=\"stop-color:#f9ca24;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#6c5ce7;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");
    
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));
    
    if !data.is_empty() {
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        
        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };
        
        // 生成路径
        let mut path = String::from("M");
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        // 绘制渐变曲线
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"url(#multiGradient)\" stroke-width=\"5\" fill=\"none\"/>\n", path));
        
        // 绘制渐变数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"5\" fill=\"url(#multiGradient)\" stroke=\"white\" stroke-width=\"2\"/>\n", px, py));
        }
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建虚线曲线图
pub fn create_dashed_line_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height));
    svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", chart_x, chart_y, chart_x, chart_y + chart_height));
    
    if !data.is_empty() {
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        
        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };
        
        // 生成路径
        let mut path = String::from("M");
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        // 绘制虚线曲线
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#e74c3c\" stroke-width=\"3\" stroke-dasharray=\"10,5\" fill=\"none\"/>\n", path));
        
        // 绘制方形数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"8\" height=\"8\" fill=\"#e74c3c\" stroke=\"white\" stroke-width=\"2\" transform=\"translate(-4,-4)\"/>\n", px, py));
        }
    }
    
    svg.push_str("</svg>");
    svg
}
