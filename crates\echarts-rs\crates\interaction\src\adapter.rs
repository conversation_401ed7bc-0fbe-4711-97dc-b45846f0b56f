//! 交互适配层
//!
//! 负责在底层通用交互事件和中层图表特定交互事件之间进行转换和协调

use crate::KeyModifiers;
use echarts_core::{Point, Bounds, professional_interactions::{
    InteractionEvent as BaseInteractionEvent, 
    InteractionResponse as BaseInteractionResponse,
    InteractionController,
    MouseButton as BaseMouseButton,
    KeyModifiers as BaseKeyModifiers,
}};
use std::collections::HashMap;

/// 图表特定交互事件
#[derive(Debug, Clone)]
pub enum ChartInteractionEvent {
    /// 数据点悬停
    PointHover {
        chart_id: String,
        series_index: usize,
        point_index: usize,
        position: Point,
        data_value: (f64, f64),
    },
    
    /// 数据点点击
    PointClick {
        chart_id: String,
        series_index: usize,
        point_index: usize,
        position: Point,
        data_value: (f64, f64),
        modifiers: KeyModifiers,
    },
    
    /// 数据点离开
    PointLeave {
        chart_id: String,
    },
    
    /// 系列悬停
    SeriesHover {
        chart_id: String,
        series_index: usize,
    },
    
    /// 系列离开
    SeriesLeave {
        chart_id: String,
    },
    
    /// 区域选择
    AreaSelection {
        chart_id: String,
        selection_bounds: Bounds,
        selected_points: Vec<(usize, usize)>, // (series_index, point_index)
    },
    
    /// 缩放请求
    ZoomRequest {
        chart_id: String,
        zoom_factor: f64,
        center_point: Point,
    },
    
    /// 平移请求
    PanRequest {
        chart_id: String,
        delta: Point,
    },
}

/// 图表交互结果
#[derive(Debug, Clone)]
pub enum ChartInteractionResult {
    /// 无操作
    None,
    
    /// 需要重绘
    Redraw {
        chart_id: String,
    },
    
    /// 显示工具提示
    ShowTooltip {
        chart_id: String,
        content: AdapterTooltipContent,
        position: Point,
    },
    
    /// 隐藏工具提示
    HideTooltip {
        chart_id: String,
    },
    
    /// 高亮变化
    HighlightChanged {
        chart_id: String,
        highlighted_points: Vec<(usize, usize)>,
    },
    
    /// 选择变化
    SelectionChanged {
        chart_id: String,
        selected_points: Vec<(usize, usize)>,
    },
    
    /// 视口变化
    ViewportChanged {
        chart_id: String,
        new_bounds: Bounds,
        zoom_level: f64,
    },
}

/// 工具提示内容 (适配层版本)
#[derive(Debug, Clone)]
pub struct AdapterTooltipContent {
    pub title: String,
    pub items: Vec<AdapterTooltipItem>,
    pub background_color: echarts_core::Color,
    pub text_color: echarts_core::Color,
}

/// 工具提示项 (适配层版本)
#[derive(Debug, Clone)]
pub struct AdapterTooltipItem {
    pub label: String,
    pub value: String,
    pub color: Option<echarts_core::Color>,
    pub series_name: String,
}

/// 图表交互特征
pub trait ChartInteraction {
    /// 获取图表ID
    fn chart_id(&self) -> &str;
    
    /// 处理图表特定事件
    fn handle_chart_event(&mut self, event: ChartInteractionEvent) -> ChartInteractionResult;
    
    /// 查找指定位置的数据点
    fn find_point_at(&self, position: Point, tolerance: f64) -> Option<(usize, usize, (f64, f64))>;
    
    /// 获取数据点的屏幕位置
    fn get_point_position(&self, series_index: usize, point_index: usize) -> Option<Point>;
    
    /// 检查点是否在图表区域内
    fn contains_point(&self, position: Point) -> bool;
    
    /// 获取图表边界
    fn chart_bounds(&self) -> Bounds;
}

/// 交互适配器
pub struct InteractionAdapter {
    /// 注册的图表交互控制器
    chart_controllers: HashMap<String, Box<dyn ChartInteraction>>,
    
    /// 底层交互控制器
    base_controllers: Vec<Box<dyn InteractionController>>,
    
    /// 事件优先级配置
    event_priority: EventPriorityConfig,
    
    /// 当前悬停状态
    current_hover: Option<HoverState>,
    
    /// 当前选择状态
    current_selection: SelectionState,
    
    /// 交互历史（用于手势识别等）
    interaction_history: Vec<InteractionHistoryItem>,
}

/// 事件优先级配置
#[derive(Debug, Clone)]
pub struct EventPriorityConfig {
    /// 图表特定事件优先级
    pub chart_events_priority: u8,
    
    /// 通用交互事件优先级
    pub base_events_priority: u8,
    
    /// 是否允许事件冒泡
    pub allow_event_bubbling: bool,
}

/// 悬停状态
#[derive(Debug, Clone)]
struct HoverState {
    chart_id: String,
    series_index: usize,
    point_index: usize,
    position: Point,
}

/// 选择状态
#[derive(Debug, Clone, Default)]
struct SelectionState {
    selected_points: HashMap<String, Vec<(usize, usize)>>, // chart_id -> points
}

/// 交互历史项
#[derive(Debug, Clone)]
struct InteractionHistoryItem {
    timestamp: std::time::Instant,
    event_type: String,
    position: Point,
}

impl InteractionAdapter {
    /// 创建新的交互适配器
    pub fn new() -> Self {
        Self {
            chart_controllers: HashMap::new(),
            base_controllers: Vec::new(),
            event_priority: EventPriorityConfig {
                chart_events_priority: 100,
                base_events_priority: 50,
                allow_event_bubbling: true,
            },
            current_hover: None,
            current_selection: SelectionState::default(),
            interaction_history: Vec::new(),
        }
    }
    
    /// 注册图表交互控制器
    pub fn register_chart(&mut self, controller: Box<dyn ChartInteraction>) {
        let chart_id = controller.chart_id().to_string();
        self.chart_controllers.insert(chart_id, controller);
    }
    
    /// 注册底层交互控制器
    pub fn register_base_controller(&mut self, controller: Box<dyn InteractionController>) {
        self.base_controllers.push(controller);
    }
    
    /// 处理输入事件
    pub fn handle_input_event(&mut self, event: BaseInteractionEvent) -> Vec<ChartInteractionResult> {
        let mut results = Vec::new();
        
        // 记录交互历史
        self.record_interaction(&event);
        
        // 首先尝试转换为图表特定事件
        if let Some(chart_events) = self.convert_to_chart_events(&event) {
            for chart_event in chart_events {
                if let Some(result) = self.handle_chart_event(chart_event) {
                    results.push(result);
                }
            }
        }
        
        // 如果允许事件冒泡，处理底层事件
        if self.event_priority.allow_event_bubbling || results.is_empty() {
            let responses: Vec<_> = self.base_controllers.iter_mut()
                .map(|controller| controller.handle_event(event.clone()))
                .collect();

            for response in responses {
                if let Some(result) = self.convert_base_response(response) {
                    results.push(result);
                }
            }
        }
        
        results
    }
    
    /// 转换底层事件为图表特定事件
    fn convert_to_chart_events(&self, event: &BaseInteractionEvent) -> Option<Vec<ChartInteractionEvent>> {
        match event {
            BaseInteractionEvent::MouseMove { position, modifiers } => {
                self.handle_mouse_move(*position, modifiers)
            }
            
            BaseInteractionEvent::MouseClick { position, button, modifiers } => {
                self.handle_mouse_click(*position, button, modifiers)
            }
            
            BaseInteractionEvent::MouseWheel { position, delta, modifiers } => {
                self.handle_mouse_wheel(*position, *delta, modifiers)
            }
            
            _ => None,
        }
    }
    
    /// 处理鼠标移动
    fn handle_mouse_move(&self, position: Point, _modifiers: &BaseKeyModifiers) -> Option<Vec<ChartInteractionEvent>> {
        let mut events = Vec::new();
        
        // 查找鼠标位置下的图表和数据点
        for (chart_id, controller) in &self.chart_controllers {
            if controller.contains_point(position) {
                if let Some((series_idx, point_idx, data_value)) = controller.find_point_at(position, 10.0) {
                    events.push(ChartInteractionEvent::PointHover {
                        chart_id: chart_id.clone(),
                        series_index: series_idx,
                        point_index: point_idx,
                        position,
                        data_value,
                    });
                } else {
                    // 如果之前有悬停状态，发送离开事件
                    if let Some(ref hover_state) = self.current_hover {
                        if hover_state.chart_id == *chart_id {
                            events.push(ChartInteractionEvent::PointLeave {
                                chart_id: chart_id.clone(),
                            });
                        }
                    }
                }
                break; // 只处理第一个匹配的图表
            }
        }
        
        if events.is_empty() {
            None
        } else {
            Some(events)
        }
    }
    
    /// 处理鼠标点击
    fn handle_mouse_click(&self, position: Point, _button: &BaseMouseButton, modifiers: &BaseKeyModifiers) -> Option<Vec<ChartInteractionEvent>> {
        let mut events = Vec::new();
        
        for (chart_id, controller) in &self.chart_controllers {
            if controller.contains_point(position) {
                if let Some((series_idx, point_idx, data_value)) = controller.find_point_at(position, 10.0) {
                    events.push(ChartInteractionEvent::PointClick {
                        chart_id: chart_id.clone(),
                        series_index: series_idx,
                        point_index: point_idx,
                        position,
                        data_value,
                        modifiers: self.convert_key_modifiers(modifiers),
                    });
                }
                break;
            }
        }
        
        if events.is_empty() {
            None
        } else {
            Some(events)
        }
    }
    
    /// 处理鼠标滚轮
    fn handle_mouse_wheel(&self, position: Point, delta: f64, modifiers: &BaseKeyModifiers) -> Option<Vec<ChartInteractionEvent>> {
        let mut events = Vec::new();
        
        for (chart_id, controller) in &self.chart_controllers {
            if controller.contains_point(position) {
                if modifiers.ctrl {
                    // Ctrl + 滚轮 = 缩放
                    let zoom_factor = if delta > 0.0 { 1.1 } else { 0.9 };
                    events.push(ChartInteractionEvent::ZoomRequest {
                        chart_id: chart_id.clone(),
                        zoom_factor,
                        center_point: position,
                    });
                } else {
                    // 普通滚轮 = 平移
                    events.push(ChartInteractionEvent::PanRequest {
                        chart_id: chart_id.clone(),
                        delta: Point::new(0.0, delta * 20.0), // 转换为像素偏移
                    });
                }
                break;
            }
        }
        
        if events.is_empty() {
            None
        } else {
            Some(events)
        }
    }
    
    /// 处理图表事件
    fn handle_chart_event(&mut self, event: ChartInteractionEvent) -> Option<ChartInteractionResult> {
        match &event {
            ChartInteractionEvent::PointHover { chart_id, series_index, point_index, position, .. } => {
                // 更新悬停状态
                self.current_hover = Some(HoverState {
                    chart_id: chart_id.clone(),
                    series_index: *series_index,
                    point_index: *point_index,
                    position: *position,
                });
            }
            
            ChartInteractionEvent::PointLeave { chart_id } => {
                // 清除悬停状态
                if let Some(ref hover_state) = self.current_hover {
                    if hover_state.chart_id == *chart_id {
                        self.current_hover = None;
                    }
                }
            }
            
            _ => {}
        }
        
        // 转发给对应的图表控制器
        if let Some(chart_id) = self.extract_chart_id(&event) {
            if let Some(controller) = self.chart_controllers.get_mut(&chart_id) {
                return Some(controller.handle_chart_event(event));
            }
        }
        
        None
    }
    
    /// 提取事件中的图表ID
    fn extract_chart_id(&self, event: &ChartInteractionEvent) -> Option<String> {
        match event {
            ChartInteractionEvent::PointHover { chart_id, .. } |
            ChartInteractionEvent::PointClick { chart_id, .. } |
            ChartInteractionEvent::PointLeave { chart_id } |
            ChartInteractionEvent::SeriesHover { chart_id, .. } |
            ChartInteractionEvent::SeriesLeave { chart_id } |
            ChartInteractionEvent::AreaSelection { chart_id, .. } |
            ChartInteractionEvent::ZoomRequest { chart_id, .. } |
            ChartInteractionEvent::PanRequest { chart_id, .. } => {
                Some(chart_id.clone())
            }
        }
    }
    
    /// 转换底层响应为图表结果
    fn convert_base_response(&self, response: BaseInteractionResponse) -> Option<ChartInteractionResult> {
        match response {
            BaseInteractionResponse::Redraw => {
                Some(ChartInteractionResult::Redraw {
                    chart_id: "global".to_string(),
                })
            }
            
            BaseInteractionResponse::ViewportChanged { new_viewport } => {
                Some(ChartInteractionResult::ViewportChanged {
                    chart_id: "global".to_string(),
                    new_bounds: new_viewport.bounds,
                    zoom_level: new_viewport.zoom,
                })
            }
            
            _ => None,
        }
    }
    
    /// 转换键盘修饰键
    fn convert_key_modifiers(&self, modifiers: &BaseKeyModifiers) -> KeyModifiers {
        KeyModifiers {
            ctrl: modifiers.ctrl,
            shift: modifiers.shift,
            alt: modifiers.alt,
            meta: modifiers.meta,
        }
    }
    
    /// 记录交互历史
    fn record_interaction(&mut self, event: &BaseInteractionEvent) {
        let (event_type, position) = match event {
            BaseInteractionEvent::MouseMove { position, .. } => ("mouse_move".to_string(), *position),
            BaseInteractionEvent::MouseClick { position, .. } => ("mouse_click".to_string(), *position),
            BaseInteractionEvent::MouseWheel { position, .. } => ("mouse_wheel".to_string(), *position),
            _ => return,
        };
        
        self.interaction_history.push(InteractionHistoryItem {
            timestamp: std::time::Instant::now(),
            event_type,
            position,
        });
        
        // 保持历史记录在合理大小
        if self.interaction_history.len() > 100 {
            self.interaction_history.remove(0);
        }
    }
}

impl Default for InteractionAdapter {
    fn default() -> Self {
        Self::new()
    }
}
