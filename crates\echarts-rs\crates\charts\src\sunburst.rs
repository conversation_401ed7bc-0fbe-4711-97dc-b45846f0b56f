//! 旭日图实现
//!
//! 提供层次数据的旭日图可视化功能

use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};
use echarts_core::{
    Color, DrawCommand, Point, Series, CoordinateSystem, SeriesType, Bounds, DataSet,
    draw_commands::{PathCommand, PathStyle},
    TextStyle, LineStyle, FontWeight, FontStyle, TextAlign, TextBaseline, LineCap, LineJoin,
    Result
};
use std::f64::consts::PI;

/// 旭日图数据项
#[derive(Debug, Clone)]
pub struct SunburstDataItem {
    /// 数据名称
    pub name: String,
    /// 数值（用于计算角度）
    pub value: f64,
    /// 子项
    pub children: Vec<SunburstDataItem>,
    /// 颜色
    pub color: Option<Color>,
    /// 标签
    pub label: Option<String>,
    /// 是否可见
    pub visible: bool,
    /// 是否展开
    pub expanded: bool,
}

impl SunburstDataItem {
    /// 创建新的旭日图数据项
    pub fn new(name: impl Into<String>, value: f64) -> Self {
        Self {
            name: name.into(),
            value,
            children: Vec::new(),
            color: None,
            label: None,
            visible: true,
            expanded: true,
        }
    }

    /// 设置颜色
    pub fn color(mut self, color: Color) -> Self {
        self.color = Some(color);
        self
    }

    /// 设置标签
    pub fn label(mut self, label: impl Into<String>) -> Self {
        self.label = Some(label.into());
        self
    }

    /// 添加子项
    pub fn add_child(mut self, child: SunburstDataItem) -> Self {
        self.children.push(child);
        self
    }

    /// 设置子项
    pub fn children(mut self, children: Vec<SunburstDataItem>) -> Self {
        self.children = children;
        self
    }

    /// 设置可见性
    pub fn visible(mut self, visible: bool) -> Self {
        self.visible = visible;
        self
    }

    /// 设置展开状态
    pub fn expanded(mut self, expanded: bool) -> Self {
        self.expanded = expanded;
        self
    }

    /// 获取总值（包括子项）
    pub fn total_value(&self) -> f64 {
        if self.children.is_empty() {
            self.value
        } else {
            self.children.iter().map(|child| child.total_value()).sum()
        }
    }

    /// 是否为叶子节点
    pub fn is_leaf(&self) -> bool {
        self.children.is_empty()
    }
}

/// 旭日图标签配置
#[derive(Debug, Clone)]
pub struct SunburstLabel {
    /// 是否显示标签
    pub show: bool,
    /// 字体大小
    pub font_size: f64,
    /// 标签颜色
    pub color: Color,
    /// 标签位置
    pub position: SunburstLabelPosition,
    /// 最小显示角度
    pub min_angle: f64,
    /// 标签旋转
    pub rotate: bool,
}

impl Default for SunburstLabel {
    fn default() -> Self {
        Self {
            show: true,
            font_size: 12.0,
            color: Color::rgb(0.2, 0.2, 0.2),
            position: SunburstLabelPosition::Middle,
            min_angle: 0.1, // 约5.7度
            rotate: true,
        }
    }
}

/// 标签位置
#[derive(Debug, Clone, Copy)]
pub enum SunburstLabelPosition {
    /// 内侧
    Inside,
    /// 中间
    Middle,
    /// 外侧
    Outside,
}

/// 旭日图扇形节点（用于布局计算）
#[derive(Debug, Clone)]
struct SunburstNode {
    /// 数据项引用
    item: SunburstDataItem,
    /// 起始角度（弧度）
    start_angle: f64,
    /// 结束角度（弧度）
    end_angle: f64,
    /// 内半径
    inner_radius: f64,
    /// 外半径
    outer_radius: f64,
    /// 层级
    level: usize,
    /// 父节点索引
    parent: Option<usize>,
}

/// 旭日图系列
#[derive(Debug, Clone)]
pub struct SunburstSeries {
    /// 基础配置
    config: ChartConfig,
    /// 数据项
    data: Vec<SunburstDataItem>,
    /// 中心点
    center: (f64, f64),
    /// 半径范围
    radius: (f64, f64),
    /// 起始角度（度）
    start_angle: f64,
    /// 标签配置
    label: SunburstLabel,
    /// 边框宽度
    border_width: f64,
    /// 边框颜色
    border_color: Color,
    /// 层级颜色
    level_colors: Vec<Color>,
    /// 最大显示层级
    max_depth: usize,
    /// 层级间隙
    level_gap: f64,
}

impl SunburstSeries {
    /// 创建新的旭日图系列
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            config: ChartConfig {
                name: name.into(),
                ..ChartConfig::default()
            },
            data: Vec::new(),
            center: (0.5, 0.5),
            radius: (0.0, 0.75),
            start_angle: 90.0,
            label: SunburstLabel::default(),
            border_width: 1.0,
            border_color: Color::rgb(1.0, 1.0, 1.0),
            level_colors: vec![
                Color::rgb(0.3, 0.6, 1.0),
                Color::rgb(0.6, 0.8, 0.4),
                Color::rgb(1.0, 0.6, 0.3),
                Color::rgb(0.8, 0.4, 0.8),
                Color::rgb(0.4, 0.8, 0.8),
                Color::rgb(1.0, 0.8, 0.2),
                Color::rgb(0.8, 0.2, 0.4),
            ],
            max_depth: 4,
            level_gap: 2.0,
        }
    }

    /// 设置数据
    pub fn data(mut self, data: Vec<SunburstDataItem>) -> Self {
        self.data = data;
        self
    }

    /// 添加数据项
    pub fn add_data(mut self, item: SunburstDataItem) -> Self {
        self.data.push(item);
        self
    }

    /// 设置中心点
    pub fn center(mut self, x: f64, y: f64) -> Self {
        self.center = (x, y);
        self
    }

    /// 设置半径范围
    pub fn radius(mut self, inner: f64, outer: f64) -> Self {
        self.radius = (inner, outer);
        self
    }

    /// 设置起始角度
    pub fn start_angle(mut self, angle: f64) -> Self {
        self.start_angle = angle;
        self
    }

    /// 设置标签配置
    pub fn label(mut self, label: SunburstLabel) -> Self {
        self.label = label;
        self
    }

    /// 设置边框样式
    pub fn border(mut self, width: f64, color: Color) -> Self {
        self.border_width = width;
        self.border_color = color;
        self
    }

    /// 设置层级颜色
    pub fn level_colors(mut self, colors: Vec<Color>) -> Self {
        self.level_colors = colors;
        self
    }

    /// 设置最大显示层级
    pub fn max_depth(mut self, depth: usize) -> Self {
        self.max_depth = depth;
        self
    }

    /// 设置层级间隙
    pub fn level_gap(mut self, gap: f64) -> Self {
        self.level_gap = gap.max(0.0);
        self
    }

    /// 获取系列名称
    pub fn name(&self) -> &str {
        &self.config.name
    }

    /// 角度转弧度
    fn deg_to_rad(&self, degrees: f64) -> f64 {
        degrees * PI / 180.0
    }

    /// 弧度转角度
    fn rad_to_deg(&self, radians: f64) -> f64 {
        radians * 180.0 / PI
    }

    /// 计算旭日图布局
    fn calculate_layout(&self, bounds: Bounds) -> Vec<SunburstNode> {
        let mut nodes = Vec::new();
        
        if self.data.is_empty() {
            return nodes;
        }

        // 计算中心点和半径
        let center_x = bounds.origin.x + bounds.size.width * self.center.0;
        let center_y = bounds.origin.y + bounds.size.height * self.center.1;
        let max_radius = (bounds.size.width.min(bounds.size.height) / 2.0) * self.radius.1;
        let min_radius = max_radius * self.radius.0;

        // 计算起始角度（转换为弧度）
        let start_angle_rad = self.deg_to_rad(self.start_angle);

        // 布局根级数据项
        self.layout_items(&self.data, start_angle_rad, start_angle_rad + 2.0 * PI, 
                         min_radius, max_radius, 0, None, &mut nodes);

        nodes
    }

    /// 布局数据项
    fn layout_items(
        &self,
        items: &[SunburstDataItem],
        start_angle: f64,
        end_angle: f64,
        inner_radius: f64,
        outer_radius: f64,
        level: usize,
        parent: Option<usize>,
        nodes: &mut Vec<SunburstNode>,
    ) {
        if items.is_empty() || level >= self.max_depth {
            return;
        }

        let total_value: f64 = items.iter().map(|item| item.total_value()).sum();
        if total_value <= 0.0 {
            return;
        }

        let angle_range = end_angle - start_angle;
        let mut current_angle = start_angle;

        // 计算层级半径
        let level_thickness = if self.max_depth > 1 {
            (outer_radius - inner_radius) / self.max_depth as f64
        } else {
            outer_radius - inner_radius
        };
        
        let level_inner = inner_radius + level as f64 * level_thickness;
        let level_outer = level_inner + level_thickness - self.level_gap;

        for item in items {
            if !item.visible {
                continue;
            }

            let item_angle = angle_range * (item.total_value() / total_value);
            let item_end_angle = current_angle + item_angle;

            let node_index = nodes.len();
            nodes.push(SunburstNode {
                item: item.clone(),
                start_angle: current_angle,
                end_angle: item_end_angle,
                inner_radius: level_inner,
                outer_radius: level_outer,
                level,
                parent,
            });

            // 递归处理子项
            if !item.children.is_empty() && item.expanded && level + 1 < self.max_depth {
                self.layout_items(
                    &item.children,
                    current_angle,
                    item_end_angle,
                    inner_radius,
                    outer_radius,
                    level + 1,
                    Some(node_index),
                    nodes,
                );
            }

            current_angle = item_end_angle;
        }
    }

    /// 生成扇形和标签
    fn generate_sectors_and_labels(&self, nodes: &[SunburstNode], center: Point) -> Vec<DrawCommand> {
        let mut commands = Vec::new();

        for node in nodes {
            // 获取颜色
            let color = node.item.color.unwrap_or_else(|| {
                self.level_colors[node.level % self.level_colors.len()]
            });

            // 生成扇形路径
            let sector_commands = self.generate_sector_path(
                center,
                node.inner_radius,
                node.outer_radius,
                node.start_angle,
                node.end_angle,
            );

            commands.push(DrawCommand::Path {
                commands: sector_commands,
                style: PathStyle {
                    fill: Some(color),
                    stroke: Some(LineStyle {
                        color: self.border_color,
                        width: self.border_width,
                        dash_pattern: None,
                        cap: LineCap::Round,
                        join: LineJoin::Round,
                        opacity: 1.0,
                    }),
                    opacity: 0.8,
                    fill_rule: echarts_core::draw_commands::FillRule::NonZero,
                },
            });

            // 生成标签
            if self.label.show {
                let angle_diff = node.end_angle - node.start_angle;
                if angle_diff >= self.label.min_angle {
                    let label_text = node.item.label.as_ref().unwrap_or(&node.item.name);
                    let label_position = self.calculate_label_position(center, node);
                    let label_rotation = if self.label.rotate {
                        self.calculate_label_rotation(node)
                    } else {
                        0.0
                    };

                    commands.push(DrawCommand::Text {
                        text: label_text.clone(),
                        position: label_position,
                        style: TextStyle {
                            font_family: "Arial".to_string(),
                            font_size: self.label.font_size,
                            font_weight: FontWeight::Normal,
                            font_style: FontStyle::Normal,
                            color: self.label.color,
                            opacity: 1.0,
                            align: TextAlign::Center,
                            baseline: TextBaseline::Middle,
                            rotation: label_rotation,
                            letter_spacing: 0.0,
                            line_height: 1.2,
                        },
                    });
                }
            }
        }

        commands
    }

    /// 生成扇形路径
    fn generate_sector_path(
        &self,
        center: Point,
        inner_radius: f64,
        outer_radius: f64,
        start_angle: f64,
        end_angle: f64,
    ) -> Vec<PathCommand> {
        let mut commands = Vec::new();

        // 计算关键点
        let start_outer = Point {
            x: center.x + outer_radius * start_angle.cos(),
            y: center.y + outer_radius * start_angle.sin(),
        };

        let end_outer = Point {
            x: center.x + outer_radius * end_angle.cos(),
            y: center.y + outer_radius * end_angle.sin(),
        };

        let start_inner = Point {
            x: center.x + inner_radius * start_angle.cos(),
            y: center.y + inner_radius * start_angle.sin(),
        };

        let end_inner = Point {
            x: center.x + inner_radius * end_angle.cos(),
            y: center.y + inner_radius * end_angle.sin(),
        };

        // 移动到外弧起点
        commands.push(PathCommand::MoveTo(start_outer));

        // 外弧 - 使用简化的直线近似
        let segments = 8;
        let angle_step = (end_angle - start_angle) / segments as f64;
        for i in 1..=segments {
            let angle = start_angle + angle_step * i as f64;
            let point = Point {
                x: center.x + outer_radius * angle.cos(),
                y: center.y + outer_radius * angle.sin(),
            };
            commands.push(PathCommand::LineTo(point));
        }

        // 连接到内弧终点
        commands.push(PathCommand::LineTo(end_inner));

        // 内弧（反向）- 使用简化的直线近似
        if inner_radius > 0.0 {
            for i in 1..segments {
                let angle = end_angle - angle_step * i as f64;
                let point = Point {
                    x: center.x + inner_radius * angle.cos(),
                    y: center.y + inner_radius * angle.sin(),
                };
                commands.push(PathCommand::LineTo(point));
            }
        }

        // 闭合路径
        commands.push(PathCommand::Close);

        commands
    }

    /// 计算标签位置
    fn calculate_label_position(&self, center: Point, node: &SunburstNode) -> Point {
        let mid_angle = (node.start_angle + node.end_angle) / 2.0;
        let radius = match self.label.position {
            SunburstLabelPosition::Inside => (node.inner_radius + node.outer_radius) / 2.0 * 0.7,
            SunburstLabelPosition::Middle => (node.inner_radius + node.outer_radius) / 2.0,
            SunburstLabelPosition::Outside => node.outer_radius + 10.0,
        };

        Point {
            x: center.x + radius * mid_angle.cos(),
            y: center.y + radius * mid_angle.sin(),
        }
    }

    /// 计算标签旋转角度
    fn calculate_label_rotation(&self, node: &SunburstNode) -> f64 {
        let mid_angle = (node.start_angle + node.end_angle) / 2.0;
        let rotation = self.rad_to_deg(mid_angle);

        // 避免文字倒置
        if rotation > 90.0 && rotation < 270.0 {
            rotation + 180.0
        } else {
            rotation
        }
    }
}

impl Series for SunburstSeries {
    fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        if self.data.is_empty() {
            return Ok(commands);
        }

        // 获取绘图区域
        let bounds = coord_system.bounds();

        // 计算中心点
        let center = Point {
            x: bounds.origin.x + bounds.size.width * self.center.0,
            y: bounds.origin.y + bounds.size.height * self.center.1,
        };

        // 计算布局
        let nodes = self.calculate_layout(bounds);

        // 生成扇形和标签
        commands.extend(self.generate_sectors_and_labels(&nodes, center));

        Ok(commands)
    }

    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Tree
    }

    fn bounds(&self) -> Option<Bounds> {
        // 旭日图使用整个可用空间
        None
    }

    fn is_visible(&self) -> bool {
        true
    }

    fn z_index(&self) -> i32 {
        0
    }

    fn clone_series(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use echarts_core::{CartesianCoordinateSystem, Point, Size};

    #[test]
    fn test_sunburst_data_item() {
        let item = SunburstDataItem::new("根节点", 100.0)
            .color(Color::rgb(1.0, 0.0, 0.0))
            .label("自定义标签")
            .add_child(SunburstDataItem::new("子节点1", 30.0))
            .add_child(SunburstDataItem::new("子节点2", 70.0))
            .expanded(true);

        assert_eq!(item.name, "根节点");
        assert_eq!(item.value, 100.0);
        assert_eq!(item.total_value(), 100.0); // 子节点值之和
        assert_eq!(item.children.len(), 2);
        assert!(!item.is_leaf());
        assert_eq!(item.color, Some(Color::rgb(1.0, 0.0, 0.0)));
        assert_eq!(item.label, Some("自定义标签".to_string()));
        assert!(item.expanded);
    }

    #[test]
    fn test_sunburst_data_item_leaf() {
        let item = SunburstDataItem::new("叶子节点", 50.0);

        assert_eq!(item.total_value(), 50.0);
        assert!(item.is_leaf());
        assert!(item.children.is_empty());
        assert!(item.visible);
        assert!(item.expanded);
    }

    #[test]
    fn test_sunburst_series_creation() {
        let series = SunburstSeries::new("测试旭日图")
            .center(0.5, 0.5)
            .radius(0.1, 0.8)
            .start_angle(90.0)
            .max_depth(3)
            .level_gap(2.0);

        assert_eq!(series.name(), "测试旭日图");
        assert_eq!(series.center, (0.5, 0.5));
        assert_eq!(series.radius, (0.1, 0.8));
        assert_eq!(series.start_angle, 90.0);
        assert_eq!(series.max_depth, 3);
        assert_eq!(series.level_gap, 2.0);
    }

    #[test]
    fn test_sunburst_label_config() {
        let label = SunburstLabel {
            show: true,
            font_size: 14.0,
            color: Color::rgb(0.1, 0.1, 0.1),
            position: SunburstLabelPosition::Outside,
            min_angle: 0.2,
            rotate: false,
        };

        assert!(label.show);
        assert_eq!(label.font_size, 14.0);
        assert_eq!(label.color, Color::rgb(0.1, 0.1, 0.1));
        assert!(matches!(label.position, SunburstLabelPosition::Outside));
        assert_eq!(label.min_angle, 0.2);
        assert!(!label.rotate);
    }

    #[test]
    fn test_sunburst_series_with_data() {
        let data = vec![
            SunburstDataItem::new("分类A", 0.0)
                .add_child(SunburstDataItem::new("A1", 20.0))
                .add_child(SunburstDataItem::new("A2", 30.0)),
            SunburstDataItem::new("分类B", 0.0)
                .add_child(SunburstDataItem::new("B1", 25.0))
                .add_child(SunburstDataItem::new("B2", 25.0)),
        ];

        let series = SunburstSeries::new("层次数据")
            .data(data)
            .start_angle(0.0);

        assert_eq!(series.data.len(), 2);
        assert_eq!(series.data[0].children.len(), 2);
        assert_eq!(series.data[1].children.len(), 2);
        assert_eq!(series.start_angle, 0.0);
    }

    #[test]
    fn test_angle_conversion() {
        let series = SunburstSeries::new("测试");

        // 测试角度转弧度
        assert!((series.deg_to_rad(0.0) - 0.0).abs() < 1e-10);
        assert!((series.deg_to_rad(90.0) - PI / 2.0).abs() < 1e-10);
        assert!((series.deg_to_rad(180.0) - PI).abs() < 1e-10);
        assert!((series.deg_to_rad(360.0) - 2.0 * PI).abs() < 1e-10);

        // 测试弧度转角度
        assert!((series.rad_to_deg(0.0) - 0.0).abs() < 1e-10);
        assert!((series.rad_to_deg(PI / 2.0) - 90.0).abs() < 1e-10);
        assert!((series.rad_to_deg(PI) - 180.0).abs() < 1e-10);
        assert!((series.rad_to_deg(2.0 * PI) - 360.0).abs() < 1e-10);
    }

    #[test]
    fn test_sunburst_series_rendering() {
        let data = vec![
            SunburstDataItem::new("项目1", 40.0),
            SunburstDataItem::new("项目2", 30.0),
            SunburstDataItem::new("项目3", 30.0),
        ];

        let series = SunburstSeries::new("项目分布")
            .data(data)
            .center(0.5, 0.5)
            .radius(0.0, 0.8);

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 400.0, height: 400.0 },
            },
            (0.0, 100.0),
            (0.0, 100.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();

        // 应该生成扇形和标签命令
        assert!(!commands.is_empty());

        // 检查命令类型
        let mut path_count = 0;
        let mut text_count = 0;

        for cmd in &commands {
            match cmd {
                DrawCommand::Path { .. } => path_count += 1,
                DrawCommand::Text { .. } => text_count += 1,
                _ => {}
            }
        }

        assert!(path_count > 0); // 扇形路径
        assert!(text_count > 0); // 标签
    }

    #[test]
    fn test_sunburst_series_empty_data() {
        let series = SunburstSeries::new("空旭日图");

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 400.0, height: 400.0 },
            },
            (0.0, 100.0),
            (0.0, 100.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();
        assert!(commands.is_empty());
    }

    #[test]
    fn test_label_position_calculation() {
        let series = SunburstSeries::new("测试");
        let center = Point { x: 200.0, y: 200.0 };
        let node = SunburstNode {
            item: SunburstDataItem::new("测试", 50.0),
            start_angle: 0.0,
            end_angle: PI / 2.0,
            inner_radius: 50.0,
            outer_radius: 100.0,
            level: 0,
            parent: None,
        };

        let pos = series.calculate_label_position(center, &node);

        // 标签应该在扇形的中心角度位置
        let expected_angle = PI / 4.0; // (0 + PI/2) / 2
        let expected_radius = 75.0; // (50 + 100) / 2
        let expected_x = center.x + expected_radius * expected_angle.cos();
        let expected_y = center.y + expected_radius * expected_angle.sin();

        assert!((pos.x - expected_x).abs() < 1e-10);
        assert!((pos.y - expected_y).abs() < 1e-10);
    }

    #[test]
    fn test_label_rotation_calculation() {
        let series = SunburstSeries::new("测试");
        let node = SunburstNode {
            item: SunburstDataItem::new("测试", 50.0),
            start_angle: 0.0,
            end_angle: PI / 2.0,
            inner_radius: 50.0,
            outer_radius: 100.0,
            level: 0,
            parent: None,
        };

        let rotation = series.calculate_label_rotation(&node);

        // 中心角度是 PI/4，转换为度数是 45 度
        let expected_rotation = 45.0;
        assert!((rotation - expected_rotation).abs() < 1e-10);
    }
}
// ============================================================================
// 图表基础架构集成 - ChartBase 实现
// ============================================================================

/// 为 SunburstSeries 实现 ChartBase trait
impl ChartBase for SunburstSeries {
    type DataType = Vec<SunburstDataItem>;

    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn raw_data(&self) -> &Self::DataType {
        &self.data
    }

    fn to_dataset(&self) -> DataSet {
        // TODO: 实现 Vec<SunburstDataItem> 到 DataSet 的转换
        DataSet::new()
    }
    
    fn visible(&self) -> bool {
        self.config.visible
    }
    
    fn z_index(&self) -> i32 {
        self.config.z_index
    }
    
    fn bounds(&self) -> Option<Bounds> {
        // TODO: 为 SunburstSeries 实现边界计算
        None
    }
    
    fn config(&self) -> &ChartConfig {
        &self.config
    }
    
    fn config_mut(&mut self) -> &mut ChartConfig {
        &mut self.config
    }
}

/// 为 SunburstSeries 实现 ChartSeries trait
impl ChartSeries for SunburstSeries {
    // 所有方法都由默认实现提供，基于 ChartBase
}
