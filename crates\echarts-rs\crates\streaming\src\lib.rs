//! Real-time data streaming for ECharts-rs

use echarts_core::*;
use echarts_charts::*;
use echarts_data::*;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tokio::sync::{broadcast, mpsc};
use tokio_stream::{Stream, StreamExt};
use async_trait::async_trait;

pub mod sources;
pub mod processors;
pub mod buffers;
pub mod windows;

#[cfg(feature = "websocket")]
pub mod websocket;

#[cfg(feature = "kafka")]
pub mod kafka;

#[cfg(feature = "rabbitmq")]
pub mod rabbitmq;

/// Real-time data streaming manager
pub struct StreamingManager {
    /// Data sources
    sources: HashMap<String, Box<dyn DataSource + Send + Sync>>,
    /// Data processors
    processors: HashMap<String, Box<dyn DataProcessor + Send + Sync>>,
    /// Data buffers
    buffers: HashMap<String, Box<dyn DataBuffer + Send + Sync>>,
    /// Active streams
    streams: HashMap<String, StreamHandle>,
    /// Event broadcaster
    event_tx: broadcast::Sender<StreamEvent>,
    /// Configuration
    config: StreamingConfig,
    /// Metrics
    metrics: StreamingMetrics,
}

/// Streaming configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamingConfig {
    /// Maximum number of concurrent streams
    pub max_streams: usize,
    /// Default buffer size
    pub default_buffer_size: usize,
    /// Default window size
    pub default_window_size: Duration,
    /// Heartbeat interval
    pub heartbeat_interval: Duration,
    /// Connection timeout
    pub connection_timeout: Duration,
    /// Retry configuration
    pub retry_config: RetryConfig,
    /// Performance settings
    pub performance: PerformanceConfig,
}

/// Retry configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryConfig {
    /// Maximum retry attempts
    pub max_attempts: u32,
    /// Initial retry delay
    pub initial_delay: Duration,
    /// Maximum retry delay
    pub max_delay: Duration,
    /// Backoff multiplier
    pub backoff_multiplier: f64,
}

/// Performance configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    /// Batch size for processing
    pub batch_size: usize,
    /// Processing interval
    pub processing_interval: Duration,
    /// Memory limit per stream (MB)
    pub memory_limit_mb: usize,
    /// Enable compression
    pub compression: bool,
}

/// Data source trait for streaming
#[async_trait]
pub trait DataSource {
    /// Start streaming data
    async fn start_stream(&mut self) -> Result<Box<dyn Stream<Item = DataPoint> + Unpin + Send>>;
    
    /// Stop streaming
    async fn stop_stream(&mut self) -> Result<()>;
    
    /// Get source metadata
    fn metadata(&self) -> DataSourceMetadata;
    
    /// Check if source is healthy
    async fn health_check(&self) -> bool;
}

/// Data processor trait for real-time processing
#[async_trait]
pub trait DataProcessor {
    /// Process a batch of data points
    async fn process_batch(&self, batch: Vec<DataPoint>) -> Result<Vec<DataPoint>>;
    
    /// Process a single data point
    async fn process_point(&self, point: DataPoint) -> Result<Option<DataPoint>>;
    
    /// Get processor configuration
    fn config(&self) -> ProcessorConfig;
}

/// Data buffer trait for managing streaming data
pub trait DataBuffer: Send + Sync {
    /// Add data points to buffer
    fn push(&mut self, points: Vec<DataPoint>) -> Result<()>;
    
    /// Get data points from buffer
    fn pop(&mut self, count: usize) -> Vec<DataPoint>;
    
    /// Get buffer size
    fn size(&self) -> usize;
    
    /// Get buffer capacity
    fn capacity(&self) -> usize;
    
    /// Clear buffer
    fn clear(&mut self);
    
    /// Check if buffer is full
    fn is_full(&self) -> bool;
    
    /// Check if buffer is empty
    fn is_empty(&self) -> bool;
}

/// Stream handle for managing active streams
#[derive(Debug)]
pub struct StreamHandle {
    /// Stream ID
    pub id: String,
    /// Stream status
    pub status: StreamStatus,
    /// Start time
    pub start_time: Instant,
    /// Data points processed
    pub points_processed: u64,
    /// Bytes processed
    pub bytes_processed: u64,
    /// Last activity time
    pub last_activity: Instant,
    /// Error count
    pub error_count: u32,
    /// Stream cancellation token
    pub cancel_tx: mpsc::Sender<()>,
}

/// Stream status
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum StreamStatus {
    Starting,
    Running,
    Paused,
    Stopping,
    Stopped,
    Error,
}

/// Stream events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StreamEvent {
    /// Stream started
    StreamStarted { stream_id: String },
    /// Stream stopped
    StreamStopped { stream_id: String },
    /// Data received
    DataReceived { stream_id: String, point_count: usize },
    /// Error occurred
    Error { stream_id: String, error: String },
    /// Buffer full
    BufferFull { stream_id: String },
    /// Connection lost
    ConnectionLost { stream_id: String },
    /// Connection restored
    ConnectionRestored { stream_id: String },
}

/// Streaming metrics
#[derive(Debug, Default)]
pub struct StreamingMetrics {
    /// Total streams created
    pub streams_created: u64,
    /// Active streams
    pub active_streams: u64,
    /// Total data points processed
    pub points_processed: u64,
    /// Total bytes processed
    pub bytes_processed: u64,
    /// Total errors
    pub errors: u64,
    /// Average processing latency
    pub avg_latency_ms: f64,
    /// Throughput (points per second)
    pub throughput_pps: f64,
}

/// Processor configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessorConfig {
    /// Processor name
    pub name: String,
    /// Processing mode
    pub mode: ProcessingMode,
    /// Batch size
    pub batch_size: usize,
    /// Processing timeout
    pub timeout: Duration,
    /// Custom parameters
    pub parameters: HashMap<String, serde_json::Value>,
}

/// Processing modes
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ProcessingMode {
    /// Process each point individually
    Individual,
    /// Process in batches
    Batch,
    /// Process in time windows
    Windowed,
    /// Process on demand
    OnDemand,
}

/// Time window for data aggregation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeWindow {
    /// Window size
    pub size: Duration,
    /// Window slide interval
    pub slide: Duration,
    /// Window type
    pub window_type: WindowType,
    /// Aggregation functions
    pub aggregations: Vec<WindowAggregation>,
}

/// Window types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum WindowType {
    /// Tumbling window (non-overlapping)
    Tumbling,
    /// Sliding window (overlapping)
    Sliding,
    /// Session window (gap-based)
    Session,
}

/// Window aggregation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowAggregation {
    /// Column to aggregate
    pub column: String,
    /// Aggregation function
    pub function: AggregationFunction,
    /// Output column name
    pub output_name: String,
}

/// Circular buffer implementation
pub struct CircularBuffer {
    /// Buffer data
    data: VecDeque<DataPoint>,
    /// Maximum capacity
    capacity: usize,
}

/// Sliding window buffer
pub struct SlidingWindowBuffer {
    /// Window data
    windows: VecDeque<TimeWindow>,
    /// Current window
    current_window: Vec<DataPoint>,
    /// Window configuration
    config: TimeWindow,
    /// Last window time
    last_window_time: Option<chrono::DateTime<chrono::Utc>>,
}

impl Default for StreamingConfig {
    fn default() -> Self {
        Self {
            max_streams: 100,
            default_buffer_size: 10000,
            default_window_size: Duration::from_secs(60),
            heartbeat_interval: Duration::from_secs(30),
            connection_timeout: Duration::from_secs(10),
            retry_config: RetryConfig::default(),
            performance: PerformanceConfig::default(),
        }
    }
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            initial_delay: Duration::from_millis(100),
            max_delay: Duration::from_secs(30),
            backoff_multiplier: 2.0,
        }
    }
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            batch_size: 100,
            processing_interval: Duration::from_millis(100),
            memory_limit_mb: 100,
            compression: false,
        }
    }
}

impl StreamingManager {
    /// Create a new streaming manager
    pub fn new(config: StreamingConfig) -> Self {
        let (event_tx, _) = broadcast::channel(1000);
        
        Self {
            sources: HashMap::new(),
            processors: HashMap::new(),
            buffers: HashMap::new(),
            streams: HashMap::new(),
            event_tx,
            config,
            metrics: StreamingMetrics::default(),
        }
    }

    /// Register a data source
    pub fn register_source<S: Into<String>>(
        &mut self,
        name: S,
        source: Box<dyn DataSource + Send + Sync>,
    ) {
        self.sources.insert(name.into(), source);
    }

    /// Register a data processor
    pub fn register_processor<S: Into<String>>(
        &mut self,
        name: S,
        processor: Box<dyn DataProcessor + Send + Sync>,
    ) {
        self.processors.insert(name.into(), processor);
    }

    /// Start a new stream
    pub async fn start_stream<S: Into<String>>(
        &mut self,
        stream_id: S,
        source_name: &str,
        processor_name: Option<&str>,
    ) -> Result<()> {
        let stream_id = stream_id.into();
        
        if self.streams.len() >= self.config.max_streams {
            return Err(ChartError::Configuration("Maximum streams reached".into()));
        }

        let source = self.sources.get_mut(source_name)
            .ok_or_else(|| ChartError::Configuration(format!("Source '{}' not found", source_name)))?;

        let (cancel_tx, mut cancel_rx) = mpsc::channel(1);
        
        let handle = StreamHandle {
            id: stream_id.clone(),
            status: StreamStatus::Starting,
            start_time: Instant::now(),
            points_processed: 0,
            bytes_processed: 0,
            last_activity: Instant::now(),
            error_count: 0,
            cancel_tx,
        };

        self.streams.insert(stream_id.clone(), handle);

        // Start the stream processing task
        let event_tx = self.event_tx.clone();
        let stream_id_clone = stream_id.clone();
        
        tokio::spawn(async move {
            // Stream processing logic would go here
            let _ = event_tx.send(StreamEvent::StreamStarted { 
                stream_id: stream_id_clone 
            });
            
            // Wait for cancellation
            let _ = cancel_rx.recv().await;
        });

        self.metrics.streams_created += 1;
        self.metrics.active_streams += 1;

        Ok(())
    }

    /// Stop a stream
    pub async fn stop_stream(&mut self, stream_id: &str) -> Result<()> {
        if let Some(mut handle) = self.streams.remove(stream_id) {
            handle.status = StreamStatus::Stopping;
            let _ = handle.cancel_tx.send(()).await;
            
            self.metrics.active_streams = self.metrics.active_streams.saturating_sub(1);
            
            let _ = self.event_tx.send(StreamEvent::StreamStopped {
                stream_id: stream_id.to_string(),
            });
            
            Ok(())
        } else {
            Err(ChartError::Configuration(format!("Stream '{}' not found", stream_id)))
        }
    }

    /// Get stream status
    pub fn get_stream_status(&self, stream_id: &str) -> Option<StreamStatus> {
        self.streams.get(stream_id).map(|handle| handle.status)
    }

    /// List active streams
    pub fn list_streams(&self) -> Vec<String> {
        self.streams.keys().cloned().collect()
    }

    /// Get streaming metrics
    pub fn get_metrics(&self) -> &StreamingMetrics {
        &self.metrics
    }

    /// Subscribe to stream events
    pub fn subscribe_events(&self) -> broadcast::Receiver<StreamEvent> {
        self.event_tx.subscribe()
    }

    /// Update chart with streaming data
    pub async fn update_chart_with_stream(
        &self,
        chart: &mut Chart,
        stream_id: &str,
        series_name: &str,
    ) -> Result<()> {
        // Get data from stream buffer
        if let Some(buffer) = self.buffers.get(stream_id) {
            // This would extract recent data points and update the chart
            // Implementation depends on chart type and update strategy
        }
        
        Ok(())
    }
}

impl CircularBuffer {
    /// Create a new circular buffer
    pub fn new(capacity: usize) -> Self {
        Self {
            data: VecDeque::with_capacity(capacity),
            capacity,
        }
    }
}

impl DataBuffer for CircularBuffer {
    fn push(&mut self, mut points: Vec<DataPoint>) -> Result<()> {
        for point in points.drain(..) {
            if self.data.len() >= self.capacity {
                self.data.pop_front();
            }
            self.data.push_back(point);
        }
        Ok(())
    }

    fn pop(&mut self, count: usize) -> Vec<DataPoint> {
        let actual_count = count.min(self.data.len());
        self.data.drain(..actual_count).collect()
    }

    fn size(&self) -> usize {
        self.data.len()
    }

    fn capacity(&self) -> usize {
        self.capacity
    }

    fn clear(&mut self) {
        self.data.clear();
    }

    fn is_full(&self) -> bool {
        self.data.len() >= self.capacity
    }

    fn is_empty(&self) -> bool {
        self.data.is_empty()
    }
}

/// Real-time chart updater
pub struct RealtimeChartUpdater {
    /// Chart reference
    chart: Arc<RwLock<Chart>>,
    /// Update interval
    update_interval: Duration,
    /// Data buffer
    buffer: Arc<RwLock<dyn DataBuffer + Send + Sync>>,
    /// Update strategy
    strategy: UpdateStrategy,
}

/// Chart update strategies
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum UpdateStrategy {
    /// Replace all data
    Replace,
    /// Append new data
    Append,
    /// Sliding window
    SlidingWindow,
    /// Smart update (detect changes)
    Smart,
}

impl RealtimeChartUpdater {
    /// Create a new real-time chart updater
    pub fn new(
        chart: Arc<RwLock<Chart>>,
        buffer: Arc<RwLock<dyn DataBuffer + Send + Sync>>,
        update_interval: Duration,
        strategy: UpdateStrategy,
    ) -> Self {
        Self {
            chart,
            update_interval,
            buffer,
            strategy,
        }
    }

    /// Start updating the chart
    pub async fn start_updating(&self) -> Result<()> {
        let mut interval = tokio::time::interval(self.update_interval);
        
        loop {
            interval.tick().await;
            self.update_chart().await?;
        }
    }

    /// Update the chart with latest data
    async fn update_chart(&self) -> Result<()> {
        let mut buffer = self.buffer.write().unwrap();
        let data_points = buffer.pop(buffer.size());
        
        if !data_points.is_empty() {
            let mut chart = self.chart.write().unwrap();
            
            match self.strategy {
                UpdateStrategy::Replace => {
                    // Replace all data in the chart
                    // Implementation depends on chart structure
                }
                UpdateStrategy::Append => {
                    // Append new data to existing series
                    // Implementation depends on chart structure
                }
                UpdateStrategy::SlidingWindow => {
                    // Maintain a sliding window of data
                    // Implementation depends on chart structure
                }
                UpdateStrategy::Smart => {
                    // Intelligently update only changed data
                    // Implementation depends on chart structure
                }
            }
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_streaming_manager_creation() {
        let config = StreamingConfig::default();
        let manager = StreamingManager::new(config);
        assert_eq!(manager.list_streams().len(), 0);
    }

    #[test]
    fn test_circular_buffer() {
        let mut buffer = CircularBuffer::new(3);
        assert!(buffer.is_empty());
        assert_eq!(buffer.capacity(), 3);

        let points = vec![
            DataPoint::new(vec![DataValue::Number(1.0)]),
            DataPoint::new(vec![DataValue::Number(2.0)]),
            DataPoint::new(vec![DataValue::Number(3.0)]),
        ];

        buffer.push(points).unwrap();
        assert_eq!(buffer.size(), 3);
        assert!(buffer.is_full());

        // Add one more point (should evict the first)
        buffer.push(vec![DataPoint::new(vec![DataValue::Number(4.0)])]).unwrap();
        assert_eq!(buffer.size(), 3);

        let popped = buffer.pop(2);
        assert_eq!(popped.len(), 2);
        assert_eq!(buffer.size(), 1);
    }

    #[tokio::test]
    async fn test_stream_lifecycle() {
        let config = StreamingConfig::default();
        let mut manager = StreamingManager::new(config);

        // This would require implementing a mock data source
        // assert!(manager.start_stream("test_stream", "mock_source", None).await.is_ok());
        // assert_eq!(manager.list_streams().len(), 1);
        // assert!(manager.stop_stream("test_stream").await.is_ok());
        // assert_eq!(manager.list_streams().len(), 0);
    }
}
