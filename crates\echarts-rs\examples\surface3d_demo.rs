//! 3D曲面图演示
//!
//! 展示3D曲面图、等距投影和光照效果的使用方法

use echarts_rs::prelude::*;
use echarts_rs::{Surface3DSeries, Point3D, SimpleLighting, ChartBuilder};
use std::f64::consts::PI;

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🌐 3D曲面图演示");

    // 1. 基础3D曲面图（抛物面）
    println!("\n📊 基础3D曲面图 - 抛物面:");
    
    let paraboloid = Surface3DSeries::new("抛物面")
        .resolution(15, 15)
        .from_function(|x, y| x * x + y * y, (-2.0, 2.0), (-2.0, 2.0))
        .scale(50.0)
        .rotation(30.0, 45.0)
        .color(Color::rgb(0.3, 0.7, 1.0));
    
    println!("  ✅ 抛物面创建成功");
    println!("  - 名称: {}", paraboloid.name);
    println!("  - 类型: {:?}", paraboloid.series_type());
    println!("  - 网格分辨率: {:?}", paraboloid.grid_resolution);
    println!("  - 数据点数: {}", paraboloid.data.len());
    println!("  - 投影缩放: {}", paraboloid.scale);
    
    // 2. 波浪曲面
    println!("\n🌊 波浪曲面:");
    
    let wave_surface = Surface3DSeries::new("波浪曲面")
        .resolution(20, 20)
        .from_function(
            |x, y| (x * x + y * y).sqrt().sin() * 0.5,
            (-3.0, 3.0), 
            (-3.0, 3.0)
        )
        .scale(40.0)
        .rotation(20.0, 60.0)
        .color(Color::rgb(1.0, 0.5, 0.2))
        .wireframe(true, Color::rgb(0.2, 0.2, 0.2));
    
    println!("  ✅ 波浪曲面创建成功");
    println!("  - 网格分辨率: {:?}", wave_surface.grid_resolution);
    println!("  - 显示线框: {}", wave_surface.wireframe);
    
    // 3. 鞍面（双曲抛物面）
    println!("\n🏔️ 鞍面:");
    
    let saddle_surface = Surface3DSeries::new("鞍面")
        .resolution(12, 12)
        .from_function(|x, y| x * x - y * y, (-2.0, 2.0), (-2.0, 2.0))
        .scale(60.0)
        .rotation(45.0, 30.0)
        .color(Color::rgb(0.8, 0.3, 0.8))
        .opacity(0.9);
    
    println!("  ✅ 鞍面创建成功");
    println!("  - 透明度: {}", saddle_surface.opacity);
    
    // 4. 正弦波曲面
    println!("\n📈 正弦波曲面:");
    
    let sine_surface = Surface3DSeries::new("正弦波曲面")
        .resolution(25, 25)
        .from_function(
            |x, y| (x.sin() * y.cos()) * 0.8,
            (-PI, PI), 
            (-PI, PI)
        )
        .scale(30.0)
        .rotation(15.0, 75.0)
        .color(Color::rgb(0.2, 0.8, 0.4));
    
    println!("  ✅ 正弦波曲面创建成功");
    println!("  - X范围: [-π, π]");
    println!("  - Y范围: [-π, π]");
    
    // 5. 测试3D点操作
    println!("\n🔧 3D点操作测试:");
    
    let p1 = Point3D::new(1.0, 2.0, 3.0);
    let p2 = Point3D::new(4.0, 5.0, 6.0);
    
    let diff = p2.sub(&p1);
    let dot = p1.dot(&p2);
    let cross = p1.cross(&p2);
    let length = p1.length();
    let normalized = p1.normalize();
    
    println!("  点1: ({}, {}, {})", p1.x, p1.y, p1.z);
    println!("  点2: ({}, {}, {})", p2.x, p2.y, p2.z);
    println!("  差值: ({:.1}, {:.1}, {:.1})", diff.x, diff.y, diff.z);
    println!("  点积: {:.1}", dot);
    println!("  叉积: ({:.1}, {:.1}, {:.1})", cross.x, cross.y, cross.z);
    println!("  长度: {:.2}", length);
    println!("  单位向量: ({:.2}, {:.2}, {:.2})", normalized.x, normalized.y, normalized.z);
    
    // 6. 测试等距投影
    println!("\n📐 等距投影测试:");
    
    let test_point = Point3D::new(1.0, 1.0, 1.0);
    let center = Point::new(200.0, 200.0);
    let projected = test_point.isometric_project(50.0, center);
    
    println!("  3D点: ({}, {}, {})", test_point.x, test_point.y, test_point.z);
    println!("  投影中心: ({}, {})", center.x, center.y);
    println!("  投影结果: ({:.1}, {:.1})", projected.x, projected.y);
    
    // 7. 测试光照计算
    println!("\n💡 光照计算测试:");
    
    let lighting = SimpleLighting::default();
    
    // 测试不同方向的法向量
    let normals = vec![
        Point3D::new(0.0, 0.0, 1.0),   // 向上
        Point3D::new(0.0, 0.0, -1.0),  // 向下
        Point3D::new(1.0, 0.0, 0.0),   // 向右
        Point3D::new(-1.0, 0.0, 0.0),  // 向左
        Point3D::new(0.0, 1.0, 0.0),   // 向前
        Point3D::new(0.0, -1.0, 0.0),  // 向后
    ];
    
    let directions = ["向上", "向下", "向右", "向左", "向前", "向后"];
    
    for (i, normal) in normals.iter().enumerate() {
        let intensity = lighting.calculate_intensity(normal);
        println!("  {} 法向量光照强度: {:.2}", directions[i], intensity);
    }
    
    // 8. 测试渲染
    println!("\n🎨 渲染测试:");
    
    // 创建坐标系统
    let coord_system = CartesianCoordinateSystem::new(
        Bounds::new(50.0, 50.0, 500.0, 500.0),
        (0.0, 1.0),
        (0.0, 1.0),
    );
    
    // 渲染抛物面
    match paraboloid.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 抛物面渲染成功: {} 个绘制命令", commands.len());
            
            // 统计命令类型
            let mut path_count = 0;
            
            for cmd in &commands {
                match cmd {
                    DrawCommand::Path { .. } => path_count += 1,
                    _ => {}
                }
            }
            
            println!("    - 路径命令: {}", path_count);
        }
        Err(e) => {
            println!("  ❌ 渲染失败: {}", e);
        }
    }
    
    // 渲染波浪曲面
    match wave_surface.render_to_commands(&coord_system) {
        Ok(commands) => {
            println!("  ✅ 波浪曲面渲染成功: {} 个绘制命令", commands.len());
        }
        Err(e) => {
            println!("  ❌ 渲染失败: {}", e);
        }
    }
    
    // 9. 测试类型擦除
    println!("\n🔄 测试类型擦除:");
    
    let series_list: Vec<Box<dyn Series>> = vec![
        Box::new(paraboloid),
        Box::new(wave_surface),
        Box::new(saddle_surface),
        Box::new(sine_surface),
    ];
    
    println!("  ✅ 类型擦除成功 - 可以在同一容器中存储不同的3D曲面");
    for (i, series) in series_list.iter().enumerate() {
        println!("    {}. {} ({})", i + 1, series.name(), series.series_type().as_str());
    }
    
    // 10. 创建完整图表
    println!("\n📈 创建完整图表:");
    
    let chart = Chart::new()
        .title("3D数学函数可视化")
        .size(800.0, 600.0)
        .background_color(Color::rgb(0.95, 0.95, 0.95))
        .add_series(Box::new(Surface3DSeries::new("高斯函数")
            .resolution(20, 20)
            .from_function(
                |x, y| (-0.5 * (x * x + y * y)).exp(),
                (-3.0, 3.0), 
                (-3.0, 3.0)
            )
            .scale(80.0)
            .rotation(25.0, 45.0)
            .color(Color::rgb(1.0, 0.6, 0.2))));
    
    println!("  ✅ 完整图表创建成功");
    println!("  - 标题: {:?}", chart.title);
    println!("  - 大小: {}x{}", chart.width, chart.height);
    println!("  - 系列数: {}", chart.series.len());
    
    // 11. 使用 ChartBuilder
    println!("\n🏗️ 使用 ChartBuilder:");
    
    let surface_chart = ChartBuilder::new()
        .title("3D曲面图集合")
        .size(1000.0, 800.0)
        .add_series(Surface3DSeries::new("复合函数")
            .resolution(18, 18)
            .from_function(
                |x, y| (x * y).sin() * (x * x + y * y).sqrt().cos(),
                (-2.0, 2.0), 
                (-2.0, 2.0)
            )
            .scale(60.0)
            .rotation(35.0, 55.0)
            .color(Color::rgb(0.4, 0.8, 0.6))
            .wireframe(true, Color::rgb(0.1, 0.1, 0.1)))
        .build();
    
    println!("  ✅ 使用 ChartBuilder 创建3D曲面图成功");
    println!("  - 标题: {:?}", surface_chart.title);
    println!("  - 大小: {}x{}", surface_chart.width, surface_chart.height);
    println!("  - 系列数: {}", surface_chart.series.len());
    
    // 12. 数学函数展示
    println!("\n🧮 数学函数展示:");
    
    let functions = vec![
        ("抛物面", "z = x² + y²"),
        ("波浪", "z = sin(√(x² + y²)) × 0.5"),
        ("鞍面", "z = x² - y²"),
        ("正弦波", "z = sin(x) × cos(y) × 0.8"),
        ("高斯函数", "z = e^(-0.5(x² + y²))"),
        ("复合函数", "z = sin(xy) × cos(√(x² + y²))"),
    ];
    
    for (name, formula) in functions {
        println!("  📐 {}: {}", name, formula);
    }
    
    println!("\n🎉 3D曲面图演示完成！");
    println!("✨ Surface3DSeries 提供了强大的3D可视化功能");
    println!("🔧 支持等距投影、光照计算和多种数学函数");
    
    Ok(())
}
