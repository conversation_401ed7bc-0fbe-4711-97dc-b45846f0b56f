# ECharts 坐标系模块分析

## 模块概述

`src/coord/` 目录包含了 ECharts 支持的所有坐标系实现，坐标系是图表数据到视觉位置映射的核心组件。

## 坐标系类型

### 1. 直角坐标系 (Cartesian)
- **cartesian**: 基础直角坐标系
- **cartesian2d**: 二维直角坐标系
- 支持 X 轴和 Y 轴
- 最常用的坐标系类型

### 2. 极坐标系 (Polar)
- **polar**: 极坐标系
- 支持径向轴 (RadiusAxis) 和角度轴 (AngleAxis)
- 适用于雷达图、极坐标柱状图等

### 3. 地理坐标系 (Geo)
- **geo**: 地理坐标系
- 支持 GeoJSON 和 SVG 地图
- 支持地图投影变换
- 适用于地图可视化

### 4. 平行坐标系 (Parallel)
- **parallel**: 平行坐标系
- 支持多维数据可视化
- 每个维度对应一个平行轴

### 5. 单轴坐标系 (Single)
- **single**: 单轴坐标系
- 只有一个坐标轴
- 适用于散点图等简单场景

### 6. 日历坐标系 (Calendar)
- **calendar**: 日历坐标系
- 基于日期的坐标系
- 适用于时间序列数据

### 7. 矩阵坐标系 (Matrix)
- **matrix**: 矩阵坐标系
- 用于矩阵图表
- 支持行列布局

### 8. 雷达坐标系 (Radar)
- **radar**: 雷达坐标系
- 专用于雷达图
- 支持多个指标轴

## 坐标系架构

### 1. 基础架构

```
CoordinateSystem (接口)
├── 坐标转换方法
├── 包含检测方法
├── 维度定义
└── 更新方法

具体坐标系实现
├── [CoordType].ts          # 坐标系主类
├── [CoordType]Model.ts     # 坐标系模型
├── Axis classes            # 坐标轴实现
├── creator.ts              # 创建器
└── prepareCustom.ts        # 自定义图表支持
```

### 2. 核心接口

```typescript
interface CoordinateSystem {
    type: string
    dimensions: string[]
    
    // 数据点到像素坐标转换
    dataToPoint(data: ScaleDataValue[], clamp?: boolean): number[]
    
    // 像素坐标到数据点转换
    pointToData(point: number[]): number[]
    
    // 检测点是否在坐标系内
    containPoint(point: number[]): boolean
    
    // 获取坐标轴
    getAxis?(dim?: string): Axis
}
```

## 主要坐标系详细分析

### 1. Cartesian2D (二维直角坐标系)

**文件结构**:
```
cartesian/
├── Cartesian2D.ts           # 二维直角坐标系
├── Cartesian.ts             # 基础直角坐标系
├── Axis2D.ts                # 二维坐标轴
├── AxisModel.ts             # 坐标轴模型
├── Grid.ts                  # 网格容器
├── GridModel.ts             # 网格模型
├── cartesianAxisHelper.ts   # 坐标轴辅助函数
├── defaultAxisExtentFromData.ts  # 默认轴范围
├── legacyContainLabel.ts    # 兼容性处理
└── prepareCustom.ts         # 自定义图表支持
```

**主要特性**:
- 支持线性、对数、时间等多种刻度类型
- 支持轴反转和轴断点
- 支持多网格布局
- 支持仿射变换优化大数据渲染

**核心方法**:
```typescript
class Cartesian2D {
    // 数据到像素坐标转换
    dataToPoint(data: ScaleDataValue[]): number[]
    
    // 像素坐标到数据转换
    pointToData(point: number[]): number[]
    
    // 计算仿射变换矩阵（性能优化）
    calcAffineTransform(): void
    
    // 获取坐标轴
    getAxis(dim: 'x' | 'y'): Axis2D
}
```

### 2. Polar (极坐标系)

**文件结构**:
```
polar/
├── Polar.ts                 # 极坐标系
├── AngleAxis.ts             # 角度轴
├── RadiusAxis.ts            # 径向轴
├── AxisModel.ts             # 轴模型
├── PolarModel.ts            # 极坐标模型
├── polarCreator.ts          # 创建器
└── prepareCustom.ts         # 自定义图表支持
```

**主要特性**:
- 支持角度轴和径向轴
- 支持顺时针和逆时针方向
- 支持起始角度配置
- 适用于雷达图、极坐标柱状图

**坐标转换**:
```typescript
class Polar {
    // 极坐标到直角坐标转换
    coordToPoint(coord: number[]): number[] {
        const radius = coord[0]
        const angle = coord[1] / 180 * Math.PI
        const x = Math.cos(angle) * radius + this.cx
        const y = Math.sin(angle) * radius + this.cy
        return [x, y]
    }
    
    // 直角坐标到极坐标转换
    pointToCoord(point: number[]): number[] {
        const dx = point[0] - this.cx
        const dy = point[1] - this.cy
        const radius = Math.sqrt(dx * dx + dy * dy)
        const angle = Math.atan2(dy, dx) / Math.PI * 180
        return [radius, angle]
    }
}
```

### 3. Geo (地理坐标系)

**文件结构**:
```
geo/
├── Geo.ts                   # 地理坐标系
├── GeoModel.ts              # 地理模型
├── Region.ts                # 地理区域
├── GeoJSONResource.ts       # GeoJSON 资源
├── GeoSVGResource.ts        # SVG 资源
├── geoSourceManager.ts      # 地理数据源管理
├── geoCreator.ts            # 创建器
├── geoTypes.ts              # 类型定义
├── parseGeoJson.ts          # GeoJSON 解析
├── prepareCustom.ts         # 自定义图表支持
└── fix/                     # 地图数据修复
```

**主要特性**:
- 支持 GeoJSON 和 SVG 两种地图格式
- 支持地图投影变换
- 支持地理区域管理
- 支持地图缩放和平移

**投影系统**:
```typescript
interface GeoProjection {
    // 经纬度到平面坐标投影
    project(point: number[]): number[]
    
    // 平面坐标到经纬度反投影
    unproject(point: number[]): number[]
    
    // 投影流处理
    stream?(transform: any): any
}
```

### 4. Parallel (平行坐标系)

**文件结构**:
```
parallel/
├── Parallel.ts              # 平行坐标系
├── ParallelAxis.ts          # 平行轴
├── AxisModel.ts             # 轴模型
├── ParallelModel.ts         # 平行坐标模型
├── parallelCreator.ts       # 创建器
└── parallelPreprocessor.ts  # 预处理器
```

**主要特性**:
- 支持多维数据可视化
- 每个维度对应一个平行轴
- 支持轴的重新排序
- 支持数据刷选

## 坐标轴系统

### 1. 轴的基础架构

```typescript
abstract class Axis {
    type: string
    dim: string
    scale: Scale
    
    // 数据值到像素位置转换
    dataToCoord(data: ScaleDataValue): number
    
    // 像素位置到数据值转换
    coordToData(coord: number): number
    
    // 获取刻度
    getTicksCoords(): number[]
    
    // 获取标签
    getViewLabels(): string[]
}
```

### 2. 刻度系统

```typescript
// 支持的刻度类型
type ScaleType = 'ordinal' | 'interval' | 'time' | 'log'

interface Scale {
    type: ScaleType
    
    // 设置数据范围
    setExtent(start: number, end: number): void
    
    // 获取数据范围
    getExtent(): [number, number]
    
    // 数据值标准化
    normalize(val: ScaleDataValue): number
    
    // 标准化值反转为数据值
    scale(val: number): number
}
```

### 3. 轴配置选项

```typescript
interface AxisOption {
    type?: 'value' | 'category' | 'time' | 'log'
    name?: string
    nameLocation?: 'start' | 'middle' | 'end'
    nameGap?: number
    nameRotate?: number
    inverse?: boolean
    boundaryGap?: boolean | [string | number, string | number]
    min?: number | string | Function
    max?: number | string | Function
    scale?: boolean
    splitNumber?: number
    minInterval?: number
    maxInterval?: number
    interval?: number
    logBase?: number
    silent?: boolean
    triggerEvent?: boolean
    axisLine?: AxisLineOption
    axisTick?: AxisTickOption
    axisLabel?: AxisLabelOption
    splitLine?: SplitLineOption
    splitArea?: SplitAreaOption
    data?: (string | number | AxisDataItemOption)[]
}
```

## 坐标系管理

### 1. 坐标系注册

```typescript
// 坐标系创建器注册
registerCoordinateSystem('cartesian2d', {
    create: function(ecModel: GlobalModel, api: ExtensionAPI) {
        // 创建直角坐标系实例
    },
    dimensions: ['x', 'y']
})
```

### 2. 坐标系生命周期

```typescript
interface CoordinateSystemMaster {
    // 更新坐标系
    update(ecModel: GlobalModel, api: ExtensionAPI): void
    
    // 调整大小
    resize(ecModel: GlobalModel, api: ExtensionAPI): void
    
    // 获取坐标系列表
    getCoordinateSystems(): CoordinateSystem[]
}
```

## 性能优化

### 1. 仿射变换优化
```typescript
// 对于大数据量的时间序列，使用仿射变换加速坐标转换
class Cartesian2D {
    calcAffineTransform() {
        // 计算变换矩阵
        this._transform = [scaleX, 0, 0, scaleY, translateX, translateY]
        this._invTransform = invert(this._transform)
    }
    
    dataToPoint(data: number[]): number[] {
        if (this._transform) {
            // 使用矩阵变换快速计算
            return applyTransform([], data, this._transform)
        }
        // 常规计算
        return [this.getAxis('x').dataToCoord(data[0]), 
                this.getAxis('y').dataToCoord(data[1])]
    }
}
```

### 2. 缓存机制
- 轴刻度计算结果缓存
- 坐标转换结果缓存
- 地理区域边界缓存

### 3. 延迟计算
- 按需计算轴范围
- 延迟创建坐标系实例
- 渐进式地图渲染

## 扩展机制

### 1. 自定义坐标系
```typescript
// 1. 实现坐标系接口
class CustomCoordinateSystem implements CoordinateSystem {
    type = 'custom'
    dimensions = ['x', 'y']
    
    dataToPoint(data: number[]): number[] {
        // 自定义坐标转换逻辑
    }
    
    pointToData(point: number[]): number[] {
        // 自定义反向转换逻辑
    }
}

// 2. 注册坐标系
registerCoordinateSystem('custom', CustomCoordinateSystem)
```

### 2. 自定义投影
```typescript
// 地理坐标系支持自定义投影
const customProjection: GeoProjection = {
    project: (point: number[]) => {
        // 自定义投影算法
        return [x, y]
    },
    unproject: (point: number[]) => {
        // 反投影算法
        return [lng, lat]
    }
}
```

## 重构建议

### 1. 接口统一
- **标准化接口**: 统一坐标系接口定义
- **类型安全**: 加强 TypeScript 类型约束
- **API 一致性**: 统一坐标转换 API

### 2. 性能优化
- **WebGL 支持**: 大数据场景使用 GPU 加速
- **空间索引**: 地理数据使用空间索引
- **并行计算**: 复杂投影计算并行化

### 3. 功能扩展
- **3D 坐标系**: 支持三维坐标系
- **自定义投影**: 更多地图投影支持
- **动态坐标系**: 支持坐标系动画变换

### 4. 代码组织
- **模块解耦**: 减少坐标系间的依赖
- **工具函数**: 提取通用的坐标计算函数
- **测试覆盖**: 增加坐标转换的单元测试

### 5. 开发体验
- **调试工具**: 坐标系可视化调试工具
- **文档完善**: 详细的坐标系使用文档
- **示例丰富**: 各种坐标系的使用示例
