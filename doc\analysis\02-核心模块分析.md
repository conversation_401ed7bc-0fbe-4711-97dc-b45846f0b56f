# ECharts 核心模块分析

## 模块概述

`src/core/` 目录包含了 ECharts 的核心功能模块，是整个框架的基础架构层。

## 核心文件分析

### 1. echarts.ts - 主核心文件

**文件职责**: ECharts 的主要入口和核心实现
**代码行数**: 3280+ 行
**主要功能**:

#### 核心类定义
- `EChartsType`: ECharts 实例的主要类型定义
- 图表实例的生命周期管理
- 事件系统和交互处理
- 渲染调度和更新机制

#### 主要方法
```typescript
// 核心 API 方法
- init(): 初始化图表实例
- setOption(): 设置图表配置
- getOption(): 获取图表配置
- resize(): 调整图表尺寸
- dispose(): 销毁图表实例
- clear(): 清空图表
- refresh(): 刷新图表
```

#### 依赖关系
- **ZRender**: 底层渲染引擎
- **GlobalModel**: 全局数据模型
- **ExtensionAPI**: 扩展 API
- **CoordinateSystemManager**: 坐标系管理器
- **Scheduler**: 任务调度器

#### 设计模式
- **单例模式**: 图表实例管理
- **观察者模式**: 事件系统
- **策略模式**: 渲染策略选择
- **工厂模式**: 组件创建

### 2. lifecycle.ts - 生命周期管理

**文件职责**: 管理 ECharts 的生命周期事件
**代码行数**: 73 行
**主要功能**:

#### 生命周期事件
```typescript
interface LifecycleEvents {
    'afterinit': [EChartsType],                    // 初始化完成
    'series:beforeupdate': [GlobalModel, ExtensionAPI, UpdateLifecycleParams],  // 系列更新前
    'series:layoutlabels': [GlobalModel, ExtensionAPI, UpdateLifecycleParams],  // 标签布局
    'series:transition': [GlobalModel, ExtensionAPI, UpdateLifecycleParams],    // 系列过渡
    'series:afterupdate': [GlobalModel, ExtensionAPI, UpdateLifecycleParams],   // 系列更新后
    'afterupdate': [GlobalModel, ExtensionAPI]     // 更新完成
}
```

#### 更新参数
```typescript
interface UpdateLifecycleParams {
    updatedSeries?: SeriesModel[]           // 更新的系列
    optionChanged?: boolean                 // 配置是否改变
    seriesTransition?: UpdateLifecycleTransitionOpt  // 系列过渡配置
}
```

#### 架构特点
- **事件驱动**: 基于 Eventful 实现
- **解耦设计**: 生命周期与具体实现分离
- **扩展性**: 支持自定义生命周期事件

### 3. Scheduler.ts - 任务调度器

**文件职责**: 管理 ECharts 的任务调度和执行
**代码行数**: 690+ 行
**主要功能**:

#### 任务类型定义
```typescript
type GeneralTask = Task<TaskContext>        // 通用任务
type SeriesTask = Task<SeriesTaskContext>   // 系列任务
type OverallTask = Task<OverallTaskContext> // 整体任务
type StubTask = Task<StubTaskContext>       // 存根任务
```

#### 管道机制
```typescript
type Pipeline = {
    id: string                    // 管道 ID
    head: GeneralTask            // 头任务
    tail: GeneralTask            // 尾任务
    threshold: number            // 阈值
    progressiveEnabled: boolean  // 渐进式渲染
    blockIndex: number          // 块索引
    step: number                // 步长
    count: number               // 计数
    currentTask?: GeneralTask   // 当前任务
    context?: PipelineContext   // 管道上下文
}
```

#### 调度策略
- **渐进式渲染**: 大数据集分块处理
- **任务优先级**: 基于重要性排序
- **资源管理**: 内存和 CPU 使用优化
- **错误恢复**: 任务失败处理机制

### 4. ExtensionAPI.ts - 扩展 API

**文件职责**: 为扩展提供统一的 API 接口
**主要功能**:
- 坐标系转换 API
- 图形绘制 API
- 数据查询 API
- 事件处理 API

### 5. CoordinateSystem.ts - 坐标系管理

**文件职责**: 管理各种坐标系的注册和创建
**主要功能**:
- 坐标系注册机制
- 坐标系实例管理
- 坐标转换接口

### 6. task.ts - 任务系统

**文件职责**: 定义任务的基础结构和执行机制
**主要功能**:
- 任务创建和执行
- 任务上下文管理
- 进度回调处理

### 7. impl.ts - 实现细节

**文件职责**: 核心功能的具体实现
**主要功能**:
- 内部实现细节
- 性能优化代码
- 兼容性处理

### 8. locale.ts - 国际化支持

**文件职责**: 提供国际化和本地化支持
**主要功能**:
- 语言包管理
- 文本本地化
- 区域设置

## 核心架构特点

### 1. 模块化设计
- **职责分离**: 每个模块有明确的职责边界
- **低耦合**: 模块间依赖关系清晰
- **高内聚**: 相关功能集中在同一模块

### 2. 事件驱动架构
- **生命周期事件**: 完整的生命周期管理
- **用户交互事件**: 鼠标、键盘等交互处理
- **数据变更事件**: 数据更新自动触发重绘

### 3. 任务调度系统
- **异步处理**: 避免阻塞主线程
- **渐进式渲染**: 大数据集优化
- **优先级管理**: 重要任务优先执行

### 4. 扩展机制
- **插件化**: 支持功能模块动态加载
- **API 统一**: 扩展开发接口标准化
- **向后兼容**: 保持 API 稳定性

## 性能优化策略

### 1. 渲染优化
- **脏检查**: 只重绘变更部分
- **批量更新**: 合并多次更新操作
- **缓存机制**: 复用计算结果

### 2. 内存管理
- **对象池**: 复用图形对象
- **垃圾回收**: 及时清理无用对象
- **内存监控**: 防止内存泄漏

### 3. 计算优化
- **延迟计算**: 按需计算复杂属性
- **并行处理**: 利用 Web Worker
- **算法优化**: 使用高效算法

## 重构建议

### 1. 代码组织
- **文件拆分**: echarts.ts 文件过大，建议拆分
- **接口抽象**: 提取更多抽象接口
- **类型定义**: 加强 TypeScript 类型约束

### 2. 性能优化
- **懒加载**: 非核心功能延迟加载
- **Tree Shaking**: 更好的死代码消除
- **Bundle 优化**: 减少包体积

### 3. 可维护性
- **文档完善**: 增加详细的 API 文档
- **测试覆盖**: 提高单元测试覆盖率
- **错误处理**: 完善错误处理机制

### 4. 开发体验
- **调试工具**: 提供更好的调试支持
- **开发模式**: 增强开发时的错误提示
- **热更新**: 支持开发时热更新
