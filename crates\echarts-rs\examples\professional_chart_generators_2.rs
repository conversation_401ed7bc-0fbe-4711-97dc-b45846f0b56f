//! 专业图表生成器 - 第二部分
//!
//! 包含科学数据、商业智能、实时监控等图表的生成函数

use std::collections::HashMap;

/// 创建实验散点图
pub fn create_experiment_scatter_chart(title: &str, data: &[super::ErrorBarPoint], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 定义滤镜
    svg.push_str("  <defs>\n");
    svg.push_str("    <filter id=\"glow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n");
    svg.push_str("      <feGaussianBlur stdDeviation=\"2\" result=\"coloredBlur\"/>\n");
    svg.push_str("      <feMerge>\n");
    svg.push_str("        <feMergeNode in=\"coloredBlur\"/>\n");
    svg.push_str("        <feMergeNode in=\"SourceGraphic\"/>\n");
    svg.push_str("      </feMerge>\n");
    svg.push_str("    </filter>\n");
    svg.push_str("  </defs>\n");
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 绘制背景
    svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, chart_height));
    
    if !data.is_empty() {
        // 计算数据范围
        let min_x = data.iter().map(|p| p.x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|p| p.x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|p| p.y - p.error_low).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|p| p.y + p.error_high).fold(f64::NEG_INFINITY, f64::max);
        
        let x_range = max_x - min_x;
        let y_range = max_y - min_y;
        
        // 绘制网格线
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", x, chart_y, x, chart_y + chart_height));
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));
            
            // 坐标轴标签
            let x_value = min_x + (i as f64 / 5.0) * x_range;
            let y_value = max_y - (i as f64 / 5.0) * y_range;
            
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{:.1}</text>\n", x, chart_y + chart_height + 20.0, x_value));
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.1}</text>\n", chart_x - 10.0, y + 4.0, y_value));
        }
        
        // 绘制数据点和误差条
        for point in data {
            let px = chart_x + ((point.x - min_x) / x_range) * chart_width;
            let py = chart_y + chart_height - ((point.y - min_y) / y_range) * chart_height;
            let py_low = chart_y + chart_height - ((point.y - point.error_low - min_y) / y_range) * chart_height;
            let py_high = chart_y + chart_height - ((point.y + point.error_high - min_y) / y_range) * chart_height;
            
            // 误差条
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", px, py_low, px, py_high));
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", px - 3.0, py_low, px + 3.0, py_low));
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n", px - 3.0, py_high, px + 3.0, py_high));
            
            // 数据点
            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"6\" fill=\"#2196f3\" stroke=\"white\" stroke-width=\"2\" filter=\"url(#glow)\"/>\n", px, py));
        }
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建回归分析图表
pub fn create_regression_chart(title: &str, data: &(Vec<(f64, f64)>, Vec<(f64, f64)>), width: f64, height: f64) -> String {
    let (scatter_data, regression_line) = data;
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 绘制背景
    svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, chart_height));
    
    if !scatter_data.is_empty() {
        // 计算数据范围
        let mut all_values: Vec<f64> = scatter_data.iter().map(|(_, y)| *y).collect();
        all_values.extend(regression_line.iter().map(|(_, y)| *y));
        
        let min_x = scatter_data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = scatter_data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = all_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_y = all_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        
        let x_range = max_x - min_x;
        let y_range = max_y - min_y;
        
        // 绘制网格线
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", x, chart_y, x, chart_y + chart_height));
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));
        }
        
        // 绘制回归线
        if !regression_line.is_empty() {
            let mut path = String::from("M");
            
            for (i, (x, y)) in regression_line.iter().enumerate() {
                let px = chart_x + ((x - min_x) / x_range) * chart_width;
                let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;
                
                if i == 0 {
                    path.push_str(&format!(" {} {}", px, py));
                } else {
                    path.push_str(&format!(" L {} {}", px, py));
                }
            }
            
            svg.push_str(&format!("  <path d=\"{}\" stroke=\"#f44336\" stroke-width=\"3\" fill=\"none\"/>\n", path));
        }
        
        // 绘制散点
        for (x, y) in scatter_data {
            let px = chart_x + ((x - min_x) / x_range) * chart_width;
            let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;
            
            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#2196f3\" stroke=\"white\" stroke-width=\"1\" opacity=\"0.7\"/>\n", px, py));
        }
        
        // 图例
        svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#2196f3\" stroke=\"white\" stroke-width=\"1\"/>\n", chart_x + chart_width + 20.0, chart_y + 20.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">观测数据</text>\n", chart_x + chart_width + 35.0, chart_y + 25.0));
        
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f44336\" stroke-width=\"3\"/>\n", chart_x + chart_width + 20.0, chart_y + 40.0, chart_x + chart_width + 30.0, chart_y + 40.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">回归线</text>\n", chart_x + chart_width + 35.0, chart_y + 45.0));
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建误差条图表
pub fn create_error_bar_chart(title: &str, data: &[super::ErrorBarPoint], width: f64, height: f64) -> String {
    create_experiment_scatter_chart(title, data, width, height)
}

/// 创建相关性分析图表
pub fn create_correlation_chart(title: &str, data: &HashMap<String, Vec<(f64, f64)>>, width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 200.0;
    let chart_height = height - 120.0;
    
    // 绘制背景
    svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, chart_height));
    
    if !data.is_empty() {
        // 计算全局范围
        let mut all_values = Vec::new();
        for series in data.values() {
            all_values.extend(series.iter().map(|(_, y)| *y));
        }
        
        let min_y = all_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_y = all_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let y_range = max_y - min_y;
        
        let max_x = data.values().map(|s| s.len()).max().unwrap_or(0) as f64;
        
        // 绘制网格线
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));
            
            let value = max_y - (i as f64 / 5.0) * y_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.1}</text>\n", chart_x - 10.0, y + 4.0, value));
        }
        
        // 颜色数组
        let colors = vec!["2196f3", "4caf50", "ff9800", "f44336"];
        
        // 绘制每个变量的曲线
        for (idx, (name, series)) in data.iter().enumerate() {
            let color = &colors[idx % colors.len()];
            
            if !series.is_empty() {
                let mut path = String::from("M");
                
                for (i, (_, y)) in series.iter().enumerate() {
                    let px = chart_x + (i as f64 / max_x) * chart_width;
                    let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;
                    
                    if i == 0 {
                        path.push_str(&format!(" {} {}", px, py));
                    } else {
                        path.push_str(&format!(" L {} {}", px, py));
                    }
                }
                
                svg.push_str("  <path d=\"");
                svg.push_str(&path);
                svg.push_str("\" stroke=\"#");
                svg.push_str(color);
                svg.push_str("\" stroke-width=\"2\" fill=\"none\"/>\n");
                
                // 图例
                let legend_y = chart_y + 30.0 + idx as f64 * 25.0;
                svg.push_str("  <line x1=\"");
                svg.push_str(&(chart_x + chart_width + 20.0).to_string());
                svg.push_str("\" y1=\"");
                svg.push_str(&legend_y.to_string());
                svg.push_str("\" x2=\"");
                svg.push_str(&(chart_x + chart_width + 40.0).to_string());
                svg.push_str("\" y2=\"");
                svg.push_str(&legend_y.to_string());
                svg.push_str("\" stroke=\"#");
                svg.push_str(color);
                svg.push_str("\" stroke-width=\"2\"/>\n");
                svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">{}</text>\n", chart_x + chart_width + 45.0, legend_y + 4.0, name));
            }
        }
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建KPI仪表板
pub fn create_kpi_dashboard(title: &str, data: &[super::KpiData], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 定义渐变
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"positiveGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#4caf50;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#8bc34a;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("    <linearGradient id=\"negativeGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#f44336;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#ff5722;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f5f5f5\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"24\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 计算布局
    let cols = 2;
    let rows = (data.len() + cols - 1) / cols;
    let kpi_width = (width - 100.0) / cols as f64;
    let kpi_height = (height - 100.0) / rows as f64;
    
    for (idx, kpi) in data.iter().enumerate() {
        let col = idx % cols;
        let row = idx / cols;
        
        let x = 50.0 + col as f64 * kpi_width;
        let y = 60.0 + row as f64 * kpi_height;
        
        // KPI卡片背景
        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\" rx=\"8\"/>\n", 
            x, y, kpi_width - 20.0, kpi_height - 20.0));
        
        // KPI名称
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"16\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", 
            x + 20.0, y + 30.0, kpi.name));
        
        // 当前值
        let is_positive = kpi.current_value >= kpi.target_value;
        let value_color = if is_positive { "#4caf50" } else { "#f44336" };
        
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"32\" font-weight=\"bold\" fill=\"{}\">{:.0}{}</text>\n", 
            x + 20.0, y + 70.0, value_color, kpi.current_value, kpi.unit));
        
        // 目标值
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"14\" fill=\"#666\">目标: {:.0}{}</text>\n", 
            x + 20.0, y + 90.0, kpi.target_value, kpi.unit));
        
        // 趋势图
        if !kpi.trend.is_empty() {
            let trend_x = x + 20.0;
            let trend_y = y + 110.0;
            let trend_width = kpi_width - 60.0;
            let trend_height = 60.0;
            
            let min_trend = kpi.trend.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
            let max_trend = kpi.trend.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
            let trend_range = max_trend - min_trend;
            
            let mut trend_path = String::from("M");
            
            for (i, (_, value)) in kpi.trend.iter().enumerate() {
                let px = trend_x + (i as f64 / kpi.trend.len() as f64) * trend_width;
                let py = trend_y + trend_height - ((value - min_trend) / trend_range) * trend_height;
                
                if i == 0 {
                    trend_path.push_str(&format!(" {} {}", px, py));
                } else {
                    trend_path.push_str(&format!(" L {} {}", px, py));
                }
            }
            
            svg.push_str(&format!("  <path d=\"{}\" stroke=\"{}\" stroke-width=\"2\" fill=\"none\"/>\n", trend_path, value_color));
        }
    }
    
    svg.push_str("</svg>");
    svg
}
