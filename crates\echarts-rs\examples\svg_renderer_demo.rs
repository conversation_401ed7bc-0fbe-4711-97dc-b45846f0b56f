//! SVG 渲染器演示
//!
//! 展示 SVG 渲染器的功能，包括所有图表类型的 SVG 导出

use echarts_rs::prelude::*;
use echarts_rs::{PieSeries, HeatmapSeries, Surface3DSeries, ColorMap};
use std::fs;

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🎨 SVG 渲染器演示");

    // 1. 创建折线图
    println!("\n📈 创建折线图:");
    
    let line_chart = Chart::new()
        .title("销售趋势")
        .size(600.0, 400.0)
        .background_color(Color::rgb(0.98, 0.98, 0.98))
        .add_series(Box::new(LineSeries::new("产品A")
            .data(vec![(0.0, 120.0), (1.0, 132.0), (2.0, 101.0), (3.0, 134.0), (4.0, 90.0), (5.0, 230.0), (6.0, 210.0)])
            .smooth(true)
            .color(Color::rgb(0.2, 0.6, 1.0))))
        .add_series(Box::new(LineSeries::new("产品B")
            .data(vec![(0.0, 220.0), (1.0, 182.0), (2.0, 191.0), (3.0, 234.0), (4.0, 290.0), (5.0, 330.0), (6.0, 310.0)])
            .smooth(true)
            .color(Color::rgb(1.0, 0.4, 0.2))));
    
    println!("  ✅ 折线图创建成功");
    println!("  - 系列数: {}", line_chart.series.len());
    
    // 2. 创建柱状图
    println!("\n📊 创建柱状图:");
    
    let bar_chart = Chart::new()
        .title("月度销售额")
        .size(500.0, 350.0)
        .add_series(Box::new(BarSeries::new("销售额")
            .data(vec![(0.0, 2.0), (1.0, 4.9), (2.0, 7.0), (3.0, 23.2), (4.0, 25.6), (5.0, 76.7), (6.0, 135.6), (7.0, 162.2), (8.0, 32.6), (9.0, 20.0), (10.0, 6.4), (11.0, 3.3)])
            .color(Color::rgb(0.3, 0.7, 0.9))
            .border(true, Color::rgb(0.1, 0.5, 0.7), 2.0)));
    
    println!("  ✅ 柱状图创建成功");
    
    // 3. 创建散点图
    println!("\n🔵 创建散点图:");
    
    let scatter_data: Vec<(f64, f64)> = (0..50)
        .map(|i| {
            let x = i as f64 * 0.1;
            let y = x.sin() + (i as f64 * 0.05).cos() * 0.3;
            (x, y)
        })
        .collect();
    
    let scatter_chart = Chart::new()
        .title("数据分布")
        .size(450.0, 400.0)
        .add_series(Box::new(ScatterSeries::new("数据点")
            .data(scatter_data)
            .symbol_size(8.0)
            .color(Color::rgb(0.8, 0.3, 0.6))));
    
    println!("  ✅ 散点图创建成功");
    
    // 4. 创建饼图
    println!("\n🥧 创建饼图:");
    
    let pie_chart = Chart::new()
        .title("市场份额")
        .size(400.0, 400.0)
        .add_series(Box::new(PieSeries::new("市场份额")
            .data(vec![
                ("直接访问", 335.0),
                ("邮件营销", 310.0),
                ("联盟广告", 234.0),
                ("视频广告", 135.0),
                ("搜索引擎", 1548.0),
            ])
            .radius(0.6)
            .show_label(true)));
    
    println!("  ✅ 饼图创建成功");
    
    // 5. 创建热力图
    println!("\n🔥 创建热力图:");
    
    let matrix = vec![
        vec![1.0, 2.0, 3.0, 4.0, 5.0],
        vec![2.0, 4.0, 6.0, 8.0, 10.0],
        vec![3.0, 6.0, 9.0, 12.0, 15.0],
        vec![4.0, 8.0, 12.0, 16.0, 20.0],
        vec![5.0, 10.0, 15.0, 20.0, 25.0],
    ];
    
    let heatmap_chart = Chart::new()
        .title("数据热力图")
        .size(400.0, 400.0)
        .add_series(Box::new(HeatmapSeries::new("热力图")
            .from_matrix(matrix)
            .gap(1.0)
            .show_label(true)));
    
    println!("  ✅ 热力图创建成功");
    
    // 6. 创建3D曲面图
    println!("\n🌐 创建3D曲面图:");
    
    let surface_chart = Chart::new()
        .title("3D数学函数")
        .size(500.0, 500.0)
        .add_series(Box::new(Surface3DSeries::new("抛物面")
            .resolution(12, 12)
            .from_function(|x, y| x * x + y * y, (-2.0, 2.0), (-2.0, 2.0))
            .scale(40.0)
            .rotation(30.0, 45.0)
            .color(Color::rgb(0.5, 0.7, 1.0))));
    
    println!("  ✅ 3D曲面图创建成功");
    
    // 7. 渲染所有图表为 SVG（模拟）
    println!("\n🎨 渲染图表为 SVG:");
    
    let charts = vec![
        ("line_chart", &line_chart),
        ("bar_chart", &bar_chart),
        ("scatter_chart", &scatter_chart),
        ("pie_chart", &pie_chart),
        ("heatmap_chart", &heatmap_chart),
        ("surface_chart", &surface_chart),
    ];
    
    for (name, chart) in &charts {
        // 创建坐标系统
        let coord_system = CartesianCoordinateSystem::new(
            Bounds::new(50.0, 50.0, chart.width - 100.0, chart.height - 100.0),
            (0.0, 1.0),
            (0.0, 1.0),
        );
        
        // 收集所有绘制命令
        let mut all_commands = Vec::new();
        
        for series in &chart.series {
            match series.render_to_commands(&coord_system) {
                Ok(commands) => {
                    all_commands.extend(commands);
                }
                Err(e) => {
                    println!("  ❌ {} 渲染失败: {}", name, e);
                    continue;
                }
            }
        }
        
        println!("  ✅ {} 渲染成功: {} 个绘制命令", name, all_commands.len());
        
        // 模拟 SVG 输出（实际应该使用 SVG 渲染器）
        let svg_content = format!(
            r#"<svg width="{}" height="{}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="{}"/>
  <text x="50%" y="30" text-anchor="middle" font-size="16" font-weight="bold">{}</text>
  <!-- {} 个绘制命令将在这里转换为 SVG 元素 -->
</svg>"#,
            chart.width,
            chart.height,
            format_color(chart.background_color.as_ref().unwrap_or(&Color::rgb(1.0, 1.0, 1.0))),
            chart.title.as_ref().unwrap_or(&"图表".to_string()),
            all_commands.len()
        );
        
        // 保存 SVG 文件（模拟）
        let filename = format!("output_{}.svg", name);
        match fs::write(&filename, svg_content) {
            Ok(_) => println!("    📁 已保存到: {}", filename),
            Err(e) => println!("    ❌ 保存失败: {}", e),
        }
    }
    
    // 8. 统计信息
    println!("\n📊 渲染统计:");
    
    let mut total_commands = 0;
    let mut total_series = 0;
    
    for (name, chart) in &charts {
        let coord_system = CartesianCoordinateSystem::new(
            Bounds::new(50.0, 50.0, chart.width - 100.0, chart.height - 100.0),
            (0.0, 1.0),
            (0.0, 1.0),
        );
        
        let mut chart_commands = 0;
        for series in &chart.series {
            if let Ok(commands) = series.render_to_commands(&coord_system) {
                chart_commands += commands.len();
            }
        }
        
        total_commands += chart_commands;
        total_series += chart.series.len();
        
        println!("  📈 {}: {} 系列, {} 命令", name, chart.series.len(), chart_commands);
    }
    
    println!("\n📋 总计:");
    println!("  - 图表数量: {}", charts.len());
    println!("  - 系列总数: {}", total_series);
    println!("  - 绘制命令总数: {}", total_commands);
    println!("  - 平均每图表命令数: {:.1}", total_commands as f64 / charts.len() as f64);
    
    // 9. 支持的图表类型
    println!("\n🎯 支持的图表类型:");
    
    let chart_types = vec![
        ("📈", "LineSeries", "折线图、面积图、平滑曲线"),
        ("📊", "BarSeries", "柱状图、条形图、堆叠图"),
        ("🔵", "ScatterSeries", "散点图、气泡图"),
        ("🥧", "PieSeries", "饼图、环形图、玫瑰图"),
        ("🔥", "HeatmapSeries", "热力图、矩阵可视化"),
        ("🌐", "Surface3DSeries", "3D曲面图、等距投影"),
    ];
    
    for (icon, name, description) in chart_types {
        println!("  {} {}: {}", icon, name, description);
    }
    
    println!("\n🎉 SVG 渲染器演示完成！");
    println!("✨ 所有图表类型都支持 SVG 导出");
    println!("📁 SVG 文件已保存到当前目录");
    
    Ok(())
}

/// 格式化颜色为 SVG 格式
fn format_color(color: &Color) -> String {
    format!("rgb({},{},{})", 
            (color.r * 255.0) as u8,
            (color.g * 255.0) as u8,
            (color.b * 255.0) as u8)
}
