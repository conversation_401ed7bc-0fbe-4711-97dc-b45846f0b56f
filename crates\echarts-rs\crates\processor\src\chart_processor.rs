/*!
 * 图表处理器
 * 
 * 负责解析不同类型的ECharts图表配置
 */

use crate::{ProcessedChart, DrawOperation, Point, Bounds, Color, TextAlign, Result, ProcessorError};
use serde_json::Value;

/// 图表类型
#[derive(Debug, Clone)]
pub enum ChartType {
    Bar,
    Line,
    Pie,
    Scatter,
    Area,
    Unknown(String),
}

/// 系列数据处理器
pub struct SeriesProcessor;

impl SeriesProcessor {
    /// 处理系列数据
    pub fn process_series(
        series: &Value,
        plot_area: Bounds,
        operations: &mut Vec<DrawOperation>,
    ) -> Result<()> {
        let chart_type = Self::determine_chart_type(series);
        let data = series.get("data").and_then(|d| d.as_array());

        if let Some(data_array) = data {
            match chart_type {
                ChartType::Bar => Self::process_bar_series(data_array, plot_area, operations)?,
                ChartType::Line => Self::process_line_series(data_array, plot_area, operations)?,
                ChartType::Pie => Self::process_pie_series(data_array, plot_area, operations)?,
                ChartType::Scatter => Self::process_scatter_series(data_array, plot_area, operations)?,
                ChartType::Area => Self::process_area_series(data_array, plot_area, operations)?,
                ChartType::Unknown(type_name) => {
                    println!("⚠️  不支持的图表类型: {}", type_name);
                    Self::process_bar_series(data_array, plot_area, operations)?; // 默认使用柱状图
                }
            }
        }

        Ok(())
    }

    /// 确定图表类型
    fn determine_chart_type(series: &Value) -> ChartType {
        match series.get("type").and_then(|t| t.as_str()) {
            Some("bar") => ChartType::Bar,
            Some("line") => ChartType::Line,
            Some("pie") => ChartType::Pie,
            Some("scatter") => ChartType::Scatter,
            Some("area") => ChartType::Area,
            Some(other) => ChartType::Unknown(other.to_string()),
            None => ChartType::Line, // 默认类型
        }
    }

    /// 处理柱状图系列
    fn process_bar_series(
        data: &[Value],
        plot_area: Bounds,
        operations: &mut Vec<DrawOperation>,
    ) -> Result<()> {
        let bar_count = data.len();
        if bar_count == 0 {
            return Ok(());
        }

        let bar_width = plot_area.width / (bar_count as f64 + 1.0);
        let max_value = data
            .iter()
            .filter_map(|v| v.as_f64())
            .fold(0.0f64, |a, b| a.max(b));

        if max_value <= 0.0 {
            return Ok(());
        }

        for (i, value) in data.iter().enumerate() {
            if let Some(val) = value.as_f64() {
                let x = plot_area.x + (i as f64 + 1.0) * bar_width;
                let height = (val / max_value) * plot_area.height * 0.8;
                let y = plot_area.y + plot_area.height - height;

                let bar_bounds = Bounds::new(x - bar_width * 0.3, y, bar_width * 0.6, height);

                operations.push(DrawOperation::Rect {
                    bounds: bar_bounds,
                    fill_color: Color::BLUE,
                    stroke_color: None,
                    stroke_width: 0.0,
                });
            }
        }

        Ok(())
    }

    /// 处理折线图系列
    fn process_line_series(
        data: &[Value],
        plot_area: Bounds,
        operations: &mut Vec<DrawOperation>,
    ) -> Result<()> {
        let point_count = data.len();
        if point_count < 2 {
            return Ok(());
        }

        let step_x = plot_area.width / (point_count as f64 - 1.0);
        let max_value = data
            .iter()
            .filter_map(|v| v.as_f64())
            .fold(0.0f64, |a, b| a.max(b));

        if max_value <= 0.0 {
            return Ok(());
        }

        let mut points = Vec::new();
        for (i, value) in data.iter().enumerate() {
            if let Some(val) = value.as_f64() {
                let x = plot_area.x + i as f64 * step_x;
                let y = plot_area.y + plot_area.height - (val / max_value) * plot_area.height * 0.8;
                points.push(Point::new(x, y));
            }
        }

        // 生成连线操作
        for i in 0..points.len() - 1 {
            operations.push(DrawOperation::Line {
                from: points[i],
                to: points[i + 1],
                color: Color::BLUE,
                width: 2.0,
                dash_pattern: None,
            });
        }

        // 生成数据点操作
        for point in points {
            operations.push(DrawOperation::Circle {
                center: point,
                radius: 4.0,
                fill_color: Color::BLUE,
                stroke_color: None,
                stroke_width: 0.0,
            });
        }

        Ok(())
    }

    /// 处理饼图系列
    fn process_pie_series(
        data: &[Value],
        plot_area: Bounds,
        operations: &mut Vec<DrawOperation>,
    ) -> Result<()> {
        let center = Point::new(
            plot_area.x + plot_area.width / 2.0,
            plot_area.y + plot_area.height / 2.0,
        );
        let radius = plot_area.width.min(plot_area.height) / 3.0;

        // 计算数据总和
        let total: f64 = data
            .iter()
            .filter_map(|v| {
                if let Some(obj) = v.as_object() {
                    obj.get("value").and_then(|v| v.as_f64())
                } else {
                    v.as_f64()
                }
            })
            .sum();

        if total <= 0.0 {
            return Ok(());
        }

        // 定义饼图颜色
        let colors = [
            Color::rgb_u8(51, 153, 229),    // 蓝色
            Color::rgb_u8(229, 102, 51),    // 橙色
            Color::rgb_u8(76, 204, 76),     // 绿色
            Color::rgb_u8(229, 204, 51),    // 黄色
            Color::rgb_u8(204, 76, 204),    // 紫色
            Color::rgb_u8(51, 204, 204),    // 青色
        ];

        let mut current_angle = 0.0; // 从0度开始

        // 为每个数据项生成扇形
        for (i, value_data) in data.iter().enumerate() {
            let value = if let Some(obj) = value_data.as_object() {
                obj.get("value").and_then(|v| v.as_f64()).unwrap_or(0.0)
            } else {
                value_data.as_f64().unwrap_or(0.0)
            };

            if value <= 0.0 {
                continue;
            }

            let percentage = value / total;
            let slice_angle = 360.0 * percentage; // 扇形角度
            let end_angle = current_angle + slice_angle;

            // 选择颜色
            let color = colors[i % colors.len()];

            // 生成扇形路径点
            let path_points = Self::generate_pie_slice_points(
                center,
                radius,
                current_angle,
                end_angle,
                32 // 分段数，用于近似圆弧
            );

            // 添加扇形路径操作
            operations.push(DrawOperation::Path {
                points: path_points,
                fill_color: Some(color),
                stroke_color: Some(Color::WHITE),
                stroke_width: 2.0,
                closed: true,
            });

            current_angle = end_angle;
        }

        Ok(())
    }

    /// 生成饼图扇形的路径点
    fn generate_pie_slice_points(
        center: Point,
        radius: f64,
        start_angle: f64,
        end_angle: f64,
        segments: usize,
    ) -> Vec<Point> {
        let mut points = Vec::new();

        // 添加中心点
        points.push(center);

        // 生成圆弧上的点
        let angle_step = (end_angle - start_angle) / segments as f64;
        for i in 0..=segments {
            let angle = start_angle + i as f64 * angle_step;
            let radian = angle.to_radians();
            let x = center.x + radius * radian.cos();
            let y = center.y + radius * radian.sin();
            points.push(Point::new(x, y));
        }

        // 回到中心点闭合路径
        points.push(center);

        points
    }

    /// 处理散点图系列
    fn process_scatter_series(
        data: &[Value],
        plot_area: Bounds,
        operations: &mut Vec<DrawOperation>,
    ) -> Result<()> {
        for (i, value) in data.iter().enumerate() {
            if let Some(val) = value.as_f64() {
                let x = plot_area.x + (i as f64 / data.len() as f64) * plot_area.width;
                let y = plot_area.y + plot_area.height / 2.0 + (val - 25.0) * 5.0;

                operations.push(DrawOperation::Circle {
                    center: Point::new(x, y),
                    radius: 6.0,
                    fill_color: Color::RED,
                    stroke_color: None,
                    stroke_width: 0.0,
                });
            }
        }

        Ok(())
    }

    /// 处理面积图系列
    fn process_area_series(
        data: &[Value],
        plot_area: Bounds,
        operations: &mut Vec<DrawOperation>,
    ) -> Result<()> {
        let point_count = data.len();
        if point_count < 2 {
            return Ok(());
        }

        let step_x = plot_area.width / (point_count as f64 - 1.0);
        let max_value = data
            .iter()
            .filter_map(|v| v.as_f64())
            .fold(0.0f64, |a, b| a.max(b));

        if max_value <= 0.0 {
            return Ok(());
        }

        let mut points = Vec::new();
        
        // 添加底部起始点
        points.push(Point::new(plot_area.x, plot_area.y + plot_area.height));
        
        // 添加数据点
        for (i, value) in data.iter().enumerate() {
            if let Some(val) = value.as_f64() {
                let x = plot_area.x + i as f64 * step_x;
                let y = plot_area.y + plot_area.height - (val / max_value) * plot_area.height * 0.8;
                points.push(Point::new(x, y));
            }
        }
        
        // 添加底部结束点
        points.push(Point::new(
            plot_area.x + plot_area.width,
            plot_area.y + plot_area.height,
        ));

        // 生成面积路径
        operations.push(DrawOperation::Path {
            points,
            fill_color: Some(Color::rgba_u8(84, 112, 198, 100)), // 半透明填充
            stroke_color: Some(Color::BLUE),
            stroke_width: 2.0,
            closed: true,
        });

        Ok(())
    }
}

/// 标题处理器
pub struct TitleProcessor;

impl TitleProcessor {
    /// 处理标题
    pub fn process_title(
        title_config: &Value,
        bounds: Bounds,
        operations: &mut Vec<DrawOperation>,
    ) -> Result<()> {
        if let Some(text) = title_config.get("text").and_then(|t| t.as_str()) {
            let font_size = title_config
                .get("textStyle")
                .and_then(|ts| ts.get("fontSize"))
                .and_then(|fs| fs.as_f64())
                .unwrap_or(18.0);

            let position = Point::new(bounds.x + bounds.width / 2.0, bounds.y + 30.0);

            operations.push(DrawOperation::Text {
                text: text.to_string(),
                position,
                font_size,
                color: Color::BLACK,
                font_family: "Arial".to_string(),
                text_align: TextAlign::Center,
            });
        }

        Ok(())
    }
}
