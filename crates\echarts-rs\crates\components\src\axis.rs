//! Axis component implementation

use crate::{Component, Renderable, Themeable};
use echarts_core::*;
use echarts_core::RenderContext;
use echarts_themes::Theme;
use serde::{Deserialize, Serialize};
use std::any::Any;

/// Axis component for coordinate system axes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Axis {
    /// Whether the axis is visible
    pub visible: bool,

    /// Axis type
    pub axis_type: AxisType,

    /// Axis position
    pub position: AxisPosition,

    /// Axis name
    pub name: Option<String>,

    /// Axis name text style
    pub name_text_style: TextStyle,

    /// Whether to show axis line
    pub show_line: bool,

    /// Axis line style
    pub line_style: LineStyle,

    /// Whether to show tick marks
    pub show_ticks: bool,

    /// Tick mark style
    pub tick_style: LineStyle,

    /// Whether to show labels
    pub show_labels: bool,

    /// Label text style
    pub label_text_style: TextStyle,

    /// Label formatter
    pub label_formatter: Option<String>,

    /// Minimum value (for value axes)
    pub min: Option<f64>,

    /// Maximum value (for value axes)
    pub max: Option<f64>,

    /// Categories (for category axes)
    pub categories: Vec<String>,
}

// AxisType is re-exported from echarts_core

/// Axis positions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AxisPosition {
    /// Bottom position (for X axis)
    Bottom,
    /// Top position (for X axis)
    Top,
    /// Left position (for Y axis)
    Left,
    /// Right position (for Y axis)
    Right,
}

impl Default for Axis {
    fn default() -> Self {
        Axis {
            visible: true,
            axis_type: AxisType::Value,
            position: AxisPosition::Bottom,
            name: None,
            name_text_style: TextStyle::default(),
            show_line: true,
            line_style: LineStyle::default(),
            show_ticks: true,
            tick_style: LineStyle::default(),
            show_labels: true,
            label_text_style: TextStyle::default(),
            label_formatter: None,
            min: None,
            max: None,
            categories: Vec::new(),
        }
    }
}

impl Axis {
    /// Create a new axis with default settings
    pub fn new() -> Self {
        Self::default()
    }

    /// Create a category axis
    pub fn category() -> Self {
        Self {
            axis_type: AxisType::Category,
            ..Self::default()
        }
    }

    /// Create a value axis (linear)
    pub fn value() -> Self {
        Self {
            axis_type: AxisType::Value,
            ..Self::default()
        }
    }

    /// Set axis type
    pub fn axis_type(mut self, axis_type: AxisType) -> Self {
        self.axis_type = axis_type;
        self
    }

    /// Set axis position
    pub fn position(mut self, position: AxisPosition) -> Self {
        self.position = position;
        self
    }

    /// Set axis name
    pub fn name<S: Into<String>>(mut self, name: S) -> Self {
        self.name = Some(name.into());
        self
    }
}

impl Component for Axis {
    fn component_type(&self) -> &'static str {
        "axis"
    }

    fn is_visible(&self) -> bool {
        self.visible
    }

    fn set_visible(&mut self, visible: bool) {
        self.visible = visible;
    }
}

impl Renderable for Axis {
    fn render<R: ChartRenderer>(&self, ctx: &mut RenderContext<R>, bounds: Bounds) -> Result<()> {
        if !self.visible {
            return Ok(());
        }

        // Calculate axis line position
        let axis_line = self.calculate_axis_line(bounds);

        // Draw axis line
        ctx.draw_line(
            axis_line.0,
            axis_line.1,
            self.line_style.color,
            self.line_style.width,
        );

        // Draw axis name if present
        if let Some(ref name) = self.name {
            let name_pos = self.calculate_name_position(bounds);
            ctx.draw_text(name.clone(), name_pos, self.name_text_style.clone());
        }

        // Draw tick marks and labels
        self.draw_ticks_and_labels(ctx, bounds)?;

        Ok(())
    }
}

impl Themeable for Axis {
    fn apply_theme(&mut self, theme: &Theme) {
        self.name_text_style.color = theme.text_style.color;
        self.label_text_style.color = theme.text_style.color;
        self.line_style.color = theme.text_style.color;
        self.tick_style.color = theme.text_style.color;
    }
}

impl Axis {
    /// Calculate axis line start and end points
    fn calculate_axis_line(&self, bounds: Bounds) -> (Point, Point) {
        match self.position {
            AxisPosition::Bottom => (
                Point::new(bounds.origin.x, bounds.origin.y + bounds.height()),
                Point::new(
                    bounds.origin.x + bounds.width(),
                    bounds.origin.y + bounds.height(),
                ),
            ),
            AxisPosition::Top => (
                Point::new(bounds.origin.x, bounds.origin.y),
                Point::new(bounds.origin.x + bounds.width(), bounds.origin.y),
            ),
            AxisPosition::Left => (
                Point::new(bounds.origin.x, bounds.origin.y),
                Point::new(bounds.origin.x, bounds.origin.y + bounds.height()),
            ),
            AxisPosition::Right => (
                Point::new(bounds.origin.x + bounds.width(), bounds.origin.y),
                Point::new(
                    bounds.origin.x + bounds.width(),
                    bounds.origin.y + bounds.height(),
                ),
            ),
        }
    }

    /// Calculate axis name position
    fn calculate_name_position(&self, bounds: Bounds) -> Point {
        match self.position {
            AxisPosition::Bottom => Point::new(
                bounds.origin.x + bounds.width() / 2.0,
                bounds.origin.y + bounds.height() + 40.0,
            ),
            AxisPosition::Top => Point::new(
                bounds.origin.x + bounds.width() / 2.0,
                bounds.origin.y - 20.0,
            ),
            AxisPosition::Left => Point::new(
                bounds.origin.x - 40.0,
                bounds.origin.y + bounds.height() / 2.0,
            ),
            AxisPosition::Right => Point::new(
                bounds.origin.x + bounds.width() + 40.0,
                bounds.origin.y + bounds.height() / 2.0,
            ),
        }
    }

    /// Draw tick marks and labels
    fn draw_ticks_and_labels<R: ChartRenderer>(&self, ctx: &mut RenderContext<R>, bounds: Bounds) -> Result<()> {
        // Simplified tick drawing - would normally be based on data range and scale
        let num_ticks = 5;
        let tick_length = 6.0; // Fixed tick length

        for i in 0..=num_ticks {
            let t = i as f64 / num_ticks as f64;

            let (tick_start, tick_end, label_pos) = match self.position {
                AxisPosition::Bottom => {
                    let x = bounds.origin.x + bounds.width() * t;
                    let y = bounds.origin.y + bounds.height();
                    (
                        Point::new(x, y),
                        Point::new(x, y + tick_length),
                        Point::new(x, y + tick_length + 15.0),
                    )
                }
                AxisPosition::Top => {
                    let x = bounds.origin.x + bounds.width() * t;
                    let y = bounds.origin.y;
                    (
                        Point::new(x, y),
                        Point::new(x, y - tick_length),
                        Point::new(x, y - tick_length - 15.0),
                    )
                }
                AxisPosition::Left => {
                    let x = bounds.origin.x;
                    let y = bounds.origin.y + bounds.height() * (1.0 - t);
                    (
                        Point::new(x, y),
                        Point::new(x - tick_length, y),
                        Point::new(x - tick_length - 30.0, y),
                    )
                }
                AxisPosition::Right => {
                    let x = bounds.origin.x + bounds.width();
                    let y = bounds.origin.y + bounds.height() * (1.0 - t);
                    (
                        Point::new(x, y),
                        Point::new(x + tick_length, y),
                        Point::new(x + tick_length + 10.0, y),
                    )
                }
            };

            // Draw tick mark
            ctx.draw_line(
                tick_start,
                tick_end,
                self.tick_style.color,
                self.tick_style.width,
            );

            // Draw label
            let label = format!("{}", i * 20); // Simplified label
            ctx.draw_text(label, label_pos, self.label_text_style.clone());
        }

        Ok(())
    }
}

impl ChartComponent for Axis {
    fn component_type(&self) -> &'static str {
        "axis"
    }

    fn is_visible(&self) -> bool {
        self.visible
    }

    fn render_to_commands(&self, bounds: Bounds) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        if !self.visible {
            return Ok(commands);
        }

        // 根据轴的位置和类型生成绘制命令
        match self.position {
            AxisPosition::Bottom => {
                // X轴在底部
                if self.show_line {
                    commands.push(DrawCommand::Line {
                        from: Point::new(bounds.origin.x, bounds.origin.y + bounds.size.height),
                        to: Point::new(bounds.origin.x + bounds.size.width, bounds.origin.y + bounds.size.height),
                        style: self.line_style.clone(),
                    });
                }

                // 添加刻度和标签
                if self.show_ticks || self.show_labels {
                    self.add_bottom_axis_ticks_and_labels(&mut commands, bounds)?;
                }
            },
            AxisPosition::Left => {
                // Y轴在左侧
                if self.show_line {
                    commands.push(DrawCommand::Line {
                        from: Point::new(bounds.origin.x, bounds.origin.y),
                        to: Point::new(bounds.origin.x, bounds.origin.y + bounds.size.height),
                        style: self.line_style.clone(),
                    });
                }

                // 添加刻度和标签
                if self.show_ticks || self.show_labels {
                    self.add_left_axis_ticks_and_labels(&mut commands, bounds)?;
                }
            },
            AxisPosition::Top => {
                // X轴在顶部
                if self.show_line {
                    commands.push(DrawCommand::Line {
                        from: Point::new(bounds.origin.x, bounds.origin.y),
                        to: Point::new(bounds.origin.x + bounds.size.width, bounds.origin.y),
                        style: self.line_style.clone(),
                    });
                }
            },
            AxisPosition::Right => {
                // Y轴在右侧
                if self.show_line {
                    commands.push(DrawCommand::Line {
                        from: Point::new(bounds.origin.x + bounds.size.width, bounds.origin.y),
                        to: Point::new(bounds.origin.x + bounds.size.width, bounds.origin.y + bounds.size.height),
                        style: self.line_style.clone(),
                    });
                }
            },
        }

        Ok(commands)
    }

    fn clone_component(&self) -> Box<dyn ChartComponent> {
        Box::new(self.clone())
    }

    fn as_any(&self) -> &dyn Any {
        self
    }
}

impl Axis {
    /// 为底部X轴添加刻度和标签
    fn add_bottom_axis_ticks_and_labels(&self, commands: &mut Vec<DrawCommand>, bounds: Bounds) -> Result<()> {
        let tick_count = 6; // 默认6个刻度
        let y = bounds.origin.y + bounds.size.height;

        for i in 0..=tick_count {
            let t = i as f64 / tick_count as f64;
            let x = bounds.origin.x + bounds.size.width * t;

            // 刻度线
            if self.show_ticks {
                commands.push(DrawCommand::Line {
                    from: Point::new(x, y),
                    to: Point::new(x, y + 5.0),
                    style: self.tick_style.clone(),
                });
            }

            // 标签
            if self.show_labels {
                let label_value = if let (Some(min), Some(max)) = (self.min, self.max) {
                    min + (max - min) * t
                } else {
                    i as f64 * 100.0 // 默认值
                };

                commands.push(DrawCommand::Text {
                    text: format!("{:.0}", label_value),
                    position: Point::new(x, y + 20.0),
                    style: self.label_text_style.clone(),
                });
            }
        }

        Ok(())
    }

    /// 为左侧Y轴添加刻度和标签
    fn add_left_axis_ticks_and_labels(&self, commands: &mut Vec<DrawCommand>, bounds: Bounds) -> Result<()> {
        let tick_count = 5; // 默认5个刻度
        let x = bounds.origin.x;

        for i in 0..=tick_count {
            let t = i as f64 / tick_count as f64;
            let y = bounds.origin.y + bounds.size.height * (1.0 - t); // 从下到上

            // 刻度线
            if self.show_ticks {
                commands.push(DrawCommand::Line {
                    from: Point::new(x - 5.0, y),
                    to: Point::new(x, y),
                    style: self.tick_style.clone(),
                });
            }

            // 标签
            if self.show_labels {
                let label_value = if let (Some(min), Some(max)) = (self.min, self.max) {
                    min + (max - min) * t
                } else {
                    i as f64 * 20.0 // 默认值
                };

                commands.push(DrawCommand::Text {
                    text: format!("{:.0}", label_value),
                    position: Point::new(x - 30.0, y + 4.0),
                    style: self.label_text_style.clone(),
                });
            }
        }

        Ok(())
    }
}
