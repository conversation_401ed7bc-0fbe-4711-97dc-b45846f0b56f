use gpui::{AnyElement, App, IntoElement, SharedString, Window};

use gpui::RenderOnce;
use gpui_component::Icon;

#[derive(IntoElement, Clone)]
pub enum SvgName {
    Folder,
    Blocks,
    Search,
    Connect,
    Disconnect,
    Stop,
    Run,
    LoaderCircle,
    AlertTriangle,
}

impl SvgName {
    pub fn path(&self) -> SharedString {
        match self {
            SvgName::Folder => "icons/folder.svg",
            SvgName::Blocks => "icons/blocks.svg",
            SvgName::Search => "icons/search.svg",
            SvgName::Connect => "icons/link.svg", /* 连接 */
            SvgName::Disconnect => "icons/unplug.svg", /* 断开 */
            SvgName::Stop => "icons/circle-pause.svg",
            SvgName::Run => "icons/circle-play.svg",
            SvgName::LoaderCircle => "icons/loader-circle.svg",
            SvgName::AlertTriangle => "icons/triangle-alert.svg",
        }
        .into()
    }
    pub fn icon(self) -> Icon {
        Icon::default().path(self.path())
    }
}

impl From<SvgName> for AnyElement {
    fn from(val: SvgName) -> Self {
        val.icon().into_any_element()
    }
}

impl RenderOnce for SvgName {
    fn render(self, _: &mut Window, _cx: &mut App) -> impl IntoElement {
        gpui::svg().path(self.path())
    }
}
