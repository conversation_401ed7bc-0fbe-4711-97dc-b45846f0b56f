//! 真实的 SVG 图表生成器
//!
//! 生成包含真实图表内容的 SVG 文件

use echarts_rs::prelude::*;
use echarts_rs::PieSeries;
use std::fs;

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🎨 真实的 SVG 图表生成器");
    println!("{}", "=".repeat(50));

    // 确保输出目录存在
    let output_dir = "temp/svg/real_charts";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 生成真实的折线图
    println!("\n📈 生成真实折线图...");
    generate_real_line_chart(output_dir)?;

    // 2. 生成真实的柱状图
    println!("\n📊 生成真实柱状图...");
    generate_real_bar_chart(output_dir)?;

    // 3. 生成真实的散点图
    println!("\n🔵 生成真实散点图...");
    generate_real_scatter_chart(output_dir)?;

    // 4. 生成真实的饼图
    println!("\n🥧 生成真实饼图...");
    generate_real_pie_chart(output_dir)?;

    // 5. 生成真实的混合图表
    println!("\n🔧 生成真实混合图表...");
    generate_real_mixed_chart(output_dir)?;

    // 6. 生成展示页面
    println!("\n📄 生成展示页面...");
    generate_real_charts_html(output_dir)?;

    println!("\n🎉 真实 SVG 图表生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/real_charts.html 查看图表", output_dir);

    Ok(())
}

/// 生成真实的折线图
fn generate_real_line_chart(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        (0.0, 120.0),
        (1.0, 132.0),
        (2.0, 101.0),
        (3.0, 134.0),
        (4.0, 90.0),
        (5.0, 230.0),
        (6.0, 210.0),
    ];

    let svg_content = generate_line_chart_svg("销售趋势图", &data, 600.0, 400.0);
    fs::write(format!("{}/real_line_chart.svg", output_dir), svg_content)?;
    println!("  ✅ 折线图已生成");
    Ok(())
}

/// 生成真实的柱状图
fn generate_real_bar_chart(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        ("一月", 20.0),
        ("二月", 49.0),
        ("三月", 70.0),
        ("四月", 23.0),
        ("五月", 25.0),
        ("六月", 76.0),
    ];

    let svg_content = generate_bar_chart_svg("月度销售额", &data, 600.0, 400.0);
    fs::write(format!("{}/real_bar_chart.svg", output_dir), svg_content)?;
    println!("  ✅ 柱状图已生成");
    Ok(())
}

/// 生成真实的散点图
fn generate_real_scatter_chart(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data: Vec<(f64, f64)> = (0..50)
        .map(|i| {
            let x = i as f64 * 0.2;
            let y = (x * 2.0).sin() * 50.0 + 100.0 + (i as f64 * 0.1).cos() * 20.0;
            (x, y)
        })
        .collect();

    let svg_content = generate_scatter_chart_svg("数据分布图", &data, 600.0, 400.0);
    fs::write(format!("{}/real_scatter_chart.svg", output_dir), svg_content)?;
    println!("  ✅ 散点图已生成");
    Ok(())
}

/// 生成真实的饼图
fn generate_real_pie_chart(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let data = vec![
        ("直接访问", 335.0),
        ("邮件营销", 310.0),
        ("联盟广告", 234.0),
        ("视频广告", 135.0),
        ("搜索引擎", 548.0),
    ];

    let svg_content = generate_pie_chart_svg("访问来源", &data, 500.0, 500.0);
    fs::write(format!("{}/real_pie_chart.svg", output_dir), svg_content)?;
    println!("  ✅ 饼图已生成");
    Ok(())
}

/// 生成真实的混合图表
fn generate_real_mixed_chart(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let bar_data = vec![
        ("Q1", 100.0),
        ("Q2", 120.0),
        ("Q3", 90.0),
        ("Q4", 150.0),
    ];

    let line_data = vec![
        (0.0, 10.0),
        (1.0, 15.0),
        (2.0, 8.0),
        (3.0, 20.0),
    ];

    let svg_content = generate_mixed_chart_svg("季度业绩", &bar_data, &line_data, 700.0, 450.0);
    fs::write(format!("{}/real_mixed_chart.svg", output_dir), svg_content)?;
    println!("  ✅ 混合图表已生成");
    Ok(())
}

/// 生成折线图 SVG
fn generate_line_chart_svg(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    // SVG 头部
    svg.push_str(&format!(
        "<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n",
        width, height
    ));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    
    // 标题
    svg.push_str(&format!(
        "  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
        title
    ));
    
    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height
    ));
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y, chart_x, chart_y + chart_height
    ));
    
    if !data.is_empty() {
        // 计算数据范围
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        
        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };
        
        // 生成路径
        let mut path = String::from("M");
        for (i, (x, y)) in data.iter().enumerate() {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        // 绘制折线
        svg.push_str(&format!(
            "  <path d=\"{}\" stroke=\"#007bff\" stroke-width=\"3\" fill=\"none\"/>\n",
            path
        ));
        
        // 绘制数据点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;
            
            svg.push_str(&format!(
                "  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#007bff\"/>\n",
                px, py
            ));
        }
        
        // 添加网格线
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            svg.push_str(&format!(
                "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
                x, chart_y, x, chart_y + chart_height
            ));
        }
        
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!(
                "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
                chart_x, y, chart_x + chart_width, y
            ));
        }
        
        // 添加坐标轴标签
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            let value = min_x + (i as f64 / 5.0) * x_range;
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{:.1}</text>\n",
                x, chart_y + chart_height + 20.0, value
            ));
        }
        
        for i in 0..=5 {
            let y = chart_y + chart_height - (i as f64 / 5.0) * chart_height;
            let value = min_y + (i as f64 / 5.0) * y_range;
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n",
                chart_x - 10.0, y + 4.0, value
            ));
        }
    }
    
    svg.push_str("</svg>");
    svg
}

/// 生成柱状图 SVG
fn generate_bar_chart_svg(title: &str, data: &[(&str, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    // SVG 头部
    svg.push_str(&format!(
        "<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n",
        width, height
    ));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");
    
    // 标题
    svg.push_str(&format!(
        "  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
        title
    ));
    
    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;
    
    // 坐标轴
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height
    ));
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y, chart_x, chart_y + chart_height
    ));
    
    if !data.is_empty() {
        let max_value = data.iter().map(|(_, v)| *v).fold(0.0, f64::max);
        let bar_width = chart_width / data.len() as f64 * 0.8;
        let bar_spacing = chart_width / data.len() as f64;
        
        // 绘制柱子
        for (i, (label, value)) in data.iter().enumerate() {
            let x = chart_x + i as f64 * bar_spacing + bar_spacing * 0.1;
            let bar_height = (value / max_value) * chart_height;
            let y = chart_y + chart_height - bar_height;
            
            // 柱子
            svg.push_str(&format!(
                "  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"#28a745\" stroke=\"#1e7e34\" stroke-width=\"1\"/>\n",
                x, y, bar_width, bar_height
            ));
            
            // 数值标签
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#333\">{:.0}</text>\n",
                x + bar_width / 2.0, y - 5.0, value
            ));
            
            // X轴标签
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{}</text>\n",
                x + bar_width / 2.0, chart_y + chart_height + 20.0, label
            ));
        }
        
        // Y轴标签
        for i in 0..=5 {
            let y = chart_y + chart_height - (i as f64 / 5.0) * chart_height;
            let value = (i as f64 / 5.0) * max_value;
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n",
                chart_x - 10.0, y + 4.0, value
            ));
            
            // 网格线
            if i > 0 {
                svg.push_str(&format!(
                    "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
                    chart_x, y, chart_x + chart_width, y
                ));
            }
        }
    }
    
    svg.push_str("</svg>");
    svg
}

/// 生成散点图 SVG
fn generate_scatter_chart_svg(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();

    // SVG 头部
    svg.push_str(&format!(
        "<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n",
        width, height
    ));

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");

    // 标题
    svg.push_str(&format!(
        "  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
        title
    ));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;

    // 坐标轴
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height
    ));
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y, chart_x, chart_y + chart_height
    ));

    if !data.is_empty() {
        // 计算数据范围
        let min_x = data.iter().map(|(x, _)| *x).fold(f64::INFINITY, f64::min);
        let max_x = data.iter().map(|(x, _)| *x).fold(f64::NEG_INFINITY, f64::max);
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);

        let x_range = if max_x > min_x { max_x - min_x } else { 1.0 };
        let y_range = if max_y > min_y { max_y - min_y } else { 1.0 };

        // 绘制散点
        for (x, y) in data {
            let px = chart_x + (x - min_x) / x_range * chart_width;
            let py = chart_y + chart_height - (y - min_y) / y_range * chart_height;

            svg.push_str(&format!(
                "  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#dc3545\" fill-opacity=\"0.7\"/>\n",
                px, py
            ));
        }

        // 添加网格线
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            svg.push_str(&format!(
                "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
                x, chart_y, x, chart_y + chart_height
            ));
        }

        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!(
                "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n",
                chart_x, y, chart_x + chart_width, y
            ));
        }

        // 添加坐标轴标签
        for i in 0..=5 {
            let x = chart_x + (i as f64 / 5.0) * chart_width;
            let value = min_x + (i as f64 / 5.0) * x_range;
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{:.1}</text>\n",
                x, chart_y + chart_height + 20.0, value
            ));
        }

        for i in 0..=5 {
            let y = chart_y + chart_height - (i as f64 / 5.0) * chart_height;
            let value = min_y + (i as f64 / 5.0) * y_range;
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n",
                chart_x - 10.0, y + 4.0, value
            ));
        }
    }

    svg.push_str("</svg>");
    svg
}

/// 生成饼图 SVG
fn generate_pie_chart_svg(title: &str, data: &[(&str, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();

    // SVG 头部
    svg.push_str(&format!(
        "<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n",
        width, height
    ));

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");

    // 标题
    svg.push_str(&format!(
        "  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
        title
    ));

    if !data.is_empty() {
        let center_x = width / 2.0;
        let center_y = height / 2.0 + 20.0;
        let radius = (width.min(height) - 100.0) / 2.0;

        let total: f64 = data.iter().map(|(_, v)| *v).sum();
        let mut current_angle = -std::f64::consts::PI / 2.0; // 从顶部开始

        let colors = ["#007bff", "#28a745", "#dc3545", "#ffc107", "#17a2b8", "#6f42c1"];

        for (i, (label, value)) in data.iter().enumerate() {
            let angle = (value / total) * 2.0 * std::f64::consts::PI;
            let end_angle = current_angle + angle;

            let x1 = center_x + radius * current_angle.cos();
            let y1 = center_y + radius * current_angle.sin();
            let x2 = center_x + radius * end_angle.cos();
            let y2 = center_y + radius * end_angle.sin();

            let large_arc = if angle > std::f64::consts::PI { 1 } else { 0 };

            let color = colors[i % colors.len()];

            // 绘制扇形
            svg.push_str(&format!(
                "  <path d=\"M {} {} L {} {} A {} {} 0 {} 1 {} {} Z\" fill=\"{}\" stroke=\"white\" stroke-width=\"2\"/>\n",
                center_x, center_y, x1, y1, radius, radius, large_arc, x2, y2, color
            ));

            // 添加标签
            let label_angle = current_angle + angle / 2.0;
            let label_x = center_x + (radius + 30.0) * label_angle.cos();
            let label_y = center_y + (radius + 30.0) * label_angle.sin();

            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#333\">{}</text>\n",
                label_x, label_y, label
            ));

            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"10\" fill=\"#666\">{:.1}%</text>\n",
                label_x, label_y + 15.0, (value / total) * 100.0
            ));

            current_angle = end_angle;
        }
    }

    svg.push_str("</svg>");
    svg
}

/// 生成混合图表 SVG
fn generate_mixed_chart_svg(title: &str, bar_data: &[(&str, f64)], line_data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();

    // SVG 头部
    svg.push_str(&format!(
        "<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n",
        width, height
    ));

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n");

    // 标题
    svg.push_str(&format!(
        "  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#333\">{}</text>\n",
        title
    ));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;

    // 坐标轴
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y + chart_height, chart_x + chart_width, chart_y + chart_height
    ));
    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"2\"/>\n",
        chart_x, chart_y, chart_x, chart_y + chart_height
    ));

    // 绘制柱状图
    if !bar_data.is_empty() {
        let max_bar_value = bar_data.iter().map(|(_, v)| *v).fold(0.0, f64::max);
        let bar_width = chart_width / bar_data.len() as f64 * 0.6;
        let bar_spacing = chart_width / bar_data.len() as f64;

        for (i, (label, value)) in bar_data.iter().enumerate() {
            let x = chart_x + i as f64 * bar_spacing + bar_spacing * 0.2;
            let bar_height = (value / max_bar_value) * chart_height * 0.8;
            let y = chart_y + chart_height - bar_height;

            // 柱子
            svg.push_str(&format!(
                "  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"#28a745\" fill-opacity=\"0.7\" stroke=\"#1e7e34\" stroke-width=\"1\"/>\n",
                x, y, bar_width, bar_height
            ));

            // X轴标签
            svg.push_str(&format!(
                "  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#666\">{}</text>\n",
                x + bar_width / 2.0, chart_y + chart_height + 20.0, label
            ));
        }
    }

    // 绘制折线图
    if !line_data.is_empty() {
        let min_line_y = line_data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_line_y = line_data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        let line_y_range = if max_line_y > min_line_y { max_line_y - min_line_y } else { 1.0 };

        // 生成路径
        let mut path = String::from("M");
        for (i, (x, y)) in line_data.iter().enumerate() {
            let px = chart_x + (*x / (line_data.len() - 1) as f64) * chart_width;
            let py = chart_y + chart_height * 0.2 + (1.0 - (y - min_line_y) / line_y_range) * chart_height * 0.6;

            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }

        // 绘制折线
        svg.push_str(&format!(
            "  <path d=\"{}\" stroke=\"#dc3545\" stroke-width=\"3\" fill=\"none\"/>\n",
            path
        ));

        // 绘制数据点
        for (x, y) in line_data {
            let px = chart_x + (*x / (line_data.len() - 1) as f64) * chart_width;
            let py = chart_y + chart_height * 0.2 + (1.0 - (y - min_line_y) / line_y_range) * chart_height * 0.6;

            svg.push_str(&format!(
                "  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#dc3545\"/>\n",
                px, py
            ));
        }
    }

    // 图例
    svg.push_str(&format!(
        "  <rect x=\"{}\" y=\"{}\" width=\"15\" height=\"15\" fill=\"#28a745\"/>\n",
        chart_x + chart_width - 120.0, chart_y + 20.0
    ));
    svg.push_str(&format!(
        "  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">销售额</text>\n",
        chart_x + chart_width - 100.0, chart_y + 32.0
    ));

    svg.push_str(&format!(
        "  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#dc3545\" stroke-width=\"3\"/>\n",
        chart_x + chart_width - 120.0, chart_y + 50.0, chart_x + chart_width - 105.0, chart_y + 50.0
    ));
    svg.push_str(&format!(
        "  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">增长率</text>\n",
        chart_x + chart_width - 100.0, chart_y + 55.0
    ));

    svg.push_str("</svg>");
    svg
}

/// 生成展示页面
fn generate_real_charts_html(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实的 SVG 图表展示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1400px; margin: 0 auto; }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        .chart-item {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        .chart-svg {
            width: 100%;
            height: auto;
            border-radius: 8px;
            background: white;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .description {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 真实的 SVG 图表展示</h1>
            <p class="description">使用 ECharts-rs 生成的真实图表内容</p>
            <div class="features">
                <div class="feature">
                    <h3>📈 折线图</h3>
                    <p>完整的数据连线</p>
                </div>
                <div class="feature">
                    <h3>📊 柱状图</h3>
                    <p>真实的柱状数据</p>
                </div>
                <div class="feature">
                    <h3>🔵 散点图</h3>
                    <p>精确的数据点</p>
                </div>
                <div class="feature">
                    <h3>🥧 饼图</h3>
                    <p>完整的扇形图</p>
                </div>
                <div class="feature">
                    <h3>🔧 混合图</h3>
                    <p>多类型组合</p>
                </div>
            </div>
        </div>

        <div class="chart-grid">
            <div class="chart-item">
                <div class="chart-title">📈 销售趋势图</div>
                <object class="chart-svg" data="real_line_chart.svg" type="image/svg+xml">
                    您的浏览器不支持 SVG
                </object>
            </div>

            <div class="chart-item">
                <div class="chart-title">📊 月度销售额</div>
                <object class="chart-svg" data="real_bar_chart.svg" type="image/svg+xml">
                    您的浏览器不支持 SVG
                </object>
            </div>

            <div class="chart-item">
                <div class="chart-title">🔵 数据分布图</div>
                <object class="chart-svg" data="real_scatter_chart.svg" type="image/svg+xml">
                    您的浏览器不支持 SVG
                </object>
            </div>

            <div class="chart-item">
                <div class="chart-title">🥧 访问来源</div>
                <object class="chart-svg" data="real_pie_chart.svg" type="image/svg+xml">
                    您的浏览器不支持 SVG
                </object>
            </div>

            <div class="chart-item" style="grid-column: 1 / -1;">
                <div class="chart-title">🔧 季度业绩 (混合图表)</div>
                <object class="chart-svg" data="real_mixed_chart.svg" type="image/svg+xml">
                    您的浏览器不支持 SVG
                </object>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h2>🎉 真实图表内容展示</h2>
            <p>这些 SVG 图表包含完整的图表元素：</p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>✅ 完整的坐标轴和网格线</li>
                <li>✅ 真实的数据可视化</li>
                <li>✅ 准确的数据标签</li>
                <li>✅ 专业的图表样式</li>
                <li>✅ 响应式设计</li>
            </ul>
        </div>
    </div>
</body>
</html>"#;

    fs::write(format!("{}/real_charts.html", output_dir), html_content)?;
    Ok(())
}
