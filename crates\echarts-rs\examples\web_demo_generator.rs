//! Web演示生成器
//!
//! 生成完整的HTML页面，展示ECharts-rs在Web平台上的渲染能力

use std::fs;
use echarts_rs::{LineSeries, BarSeries, PieSeries, ScatterSeries, Color};
use echarts_core::{Chart, Bounds, DrawCommand, Point};
// use echarts_renderer::HtmlCanvasRenderer;

mod web_demo_utils;
use web_demo_utils::{generate_chart_html, generate_comprehensive_demo, generate_index_page};

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("🌐 Web演示生成器");
    println!("{}", "=".repeat(60));

    // 确保输出目录存在
    let output_dir = "temp/web_demo";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 生成线图演示
    println!("\n📈 1. 生成线图演示...");
    generate_line_chart_demo(output_dir)?;

    // 2. 生成柱图演示
    println!("\n📊 2. 生成柱图演示...");
    generate_bar_chart_demo(output_dir)?;

    // 3. 生成饼图演示
    println!("\n🥧 3. 生成饼图演示...");
    generate_pie_chart_demo(output_dir)?;

    // 4. 生成散点图演示
    println!("\n🔵 4. 生成散点图演示...");
    generate_scatter_chart_demo(output_dir)?;

    // 5. 生成综合演示页面
    println!("\n🎨 5. 生成综合演示页面...");
    generate_comprehensive_demo(output_dir)?;

    // 6. 生成主页面
    println!("\n🏠 6. 生成主页面...");
    generate_index_page(output_dir)?;

    println!("\n🎉 Web演示生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/index.html 查看演示", output_dir);

    Ok(())
}

/// 生成线图演示
fn generate_line_chart_demo(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建线图数据
    let line_series = LineSeries::new("销售趋势")
        .data(vec![
            (0.0, 120.0),
            (1.0, 132.0),
            (2.0, 101.0),
            (3.0, 134.0),
            (4.0, 90.0),
            (5.0, 230.0),
            (6.0, 210.0),
        ])
        .smooth(true)
        .color(Color::rgb(0.2, 0.6, 1.0));

    // 创建图表
    let mut chart = Chart::new();
    chart.add_series(Box::new(line_series));

    // 生成渲染命令
    let bounds = Bounds::new(50.0, 50.0, 700.0, 400.0);
    let commands = generate_mock_line_commands(bounds);

    // 创建HTML页面
    let html_content = generate_chart_html(
        "线图演示",
        "ECharts-rs 线图渲染演示",
        &commands,
        bounds,
        "这是一个使用ECharts-rs生成的线图演示，展示了销售趋势数据的可视化。"
    );

    fs::write(format!("{}/line_chart.html", output_dir), html_content)?;
    println!("  ✅ 线图演示页面已生成");
    Ok(())
}

/// 生成柱图演示
fn generate_bar_chart_demo(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建柱图数据 (使用数值索引)
    let bar_series = BarSeries::new("产品销量")
        .data(vec![
            (0.0, 320.0),
            (1.0, 280.0),
            (2.0, 250.0),
            (3.0, 200.0),
            (4.0, 180.0),
        ])
        .color(Color::rgb(0.9, 0.4, 0.2));

    // 创建图表
    let mut chart = Chart::new();
    chart.add_series(Box::new(bar_series));

    // 生成渲染命令
    let bounds = Bounds::new(50.0, 50.0, 700.0, 400.0);
    let commands = generate_mock_bar_commands(bounds);

    // 创建HTML页面
    let html_content = generate_chart_html(
        "柱图演示",
        "ECharts-rs 柱图渲染演示",
        &commands,
        bounds,
        "这是一个使用ECharts-rs生成的柱图演示，展示了产品销量数据的对比分析。"
    );

    fs::write(format!("{}/bar_chart.html", output_dir), html_content)?;
    println!("  ✅ 柱图演示页面已生成");
    Ok(())
}

/// 生成饼图演示
fn generate_pie_chart_demo(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建饼图数据
    let pie_series = PieSeries::new("市场份额")
        .data(vec![
            ("移动端", 60.0),
            ("桌面端", 30.0),
            ("平板端", 10.0),
        ])
        .radius(0.7);

    // 创建图表
    let mut chart = Chart::new();
    chart.add_series(Box::new(pie_series));

    // 生成渲染命令
    let bounds = Bounds::new(50.0, 50.0, 700.0, 400.0);
    let commands = generate_mock_pie_commands(bounds);

    // 创建HTML页面
    let html_content = generate_chart_html(
        "饼图演示",
        "ECharts-rs 饼图渲染演示",
        &commands,
        bounds,
        "这是一个使用ECharts-rs生成的饼图演示，展示了市场份额的分布情况。"
    );

    fs::write(format!("{}/pie_chart.html", output_dir), html_content)?;
    println!("  ✅ 饼图演示页面已生成");
    Ok(())
}

/// 生成散点图演示
fn generate_scatter_chart_demo(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 创建散点图数据
    let scatter_series = ScatterSeries::new("数据分布")
        .data(vec![
            (10.0, 20.0),
            (15.0, 35.0),
            (20.0, 25.0),
            (25.0, 45.0),
            (30.0, 40.0),
            (35.0, 55.0),
            (40.0, 50.0),
        ])
        .symbol_size(8.0)
        .color(Color::rgb(0.4, 0.8, 0.4));

    // 创建图表
    let mut chart = Chart::new();
    chart.add_series(Box::new(scatter_series));

    // 生成渲染命令
    let bounds = Bounds::new(50.0, 50.0, 700.0, 400.0);
    let commands = generate_mock_scatter_commands(bounds);

    // 创建HTML页面
    let html_content = generate_chart_html(
        "散点图演示",
        "ECharts-rs 散点图渲染演示",
        &commands,
        bounds,
        "这是一个使用ECharts-rs生成的散点图演示，展示了数据点的分布关系。"
    );

    fs::write(format!("{}/scatter_chart.html", output_dir), html_content)?;
    println!("  ✅ 散点图演示页面已生成");
    Ok(())
}

/// 生成模拟线图命令
fn generate_mock_line_commands(bounds: Bounds) -> Vec<DrawCommand> {
    let mut commands = Vec::new();
    
    // 绘制坐标轴
    commands.push(DrawCommand::Line {
        from: Point::new(bounds.origin.x, bounds.origin.y + bounds.size.height),
        to: Point::new(bounds.origin.x + bounds.size.width, bounds.origin.y + bounds.size.height),
        style: echarts_core::draw_commands::LineStyle::default(),
    });
    
    commands.push(DrawCommand::Line {
        from: Point::new(bounds.origin.x, bounds.origin.y),
        to: Point::new(bounds.origin.x, bounds.origin.y + bounds.size.height),
        style: echarts_core::draw_commands::LineStyle::default(),
    });
    
    // 绘制数据线
    let data_points = vec![
        (0.0, 120.0), (1.0, 132.0), (2.0, 101.0), (3.0, 134.0), 
        (4.0, 90.0), (5.0, 230.0), (6.0, 210.0)
    ];
    
    for i in 0..data_points.len() - 1 {
        let x1 = bounds.origin.x + (data_points[i].0 / 6.0) * bounds.size.width;
        let y1 = bounds.origin.y + bounds.size.height - (data_points[i].1 / 250.0) * bounds.size.height;
        let x2 = bounds.origin.x + (data_points[i + 1].0 / 6.0) * bounds.size.width;
        let y2 = bounds.origin.y + bounds.size.height - (data_points[i + 1].1 / 250.0) * bounds.size.height;
        
        commands.push(DrawCommand::Line {
            from: Point::new(x1, y1),
            to: Point::new(x2, y2),
            style: echarts_core::draw_commands::LineStyle {
                color: Color::rgb(0.2, 0.6, 1.0),
                width: 2.0,
                ..Default::default()
            },
        });
    }
    
    commands
}

/// 生成模拟柱图命令
fn generate_mock_bar_commands(bounds: Bounds) -> Vec<DrawCommand> {
    let mut commands = Vec::new();
    
    // 绘制坐标轴
    commands.push(DrawCommand::Line {
        from: Point::new(bounds.origin.x, bounds.origin.y + bounds.size.height),
        to: Point::new(bounds.origin.x + bounds.size.width, bounds.origin.y + bounds.size.height),
        style: echarts_core::draw_commands::LineStyle::default(),
    });
    
    commands.push(DrawCommand::Line {
        from: Point::new(bounds.origin.x, bounds.origin.y),
        to: Point::new(bounds.origin.x, bounds.origin.y + bounds.size.height),
        style: echarts_core::draw_commands::LineStyle::default(),
    });
    
    // 绘制柱子
    let data = vec![320.0, 280.0, 250.0, 200.0, 180.0];
    let bar_width = bounds.size.width / (data.len() as f64 * 1.5);
    
    for (i, &value) in data.iter().enumerate() {
        let x = bounds.origin.x + (i as f64 + 0.5) * bar_width * 1.2;
        let height = (value / 350.0) * bounds.size.height;
        let y = bounds.origin.y + bounds.size.height - height;
        
        commands.push(DrawCommand::Rect {
            bounds: Bounds::new(x, y, bar_width, height),
            style: echarts_core::draw_commands::RectStyle {
                fill: Some(Color::rgb(0.9, 0.4, 0.2)),
                stroke: Some(echarts_core::draw_commands::LineStyle {
                    color: Color::rgb(0.7, 0.2, 0.1),
                    width: 1.0,
                    dash_pattern: None,
                    cap: echarts_core::draw_commands::LineCap::Butt,
                    join: echarts_core::draw_commands::LineJoin::Miter,
                    opacity: 1.0,
                }),
                opacity: 1.0,
                corner_radius: 0.0,
            },
        });
    }
    
    commands
}

/// 生成模拟饼图命令
fn generate_mock_pie_commands(bounds: Bounds) -> Vec<DrawCommand> {
    let mut commands = Vec::new();

    let center = Point::new(
        bounds.origin.x + bounds.size.width / 2.0,
        bounds.origin.y + bounds.size.height / 2.0
    );
    let radius = bounds.size.width.min(bounds.size.height) / 3.0;

    // 绘制饼图扇形
    let data = vec![60.0, 30.0, 10.0];
    let colors = vec![
        Color::rgb(0.2, 0.6, 1.0),
        Color::rgb(0.9, 0.4, 0.2),
        Color::rgb(0.4, 0.8, 0.4),
    ];

    let mut start_angle: f64 = 0.0;
    for (i, &value) in data.iter().enumerate() {
        let angle = (value / 100.0) * 2.0 * std::f64::consts::PI;

        // 使用Path命令绘制扇形 (简化为直线路径)
        let path_commands = vec![
            echarts_core::draw_commands::PathCommand::MoveTo(center),
            echarts_core::draw_commands::PathCommand::LineTo(Point::new(
                center.x + radius * start_angle.cos(),
                center.y + radius * start_angle.sin()
            )),
            echarts_core::draw_commands::PathCommand::LineTo(Point::new(
                center.x + radius * (start_angle + angle).cos(),
                center.y + radius * (start_angle + angle).sin()
            )),
            echarts_core::draw_commands::PathCommand::Close,
        ];

        commands.push(DrawCommand::Path {
            commands: path_commands,
            style: echarts_core::draw_commands::PathStyle {
                fill: Some(colors[i]),
                stroke: Some(echarts_core::draw_commands::LineStyle {
                    color: Color::rgb(1.0, 1.0, 1.0),
                    width: 2.0,
                    dash_pattern: None,
                    cap: echarts_core::draw_commands::LineCap::Butt,
                    join: echarts_core::draw_commands::LineJoin::Miter,
                    opacity: 1.0,
                }),
                opacity: 1.0,
                fill_rule: echarts_core::draw_commands::FillRule::NonZero,
            },
        });

        start_angle += angle;
    }

    commands
}

/// 生成模拟散点图命令
fn generate_mock_scatter_commands(bounds: Bounds) -> Vec<DrawCommand> {
    let mut commands = Vec::new();

    // 绘制坐标轴
    commands.push(DrawCommand::Line {
        from: Point::new(bounds.origin.x, bounds.origin.y + bounds.size.height),
        to: Point::new(bounds.origin.x + bounds.size.width, bounds.origin.y + bounds.size.height),
        style: echarts_core::draw_commands::LineStyle::default(),
    });

    commands.push(DrawCommand::Line {
        from: Point::new(bounds.origin.x, bounds.origin.y),
        to: Point::new(bounds.origin.x, bounds.origin.y + bounds.size.height),
        style: echarts_core::draw_commands::LineStyle::default(),
    });

    // 绘制散点
    let data_points = vec![
        (10.0, 20.0), (15.0, 35.0), (20.0, 25.0), (25.0, 45.0),
        (30.0, 40.0), (35.0, 55.0), (40.0, 50.0)
    ];

    for &(x, y) in &data_points {
        let px = bounds.origin.x + (x / 50.0) * bounds.size.width;
        let py = bounds.origin.y + bounds.size.height - (y / 60.0) * bounds.size.height;

        commands.push(DrawCommand::Circle {
            center: Point::new(px, py),
            radius: 4.0,
            style: echarts_core::draw_commands::CircleStyle {
                fill: Some(Color::rgb(0.4, 0.8, 0.4)),
                stroke: Some(echarts_core::draw_commands::LineStyle {
                    color: Color::rgb(0.2, 0.6, 0.2),
                    width: 1.0,
                    dash_pattern: None,
                    cap: echarts_core::draw_commands::LineCap::Butt,
                    join: echarts_core::draw_commands::LineJoin::Miter,
                    opacity: 1.0,
                }),
                opacity: 1.0,
            },
        });
    }

    commands
}
