{"$message_type":"diagnostic","message":"unused imports: `Bounds`, `CircleStyle`, `LineStyle`, `PathStyle`, `Point`, `RectStyle`, and `TextStyle`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":121,"byte_end":127,"line_start":6,"line_end":6,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    Bounds, Color, DrawCommand, Point, Result,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":149,"byte_end":154,"line_start":6,"line_end":6,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"    Bounds, Color, DrawCommand, Point, Result,","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":193,"byte_end":202,"line_start":8,"line_end":8,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        LineStyle, CircleStyle, RectStyle, TextStyle, PathCommand, PathStyle,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":204,"byte_end":215,"line_start":8,"line_end":8,"column_start":20,"column_end":31,"is_primary":true,"text":[{"text":"        LineStyle, CircleStyle, RectStyle, TextStyle, PathCommand, PathStyle,","highlight_start":20,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":217,"byte_end":226,"line_start":8,"line_end":8,"column_start":33,"column_end":42,"is_primary":true,"text":[{"text":"        LineStyle, CircleStyle, RectStyle, TextStyle, PathCommand, PathStyle,","highlight_start":33,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":228,"byte_end":237,"line_start":8,"line_end":8,"column_start":44,"column_end":53,"is_primary":true,"text":[{"text":"        LineStyle, CircleStyle, RectStyle, TextStyle, PathCommand, PathStyle,","highlight_start":44,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":252,"byte_end":261,"line_start":8,"line_end":8,"column_start":68,"column_end":77,"is_primary":true,"text":[{"text":"        LineStyle, CircleStyle, RectStyle, TextStyle, PathCommand, PathStyle,","highlight_start":68,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":121,"byte_end":129,"line_start":6,"line_end":6,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    Bounds, Color, DrawCommand, Point, Result,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":147,"byte_end":154,"line_start":6,"line_end":6,"column_start":31,"column_end":38,"is_primary":true,"text":[{"text":"    Bounds, Color, DrawCommand, Point, Result,","highlight_start":31,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":193,"byte_end":239,"line_start":8,"line_end":8,"column_start":9,"column_end":55,"is_primary":true,"text":[{"text":"        LineStyle, CircleStyle, RectStyle, TextStyle, PathCommand, PathStyle,","highlight_start":9,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":250,"byte_end":261,"line_start":8,"line_end":8,"column_start":66,"column_end":77,"is_primary":true,"text":[{"text":"        LineStyle, CircleStyle, RectStyle, TextStyle, PathCommand, PathStyle,","highlight_start":66,"highlight_end":77}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Bounds`, `CircleStyle`, `LineStyle`, `PathStyle`, `Point`, `RectStyle`, and `TextStyle`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Bounds, Color, DrawCommand, Point, Result,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    draw_commands::{\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        LineStyle, CircleStyle, RectStyle, TextStyle, PathCommand, PathStyle,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"1 warning emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 1 warning emitted\u001b[0m\n\n"}
