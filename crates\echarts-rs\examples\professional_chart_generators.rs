//! 专业图表生成器
//!
//! 包含各种专业级图表的生成函数

use std::collections::HashMap;

/// 创建蜡烛图
pub fn create_candlestick_chart(title: &str, data: &[super::CandlestickPoint], width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 定义渐变和滤镜
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"bullishGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#4caf50;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#2e7d32;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("    <linearGradient id=\"bearishGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#f44336;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#c62828;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("    <filter id=\"shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n");
    svg.push_str("      <feDropShadow dx=\"1\" dy=\"1\" stdDeviation=\"1\" flood-color=\"#000000\" flood-opacity=\"0.3\"/>\n");
    svg.push_str("    </filter>\n");
    svg.push_str("  </defs>\n");
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 200.0; // 为成交量留出空间
    let volume_height = 120.0;
    
    if !data.is_empty() {
        // 计算价格范围
        let min_price = data.iter().map(|p| p.low).fold(f64::INFINITY, f64::min);
        let max_price = data.iter().map(|p| p.high).fold(f64::NEG_INFINITY, f64::max);
        let price_range = max_price - min_price;
        
        // 计算成交量范围
        let max_volume = data.iter().map(|p| p.volume).fold(f64::NEG_INFINITY, f64::max);
        
        // 绘制价格区域背景
        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, chart_height));
        
        // 绘制成交量区域背景
        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"#f5f5f5\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y + chart_height + 20.0, chart_width, volume_height));
        
        // 绘制网格线
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));
            
            // 价格标签
            let price = max_price - (i as f64 / 5.0) * price_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.2}</text>\n", chart_x - 10.0, y + 4.0, price));
        }
        
        // 绘制蜡烛
        let candle_width = (chart_width / data.len() as f64) * 0.6;
        
        for (i, point) in data.iter().enumerate() {
            let x = chart_x + (i as f64 + 0.5) * (chart_width / data.len() as f64);
            
            // 价格坐标
            let open_y = chart_y + chart_height - ((point.open - min_price) / price_range) * chart_height;
            let close_y = chart_y + chart_height - ((point.close - min_price) / price_range) * chart_height;
            let high_y = chart_y + chart_height - ((point.high - min_price) / price_range) * chart_height;
            let low_y = chart_y + chart_height - ((point.low - min_price) / price_range) * chart_height;
            
            // 判断涨跌
            let is_bullish = point.close >= point.open;
            let fill_color = if is_bullish { "url(#bullishGradient)" } else { "url(#bearishGradient)" };
            
            // 绘制影线
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"1\"/>\n", x, high_y, x, low_y));
            
            // 绘制实体
            let body_top = open_y.min(close_y);
            let body_height = (open_y - close_y).abs().max(1.0);
            
            svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"{}\" stroke=\"#333\" stroke-width=\"1\" filter=\"url(#shadow)\"/>\n", 
                x - candle_width / 2.0, body_top, candle_width, body_height, fill_color));
            
            // 绘制成交量柱
            let volume_bar_height = (point.volume / max_volume) * volume_height;
            let volume_y = chart_y + chart_height + 20.0 + volume_height - volume_bar_height;
            let volume_color = if is_bullish { "#4caf50" } else { "#f44336" };
            
            svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"{}\" opacity=\"0.7\"/>\n", 
                x - candle_width / 2.0, volume_y, candle_width, volume_bar_height, volume_color));
        }
        
        // 添加标签
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"14\" font-weight=\"bold\" fill=\"#333\">价格</text>\n", chart_x, chart_y - 10.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"14\" font-weight=\"bold\" fill=\"#333\">成交量</text>\n", chart_x, chart_y + chart_height + 40.0));
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建股票成交量图表
pub fn create_stock_volume_chart(title: &str, data: &StockData, width: f64, height: f64) -> String {
    create_candlestick_chart(title, &data.data, width, height)
}

/// 创建多股票对比图表
pub fn create_multi_stock_chart(title: &str, data: &HashMap<String, Vec<(f64, f64)>>, width: f64, height: f64) -> String {
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 定义颜色
    let colors = vec!["2196f3", "4caf50", "ff9800", "f44336", "9c27b0"];
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 250.0; // 为图例留出空间
    let chart_height = height - 120.0;
    
    // 绘制背景
    svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, chart_height));
    
    if !data.is_empty() {
        // 计算全局范围
        let mut all_values = Vec::new();
        for series in data.values() {
            all_values.extend(series.iter().map(|(_, y)| *y));
        }
        
        let min_y = all_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_y = all_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let y_range = max_y - min_y;
        
        let max_x = data.values().map(|s| s.len()).max().unwrap_or(0) as f64;
        
        // 绘制网格线
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));
            
            let value = max_y - (i as f64 / 5.0) * y_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n", chart_x - 10.0, y + 4.0, value));
        }
        
        // 绘制每个股票的曲线
        for (idx, (symbol, series)) in data.iter().enumerate() {
            let color = &colors[idx % colors.len()];
            
            if !series.is_empty() {
                let mut path = String::from("M");
                
                for (i, (_, y)) in series.iter().enumerate() {
                    let px = chart_x + (i as f64 / max_x) * chart_width;
                    let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;
                    
                    if i == 0 {
                        path.push_str(&format!(" {} {}", px, py));
                    } else {
                        path.push_str(&format!(" L {} {}", px, py));
                    }
                }
                
                svg.push_str("  <path d=\"");
                svg.push_str(&path);
                svg.push_str("\" stroke=\"#");
                svg.push_str(color);
                svg.push_str("\" stroke-width=\"3\" fill=\"none\"/>\n");
                
                // 图例
                let legend_y = chart_y + 30.0 + idx as f64 * 25.0;
                svg.push_str("  <line x1=\"");
                svg.push_str(&(chart_x + chart_width + 20.0).to_string());
                svg.push_str("\" y1=\"");
                svg.push_str(&legend_y.to_string());
                svg.push_str("\" x2=\"");
                svg.push_str(&(chart_x + chart_width + 40.0).to_string());
                svg.push_str("\" y2=\"");
                svg.push_str(&legend_y.to_string());
                svg.push_str("\" stroke=\"#");
                svg.push_str(color);
                svg.push_str("\" stroke-width=\"3\"/>\n");
                svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"14\" fill=\"#333\">{}</text>\n", chart_x + chart_width + 45.0, legend_y + 4.0, symbol));
            }
        }
    }
    
    svg.push_str("</svg>");
    svg
}

/// 创建财务指标图表
pub fn create_financial_metrics_chart(title: &str, data: &HashMap<String, Vec<(f64, f64)>>, width: f64, height: f64) -> String {
    create_multi_stock_chart(title, data, width, height)
}

/// 创建移动平均线图表
pub fn create_moving_average_chart(title: &str, data: &(Vec<(f64, f64)>, Vec<super::TechnicalIndicator>), width: f64, height: f64) -> String {
    let (price_data, indicators) = data;
    let mut svg = String::new();
    
    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));
    
    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");
    
    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));
    
    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 200.0;
    let chart_height = height - 120.0;
    
    // 绘制背景
    svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, chart_height));
    
    if !price_data.is_empty() {
        // 计算价格范围
        let mut all_values: Vec<f64> = price_data.iter().map(|(_, y)| *y).collect();
        for indicator in indicators {
            all_values.extend(indicator.values.iter().map(|(_, y)| *y));
        }
        
        let min_y = all_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_y = all_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let y_range = max_y - min_y;
        let max_x = price_data.len() as f64;
        
        // 绘制网格线
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));
            
            let value = max_y - (i as f64 / 5.0) * y_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.2}</text>\n", chart_x - 10.0, y + 4.0, value));
        }
        
        // 绘制价格曲线
        let mut price_path = String::from("M");
        for (i, (_, y)) in price_data.iter().enumerate() {
            let px = chart_x + (i as f64 / max_x) * chart_width;
            let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;
            
            if i == 0 {
                price_path.push_str(&format!(" {} {}", px, py));
            } else {
                price_path.push_str(&format!(" L {} {}", px, py));
            }
        }
        
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#333\" stroke-width=\"2\" fill=\"none\"/>\n", price_path));
        
        // 绘制移动平均线
        for (idx, indicator) in indicators.iter().enumerate() {
            if !indicator.values.is_empty() {
                let mut ma_path = String::from("M");
                
                for (i, (_, y)) in indicator.values.iter().enumerate() {
                    let px = chart_x + (i as f64 / max_x) * chart_width;
                    let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;
                    
                    if i == 0 {
                        ma_path.push_str(&format!(" {} {}", px, py));
                    } else {
                        ma_path.push_str(&format!(" L {} {}", px, py));
                    }
                }
                
                let stroke_dasharray = match indicator.line_type {
                    super::LineType::Dashed => "stroke-dasharray=\"5,5\"",
                    super::LineType::Dotted => "stroke-dasharray=\"2,2\"",
                    super::LineType::DashDot => "stroke-dasharray=\"8,3,2,3\"",
                    _ => "",
                };
                
                svg.push_str("  <path d=\"");
                svg.push_str(&ma_path);
                svg.push_str("\" stroke=\"#");
                svg.push_str(&indicator.color);
                svg.push_str("\" stroke-width=\"2\" fill=\"none\" ");
                svg.push_str(stroke_dasharray);
                svg.push_str("/>\n");
                
                // 图例
                let legend_y = chart_y + 30.0 + idx as f64 * 25.0;
                svg.push_str("  <line x1=\"");
                svg.push_str(&(chart_x + chart_width + 20.0).to_string());
                svg.push_str("\" y1=\"");
                svg.push_str(&legend_y.to_string());
                svg.push_str("\" x2=\"");
                svg.push_str(&(chart_x + chart_width + 40.0).to_string());
                svg.push_str("\" y2=\"");
                svg.push_str(&legend_y.to_string());
                svg.push_str("\" stroke=\"#");
                svg.push_str(&indicator.color);
                svg.push_str("\" stroke-width=\"2\" ");
                svg.push_str(stroke_dasharray);
                svg.push_str("/>\n");
                svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">{}</text>\n", chart_x + chart_width + 45.0, legend_y + 4.0, indicator.name));
            }
        }
        
        // 价格图例
        svg.push_str("  <line x1=\"");
        svg.push_str(&(chart_x + chart_width + 20.0).to_string());
        svg.push_str("\" y1=\"");
        svg.push_str(&(chart_y + 5.0).to_string());
        svg.push_str("\" x2=\"");
        svg.push_str(&(chart_x + chart_width + 40.0).to_string());
        svg.push_str("\" y2=\"");
        svg.push_str(&(chart_y + 5.0).to_string());
        svg.push_str("\" stroke=\"#333\" stroke-width=\"2\"/>\n");
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">价格</text>\n", chart_x + chart_width + 45.0, chart_y + 9.0));
    }
    
    svg.push_str("</svg>");
    svg
}
