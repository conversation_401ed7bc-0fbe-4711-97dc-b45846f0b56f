use std::io;
use thiserror::Error;

/// 协议错误枚举
///
/// 定义了TSDAQ协议中可能出现的各种错误类型，
/// 包括设备通信、数据解析、配置等错误。
#[derive(Debug, Error)]
pub enum ProtocolError {
    /// 设备相关错误
    #[error("设备错误: {0}")]
    DeviceError(String),

    /// IO操作错误
    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),

    /// 串口通信错误
    #[error("串口错误: {0}")]
    Serial(#[from] serialport::Error),

    /// 数据序列化/反序列化错误
    #[error("反序列化错误: {0}")]
    Serde(#[from] serde_json::Error),

    /// 数据解码错误
    #[error("解码错误: {0}")]
    DecodeError(String),

    /// 校验和错误
    #[error("校验和错误")]
    ChecksumError,

    /// 校验和不匹配
    #[error("校验和不匹配")]
    ChecksumMismatch,

    /// 命令格式错误
    #[error("命令格式错误")]
    CommandFormatError,

    /// 帧解析错误
    #[error("帧错误: {0}")]
    Frame(String),

    /// 帧格式错误
    #[error("帧格式错误: {0}")]
    FrameFormatError(String),

    /// 帧头错误
    #[error("帧头错误")]
    FrameHeaderError,

    /// 数据溢出错误
    #[error("数据溢出: {0}")]
    DataOverflow(String),

    /// 数据不足错误
    #[error("数据不足")]
    NotEnoughData,

    /// 操作超时错误
    #[error("设备超时")]
    Timeout,

    /// 操作取消错误
    #[error("操作已取消")]
    Cancelled,

    /// 配置错误
    #[error("配置错误: {0}")]
    Config(String),
}

impl From<io::ErrorKind> for ProtocolError {
    fn from(kind: io::ErrorKind) -> Self {
        ProtocolError::Io(io::Error::from(kind))
    }
}

impl ProtocolError {
    /// 检查是否为可恢复的错误
    pub fn is_recoverable(&self) -> bool {
        matches!(
            self,
            ProtocolError::Timeout | ProtocolError::Io(_) | ProtocolError::Serial(_)
        )
    }

    /// 检查是否为致命错误
    pub fn is_fatal(&self) -> bool {
        matches!(
            self,
            ProtocolError::DeviceError(_)
                | ProtocolError::FrameFormatError(_)
                | ProtocolError::Config(_)
        )
    }

    /// 获取错误的严重程度
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            ProtocolError::Timeout | ProtocolError::Cancelled => ErrorSeverity::Warning,
            ProtocolError::Io(_) | ProtocolError::Serial(_) => ErrorSeverity::Error,
            ProtocolError::DeviceError(_)
            | ProtocolError::FrameFormatError(_)
            | ProtocolError::Config(_) => ErrorSeverity::Fatal,
            _ => ErrorSeverity::Error,
        }
    }

    /// 获取错误的用户友好描述
    pub fn user_message(&self) -> &'static str {
        match self {
            ProtocolError::DeviceError(_) => "设备通信失败，请检查设备连接",
            ProtocolError::Io(_) => "网络或文件IO错误",
            ProtocolError::Serial(_) => "串口通信错误，请检查串口设置",
            ProtocolError::Timeout => "操作超时，请重试",
            ProtocolError::Cancelled => "操作已取消",
            ProtocolError::DataOverflow(_) => "数据缓冲区溢出",
            ProtocolError::Config(_) => "配置参数错误",
            ProtocolError::Frame(_) => "数据帧格式错误",
            _ => "未知错误",
        }
    }
}

/// 错误严重程度
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ErrorSeverity {
    /// 警告 - 可以继续操作
    Warning,
    /// 错误 - 需要处理但可能可恢复
    Error,
    /// 致命错误 - 需要停止操作
    Fatal,
}

impl ErrorSeverity {
    /// 检查是否为严重错误
    pub fn is_serious(&self) -> bool {
        matches!(self, ErrorSeverity::Error | ErrorSeverity::Fatal)
    }

    /// 获取严重程度的描述
    pub fn description(&self) -> &'static str {
        match self {
            ErrorSeverity::Warning => "警告",
            ErrorSeverity::Error => "错误",
            ErrorSeverity::Fatal => "致命错误",
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_severity() {
        let timeout = ProtocolError::Timeout;
        assert!(timeout.is_recoverable());
        assert_eq!(timeout.severity(), ErrorSeverity::Warning);

        let device_error = ProtocolError::DeviceError("test".to_string());
        assert!(device_error.is_fatal());
        assert_eq!(device_error.severity(), ErrorSeverity::Fatal);
    }

    #[test]
    fn test_user_messages() {
        let timeout = ProtocolError::Timeout;
        assert_eq!(timeout.user_message(), "操作超时，请重试");

        let device_error = ProtocolError::DeviceError("test".to_string());
        assert_eq!(device_error.user_message(), "设备通信失败，请检查设备连接");
    }
}
