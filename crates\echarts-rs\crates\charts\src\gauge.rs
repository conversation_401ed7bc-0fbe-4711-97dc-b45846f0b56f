//! 仪表盘图实现
//!
//! 提供仪表盘样式的数据可视化功能

use echarts_core::{
    Color, DrawCommand, PathCommand, Point, Series, CoordinateSystem, SeriesType, DataSet, Bounds,
    draw_commands::{PathStyle, CircleStyle, FillRule},
    TextStyle, LineStyle, FontWeight, FontStyle, TextAlign, TextBaseline, LineCap, LineJoin,
    Result
};
use crate::base::{ChartBase, ChartSeries, ChartConfig, BoundsCalculator};
use std::f64::consts::PI;

/// 仪表盘数据项
#[derive(Debug, Clone)]
pub struct GaugeDataItem {
    /// 数据名称
    pub name: String,
    /// 数值
    pub value: f64,
    /// 颜色
    pub color: Option<Color>,
}

impl GaugeDataItem {
    /// 创建新的仪表盘数据项
    pub fn new(name: impl Into<String>, value: f64) -> Self {
        Self {
            name: name.into(),
            value,
            color: None,
        }
    }

    /// 设置颜色
    pub fn color(mut self, color: Color) -> Self {
        self.color = Some(color);
        self
    }
}

/// 仪表盘指针样式
#[derive(Debug, Clone)]
pub struct PointerStyle {
    /// 指针长度（相对于半径的比例）
    pub length: f64,
    /// 指针宽度
    pub width: f64,
    /// 指针颜色
    pub color: Color,
}

impl Default for PointerStyle {
    fn default() -> Self {
        Self {
            length: 0.8,
            width: 6.0,
            color: Color::rgb(0.2, 0.2, 0.2),
        }
    }
}

/// 仪表盘刻度配置
#[derive(Debug, Clone)]
pub struct AxisTick {
    /// 是否显示刻度
    pub show: bool,
    /// 刻度长度
    pub length: f64,
    /// 刻度宽度
    pub width: f64,
    /// 刻度颜色
    pub color: Color,
    /// 刻度数量
    pub split_number: usize,
}

impl Default for AxisTick {
    fn default() -> Self {
        Self {
            show: true,
            length: 15.0,
            width: 2.0,
            color: Color::rgb(0.5, 0.5, 0.5),
            split_number: 10,
        }
    }
}

/// 仪表盘标签配置
#[derive(Debug, Clone)]
pub struct AxisLabel {
    /// 是否显示标签
    pub show: bool,
    /// 标签距离
    pub distance: f64,
    /// 字体大小
    pub font_size: f64,
    /// 标签颜色
    pub color: Color,
    /// 数值格式化函数
    pub formatter: Option<String>,
}

impl Default for AxisLabel {
    fn default() -> Self {
        Self {
            show: true,
            distance: 25.0,
            font_size: 12.0,
            color: Color::rgb(0.3, 0.3, 0.3),
            formatter: None,
        }
    }
}

/// 仪表盘系列
#[derive(Debug, Clone)]
pub struct GaugeSeries {
    /// 基础配置
    config: ChartConfig,
    /// 数据项
    data: Vec<GaugeDataItem>,
    /// 中心点位置 (x, y) 相对坐标 [0, 1]
    center: (f64, f64),
    /// 半径
    radius: f64,
    /// 起始角度（度）
    start_angle: f64,
    /// 结束角度（度）
    end_angle: f64,
    /// 最小值
    min: f64,
    /// 最大值
    max: f64,
    /// 指针样式
    pointer: PointerStyle,
    /// 刻度配置
    axis_tick: AxisTick,
    /// 标签配置
    axis_label: AxisLabel,
    /// 是否显示标题
    show_title: bool,
    /// 标题位置
    title_offset_center: (f64, f64),
    /// 是否显示详情
    show_detail: bool,
    /// 详情位置
    detail_offset_center: (f64, f64),
}

impl GaugeSeries {
    /// 创建新的仪表盘系列
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            config: ChartConfig {
                name: name.into(),
                ..ChartConfig::default()
            },
            data: Vec::new(),
            center: (0.5, 0.5),
            radius: 0.75,
            start_angle: 225.0,
            end_angle: -45.0,
            min: 0.0,
            max: 100.0,
            pointer: PointerStyle::default(),
            axis_tick: AxisTick::default(),
            axis_label: AxisLabel::default(),
            show_title: true,
            title_offset_center: (0.0, -0.4),
            show_detail: true,
            detail_offset_center: (0.0, 0.4),
        }
    }

    /// 设置数据
    pub fn data(mut self, data: Vec<GaugeDataItem>) -> Self {
        self.data = data;
        self
    }

    /// 添加数据项
    pub fn add_data(mut self, item: GaugeDataItem) -> Self {
        self.data.push(item);
        self
    }

    /// 设置中心点
    pub fn center(mut self, x: f64, y: f64) -> Self {
        self.center = (x.clamp(0.0, 1.0), y.clamp(0.0, 1.0));
        self
    }

    /// 设置半径
    pub fn radius(mut self, radius: f64) -> Self {
        self.radius = radius.clamp(0.1, 1.0);
        self
    }

    /// 设置角度范围
    pub fn angle_range(mut self, start: f64, end: f64) -> Self {
        self.start_angle = start;
        self.end_angle = end;
        self
    }

    /// 设置数值范围
    pub fn value_range(mut self, min: f64, max: f64) -> Self {
        self.min = min;
        self.max = max;
        self
    }

    /// 设置指针样式
    pub fn pointer_style(mut self, pointer: PointerStyle) -> Self {
        self.pointer = pointer;
        self
    }

    /// 设置刻度配置
    pub fn axis_tick(mut self, axis_tick: AxisTick) -> Self {
        self.axis_tick = axis_tick;
        self
    }

    /// 设置标签配置
    pub fn axis_label(mut self, axis_label: AxisLabel) -> Self {
        self.axis_label = axis_label;
        self
    }

    /// 设置是否显示标题
    pub fn show_title(mut self, show: bool) -> Self {
        self.show_title = show;
        self
    }

    /// 设置是否显示详情
    pub fn show_detail(mut self, show: bool) -> Self {
        self.show_detail = show;
        self
    }

    /// 获取系列名称
    pub fn name(&self) -> &str {
        &self.config.name
    }

    /// 角度转弧度
    fn deg_to_rad(&self, deg: f64) -> f64 {
        deg * PI / 180.0
    }

    /// 计算数值对应的角度
    fn value_to_angle(&self, value: f64) -> f64 {
        let normalized = (value - self.min) / (self.max - self.min);
        let angle_range = self.end_angle - self.start_angle;
        self.start_angle + angle_range * normalized.clamp(0.0, 1.0)
    }

    /// 生成仪表盘外圈
    fn generate_gauge_arc(&self, center: Point, radius: f64) -> Vec<DrawCommand> {
        let mut commands = Vec::new();
        
        let start_rad = self.deg_to_rad(self.start_angle);
        let end_rad = self.deg_to_rad(self.end_angle);
        
        // 计算弧的起点和终点
        let start_point = Point {
            x: center.x + radius * start_rad.cos(),
            y: center.y + radius * start_rad.sin(),
        };
        let end_point = Point {
            x: center.x + radius * end_rad.cos(),
            y: center.y + radius * end_rad.sin(),
        };
        
        // 生成弧形路径
        let mut path_commands = Vec::new();
        path_commands.push(PathCommand::MoveTo(start_point));
        
        // 使用多个小弧段来近似大弧
        let angle_diff = end_rad - start_rad;
        let segments = 20;
        for i in 1..=segments {
            let angle = start_rad + angle_diff * (i as f64 / segments as f64);
            let point = Point {
                x: center.x + radius * angle.cos(),
                y: center.y + radius * angle.sin(),
            };
            path_commands.push(PathCommand::LineTo(point));
        }
        
        commands.push(DrawCommand::Path {
            commands: path_commands,
            style: PathStyle {
                fill: None,
                stroke: Some(LineStyle {
                    color: Color::rgb(0.8, 0.8, 0.8),
                    width: 8.0,
                    dash_pattern: None,
                    cap: LineCap::Round,
                    join: LineJoin::Round,
                    opacity: 1.0,
                }),
                opacity: 1.0,
                fill_rule: FillRule::NonZero,
            },
        });
        
        commands
    }

    /// 生成刻度线
    fn generate_ticks(&self, center: Point, radius: f64) -> Vec<DrawCommand> {
        let mut commands = Vec::new();
        
        if !self.axis_tick.show {
            return commands;
        }
        
        let angle_range = self.end_angle - self.start_angle;
        let tick_count = self.axis_tick.split_number + 1;
        
        for i in 0..tick_count {
            let angle = self.start_angle + angle_range * (i as f64 / self.axis_tick.split_number as f64);
            let angle_rad = self.deg_to_rad(angle);
            
            let outer_point = Point {
                x: center.x + radius * angle_rad.cos(),
                y: center.y + radius * angle_rad.sin(),
            };
            let inner_point = Point {
                x: center.x + (radius - self.axis_tick.length) * angle_rad.cos(),
                y: center.y + (radius - self.axis_tick.length) * angle_rad.sin(),
            };
            
            commands.push(DrawCommand::Path {
                commands: vec![
                    PathCommand::MoveTo(inner_point),
                    PathCommand::LineTo(outer_point),
                ],
                style: PathStyle {
                    fill: None,
                    stroke: Some(LineStyle {
                        color: self.axis_tick.color,
                        width: self.axis_tick.width,
                        dash_pattern: None,
                        cap: LineCap::Round,
                        join: LineJoin::Round,
                        opacity: 1.0,
                    }),
                    opacity: 1.0,
                    fill_rule: FillRule::NonZero,
                },
            });
        }
        
        commands
    }

    /// 生成标签
    fn generate_labels(&self, center: Point, radius: f64) -> Vec<DrawCommand> {
        let mut commands = Vec::new();

        if !self.axis_label.show {
            return commands;
        }

        let angle_range = self.end_angle - self.start_angle;
        let label_count = self.axis_tick.split_number + 1;
        let label_radius = radius - self.axis_label.distance;

        for i in 0..label_count {
            let angle = self.start_angle + angle_range * (i as f64 / self.axis_tick.split_number as f64);
            let angle_rad = self.deg_to_rad(angle);

            let label_point = Point {
                x: center.x + label_radius * angle_rad.cos(),
                y: center.y + label_radius * angle_rad.sin(),
            };

            let value = self.min + (self.max - self.min) * (i as f64 / self.axis_tick.split_number as f64);
            let label_text = if let Some(ref formatter) = self.axis_label.formatter {
                formatter.replace("{value}", &format!("{:.0}", value))
            } else {
                format!("{:.0}", value)
            };

            commands.push(DrawCommand::Text {
                text: label_text,
                position: label_point,
                style: TextStyle {
                    font_family: "Arial".to_string(),
                    font_size: self.axis_label.font_size,
                    font_weight: FontWeight::Normal,
                    font_style: FontStyle::Normal,
                    color: self.axis_label.color,
                    opacity: 1.0,
                    align: TextAlign::Center,
                    baseline: TextBaseline::Middle,
                    rotation: 0.0,
                    letter_spacing: 0.0,
                    line_height: 1.2,
                },
            });
        }

        commands
    }

    /// 生成指针
    fn generate_pointer(&self, center: Point, radius: f64, value: f64) -> Vec<DrawCommand> {
        let mut commands = Vec::new();

        let angle = self.value_to_angle(value);
        let angle_rad = self.deg_to_rad(angle);
        let pointer_length = radius * self.pointer.length;

        // 指针端点
        let pointer_end = Point {
            x: center.x + pointer_length * angle_rad.cos(),
            y: center.y + pointer_length * angle_rad.sin(),
        };

        // 指针基部的两个点（形成三角形）
        let base_angle1 = angle_rad + PI / 2.0;
        let base_angle2 = angle_rad - PI / 2.0;
        let base_radius = self.pointer.width / 2.0;

        let base_point1 = Point {
            x: center.x + base_radius * base_angle1.cos(),
            y: center.y + base_radius * base_angle1.sin(),
        };
        let base_point2 = Point {
            x: center.x + base_radius * base_angle2.cos(),
            y: center.y + base_radius * base_angle2.sin(),
        };

        // 绘制指针三角形
        commands.push(DrawCommand::Path {
            commands: vec![
                PathCommand::MoveTo(pointer_end),
                PathCommand::LineTo(base_point1),
                PathCommand::LineTo(base_point2),
                PathCommand::Close,
            ],
            style: PathStyle {
                fill: Some(self.pointer.color),
                stroke: Some(LineStyle {
                    color: Color::rgb(0.0, 0.0, 0.0),
                    width: 1.0,
                    dash_pattern: None,
                    cap: LineCap::Round,
                    join: LineJoin::Round,
                    opacity: 1.0,
                }),
                opacity: 1.0,
                fill_rule: FillRule::NonZero,
            },
        });

        // 绘制中心圆点
        commands.push(DrawCommand::Circle {
            center,
            radius: self.pointer.width,
            style: CircleStyle {
                fill: Some(self.pointer.color),
                stroke: Some(LineStyle {
                    color: Color::rgb(0.0, 0.0, 0.0),
                    width: 1.0,
                    dash_pattern: None,
                    cap: LineCap::Round,
                    join: LineJoin::Round,
                    opacity: 1.0,
                }),
                opacity: 1.0,
            },
        });

        commands
    }

    /// 生成标题和详情
    fn generate_title_and_detail(&self, center: Point, radius: f64) -> Vec<DrawCommand> {
        let mut commands = Vec::new();

        if let Some(data_item) = self.data.first() {
            // 生成标题
            if self.show_title {
                let title_point = Point {
                    x: center.x + radius * self.title_offset_center.0,
                    y: center.y + radius * self.title_offset_center.1,
                };

                commands.push(DrawCommand::Text {
                    text: data_item.name.clone(),
                    position: title_point,
                    style: TextStyle {
                        font_family: "Arial".to_string(),
                        font_size: 16.0,
                        font_weight: FontWeight::Bold,
                        font_style: FontStyle::Normal,
                        color: Color::rgb(0.2, 0.2, 0.2),
                        opacity: 1.0,
                        align: TextAlign::Center,
                        baseline: TextBaseline::Middle,
                        rotation: 0.0,
                        letter_spacing: 0.0,
                        line_height: 1.2,
                    },
                });
            }

            // 生成详情
            if self.show_detail {
                let detail_point = Point {
                    x: center.x + radius * self.detail_offset_center.0,
                    y: center.y + radius * self.detail_offset_center.1,
                };

                commands.push(DrawCommand::Text {
                    text: format!("{:.1}", data_item.value),
                    position: detail_point,
                    style: TextStyle {
                        font_family: "Arial".to_string(),
                        font_size: 24.0,
                        font_weight: FontWeight::Bold,
                        font_style: FontStyle::Normal,
                        color: data_item.color.unwrap_or(Color::rgb(0.2, 0.2, 0.2)),
                        opacity: 1.0,
                        align: TextAlign::Center,
                        baseline: TextBaseline::Middle,
                        rotation: 0.0,
                        letter_spacing: 0.0,
                        line_height: 1.2,
                    },
                });
            }
        }

        commands
    }
}

impl Series for GaugeSeries {
    fn render_to_commands(&self, coord_system: &dyn CoordinateSystem) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        if self.data.is_empty() {
            return Ok(commands);
        }

        // 获取绘图区域
        let bounds = coord_system.bounds();
        let chart_center = Point {
            x: bounds.origin.x + bounds.size.width * self.center.0,
            y: bounds.origin.y + bounds.size.height * self.center.1,
        };
        let chart_radius = (bounds.size.width.min(bounds.size.height) / 2.0) * self.radius;

        // 生成仪表盘外圈
        commands.extend(self.generate_gauge_arc(chart_center, chart_radius));

        // 生成刻度线
        commands.extend(self.generate_ticks(chart_center, chart_radius));

        // 生成标签
        commands.extend(self.generate_labels(chart_center, chart_radius));

        // 生成指针（使用第一个数据项）
        if let Some(data_item) = self.data.first() {
            commands.extend(self.generate_pointer(chart_center, chart_radius, data_item.value));
        }

        // 生成标题和详情
        commands.extend(self.generate_title_and_detail(chart_center, chart_radius));

        Ok(commands)
    }

    fn name(&self) -> &str {
        &self.config.name
    }

    fn series_type(&self) -> SeriesType {
        SeriesType::Gauge
    }

    fn bounds(&self) -> Option<echarts_core::Bounds> {
        // 仪表盘通常使用整个可用空间
        None
    }

    fn is_visible(&self) -> bool {
        true
    }

    fn z_index(&self) -> i32 {
        0
    }

    fn clone_series(&self) -> Box<dyn Series> {
        Box::new(self.clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use echarts_core::{CartesianCoordinateSystem, Bounds, Size};

    #[test]
    fn test_gauge_series_creation() {
        let series = GaugeSeries::new("测试仪表盘")
            .center(0.5, 0.5)
            .radius(0.8)
            .value_range(0.0, 100.0)
            .angle_range(225.0, -45.0);

        assert_eq!(series.name(), "测试仪表盘");
        assert_eq!(series.center, (0.5, 0.5));
        assert_eq!(series.radius, 0.8);
        assert_eq!(series.min, 0.0);
        assert_eq!(series.max, 100.0);
        assert_eq!(series.start_angle, 225.0);
        assert_eq!(series.end_angle, -45.0);
    }

    #[test]
    fn test_gauge_data_item() {
        let data_item = GaugeDataItem::new("速度", 75.5)
            .color(Color::rgb(1.0, 0.0, 0.0));

        assert_eq!(data_item.name, "速度");
        assert_eq!(data_item.value, 75.5);
        assert_eq!(data_item.color, Some(Color::rgb(1.0, 0.0, 0.0)));
    }

    #[test]
    fn test_pointer_style() {
        let pointer = PointerStyle {
            length: 0.9,
            width: 8.0,
            color: Color::rgb(0.0, 1.0, 0.0),
        };

        assert_eq!(pointer.length, 0.9);
        assert_eq!(pointer.width, 8.0);
        assert_eq!(pointer.color, Color::rgb(0.0, 1.0, 0.0));
    }

    #[test]
    fn test_gauge_series_with_data() {
        let data = vec![
            GaugeDataItem::new("CPU使用率", 65.0).color(Color::rgb(0.2, 0.8, 0.2)),
        ];

        let series = GaugeSeries::new("系统监控")
            .data(data)
            .value_range(0.0, 100.0)
            .show_title(true)
            .show_detail(true);

        assert_eq!(series.data.len(), 1);
        assert_eq!(series.data[0].name, "CPU使用率");
        assert_eq!(series.data[0].value, 65.0);
        assert!(series.show_title);
        assert!(series.show_detail);
    }

    #[test]
    fn test_value_to_angle_conversion() {
        let series = GaugeSeries::new("测试")
            .value_range(0.0, 100.0)
            .angle_range(180.0, 0.0);

        // 测试边界值
        assert_eq!(series.value_to_angle(0.0), 180.0);
        assert_eq!(series.value_to_angle(100.0), 0.0);
        assert_eq!(series.value_to_angle(50.0), 90.0);
    }

    #[test]
    fn test_deg_to_rad_conversion() {
        let series = GaugeSeries::new("测试");

        assert!((series.deg_to_rad(0.0) - 0.0).abs() < 1e-10);
        assert!((series.deg_to_rad(90.0) - PI / 2.0).abs() < 1e-10);
        assert!((series.deg_to_rad(180.0) - PI).abs() < 1e-10);
        assert!((series.deg_to_rad(270.0) - 3.0 * PI / 2.0).abs() < 1e-10);
    }

    #[test]
    fn test_gauge_series_rendering() {
        let data = vec![
            GaugeDataItem::new("温度", 42.5),
        ];

        let series = GaugeSeries::new("温度计")
            .data(data)
            .value_range(0.0, 100.0)
            .show_title(true)
            .show_detail(true);

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 400.0, height: 400.0 },
            },
            (0.0, 100.0),
            (0.0, 100.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();

        // 应该生成多个命令：外圈 + 刻度 + 标签 + 指针 + 标题/详情
        assert!(!commands.is_empty());

        // 检查命令类型
        let mut path_count = 0;
        let mut text_count = 0;
        let mut circle_count = 0;

        for cmd in &commands {
            match cmd {
                DrawCommand::Path { .. } => path_count += 1,
                DrawCommand::Text { .. } => text_count += 1,
                DrawCommand::Circle { .. } => circle_count += 1,
                _ => {}
            }
        }

        assert!(path_count > 0); // 外圈、刻度、指针
        assert!(text_count > 0); // 标签、标题、详情
        assert!(circle_count > 0); // 中心圆点
    }

    #[test]
    fn test_gauge_series_empty_data() {
        let series = GaugeSeries::new("空仪表盘");

        let coord_system = CartesianCoordinateSystem::new(
            Bounds {
                origin: Point { x: 0.0, y: 0.0 },
                size: Size { width: 400.0, height: 400.0 },
            },
            (0.0, 100.0),
            (0.0, 100.0),
        );

        let commands = series.render_to_commands(&coord_system).unwrap();
        assert!(commands.is_empty());
    }
}
// ============================================================================
// 图表基础架构集成 - ChartBase 实现
// ============================================================================

/// 为 GaugeSeries 实现 ChartBase trait
impl ChartBase for GaugeSeries {
    type DataType = Vec<GaugeDataItem>;

    fn name(&self) -> &str {
        &self.config.name
    }
    
    fn raw_data(&self) -> &Self::DataType {
        &self.data
    }

    fn to_dataset(&self) -> DataSet {
        // TODO: 实现 Vec<GaugeDataItem> 到 DataSet 的转换
        DataSet::new()
    }
    
    fn visible(&self) -> bool {
        self.config.visible
    }
    
    fn z_index(&self) -> i32 {
        self.config.z_index
    }
    
    fn bounds(&self) -> Option<Bounds> {
        // TODO: 为 GaugeSeries 实现边界计算
        None
    }
    
    fn config(&self) -> &ChartConfig {
        &self.config
    }
    
    fn config_mut(&mut self) -> &mut ChartConfig {
        &mut self.config
    }
}

/// 为 GaugeSeries 实现 ChartSeries trait
impl ChartSeries for GaugeSeries {
    // 所有方法都由默认实现提供，基于 ChartBase
}
