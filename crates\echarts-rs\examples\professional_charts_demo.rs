//! 专业曲线图演示
//!
//! 展示专业级曲线图功能的简化版本

use std::fs;
use std::collections::HashMap;

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    println!("📈 专业曲线图演示系统");
    println!("{}", "=".repeat(50));

    // 确保输出目录存在
    let output_dir = "temp/svg/professional_charts_demo";
    fs::create_dir_all(output_dir)?;
    println!("📁 输出目录: {}", output_dir);

    // 1. 金融图表
    println!("\n💰 1. 生成金融图表...");
    generate_financial_demo(output_dir)?;

    // 2. 技术分析图表
    println!("\n📊 2. 生成技术分析图表...");
    generate_technical_demo(output_dir)?;

    // 3. 商业智能图表
    println!("\n💼 3. 生成商业智能图表...");
    generate_business_demo(output_dir)?;

    // 4. 实时监控图表
    println!("\n⚡ 4. 生成实时监控图表...");
    generate_monitoring_demo(output_dir)?;

    // 5. 高级分析图表
    println!("\n🧮 5. 生成高级分析图表...");
    generate_analytics_demo(output_dir)?;

    // 6. 生成展示页面
    println!("\n📄 6. 生成展示页面...");
    generate_demo_showcase(output_dir)?;

    println!("\n🎉 专业曲线图演示生成完成！");
    println!("📁 所有文件已保存到: {}", output_dir);
    println!("🌐 打开 {}/professional_demo.html 查看演示", output_dir);

    Ok(())
}

/// 生成金融图表演示
fn generate_financial_demo(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // K线图数据
    let candlestick_data = generate_candlestick_data();
    let candlestick_svg = create_professional_candlestick_chart("专业K线图", &candlestick_data, 900.0, 600.0);
    fs::write(format!("{}/01_professional_candlestick.svg", output_dir), candlestick_svg)?;

    // 股票对比图
    let stock_comparison = generate_stock_comparison_data();
    let comparison_svg = create_stock_comparison_chart("多股票对比分析", &stock_comparison, 900.0, 600.0);
    fs::write(format!("{}/02_stock_comparison.svg", output_dir), comparison_svg)?;

    println!("  ✅ 金融图表演示生成完成 (2个图表)");
    Ok(())
}

/// 生成技术分析图表演示
fn generate_technical_demo(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 移动平均线
    let ma_data = generate_moving_average_data();
    let ma_svg = create_moving_average_chart("移动平均线技术分析", &ma_data, 900.0, 600.0);
    fs::write(format!("{}/03_moving_average_analysis.svg", output_dir), ma_svg)?;

    // RSI指标
    let rsi_data = generate_rsi_data();
    let rsi_svg = create_rsi_indicator_chart("RSI相对强弱指标", &rsi_data, 900.0, 500.0);
    fs::write(format!("{}/04_rsi_indicator.svg", output_dir), rsi_svg)?;

    println!("  ✅ 技术分析图表演示生成完成 (2个图表)");
    Ok(())
}

/// 生成商业智能图表演示
fn generate_business_demo(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // KPI仪表板
    let kpi_data = generate_kpi_data();
    let kpi_svg = create_kpi_dashboard_chart("KPI关键指标仪表板", &kpi_data, 1000.0, 700.0);
    fs::write(format!("{}/05_kpi_dashboard.svg", output_dir), kpi_svg)?;

    // 销售漏斗
    let funnel_data = generate_sales_funnel_data();
    let funnel_svg = create_sales_funnel_chart("销售转化漏斗分析", &funnel_data, 800.0, 600.0);
    fs::write(format!("{}/06_sales_funnel.svg", output_dir), funnel_svg)?;

    println!("  ✅ 商业智能图表演示生成完成 (2个图表)");
    Ok(())
}

/// 生成实时监控图表演示
fn generate_monitoring_demo(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 系统性能监控
    let performance_data = generate_performance_monitoring_data();
    let performance_svg = create_performance_monitoring_chart("系统性能实时监控", &performance_data, 1000.0, 600.0);
    fs::write(format!("{}/07_performance_monitoring.svg", output_dir), performance_svg)?;

    // 实时数据流
    let stream_data = generate_realtime_stream_data();
    let stream_svg = create_realtime_stream_chart("实时数据流监控", &stream_data, 900.0, 500.0);
    fs::write(format!("{}/08_realtime_stream.svg", output_dir), stream_svg)?;

    println!("  ✅ 实时监控图表演示生成完成 (2个图表)");
    Ok(())
}

/// 生成高级分析图表演示
fn generate_analytics_demo(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 预测分析
    let prediction_data = generate_prediction_analysis_data();
    let prediction_svg = create_prediction_analysis_chart("预测分析与置信区间", &prediction_data, 900.0, 600.0);
    fs::write(format!("{}/09_prediction_analysis.svg", output_dir), prediction_svg)?;

    // 异常检测
    let anomaly_data = generate_anomaly_detection_data();
    let anomaly_svg = create_anomaly_detection_chart("异常检测分析", &anomaly_data, 900.0, 600.0);
    fs::write(format!("{}/10_anomaly_detection.svg", output_dir), anomaly_svg)?;

    println!("  ✅ 高级分析图表演示生成完成 (2个图表)");
    Ok(())
}

// ============================================================================
// 数据生成函数
// ============================================================================

/// 蜡烛图数据点
#[derive(Debug, Clone)]
struct CandlestickPoint {
    timestamp: f64,
    open: f64,
    high: f64,
    low: f64,
    close: f64,
    volume: f64,
}

/// 生成蜡烛图数据
fn generate_candlestick_data() -> Vec<CandlestickPoint> {
    let mut data = Vec::new();
    let mut price = 100.0;
    
    for i in 0..60 {
        let timestamp = i as f64;
        let volatility = 0.02;
        
        let open = price;
        let change = (rand_f64() - 0.5) * volatility * price;
        let close = open + change;
        
        let high = open.max(close) + rand_f64() * volatility * price * 0.5;
        let low = open.min(close) - rand_f64() * volatility * price * 0.5;
        let volume = 1000000.0 + rand_f64() * 5000000.0;
        
        data.push(CandlestickPoint {
            timestamp,
            open,
            high,
            low,
            close,
            volume,
        });
        
        price = close;
    }
    
    data
}

/// 生成股票对比数据
fn generate_stock_comparison_data() -> HashMap<String, Vec<(f64, f64)>> {
    let mut stocks = HashMap::new();
    
    let symbols = vec!["AAPL", "GOOGL", "MSFT", "TSLA"];
    
    for symbol in symbols {
        let mut data = Vec::new();
        let mut price = 100.0 + rand_f64() * 200.0;
        
        for i in 0..100 {
            let change = (rand_f64() - 0.5) * 0.05 * price;
            price += change;
            data.push((i as f64, price));
        }
        
        stocks.insert(symbol.to_string(), data);
    }
    
    stocks
}

/// 生成移动平均线数据
fn generate_moving_average_data() -> (Vec<(f64, f64)>, Vec<(String, Vec<(f64, f64)>, String)>) {
    let mut price_data = Vec::new();
    let mut price = 100.0;
    
    // 生成价格数据
    for i in 0..100 {
        let change = (rand_f64() - 0.5) * 0.04 * price;
        price += change;
        price_data.push((i as f64, price));
    }
    
    // 计算移动平均线
    let ma5 = calculate_moving_average(&price_data, 5);
    let ma20 = calculate_moving_average(&price_data, 20);
    let ma60 = calculate_moving_average(&price_data, 60);
    
    let indicators = vec![
        ("MA5".to_string(), ma5, "ff6b6b".to_string()),
        ("MA20".to_string(), ma20, "4ecdc4".to_string()),
        ("MA60".to_string(), ma60, "45b7d1".to_string()),
    ];
    
    (price_data, indicators)
}

/// 计算移动平均线
fn calculate_moving_average(data: &[(f64, f64)], period: usize) -> Vec<(f64, f64)> {
    let mut ma_data = Vec::new();
    
    for i in period..data.len() {
        let sum: f64 = data[i-period+1..=i].iter().map(|(_, y)| y).sum();
        let avg = sum / period as f64;
        ma_data.push((data[i].0, avg));
    }
    
    ma_data
}

/// 生成RSI数据
fn generate_rsi_data() -> (Vec<(f64, f64)>, Vec<(f64, f64)>) {
    let mut price_data = Vec::new();
    let mut price = 100.0;
    
    // 生成价格数据
    for i in 0..100 {
        let change = (rand_f64() - 0.5) * 0.06 * price;
        price += change;
        price_data.push((i as f64, price));
    }
    
    // 计算RSI
    let rsi_data = calculate_rsi(&price_data, 14);
    
    (price_data, rsi_data)
}

/// 计算RSI
fn calculate_rsi(data: &[(f64, f64)], period: usize) -> Vec<(f64, f64)> {
    let mut rsi_data = Vec::new();
    
    if data.len() < period + 1 {
        return rsi_data;
    }
    
    for i in period..data.len() {
        let mut gains = 0.0;
        let mut losses = 0.0;
        
        for j in i-period+1..=i {
            let change = data[j].1 - data[j-1].1;
            if change > 0.0 {
                gains += change;
            } else {
                losses += -change;
            }
        }
        
        let avg_gain = gains / period as f64;
        let avg_loss = losses / period as f64;
        
        let rs = if avg_loss != 0.0 { avg_gain / avg_loss } else { 100.0 };
        let rsi = 100.0 - (100.0 / (1.0 + rs));
        
        rsi_data.push((data[i].0, rsi));
    }
    
    rsi_data
}

/// KPI数据
#[derive(Debug, Clone)]
struct KpiMetric {
    name: String,
    current_value: f64,
    target_value: f64,
    trend: Vec<(f64, f64)>,
    unit: String,
}

/// 生成KPI数据
fn generate_kpi_data() -> Vec<KpiMetric> {
    vec![
        KpiMetric {
            name: "月度收入".to_string(),
            current_value: 1250000.0,
            target_value: 1200000.0,
            trend: (0..30).map(|i| (i as f64, 1000000.0 + i as f64 * 8000.0 + (rand_f64() - 0.5) * 50000.0)).collect(),
            unit: "元".to_string(),
        },
        KpiMetric {
            name: "用户增长".to_string(),
            current_value: 15420.0,
            target_value: 15000.0,
            trend: (0..30).map(|i| (i as f64, 10000.0 + i as f64 * 180.0 + (rand_f64() - 0.5) * 500.0)).collect(),
            unit: "人".to_string(),
        },
        KpiMetric {
            name: "转化率".to_string(),
            current_value: 3.45,
            target_value: 3.50,
            trend: (0..30).map(|i| (i as f64, 3.0 + (i as f64 * 0.01) + (rand_f64() - 0.5) * 0.2)).collect(),
            unit: "%".to_string(),
        },
        KpiMetric {
            name: "客户满意度".to_string(),
            current_value: 4.2,
            target_value: 4.0,
            trend: (0..30).map(|i| (i as f64, 3.8 + (i as f64 * 0.01) + (rand_f64() - 0.5) * 0.3)).collect(),
            unit: "分".to_string(),
        },
    ]
}

/// 生成销售漏斗数据
fn generate_sales_funnel_data() -> Vec<(String, f64)> {
    vec![
        ("网站访问".to_string(), 100000.0),
        ("注册用户".to_string(), 25000.0),
        ("活跃用户".to_string(), 15000.0),
        ("付费用户".to_string(), 3000.0),
        ("续费用户".to_string(), 2100.0),
    ]
}

/// 生成性能监控数据
fn generate_performance_monitoring_data() -> HashMap<String, Vec<(f64, f64)>> {
    let mut data = HashMap::new();
    
    // CPU使用率
    let cpu_usage: Vec<(f64, f64)> = (0..120).map(|i| {
        let time = i as f64;
        let base = 30.0;
        let spike = if i % 20 == 0 { 40.0 } else { 0.0 };
        let noise = (rand_f64() - 0.5) * 10.0;
        (time, (base + spike + noise).max(0.0).min(100.0))
    }).collect();
    
    // 内存使用率
    let memory_usage: Vec<(f64, f64)> = (0..120).map(|i| {
        let time = i as f64;
        let base = 45.0 + (time * 0.1).sin() * 15.0;
        let noise = (rand_f64() - 0.5) * 5.0;
        (time, (base + noise).max(0.0).min(100.0))
    }).collect();
    
    data.insert("CPU使用率".to_string(), cpu_usage);
    data.insert("内存使用率".to_string(), memory_usage);
    
    data
}

/// 生成实时流数据
fn generate_realtime_stream_data() -> Vec<(f64, f64)> {
    (0..200).map(|i| {
        let time = i as f64;
        let base = 50.0;
        let wave1 = (time * 0.1).sin() * 20.0;
        let wave2 = (time * 0.05).cos() * 10.0;
        let noise = (rand_f64() - 0.5) * 5.0;
        (time, base + wave1 + wave2 + noise)
    }).collect()
}

/// 生成预测分析数据
fn generate_prediction_analysis_data() -> (Vec<(f64, f64)>, Vec<(f64, f64)>, Vec<(f64, f64)>, Vec<(f64, f64)>) {
    let mut historical = Vec::new();
    let mut prediction = Vec::new();
    let mut upper_bound = Vec::new();
    let mut lower_bound = Vec::new();
    
    // 历史数据
    for i in 0..50 {
        let x = i as f64;
        let y = 100.0 + (x * 0.2).sin() * 30.0 + (rand_f64() - 0.5) * 10.0;
        historical.push((x, y));
    }
    
    // 预测数据
    let last_value = historical.last().unwrap().1;
    for i in 50..80 {
        let x = i as f64;
        let pred_y = last_value + (x - 50.0) * 0.5 + ((x - 50.0) * 0.2).sin() * 20.0;
        let confidence = 5.0 + (x - 50.0) * 0.5;
        
        prediction.push((x, pred_y));
        upper_bound.push((x, pred_y + confidence));
        lower_bound.push((x, pred_y - confidence));
    }
    
    (historical, prediction, upper_bound, lower_bound)
}

/// 生成异常检测数据
fn generate_anomaly_detection_data() -> (Vec<(f64, f64)>, Vec<(f64, f64)>) {
    let mut normal_data = Vec::new();
    let mut anomalies = Vec::new();
    
    for i in 0..200 {
        let x = i as f64;
        let base = 50.0 + (x * 0.1).sin() * 20.0;
        
        // 添加异常点
        if i == 50 || i == 120 || i == 180 {
            let anomaly_value = base + (rand_f64() - 0.5) * 80.0;
            anomalies.push((x, anomaly_value));
            normal_data.push((x, anomaly_value));
        } else {
            let normal_value = base + (rand_f64() - 0.5) * 5.0;
            normal_data.push((x, normal_value));
        }
    }
    
    (normal_data, anomalies)
}

/// 简单随机数生成器
fn rand_f64() -> f64 {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};
    use std::time::{SystemTime, UNIX_EPOCH};
    
    let mut hasher = DefaultHasher::new();
    SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_nanos().hash(&mut hasher);
    (hasher.finish() % 1000000) as f64 / 1000000.0
}

// ============================================================================
// 图表生成函数
// ============================================================================

/// 创建专业K线图
fn create_professional_candlestick_chart(title: &str, data: &[CandlestickPoint], width: f64, height: f64) -> String {
    let mut svg = String::new();

    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 定义渐变和滤镜
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"bullishGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#4caf50;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#2e7d32;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("    <linearGradient id=\"bearishGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#f44336;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#c62828;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("    <filter id=\"shadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n");
    svg.push_str("      <feDropShadow dx=\"1\" dy=\"1\" stdDeviation=\"1\" flood-color=\"#000000\" flood-opacity=\"0.3\"/>\n");
    svg.push_str("    </filter>\n");
    svg.push_str("  </defs>\n");

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 200.0;
    let volume_height = 120.0;

    if !data.is_empty() {
        // 计算价格范围
        let min_price = data.iter().map(|p| p.low).fold(f64::INFINITY, f64::min);
        let max_price = data.iter().map(|p| p.high).fold(f64::NEG_INFINITY, f64::max);
        let price_range = max_price - min_price;

        // 计算成交量范围
        let max_volume = data.iter().map(|p| p.volume).fold(f64::NEG_INFINITY, f64::max);

        // 绘制价格区域背景
        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, chart_height));

        // 绘制成交量区域背景
        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"#f5f5f5\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y + chart_height + 20.0, chart_width, volume_height));

        // 绘制网格线
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));

            // 价格标签
            let price = max_price - (i as f64 / 5.0) * price_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.2}</text>\n", chart_x - 10.0, y + 4.0, price));
        }

        // 绘制蜡烛
        let candle_width = (chart_width / data.len() as f64) * 0.6;

        for (i, point) in data.iter().enumerate() {
            let x = chart_x + (i as f64 + 0.5) * (chart_width / data.len() as f64);

            // 价格坐标
            let open_y = chart_y + chart_height - ((point.open - min_price) / price_range) * chart_height;
            let close_y = chart_y + chart_height - ((point.close - min_price) / price_range) * chart_height;
            let high_y = chart_y + chart_height - ((point.high - min_price) / price_range) * chart_height;
            let low_y = chart_y + chart_height - ((point.low - min_price) / price_range) * chart_height;

            // 判断涨跌
            let is_bullish = point.close >= point.open;
            let fill_color = if is_bullish { "url(#bullishGradient)" } else { "url(#bearishGradient)" };

            // 绘制影线
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#666\" stroke-width=\"1\"/>\n", x, high_y, x, low_y));

            // 绘制实体
            let body_top = open_y.min(close_y);
            let body_height = (open_y - close_y).abs().max(1.0);

            svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"{}\" stroke=\"#333\" stroke-width=\"1\" filter=\"url(#shadow)\"/>\n",
                x - candle_width / 2.0, body_top, candle_width, body_height, fill_color));

            // 绘制成交量柱
            let volume_bar_height = (point.volume / max_volume) * volume_height;
            let volume_y = chart_y + chart_height + 20.0 + volume_height - volume_bar_height;
            let volume_color = if is_bullish { "#4caf50" } else { "#f44336" };

            svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"{}\" opacity=\"0.7\"/>\n",
                x - candle_width / 2.0, volume_y, candle_width, volume_bar_height, volume_color));
        }

        // 添加标签
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"14\" font-weight=\"bold\" fill=\"#333\">价格</text>\n", chart_x, chart_y - 10.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"14\" font-weight=\"bold\" fill=\"#333\">成交量</text>\n", chart_x, chart_y + chart_height + 40.0));
    }

    svg.push_str("</svg>");
    svg
}

/// 创建股票对比图表
fn create_stock_comparison_chart(title: &str, data: &HashMap<String, Vec<(f64, f64)>>, width: f64, height: f64) -> String {
    let mut svg = String::new();

    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 定义颜色
    let colors = vec!["2196f3", "4caf50", "ff9800", "f44336", "9c27b0"];

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 250.0;
    let chart_height = height - 120.0;

    // 绘制背景
    svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, chart_height));

    if !data.is_empty() {
        // 计算全局范围
        let mut all_values = Vec::new();
        for series in data.values() {
            all_values.extend(series.iter().map(|(_, y)| *y));
        }

        let min_y = all_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_y = all_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let y_range = max_y - min_y;

        let max_x = data.values().map(|s| s.len()).max().unwrap_or(0) as f64;

        // 绘制网格线
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));

            let value = max_y - (i as f64 / 5.0) * y_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n", chart_x - 10.0, y + 4.0, value));
        }

        // 绘制每个股票的曲线
        for (idx, (symbol, series)) in data.iter().enumerate() {
            let color = &colors[idx % colors.len()];

            if !series.is_empty() {
                let mut path = String::from("M");

                for (i, (_, y)) in series.iter().enumerate() {
                    let px = chart_x + (i as f64 / max_x) * chart_width;
                    let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;

                    if i == 0 {
                        path.push_str(&format!(" {} {}", px, py));
                    } else {
                        path.push_str(&format!(" L {} {}", px, py));
                    }
                }

                svg.push_str("  <path d=\"");
                svg.push_str(&path);
                svg.push_str("\" stroke=\"#");
                svg.push_str(color);
                svg.push_str("\" stroke-width=\"3\" fill=\"none\"/>\n");

                // 图例
                let legend_y = chart_y + 30.0 + idx as f64 * 25.0;
                svg.push_str("  <line x1=\"");
                svg.push_str(&(chart_x + chart_width + 20.0).to_string());
                svg.push_str("\" y1=\"");
                svg.push_str(&legend_y.to_string());
                svg.push_str("\" x2=\"");
                svg.push_str(&(chart_x + chart_width + 40.0).to_string());
                svg.push_str("\" y2=\"");
                svg.push_str(&legend_y.to_string());
                svg.push_str("\" stroke=\"#");
                svg.push_str(color);
                svg.push_str("\" stroke-width=\"3\"/>\n");
                svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"14\" fill=\"#333\">{}</text>\n", chart_x + chart_width + 45.0, legend_y + 4.0, symbol));
            }
        }
    }

    svg.push_str("</svg>");
    svg
}

// 引入图表生成器
mod professional_demo_charts;
use professional_demo_charts::*;

/// 创建销售漏斗图表
fn create_sales_funnel_chart(title: &str, data: &[(String, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();

    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 定义渐变
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"funnelGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#2196f3;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#1976d2;stop-opacity:1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    if !data.is_empty() {
        let chart_x = 100.0;
        let chart_y = 60.0;
        let chart_width = width - 200.0;
        let chart_height = height - 120.0;

        let max_value = data.iter().map(|(_, v)| *v).fold(f64::NEG_INFINITY, f64::max);
        let stage_height = chart_height / data.len() as f64;

        for (idx, (stage, value)) in data.iter().enumerate() {
            let y = chart_y + idx as f64 * stage_height;
            let stage_width = (value / max_value) * chart_width;
            let x_offset = (chart_width - stage_width) / 2.0;

            // 漏斗段
            svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"url(#funnelGradient)\" stroke=\"white\" stroke-width=\"2\"/>\n",
                chart_x + x_offset, y, stage_width, stage_height - 5.0));

            // 标签
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"14\" font-weight=\"bold\" fill=\"white\">{}</text>\n",
                chart_x + chart_width / 2.0, y + stage_height / 2.0 - 5.0, stage));

            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"white\">{:.0}</text>\n",
                chart_x + chart_width / 2.0, y + stage_height / 2.0 + 10.0, value));

            // 转化率
            if idx > 0 {
                let prev_value = data[idx - 1].1;
                let conversion_rate = (value / prev_value) * 100.0;
                svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"start\" font-size=\"12\" fill=\"#666\">{:.1}%</text>\n",
                    chart_x + chart_width + 20.0, y + stage_height / 2.0, conversion_rate));
            }
        }
    }

    svg.push_str("</svg>");
    svg
}

/// 创建性能监控图表
fn create_performance_monitoring_chart(title: &str, data: &HashMap<String, Vec<(f64, f64)>>, width: f64, height: f64) -> String {
    let mut svg = String::new();

    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 200.0;
    let chart_height = height - 120.0;

    // 绘制背景
    svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, chart_height));

    if !data.is_empty() {
        // 绘制网格线
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));

            let value = 100.0 - (i as f64 / 5.0) * 100.0;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}%</text>\n", chart_x - 10.0, y + 4.0, value));
        }

        // 颜色数组
        let colors = vec!["2196f3", "f44336"];

        // 绘制每个指标的曲线
        for (idx, (name, series)) in data.iter().enumerate() {
            let color = &colors[idx % colors.len()];

            if !series.is_empty() {
                let max_x = series.len() as f64;
                let mut path = String::from("M");

                for (i, (_, y)) in series.iter().enumerate() {
                    let px = chart_x + (i as f64 / max_x) * chart_width;
                    let py = chart_y + chart_height - (y / 100.0) * chart_height;

                    if i == 0 {
                        path.push_str(&format!(" {} {}", px, py));
                    } else {
                        path.push_str(&format!(" L {} {}", px, py));
                    }
                }

                svg.push_str("  <path d=\"");
                svg.push_str(&path);
                svg.push_str("\" stroke=\"#");
                svg.push_str(color);
                svg.push_str("\" stroke-width=\"2\" fill=\"none\"/>\n");

                // 图例
                let legend_y = chart_y + 30.0 + idx as f64 * 25.0;
                svg.push_str("  <line x1=\"");
                svg.push_str(&(chart_x + chart_width + 20.0).to_string());
                svg.push_str("\" y1=\"");
                svg.push_str(&legend_y.to_string());
                svg.push_str("\" x2=\"");
                svg.push_str(&(chart_x + chart_width + 40.0).to_string());
                svg.push_str("\" y2=\"");
                svg.push_str(&legend_y.to_string());
                svg.push_str("\" stroke=\"#");
                svg.push_str(color);
                svg.push_str("\" stroke-width=\"2\"/>\n");
                svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">{}</text>\n", chart_x + chart_width + 45.0, legend_y + 4.0, name));
            }
        }
    }

    svg.push_str("</svg>");
    svg
}

/// 创建实时流图表
fn create_realtime_stream_chart(title: &str, data: &[(f64, f64)], width: f64, height: f64) -> String {
    let mut svg = String::new();

    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 定义渐变
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"streamGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#00bcd4;stop-opacity:0.8\" />\n");
    svg.push_str("      <stop offset=\"50%\" style=\"stop-color:#2196f3;stop-opacity:1\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#3f51b5;stop-opacity:0.8\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 160.0;
    let chart_height = height - 120.0;

    // 绘制背景
    svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, chart_height));

    if !data.is_empty() {
        // 计算数据范围
        let min_y = data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        let y_range = max_y - min_y;
        let max_x = data.len() as f64;

        // 绘制网格线
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));

            let value = max_y - (i as f64 / 5.0) * y_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.1}</text>\n", chart_x - 10.0, y + 4.0, value));
        }

        // 生成路径
        let mut path = String::from("M");
        for (i, (_, y)) in data.iter().enumerate() {
            let px = chart_x + (i as f64 / max_x) * chart_width;
            let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;

            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }

        // 绘制流线
        svg.push_str(&format!("  <path d=\"{}\" stroke=\"url(#streamGradient)\" stroke-width=\"3\" fill=\"none\">\n", path));
        svg.push_str("    <animate attributeName=\"stroke-dasharray\" values=\"0,1000;1000,0\" dur=\"3s\" repeatCount=\"indefinite\"/>\n");
        svg.push_str("  </path>\n");

        // 绘制动态数据点
        for (i, (_, y)) in data.iter().enumerate() {
            if i % 20 == 0 { // 每20个点显示一个
                let px = chart_x + (i as f64 / max_x) * chart_width;
                let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;

                svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"4\" fill=\"#2196f3\" stroke=\"white\" stroke-width=\"2\">\n", px, py));
                svg.push_str(&format!("    <animate attributeName=\"r\" values=\"3;8;3\" dur=\"2s\" begin=\"{}s\" repeatCount=\"indefinite\"/>\n", i as f64 * 0.1));
                svg.push_str("  </circle>\n");
            }
        }
    }

    svg.push_str("</svg>");
    svg
}

/// 创建预测分析图表
fn create_prediction_analysis_chart(title: &str, data: &(Vec<(f64, f64)>, Vec<(f64, f64)>, Vec<(f64, f64)>, Vec<(f64, f64)>), width: f64, height: f64) -> String {
    let (historical, prediction, upper_bound, lower_bound) = data;
    let mut svg = String::new();

    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 定义渐变和滤镜
    svg.push_str("  <defs>\n");
    svg.push_str("    <linearGradient id=\"confidenceGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n");
    svg.push_str("      <stop offset=\"0%\" style=\"stop-color:#2196f3;stop-opacity:0.3\" />\n");
    svg.push_str("      <stop offset=\"100%\" style=\"stop-color:#2196f3;stop-opacity:0.1\" />\n");
    svg.push_str("    </linearGradient>\n");
    svg.push_str("  </defs>\n");

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 200.0;
    let chart_height = height - 120.0;

    // 绘制背景
    svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, chart_height));

    if !historical.is_empty() && !prediction.is_empty() {
        // 计算数据范围
        let mut all_values: Vec<f64> = historical.iter().map(|(_, y)| *y).collect();
        all_values.extend(prediction.iter().map(|(_, y)| *y));
        all_values.extend(upper_bound.iter().map(|(_, y)| *y));
        all_values.extend(lower_bound.iter().map(|(_, y)| *y));

        let min_y = all_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_y = all_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let y_range = max_y - min_y;

        let max_x = prediction.last().unwrap().0;
        let split_x = historical.last().unwrap().0;

        // 绘制网格线
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));

            let value = max_y - (i as f64 / 5.0) * y_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.0}</text>\n", chart_x - 10.0, y + 4.0, value));
        }

        // 分割线
        let split_px = chart_x + (split_x / max_x) * chart_width;
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#ff9800\" stroke-width=\"2\" stroke-dasharray=\"5,5\"/>\n", split_px, chart_y, split_px, chart_y + chart_height));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"middle\" font-size=\"12\" fill=\"#ff9800\">预测开始</text>\n", split_px, chart_y - 10.0));

        // 绘制置信区间
        if !upper_bound.is_empty() && !lower_bound.is_empty() {
            let mut area_path = String::new();

            // 上边界
            for (i, (x, y)) in upper_bound.iter().enumerate() {
                let px = chart_x + (x / max_x) * chart_width;
                let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;

                if i == 0 {
                    area_path.push_str(&format!("M {} {}", px, py));
                } else {
                    area_path.push_str(&format!(" L {} {}", px, py));
                }
            }

            // 下边界（反向）
            for (x, y) in lower_bound.iter().rev() {
                let px = chart_x + (x / max_x) * chart_width;
                let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;
                area_path.push_str(&format!(" L {} {}", px, py));
            }
            area_path.push_str(" Z");

            svg.push_str(&format!("  <path d=\"{}\" fill=\"url(#confidenceGradient)\"/>\n", area_path));
        }

        // 绘制历史数据
        let mut historical_path = String::from("M");
        for (i, (x, y)) in historical.iter().enumerate() {
            let px = chart_x + (x / max_x) * chart_width;
            let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;

            if i == 0 {
                historical_path.push_str(&format!(" {} {}", px, py));
            } else {
                historical_path.push_str(&format!(" L {} {}", px, py));
            }
        }

        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#333\" stroke-width=\"3\" fill=\"none\"/>\n", historical_path));

        // 绘制预测数据
        let mut forecast_path = String::from("M");
        for (i, (x, y)) in prediction.iter().enumerate() {
            let px = chart_x + (x / max_x) * chart_width;
            let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;

            if i == 0 {
                forecast_path.push_str(&format!(" {} {}", px, py));
            } else {
                forecast_path.push_str(&format!(" L {} {}", px, py));
            }
        }

        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#2196f3\" stroke-width=\"3\" stroke-dasharray=\"8,4\" fill=\"none\"/>\n", forecast_path));

        // 图例
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#333\" stroke-width=\"3\"/>\n", chart_x + chart_width + 20.0, chart_y + 20.0, chart_x + chart_width + 40.0, chart_y + 20.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">历史数据</text>\n", chart_x + chart_width + 45.0, chart_y + 25.0));

        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#2196f3\" stroke-width=\"3\" stroke-dasharray=\"8,4\"/>\n", chart_x + chart_width + 20.0, chart_y + 40.0, chart_x + chart_width + 40.0, chart_y + 40.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">预测数据</text>\n", chart_x + chart_width + 45.0, chart_y + 45.0));

        svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"20\" height=\"10\" fill=\"url(#confidenceGradient)\"/>\n", chart_x + chart_width + 20.0, chart_y + 55.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">置信区间</text>\n", chart_x + chart_width + 45.0, chart_y + 65.0));
    }

    svg.push_str("</svg>");
    svg
}

/// 创建异常检测图表
fn create_anomaly_detection_chart(title: &str, data: &(Vec<(f64, f64)>, Vec<(f64, f64)>), width: f64, height: f64) -> String {
    let (normal_data, anomalies) = data;
    let mut svg = String::new();

    svg.push_str(&format!("<svg width=\"{}\" height=\"{}\" xmlns=\"http://www.w3.org/2000/svg\">\n", width, height));

    // 定义滤镜
    svg.push_str("  <defs>\n");
    svg.push_str("    <filter id=\"anomalyGlow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n");
    svg.push_str("      <feGaussianBlur stdDeviation=\"3\" result=\"coloredBlur\"/>\n");
    svg.push_str("      <feMerge>\n");
    svg.push_str("        <feMergeNode in=\"coloredBlur\"/>\n");
    svg.push_str("        <feMergeNode in=\"SourceGraphic\"/>\n");
    svg.push_str("      </feMerge>\n");
    svg.push_str("    </filter>\n");
    svg.push_str("  </defs>\n");

    // 背景
    svg.push_str("  <rect width=\"100%\" height=\"100%\" fill=\"#fafafa\"/>\n");

    // 标题
    svg.push_str(&format!("  <text x=\"50%\" y=\"30\" text-anchor=\"middle\" font-size=\"20\" font-weight=\"bold\" fill=\"#333\">{}</text>\n", title));

    // 图表区域
    let chart_x = 80.0;
    let chart_y = 60.0;
    let chart_width = width - 200.0;
    let chart_height = height - 120.0;

    // 绘制背景
    svg.push_str(&format!("  <rect x=\"{}\" y=\"{}\" width=\"{}\" height=\"{}\" fill=\"white\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n", chart_x, chart_y, chart_width, chart_height));

    if !normal_data.is_empty() {
        // 计算数据范围
        let min_y = normal_data.iter().map(|(_, y)| *y).fold(f64::INFINITY, f64::min);
        let max_y = normal_data.iter().map(|(_, y)| *y).fold(f64::NEG_INFINITY, f64::max);
        let y_range = max_y - min_y;
        let max_x = normal_data.len() as f64;

        // 绘制网格线
        for i in 0..=5 {
            let y = chart_y + (i as f64 / 5.0) * chart_height;
            svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#f0f0f0\" stroke-width=\"1\"/>\n", chart_x, y, chart_x + chart_width, y));

            let value = max_y - (i as f64 / 5.0) * y_range;
            svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" text-anchor=\"end\" font-size=\"12\" fill=\"#666\">{:.1}</text>\n", chart_x - 10.0, y + 4.0, value));
        }

        // 绘制正常数据曲线
        let mut path = String::from("M");
        for (i, (_, y)) in normal_data.iter().enumerate() {
            let px = chart_x + (i as f64 / max_x) * chart_width;
            let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;

            if i == 0 {
                path.push_str(&format!(" {} {}", px, py));
            } else {
                path.push_str(&format!(" L {} {}", px, py));
            }
        }

        svg.push_str(&format!("  <path d=\"{}\" stroke=\"#2196f3\" stroke-width=\"2\" fill=\"none\"/>\n", path));

        // 绘制异常点
        for (x, y) in anomalies {
            let px = chart_x + (x / max_x) * chart_width;
            let py = chart_y + chart_height - ((y - min_y) / y_range) * chart_height;

            svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"8\" fill=\"#f44336\" stroke=\"white\" stroke-width=\"2\" filter=\"url(#anomalyGlow)\">\n", px, py));
            svg.push_str("    <animate attributeName=\"r\" values=\"6;12;6\" dur=\"2s\" repeatCount=\"indefinite\"/>\n");
            svg.push_str("  </circle>\n");
        }

        // 图例
        svg.push_str(&format!("  <line x1=\"{}\" y1=\"{}\" x2=\"{}\" y2=\"{}\" stroke=\"#2196f3\" stroke-width=\"2\"/>\n", chart_x + chart_width + 20.0, chart_y + 20.0, chart_x + chart_width + 40.0, chart_y + 20.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">正常数据</text>\n", chart_x + chart_width + 45.0, chart_y + 25.0));

        svg.push_str(&format!("  <circle cx=\"{}\" cy=\"{}\" r=\"6\" fill=\"#f44336\" stroke=\"white\" stroke-width=\"2\"/>\n", chart_x + chart_width + 30.0, chart_y + 40.0));
        svg.push_str(&format!("  <text x=\"{}\" y=\"{}\" font-size=\"12\" fill=\"#333\">异常点</text>\n", chart_x + chart_width + 45.0, chart_y + 45.0));
    }

    svg.push_str("</svg>");
    svg
}

/// 生成展示页面
fn generate_demo_showcase(output_dir: &str) -> std::result::Result<(), Box<dyn std::error::Error>> {
    let html_content = r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业曲线图演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1600px; margin: 0 auto; }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .section {
            background: rgba(255,255,255,0.95);
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .chart-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-item:hover {
            transform: translateY(-5px);
        }
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        .chart-svg {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }
        h1 { font-size: 2.5em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h2 { color: #333; margin-bottom: 20px; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        .description {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 专业曲线图演示</h1>
            <p class="description">展现 ECharts-rs 专业级数据可视化能力</p>
            <div class="feature-list">
                <div class="feature">
                    <h3>💰 金融分析</h3>
                    <p>K线图、股票对比</p>
                </div>
                <div class="feature">
                    <h3>📊 技术指标</h3>
                    <p>移动平均线、RSI</p>
                </div>
                <div class="feature">
                    <h3>💼 商业智能</h3>
                    <p>KPI仪表板、销售漏斗</p>
                </div>
                <div class="feature">
                    <h3>⚡ 实时监控</h3>
                    <p>性能监控、数据流</p>
                </div>
                <div class="feature">
                    <h3>🧮 高级分析</h3>
                    <p>预测分析、异常检测</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>💰 金融分析图表</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">专业K线图</div>
                    <object class="chart-svg" data="01_professional_candlestick.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">多股票对比分析</div>
                    <object class="chart-svg" data="02_stock_comparison.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 技术分析图表</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">移动平均线技术分析</div>
                    <object class="chart-svg" data="03_moving_average_analysis.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">RSI相对强弱指标</div>
                    <object class="chart-svg" data="04_rsi_indicator.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>💼 商业智能图表</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">KPI关键指标仪表板</div>
                    <object class="chart-svg" data="05_kpi_dashboard.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">销售转化漏斗分析</div>
                    <object class="chart-svg" data="06_sales_funnel.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>⚡ 实时监控图表</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">系统性能实时监控</div>
                    <object class="chart-svg" data="07_performance_monitoring.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">实时数据流监控</div>
                    <object class="chart-svg" data="08_realtime_stream.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🧮 高级分析图表</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">预测分析与置信区间</div>
                    <object class="chart-svg" data="09_prediction_analysis.svg" type="image/svg+xml">SVG不支持</object>
                </div>
                <div class="chart-item">
                    <div class="chart-title">异常检测分析</div>
                    <object class="chart-svg" data="10_anomaly_detection.svg" type="image/svg+xml">SVG不支持</object>
                </div>
            </div>
        </div>

        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
            <h2>🎉 专业曲线图功能总结</h2>
            <p>ECharts-rs 专业级数据可视化能力：</p>
            <ul style="text-align: left; max-width: 800px; margin: 0 auto;">
                <li>✅ <strong>金融分析</strong> - K线图、股票对比、技术指标</li>
                <li>✅ <strong>商业智能</strong> - KPI仪表板、销售漏斗、用户行为</li>
                <li>✅ <strong>实时监控</strong> - 性能监控、数据流、系统状态</li>
                <li>✅ <strong>高级分析</strong> - 预测分析、异常检测、置信区间</li>
                <li>✅ <strong>专业渲染</strong> - 高质量SVG、动画效果、交互设计</li>
                <li>✅ <strong>数据处理</strong> - 移动平均、RSI、MACD等技术指标</li>
                <li>✅ <strong>可视化设计</strong> - 专业配色、渐变效果、阴影滤镜</li>
                <li>✅ <strong>响应式布局</strong> - 适配不同屏幕尺寸和设备</li>
            </ul>
        </div>
    </div>
</body>
</html>"#;

    fs::write(format!("{}/professional_demo.html", output_dir), html_content)?;
    Ok(())
}
