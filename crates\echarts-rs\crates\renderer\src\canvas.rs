// 从temp/canvas.rs复制基本内容
pub use crate::*;

/// 提供完整的错误处理、批量操作、性能控制和扩展性
pub trait Canvas {
    /// 错误类型
    type Error: std::error::Error + Send + Sync + 'static;

    /// 后端特定的扩展数据
    type Extension: Default;

    // === 基础绘制方法 需要手动实现 ===

    /// 绘制线条
    fn draw_line(&mut self, from: Point, to: Point, style: &LineStyle);

    /// 绘制矩形
    fn draw_rect(&mut self, bounds: Bounds, style: &FillStyle);

    /// 绘制圆形
    fn draw_circle(&mut self, center: Point, radius: f64, style: &FillStyle);

    /// 绘制文本
    fn draw_text(&mut self, text: &str, position: Point, style: &TextStyle);

    /// 绘制路径
    fn draw_path(&mut self, path: &Path, style: &LineStyle);

    /// 填充路径
    fn fill_path(&mut self, path: &Path, style: &FillStyle);

    // === 高级绘制方法（默认实现） ===

    /// 绘制椭圆
    fn draw_ellipse(&mut self, center: Point, width: f64, height: f64, style: &FillStyle) {
        let path = Path::ellipse(center, width, height);
        self.fill_path(&path, style);
    }

    /// 绘制圆角矩形
    fn draw_rounded_rect(&mut self, bounds: Bounds, radius: f64, style: &FillStyle) {
        let path = Path::rounded_rect(bounds, radius);
        self.fill_path(&path, style);
    }

    /// 绘制多边形
    fn draw_polygon(&mut self, points: &[Point], style: &FillStyle) {
        if points.is_empty() {
            return;
        }

        let mut path = Path::new().move_to(points[0]);
        for &point in &points[1..] {
            path = path.line_to(point);
        }
        path = path.close();

        self.fill_path(&path, style);
    }

    /// 绘制折线
    fn draw_polyline(&mut self, points: &[Point], style: &LineStyle) {
        if points.len() < 2 {
            return;
        }

        let mut path = Path::new().move_to(points[0]);
        for &point in &points[1..] {
            path = path.line_to(point);
        }

        self.draw_path(&path, style);
    }

    // === 便利方法（默认实现） ===

    /// 绘制带描边的矩形
    fn draw_rect_with_stroke(&mut self, bounds: Bounds, fill_style: &FillStyle, stroke_style: &LineStyle) {
        self.draw_rect(bounds, fill_style);
        let path = Path::rect(bounds);
        self.draw_path(&path, stroke_style);
    }

    /// 绘制带描边的圆形
    fn draw_circle_with_stroke(&mut self, center: Point, radius: f64, fill_style: &FillStyle, stroke_style: &LineStyle) {
        self.draw_circle(center, radius, fill_style);
        let path = Path::circle(center, radius);
        self.draw_path(&path, stroke_style);
    }

    /// 绘制简单线条
    fn draw_simple_line(&mut self, from: Point, to: Point, color: Color, width: f64) {
        let style = LineStyle {
            color,
            width,
            ..Default::default()
        };
        self.draw_line(from, to, &style);
    }

    /// 填充矩形
    fn fill_rect(&mut self, bounds: Bounds, color: Color) {
        let style = FillStyle {
            fill: Fill::Solid(color),
            opacity: 1.0,
        };
        self.draw_rect(bounds, &style);
    }

    /// 填充圆形
    fn fill_circle(&mut self, center: Point, radius: f64, color: Color) {
        let style = FillStyle {
            fill: Fill::Solid(color),
            opacity: 1.0,
        };
        self.draw_circle(center, radius, &style);
    }

    /// 绘制简单文本
    fn draw_simple_text(&mut self, text: &str, position: Point, font_size: f64, color: Color) {
        let style = TextStyle {
            font_family: "Arial".to_string(),
            font_size,
            color,
            font_weight: FontWeight::Normal,
            font_style: FontStyle::Normal,
            align: TextAlign::Left,
            baseline: TextBaseline::Top,
            opacity: 1.0,
            rotation: 0.0,
            letter_spacing: 0.0,
            line_height: 1.2,
        };
        self.draw_text(text, position, &style);
    }

    // === 状态管理（默认实现） ===

    /// 保存当前状态
    fn save(&mut self) {
        // 默认实现什么都不做
    }

    /// 恢复之前保存的状态
    fn restore(&mut self) {
        // 默认实现什么都不做
    }

    /// 设置裁剪区域
    fn set_clip(&mut self, _bounds: Bounds) {
        // 默认实现什么都不做
    }

    /// 清除裁剪区域
    fn clear_clip(&mut self) {
        // 默认实现什么都不做
    }

    /// 应用变换
    fn transform(&mut self, _transform: &Transform) {
        // 默认实现什么都不做
    }

    /// 平移坐标系
    fn translate(&mut self, dx: f64, dy: f64) {
        let transform = Transform::translate(dx, dy);
        self.transform(&transform);
    }

    /// 缩放坐标系
    fn scale(&mut self, sx: f64, sy: f64) {
        let transform = Transform::scale(sx, sy);
        self.transform(&transform);
    }

    /// 旋转坐标系
    fn rotate(&mut self, angle: f64) {
        let transform = Transform::rotate(angle);
        self.transform(&transform);
    }

    // === 控制方法（默认实现） ===

    /// 清空画布
    fn clear(&mut self) {
        // 默认实现什么都不做
    }

    /// 刷新渲染
    fn flush(&mut self) {
        // 默认实现什么都不做
    }

    /// 获取画布边界
    fn bounds(&self) -> Bounds {
        // 默认返回一个大的边界
        Bounds::new(0.0, 0.0, 1000.0, 1000.0)
    }

    /// 设置画布边界
    fn set_bounds(&mut self, _bounds: Bounds) {
        // 默认实现什么都不做
    }
}