//! Tooltip component implementation

use crate::{Component, Position, Renderable, Themeable};
use echarts_core::*;
use echarts_charts::RenderContext;
use echarts_themes::Theme;
use serde::{Deserialize, Serialize};

/// Tooltip component for displaying data information on hover
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Tooltip {
    /// Whether the tooltip is visible
    pub visible: bool,

    /// Tooltip trigger type
    pub trigger: TooltipTrigger,

    /// Tooltip position
    pub position: Position,

    /// Background color
    pub background_color: Color,

    /// Border color
    pub border_color: Color,

    /// Border width
    pub border_width: f64,

    /// Text style
    pub text_style: TextStyle,

    /// Padding
    pub padding: [f64; 4], // top, right, bottom, left

    /// Border radius
    pub border_radius: f64,

    /// Custom formatter function
    pub formatter: Option<String>,
}

/// Tooltip trigger types
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum TooltipTrigger {
    /// Trigger on item hover
    Item,
    /// Trigger on axis hover
    Axis,
    /// No trigger
    None,
}

impl Default for Tooltip {
    fn default() -> Self {
        Tooltip {
            visible: true,
            trigger: TooltipTrigger::Item,
            position: Position::Absolute { x: 0.0, y: 0.0 },
            background_color: Color::from_rgba(0, 0, 0, 204), // 0.8 * 255 = 204
            border_color: Color::from_rgba(255, 255, 255, 77), // 0.3 * 255 = 77
            border_width: 1.0,
            text_style: TextStyle {
                color: Color::WHITE,
                ..Default::default()
            },
            padding: [8.0, 12.0, 8.0, 12.0],
            border_radius: 4.0,
            formatter: None,
        }
    }
}

impl Tooltip {
    /// Create a new tooltip
    pub fn new() -> Self {
        Self::default()
    }

    /// Set the trigger type
    pub fn trigger(mut self, trigger: TooltipTrigger) -> Self {
        self.trigger = trigger;
        self
    }

    /// Set the position
    pub fn position(mut self, position: Position) -> Self {
        self.position = position;
        self
    }

    /// Set the background color
    pub fn background_color(mut self, color: Color) -> Self {
        self.background_color = color;
        self
    }

    /// Set the formatter
    pub fn formatter<S: Into<String>>(mut self, formatter: S) -> Self {
        self.formatter = Some(formatter.into());
        self
    }

    /// Set visibility
    pub fn visible(mut self, visible: bool) -> Self {
        self.visible = visible;
        self
    }
}

impl Component for Tooltip {
    fn component_type(&self) -> &'static str {
        "tooltip"
    }

    fn is_visible(&self) -> bool {
        self.visible
    }

    fn set_visible(&mut self, visible: bool) {
        self.visible = visible;
    }
}

impl Renderable for Tooltip {
    fn render<R: ChartRenderer>(&self, ctx: &mut RenderContext<R>, bounds: Bounds) -> Result<()> {
        if !self.visible {
            return Ok(());
        }

        // Calculate tooltip bounds
        let tooltip_bounds = self.calculate_tooltip_bounds(bounds);

        // Draw background with border radius
        self.draw_background(ctx, tooltip_bounds);

        // Draw border
        if self.border_width > 0.0 {
            ctx.set_stroke(self.border_color, self.border_width);
            ctx.stroke_rect(tooltip_bounds);
        }

        // Draw content
        self.draw_content(ctx, tooltip_bounds)?;

        Ok(())
    }
}

impl Themeable for Tooltip {
    fn apply_theme(&mut self, theme: &Theme) {
        // Apply theme colors if not explicitly set
        if self.background_color == Color::from_rgba(0, 0, 0, 204) {
            self.background_color = theme.background_color.with_alpha(0.9);
        }
    }
}

impl Tooltip {
    /// Calculate tooltip bounds based on position and content
    fn calculate_tooltip_bounds(&self, _container_bounds: Bounds) -> Bounds {
        // For now, return a fixed size tooltip
        // TODO: Calculate based on content and position
        let width = 150.0;
        let height = 60.0;

        // Position at mouse cursor (would be passed in real implementation)
        Bounds::new(100.0, 100.0, width, height)
    }

    /// Draw tooltip background
    fn draw_background(&self, ctx: &mut RenderContext, bounds: Bounds) {
        // For now, just draw a simple rectangle
        // TODO: Implement rounded corners based on border_radius
        ctx.set_fill_color(self.background_color);
        ctx.fill_rect(bounds);
    }

    /// Draw tooltip content
    fn draw_content(&self, ctx: &mut RenderContext, bounds: Bounds) -> Result<()> {
        // Draw sample tooltip text
        let text_x = bounds.origin.x + self.padding[3]; // left padding
        let text_y = bounds.origin.y + self.padding[0] + self.text_style.font_size; // top padding + font size
        let text_pos = Point::new(text_x, text_y);

        let content = self
            .formatter
            .as_ref()
            .map(|f| f.clone())
            .unwrap_or_else(|| "Tooltip Content".to_string());

        ctx.draw_text(content, text_pos, self.text_style.clone());

        Ok(())
    }

    /// Show tooltip at specific position with content
    pub fn show_at(&mut self, _position: Point, _content: String) {
        self.visible = true;
        // TODO: Store position and content for rendering
    }

    /// Hide tooltip
    pub fn hide(&mut self) {
        self.visible = false;
    }
}
