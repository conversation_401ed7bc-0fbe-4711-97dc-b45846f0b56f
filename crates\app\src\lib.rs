//  #[macro_use]
// extern crate rust_i18n;
// rust_i18n::i18n!("locales", fallback = "en");

mod button_story;
// mod chart_story;  // 暂时注释掉，等chart库修复后再启用
mod echarts_story;
// mod chart_story2;
mod assets;
mod device;
mod fixtures;
mod logging;
mod panel;
mod status_bar;
mod story_container;
mod story_workspace;
mod title_bar;
mod toggle_panel_action;

use tracing_appender::non_blocking::WorkerGuard;

use std::collections::HashSet;

pub use button_story::ButtonStory;
// pub use chart_story::ChartStory;  // 暂时注释掉，等chart库修复后再启用
pub use echarts_story::EchartsStory;
pub use status_bar::AppStatusBar;
pub use title_bar::AppTitleBar;

pub use assets::Assets;
pub use fixtures::*;
pub use logging::*;
pub use panel::*;
pub use story_container::*;
pub use story_workspace::*;
pub use toggle_panel_action::*;

pub use device::*;

mod story_trait;
pub use story_trait::Story;

use gpui::{
    actions, div, prelude::FluentBuilder as _, px, rems, size, Action, AnyElement, AnyView, App,
    AppContext, BackgroundExecutor, Bounds, Context, Div, Entity, Focusable, Global,
    InteractiveElement, IntoElement, KeyBinding, Menu, MenuItem, ParentElement, Render, RenderOnce,
    SharedString, StatefulInteractiveElement, Styled, Window, WindowBounds, WindowKind,
    WindowOptions,
};

use serde::Deserialize;
use tracing::Level;
use tracing_subscriber::util::SubscriberInitExt as _;

use gpui_component::{
    context_menu::ContextMenuExt,
    dock::{register_panel, Panel, PanelEvent, PanelInfo},
    h_flex,
    notification::Notification,
    scroll::ScrollbarShow,
    v_flex, ActiveTheme, ContextModal, IconName, Root, TitleBar,
};

use std::ops::Deref;
rust_i18n::i18n!("locales", fallback = "en");

// #[inline]
// pub fn locale() -> impl Deref<Target = str> {
//     rust_i18n::locale()
// }

// #[inline]
// pub fn set_locale(locale: &str) {
//     rust_i18n::set_locale(locale)
// }

#[derive(Action, Clone, PartialEq, Eq)]
#[action(namespace = story, no_json)]
pub struct DeviceStatusWrapper(device::DeviceStatus);

#[derive(Action, Clone, PartialEq, Eq, Deserialize)]
#[action(namespace = story, no_json)]
pub struct SelectLocale(SharedString);

#[derive(Action, Clone, PartialEq, Eq, Deserialize)]
#[action(namespace = story, no_json)]
pub struct SelectFont(pub usize);

#[derive(Action, Clone, PartialEq, Eq, Deserialize)]
#[action(namespace = story, no_json)]
pub struct SelectRadius(pub usize);

#[derive(Action, Clone, PartialEq, Eq, Deserialize)]
#[action(namespace = story, no_json)]
pub struct SelectScrollbarShow(pub ScrollbarShow);

actions!(story, [Quit, Open, CloseWindow, ToggleSearch]);

const PANEL_NAME: &str = "StoryContainer";

pub struct AppState {
    pub invisible_panels: Entity<HashSet<SharedString>>,
    pub log_guards: Vec<WorkerGuard>,
    pub bg_exec: BackgroundExecutor,
}
impl AppState {
    fn init(cx: &mut App) {
        let log_config = LogConfig {
            log_dir: "logs".into(),
            max_files: 7,
            terminal_level: Level::WARN,
            file_level: Level::WARN,
            high_freq_targets: vec!["high_freq".to_string()],
        };

        let log_guards = init_logging(log_config);
        let state = Self {
            invisible_panels: cx.new(|_| HashSet::new()),
            log_guards,
            bg_exec: cx.background_executor().clone(),
        };
        cx.set_global::<AppState>(state);
    }

    pub fn global(cx: &App) -> &Self {
        cx.global::<Self>()
    }

    pub fn global_mut(cx: &mut App) -> &mut Self {
        cx.global_mut::<Self>()
    }
}

pub fn create_new_window<F, E>(title: &str, crate_view_fn: F, cx: &mut App)
where
    E: Into<AnyView>,
    F: FnOnce(&mut Window, &mut App) -> E + Send + 'static,
{
    let mut window_size = size(px(1600.0), px(1200.0));
    if let Some(display) = cx.primary_display() {
        let display_size = display.bounds().size;
        window_size.width = window_size.width.min(display_size.width * 0.85);
        window_size.height = window_size.height.min(display_size.height * 0.85);
    }
    let window_bounds = Bounds::centered(None, window_size, cx);
    let title = SharedString::from(title.to_string());

    cx.spawn(async move |cx| {
        let options = WindowOptions {
            window_bounds: Some(WindowBounds::Windowed(window_bounds)),
            titlebar: Some(TitleBar::title_bar_options()),
            window_min_size: Some(gpui::Size {
                width: px(640.),
                height: px(480.),
            }),
            kind: WindowKind::Normal,
            #[cfg(target_os = "linux")]
            window_background: gpui::WindowBackgroundAppearance::Transparent,
            #[cfg(target_os = "linux")]
            window_decorations: Some(gpui::WindowDecorations::Client),
            ..Default::default()
        };

        let window = cx
            .open_window(options, |window, cx| {
                let view = crate_view_fn(window, cx);
                let root = cx.new(|cx| StoryRoot::new(title.clone(), view, window, cx));

                cx.new(|cx| Root::new(root.into(), window, cx))
            })
            .expect("failed to open window");

        window
            .update(cx, |_, window, _| {
                window.activate_window();
                window.set_window_title(&title);
            })
            .expect("failed to update window");

        Ok::<_, anyhow::Error>(())
    })
    .detach();
}

struct StoryRoot {
    title_bar: Entity<AppTitleBar>,
    view: AnyView,
}

impl StoryRoot {
    pub fn new(
        title: impl Into<SharedString>,
        view: impl Into<AnyView>,
        window: &mut Window,
        cx: &mut Context<Self>,
    ) -> Self {
        let title_bar = cx.new(|cx| AppTitleBar::new(title, window, cx));
        Self {
            title_bar,
            view: view.into(),
        }
    }
}

impl Render for StoryRoot {
    fn render(&mut self, window: &mut Window, cx: &mut Context<Self>) -> impl IntoElement {
        let drawer_layer = Root::render_drawer_layer(window, cx);
        let modal_layer = Root::render_modal_layer(window, cx);
        let notification_layer = Root::render_notification_layer(window, cx);

        div()
            .size_full()
            .child(
                v_flex()
                    .size_full()
                    .child(self.title_bar.clone())
                    .child(div().flex_1().overflow_hidden().child(self.view.clone())),
            )
            .children(drawer_layer)
            .children(modal_layer)
            .child(div().absolute().top_8().children(notification_layer))
    }
}

impl Global for AppState {}
impl Global for LogConfig {}
impl Global for LogSettings {}

pub fn fscdaq_init(cx: &mut App) {
    // tracing::logging::init_logging();
    // #[allow(unused_imports)]
    // use error_handling::TracedError;
    // tracing_subscriber::registry()
    //     .with(tracing_subscriber::fmt::layer())
    //     .with(
    //         tracing_subscriber::EnvFilter::from_default_env()
    //             .add_directive("gpui_component=trace".parse().unwrap()),
    //     )
    //     .init();

    // cx.set_global(log_guards);

    gpui_component::init(cx);
    AppState::init(cx);
    // input_story::init(cx);
    // number_input_story::init(cx);
    // textarea_story::init(cx);
    // dropdown_story::init(cx);
    // popover_story::init(cx);
    // menu_story::init(cx);
    // webview_story::init(cx);
    // tooltip_story::init(cx);
    // otp_input_story::init(cx);

    let http_client = std::sync::Arc::new(
        reqwest_client::ReqwestClient::user_agent("gpui-component/story").unwrap(),
    );
    cx.set_http_client(http_client);

    cx.bind_keys([
        KeyBinding::new("/", ToggleSearch, None),
        KeyBinding::new("cmd-q", Quit, None),
    ]);

    cx.on_action(|_: &Quit, cx: &mut App| {
        cx.quit();
    });

    register_panel(cx, PANEL_NAME, |_, _, info, window, cx| {
        let story_state = match info {
            PanelInfo::Panel(value) => StoryState::from_value(value.clone()),
            _ => {
                unreachable!("Invalid PanelInfo: {:?}", info)
            }
        };

        let view = cx.new(|cx| {
            let (title, description, closable, zoomable, story, on_active) =
                story_state.to_story(window, cx);
            let mut container = StoryContainer::new(window, cx)
                .story(story, story_state.story_klass)
                .on_active(on_active);

            cx.on_focus_in(
                &container.focus_handle,
                window,
                |this: &mut StoryContainer, _, _| {
                    println!("StoryContainer focus in: {}", this.name);
                },
            )
            .detach();

            container.name = title.into();
            container.description = description.into();
            container.closable = closable;
            container.zoomable = zoomable;
            container
        });
        Box::new(view)
    });

    use gpui_component::input::{Copy, Cut, Paste, Redo, Undo};
    cx.set_menus(vec![
        Menu {
            name: "GPUI App".into(),
            items: vec![MenuItem::action("Quit", Quit)],
        },
        Menu {
            name: "Edit".into(),
            items: vec![
                MenuItem::os_action("Undo", Undo, gpui::OsAction::Undo),
                MenuItem::os_action("Redo", Redo, gpui::OsAction::Redo),
                MenuItem::separator(),
                MenuItem::os_action("Cut", Cut, gpui::OsAction::Cut),
                MenuItem::os_action("Copy", Copy, gpui::OsAction::Copy),
                MenuItem::os_action("Paste", Paste, gpui::OsAction::Paste),
            ],
        },
        Menu {
            name: "Window".into(),
            items: vec![],
        },
    ]);
    cx.activate(true);
}

actions!(story, [ShowPanelInfo]);

#[derive(IntoElement)]
struct StorySection {
    base: Div,
    title: AnyElement,
    children: Vec<AnyElement>,
}

impl StorySection {
    #[allow(unused)]
    fn max_w_md(mut self) -> Self {
        self.base = self.base.max_w(rems(48.));
        self
    }

    #[allow(unused)]
    fn max_w_lg(mut self) -> Self {
        self.base = self.base.max_w(rems(64.));
        self
    }

    #[allow(unused)]
    fn max_w_xl(mut self) -> Self {
        self.base = self.base.max_w(rems(80.));
        self
    }

    #[allow(unused)]
    fn max_w_2xl(mut self) -> Self {
        self.base = self.base.max_w(rems(96.));
        self
    }
}

impl ParentElement for StorySection {
    fn extend(&mut self, elements: impl IntoIterator<Item = AnyElement>) {
        self.children.extend(elements);
    }
}

impl Styled for StorySection {
    fn style(&mut self) -> &mut gpui::StyleRefinement {
        self.base.style()
    }
}

impl RenderOnce for StorySection {
    fn render(self, _: &mut Window, cx: &mut App) -> impl IntoElement {
        v_flex()
            .gap_2()
            .mb_5()
            .w_full()
            .child(
                h_flex()
                    .justify_between()
                    .w_full()
                    .gap_4()
                    .child(self.title),
            )
            .child(
                v_flex()
                    .p_4()
                    .overflow_x_hidden()
                    .border_1()
                    .border_color(cx.theme().border)
                    .rounded_lg()
                    .items_center()
                    .justify_center()
                    .child(self.base.children(self.children)),
            )
    }
}
impl ContextMenuExt for StorySection {}

pub(crate) fn section(title: impl IntoElement) -> StorySection {
    StorySection {
        title: title.into_any_element(),
        base: h_flex()
            .flex_wrap()
            .justify_center()
            .items_center()
            .w_full()
            .gap_4(),
        children: vec![],
    }
}
