[package]
name = "rust-echarts-charts-3d"
version = "0.1.0"
edition = "2021"
description = "3D chart implementations for ECharts-rs"
license = "MIT"
repository = "https://github.com/your-repo/echarts-rs"

[dependencies]
rust-echarts-core = { path = "../core" }
rust-echarts-charts = { path = "../charts" }
rust-echarts-renderer = { path = "../renderer" }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 3D math and graphics
nalgebra = "0.32"
glam = "0.24"

[dev-dependencies]
approx = "0.5"
