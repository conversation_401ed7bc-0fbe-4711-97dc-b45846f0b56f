//! Chart 构建器 - 使用真实的 charts 实现
//!
//! 这个模块提供了便捷的 API 来创建使用 charts crate 真实实现的图表

use crate::prelude::*;

/// 增强的图表构建器
/// 
/// 使用 charts crate 的真实 Series 实现，支持所有高级功能
#[derive(Debug)]
pub struct ChartBuilder {
    chart: Chart,
}

impl ChartBuilder {
    /// 创建新的图表构建器
    pub fn new() -> Self {
        Self {
            chart: Chart::new(),
        }
    }

    /// 设置图表标题
    pub fn title<S: Into<String>>(mut self, title: S) -> Self {
        self.chart = self.chart.title(title);
        self
    }

    /// 设置图表大小
    pub fn size(mut self, width: f64, height: f64) -> Self {
        self.chart = self.chart.size(width, height);
        self
    }

    /// 设置背景颜色
    pub fn background_color(mut self, color: Color) -> Self {
        self.chart = self.chart.background_color(color);
        self
    }

    /// 设置边距
    pub fn padding(mut self, top: f64, right: f64, bottom: f64, left: f64) -> Self {
        self.chart = self.chart.padding(top, right, bottom, left);
        self
    }

    // === 添加各种类型的 Series（使用真实实现）===

    /// 添加折线图系列（基础版本）
    pub fn add_line_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>) -> Self {
        let series = LineSeries::new(name).data(data);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加高级折线图系列（支持所有功能）
    pub fn add_advanced_line_series<S: Into<String>, F>(mut self, name: S, data: Vec<(f64, f64)>, config: F) -> Self 
    where 
        F: FnOnce(LineSeries) -> LineSeries
    {
        let series = LineSeries::new(name).data(data);
        let configured_series = config(series);
        self.chart = self.chart.add_series(Box::new(configured_series));
        self
    }

    /// 添加平滑折线图
    pub fn add_smooth_line_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, color: Color) -> Self {
        let series = LineSeries::new(name)
            .data(data)
            .color(color)
            .smooth(true)
            .smoothness(0.3)
            .show_symbols(true);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加面积图
    pub fn add_area_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, color: Color) -> Self {
        let series = LineSeries::new_area(name, color)
            .data(data)
            .area_opacity(0.3);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加阶梯线图
    pub fn add_step_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, step_type: StepType) -> Self {
        let series = LineSeries::new_step(name, step_type)
            .data(data)
            .color(Color::rgb(0.2, 0.6, 1.0));
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加柱状图系列（基础版本）
    pub fn add_bar_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>) -> Self {
        let series = BarSeries::new(name).data(data);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加高级柱状图系列（支持所有功能）
    pub fn add_advanced_bar_series<S: Into<String>, F>(mut self, name: S, data: Vec<(f64, f64)>, config: F) -> Self 
    where 
        F: FnOnce(BarSeries) -> BarSeries
    {
        let series = BarSeries::new(name).data(data);
        let configured_series = config(series);
        self.chart = self.chart.add_series(Box::new(configured_series));
        self
    }

    /// 添加带边框的柱状图
    pub fn add_bordered_bar_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, color: Color, border_color: Color) -> Self {
        let series = BarSeries::new(name)
            .data(data)
            .color(color)
            .border(true, border_color, 1.0)
            .bar_width(0.6);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加散点图系列（基础版本）
    pub fn add_scatter_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>) -> Self {
        let series = ScatterSeries::new(name).data(data);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加高级散点图系列（支持所有功能）
    pub fn add_advanced_scatter_series<S: Into<String>, F>(mut self, name: S, data: Vec<(f64, f64)>, config: F) -> Self 
    where 
        F: FnOnce(ScatterSeries) -> ScatterSeries
    {
        let series = ScatterSeries::new(name).data(data);
        let configured_series = config(series);
        self.chart = self.chart.add_series(Box::new(configured_series));
        self
    }

    /// 添加自定义符号的散点图
    pub fn add_symbol_scatter_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, symbol_type: SymbolType, size: f64, color: Color) -> Self {
        let series = ScatterSeries::new(name)
            .data(data)
            .color(color)
            .symbol_type(symbol_type)
            .symbol_size(size);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加任意 Series（完全的灵活性）
    pub fn add_series<S: Series + 'static>(mut self, series: S) -> Self {
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 构建最终的图表
    pub fn build(self) -> Chart {
        self.chart
    }

    /// 构建并渲染为 DrawCommand
    pub fn render(self) -> Result<Vec<DrawCommand>> {
        self.chart.render_to_commands()
    }
}

impl Default for ChartBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 便捷的图表创建函数
impl ChartBuilder {
    /// 创建折线图
    pub fn line_chart() -> Self {
        Self::new()
    }

    /// 创建柱状图
    pub fn bar_chart() -> Self {
        Self::new()
    }

    /// 创建散点图
    pub fn scatter_chart() -> Self {
        Self::new()
    }

    /// 创建混合图表（可包含多种类型的系列）
    pub fn mixed_chart() -> Self {
        Self::new()
    }

    /// 创建仪表板（多个子图）
    pub fn dashboard() -> Self {
        Self::new().size(1200.0, 800.0)
    }
}

/// 快速创建图表的便捷函数
pub fn line_chart<S: Into<String>>(name: S, data: Vec<(f64, f64)>) -> Chart {
    ChartBuilder::line_chart()
        .add_line_series(name, data)
        .build()
}

pub fn bar_chart<S: Into<String>>(name: S, data: Vec<(f64, f64)>) -> Chart {
    ChartBuilder::bar_chart()
        .add_bar_series(name, data)
        .build()
}

pub fn scatter_chart<S: Into<String>>(name: S, data: Vec<(f64, f64)>) -> Chart {
    ChartBuilder::scatter_chart()
        .add_scatter_series(name, data)
        .build()
}

/// 创建多系列图表的便捷函数
pub fn multi_line_chart(series_data: Vec<(&str, Vec<(f64, f64)>)>) -> Chart {
    let mut builder = ChartBuilder::line_chart();
    
    for (name, data) in series_data {
        builder = builder.add_line_series(name, data);
    }
    
    builder.build()
}

pub fn multi_bar_chart(series_data: Vec<(&str, Vec<(f64, f64)>)>) -> Chart {
    let mut builder = ChartBuilder::bar_chart();
    
    for (name, data) in series_data {
        builder = builder.add_bar_series(name, data);
    }
    
    builder.build()
}

/// 创建混合图表的便捷函数
pub fn mixed_chart() -> ChartBuilder {
    ChartBuilder::mixed_chart()
}
