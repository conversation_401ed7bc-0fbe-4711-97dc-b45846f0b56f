//! Chart 构建器 - 使用真实的 charts 实现
//!
//! 这个模块提供了便捷的 API 来创建使用 charts crate 真实实现的图表

use crate::prelude::*;
use echarts_components::{Axis, Grid, Legend, AxisPosition, AxisType};

/// 增强的图表结构，包含组件信息
#[derive(Debug)]
pub struct EnhancedChart {
    /// 基础图表
    pub chart: Chart,
    /// X轴配置
    pub x_axis: Option<Axis>,
    /// Y轴配置
    pub y_axis: Option<Axis>,
    /// 网格配置
    pub grid: Option<Grid>,
    /// 图例配置
    pub legend: Option<Legend>,
}

impl EnhancedChart {
    /// 渲染增强图表为 DrawCommand 列表
    ///
    /// 这个方法会处理所有组件并生成完整的绘制命令
    pub fn render_to_commands(&self) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        // 首先渲染基础图表
        let chart_commands = self.chart.render_to_commands()?;
        commands.extend(chart_commands);

        // TODO: 在下一个任务中实现组件渲染
        // 这里会添加坐标轴、网格、图例的渲染逻辑

        Ok(commands)
    }
}

/// 增强的图表构建器
///
/// 使用 charts crate 的真实 Series 实现，支持所有高级功能
#[derive(Debug)]
pub struct ChartBuilder {
    chart: Chart,
    /// X轴配置
    x_axis: Option<Axis>,
    /// Y轴配置
    y_axis: Option<Axis>,
    /// 网格配置
    grid: Option<Grid>,
    /// 图例配置
    legend: Option<Legend>,
}

impl ChartBuilder {
    /// 创建新的图表构建器
    pub fn new() -> Self {
        Self {
            chart: Chart::new(),
            x_axis: None,
            y_axis: None,
            grid: None,
            legend: None,
        }
    }

    /// 设置图表标题
    pub fn title<S: Into<String>>(mut self, title: S) -> Self {
        self.chart = self.chart.title(title);
        self
    }

    /// 设置图表大小
    pub fn size(mut self, width: f64, height: f64) -> Self {
        self.chart = self.chart.size(width, height);
        self
    }

    /// 设置背景颜色
    pub fn background_color(mut self, color: Color) -> Self {
        self.chart = self.chart.background_color(color);
        self
    }

    /// 设置边距
    pub fn padding(mut self, top: f64, right: f64, bottom: f64, left: f64) -> Self {
        self.chart = self.chart.padding(top, right, bottom, left);
        self
    }

    // === 组件配置方法 ===

    /// 配置X轴
    pub fn x_axis(mut self, axis: Axis) -> Self {
        self.x_axis = Some(axis);
        self
    }

    /// 配置Y轴
    pub fn y_axis(mut self, axis: Axis) -> Self {
        self.y_axis = Some(axis);
        self
    }

    /// 使用默认的数值X轴
    pub fn default_x_axis(mut self, min: Option<f64>, max: Option<f64>) -> Self {
        let mut axis = Axis::value().position(AxisPosition::Bottom);
        axis.min = min;
        axis.max = max;
        axis.show_line = true;
        axis.show_ticks = true;
        axis.show_labels = true;
        self.x_axis = Some(axis);
        self
    }

    /// 使用默认的数值Y轴
    pub fn default_y_axis(mut self, min: Option<f64>, max: Option<f64>) -> Self {
        let mut axis = Axis::value().position(AxisPosition::Left);
        axis.min = min;
        axis.max = max;
        axis.show_line = true;
        axis.show_ticks = true;
        axis.show_labels = true;
        self.y_axis = Some(axis);
        self
    }

    /// 使用分类X轴
    pub fn category_x_axis(mut self, categories: Vec<String>) -> Self {
        let mut axis = Axis::category().position(AxisPosition::Bottom);
        axis.categories = categories;
        axis.show_line = true;
        axis.show_ticks = true;
        axis.show_labels = true;
        self.x_axis = Some(axis);
        self
    }

    /// 配置网格
    pub fn grid(mut self, grid: Grid) -> Self {
        self.grid = Some(grid);
        self
    }

    /// 使用默认网格（显示网格线）
    pub fn default_grid(mut self) -> Self {
        let mut grid = Grid::default();
        grid.show_grid_lines = true;
        grid.bounds = [80.0, 60.0, 40.0, 80.0]; // left, top, right, bottom margins
        self.grid = Some(grid);
        self
    }

    /// 使用自定义边距的网格
    pub fn grid_with_margins(mut self, left: f64, top: f64, right: f64, bottom: f64) -> Self {
        let mut grid = Grid::default();
        grid.show_grid_lines = true;
        grid.bounds = [left, top, right, bottom];
        self.grid = Some(grid);
        self
    }

    /// 配置图例
    pub fn legend(mut self, legend: Legend) -> Self {
        self.legend = Some(legend);
        self
    }

    /// 使用默认图例
    pub fn default_legend(mut self) -> Self {
        let legend = Legend::default();
        self.legend = Some(legend);
        self
    }

    // === 添加各种类型的 Series（使用真实实现）===

    /// 添加折线图系列（基础版本）
    pub fn add_line_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>) -> Self {
        let series = LineSeries::new(name).data(data);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加高级折线图系列（支持所有功能）
    pub fn add_advanced_line_series<S: Into<String>, F>(mut self, name: S, data: Vec<(f64, f64)>, config: F) -> Self 
    where 
        F: FnOnce(LineSeries) -> LineSeries
    {
        let series = LineSeries::new(name).data(data);
        let configured_series = config(series);
        self.chart = self.chart.add_series(Box::new(configured_series));
        self
    }

    /// 添加平滑折线图
    pub fn add_smooth_line_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, color: Color) -> Self {
        let series = LineSeries::new(name)
            .data(data)
            .color(color)
            .smooth(true)
            .smoothness(0.3)
            .show_symbols(true);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加面积图
    pub fn add_area_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, color: Color) -> Self {
        let series = LineSeries::new_area(name, color)
            .data(data)
            .area_opacity(0.3);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加阶梯线图
    pub fn add_step_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, step_type: StepType) -> Self {
        let series = LineSeries::new_step(name, step_type)
            .data(data)
            .color(Color::rgb(0.2, 0.6, 1.0));
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加柱状图系列（基础版本）
    pub fn add_bar_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>) -> Self {
        let series = BarSeries::new(name).data(data);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加高级柱状图系列（支持所有功能）
    pub fn add_advanced_bar_series<S: Into<String>, F>(mut self, name: S, data: Vec<(f64, f64)>, config: F) -> Self 
    where 
        F: FnOnce(BarSeries) -> BarSeries
    {
        let series = BarSeries::new(name).data(data);
        let configured_series = config(series);
        self.chart = self.chart.add_series(Box::new(configured_series));
        self
    }

    /// 添加带边框的柱状图
    pub fn add_bordered_bar_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, color: Color, border_color: Color) -> Self {
        let series = BarSeries::new(name)
            .data(data)
            .color(color)
            .border(true, border_color, 1.0)
            .bar_width(0.6);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加散点图系列（基础版本）
    pub fn add_scatter_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>) -> Self {
        let series = ScatterSeries::new(name).data(data);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加高级散点图系列（支持所有功能）
    pub fn add_advanced_scatter_series<S: Into<String>, F>(mut self, name: S, data: Vec<(f64, f64)>, config: F) -> Self 
    where 
        F: FnOnce(ScatterSeries) -> ScatterSeries
    {
        let series = ScatterSeries::new(name).data(data);
        let configured_series = config(series);
        self.chart = self.chart.add_series(Box::new(configured_series));
        self
    }

    /// 添加自定义符号的散点图
    pub fn add_symbol_scatter_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, symbol_type: SymbolType, size: f64, color: Color) -> Self {
        let series = ScatterSeries::new(name)
            .data(data)
            .color(color)
            .symbol_type(symbol_type)
            .symbol_size(size);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加任意 Series（完全的灵活性）
    pub fn add_series<S: Series + 'static>(mut self, series: S) -> Self {
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 构建最终的图表
    ///
    /// 注意：当前版本的 Chart 结构体不直接支持组件，
    /// 组件信息会在渲染时通过 ChartBuilder 的扩展渲染方法处理
    pub fn build(self) -> Chart {
        self.chart
    }

    /// 构建增强的图表（包含组件信息）
    ///
    /// 返回一个包含所有组件配置的 EnhancedChart
    pub fn build_enhanced(self) -> EnhancedChart {
        EnhancedChart {
            chart: self.chart,
            x_axis: self.x_axis,
            y_axis: self.y_axis,
            grid: self.grid,
            legend: self.legend,
        }
    }

    /// 构建并渲染为 DrawCommand
    pub fn render(self) -> Result<Vec<DrawCommand>> {
        self.chart.render_to_commands()
    }
}

impl Default for ChartBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 便捷的图表创建函数
impl ChartBuilder {
    /// 创建折线图
    pub fn line_chart() -> Self {
        Self::new()
    }

    /// 创建柱状图
    pub fn bar_chart() -> Self {
        Self::new()
    }

    /// 创建散点图
    pub fn scatter_chart() -> Self {
        Self::new()
    }

    /// 创建混合图表（可包含多种类型的系列）
    pub fn mixed_chart() -> Self {
        Self::new()
    }

    /// 创建仪表板（多个子图）
    pub fn dashboard() -> Self {
        Self::new().size(1200.0, 800.0)
    }

    /// 创建带有默认坐标轴和网格的折线图
    pub fn line_chart_with_axes() -> Self {
        Self::line_chart()
            .default_x_axis(None, None)
            .default_y_axis(None, None)
            .default_grid()
    }

    /// 创建带有默认坐标轴和网格的柱状图
    pub fn bar_chart_with_axes() -> Self {
        Self::bar_chart()
            .default_x_axis(None, None)
            .default_y_axis(None, None)
            .default_grid()
    }

    /// 创建带有默认坐标轴和网格的散点图
    pub fn scatter_chart_with_axes() -> Self {
        Self::scatter_chart()
            .default_x_axis(None, None)
            .default_y_axis(None, None)
            .default_grid()
    }

    /// 创建完整配置的图表（包含所有组件）
    pub fn full_featured_chart() -> Self {
        Self::new()
            .default_x_axis(None, None)
            .default_y_axis(None, None)
            .default_grid()
            .default_legend()
    }
}

/// 快速创建图表的便捷函数
pub fn line_chart<S: Into<String>>(name: S, data: Vec<(f64, f64)>) -> Chart {
    ChartBuilder::line_chart()
        .add_line_series(name, data)
        .build()
}

pub fn bar_chart<S: Into<String>>(name: S, data: Vec<(f64, f64)>) -> Chart {
    ChartBuilder::bar_chart()
        .add_bar_series(name, data)
        .build()
}

pub fn scatter_chart<S: Into<String>>(name: S, data: Vec<(f64, f64)>) -> Chart {
    ChartBuilder::scatter_chart()
        .add_scatter_series(name, data)
        .build()
}

/// 创建多系列图表的便捷函数
pub fn multi_line_chart(series_data: Vec<(&str, Vec<(f64, f64)>)>) -> Chart {
    let mut builder = ChartBuilder::line_chart();
    
    for (name, data) in series_data {
        builder = builder.add_line_series(name, data);
    }
    
    builder.build()
}

pub fn multi_bar_chart(series_data: Vec<(&str, Vec<(f64, f64)>)>) -> Chart {
    let mut builder = ChartBuilder::bar_chart();
    
    for (name, data) in series_data {
        builder = builder.add_bar_series(name, data);
    }
    
    builder.build()
}

/// 创建混合图表的便捷函数
pub fn mixed_chart() -> ChartBuilder {
    ChartBuilder::mixed_chart()
}

/// 高级图表构建器
///
/// 提供更丰富的配置选项和自动化功能
#[derive(Debug)]
pub struct AdvancedChartBuilder {
    /// 基础构建器
    builder: ChartBuilder,
    /// 自动配置选项
    auto_config: AutoConfigOptions,
    /// 主题名称
    theme_name: Option<String>,
    /// 响应式配置
    responsive: bool,
}

/// 自动配置选项
#[derive(Debug, Clone)]
pub struct AutoConfigOptions {
    /// 自动计算坐标轴范围
    pub auto_axis_range: bool,
    /// 自动选择网格密度
    pub auto_grid_density: bool,
    /// 自动调整边距
    pub auto_margins: bool,
    /// 自动选择颜色
    pub auto_colors: bool,
    /// 自动优化性能
    pub auto_performance: bool,
}

impl Default for AutoConfigOptions {
    fn default() -> Self {
        Self {
            auto_axis_range: true,
            auto_grid_density: true,
            auto_margins: true,
            auto_colors: true,
            auto_performance: true,
        }
    }
}

impl AdvancedChartBuilder {
    /// 创建新的高级构建器
    pub fn new() -> Self {
        Self {
            builder: ChartBuilder::new(),
            auto_config: AutoConfigOptions::default(),
            theme_name: None,
            responsive: false,
        }
    }

    /// 从基础构建器创建
    pub fn from_builder(builder: ChartBuilder) -> Self {
        Self {
            builder,
            auto_config: AutoConfigOptions::default(),
            theme_name: None,
            responsive: false,
        }
    }

    /// 设置自动配置选项
    pub fn auto_config(mut self, config: AutoConfigOptions) -> Self {
        self.auto_config = config;
        self
    }

    /// 启用响应式
    pub fn responsive(mut self, responsive: bool) -> Self {
        self.responsive = responsive;
        self
    }

    /// 设置主题
    pub fn theme<S: Into<String>>(mut self, theme_name: S) -> Self {
        self.theme_name = Some(theme_name.into());
        self
    }

    /// 智能折线图配置
    pub fn smart_line_chart(mut self) -> Self {
        self.builder = self.builder
            .line_chart_with_axes()
            .grid_with_margins(100.0, 80.0, 50.0, 100.0);

        if self.auto_config.auto_colors {
            // 自动应用颜色方案
            self = self.apply_auto_colors();
        }

        self
    }

    /// 智能柱状图配置
    pub fn smart_bar_chart(mut self) -> Self {
        self.builder = self.builder
            .bar_chart_with_axes()
            .grid_with_margins(120.0, 80.0, 50.0, 100.0); // 柱状图需要更多左边距

        if self.auto_config.auto_colors {
            self = self.apply_auto_colors();
        }

        self
    }

    /// 智能散点图配置
    pub fn smart_scatter_chart(mut self) -> Self {
        self.builder = self.builder
            .scatter_chart_with_axes()
            .grid_with_margins(100.0, 80.0, 50.0, 100.0);

        if self.auto_config.auto_colors {
            self = self.apply_auto_colors();
        }

        self
    }

    /// 自动分析数据并配置坐标轴
    pub fn auto_analyze_data<T: Into<DataValue>>(mut self, data: &[(T, T)]) -> Self {
        if !self.auto_config.auto_axis_range {
            return self;
        }

        // 分析数据范围
        let (x_min, x_max, y_min, y_max) = self.analyze_data_range(data);

        // 自动配置坐标轴
        self.builder = self.builder
            .default_x_axis(Some(x_min), Some(x_max))
            .default_y_axis(Some(y_min), Some(y_max));

        self
    }

    /// 应用自动颜色方案
    fn apply_auto_colors(mut self) -> Self {
        // TODO: 实现智能颜色选择
        // 这里可以根据数据类型、系列数量等自动选择合适的颜色方案
        self
    }

    /// 分析数据范围
    fn analyze_data_range<T: Into<DataValue>>(&self, data: &[(T, T)]) -> (f64, f64, f64, f64) {
        // TODO: 实现数据范围分析
        // 这里应该分析数据的最小值、最大值，并添加适当的边距
        (0.0, 100.0, 0.0, 100.0) // 临时返回值
    }

    /// 构建最终的增强图表
    pub fn build(mut self) -> EnhancedChart {
        // 应用自动配置
        if self.auto_config.auto_margins {
            self = self.apply_auto_margins();
        }

        if self.auto_config.auto_grid_density {
            self = self.apply_auto_grid_density();
        }

        if self.auto_config.auto_performance {
            self = self.apply_performance_optimizations();
        }

        // 应用主题
        if let Some(theme_name) = &self.theme_name {
            self = self.apply_theme(theme_name);
        }

        self.builder.build_enhanced()
    }

    /// 应用自动边距
    fn apply_auto_margins(mut self) -> Self {
        // TODO: 根据标签长度、字体大小等自动计算边距
        self
    }

    /// 应用自动网格密度
    fn apply_auto_grid_density(mut self) -> Self {
        // TODO: 根据图表大小和数据密度自动调整网格
        self
    }

    /// 应用性能优化
    fn apply_performance_optimizations(mut self) -> Self {
        // TODO: 根据数据量自动启用性能优化
        self
    }

    /// 应用主题
    fn apply_theme(mut self, _theme_name: &str) -> Self {
        // TODO: 集成主题系统
        self
    }

    /// 获取基础构建器的引用
    pub fn builder(&self) -> &ChartBuilder {
        &self.builder
    }

    /// 获取基础构建器的可变引用
    pub fn builder_mut(&mut self) -> &mut ChartBuilder {
        &mut self.builder
    }
}

// === 高级构建器便捷函数 ===

/// 创建智能折线图
pub fn smart_line_chart() -> AdvancedChartBuilder {
    AdvancedChartBuilder::new().smart_line_chart()
}

/// 创建智能柱状图
pub fn smart_bar_chart() -> AdvancedChartBuilder {
    AdvancedChartBuilder::new().smart_bar_chart()
}

/// 创建智能散点图
pub fn smart_scatter_chart() -> AdvancedChartBuilder {
    AdvancedChartBuilder::new().smart_scatter_chart()
}

/// 创建响应式图表
pub fn responsive_chart() -> AdvancedChartBuilder {
    AdvancedChartBuilder::new().responsive(true)
}

/// 创建主题化图表
pub fn themed_chart<S: Into<String>>(theme: S) -> AdvancedChartBuilder {
    AdvancedChartBuilder::new().theme(theme)
}
