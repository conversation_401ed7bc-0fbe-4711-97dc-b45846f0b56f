//! Chart 构建器 - 使用真实的 charts 实现
//!
//! 这个模块提供了便捷的 API 来创建使用 charts crate 真实实现的图表

use crate::prelude::*;
use echarts_core::{LineStyle, TextStyle, draw_commands::RectStyle};

/// 简化的坐标轴配置
#[derive(Debug, Clone)]
pub struct SimpleAxis {
    pub visible: bool,
    pub position: AxisPosition,
    pub min: Option<f64>,
    pub max: Option<f64>,
    pub name: Option<String>,
    pub show_line: bool,
    pub show_ticks: bool,
    pub show_labels: bool,
    pub line_color: Color,
    pub label_color: Color,
    pub font_size: f64,
}

/// 坐标轴位置
#[derive(Debug, Clone)]
pub enum AxisPosition {
    Bottom,
    Top,
    Left,
    Right,
}

/// 简化的网格配置
#[derive(Debug, Clone)]
pub struct SimpleGrid {
    pub visible: bool,
    pub show_grid_lines: bool,
    pub grid_line_color: Color,
    pub grid_line_width: f64,
    pub background_color: Option<Color>,
}

/// 简化的图例配置
#[derive(Debug, <PERSON><PERSON>)]
pub struct SimpleLegend {
    pub visible: bool,
    pub position: LegendPosition,
}

/// 图例位置
#[derive(Debug, Clone)]
pub enum LegendPosition {
    Top,
    Bottom,
    Left,
    Right,
}

impl Default for SimpleAxis {
    fn default() -> Self {
        Self {
            visible: true,
            position: AxisPosition::Bottom,
            min: None,
            max: None,
            name: None,
            show_line: true,
            show_ticks: true,
            show_labels: true,
            line_color: Color::rgb(0.2, 0.2, 0.2),
            label_color: Color::rgb(0.4, 0.4, 0.4),
            font_size: 12.0,
        }
    }
}

impl Default for SimpleGrid {
    fn default() -> Self {
        Self {
            visible: true,
            show_grid_lines: true,
            grid_line_color: Color::rgba(0.7, 0.7, 0.7, 0.3),
            grid_line_width: 0.5,
            background_color: None,
        }
    }
}

impl Default for SimpleLegend {
    fn default() -> Self {
        Self {
            visible: true,
            position: LegendPosition::Bottom,
        }
    }
}

/// 边距配置
#[derive(Debug, Clone)]
struct Margins {
    pub top: f64,
    pub right: f64,
    pub bottom: f64,
    pub left: f64,
}

impl Default for Margins {
    fn default() -> Self {
        Self {
            top: 60.0,
            right: 40.0,
            bottom: 80.0,
            left: 80.0,
        }
    }
}


/// 增强的图表结构，包含组件信息
#[derive(Debug)]
pub struct EnhancedChart {
    /// 基础图表
    pub chart: Chart,
    /// X轴配置
    pub x_axis: Option<SimpleAxis>,
    /// Y轴配置
    pub y_axis: Option<SimpleAxis>,
    /// 网格配置
    pub grid: Option<SimpleGrid>,
    /// 图例配置
    pub legend: Option<SimpleLegend>,
}

impl EnhancedChart {
    /// 渲染增强图表为 DrawCommand 列表
    ///
    /// 这个方法会处理所有组件并生成完整的绘制命令
    pub fn render_to_commands(&self) -> Result<Vec<DrawCommand>> {
        let mut commands = Vec::new();

        // 首先渲染基础图表
        let chart_commands = self.chart.render_to_commands()?;
        commands.extend(chart_commands);

        // 渲染增强组件
        let plot_area = self.calculate_plot_area();

        // 渲染网格（在系列之前）
        if let Some(grid) = &self.grid {
            let grid_commands = self.render_grid(grid, plot_area);
            commands.extend(grid_commands);
        }

        // 渲染坐标轴
        if let Some(x_axis) = &self.x_axis {
            let axis_commands = self.render_axis(x_axis, plot_area);
            commands.extend(axis_commands);
        }

        if let Some(y_axis) = &self.y_axis {
            let axis_commands = self.render_axis(y_axis, plot_area);
            commands.extend(axis_commands);
        }

        // 渲染图例（在最后）
        if let Some(legend) = &self.legend {
            let legend_bounds = self.calculate_legend_area();
            let legend_commands = self.render_legend(legend, legend_bounds);
            commands.extend(legend_commands);
        }

        Ok(commands)
    }

    /// 计算绘图区域
    fn calculate_plot_area(&self) -> Bounds {
        let title_height = if self.chart.title.is_some() { 40.0 } else { 0.0 };
        let legend_height = if self.legend.is_some() { 60.0 } else { 0.0 };

        // 基础边距
        let left_margin = 100.0;   // 为Y轴标签留出空间
        let right_margin = 50.0;
        let top_margin = 80.0 + title_height;
        let bottom_margin = 100.0 + legend_height; // 为X轴标签和图例留出空间

        Bounds::new(
            left_margin,
            top_margin,
            self.chart.width - left_margin - right_margin,
            self.chart.height - top_margin - bottom_margin,
        )
    }

    /// 计算图例区域
    fn calculate_legend_area(&self) -> Bounds {
        let legend_height = 50.0;
        let margin = 10.0;

        Bounds::new(
            margin,
            self.chart.height - legend_height - margin,
            self.chart.width - 2.0 * margin,
            legend_height,
        )
    }

    /// 渲染网格
    fn render_grid(&self, grid: &SimpleGrid, bounds: Bounds) -> Vec<DrawCommand> {
        let mut commands = Vec::new();

        if !grid.visible {
            return commands;
        }

        // 渲染背景
        if let Some(bg_color) = grid.background_color {
            commands.push(DrawCommand::Rect {
                bounds,
                style: RectStyle {
                    fill: Some(bg_color),
                    stroke: None,
                    opacity: 1.0,
                    corner_radius: 0.0,
                },
            });
        }

        // 渲染网格线
        if grid.show_grid_lines {
            // 垂直网格线
            let v_lines = 10;
            for i in 0..=v_lines {
                let x = bounds.origin.x + (bounds.size.width * i as f64) / v_lines as f64;
                commands.push(DrawCommand::Line {
                    from: Point::new(x, bounds.origin.y),
                    to: Point::new(x, bounds.origin.y + bounds.size.height),
                    style: LineStyle {
                        color: grid.grid_line_color,
                        width: grid.grid_line_width,
                        ..Default::default()
                    },
                });
            }

            // 水平网格线
            let h_lines = 8;
            for i in 0..=h_lines {
                let y = bounds.origin.y + (bounds.size.height * i as f64) / h_lines as f64;
                commands.push(DrawCommand::Line {
                    from: Point::new(bounds.origin.x, y),
                    to: Point::new(bounds.origin.x + bounds.size.width, y),
                    style: LineStyle {
                        color: grid.grid_line_color,
                        width: grid.grid_line_width,
                        ..Default::default()
                    },
                });
            }
        }

        commands
    }

    /// 渲染坐标轴
    fn render_axis(&self, axis: &SimpleAxis, bounds: Bounds) -> Vec<DrawCommand> {
        let mut commands = Vec::new();

        if !axis.visible {
            return commands;
        }

        match axis.position {
            AxisPosition::Bottom => {
                // X轴在底部
                if axis.show_line {
                    commands.push(DrawCommand::Line {
                        from: Point::new(bounds.origin.x, bounds.origin.y + bounds.size.height),
                        to: Point::new(bounds.origin.x + bounds.size.width, bounds.origin.y + bounds.size.height),
                        style: LineStyle {
                            color: axis.line_color,
                            width: 2.0,
                            ..Default::default()
                        },
                    });
                }

                // 添加刻度和标签
                if axis.show_ticks || axis.show_labels {
                    self.render_bottom_axis_ticks(&mut commands, axis, bounds);
                }
            },
            AxisPosition::Left => {
                // Y轴在左侧
                if axis.show_line {
                    commands.push(DrawCommand::Line {
                        from: Point::new(bounds.origin.x, bounds.origin.y),
                        to: Point::new(bounds.origin.x, bounds.origin.y + bounds.size.height),
                        style: LineStyle {
                            color: axis.line_color,
                            width: 2.0,
                            ..Default::default()
                        },
                    });
                }

                // 添加刻度和标签
                if axis.show_ticks || axis.show_labels {
                    self.render_left_axis_ticks(&mut commands, axis, bounds);
                }
            },
            AxisPosition::Top => {
                // X轴在顶部
                if axis.show_line {
                    commands.push(DrawCommand::Line {
                        from: Point::new(bounds.origin.x, bounds.origin.y),
                        to: Point::new(bounds.origin.x + bounds.size.width, bounds.origin.y),
                        style: LineStyle {
                            color: axis.line_color,
                            width: 2.0,
                            ..Default::default()
                        },
                    });
                }
            },
            AxisPosition::Right => {
                // Y轴在右侧
                if axis.show_line {
                    commands.push(DrawCommand::Line {
                        from: Point::new(bounds.origin.x + bounds.size.width, bounds.origin.y),
                        to: Point::new(bounds.origin.x + bounds.size.width, bounds.origin.y + bounds.size.height),
                        style: LineStyle {
                            color: axis.line_color,
                            width: 2.0,
                            ..Default::default()
                        },
                    });
                }
            },
        }

        commands
    }

    /// 渲染图例
    fn render_legend(&self, _legend: &SimpleLegend, _bounds: Bounds) -> Vec<DrawCommand> {
        // TODO: 实现图例渲染
        Vec::new()
    }

    /// 渲染底部X轴刻度和标签
    fn render_bottom_axis_ticks(&self, commands: &mut Vec<DrawCommand>, axis: &SimpleAxis, bounds: Bounds) {
        let tick_count = 6;
        let y = bounds.origin.y + bounds.size.height;

        for i in 0..=tick_count {
            let t = i as f64 / tick_count as f64;
            let x = bounds.origin.x + bounds.size.width * t;

            // 刻度线
            if axis.show_ticks {
                commands.push(DrawCommand::Line {
                    from: Point::new(x, y),
                    to: Point::new(x, y + 5.0),
                    style: LineStyle {
                        color: axis.line_color,
                        width: 1.0,
                        ..Default::default()
                    },
                });
            }

            // 标签
            if axis.show_labels {
                let label_value = if let (Some(min), Some(max)) = (axis.min, axis.max) {
                    min + (max - min) * t
                } else {
                    i as f64 * 20.0
                };

                commands.push(DrawCommand::Text {
                    text: format!("{:.0}", label_value),
                    position: Point::new(x, y + 20.0),
                    style: TextStyle {
                        color: axis.label_color,
                        font_size: axis.font_size,
                        ..Default::default()
                    },
                });
            }
        }
    }

    /// 渲染左侧Y轴刻度和标签
    fn render_left_axis_ticks(&self, commands: &mut Vec<DrawCommand>, axis: &SimpleAxis, bounds: Bounds) {
        let tick_count = 5;
        let x = bounds.origin.x;

        for i in 0..=tick_count {
            let t = i as f64 / tick_count as f64;
            let y = bounds.origin.y + bounds.size.height * (1.0 - t);

            // 刻度线
            if axis.show_ticks {
                commands.push(DrawCommand::Line {
                    from: Point::new(x - 5.0, y),
                    to: Point::new(x, y),
                    style: LineStyle {
                        color: axis.line_color,
                        width: 1.0,
                        ..Default::default()
                    },
                });
            }

            // 标签
            if axis.show_labels {
                let label_value = if let (Some(min), Some(max)) = (axis.min, axis.max) {
                    min + (max - min) * t
                } else {
                    i as f64 * 20.0
                };

                commands.push(DrawCommand::Text {
                    text: format!("{:.0}", label_value),
                    position: Point::new(x - 30.0, y + 4.0),
                    style: TextStyle {
                        color: axis.label_color,
                        font_size: axis.font_size,
                        ..Default::default()
                    },
                });
            }
        }
    }
}

/// 增强的图表构建器
///
/// 使用 charts crate 的真实 Series 实现，支持所有高级功能
#[derive(Debug)]
pub struct ChartBuilder {
    chart: Chart,
    /// X轴配置
    pub x_axis: Option<SimpleAxis>,
    /// Y轴配置
    pub y_axis: Option<SimpleAxis>,
    /// 网格配置
    pub grid: Option<SimpleGrid>,
    /// 图例配置
    pub legend: Option<SimpleLegend>,
}

impl ChartBuilder {
    /// 创建新的图表构建器
    pub fn new() -> Self {
        Self {
            chart: Chart::new(),
            x_axis: None,
            y_axis: None,
            grid: None,
            legend: None,
        }
    }

    /// 设置图表标题
    pub fn title<S: Into<String>>(mut self, title: S) -> Self {
        self.chart = self.chart.title(title);
        self
    }

    /// 设置图表大小
    pub fn size(mut self, width: f64, height: f64) -> Self {
        self.chart = self.chart.size(width, height);
        self
    }

    /// 设置背景颜色
    pub fn background_color(mut self, color: Color) -> Self {
        self.chart = self.chart.background_color(color);
        self
    }

    /// 设置边距
    pub fn padding(mut self, top: f64, right: f64, bottom: f64, left: f64) -> Self {
        self.chart = self.chart.padding(top, right, bottom, left);
        self
    }

    // === 组件配置方法 ===

    /// 配置X轴
    pub fn x_axis(mut self, axis: SimpleAxis) -> Self {
        self.x_axis = Some(axis);
        self
    }

    /// 配置Y轴
    pub fn y_axis(mut self, axis: SimpleAxis) -> Self {
        self.y_axis = Some(axis);
        self
    }

    /// 使用默认的数值X轴
    pub fn default_x_axis(mut self, min: Option<f64>, max: Option<f64>) -> Self {
        let mut axis = SimpleAxis::default();
        axis.position = AxisPosition::Bottom;
        axis.min = min;
        axis.max = max;
        self.x_axis = Some(axis);
        self
    }

    /// 使用默认的数值Y轴
    pub fn default_y_axis(mut self, min: Option<f64>, max: Option<f64>) -> Self {
        let mut axis = SimpleAxis::default();
        axis.position = AxisPosition::Left;
        axis.min = min;
        axis.max = max;
        self.y_axis = Some(axis);
        self
    }

    /// 使用分类X轴
    pub fn category_x_axis(mut self, _categories: Vec<String>) -> Self {
        let mut axis = SimpleAxis::default();
        axis.position = AxisPosition::Bottom;
        self.x_axis = Some(axis);
        self
    }

    /// 配置网格
    pub fn grid(mut self, grid: SimpleGrid) -> Self {
        self.grid = Some(grid);
        self
    }

    /// 使用默认网格（显示网格线）
    pub fn default_grid(mut self) -> Self {
        let grid = SimpleGrid::default();
        self.grid = Some(grid);
        self
    }

    /// 使用自定义边距的网格
    pub fn grid_with_margins(mut self, _left: f64, _top: f64, _right: f64, _bottom: f64) -> Self {
        let grid = SimpleGrid::default();
        self.grid = Some(grid);
        self
    }

    /// 配置图例
    pub fn legend(mut self, legend: SimpleLegend) -> Self {
        self.legend = Some(legend);
        self
    }

    /// 使用默认图例
    pub fn default_legend(mut self) -> Self {
        let legend = SimpleLegend::default();
        self.legend = Some(legend);
        self
    }

    // === 添加各种类型的 Series（使用真实实现）===

    /// 添加折线图系列（基础版本）
    pub fn add_line_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>) -> Self {
        let series = LineSeries::new(name).data(data);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加高级折线图系列（支持所有功能）
    pub fn add_advanced_line_series<S: Into<String>, F>(mut self, name: S, data: Vec<(f64, f64)>, config: F) -> Self 
    where 
        F: FnOnce(LineSeries) -> LineSeries
    {
        let series = LineSeries::new(name).data(data);
        let configured_series = config(series);
        self.chart = self.chart.add_series(Box::new(configured_series));
        self
    }

    /// 添加平滑折线图
    pub fn add_smooth_line_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, color: Color) -> Self {
        let series = LineSeries::new(name)
            .data(data)
            .color(color)
            .smooth(true)
            .smoothness(0.3)
            .show_symbols(true);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加面积图
    pub fn add_area_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, color: Color) -> Self {
        let series = LineSeries::new_area(name, color)
            .data(data)
            .area_opacity(0.3);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加阶梯线图
    pub fn add_step_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, step_type: StepType) -> Self {
        let series = LineSeries::new_step(name, step_type)
            .data(data)
            .color(Color::rgb(0.2, 0.6, 1.0));
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加柱状图系列（基础版本）
    pub fn add_bar_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>) -> Self {
        let series = BarSeries::new(name).data(data);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加高级柱状图系列（支持所有功能）
    pub fn add_advanced_bar_series<S: Into<String>, F>(mut self, name: S, data: Vec<(f64, f64)>, config: F) -> Self 
    where 
        F: FnOnce(BarSeries) -> BarSeries
    {
        let series = BarSeries::new(name).data(data);
        let configured_series = config(series);
        self.chart = self.chart.add_series(Box::new(configured_series));
        self
    }

    /// 添加带边框的柱状图
    pub fn add_bordered_bar_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, color: Color, border_color: Color) -> Self {
        let series = BarSeries::new(name)
            .data(data)
            .color(color)
            .border(true, border_color, 1.0)
            .bar_width(0.6);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加散点图系列（基础版本）
    pub fn add_scatter_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>) -> Self {
        let series = ScatterSeries::new(name).data(data);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加高级散点图系列（支持所有功能）
    pub fn add_advanced_scatter_series<S: Into<String>, F>(mut self, name: S, data: Vec<(f64, f64)>, config: F) -> Self 
    where 
        F: FnOnce(ScatterSeries) -> ScatterSeries
    {
        let series = ScatterSeries::new(name).data(data);
        let configured_series = config(series);
        self.chart = self.chart.add_series(Box::new(configured_series));
        self
    }

    /// 添加自定义符号的散点图
    pub fn add_symbol_scatter_series<S: Into<String>>(mut self, name: S, data: Vec<(f64, f64)>, symbol_type: SymbolType, size: f64, color: Color) -> Self {
        let series = ScatterSeries::new(name)
            .data(data)
            .color(color)
            .symbol_type(symbol_type)
            .symbol_size(size);
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 添加任意 Series（完全的灵活性）
    pub fn add_series<S: Series + 'static>(mut self, series: S) -> Self {
        self.chart = self.chart.add_series(Box::new(series));
        self
    }

    /// 构建最终的图表
    ///
    /// 注意：当前版本的 Chart 结构体不直接支持组件，
    /// 组件信息会在渲染时通过 ChartBuilder 的扩展渲染方法处理
    pub fn build(self) -> Chart {
        self.chart
    }

    /// 构建增强的图表（包含组件信息）
    ///
    /// 返回一个包含所有组件配置的 EnhancedChart
    pub fn build_enhanced(self) -> EnhancedChart {
        EnhancedChart {
            chart: self.chart,
            x_axis: self.x_axis,
            y_axis: self.y_axis,
            grid: self.grid,
            legend: self.legend,
        }
    }

    /// 构建并渲染为 DrawCommand
    pub fn render(self) -> Result<Vec<DrawCommand>> {
        self.chart.render_to_commands()
    }
}

impl Default for ChartBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 便捷的图表创建函数
impl ChartBuilder {
    /// 创建折线图
    pub fn line_chart() -> Self {
        Self::new()
    }

    /// 创建柱状图
    pub fn bar_chart() -> Self {
        Self::new()
    }

    /// 创建散点图
    pub fn scatter_chart() -> Self {
        Self::new()
    }

    /// 创建混合图表（可包含多种类型的系列）
    pub fn mixed_chart() -> Self {
        Self::new()
    }

    /// 创建仪表板（多个子图）
    pub fn dashboard() -> Self {
        Self::new().size(1200.0, 800.0)
    }

    /// 创建带有默认坐标轴和网格的折线图
    pub fn line_chart_with_axes() -> Self {
        Self::line_chart()
            .default_x_axis(None, None)
            .default_y_axis(None, None)
            .default_grid()
    }

    /// 创建带有默认坐标轴和网格的柱状图
    pub fn bar_chart_with_axes() -> Self {
        Self::bar_chart()
            .default_x_axis(None, None)
            .default_y_axis(None, None)
            .default_grid()
    }

    /// 创建带有默认坐标轴和网格的散点图
    pub fn scatter_chart_with_axes() -> Self {
        Self::scatter_chart()
            .default_x_axis(None, None)
            .default_y_axis(None, None)
            .default_grid()
    }

    /// 创建完整配置的图表（包含所有组件）
    pub fn full_featured_chart() -> Self {
        Self::new()
            .default_x_axis(None, None)
            .default_y_axis(None, None)
            .default_grid()
            .default_legend()
    }
}

/// 快速创建图表的便捷函数
pub fn line_chart<S: Into<String>>(name: S, data: Vec<(f64, f64)>) -> Chart {
    ChartBuilder::line_chart()
        .add_line_series(name, data)
        .build()
}

pub fn bar_chart<S: Into<String>>(name: S, data: Vec<(f64, f64)>) -> Chart {
    ChartBuilder::bar_chart()
        .add_bar_series(name, data)
        .build()
}

pub fn scatter_chart<S: Into<String>>(name: S, data: Vec<(f64, f64)>) -> Chart {
    ChartBuilder::scatter_chart()
        .add_scatter_series(name, data)
        .build()
}

/// 创建多系列图表的便捷函数
pub fn multi_line_chart(series_data: Vec<(&str, Vec<(f64, f64)>)>) -> Chart {
    let mut builder = ChartBuilder::line_chart();
    
    for (name, data) in series_data {
        builder = builder.add_line_series(name, data);
    }
    
    builder.build()
}

pub fn multi_bar_chart(series_data: Vec<(&str, Vec<(f64, f64)>)>) -> Chart {
    let mut builder = ChartBuilder::bar_chart();
    
    for (name, data) in series_data {
        builder = builder.add_bar_series(name, data);
    }
    
    builder.build()
}

/// 创建混合图表的便捷函数
pub fn mixed_chart() -> ChartBuilder {
    ChartBuilder::mixed_chart()
}

/// 高级图表构建器
///
/// 提供更丰富的配置选项和自动化功能
#[derive(Debug)]
pub struct AdvancedChartBuilder {
    /// 基础构建器
    builder: ChartBuilder,
    /// 自动配置选项
    auto_config: AutoConfigOptions,
    /// 主题名称
    theme_name: Option<String>,
    /// 响应式配置
    responsive: bool,
}

/// 自动配置选项
#[derive(Debug, Clone)]
pub struct AutoConfigOptions {
    /// 自动计算坐标轴范围
    pub auto_axis_range: bool,
    /// 自动选择网格密度
    pub auto_grid_density: bool,
    /// 自动调整边距
    pub auto_margins: bool,
    /// 自动选择颜色
    pub auto_colors: bool,
    /// 自动优化性能
    pub auto_performance: bool,
}

impl Default for AutoConfigOptions {
    fn default() -> Self {
        Self {
            auto_axis_range: true,
            auto_grid_density: true,
            auto_margins: true,
            auto_colors: true,
            auto_performance: true,
        }
    }
}

impl AdvancedChartBuilder {
    /// 创建新的高级构建器
    pub fn new() -> Self {
        Self {
            builder: ChartBuilder::new(),
            auto_config: AutoConfigOptions::default(),
            theme_name: None,
            responsive: false,
        }
    }

    /// 从基础构建器创建
    pub fn from_builder(builder: ChartBuilder) -> Self {
        Self {
            builder,
            auto_config: AutoConfigOptions::default(),
            theme_name: None,
            responsive: false,
        }
    }

    /// 设置自动配置选项
    pub fn auto_config(mut self, config: AutoConfigOptions) -> Self {
        self.auto_config = config;
        self
    }

    /// 启用响应式
    pub fn responsive(mut self, responsive: bool) -> Self {
        self.responsive = responsive;
        self
    }

    /// 设置主题
    pub fn theme<S: Into<String>>(mut self, theme_name: S) -> Self {
        self.theme_name = Some(theme_name.into());
        self
    }

    /// 智能折线图配置
    pub fn smart_line_chart(mut self) -> Self {
        self.builder = ChartBuilder::line_chart_with_axes()
            .grid_with_margins(100.0, 80.0, 50.0, 100.0);

        if self.auto_config.auto_colors {
            // 自动应用颜色方案
            self = self.apply_auto_colors();
        }

        self
    }

    /// 智能柱状图配置
    pub fn smart_bar_chart(mut self) -> Self {
        self.builder = ChartBuilder::bar_chart_with_axes()
            .grid_with_margins(120.0, 80.0, 50.0, 100.0); // 柱状图需要更多左边距

        if self.auto_config.auto_colors {
            self = self.apply_auto_colors();
        }

        self
    }

    /// 智能散点图配置
    pub fn smart_scatter_chart(mut self) -> Self {
        self.builder = ChartBuilder::scatter_chart_with_axes()
            .grid_with_margins(100.0, 80.0, 50.0, 100.0);

        if self.auto_config.auto_colors {
            self = self.apply_auto_colors();
        }

        self
    }

    /// 自动分析数据并配置坐标轴
    pub fn auto_analyze_data<T>(mut self, data: &[(T, T)]) -> Self
    where
        T: Clone + Into<f64>
    {
        if !self.auto_config.auto_axis_range {
            return self;
        }

        // 分析数据范围
        let (x_min, x_max, y_min, y_max) = self.analyze_data_range(data);

        // 自动配置坐标轴
        self.builder = self.builder
            .default_x_axis(Some(x_min), Some(x_max))
            .default_y_axis(Some(y_min), Some(y_max));

        self
    }

    /// 应用自动颜色方案
    fn apply_auto_colors(mut self) -> Self {
        // 获取系列数量
        let series_count = self.builder.chart.series.len();

        // 根据系列数量选择颜色方案
        let _color_palette = self.get_color_palette_for_series_count(series_count);

        // 应用颜色到系列（这里需要修改系列的颜色，但由于 Series trait 的限制，
        // 我们暂时在构建器级别记录颜色方案，供后续使用）
        self.auto_config.auto_colors = true;

        // 存储颜色方案到主题名称中（临时方案）
        if self.theme_name.is_none() {
            self.theme_name = Some(format!("auto_colors_{}", series_count));
        }

        self
    }

    /// 根据系列数量获取颜色方案
    fn get_color_palette_for_series_count(&self, count: usize) -> Vec<Color> {
        match count {
            0 => vec![],
            1 => vec![Color::rgb(0.2, 0.6, 0.9)], // 单系列用蓝色
            2 => vec![
                Color::rgb(0.2, 0.6, 0.9),  // 蓝色
                Color::rgb(0.9, 0.4, 0.2),  // 橙色
            ],
            3 => vec![
                Color::rgb(0.2, 0.6, 0.9),  // 蓝色
                Color::rgb(0.9, 0.4, 0.2),  // 橙色
                Color::rgb(0.2, 0.8, 0.4),  // 绿色
            ],
            4 => vec![
                Color::rgb(0.2, 0.6, 0.9),  // 蓝色
                Color::rgb(0.9, 0.4, 0.2),  // 橙色
                Color::rgb(0.2, 0.8, 0.4),  // 绿色
                Color::rgb(0.8, 0.2, 0.6),  // 紫色
            ],
            _ => {
                // 多系列使用渐变色方案
                let mut colors = Vec::new();
                for i in 0..count {
                    let hue = (i as f64 * 360.0 / count as f64) % 360.0;
                    colors.push(Color::from_hsl(hue as f32, 0.7, 0.5));
                }
                colors
            }
        }
    }

    /// 分析数据范围
    fn analyze_data_range<T>(&self, data: &[(T, T)]) -> (f64, f64, f64, f64)
    where
        T: Clone + Into<f64>
    {
        if data.is_empty() {
            return (0.0, 100.0, 0.0, 100.0);
        }

        // 提取所有 x 和 y 值
        let mut x_values = Vec::new();
        let mut y_values = Vec::new();

        for (x, y) in data {
            x_values.push(x.clone().into());
            y_values.push(y.clone().into());
        }

        // 计算最小值和最大值
        let x_min = x_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let x_max = x_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let y_min = y_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let y_max = y_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));

        // 添加适当的边距（10%）
        let x_range = x_max - x_min;
        let y_range = y_max - y_min;

        let x_margin = if x_range > 0.0 { x_range * 0.1 } else { 1.0 };
        let y_margin = if y_range > 0.0 { y_range * 0.1 } else { 1.0 };

        (
            x_min - x_margin,
            x_max + x_margin,
            y_min - y_margin,
            y_max + y_margin,
        )
    }

    /// 构建最终的增强图表
    pub fn build(mut self) -> EnhancedChart {
        // 应用自动配置
        if self.auto_config.auto_margins {
            self = self.apply_auto_margins();
        }

        if self.auto_config.auto_grid_density {
            self = self.apply_auto_grid_density();
        }

        if self.auto_config.auto_performance {
            self = self.apply_performance_optimizations();
        }

        // 应用主题
        if let Some(theme_name) = self.theme_name.clone() {
            self = self.apply_theme(&theme_name);
        }

        self.builder.build_enhanced()
    }

    /// 应用自动边距
    fn apply_auto_margins(mut self) -> Self {
        // 分析坐标轴标签需要的空间
        let margins = self.calculate_optimal_margins();

        // 应用计算出的边距
        self.builder = self.builder.padding(
            margins.top,
            margins.right,
            margins.bottom,
            margins.left,
        );

        self
    }

    /// 计算最优边距
    fn calculate_optimal_margins(&self) -> Margins {
        let mut margins = Margins::default();

        // 基础边距
        margins.left = 80.0;
        margins.right = 40.0;
        margins.top = 60.0;
        margins.bottom = 80.0;

        // 根据标题调整上边距
        if self.builder.chart.title.is_some() {
            margins.top += 40.0;
        }

        // 根据Y轴标签调整左边距
        if let Some(y_axis) = &self.builder.y_axis {
            if y_axis.show_labels {
                // 估算标签宽度（基于字体大小）
                let font_size = y_axis.font_size;
                let estimated_label_width = font_size * 4.0; // 估算4个字符宽度
                margins.left = margins.left.max(estimated_label_width + 20.0);
            }

            // 如果有轴名称，增加额外空间
            if y_axis.name.is_some() {
                margins.left += 30.0;
            }
        }

        // 根据X轴标签调整下边距
        if let Some(x_axis) = &self.builder.x_axis {
            if x_axis.show_labels {
                let font_size = x_axis.font_size;
                margins.bottom = margins.bottom.max(font_size + 40.0);
            }

            // 如果有轴名称，增加额外空间
            if x_axis.name.is_some() {
                margins.bottom += 25.0;
            }
        }

        // 根据图例调整边距
        if self.builder.legend.is_some() {
            margins.bottom += 60.0; // 为图例留出空间
        }

        margins
    }

    /// 应用自动网格密度
    fn apply_auto_grid_density(mut self) -> Self {
        // 根据图表大小计算最优网格密度
        let chart_width = self.builder.chart.width;
        let chart_height = self.builder.chart.height;

        // 计算网格间距（基于图表大小）
        let optimal_h_spacing = self.calculate_optimal_grid_spacing(chart_width, true);
        let optimal_v_spacing = self.calculate_optimal_grid_spacing(chart_height, false);

        // 如果已有网格，更新其配置
        if let Some(grid) = &mut self.builder.grid {
            // 根据计算的间距调整网格样式
            if optimal_h_spacing < 30.0 || optimal_v_spacing < 30.0 {
                // 密集网格使用更细的线条
                grid.grid_line_width = 0.3;
                grid.grid_line_color = Color::rgba(0.7, 0.7, 0.7, 0.3);
            } else if optimal_h_spacing > 80.0 || optimal_v_spacing > 80.0 {
                // 稀疏网格使用更粗的线条
                grid.grid_line_width = 0.8;
                grid.grid_line_color = Color::rgba(0.7, 0.7, 0.7, 0.6);
            }
        } else {
            // 创建自适应网格
            let mut grid = SimpleGrid::default();

            // 根据计算的间距调整网格样式
            if optimal_h_spacing < 30.0 || optimal_v_spacing < 30.0 {
                // 密集网格使用更细的线条
                grid.grid_line_width = 0.3;
                grid.grid_line_color = Color::rgba(0.7, 0.7, 0.7, 0.3);
            } else if optimal_h_spacing > 80.0 || optimal_v_spacing > 80.0 {
                // 稀疏网格使用更粗的线条
                grid.grid_line_width = 0.8;
                grid.grid_line_color = Color::rgba(0.7, 0.7, 0.7, 0.6);
            }

            self.builder.grid = Some(grid);
        }

        self
    }

    /// 计算最优网格间距
    fn calculate_optimal_grid_spacing(&self, dimension: f64, _is_horizontal: bool) -> f64 {
        // 目标：每个网格单元大小在 40-100 像素之间
        let min_spacing = 40.0;
        let max_spacing = 100.0;
        let target_spacing = 60.0;

        // 根据维度大小计算网格数量
        let grid_count = (dimension / target_spacing).round().max(3.0).min(15.0);
        let spacing = dimension / grid_count;

        // 确保间距在合理范围内
        spacing.max(min_spacing).min(max_spacing)
    }

    /// 应用性能优化
    fn apply_performance_optimizations(mut self) -> Self {
        // 分析数据量和复杂度
        let total_data_points = self.estimate_total_data_points();
        let chart_area = self.builder.chart.width * self.builder.chart.height;

        // 根据数据量应用不同的优化策略
        if total_data_points > 10000 {
            // 大数据量优化
            self = self.apply_large_dataset_optimizations();
        } else if total_data_points > 1000 {
            // 中等数据量优化
            self = self.apply_medium_dataset_optimizations();
        }

        // 根据图表大小优化
        if chart_area > 1000000.0 {
            // 大图表优化
            self = self.apply_large_chart_optimizations();
        }

        self
    }

    /// 估算总数据点数量
    fn estimate_total_data_points(&self) -> usize {
        // 这里需要访问系列数据，但由于 Series trait 的限制，
        // 我们使用系列数量作为估算基础
        let series_count = self.builder.chart.series.len();

        // 假设每个系列平均有100个数据点（可以根据实际情况调整）
        series_count * 100
    }

    /// 应用大数据集优化
    fn apply_large_dataset_optimizations(mut self) -> Self {
        // 减少网格线密度
        if let Some(grid) = &mut self.builder.grid {
            grid.grid_line_width = 0.2;
            grid.grid_line_color = Color::rgba(0.7, 0.7, 0.7, 0.2);
        }

        // 简化坐标轴刻度
        if let Some(_x_axis) = &mut self.builder.x_axis {
            // 减少刻度数量（这里需要扩展 Axis 结构来支持）
        }

        self
    }

    /// 应用中等数据集优化
    fn apply_medium_dataset_optimizations(mut self) -> Self {
        // 适中的网格线设置
        if let Some(grid) = &mut self.builder.grid {
            grid.grid_line_width = 0.4;
            grid.grid_line_color = Color::rgba(0.7, 0.7, 0.7, 0.4);
        }

        self
    }

    /// 应用大图表优化
    fn apply_large_chart_optimizations(mut self) -> Self {
        // 增加字体大小以适应大图表
        if let Some(x_axis) = &mut self.builder.x_axis {
            x_axis.font_size = x_axis.font_size * 1.2;
        }

        if let Some(y_axis) = &mut self.builder.y_axis {
            y_axis.font_size = y_axis.font_size * 1.2;
        }

        self
    }

    /// 应用主题
    fn apply_theme(mut self, theme_name: &str) -> Self {
        // 根据主题名称应用相应的样式
        match theme_name {
            "light" => self = self.apply_light_theme(),
            "dark" => self = self.apply_dark_theme(),
            "scientific" => self = self.apply_scientific_theme(),
            "financial" => self = self.apply_financial_theme(),
            "minimal" => self = self.apply_minimal_theme(),
            name if name.starts_with("auto_colors_") => {
                // 自动颜色主题，已在 apply_auto_colors 中处理
            },
            _ => {
                // 默认使用亮色主题
                self = self.apply_light_theme();
            }
        }

        self
    }

    /// 应用亮色主题
    fn apply_light_theme(mut self) -> Self {
        // 坐标轴样式
        if let Some(x_axis) = &mut self.builder.x_axis {
            x_axis.line_color = Color::rgb(0.2, 0.2, 0.2);
            x_axis.label_color = Color::rgb(0.4, 0.4, 0.4);
        }

        if let Some(y_axis) = &mut self.builder.y_axis {
            y_axis.line_color = Color::rgb(0.2, 0.2, 0.2);
            y_axis.label_color = Color::rgb(0.4, 0.4, 0.4);
        }

        // 网格样式
        if let Some(grid) = &mut self.builder.grid {
            grid.grid_line_color = Color::rgba(0.0, 0.0, 0.0, 0.1);
            grid.background_color = Some(Color::rgba(0.95, 0.95, 0.95, 0.3));
        }

        // 图表背景
        self.builder = self.builder.background_color(Color::rgb(1.0, 1.0, 1.0));

        self
    }

    /// 应用暗色主题
    fn apply_dark_theme(mut self) -> Self {
        // 坐标轴样式
        if let Some(x_axis) = &mut self.builder.x_axis {
            x_axis.line_color = Color::rgb(0.8, 0.8, 0.8);
            x_axis.label_color = Color::rgb(0.6, 0.6, 0.6);
        }

        if let Some(y_axis) = &mut self.builder.y_axis {
            y_axis.line_color = Color::rgb(0.8, 0.8, 0.8);
            y_axis.label_color = Color::rgb(0.6, 0.6, 0.6);
        }

        // 网格样式
        if let Some(grid) = &mut self.builder.grid {
            grid.grid_line_color = Color::rgba(1.0, 1.0, 1.0, 0.1);
            grid.background_color = Some(Color::rgba(0.1, 0.1, 0.1, 0.3));
        }

        // 图表背景
        self.builder = self.builder.background_color(Color::rgb(0.1, 0.1, 0.1));

        self
    }

    /// 应用科学主题
    fn apply_scientific_theme(mut self) -> Self {
        // 科学图表通常需要精确的网格和清晰的坐标轴
        if let Some(grid) = &mut self.builder.grid {
            grid.grid_line_color = Color::rgba(0.0, 0.0, 0.0, 0.2);
            grid.grid_line_width = 0.5;
        }

        // 使用更正式的字体设置
        if let Some(x_axis) = &mut self.builder.x_axis {
            x_axis.font_size = 11.0;
        }

        if let Some(y_axis) = &mut self.builder.y_axis {
            y_axis.font_size = 11.0;
        }

        self.builder = self.builder.background_color(Color::rgb(0.98, 0.98, 0.98));

        self
    }

    /// 应用金融主题
    fn apply_financial_theme(mut self) -> Self {
        // 金融图表通常使用绿色和红色
        if let Some(grid) = &mut self.builder.grid {
            grid.grid_line_color = Color::rgba(0.0, 0.0, 0.0, 0.05);
            grid.background_color = Some(Color::rgba(0.99, 0.99, 0.99, 1.0));
            grid.grid_line_width = 0.3; // 更密集的网格用于精确读数
        }

        self.builder = self.builder.background_color(Color::rgb(0.99, 0.99, 0.99));

        self
    }

    /// 应用极简主题
    fn apply_minimal_theme(mut self) -> Self {
        // 极简主题：最少的视觉元素
        if let Some(grid) = &mut self.builder.grid {
            grid.show_grid_lines = false; // 不显示网格线
        }

        // 简化坐标轴
        if let Some(x_axis) = &mut self.builder.x_axis {
            x_axis.show_ticks = false; // 不显示刻度
        }

        if let Some(y_axis) = &mut self.builder.y_axis {
            y_axis.show_ticks = false;
        }

        self.builder = self.builder.background_color(Color::rgb(1.0, 1.0, 1.0));

        self
    }

    /// 获取基础构建器的引用
    pub fn builder(&self) -> &ChartBuilder {
        &self.builder
    }

    /// 获取基础构建器的可变引用
    pub fn builder_mut(&mut self) -> &mut ChartBuilder {
        &mut self.builder
    }
}

// === 高级构建器便捷函数 ===

/// 创建智能折线图
pub fn smart_line_chart() -> AdvancedChartBuilder {
    AdvancedChartBuilder::new().smart_line_chart()
}

/// 创建智能柱状图
pub fn smart_bar_chart() -> AdvancedChartBuilder {
    AdvancedChartBuilder::new().smart_bar_chart()
}

/// 创建智能散点图
pub fn smart_scatter_chart() -> AdvancedChartBuilder {
    AdvancedChartBuilder::new().smart_scatter_chart()
}

/// 创建响应式图表
pub fn responsive_chart() -> AdvancedChartBuilder {
    AdvancedChartBuilder::new().responsive(true)
}

/// 创建主题化图表
pub fn themed_chart<S: Into<String>>(theme: S) -> AdvancedChartBuilder {
    AdvancedChartBuilder::new().theme(theme)
}
