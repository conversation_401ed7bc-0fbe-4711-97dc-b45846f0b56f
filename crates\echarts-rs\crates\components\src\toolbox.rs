//! Toolbox component implementation

use crate::{Component, Orientation, Position, Renderable, Themeable};
use echarts_core::*;
use echarts_charts::RenderContext;
use echarts_themes::Theme;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Toolbox component for chart tools and features
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Toolbox {
    /// Whether the toolbox is visible
    pub visible: bool,

    /// Position of the toolbox
    pub position: Position,

    /// Orientation of the toolbox
    pub orientation: Orientation,

    /// Item size
    pub item_size: f64,

    /// Gap between items
    pub item_gap: f64,

    /// Whether to show title
    pub show_title: bool,

    /// Feature configurations
    pub features: HashMap<String, ToolboxFeature>,

    /// Icon style
    pub icon_style: IconStyle,

    /// Emphasis style
    pub emphasis: EmphasisStyle,

    /// Tooltip configuration
    pub tooltip: Option<ToolboxTooltip>,

    /// Z-level for rendering order
    pub z_level: i32,

    /// Z-index for rendering order
    pub z: i32,
}

/// Toolbox feature configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ToolboxFeature {
    /// Whether this feature is enabled
    pub enabled: bool,

    /// Feature type
    pub feature_type: FeatureType,

    /// Custom icon path or built-in icon
    pub icon: Option<String>,

    /// Feature title
    pub title: String,

    /// Feature options
    pub options: FeatureOptions,
}

/// Types of toolbox features
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum FeatureType {
    /// Save as image
    SaveAsImage,
    /// Restore chart
    Restore,
    /// Data view
    DataView,
    /// Data zoom
    DataZoom,
    /// Magic type (switch chart types)
    MagicType,
    /// Brush selection
    Brush,
    /// Custom feature
    Custom,
}

/// Feature-specific options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeatureOptions {
    /// Save as image options
    pub save_as_image: Option<SaveAsImageOptions>,
    /// Data view options
    pub data_view: Option<DataViewOptions>,
    /// Magic type options
    pub magic_type: Option<MagicTypeOptions>,
    /// Brush options
    pub brush: Option<BrushOptions>,
    /// Custom options
    pub custom: Option<CustomOptions>,
}

/// Save as image feature options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SaveAsImageOptions {
    /// Image type
    pub image_type: ImageType,
    /// Image name
    pub name: String,
    /// Background color
    pub background_color: Color,
    /// Exclude components
    pub exclude_components: Vec<String>,
    /// Show title
    pub show_title: bool,
    /// Title text
    pub title: String,
    /// Pixel ratio
    pub pixel_ratio: f64,
}

/// Data view feature options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataViewOptions {
    /// Whether data is editable
    pub read_only: bool,
    /// Language configuration
    pub lang: Vec<String>,
    /// Background color
    pub background_color: Color,
    /// Text color
    pub text_color: Color,
    /// Text area color
    pub text_area_color: Color,
    /// Text area border color
    pub text_area_border_color: Color,
    /// Button color
    pub button_color: Color,
    /// Button text color
    pub button_text_color: Color,
}

/// Magic type feature options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MagicTypeOptions {
    /// Available chart types
    pub types: Vec<MagicChartType>,
    /// Type-specific options
    pub type_options: HashMap<String, serde_json::Value>,
}

/// Chart types for magic type feature
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum MagicChartType {
    Line,
    Bar,
    Stack,
    Tiled,
}

/// Brush feature options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrushOptions {
    /// Brush types
    pub types: Vec<BrushType>,
    /// Icon for each brush type
    pub icons: HashMap<String, String>,
    /// Title for each brush type
    pub titles: HashMap<String, String>,
}

/// Brush types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum BrushType {
    Rect,
    Polygon,
    LineX,
    LineY,
    Keep,
    Clear,
}

/// Custom feature options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomOptions {
    /// Custom data
    pub data: serde_json::Value,
}

/// Image types for save as image
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ImageType {
    Png,
    Jpeg,
    Svg,
}

/// Icon style for toolbox items
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IconStyle {
    /// Icon color
    pub color: Color,
    /// Icon border color
    pub border_color: Color,
    /// Icon border width
    pub border_width: f64,
    /// Icon border type
    pub border_type: BorderType,
    /// Icon shadow blur
    pub shadow_blur: f64,
    /// Icon shadow color
    pub shadow_color: Color,
    /// Icon shadow offset
    pub shadow_offset: (f64, f64),
    /// Icon opacity
    pub opacity: f64,
}

/// Border types for icons
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum BorderType {
    Solid,
    Dashed,
    Dotted,
}

/// Emphasis style for toolbox items
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmphasisStyle {
    /// Icon style on emphasis
    pub icon_style: IconStyle,
}

/// Toolbox tooltip configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolboxTooltip {
    /// Whether to show tooltip
    pub show: bool,
    /// Tooltip formatter
    pub formatter: Option<String>,
    /// Background color
    pub background_color: Color,
    /// Border color
    pub border_color: Color,
    /// Border width
    pub border_width: f64,
    /// Text style
    pub text_style: TextStyle,
}

impl Default for Toolbox {
    fn default() -> Self {
        let mut features = HashMap::new();

        // Add default features
        features.insert(
            "saveAsImage".to_string(),
            ToolboxFeature {
                enabled: true,
                feature_type: FeatureType::SaveAsImage,
                icon: None,
                title: "Save as Image".to_string(),
                options: FeatureOptions {
                    save_as_image: Some(SaveAsImageOptions::default()),
                    data_view: None,
                    magic_type: None,
                    brush: None,
                    custom: None,
                },
            },
        );

        features.insert(
            "restore".to_string(),
            ToolboxFeature {
                enabled: true,
                feature_type: FeatureType::Restore,
                icon: None,
                title: "Restore".to_string(),
                options: FeatureOptions::default(),
            },
        );

        Self {
            visible: true,
            position: Position::TopRight,
            orientation: Orientation::Horizontal,
            item_size: 15.0,
            item_gap: 10.0,
            show_title: true,
            features,
            icon_style: IconStyle::default(),
            emphasis: EmphasisStyle::default(),
            tooltip: Some(ToolboxTooltip::default()),
            z_level: 0,
            z: 2,
        }
    }
}

impl Default for FeatureOptions {
    fn default() -> Self {
        Self {
            save_as_image: None,
            data_view: None,
            magic_type: None,
            brush: None,
            custom: None,
        }
    }
}

impl Default for SaveAsImageOptions {
    fn default() -> Self {
        Self {
            image_type: ImageType::Png,
            name: "chart".to_string(),
            background_color: Color::WHITE,
            exclude_components: Vec::new(),
            show_title: false,
            title: "".to_string(),
            pixel_ratio: 1.0,
        }
    }
}

impl Default for IconStyle {
    fn default() -> Self {
        Self {
            color: Color::from_rgba(102, 102, 102, 255),
            border_color: Color::TRANSPARENT,
            border_width: 0.0,
            border_type: BorderType::Solid,
            shadow_blur: 0.0,
            shadow_color: Color::TRANSPARENT,
            shadow_offset: (0.0, 0.0),
            opacity: 1.0,
        }
    }
}

impl Default for EmphasisStyle {
    fn default() -> Self {
        Self {
            icon_style: IconStyle {
                color: Color::from_rgba(51, 51, 51, 255),
                ..IconStyle::default()
            },
        }
    }
}

impl Default for ToolboxTooltip {
    fn default() -> Self {
        Self {
            show: true,
            formatter: None,
            background_color: Color::from_rgba(50, 50, 50, 200),
            border_color: Color::from_rgba(51, 51, 51, 255),
            border_width: 1.0,
            text_style: TextStyle {
                color: Color::WHITE,
                font_size: 12.0,
                ..TextStyle::default()
            },
        }
    }
}

impl Component for Toolbox {
    fn component_type(&self) -> &'static str {
        "toolbox"
    }

    fn is_visible(&self) -> bool {
        self.visible
    }

    fn set_visible(&mut self, visible: bool) {
        self.visible = visible;
    }
}

impl Renderable for Toolbox {
    fn render<R: ChartRenderer>(&self, ctx: &mut RenderContext<R>, bounds: Bounds) -> Result<()> {
        if !self.visible {
            return Ok(());
        }

        let toolbox_bounds = self.calculate_bounds(bounds);
        self.render_features(ctx, toolbox_bounds)?;

        Ok(())
    }
}

impl Themeable for Toolbox {
    fn apply_theme(&mut self, theme: &Theme) {
        // Apply theme colors to toolbox
        self.icon_style.color = theme.text_style.color;
    }
}

impl Toolbox {
    /// Create a new toolbox
    pub fn new() -> Self {
        Self::default()
    }

    /// Set toolbox position
    pub fn position(mut self, position: Position) -> Self {
        self.position = position;
        self
    }

    /// Set toolbox orientation
    pub fn orientation(mut self, orientation: Orientation) -> Self {
        self.orientation = orientation;
        self
    }

    /// Set item size
    pub fn item_size(mut self, size: f64) -> Self {
        self.item_size = size;
        self
    }

    /// Set item gap
    pub fn item_gap(mut self, gap: f64) -> Self {
        self.item_gap = gap;
        self
    }

    /// Add a feature
    pub fn add_feature<S: Into<String>>(mut self, name: S, feature: ToolboxFeature) -> Self {
        self.features.insert(name.into(), feature);
        self
    }

    /// Enable/disable a feature
    pub fn set_feature_enabled<S: AsRef<str>>(&mut self, name: S, enabled: bool) {
        if let Some(feature) = self.features.get_mut(name.as_ref()) {
            feature.enabled = enabled;
        }
    }

    /// Calculate toolbox bounds based on position
    fn calculate_bounds(&self, container_bounds: Bounds) -> Bounds {
        let enabled_features: Vec<_> = self.features.values().filter(|f| f.enabled).collect();

        let item_count = enabled_features.len() as f64;
        let total_gap = (item_count - 1.0).max(0.0) * self.item_gap;

        let (width, height) = match self.orientation {
            Orientation::Horizontal => (item_count * self.item_size + total_gap, self.item_size),
            Orientation::Vertical => (self.item_size, item_count * self.item_size + total_gap),
        };

        let (x, y) = match self.position {
            Position::TopLeft => (
                container_bounds.origin.x + 10.0,
                container_bounds.origin.y + 10.0,
            ),
            Position::TopRight => (
                container_bounds.origin.x + container_bounds.width() - width - 10.0,
                container_bounds.origin.y + 10.0,
            ),
            Position::BottomLeft => (
                container_bounds.origin.x + 10.0,
                container_bounds.origin.y + container_bounds.height() - height - 10.0,
            ),
            Position::BottomRight => (
                container_bounds.origin.x + container_bounds.width() - width - 10.0,
                container_bounds.origin.y + container_bounds.height() - height - 10.0,
            ),
            Position::Top => (
                container_bounds.origin.x + (container_bounds.width() - width) / 2.0,
                container_bounds.origin.y + 10.0,
            ),
            Position::Bottom => (
                container_bounds.origin.x + (container_bounds.width() - width) / 2.0,
                container_bounds.origin.y + container_bounds.height() - height - 10.0,
            ),
            Position::Left => (
                container_bounds.origin.x + 10.0,
                container_bounds.origin.y + (container_bounds.height() - height) / 2.0,
            ),
            Position::Right => (
                container_bounds.origin.x + container_bounds.width() - width - 10.0,
                container_bounds.origin.y + (container_bounds.height() - height) / 2.0,
            ),
            Position::Center => (
                container_bounds.origin.x + (container_bounds.width() - width) / 2.0,
                container_bounds.origin.y + (container_bounds.height() - height) / 2.0,
            ),
            Position::Absolute { x, y } => (x, y),
            Position::Relative { x, y } => (
                container_bounds.origin.x + container_bounds.width() * x / 100.0,
                container_bounds.origin.y + container_bounds.height() * y / 100.0,
            ),
        };

        Bounds::new(x, y, width, height)
    }

    /// Render toolbox features
    fn render_features(&self, ctx: &mut RenderContext, bounds: Bounds) -> Result<()> {
        let enabled_features: Vec<_> = self.features.iter().filter(|(_, f)| f.enabled).collect();

        let mut current_x = bounds.origin.x;
        let mut current_y = bounds.origin.y;

        for (name, feature) in enabled_features {
            let item_bounds = Bounds::new(current_x, current_y, self.item_size, self.item_size);

            self.render_feature_item(ctx, item_bounds, name, feature)?;

            match self.orientation {
                Orientation::Horizontal => {
                    current_x += self.item_size + self.item_gap;
                }
                Orientation::Vertical => {
                    current_y += self.item_size + self.item_gap;
                }
            }
        }

        Ok(())
    }

    /// Render a single feature item
    fn render_feature_item(
        &self,
        ctx: &mut RenderContext,
        bounds: Bounds,
        _name: &str,
        feature: &ToolboxFeature,
    ) -> Result<()> {
        // Draw background
        ctx.set_fill_color(Color::TRANSPARENT);
        ctx.fill_rect(bounds);

        // Draw icon based on feature type
        self.draw_feature_icon(ctx, bounds, feature)?;

        // Draw border if needed
        if self.icon_style.border_width > 0.0 {
            ctx.set_stroke(self.icon_style.border_color, self.icon_style.border_width);
            ctx.stroke_rect(bounds);
        }

        Ok(())
    }

    /// Draw feature icon
    fn draw_feature_icon(
        &self,
        ctx: &mut RenderContext,
        bounds: Bounds,
        feature: &ToolboxFeature,
    ) -> Result<()> {
        let center = Point::new(
            bounds.origin.x + bounds.width() / 2.0,
            bounds.origin.y + bounds.height() / 2.0,
        );

        // For now, draw simple geometric shapes for different features
        match feature.feature_type {
            FeatureType::SaveAsImage => {
                // Draw a camera-like icon
                let rect_bounds = Bounds::new(center.x - 6.0, center.y - 4.0, 12.0, 8.0);
                ctx.set_stroke(self.icon_style.color, 1.0);
                ctx.stroke_rect(rect_bounds);

                // Draw lens
                let lens_center = Point::new(center.x + 2.0, center.y);
                self.draw_circle(ctx, lens_center, 2.0, self.icon_style.color);
            }
            FeatureType::Restore => {
                // Draw a refresh icon
                self.draw_refresh_icon(ctx, center, 6.0, self.icon_style.color)?;
            }
            FeatureType::DataView => {
                // Draw a table-like icon
                for i in 0..3 {
                    for j in 0..3 {
                        let x = center.x - 4.0 + j as f64 * 3.0;
                        let y = center.y - 4.0 + i as f64 * 3.0;
                        let cell_bounds = Bounds::new(x, y, 2.0, 2.0);
                        ctx.set_fill_color(self.icon_style.color);
                        ctx.fill_rect(cell_bounds);
                    }
                }
            }
            FeatureType::DataZoom => {
                // Draw a magnifying glass
                self.draw_circle(ctx, center, 4.0, Color::TRANSPARENT);
                ctx.draw_line(
                    Point::new(center.x + 3.0, center.y + 3.0),
                    Point::new(center.x + 6.0, center.y + 6.0),
                    self.icon_style.color,
                    1.0,
                );
            }
            FeatureType::MagicType => {
                // Draw a wand-like icon
                ctx.draw_line(
                    Point::new(center.x - 4.0, center.y + 4.0),
                    Point::new(center.x + 4.0, center.y - 4.0),
                    self.icon_style.color,
                    2.0,
                );
                // Draw star at tip
                self.draw_star(
                    ctx,
                    Point::new(center.x + 4.0, center.y - 4.0),
                    2.0,
                    self.icon_style.color,
                )?;
            }
            FeatureType::Brush => {
                // Draw a brush icon
                ctx.draw_line(
                    Point::new(center.x - 3.0, center.y + 3.0),
                    Point::new(center.x + 3.0, center.y - 3.0),
                    self.icon_style.color,
                    3.0,
                );
            }
            FeatureType::Custom => {
                // Draw a generic icon
                self.draw_circle(ctx, center, 4.0, self.icon_style.color);
            }
        }

        Ok(())
    }

    /// Draw a circle (helper method)
    fn draw_circle(&self, ctx: &mut RenderContext, center: Point, radius: f64, color: Color) {
        let mut path = Path::new();
        let segments = 16;
        for i in 0..=segments {
            let angle = (i as f64 / segments as f64) * 2.0 * std::f64::consts::PI;
            let x = center.x + radius * angle.cos();
            let y = center.y + radius * angle.sin();
            let point = Point::new(x, y);

            if i == 0 {
                path = path.move_to(point);
            } else {
                path = path.line_to(point);
            }
        }
        path = path.close();
        ctx.draw_path(path, None, Some((color, 1.0)));
    }

    /// Draw a refresh icon
    fn draw_refresh_icon(
        &self,
        ctx: &mut RenderContext,
        center: Point,
        radius: f64,
        color: Color,
    ) -> Result<()> {
        // Draw circular arrow
        let mut path = Path::new();
        let start_angle = -std::f64::consts::PI / 4.0;
        let end_angle = start_angle + 3.0 * std::f64::consts::PI / 2.0;

        // Draw arc
        let segments = 12;
        for i in 0..segments {
            let angle = start_angle + (end_angle - start_angle) * i as f64 / (segments - 1) as f64;
            let x = center.x + radius * angle.cos();
            let y = center.y + radius * angle.sin();
            let point = Point::new(x, y);

            if i == 0 {
                path = path.move_to(point);
            } else {
                path = path.line_to(point);
            }
        }

        ctx.draw_path(path, None, Some((color, 1.0)));

        // Draw arrow head
        let arrow_angle = end_angle;
        let arrow_tip = Point::new(
            center.x + radius * arrow_angle.cos(),
            center.y + radius * arrow_angle.sin(),
        );
        let arrow_base1 = Point::new(
            arrow_tip.x - 2.0 * (arrow_angle + 0.5).cos(),
            arrow_tip.y - 2.0 * (arrow_angle + 0.5).sin(),
        );
        let arrow_base2 = Point::new(
            arrow_tip.x - 2.0 * (arrow_angle - 0.5).cos(),
            arrow_tip.y - 2.0 * (arrow_angle - 0.5).sin(),
        );

        ctx.draw_line(arrow_tip, arrow_base1, color, 1.0);
        ctx.draw_line(arrow_tip, arrow_base2, color, 1.0);

        Ok(())
    }

    /// Draw a star
    fn draw_star(
        &self,
        ctx: &mut RenderContext,
        center: Point,
        radius: f64,
        color: Color,
    ) -> Result<()> {
        let mut path = Path::new();
        let points = 5;
        let inner_radius = radius * 0.4;

        for i in 0..(points * 2) {
            let angle = (i as f64 / (points * 2) as f64) * 2.0 * std::f64::consts::PI
                - std::f64::consts::PI / 2.0;
            let r = if i % 2 == 0 { radius } else { inner_radius };
            let x = center.x + r * angle.cos();
            let y = center.y + r * angle.sin();
            let point = Point::new(x, y);

            if i == 0 {
                path = path.move_to(point);
            } else {
                path = path.line_to(point);
            }
        }
        path = path.close();

        ctx.draw_path(path, Some(color), None);
        Ok(())
    }
}

/// Convenient constructors
impl Toolbox {
    /// Create a minimal toolbox with save and restore
    pub fn minimal() -> Self {
        let mut toolbox = Self::new();
        toolbox.features.clear();

        toolbox.features.insert(
            "saveAsImage".to_string(),
            ToolboxFeature {
                enabled: true,
                feature_type: FeatureType::SaveAsImage,
                icon: None,
                title: "Save".to_string(),
                options: FeatureOptions {
                    save_as_image: Some(SaveAsImageOptions::default()),
                    ..FeatureOptions::default()
                },
            },
        );

        toolbox.features.insert(
            "restore".to_string(),
            ToolboxFeature {
                enabled: true,
                feature_type: FeatureType::Restore,
                icon: None,
                title: "Reset".to_string(),
                options: FeatureOptions::default(),
            },
        );

        toolbox
    }

    /// Create a full-featured toolbox
    pub fn full() -> Self {
        let mut toolbox = Self::new();

        // Add data view
        toolbox.features.insert(
            "dataView".to_string(),
            ToolboxFeature {
                enabled: true,
                feature_type: FeatureType::DataView,
                icon: None,
                title: "Data View".to_string(),
                options: FeatureOptions {
                    data_view: Some(DataViewOptions {
                        read_only: false,
                        lang: vec![
                            "Data View".to_string(),
                            "Close".to_string(),
                            "Refresh".to_string(),
                        ],
                        background_color: Color::WHITE,
                        text_color: Color::BLACK,
                        text_area_color: Color::WHITE,
                        text_area_border_color: Color::from_rgba(204, 204, 204, 255),
                        button_color: Color::from_rgba(64, 158, 255, 255),
                        button_text_color: Color::WHITE,
                    }),
                    ..FeatureOptions::default()
                },
            },
        );

        // Add magic type
        toolbox.features.insert(
            "magicType".to_string(),
            ToolboxFeature {
                enabled: true,
                feature_type: FeatureType::MagicType,
                icon: None,
                title: "Switch Chart Type".to_string(),
                options: FeatureOptions {
                    magic_type: Some(MagicTypeOptions {
                        types: vec![MagicChartType::Line, MagicChartType::Bar],
                        type_options: HashMap::new(),
                    }),
                    ..FeatureOptions::default()
                },
            },
        );

        toolbox
    }
}
