//! 性能优化模块
//! 
//! 提供各种性能优化功能，包括数据采样、内存管理、GPU加速等

use crate::DataPoint;
use std::collections::VecDeque;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::time::{Duration, Instant};

/// 性能配置
#[derive(Debug, Clone)]
pub struct PerformanceConfig {
    /// 最大数据点数量
    pub max_data_points: usize,
    /// 启用GPU加速
    pub enable_gpu_acceleration: bool,
    /// 内存池大小
    pub memory_pool_size: usize,
    /// 数据采样策略
    pub sampling_strategy: SamplingStrategy,
    /// 渲染优化级别
    pub optimization_level: OptimizationLevel,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            max_data_points: 10_000,
            enable_gpu_acceleration: true,
            memory_pool_size: 1024 * 1024, // 1MB
            sampling_strategy: SamplingStrategy::Adaptive,
            optimization_level: OptimizationLevel::Balanced,
        }
    }
}

/// 数据采样策略
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum SamplingStrategy {
    /// 无采样
    None,
    /// 均匀采样
    Uniform,
    /// 自适应采样
    Adaptive,
    /// 重要性采样
    Importance,
}

/// 优化级别
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum OptimizationLevel {
    /// 无优化
    None,
    /// 基础优化
    Basic,
    /// 平衡优化
    Balanced,
    /// 激进优化
    Aggressive,
}

/// 数据采样器
pub struct DataSampler {
    strategy: SamplingStrategy,
    max_points: usize,
    #[allow(dead_code)]
    importance_threshold: f64,
}

impl DataSampler {
    /// 创建新的数据采样器
    pub fn new(strategy: SamplingStrategy, max_points: usize) -> Self {
        Self {
            strategy,
            max_points,
            importance_threshold: 0.1,
        }
    }

    /// 对数据进行采样
    pub fn sample(&self, data: &[DataPoint]) -> Vec<DataPoint> {
        if data.len() <= self.max_points {
            return data.to_vec();
        }

        match self.strategy {
            SamplingStrategy::None => data.to_vec(),
            SamplingStrategy::Uniform => self.uniform_sampling(data),
            SamplingStrategy::Adaptive => self.adaptive_sampling(data),
            SamplingStrategy::Importance => self.importance_sampling(data),
        }
    }

    /// 均匀采样
    fn uniform_sampling(&self, data: &[DataPoint]) -> Vec<DataPoint> {
        let step = data.len() / self.max_points;
        if step <= 1 {
            return data.to_vec();
        }

        data.iter()
            .step_by(step)
            .cloned()
            .take(self.max_points)
            .collect()
    }

    /// 自适应采样
    fn adaptive_sampling(&self, data: &[DataPoint]) -> Vec<DataPoint> {
        if data.len() <= self.max_points {
            return data.to_vec();
        }

        let mut result = Vec::with_capacity(self.max_points);
        let step = data.len() as f64 / self.max_points as f64;
        
        for i in 0..self.max_points {
            let index = (i as f64 * step) as usize;
            if index < data.len() {
                result.push(data[index].clone());
            }
        }

        // 确保包含第一个和最后一个点
        if !result.is_empty() && !data.is_empty() {
            result[0] = data[0].clone();
            if result.len() > 1 {
                let last_index = result.len() - 1;
                result[last_index] = data[data.len() - 1].clone();
            }
        }

        result
    }

    /// 重要性采样
    fn importance_sampling(&self, data: &[DataPoint]) -> Vec<DataPoint> {
        if data.len() <= self.max_points {
            return data.to_vec();
        }

        // 计算每个点的重要性（基于变化率）
        let mut importance_scores = vec![0.0; data.len()];
        
        for i in 1..data.len() - 1 {
            let prev_y = data[i - 1].y();
            let curr_y = data[i].y();
            let next_y = data[i + 1].y();
            
            // 计算二阶导数作为重要性指标
            let second_derivative = (next_y - 2.0 * curr_y + prev_y).abs();
            importance_scores[i] = second_derivative;
        }

        // 选择重要性最高的点
        let mut indexed_scores: Vec<(usize, f64)> = importance_scores
            .into_iter()
            .enumerate()
            .collect();
        
        indexed_scores.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));
        
        let mut selected_indices: Vec<usize> = indexed_scores
            .into_iter()
            .take(self.max_points)
            .map(|(index, _)| index)
            .collect();
        
        selected_indices.sort();
        
        selected_indices
            .into_iter()
            .map(|i| data[i].clone())
            .collect()
    }
}

/// 内存池管理器
pub struct MemoryPool {
    pool: VecDeque<Vec<u8>>,
    total_size: AtomicUsize,
    max_size: usize,
}

impl MemoryPool {
    /// 创建新的内存池
    pub fn new(max_size: usize) -> Self {
        Self {
            pool: VecDeque::new(),
            total_size: AtomicUsize::new(0),
            max_size,
        }
    }

    /// 分配内存
    pub fn allocate(&mut self, size: usize) -> Vec<u8> {
        if let Some(mut buffer) = self.pool.pop_front() {
            if buffer.capacity() >= size {
                buffer.clear();
                buffer.resize(size, 0);
                return buffer;
            }
        }

        let buffer = vec![0; size];
        self.total_size.fetch_add(size, Ordering::Relaxed);
        buffer
    }

    /// 释放内存
    pub fn deallocate(&mut self, buffer: Vec<u8>) {
        let size = buffer.capacity();
        
        if self.total_size.load(Ordering::Relaxed) + size <= self.max_size {
            self.pool.push_back(buffer);
        } else {
            self.total_size.fetch_sub(size, Ordering::Relaxed);
        }
    }

    /// 获取当前使用的内存大小
    pub fn current_size(&self) -> usize {
        self.total_size.load(Ordering::Relaxed)
    }

    /// 清理内存池
    pub fn clear(&mut self) {
        self.pool.clear();
        self.total_size.store(0, Ordering::Relaxed);
    }
}

/// 性能监控器
pub struct PerformanceMonitor {
    start_time: Instant,
    frame_times: VecDeque<Duration>,
    max_frame_history: usize,
    total_frames: u64,
}

impl PerformanceMonitor {
    /// 创建新的性能监控器
    pub fn new() -> Self {
        Self {
            start_time: Instant::now(),
            frame_times: VecDeque::new(),
            max_frame_history: 60, // 保留最近60帧
            total_frames: 0,
        }
    }

    /// 记录帧时间
    pub fn record_frame(&mut self, frame_time: Duration) {
        self.frame_times.push_back(frame_time);
        self.total_frames += 1;

        if self.frame_times.len() > self.max_frame_history {
            self.frame_times.pop_front();
        }
    }

    /// 获取平均FPS
    pub fn average_fps(&self) -> f64 {
        if self.frame_times.is_empty() {
            return 0.0;
        }

        let total_time: Duration = self.frame_times.iter().sum();
        let average_frame_time = total_time / self.frame_times.len() as u32;
        
        if average_frame_time.as_secs_f64() > 0.0 {
            1.0 / average_frame_time.as_secs_f64()
        } else {
            0.0
        }
    }

    /// 获取最小FPS
    pub fn min_fps(&self) -> f64 {
        if let Some(max_frame_time) = self.frame_times.iter().max() {
            if max_frame_time.as_secs_f64() > 0.0 {
                1.0 / max_frame_time.as_secs_f64()
            } else {
                0.0
            }
        } else {
            0.0
        }
    }

    /// 获取最大FPS
    pub fn max_fps(&self) -> f64 {
        if let Some(min_frame_time) = self.frame_times.iter().min() {
            if min_frame_time.as_secs_f64() > 0.0 {
                1.0 / min_frame_time.as_secs_f64()
            } else {
                0.0
            }
        } else {
            0.0
        }
    }

    /// 获取总运行时间
    pub fn total_runtime(&self) -> Duration {
        self.start_time.elapsed()
    }

    /// 获取总帧数
    pub fn total_frames(&self) -> u64 {
        self.total_frames
    }

    /// 重置统计信息
    pub fn reset(&mut self) {
        self.start_time = Instant::now();
        self.frame_times.clear();
        self.total_frames = 0;
    }
}

/// 性能统计信息
#[derive(Debug, Clone)]
pub struct PerformanceStats {
    pub average_fps: f64,
    pub min_fps: f64,
    pub max_fps: f64,
    pub total_runtime: Duration,
    pub total_frames: u64,
    pub memory_usage: usize,
    pub data_points_processed: usize,
    pub optimization_level: OptimizationLevel,
}

impl PerformanceStats {
    /// 创建新的性能统计
    pub fn new() -> Self {
        Self {
            average_fps: 0.0,
            min_fps: 0.0,
            max_fps: 0.0,
            total_runtime: Duration::ZERO,
            total_frames: 0,
            memory_usage: 0,
            data_points_processed: 0,
            optimization_level: OptimizationLevel::None,
        }
    }

    /// 从监控器更新统计信息
    pub fn update_from_monitor(&mut self, monitor: &PerformanceMonitor, memory_usage: usize) {
        self.average_fps = monitor.average_fps();
        self.min_fps = monitor.min_fps();
        self.max_fps = monitor.max_fps();
        self.total_runtime = monitor.total_runtime();
        self.total_frames = monitor.total_frames();
        self.memory_usage = memory_usage;
    }
}

impl Default for PerformanceStats {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::DataValue;

    #[test]
    fn test_data_sampler_uniform() {
        let data: Vec<DataPoint> = (0..1000).map(|i| {
            DataPoint::new(vec![
                DataValue::Number(i as f64),
                DataValue::Number((i * 2) as f64)
            ])
        }).collect();

        let sampler = DataSampler::new(SamplingStrategy::Uniform, 100);
        let sampled = sampler.sample(&data);

        assert_eq!(sampled.len(), 100);
        assert_eq!(sampled[0].x(), 0.0);
    }

    #[test]
    fn test_memory_pool() {
        let mut pool = MemoryPool::new(1024);
        
        let buffer1 = pool.allocate(256);
        assert_eq!(buffer1.len(), 256);
        
        let buffer2 = pool.allocate(512);
        assert_eq!(buffer2.len(), 512);
        
        pool.deallocate(buffer1);
        pool.deallocate(buffer2);
        
        assert!(pool.current_size() <= 1024);
    }

    #[test]
    fn test_performance_monitor() {
        let mut monitor = PerformanceMonitor::new();
        
        monitor.record_frame(Duration::from_millis(16)); // ~60 FPS
        monitor.record_frame(Duration::from_millis(33)); // ~30 FPS
        
        let avg_fps = monitor.average_fps();
        assert!(avg_fps > 0.0);
        assert!(avg_fps < 100.0);
    }

    #[test]
    fn test_adaptive_sampling() {
        let data: Vec<DataPoint> = (0..1000).map(|i| {
            let x = i as f64;
            let y = (x * 0.01).sin() * 100.0;
            DataPoint::new(vec![DataValue::Number(x), DataValue::Number(y)])
        }).collect();

        let sampler = DataSampler::new(SamplingStrategy::Adaptive, 100);
        let sampled = sampler.sample(&data);

        assert_eq!(sampled.len(), 100);
        // 第一个和最后一个点应该被保留
        assert_eq!(sampled[0].x(), data[0].x());
        assert_eq!(sampled[sampled.len() - 1].x(), data[data.len() - 1].x());
    }
}
