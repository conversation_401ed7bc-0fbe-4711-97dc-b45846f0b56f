{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 11882131123028485468, "profile": 2225463790103693989, "path": 9725413575319894988, "deps": [[3060637413840920116, "proc_macro2", false, 14205022684772405266], [4974441333307933176, "syn", false, 443193271066395349], [17990358020177143287, "quote", false, 11630545452024400737]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\nalgebra-macros-903462a57ac51b10\\dep-lib-nalgebra_macros", "checksum": false}}], "rustflags": ["-C", "link-arg=/STACK:16000000"], "config": 2069994364910194474, "compile_kind": 0}