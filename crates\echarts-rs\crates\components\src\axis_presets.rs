//! 坐标轴配置预设
//!
//! 提供常用的坐标轴配置，如时间轴、对数轴、分类轴等

use crate::axis::{Axis, AxisPosition, AxisType};
use echarts_core::{Color, LineStyle, TextStyle};

/// 坐标轴预设配置
pub struct AxisPresets;

impl AxisPresets {
    /// 创建标准数值X轴
    pub fn standard_x_axis() -> Axis {
        let mut axis = Axis::value().position(AxisPosition::Bottom);
        axis.show_line = true;
        axis.show_ticks = true;
        axis.show_labels = true;
        axis.line_style = LineStyle {
            color: Color::rgb(0.2, 0.2, 0.2),
            width: 2.0,
            ..Default::default()
        };
        axis.label_text_style = TextStyle {
            color: Color::rgb(0.3, 0.3, 0.3),
            font_size: 12.0,
            ..Default::default()
        };
        axis
    }

    /// 创建标准数值Y轴
    pub fn standard_y_axis() -> Axis {
        let mut axis = Axis::value().position(AxisPosition::Left);
        axis.show_line = true;
        axis.show_ticks = true;
        axis.show_labels = true;
        axis.line_style = LineStyle {
            color: Color::rgb(0.2, 0.2, 0.2),
            width: 2.0,
            ..Default::default()
        };
        axis.label_text_style = TextStyle {
            color: Color::rgb(0.3, 0.3, 0.3),
            font_size: 12.0,
            ..Default::default()
        };
        axis
    }

    /// 创建时间轴（X轴）
    pub fn time_x_axis() -> Axis {
        let mut axis = Self::standard_x_axis();
        axis.axis_type = AxisType::Time;
        axis.name = Some("时间".to_string());
        axis
    }

    /// 创建分类X轴
    pub fn category_x_axis(categories: Vec<String>) -> Axis {
        let mut axis = Axis::category().position(AxisPosition::Bottom);
        axis.categories = categories;
        axis.show_line = true;
        axis.show_ticks = true;
        axis.show_labels = true;
        axis.line_style = LineStyle {
            color: Color::rgb(0.2, 0.2, 0.2),
            width: 2.0,
            ..Default::default()
        };
        axis.label_text_style = TextStyle {
            color: Color::rgb(0.3, 0.3, 0.3),
            font_size: 12.0,
            ..Default::default()
        };
        axis
    }

    /// 创建对数Y轴
    pub fn log_y_axis() -> Axis {
        let mut axis = Self::standard_y_axis();
        axis.axis_type = AxisType::Log;
        axis.name = Some("对数值".to_string());
        axis.min = Some(1.0); // 对数轴最小值不能为0
        axis
    }

    /// 创建百分比Y轴
    pub fn percentage_y_axis() -> Axis {
        let mut axis = Self::standard_y_axis();
        axis.name = Some("百分比 (%)".to_string());
        axis.min = Some(0.0);
        axis.max = Some(100.0);
        axis
    }

    /// 创建温度轴（带单位）
    pub fn temperature_y_axis() -> Axis {
        let mut axis = Self::standard_y_axis();
        axis.name = Some("温度 (°C)".to_string());
        axis
    }

    /// 创建压力轴（带单位）
    pub fn pressure_y_axis() -> Axis {
        let mut axis = Self::standard_y_axis();
        axis.name = Some("压力 (Pa)".to_string());
        axis.min = Some(0.0);
        axis
    }

    /// 创建金融价格轴
    pub fn price_y_axis() -> Axis {
        let mut axis = Self::standard_y_axis();
        axis.name = Some("价格".to_string());
        axis.min = Some(0.0);
        // 金融数据通常需要更精确的刻度
        axis.label_text_style.font_size = 10.0;
        axis
    }

    /// 创建数据量轴（支持大数值）
    pub fn data_volume_y_axis() -> Axis {
        let mut axis = Self::standard_y_axis();
        axis.name = Some("数据量".to_string());
        axis.min = Some(0.0);
        // 可能需要科学计数法显示
        axis
    }

    /// 创建角度轴（0-360度）
    pub fn angle_axis() -> Axis {
        let mut axis = Self::standard_x_axis();
        axis.name = Some("角度 (°)".to_string());
        axis.min = Some(0.0);
        axis.max = Some(360.0);
        axis
    }

    /// 创建月份分类轴
    pub fn month_category_axis() -> Axis {
        let months = vec![
            "1月".to_string(), "2月".to_string(), "3月".to_string(),
            "4月".to_string(), "5月".to_string(), "6月".to_string(),
            "7月".to_string(), "8月".to_string(), "9月".to_string(),
            "10月".to_string(), "11月".to_string(), "12月".to_string(),
        ];
        Self::category_x_axis(months)
    }

    /// 创建星期分类轴
    pub fn weekday_category_axis() -> Axis {
        let weekdays = vec![
            "周一".to_string(), "周二".to_string(), "周三".to_string(),
            "周四".to_string(), "周五".to_string(), "周六".to_string(),
            "周日".to_string(),
        ];
        Self::category_x_axis(weekdays)
    }

    /// 创建评分轴（1-5星）
    pub fn rating_axis() -> Axis {
        let mut axis = Self::standard_x_axis();
        axis.name = Some("评分".to_string());
        axis.min = Some(1.0);
        axis.max = Some(5.0);
        axis
    }

    /// 创建年龄分组轴
    pub fn age_group_axis() -> Axis {
        let age_groups = vec![
            "0-18".to_string(), "19-30".to_string(), "31-45".to_string(),
            "46-60".to_string(), "60+".to_string(),
        ];
        Self::category_x_axis(age_groups)
    }

    /// 创建自定义范围的数值轴
    pub fn custom_range_axis(min: f64, max: f64, position: AxisPosition, name: Option<String>) -> Axis {
        let mut axis = match position {
            AxisPosition::Bottom | AxisPosition::Top => Self::standard_x_axis(),
            AxisPosition::Left | AxisPosition::Right => Self::standard_y_axis(),
        };
        axis.position = position;
        axis.min = Some(min);
        axis.max = Some(max);
        axis.name = name;
        axis
    }

    /// 创建科学计数法轴
    pub fn scientific_notation_axis() -> Axis {
        let mut axis = Self::standard_y_axis();
        axis.name = Some("科学计数".to_string());
        // TODO: 添加科学计数法格式化支持
        axis
    }

    /// 创建货币轴
    pub fn currency_axis(currency_symbol: &str) -> Axis {
        let mut axis = Self::standard_y_axis();
        axis.name = Some(format!("金额 ({})", currency_symbol));
        axis.min = Some(0.0);
        axis
    }
}

/// 轴预设的便捷函数
pub mod presets {
    use super::*;

    /// 标准XY轴对
    pub fn standard_xy_axes() -> (Axis, Axis) {
        (AxisPresets::standard_x_axis(), AxisPresets::standard_y_axis())
    }

    /// 时间-数值轴对
    pub fn time_value_axes() -> (Axis, Axis) {
        (AxisPresets::time_x_axis(), AxisPresets::standard_y_axis())
    }

    /// 分类-数值轴对
    pub fn category_value_axes(categories: Vec<String>) -> (Axis, Axis) {
        (AxisPresets::category_x_axis(categories), AxisPresets::standard_y_axis())
    }

    /// 月份-数值轴对
    pub fn month_value_axes() -> (Axis, Axis) {
        (AxisPresets::month_category_axis(), AxisPresets::standard_y_axis())
    }

    /// 星期-数值轴对
    pub fn weekday_value_axes() -> (Axis, Axis) {
        (AxisPresets::weekday_category_axis(), AxisPresets::standard_y_axis())
    }

    /// 金融图表轴对（时间-价格）
    pub fn financial_axes() -> (Axis, Axis) {
        (AxisPresets::time_x_axis(), AxisPresets::price_y_axis())
    }

    /// 科学数据轴对（标准X-对数Y）
    pub fn scientific_axes() -> (Axis, Axis) {
        (AxisPresets::standard_x_axis(), AxisPresets::log_y_axis())
    }

    /// 温度监控轴对（时间-温度）
    pub fn temperature_monitoring_axes() -> (Axis, Axis) {
        (AxisPresets::time_x_axis(), AxisPresets::temperature_y_axis())
    }

    /// 评分分析轴对（分类-评分）
    pub fn rating_analysis_axes(categories: Vec<String>) -> (Axis, Axis) {
        (AxisPresets::category_x_axis(categories), AxisPresets::rating_axis())
    }
}
