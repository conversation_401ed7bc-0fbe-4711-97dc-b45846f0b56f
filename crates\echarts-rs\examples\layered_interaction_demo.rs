//! 分层架构交互演示
//!
//! 展示新的分层交互架构如何工作：
//! - 底层：通用交互事件 (professional_interactions.rs)
//! - 适配层：事件转换和协调 (adapter.rs)
//! - 中层：图表特定交互 (line.rs 实现 ChartInteraction)
//! - 上层：统一交互管理 (InteractionManager)

use echarts_rs::{LineSeries, Color, CartesianCoordinateSystem, Bounds as EchartsBounds};
use echarts_charts::line::LabelFormatType;
use echarts_interaction::{InteractionManager, InteractionConfig, ChartInteraction};
use echarts_core::professional_interactions::{InteractionEvent as BaseInteractionEvent, MouseButton, KeyModifiers};
use echarts_core::Point;
use gpui::*;
use gpui_component::StyledExt;

fn main() {
    println!("🚀 启动分层架构交互演示...");

    let app = Application::new();
    app.run(move |cx| {
        println!("📱 应用程序上下文已创建");

        let mut window_size = size(px(1200.0), px(800.0));
        if let Some(display) = cx.primary_display() {
            let display_size = display.bounds().size;
            window_size.width = window_size.width.min(display_size.width * 0.9);
            window_size.height = window_size.height.min(display_size.height * 0.9);
            println!("🖥️  显示器大小: {:?}, 窗口大小: {:?}", display_size, window_size);
        }
        let window_bounds = gpui::Bounds::centered(None, window_size, cx);

        cx.spawn(async move |cx| {
            let options = WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(window_bounds)),
                titlebar: Some(TitlebarOptions {
                    title: Some("🏗️ 分层架构交互演示 - ECharts-rs".into()),
                    appears_transparent: false,
                    traffic_light_position: None,
                }),
                window_min_size: Some(size(px(800.0), px(600.0))),
                window_background: WindowBackgroundAppearance::Opaque,
                window_decorations: None,
                focus: true,
                show: true,
                kind: WindowKind::Normal,
                is_movable: true,
                display_id: None,
                app_id: Some("layered-interaction-demo".to_string()),
            };

            cx.update(|cx| {
                cx.open_window(options, |window, cx| {
                    println!("✅ 窗口已创建，正在初始化分层架构演示...");
                    cx.new(|_cx| LayeredInteractionDemo::new())
                })
                .expect("无法创建窗口");
            })
            .ok();
        })
        .detach();
    });
}

/// 分层架构交互演示应用
struct LayeredInteractionDemo {
    /// 上层：统一交互管理器
    interaction_manager: InteractionManager,
    
    /// 中层：图表实例
    line_chart: LineSeries,
    
    /// 演示状态
    demo_state: DemoState,
    
    /// 事件日志
    event_log: Vec<String>,
}

/// 演示状态
#[derive(Debug, Clone)]
struct DemoState {
    current_layer: InteractionLayer,
    mouse_position: Option<Point>,
    last_event: Option<String>,
    interaction_count: usize,
}

/// 交互层级
#[derive(Debug, Clone, PartialEq)]
enum InteractionLayer {
    Bottom,  // 底层通用事件
    Adapter, // 适配层转换
    Chart,   // 中层图表特定
    Manager, // 上层统一管理
}

impl InteractionLayer {
    fn name(&self) -> &'static str {
        match self {
            InteractionLayer::Bottom => "底层：通用交互事件",
            InteractionLayer::Adapter => "适配层：事件转换协调",
            InteractionLayer::Chart => "中层：图表特定交互",
            InteractionLayer::Manager => "上层：统一交互管理",
        }
    }
    
    fn description(&self) -> &'static str {
        match self {
            InteractionLayer::Bottom => "处理原始鼠标、键盘、触摸事件",
            InteractionLayer::Adapter => "转换通用事件为图表特定事件",
            InteractionLayer::Chart => "处理数据点查找、高亮、选择",
            InteractionLayer::Manager => "协调所有交互，管理全局状态",
        }
    }
    
    fn color(&self) -> Color {
        match self {
            InteractionLayer::Bottom => Color::rgb(0.9, 0.3, 0.3),
            InteractionLayer::Adapter => Color::rgb(0.9, 0.6, 0.2),
            InteractionLayer::Chart => Color::rgb(0.3, 0.7, 0.9),
            InteractionLayer::Manager => Color::rgb(0.4, 0.8, 0.4),
        }
    }
}

impl LayeredInteractionDemo {
    fn new() -> Self {
        println!("🎯 初始化分层架构交互演示...");

        // 创建示例数据
        let data = vec![
            (1.0, 20.0), (2.0, 35.0), (3.0, 25.0), (4.0, 60.0),
            (5.0, 45.0), (6.0, 80.0), (7.0, 65.0), (8.0, 90.0),
            (9.0, 75.0), (10.0, 85.0)
        ];

        // 中层：创建图表实例
        let mut line_chart = LineSeries::new("分层架构演示数据")
            .data(data)
            .color(Color::rgb(0.2, 0.6, 1.0))
            .line_width(3.0)
            .show_symbols(true)
            .symbol_size(8.0)
            .x_axis_decimal_places(1)
            .y_axis_decimal_places(0)
            .smooth(true)
            .smoothness(0.4);

        // 设置图表边界
        line_chart.bounds = Some(echarts_core::Bounds::new(50.0, 50.0, 750.0, 400.0));

        // 上层：创建交互管理器
        let config = InteractionConfig::default();
        let mut interaction_manager = InteractionManager::new(config);
        
        // 注册图表到交互管理器
        interaction_manager.register_chart(Box::new(line_chart.clone()));

        println!("📊 创建了分层架构交互系统");

        Self {
            interaction_manager,
            line_chart,
            demo_state: DemoState {
                current_layer: InteractionLayer::Manager,
                mouse_position: None,
                last_event: None,
                interaction_count: 0,
            },
            event_log: Vec::new(),
        }
    }

    /// 模拟底层事件输入
    fn simulate_mouse_move(&mut self, position: Point) {
        self.demo_state.mouse_position = Some(position);
        self.demo_state.interaction_count += 1;
        
        // 创建底层事件
        let base_event = BaseInteractionEvent::MouseMove {
            position,
            modifiers: KeyModifiers::default(),
        };
        
        // 通过上层管理器处理
        let results = self.interaction_manager.handle_input_event(base_event);
        
        // 记录事件
        let event_desc = format!("鼠标移动到 ({:.1}, {:.1}) - 产生 {} 个结果", 
            position.x, position.y, results.len());
        self.demo_state.last_event = Some(event_desc.clone());
        self.event_log.push(event_desc);
        
        // 保持日志在合理大小
        if self.event_log.len() > 10 {
            self.event_log.remove(0);
        }
        
        println!("🖱️  {}", self.demo_state.last_event.as_ref().unwrap());
    }
    
    /// 模拟点击事件
    fn simulate_mouse_click(&mut self, position: Point) {
        self.demo_state.interaction_count += 1;
        
        // 创建底层事件
        let base_event = BaseInteractionEvent::MouseClick {
            position,
            button: MouseButton::Left,
            modifiers: KeyModifiers::default(),
        };
        
        // 通过上层管理器处理
        let results = self.interaction_manager.handle_input_event(base_event);
        
        // 记录事件
        let event_desc = format!("鼠标点击 ({:.1}, {:.1}) - 产生 {} 个结果", 
            position.x, position.y, results.len());
        self.demo_state.last_event = Some(event_desc.clone());
        self.event_log.push(event_desc);
        
        if self.event_log.len() > 10 {
            self.event_log.remove(0);
        }
        
        println!("🖱️  {}", self.demo_state.last_event.as_ref().unwrap());
    }
    
    /// 切换当前展示的层级
    fn switch_layer(&mut self) {
        self.demo_state.current_layer = match self.demo_state.current_layer {
            InteractionLayer::Bottom => InteractionLayer::Adapter,
            InteractionLayer::Adapter => InteractionLayer::Chart,
            InteractionLayer::Chart => InteractionLayer::Manager,
            InteractionLayer::Manager => InteractionLayer::Bottom,
        };
        
        println!("🔄 切换到: {}", self.demo_state.current_layer.name());
    }
}

impl Render for LayeredInteractionDemo {
    fn render(&mut self, _window: &mut Window, _cx: &mut Context<Self>) -> impl IntoElement {
        println!("🎨 渲染分层架构交互演示界面...");

        // 模拟一些交互事件用于演示
        if self.demo_state.interaction_count < 5 {
            let x = 100.0 + (self.demo_state.interaction_count as f64 * 100.0);
            let y = 200.0 + (self.demo_state.interaction_count as f64 * 10.0);
            self.simulate_mouse_move(Point::new(x, y));
        }

        div()
            .size_full()
            .bg(rgb(0xf8fafc))
            .flex()
            .flex_col()
            .child(
                // 标题栏
                div()
                    .w_full()
                    .h(px(80.0))
                    .bg(rgb(0x1f2937))
                    .flex()
                    .items_center()
                    .justify_center()
                    .child(
                        div()
                            .text_2xl()
                            .font_bold()
                            .text_color(rgb(0xffffff))
                            .child("🏗️ 分层架构交互演示")
                    )
            )
            .child(
                // 当前层级信息
                div()
                    .w_full()
                    .p_4()
                    .bg(self.demo_state.current_layer.color().to_gpui_color())
                    .flex()
                    .justify_center()
                    .child(
                        div()
                            .text_center()
                            .child(
                                div()
                                    .text_lg()
                                    .font_bold()
                                    .text_color(rgb(0xffffff))
                                    .mb_1()
                                    .child(self.demo_state.current_layer.name())
                            )
                            .child(
                                div()
                                    .text_sm()
                                    .text_color(rgb(0xf0f0f0))
                                    .child(self.demo_state.current_layer.description())
                            )
                    )
            )
            .child(
                // 主要内容区域
                div()
                    .flex_1()
                    .p_6()
                    .flex()
                    .gap_6()
                    .child(
                        // 左侧：图表区域
                        div()
                            .flex_1()
                            .bg(rgb(0xffffff))
                            .border_1()
                            .border_color(rgb(0xd1d5db))
                            .rounded_lg()
                            .p_6()
                            .child(
                                div()
                                    .w_full()
                                    .h(px(400.0))
                                    .bg(rgb(0xf9fafb))
                                    .border_1()
                                    .border_color(rgb(0xe5e7eb))
                                    .rounded_md()
                                    .flex()
                                    .items_center()
                                    .justify_center()
                                    .child(
                                        div()
                                            .text_center()
                                            .child(
                                                div()
                                                    .text_2xl()
                                                    .mb_4()
                                                    .child("📈")
                                            )
                                            .child(
                                                div()
                                                    .text_xl()
                                                    .font_semibold()
                                                    .text_color(rgb(0x1f2937))
                                                    .mb_2()
                                                    .child("交互式折线图")
                                            )
                                            .child(
                                                div()
                                                    .text_sm()
                                                    .text_color(rgb(0x6b7280))
                                                    .child("🎨 分层架构渲染区域")
                                            )
                                    )
                            )
                    )
                    .child(
                        // 右侧：事件日志
                        div()
                            .w(px(300.0))
                            .bg(rgb(0xffffff))
                            .border_1()
                            .border_color(rgb(0xd1d5db))
                            .rounded_lg()
                            .p_4()
                            .flex()
                            .flex_col()
                            .child(
                                div()
                                    .text_lg()
                                    .font_bold()
                                    .text_color(rgb(0x1f2937))
                                    .mb_4()
                                    .child("📋 事件日志")
                            )
                            .child(
                                div()
                                    .flex_1()
                                    .overflow_y_auto()
                                    .child(
                                        div()
                                            .flex()
                                            .flex_col()
                                            .gap_2()
                                            .children(
                                                self.event_log.iter().enumerate().map(|(i, event)| {
                                                    div()
                                                        .p_2()
                                                        .bg(if i == self.event_log.len() - 1 {
                                                            rgb(0xe0f2fe)
                                                        } else {
                                                            rgb(0xf9fafb)
                                                        })
                                                        .border_1()
                                                        .border_color(rgb(0xe5e7eb))
                                                        .rounded_md()
                                                        .child(
                                                            div()
                                                                .text_xs()
                                                                .text_color(rgb(0x374151))
                                                                .child(event.clone())
                                                        )
                                                }).collect::<Vec<_>>()
                                            )
                                    )
                            )
                    )
            )
            .child(
                // 状态栏
                div()
                    .w_full()
                    .h(px(50.0))
                    .bg(rgb(0x374151))
                    .flex()
                    .items_center()
                    .justify_between()
                    .px_6()
                    .child(
                        div()
                            .text_sm()
                            .text_color(rgb(0x9ca3af))
                            .child(format!("🎯 当前层级: {} | 交互次数: {}", 
                                self.demo_state.current_layer.name(),
                                self.demo_state.interaction_count
                            ))
                    )
                    .child(
                        div()
                            .text_sm()
                            .text_color(rgb(0x9ca3af))
                            .child("✅ 分层架构交互系统运行中")
                    )
            )
    }
}

// 扩展 Color 以支持转换为 GPUI 颜色
trait ColorExt {
    fn to_gpui_color(&self) -> gpui::Rgba;
}

impl ColorExt for Color {
    fn to_gpui_color(&self) -> gpui::Rgba {
        gpui::rgba(self.r as f32, self.g as f32, self.b as f32, self.a as f32)
    }
}
