use anyhow::{Context as _, Result};
use gpui::*;
use gpui_component::{
    button::{Button, ButtonVariants as _},
    dock::{
        ClosePanel, DockArea, DockAreaState, DockEvent, DockItem, DockPlacement, PanelView,
        ToggleZoom,
    },
    popup_menu::PopupMenuExt,
    v_flex, ActiveTheme as _, IconName, Root, Sizable, Theme,
};

use crate::{
    fscdaq_init, AppState, AppStatusBar, AppTitleBar, ButtonStory, EchartsStory, Open, PanelId,
    PanelManager, StoryContainer, SvgName,
};

use crate::{TogglePanelAction, TogglePanelVisible};
use serde::Deserialize;
use std::{sync::Arc, time::Duration};

use rust_i18n::t;

#[derive(Action, Clone, PartialEq, Eq, Deserialize)]
#[action(namespace = story, no_json)]
pub struct AddPanel(DockPlacement);

actions!(story, [ToggleDockToggleButton]);

const MAIN_DOCK_AREA: DockAreaTab = DockAreaTab {
    id: "main-dock",
    version: 5,
};

#[cfg(debug_assertions)]
const STATE_FILE: &str = "target/docks.json";
#[cfg(not(debug_assertions))]
const STATE_FILE: &str = "docks.json";

pub fn init(cx: &mut App) {
    cx.on_action(|_action: &Open, _cx: &mut App| {});
    fscdaq_init(cx);

    cx.bind_keys(vec![
        KeyBinding::new("shift-escape", ToggleZoom, None),
        KeyBinding::new("ctrl-w", ClosePanel, None),
    ]);

    cx.activate(true);
}

pub struct StoryWorkspace {
    title_bar: Entity<AppTitleBar>,
    status_bar: Entity<AppStatusBar>,
    dock_area: Entity<DockArea>,
    last_layout_state: Option<DockAreaState>,
    toggle_button_visible: bool,
    _save_layout_task: Option<Task<()>>,
    left_panel_manager: PanelManager,
}

struct DockAreaTab {
    id: &'static str,
    version: usize,
}

impl StoryWorkspace {
    pub fn new(window: &mut Window, cx: &mut Context<Self>) -> Self {
        let status_bar = cx.new(|cx| AppStatusBar::new(window, cx));
        let dock_area =
            cx.new(|cx| DockArea::new(MAIN_DOCK_AREA.id, Some(MAIN_DOCK_AREA.version), window, cx));

        window
            .observe_window_appearance(|window, cx| {
                Theme::sync_system_appearance(Some(window), cx);
            })
            .detach();

        let weak_dock_area = dock_area.downgrade();

        // 尝试加载布局，失败则重置为默认布局
        // match Self::load_layout(dock_area.clone(), window, cx) {
        //     Ok(_) => {
        //         println!("Layout loaded successfully");
        //     }
        //     Err(err) => {
        //         eprintln!("Failed to load layout: {:?}, resetting to default", err);
        //         Self::reset_default_layout(weak_dock_area.clone(), window, cx);
        //     }
        // };
        Self::reset_default_layout(weak_dock_area.clone(), window, cx);

        cx.subscribe_in(
            &dock_area,
            window,
            |this, dock_area, ev: &DockEvent, window, cx| match ev {
                DockEvent::LayoutChanged => this.save_layout(dock_area, window, cx),
                _ => {}
            },
        )
        .detach();

        cx.on_app_quit({
            let dock_area = dock_area.clone();
            move |_, cx| {
                let state = dock_area.read(cx).dump(cx);
                cx.background_executor().spawn(async move {
                    Self::save_state(&state).unwrap();
                })
            }
        })
        .detach();

        // 创建标题栏并禁用双击功能
        let title_bar =
            cx.new(|cx| AppTitleBar::new("FscDAQ", window, cx).disable_double_click(false));

        // 创建左侧面板管理器并配置Dock集成
        let mut left_panel_manager = PanelManager::new(None)
            .with_dock_placement(DockPlacement::Left)
            .with_dock_area(weak_dock_area.clone());

        //  rust_i18n::set_locale("zh-CN");

        // 注册面板
        left_panel_manager.register_panel(
            PanelId::FileExplorer,
            Arc::new(StoryContainer::panel_with_id::<ButtonStory>(
                t!("FileExplorer.title").to_string(),
                window,
                cx,
            )),
        );

        left_panel_manager.register_panel(
            PanelId::Search,
            Arc::new(StoryContainer::panel_with_id::<ButtonStory>(
                PanelId::Search.to_string(),
                window,
                cx,
            )),
        );

        left_panel_manager.register_panel(
            PanelId::Extensions,
            Arc::new(StoryContainer::panel_with_id::<EchartsStory>(
                "extensions".to_string(),
                window,
                cx,
            )),
        );
        /* 默认显示 file-explorer  面板*/

        _ = left_panel_manager.toggle_visibility(PanelId::FileExplorer, window, cx);

        let status_bar = cx.new(|cx| AppStatusBar::new(window, cx));
        Self {
            dock_area,
            title_bar,
            status_bar,
            last_layout_state: None,
            toggle_button_visible: true,
            _save_layout_task: None,
            left_panel_manager,
        }
    }

    fn save_layout(
        &mut self,
        dock_area: &Entity<DockArea>,
        window: &mut Window,
        cx: &mut Context<Self>,
    ) {
        let dock_area = dock_area.clone();
        self._save_layout_task = Some(cx.spawn_in(window, async move |story, window| {
            Timer::after(Duration::from_secs(10)).await;

            _ = story.update_in(window, move |this, _, cx| {
                let dock_area = dock_area.read(cx);
                let state = dock_area.dump(cx);

                let last_layout_state = this.last_layout_state.clone();
                if Some(&state) == last_layout_state.as_ref() {
                    return;
                }

                Self::save_state(&state).unwrap();
                this.last_layout_state = Some(state);
            });
        }));
    }

    fn save_state(state: &DockAreaState) -> Result<()> {
        println!("Save layout...");
        let json = serde_json::to_string_pretty(state)?;

        // 确保目录存在
        if let Some(parent) = std::path::Path::new(STATE_FILE).parent() {
            if !parent.exists() {
                std::fs::create_dir_all(parent)?;
            }
        }

        std::fs::write(STATE_FILE, json)?;
        Ok(())
    }

    fn load_layout(
        dock_area: Entity<DockArea>,
        window: &mut Window,
        cx: &mut Context<Self>,
    ) -> Result<()> {
        let json = std::fs::read_to_string(STATE_FILE)?;
        let state = serde_json::from_str::<DockAreaState>(&json)?;

        // Check if the saved layout version is different from the current version
        // Notify the user and ask if they want to reset the layout to default.
        if state.version != Some(MAIN_DOCK_AREA.version) {
            let answer = window.prompt(
                PromptLevel::Info,
                "The default main layout has been updated.\n\
                Do you want to reset the layout to default?",
                None,
                &["Yes", "No"],
                cx,
            );

            let weak_dock_area = dock_area.downgrade();
            cx.spawn_in(window, async move |this, window| {
                if answer.await == Ok(0) {
                    _ = this.update_in(window, |_, window, cx| {
                        Self::reset_default_layout(weak_dock_area, window, cx);
                    });
                }
            })
            .detach();
        }

        dock_area.update(cx, |dock_area, cx| {
            dock_area.load(state, window, cx).context("load layout")?;
            dock_area.set_dock_collapsible(
                Edges {
                    left: true,
                    bottom: true,
                    right: true,
                    ..Default::default()
                },
                window,
                cx,
            );

            Ok::<(), anyhow::Error>(())
        })
    }

    fn reset_default_layout(dock_area: WeakEntity<DockArea>, window: &mut Window, cx: &mut App) {
        let dock_item = Self::init_default_layout(&dock_area, window, cx);
        let bottom_panels = DockItem::split_with_sizes(
            Axis::Vertical,
            vec![DockItem::tabs(
                vec![
                    // Arc::new(StoryContainer::panel::<ChartStory>(window, cx)),
                    Arc::new(StoryContainer::panel::<ButtonStory>(window, cx)),
                    // Arc::new(StoryContainer::panel::<ChartStory>(window, cx)),
                    Arc::new(StoryContainer::panel::<EchartsStory>(window, cx)),
                ],
                None,
                &dock_area,
                window,
                cx,
            )],
            vec![None],
            &dock_area,
            window,
            cx,
        );

        let right_panels = DockItem::split_with_sizes(
            Axis::Vertical,
            vec![DockItem::tab(
                StoryContainer::panel::<ButtonStory>(window, cx),
                &dock_area,
                window,
                cx,
            )],
            vec![None],
            &dock_area,
            window,
            cx,
        );
        _ = dock_area.update(cx, |view, cx| {
            view.set_version(MAIN_DOCK_AREA.version, window, cx);
            view.set_center(dock_item, window, cx);
            view.set_bottom_dock(bottom_panels, Some(px(200.)), true, window, cx);
            view.set_right_dock(right_panels, Some(px(320.)), true, window, cx);

            Self::save_state(&view.dump(cx)).unwrap();
        });
    }

    fn init_default_layout(
        dock_area: &WeakEntity<DockArea>,
        window: &mut Window,
        cx: &mut App,
    ) -> DockItem {
        DockItem::split_with_sizes(
            Axis::Vertical,
            vec![DockItem::tabs(
                vec![Arc::new(StoryContainer::panel::<EchartsStory>(window, cx))],
                None,
                &dock_area,
                window,
                cx,
            )],
            vec![None],
            &dock_area,
            window,
            cx,
        )
    }

    pub fn new_local(cx: &mut App) -> Task<anyhow::Result<WindowHandle<Root>>> {
        let mut window_size = size(px(1600.0), px(1200.0));
        if let Some(display) = cx.primary_display() {
            let display_size = display.bounds().size;
            window_size.width = window_size.width.min(display_size.width * 0.85);
            window_size.height = window_size.height.min(display_size.height * 0.85);
        }

        let window_bounds = Bounds::centered(None, window_size, cx);

        cx.spawn(async move |cx| {
            let options = WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(window_bounds)),
                #[cfg(not(target_os = "linux"))]
                titlebar: Some(gpui_component::TitleBar::title_bar_options()),
                window_min_size: Some(gpui::Size {
                    width: px(640.),
                    height: px(480.),
                }),
                #[cfg(target_os = "linux")]
                window_background: gpui::WindowBackgroundAppearance::Transparent,
                #[cfg(target_os = "linux")]
                window_decorations: Some(gpui::WindowDecorations::Client),
                kind: WindowKind::Normal,
                ..Default::default()
            };

            let window = cx.open_window(options, |window, cx| {
                let story_view = cx.new(|cx| StoryWorkspace::new(window, cx));
                cx.new(|cx| Root::new(story_view.into(), window, cx))
            })?;

            window
                .update(cx, |_, window, cx| {
                    window.activate_window();
                    window.set_window_title("GPUI App");
                    cx.on_release(|_, cx| {
                        cx.quit();
                    })
                    .detach();
                })
                .expect("failed to update window");

            Ok(window)
        })
    }

    fn on_action_toggle_panel_visible(
        &mut self,
        action: &TogglePanelVisible,
        _: &mut Window,
        cx: &mut Context<Self>,
    ) {
        println!(
            "Toggling panel visibility: -------》{}---------------------------",
            action.0
        );
        let panel_name = action.0.clone();
        let invisible_panels = AppState::global(cx).invisible_panels.clone();
        invisible_panels.update(cx, |names, cx| {
            if names.contains(&panel_name) {
                names.remove(&panel_name);
            } else {
                names.insert(panel_name.clone());
            }
            cx.notify();
        });
        cx.notify();
    }

    fn on_action_toggle_dock_toggle_button(
        &mut self,
        _: &ToggleDockToggleButton,
        window: &mut Window,
        cx: &mut Context<Self>,
    ) {
        self.toggle_button_visible = !self.toggle_button_visible;
        self.dock_area.update(cx, |dock_area, cx| {
            dock_area.toggle_dock(DockPlacement::Left, window, cx);
        });
    }

    fn on_action_toggle_panel(
        &mut self,
        action: &TogglePanelAction,
        window: &mut Window,
        cx: &mut Context<Self>,
    ) {
        if let Some(id) = self.left_panel_manager.find_panel_id(&action.panel) {
            if let Err(e) = self.left_panel_manager.toggle_visibility(id, window, cx) {
                eprintln!("Error toggling panel: {}", e);
            }
        }
        cx.notify();
    }
}

pub fn open_new(
    cx: &mut App,
    init: impl FnOnce(&mut Root, &mut Window, &mut Context<Root>) + 'static + Send,
) -> Task<()> {
    let task: Task<std::result::Result<WindowHandle<Root>, anyhow::Error>> =
        StoryWorkspace::new_local(cx);
    cx.spawn(async move |cx| {
        if let Some(root) = task.await.ok() {
            root.update(cx, |workspace, window, cx| init(workspace, window, cx))
                .expect("failed to init workspace");
        }
    })
}

impl Render for StoryWorkspace {
    fn render(&mut self, window: &mut Window, cx: &mut Context<Self>) -> impl IntoElement {
        let drawer_layer = Root::render_drawer_layer(window, cx);
        let modal_layer = Root::render_modal_layer(window, cx);
        let notification_layer = Root::render_notification_layer(window, cx);

        div()
            .id("story-workspace")
            .on_action(cx.listener(Self::on_action_toggle_panel))
            .on_action(cx.listener(Self::on_action_toggle_panel_visible))
            // .on_action(cx.listener(Self::on_action_toggle_dock_toggle_button))
            .relative()
            .size_full()
            .flex()
            .flex_col()
            .child(self.title_bar.clone())
            .child(
                v_flex()
                    .relative()
                    .size_full()
                    .flex()
                    .flex_row()
                    .child(
                        v_flex()
                        .flex_shrink_0()
                        .bg(cx.theme().background)
                        .border_r_1()
                        .border_color(cx.theme().border)
                        .children([
                            Button::new("file-explorer")
                                .icon(SvgName::Folder.icon())
                                .large()
                                .ghost()
                                .tooltip(t!("FileExplorer.title"))
                                .on_click(cx.listener(
                                    |this: &mut StoryWorkspace,
                                     _: &ClickEvent,
                                     window: &mut Window,
                                     cx: &mut Context<StoryWorkspace>| {
                                        if let Err(e) = this.left_panel_manager.toggle_visibility(
                                            PanelId::FileExplorer,
                                            window, 
                                            cx,
                                        ) {
                                            eprintln!("Error toggling panel: {}", e);
                                        }
                                    },
                                )),
                            Button::new("search")
                                .icon(SvgName::Search.icon())
                                .large()
                                .ghost()
                                .tooltip("搜索")
                                .on_click(cx.listener(
                                    |this: &mut StoryWorkspace,
                                     _: &ClickEvent,
                                     window: &mut Window,
                                     cx: &mut Context<StoryWorkspace>| {
                                        if let Err(e) = this.left_panel_manager.toggle_visibility(
                                            PanelId::Search,
                                            window,
                                            cx,
                                        ) {
                                            eprintln!("Error toggling panel: {}", e);
                                        }
                                    },
                                )),
                            Button::new("extensions")
                                .icon(SvgName::Blocks.icon())
                                .large()
                                .ghost()
                                .tooltip("扩展")
                                .on_click(cx.listener(
                                    |this: &mut StoryWorkspace,
                                     _: &ClickEvent,
                                     window: &mut Window,
                                     cx: &mut Context<StoryWorkspace>| {
                                        if let Err(e) = this.left_panel_manager.toggle_visibility(
                                            PanelId::Extensions,
                                            window,
                                            cx,
                                        ) {
                                            eprintln!("Error toggling panel: {}", e);
                                        }
                                    },
                                )),
                        ])
                    )
                    .child(self.dock_area.clone()),
               )
            .child(self.status_bar.clone())
            .children(drawer_layer)
            .children(modal_layer)
            .child(div().absolute().top_8().children(notification_layer))
    }
}

// fn main() {
//     let app = Application::new().with_assets(Assets);

//     // rust_i18n::set_locale("zh-CN");
//     // println!("{:?}", t!("Run.run"));

//     //  rust_i18n::set_locale("en");
//     app.run(move |cx| {
//         story_workspace::init(cx);
//         story_workspace::open_new(cx, |_, _, _| {
//             // do something
//         })
//         .detach();
//     });
// }
