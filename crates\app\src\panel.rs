use gpui::App;
use gpui::WeakEntity;
use gpui::Window;
use gpui_component::dock::{DockArea, DockPlacement, PanelView};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::str::FromStr as _;
use std::sync::Arc;
use strum_macros::{
    Display as StrumDisplay, EnumIter as StrumEnumIter, EnumString as StrumEnumString,
};

use thiserror::Error;

#[derive(
    Debug,
    Clone,
    PartialEq,
    Eq,
    Hash,
    Serialize,
    Deserialize,
    StrumEnumIter,
    StrumEnumString,
    StrumDisplay,
)]
pub enum PanelId {
    #[strum(serialize = "FileExplorer.title")]
    FileExplorer,
    #[strum(serialize = "search")]
    Search,
    #[strum(serialize = "extensions")]
    Extensions,
    #[strum(serialize = "inspector")]
    Inspector,
}

#[derive(Debug, Error, <PERSON>lone)]
#[non_exhaustive]
pub enum PanelError {
    #[error("未注册的面板: {0}")]
    Unregistered(PanelId),

    #[error("面板状态冲突: {0}")]
    StateConflict(PanelId),

    #[error("无效的面板操作")]
    InvalidOperation,

    #[error("面板系统未初始化")]
    NotInitialized,

    #[error("Dock区域未配置")]
    DockNotConfigured,
}

pub struct PanelManager {
    panels: HashMap<PanelId, Arc<dyn PanelView>>,
    active_panel: Option<PanelId>,
    dock_placement: Option<DockPlacement>,
    dock_area: Option<WeakEntity<DockArea>>,
}

impl PanelManager {
    pub fn new(active_panel: Option<PanelId>) -> Self {
        Self {
            panels: HashMap::new(),
            active_panel,
            dock_placement: None,
            dock_area: None,
        }
    }

    /// 设置面板关联的Dock区域
    pub fn with_dock_placement(mut self, placement: DockPlacement) -> Self {
        self.dock_placement = Some(placement);
        self
    }

    /// 设置关联的DockArea
    pub fn with_dock_area(mut self, dock_area: WeakEntity<DockArea>) -> Self {
        self.dock_area = Some(dock_area);
        self
    }

    /// 获取当前激活的面板ID
    pub fn active_panel(&self) -> Option<PanelId> {
        self.active_panel.clone()
    }

    /// 注册新面板
    pub fn register_panel(&mut self, id: PanelId, panel: Arc<dyn PanelView>) {
        self.panels.insert(id, panel);
    }

    /// 切换面板可见性
    pub fn toggle_visibility(
        &mut self,
        id: PanelId,
        window: &mut Window,
        cx: &mut App,
    ) -> Result<(), PanelError> {
        // 检查面板是否注册
        if !self.panels.contains_key(&id) {
            return Err(PanelError::Unregistered(id));
        }

        // 保存当前激活面板（不移动所有权）
        let current_active = self.active_panel.clone();

        // 如果点击的是当前激活的面板，则关闭它
        if current_active == Some(id.clone()) {
            // self.active_panel = None;
            // self.remove_panel_from_dock(id, window, cx)?;
            // 关闭dock区域
            self.toggle_dock(window, cx)?;
            return Ok(());
        }

        // 如果之前有激活的面板，先移除
        if let Some(prev_id) = current_active {
            self.remove_panel_from_dock(prev_id, window, cx)?;
        }

        // 激活新面板
        self.active_panel = Some(id.clone());
        self.add_panel_to_dock(id, window, cx)?;

        // 确保 dock 是展开状态
        self.ensure_dock_expanded(window, cx)?;

        Ok(())
    }

    /// 切换 dock 区域的显示/隐藏
    pub fn toggle_dock(&self, window: &mut Window, cx: &mut App) -> Result<(), PanelError> {
        let dock_area = self
            .dock_area
            .as_ref()
            .ok_or(PanelError::DockNotConfigured)?;

        let placement = self.dock_placement.ok_or(PanelError::DockNotConfigured)?;

        dock_area
            .update(cx, |dock_area, cx| {
                dock_area.toggle_dock(placement, window, cx);
            })
            .map_err(|_| PanelError::InvalidOperation)?;

        Ok(())
    }

    /// 确保 dock 区域是展开状态
    fn ensure_dock_expanded(&self, window: &mut Window, cx: &mut App) -> Result<(), PanelError> {
        let dock_area = self
            .dock_area
            .as_ref()
            .ok_or(PanelError::DockNotConfigured)?;

        let placement = self.dock_placement.ok_or(PanelError::DockNotConfigured)?;

        dock_area
            .update(cx, |dock_area, cx| {
                if !dock_area.is_dock_open(placement, cx) {
                    dock_area.toggle_dock(placement, window, cx);
                }
            })
            .map_err(|_| PanelError::InvalidOperation)?;

        Ok(())
    }

    /// 将面板添加到Dock区域
    fn add_panel_to_dock(
        &self,
        id: PanelId,
        window: &mut Window,
        cx: &mut App,
    ) -> Result<(), PanelError> {
        let panel = self
            .panels
            .get(&id)
            .ok_or(PanelError::Unregistered(id))?
            .clone();

        let dock_area = self
            .dock_area
            .as_ref()
            .ok_or(PanelError::DockNotConfigured)?;

        let placement = self.dock_placement.ok_or(PanelError::DockNotConfigured)?;

        dock_area
            .update(cx, |dock_area, cx| {
                dock_area.add_panel(panel, placement, None, window, cx);
            })
            .map_err(|_| PanelError::InvalidOperation)?;

        Ok(())
    }

    /// 从Dock区域移除面板
    fn remove_panel_from_dock(
        &self,
        id: PanelId,
        window: &mut Window,
        cx: &mut App,
    ) -> Result<(), PanelError> {
        let panel = self
            .panels
            .get(&id)
            .ok_or(PanelError::Unregistered(id))?
            .clone();

        let dock_area = self
            .dock_area
            .as_ref()
            .ok_or(PanelError::DockNotConfigured)?;

        let placement = self.dock_placement.ok_or(PanelError::DockNotConfigured)?;

        dock_area
            .update(cx, |dock_area, cx| {
                dock_area.remove_panel(panel, placement, window, cx);
            })
            .map_err(|_| PanelError::InvalidOperation)?;

        Ok(())
    }

    /// 查找面板ID
    pub fn find_panel_id(&self, name: &str) -> Option<PanelId> {
        PanelId::from_str(name).ok()
    }
}
