/*
 * @Author: Art<PERSON>
 * @Date: 2025-07-18 20:21:16
 * @LastEditors: Artis
 * @LastEditTime: 2025-07-19 16:49:25
 * @FilePath: \FscDAQ_echarts\crates\chart\src\line_chart.rs
 * @Description:
 *
 * Copyright (c) 2025 by Artis, All Rights Reserved.
 */
use gpui::{
    point, px, App, Bounds, Context, Hsla, PathBuilder, Pixels, Point, SharedString, TextAlign,
    Window,
};
use gpui_component::{plot::{Plot, scale::{Scale, ScaleLinear}}, ActiveTheme};

use crate::shape::{
    axis::{Axis, AxisText, AXIS_GAP},
    grid::{Grid, GridAttr},
    line::{Line, LineData},
    origin_point,
};
use gpui_component_macros::IntoPlot;
use num_traits::Num;

use std::collections::HashMap;
use std::iter::Sum;
use std::sync::Mutex;
use std::{f64, rc::Rc};
use std::{
    sync::Arc,
    time::{Duration, Instant},
    u32,
};

// 网格线显示模式
#[derive(<PERSON><PERSON>, Copy, PartialEq)]
pub enum GridMode {
    None,         // 不显示网格线
    Auto,         // 自动根据区域大小决定
    Fixed(usize), // 固定数量
}

#[derive(IntoPlot, Clone)]
pub struct LineChart {
    data: Vec<LineInfo>,
    stroke: Vec<Hsla>,
    dot: bool,
    optimize: bool,
    tick_margin: usize,
    decimal: usize,
    chart_style: LineStyle,
    time: f64,
    start_time: f64,
    sum_time: f64,
    // 网格样式设置
    grid_solid: bool,   // true表示实线网格，false表示虚线网格
    border_solid: bool, // true表示实线边框，false表示虚线边框
    // 网格线显示模式
    x_grid_mode: GridMode,
    y_grid_mode: GridMode,
    // 渲染缓存
    render_cache: Arc<Mutex<RenderCache>>,
    // 上次渲染时间
    last_render_time: Instant,
    // 渲染帧率限制 (FPS)
    max_fps: u32,
    // 是否启用异步数据处理
    async_data_processing: bool,
    // 数据处理任务是否正在运行
    data_processing_active: bool,
}

/// 渲染缓存，用于存储预计算的渲染数据
struct RenderCache {
    // 缓存的路径数据
    path_cache: HashMap<usize, LineData>,
    // 缓存的网格数据
    grid_cache: Option<GridData>,
    // 缓存的坐标轴数据
    axis_cache: Option<AxisData>,
    // 缓存是否有效
    is_valid: bool,
    // 缓存创建时间
    created_at: Instant,
}

impl Default for RenderCache {
    fn default() -> Self {
        Self {
            path_cache: HashMap::new(),
            grid_cache: None,
            axis_cache: None,
            is_valid: false,
            created_at: Instant::now(),
        }
    }
}

/// 缓存的网格数据
#[derive(Clone)]
struct GridData {
    lines: Vec<LineData>,
}

/// 缓存的坐标轴数据
#[derive(Clone)]
struct AxisData {
    x_axis: LineData,
    y_axis: LineData,
    labels: Vec<(String, Point<Pixels>)>,
}

#[derive(Clone, Copy)]
pub struct LineStyle {
    pub color: Hsla,
    pub width: u32,
    pub gap: f64,
}

impl Default for LineStyle {
    fn default() -> Self {
        Self {
            color: gpui::blue(),
            width: 2,
            gap: 1.0,
        }
    }
}

#[derive(Clone, Default)]
pub struct LineInfo {
    pub line_style: Option<LineStyle>,  // 线条样式
    pub plot_data: Option<Vec<Pixels>>, // 绘制数据
    pub cached_path: Option<LineData>,  // 缓存已生成的路径
}

/// 优化的数据采样算法，使用简化版的数据抽样算法
/// 适用于f64类型的数据
pub fn optimize_data_f64(data: &[f64], target_count: usize) -> Vec<f64> {
    if data.len() <= target_count || target_count < 2 {
        return data.to_vec();
    }

    let mut result = Vec::with_capacity(target_count);

    // 始终保留第一个点
    result.push(data[0]);

    // 计算抽样间隔
    let step = (data.len() - 2) as f64 / (target_count - 2) as f64;

    // 对中间点进行抽样
    for i in 1..(target_count - 1) {
        let idx = (i as f64 * step) as usize + 1;
        result.push(data[idx.min(data.len() - 2)]);
    }

    // 始终保留最后一个点
    result.push(*data.last().unwrap());

    result
}

/// 比简单的极值采样提供更好的视觉效果
/// 注意：此函数专门用于处理Pixels类型
pub fn optimize_data_pixels(data: &[Pixels], target_count: usize) -> Vec<Pixels> {
    if data.len() <= target_count || target_count < 2 {
        return data.to_vec();
    }

    // 将Pixels转换为f64进行处理
    let data_f64: Vec<f64> = data.iter().map(|p| p.to_f64()).collect();

    // 使用f64版本的优化算法
    let result_indices = downsample_indices(&data_f64, target_count);

    // 根据索引选择原始数据点
    result_indices.into_iter().map(|idx| data[idx]).collect()
}

/// 计算用于下采样的索引
fn downsample_indices(data: &[f64], target_count: usize) -> Vec<usize> {
    if data.len() <= target_count || target_count < 2 {
        return (0..data.len()).collect();
    }

    let mut result = Vec::with_capacity(target_count);

    // 始终保留第一个点和最后一个点
    result.push(0);

    // 计算间隔
    let step = (data.len() - 2) as f64 / (target_count - 2) as f64;

    // 选择中间点
    for i in 1..(target_count - 1) {
        let bucket_start = ((i - 1) as f64 * step + 1.0) as usize;
        let bucket_end = (i as f64 * step + 1.0) as usize;

        // 在每个桶中找到最大值或最小值点
        let mut min_val = f64::MAX;
        let mut max_val = f64::MIN;
        let mut min_idx = bucket_start;
        let mut max_idx = bucket_start;

        for j in bucket_start..bucket_end.min(data.len() - 1) {
            if data[j] < min_val {
                min_val = data[j];
                min_idx = j;
            }
            if data[j] > max_val {
                max_val = data[j];
                max_idx = j;
            }
        }

        // 添加最大值或最小值点（选择与前一个点差异更大的）
        let prev_val = data[result[result.len() - 1]];
        if (max_val - prev_val).abs() > (min_val - prev_val).abs() {
            result.push(max_idx);
        } else {
            result.push(min_idx);
        }
    }

    // 添加最后一个点
    result.push(data.len() - 1);

    result
}

/// 原有的极值采样算法，保留做兼容
pub fn polar_data<T>(data: &Vec<T>, group: u32) -> Vec<T>
where
    T: Copy + PartialOrd,
{
    if data.is_empty() || group == 0 {
        return vec![];
    }

    let group_size = data.len() / group as usize;
    if group_size == 0 {
        return data.clone(); // 如果数据量小于组数，则返回原数据
    }
    let mut result = Vec::new();

    for i in 0..group {
        let start = i as usize * group_size;
        let end = if i == (group - 1) {
            data.len()
        } else {
            (i + 1) as usize * group_size
        };

        if start >= data.len() {
            break;
        }

        let slice = &data[start..end];
        let mut min = slice[0];
        let mut max = slice[0];

        for &item in slice.iter().skip(1) {
            if item < min {
                min = item;
            }
            if item > max {
                max = item;
            }
        }
        // 如果最大值和最小值相同，则只添加一个值
        if max == min {
            result.push(max);
            continue;
        } else {
            result.push(max);
            result.push(min);
        }
    }

    result
}

impl LineChart {
    pub fn new(data: Vec<LineInfo>) -> Self {
        Self {
            data: data.into_iter().collect(),
            chart_style: LineStyle::default(),
            time: 10.,
            start_time: 0.0,
            sum_time: 100.0,
            stroke: vec![],
            dot: false,
            tick_margin: 5,
            decimal: 3,
            optimize: true, // 默认开启优化 优化后，点数超过显示区域两倍时使用路径绘制
            grid_solid: false, // 默认使用虚线网格
            border_solid: true, // 默认使用实线边框
            x_grid_mode: GridMode::Auto, // 默认自动调整X轴网格线数量
            y_grid_mode: GridMode::Auto, // 默认自动调整Y轴网格线数量
            render_cache: Arc::new(Mutex::new(RenderCache::default())),
            last_render_time: Instant::now(),
            max_fps: 60,
            async_data_processing: true,
            data_processing_active: false,
        }
    }

    pub fn stroke(mut self, stroke: impl Into<Hsla>) -> Self {
        self.stroke.push(stroke.into());
        self
    }

    pub fn dot(mut self) -> Self {
        self.dot = true;
        self
    }

    pub fn optimize(mut self, optimize: bool) -> Self {
        self.optimize = optimize;
        self
    }

    pub fn tick_margin(mut self, tick_margin: usize) -> Self {
        self.tick_margin = tick_margin;
        self
    }

    pub fn decimal(mut self, decimal: usize) -> Self {
        self.decimal = decimal;
        self
    }

    /// 设置网格线样式（实线或虚线）
    pub fn grid_solid(mut self, solid: bool) -> Self {
        self.grid_solid = solid;
        self
    }

    /// 设置边框线样式（实线或虚线）
    pub fn border_solid(mut self, solid: bool) -> Self {
        self.border_solid = solid;
        self
    }

    /// 设置X轴网格线显示模式
    pub fn x_grid_mode(mut self, mode: GridMode) -> Self {
        self.x_grid_mode = mode;
        self
    }

    /// 设置Y轴网格线显示模式
    pub fn y_grid_mode(mut self, mode: GridMode) -> Self {
        self.y_grid_mode = mode;
        self
    }

    /// 设置最大帧率
    pub fn max_fps(mut self, fps: u32) -> Self {
        self.max_fps = fps;
        self
    }

    /// 启用或禁用异步数据处理
    pub fn async_data_processing(mut self, enable: bool) -> Self {
        self.async_data_processing = enable;
        self
    }

    /// 计算所有数据的Y轴范围
    fn calculate_y_range(&self) -> (f64, f64) {
        let mut global_y_min = f64::INFINITY;
        let mut global_y_max = f64::NEG_INFINITY;

        // 计算所有可见数据的Y值范围
        for ch in self.data.iter() {
            if let Some(plot_data) = &ch.plot_data {
                if !plot_data.is_empty() {
                    let style = ch.line_style.unwrap_or(self.chart_style);
                    let visible_start = (self.start_time / style.gap).floor() as usize;
                    let visible_end = ((self.start_time + self.time) / style.gap).ceil() as usize;
                    let data_len = plot_data.len();

                    // 获取可见数据
                    let visible_data = if visible_start < data_len {
                        &plot_data[visible_start.min(data_len)..visible_end.min(data_len)]
                    } else {
                        &[]
                    };

                    // 计算Y值范围
                    for &y in visible_data {
                        let y_val = y.to_f64();
                        global_y_min = global_y_min.min(y_val);
                        global_y_max = global_y_max.max(y_val);
                    }
                }
            }
        }

        // 添加边距
        let y_range = if global_y_max > global_y_min {
            global_y_max - global_y_min
        } else {
            1.0
        };

        (global_y_min - y_range * 0.1, global_y_max + y_range * 0.1)
    }

    fn draw_line(&mut self, bounds: Bounds<Pixels>, window: &mut Window) {
        // 帧率控制
        let now = Instant::now();
        let frame_duration = Duration::from_secs_f64(1.0 / self.max_fps as f64);
        if now.duration_since(self.last_render_time) < frame_duration {
            return;
        }

        // 开始测量绘制时间
        let draw_start_time = Instant::now();

        // 计算Y轴范围
        let (global_y_min, global_y_max) = self.calculate_y_range();

        // 线条绘制有效区域宽度和高度
        let width = bounds.size.width.to_f64();
        let height = bounds.size.height.to_f64();

        // 记录总数据点数
        let mut total_points = 0;

        for (i, ch) in self.data.iter().enumerate() {
            // 处理绘制数据
            let data_len = ch.plot_data.as_ref().map_or(0, |v| v.len());
            if data_len == 0 {
                continue;
            }

            // 累计数据点数量
            total_points += data_len;

            // 使用适当的样式
            let mut style = ch.line_style.unwrap_or(self.chart_style);

            // 如果有stroke定义，使用对应颜色
            if i < self.stroke.len() {
                style.color = self.stroke[i];
            }

            let visible_start = (self.start_time / style.gap).floor() as usize;
            let visible_end = ((self.start_time + self.time) / style.gap).ceil() as usize;

            // 获取有效数据
            let plot = &ch.plot_data.as_ref().map_or(&[][..], |v| {
                &v[visible_start.min(data_len)..visible_end.min(data_len)]
            });

            if plot.is_empty() {
                continue;
            }

            // 设置X和Y轴的缩放
            let x_fn = ScaleLinear::new(
                vec![0.0, self.time],
                vec![0.0, width as f32]
            );

            let y_fn = ScaleLinear::new(
                vec![global_y_min, global_y_max],
                vec![height as f32, 0.0]
            );

            // 数据转换为像素点
            let points = plot
                .iter()
                .enumerate()
                .map(|(idx, &y)| {
                    let x_val = idx as f64 * style.gap;
                    let x = x_fn.tick(&x_val).unwrap_or(0.0);
                    let y = y_fn.tick(&y.to_f64()).unwrap_or(0.0);
                    (x as i32, y as i32)
                })
                .collect::<Vec<_>>();

            // 检查是否有有效的点
            if !points.is_empty() {
                // 优化：当点数超过1900时使用路径绘制
                if points.len() >= width as usize * 2 && self.optimize {
                    // println!("使用路径绘制，点数: {:?}", points);

                    // 创建路径
                    let mut path_builder = PathBuilder::fill();

                    // 移动到第一个点
                    let first_point = Point::new(
                        bounds.origin.x + Pixels(points[0].0 as f32),
                        bounds.origin.y + Pixels(points[0].1 as f32),
                    );
                    path_builder.move_to(first_point);

                    let mut d1 = Vec::new();
                    let mut d2 = Vec::new();
                    // 直接连接所有点，显示数据轮廓
                    for i in 1..points.len() {
                        let curr_x = points[i].0;
                        let curr_y = points[i].1;

                        // 添加当前点
                        let point = Point::new(
                            bounds.origin.x + Pixels(curr_x as f32),
                            bounds.origin.y + Pixels(curr_y as f32),
                        );
                        if i % 2 == 0 {
                            d1.push(point);
                        } else {
                            d2.push(point);
                        }
                    }

                    // 先正向添加偶数点
                    for point in d1 {
                        path_builder.line_to(point);
                    }

                    // 再反向添加奇数点
                    for point in d2.iter().rev() {
                        path_builder.line_to(*point);
                    }

                    path_builder.line_to(first_point);
                    // 绘制路径
                    if let Ok(path) = path_builder.build() {
                        window.paint_path(path, style.color);
                    }
                } else {
                    // 点数较少时使用原有的线段绘制方式
                    let line_data = LineData {
                        point: points.clone(),
                        color: style.color,
                        width: style.width,
                        dash_array: None, // 普通线条不使用虚线
                    };

                    let mut line = Line::new().push(line_data);
                    line.paint(&bounds, window);
                }
            }
        }

        // 结束测量并打印绘制时间
        let draw_duration = draw_start_time.elapsed();
        println!(
            "绘制线段耗时: {:?}，总数据点数: {}",
            draw_duration, total_points
        );

        self.last_render_time = now;
    }

    /// 根据显示区域大小确定网格线数量
    fn determine_grid_count(&self, size: Pixels, mode: GridMode) -> usize {
        match mode {
            GridMode::None => 0,             // 不显示网格线
            GridMode::Fixed(count) => count, // 固定数量
            GridMode::Auto => {
                // 根据区域大小自动确定网格线数量，使用0、2、4、9几种规格
                let size_value = size.to_f64();
                if size_value < 200.0 {
                    0 // 区域太小，不显示网格线
                } else if size_value < 300.0 {
                    2 // 小区域，显示2条网格线
                } else if size_value < 500.0 {
                    4 // 中等区域，显示4条网格线
                } else {
                    9 // 大区域，显示9条网格线
                }
            }
        }
    }

    fn draw_grid_axis(&mut self, bounds: Bounds<Pixels>, window: &mut Window, cx: &mut App) {
        // 通过计算颜色亮度来判断是否为浅色主题
        // 根据主题选择适当的颜色
        let grid_color = if is_light_color(cx.theme().background) {
            // 白色主题下使用白灰色
            Hsla {
                h: 0.0,
                s: 0.0,
                l: 0.7,
                a: 1.0,
            } // 使用白灰色
        } else {
            // base_color
            Hsla {
                h: 0.0,
                s: 0.0,
                l: 0.2,
                a: 1.0,
            } // 使用白灰色
        };

        let axis_color = grid_color.alpha(0.5);
        let label_color = cx.theme().muted_foreground;

        // 计算Y轴范围，用于坐标轴标签
        let (y_min, y_max) = self.calculate_y_range();

        // 根据显示区域大小和模式确定网格线数量
        let x_tick_count = self.determine_grid_count(bounds.size.width, self.x_grid_mode);
        let y_tick_count = self.determine_grid_count(bounds.size.height, self.y_grid_mode);

        // 只有在需要显示网格线时才创建和绘制
        if x_tick_count > 0 || y_tick_count > 0 {
            // 创建内部网格线
            let mut inner_x_attrs = Vec::new();
            if x_tick_count > 0 {
                inner_x_attrs = (1..x_tick_count)
                    .map(|i| {
                        let x_ratio = i as f64 / x_tick_count as f64;
                        let x = px((bounds.size.width.to_f64() * x_ratio) as f32);

                        GridAttr {
                            grid: x,
                            dash_array: if self.grid_solid {
                                None // 实线
                            } else {
                                Some([px(2.0), px(3.0)]) // 虚线
                            },
                        }
                    })
                    .collect::<Vec<_>>();
            }

            let mut inner_y_attrs = Vec::new();
            if y_tick_count > 0 {
                inner_y_attrs = (1..y_tick_count)
                    .map(|i| {
                        let y_ratio = i as f64 / y_tick_count as f64;
                        let y = px((bounds.size.height.to_f64() * y_ratio) as f32);

                        GridAttr {
                            grid: y,
                            dash_array: if self.grid_solid {
                                None // 实线
                            } else {
                                Some([px(2.0), px(3.0)]) // 虚线
                            },
                        }
                    })
                    .collect::<Vec<_>>();
            }

            // 创建并绘制内部网格
            let inner_grid = Grid::new()
                .x(inner_x_attrs)
                .y(inner_y_attrs)
                .stroke(grid_color);
            inner_grid.paint(&bounds, window);
        }

        // 绘制边框
        let border_width = if self.border_solid { 2 } else { 1 };
        let dash_array = if self.border_solid {
            None
        } else {
            Some([px(2.0), px(3.0)])
        };

        // 创建四条边框线
        let top_line = LineData {
            point: vec![(0, 0), (bounds.size.width.to_f64() as i32, 0)],
            color: grid_color,
            width: border_width,
            dash_array: dash_array.clone(),
        };

        let right_line = LineData {
            point: vec![
                (bounds.size.width.to_f64() as i32, 0),
                (
                    bounds.size.width.to_f64() as i32,
                    bounds.size.height.to_f64() as i32,
                ),
            ],
            color: grid_color,
            width: border_width,
            dash_array: dash_array.clone(),
        };

        let bottom_line = LineData {
            point: vec![
                (
                    bounds.size.width.to_f64() as i32,
                    bounds.size.height.to_f64() as i32,
                ),
                (0, bounds.size.height.to_f64() as i32),
            ],
            color: grid_color,
            width: border_width,
            dash_array: dash_array.clone(),
        };

        let left_line = LineData {
            point: vec![(0, bounds.size.height.to_f64() as i32), (0, 0)],
            color: grid_color,
            width: border_width,
            dash_array,
        };

        // 绘制边框
        let mut border = Line::new()
            .push(top_line)
            .push(right_line)
            .push(bottom_line)
            .push(left_line);
        border.paint(&bounds, window);

        // 使用与网格线相同的刻度数量来创建坐标轴标签
        // 如果网格线模式为None或数量为0，则使用固定数量的刻度
        let x_label_count = if self.x_grid_mode == GridMode::None || x_tick_count == 0 {
            // 当网格线为None或数量为0时，使用5个刻度
            5
        } else {
            x_tick_count.max(2) // 确保至少有2个刻度
        };

        let y_label_count = if self.y_grid_mode == GridMode::None || y_tick_count == 0 {
            // 当网格线为None或数量为0时，使用5个刻度
            5
        } else {
            y_tick_count.max(2) // 确保至少有2个刻度
        };

        // 创建X轴刻度标签和刻度线
        let x_labels = (0..=x_label_count)
            .map(|i| {
                let x_ratio = i as f64 / x_label_count as f64;
                let x = px((bounds.size.width.to_f64() * x_ratio) as f32);
                let value = self.start_time + (self.time * i as f64 / x_label_count as f64);

                // 确定标签对齐方式
                let align = if i == 0 {
                    TextAlign::Left // 第一个标签左对齐
                } else if i == x_label_count {
                    TextAlign::Right // 最后一个标签右对齐
                } else {
                    TextAlign::Center // 中间标签居中对齐
                };

                // 绘制X轴刻度线 - 主刻度 (向上延伸，即图表内部)
                self.draw_tick_mark(
                    Point::new(bounds.origin.x + x, bounds.origin.y + bounds.size.height),
                    Point::new(
                        bounds.origin.x + x,
                        bounds.origin.y + bounds.size.height - px(10.0),
                    ), // 向上延伸
                    axis_color,
                    window,
                );

                // 如果刻度数量足够多，添加次级刻度（短刻度）
                if x_label_count >= 2 && i < x_label_count {
                    // 在每两个主刻度之间绘制4个次级刻度线
                    for j in 1..=4 {
                        let sub_ratio = j as f64 / 5.0; // 将间隔分成5等份，绘制4个次级刻度
                        let sub_x = px((bounds.size.width.to_f64()
                            * (x_ratio + sub_ratio / x_label_count as f64))
                            as f32);

                        // 绘制次级刻度线（较短，向上延伸）
                        self.draw_tick_mark(
                            Point::new(
                                bounds.origin.x + sub_x,
                                bounds.origin.y + bounds.size.height,
                            ),
                            Point::new(
                                bounds.origin.x + sub_x,
                                bounds.origin.y + bounds.size.height - px(6.0),
                            ), // 向上延伸
                            axis_color.alpha(0.7), // 稍微淡一点
                            window,
                        );
                    }
                }

                AxisText::new(format!("{:.*}", self.decimal, value), x, label_color).align(align)
            })
            .collect::<Vec<_>>();

        // 创建Y轴刻度标签和刻度线
        let y_range = y_max - y_min;
        let y_labels = (0..=y_label_count)
            .map(|i| {
                let y_ratio = 1.0 - (i as f64 / y_label_count as f64);
                let y = px((bounds.size.height.to_f64() * y_ratio) as f32);

                // 计算该位置对应的实际值
                let value = y_min + y_range * i as f64 / y_label_count as f64;

                // 格式化标签，控制小数位数
                let label = format!("{:.*}", self.decimal, value);

                // 绘制Y轴刻度线 - 主刻度 (向右延伸，即图表内部)
                self.draw_tick_mark(
                    Point::new(bounds.origin.x, bounds.origin.y + y),
                    Point::new(bounds.origin.x + px(10.0), bounds.origin.y + y), // 向右延伸
                    axis_color,
                    window,
                );

                // 如果刻度数量足够多，添加次级刻度（短刻度）
                if y_label_count >= 2 && i < y_label_count {
                    // 在每两个主刻度之间绘制4个次级刻度线
                    for j in 1..=4 {
                        let sub_ratio = j as f64 / 5.0; // 将间隔分成5等份，绘制4个次级刻度
                        let sub_y = px((bounds.size.height.to_f64()
                            * (y_ratio - sub_ratio / y_label_count as f64))
                            as f32);

                        // 绘制次级刻度线（较短，向右延伸）
                        self.draw_tick_mark(
                            Point::new(bounds.origin.x, bounds.origin.y + sub_y),
                            Point::new(bounds.origin.x + px(6.0), bounds.origin.y + sub_y), // 向右延伸
                            axis_color.alpha(0.7), // 稍微淡一点
                            window,
                        );
                    }
                }

                // 使用右对齐，避免标签超出左边界
                AxisText::new(label, y, label_color).align(TextAlign::Right)
            })
            .collect::<Vec<_>>();

        // 创建并绘制坐标轴 - 隐藏轴线，使用边框作为轴线
        // X轴位于底部，Y轴位于左侧
        let x_axis_point = point(px(0.0), bounds.size.height);
        // 向左偏移5像素，确保标签不会太靠近边框 向上偏移5像素 显示的字对中网格线
        let y_axis_point = point(px(-5.0), px(-5.0));

        let axis = Axis::new()
            .x(x_axis_point)
            .hide_x_axis() // 隐藏X轴线，使用边框作为轴线
            .x_label(x_labels)
            .y(y_axis_point)
            .hide_y_axis() // 隐藏Y轴线，使用边框作为轴线
            .y_label(y_labels)
            .stroke(axis_color);

        axis.paint(&bounds, window, cx);
    }

    // 绘制刻度线
    fn draw_tick_mark(
        &self,
        start: Point<Pixels>,
        end: Point<Pixels>,
        color: Hsla,
        window: &mut Window,
    ) {
        let mut builder = PathBuilder::stroke(px(2.0)); // 增加线宽到2像素
        builder.move_to(start);
        builder.line_to(end);
        if let Ok(path) = builder.build() {
            window.paint_path(path, color);
        }
    }
}

impl Plot for LineChart {
    fn paint(&mut self, bounds: Bounds<Pixels>, window: &mut Window, cx: &mut App) {
        // 定义边距常量，用于绘制区域
        let left_margin = px(60.0); // 为Y轴标签留出足够空间
        let bottom_margin = px(18.0); // 为X轴标签留出足够空间
        let top_margin = px(1.0); // 顶部留白
        let right_margin = px(1.0); // 右侧留白

        // 检查边界条件
        if bounds.size.width <= left_margin + right_margin
            || bounds.size.height <= top_margin + bottom_margin
        {
            return; // 图表区域太小，不绘制
        }

        // 计算有效绘制区域
        let chart_bounds = Bounds {
            origin: point(bounds.origin.x + left_margin, bounds.origin.y + top_margin),
            size: gpui::Size {
                width: bounds.size.width - left_margin - right_margin,
                height: bounds.size.height - top_margin - bottom_margin,
            },
        };

        // 绘制网格和坐标轴
        self.draw_grid_axis(chart_bounds, window, cx);
        // let grid_axis_duration = Instant::now();
        // 绘制数据线条
        self.draw_line(chart_bounds, window);
        // let draw_duration = grid_axis_duration.elapsed();
        // println!(
        //     "绘制线段耗时: {:?}，数据点数: {}",
        //     draw_duration,
        //     self.data
        //         .iter()
        //         .map(|ch| ch.plot_data.as_ref().map_or(0, |v| v.len()))
        //         .sum::<usize>()
        // );
        // 无效缓存，确保下次重新计算
        if let Ok(mut cache) = self.render_cache.lock() {
            cache.is_valid = false;
        }
    }
}

// 辅助函数：判断颜色是否为浅色
fn is_light_color(color: Hsla) -> bool {
    // 使用HSL颜色模型中的亮度(L)来判断
    // 通常亮度大于0.5的颜色被视为浅色
    color.l > 0.5
}
